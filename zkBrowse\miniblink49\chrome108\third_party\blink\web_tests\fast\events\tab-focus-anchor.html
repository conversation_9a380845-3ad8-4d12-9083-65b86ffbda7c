<!DOCTYPE html>
<html>
<head>
<script>

function pass()
{
    var el = document.querySelector('pre');
    el.textContent += 'PASS\n';
}

function fail()
{
    var el = document.querySelector('pre');
    el.textContent += 'FAIL\n';
}

if (window.testRunner) {
    testRunner.dumpAsText();
    testRunner.overridePreference('WebKitTabToLinksPreferenceKey', false);
}

window.onload = function()
{
    if (!window.testRunner)
        return;

    for (var i = 0; i < 2; i++) {
        eventSender.keyDown('\t');
    }
};

</script>
</head>
<body>

<p>This test ensures that we can tab to an a element with a tab index. Press tab
to focus the element below.

<p><a onfocus="fail()" href="#">Not focusable (given the WebKitTabToLinks pref is set to false)</a>
<p><a onfocus="fail()">Not focusable</a>
<p><a tabindex=0 onfocus="pass()" href="#">Focusable</a>
<p><a tabindex=0 onfocus="pass()">Focusable</a>

<p>Result

<pre id="out"></pre>

</body>
</html>
