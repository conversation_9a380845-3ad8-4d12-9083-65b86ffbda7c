Tests generating AES keys

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".

PASS typeof generatedKey.extractable is 'boolean'
PASS generatedKey.extractable is true
PASS generatedKey.algorithm.name is "AES-CBC"
PASS generatedKey.algorithm.length is 128
PASS generatedKey.usages.join(',') is "encrypt"
PASS keyData.byteLength is 16
PASS Generated unique key data of length: 128 bits
PASS typeof generatedKey.extractable is 'boolean'
PASS generatedKey.extractable is true
PASS generatedKey.algorithm.name is "AES-CBC"
PASS generatedKey.algorithm.length is 256
PASS generatedKey.usages.join(',') is "encrypt"
PASS keyData.byteLength is 32
PASS Generated unique key data of length: 256 bits
PASS typeof generatedKey.extractable is 'boolean'
PASS generatedKey.extractable is true
PASS generatedKey.algorithm.name is "AES-CBC"
PASS generatedKey.algorithm.length is 128
PASS generatedKey.usages.join(',') is "decrypt,wrapKey"
PASS keyData.byteLength is 16
PASS Generated unique key data of length: 128 bits
PASS typeof generatedKey.extractable is 'boolean'
PASS generatedKey.extractable is true
PASS generatedKey.algorithm.name is "AES-CBC"
PASS generatedKey.algorithm.length is 256
PASS generatedKey.usages.join(',') is "decrypt,wrapKey"
PASS keyData.byteLength is 32
PASS Generated unique key data of length: 256 bits
PASS typeof generatedKey.extractable is 'boolean'
PASS generatedKey.extractable is true
PASS generatedKey.algorithm.name is "AES-CBC"
PASS generatedKey.algorithm.length is 128
PASS generatedKey.usages.join(',') is "encrypt,wrapKey,unwrapKey"
PASS keyData.byteLength is 16
PASS Generated unique key data of length: 128 bits
PASS typeof generatedKey.extractable is 'boolean'
PASS generatedKey.extractable is true
PASS generatedKey.algorithm.name is "AES-CBC"
PASS generatedKey.algorithm.length is 256
PASS generatedKey.usages.join(',') is "encrypt,wrapKey,unwrapKey"
PASS keyData.byteLength is 32
PASS Generated unique key data of length: 256 bits
PASS typeof generatedKey.extractable is 'boolean'
PASS generatedKey.extractable is false
PASS generatedKey.algorithm.name is "AES-CBC"
PASS generatedKey.algorithm.length is 128
PASS generatedKey.usages.join(',') is "encrypt"
PASS typeof generatedKey.extractable is 'boolean'
PASS generatedKey.extractable is false
PASS generatedKey.algorithm.name is "AES-CBC"
PASS generatedKey.algorithm.length is 256
PASS generatedKey.usages.join(',') is "encrypt"
PASS typeof generatedKey.extractable is 'boolean'
PASS generatedKey.extractable is false
PASS generatedKey.algorithm.name is "AES-CBC"
PASS generatedKey.algorithm.length is 128
PASS generatedKey.usages.join(',') is "decrypt,wrapKey"
PASS typeof generatedKey.extractable is 'boolean'
PASS generatedKey.extractable is false
PASS generatedKey.algorithm.name is "AES-CBC"
PASS generatedKey.algorithm.length is 256
PASS generatedKey.usages.join(',') is "decrypt,wrapKey"
PASS typeof generatedKey.extractable is 'boolean'
PASS generatedKey.extractable is false
PASS generatedKey.algorithm.name is "AES-CBC"
PASS generatedKey.algorithm.length is 128
PASS generatedKey.usages.join(',') is "encrypt,wrapKey,unwrapKey"
PASS typeof generatedKey.extractable is 'boolean'
PASS generatedKey.extractable is false
PASS generatedKey.algorithm.name is "AES-CBC"
PASS generatedKey.algorithm.length is 256
PASS generatedKey.usages.join(',') is "encrypt,wrapKey,unwrapKey"
PASS typeof generatedKey.extractable is 'boolean'
PASS generatedKey.extractable is true
PASS generatedKey.algorithm.name is "AES-GCM"
PASS generatedKey.algorithm.length is 128
PASS generatedKey.usages.join(',') is "encrypt"
PASS keyData.byteLength is 16
PASS Generated unique key data of length: 128 bits
PASS typeof generatedKey.extractable is 'boolean'
PASS generatedKey.extractable is true
PASS generatedKey.algorithm.name is "AES-GCM"
PASS generatedKey.algorithm.length is 256
PASS generatedKey.usages.join(',') is "encrypt"
PASS keyData.byteLength is 32
PASS Generated unique key data of length: 256 bits
PASS typeof generatedKey.extractable is 'boolean'
PASS generatedKey.extractable is true
PASS generatedKey.algorithm.name is "AES-GCM"
PASS generatedKey.algorithm.length is 128
PASS generatedKey.usages.join(',') is "decrypt,wrapKey"
PASS keyData.byteLength is 16
PASS Generated unique key data of length: 128 bits
PASS typeof generatedKey.extractable is 'boolean'
PASS generatedKey.extractable is true
PASS generatedKey.algorithm.name is "AES-GCM"
PASS generatedKey.algorithm.length is 256
PASS generatedKey.usages.join(',') is "decrypt,wrapKey"
PASS keyData.byteLength is 32
PASS Generated unique key data of length: 256 bits
PASS typeof generatedKey.extractable is 'boolean'
PASS generatedKey.extractable is true
PASS generatedKey.algorithm.name is "AES-GCM"
PASS generatedKey.algorithm.length is 128
PASS generatedKey.usages.join(',') is "encrypt,wrapKey,unwrapKey"
PASS keyData.byteLength is 16
PASS Generated unique key data of length: 128 bits
PASS typeof generatedKey.extractable is 'boolean'
PASS generatedKey.extractable is true
PASS generatedKey.algorithm.name is "AES-GCM"
PASS generatedKey.algorithm.length is 256
PASS generatedKey.usages.join(',') is "encrypt,wrapKey,unwrapKey"
PASS keyData.byteLength is 32
PASS Generated unique key data of length: 256 bits
PASS typeof generatedKey.extractable is 'boolean'
PASS generatedKey.extractable is false
PASS generatedKey.algorithm.name is "AES-GCM"
PASS generatedKey.algorithm.length is 128
PASS generatedKey.usages.join(',') is "encrypt"
PASS typeof generatedKey.extractable is 'boolean'
PASS generatedKey.extractable is false
PASS generatedKey.algorithm.name is "AES-GCM"
PASS generatedKey.algorithm.length is 256
PASS generatedKey.usages.join(',') is "encrypt"
PASS typeof generatedKey.extractable is 'boolean'
PASS generatedKey.extractable is false
PASS generatedKey.algorithm.name is "AES-GCM"
PASS generatedKey.algorithm.length is 128
PASS generatedKey.usages.join(',') is "decrypt,wrapKey"
PASS typeof generatedKey.extractable is 'boolean'
PASS generatedKey.extractable is false
PASS generatedKey.algorithm.name is "AES-GCM"
PASS generatedKey.algorithm.length is 256
PASS generatedKey.usages.join(',') is "decrypt,wrapKey"
PASS typeof generatedKey.extractable is 'boolean'
PASS generatedKey.extractable is false
PASS generatedKey.algorithm.name is "AES-GCM"
PASS generatedKey.algorithm.length is 128
PASS generatedKey.usages.join(',') is "encrypt,wrapKey,unwrapKey"
PASS typeof generatedKey.extractable is 'boolean'
PASS generatedKey.extractable is false
PASS generatedKey.algorithm.name is "AES-GCM"
PASS generatedKey.algorithm.length is 256
PASS generatedKey.usages.join(',') is "encrypt,wrapKey,unwrapKey"
PASS successfullyParsed is true

TEST COMPLETE

