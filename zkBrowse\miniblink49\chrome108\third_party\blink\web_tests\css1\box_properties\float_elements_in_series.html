<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.5.25 float</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
P { margin: 0; padding: 0; text-align:  justify;}

DIV.yellow, DIV.blue, DIV.red, DIV.green {
 width: 60px; padding: 20px; margin: 10px;
 border: 20px solid black; float: left; text-align: center;
 font-family: "Times New Roman",Times,serif;}
DIV.yellow {margin-left: 0px; background: yellow; color: black;}
DIV.blue {background: blue; color: white;}
DIV.red {background: red; color: black;}
DIV.green {background: green; color: white;}
DIV.below {clear: both;}
TABLE {margin: 20px 0px;}
</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>P { margin: 0; padding: 0; text-align:  justify;}

DIV.yellow, DIV.blue, DIV.red, DIV.green {
 width: 60px; padding: 20px; margin: 10px;
 border: 20px solid black; float: left; text-align: center;
 font-family: "Times New Roman",Times,serif;}
DIV.yellow {margin-left: 0px; background: yellow; color: black;}
DIV.blue {background: blue; color: white;}
DIV.red {background: red; color: black;}
DIV.green {background: green; color: white;}
DIV.below {clear: both;}
TABLE {margin: 20px 0px;}

</PRE>
<HR>
<P>The four floating DIV elements below should appear next to each other horizontally. The table at the bottom of this page indicates how the DIV elements should be laid out horizontally.  They should align with each other.
</P>
<div class="yellow">
  <P>Yellow</P>
</div>
<div class="blue">
  <P>Blue</P>
</div>
<div class="red">
  <P>Red</P>
</div>
<div class="green">
  <P>Green</P>
</div>

<div class="below">
<table cellspacing=0 cellpadding=0>
<tr>
<td width="20" style="background: black"><IMG SRC="blank.gif" width="20" height="1"></td>
<td width=100 style="background: yellow"><IMG SRC="blank.gif" width="100" height="1"></td>
<td width=20 style="background: black"<IMG SRC="blank.gif" width="20" height="1"></td>

<td width=20 style="background: white"><IMG SRC="blank.gif" width="20" height="1"></td>

<td width=20 style="background: black"><IMG SRC="blank.gif" width="20" height="1"></td>
<td width=100 style="background: blue"><IMG SRC="blank.gif" width="100" height="1"></td>
<td width=20 style="background: black"><IMG SRC="blank.gif" width="20" height="1"></td>

<td width=20 style="background: white"><IMG SRC="blank.gif" width="20" height="1"></td>

<td width=20 style="background: black"><IMG SRC="blank.gif" width="20" height="1"></td>
<td width=100 style="background: red"><IMG SRC="blank.gif" width="100" height="1"></td>
<td width=20 style="background: black"><IMG SRC="blank.gif" width="20" height="1">l</td>

<td width=20 style="background: white"><IMG SRC="blank.gif" width="20" height="1"></td>

<td width=20 style="background: black"><IMG SRC="blank.gif" width="20" height="1"></td>
<td width=100 style="background: green"><IMG SRC="blank.gif" width="100" height="1"></td>
<td width=20 style="background: black"><IMG SRC="blank.gif" width="20" height="1"></td>
</TR>
</TABLE>

</div>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P>The four floating DIV elements below should appear next to each other horizontally. The table at the bottom of this page indicates how the DIV elements should be laid out horizontally.  They should align with each other.
</P>
<div class="yellow">
  <P>Yellow</P>
</div>
<div class="blue">
  <P>Blue</P>
</div>
<div class="red">
  <P>Red</P>
</div>
<div class="green">
  <P>Green</P>
</div>

<div class="below">
<table cellspacing=0 cellpadding=0>
<tr>
<td width="20" style="background: black"><IMG SRC="blank.gif" width="20" height="1"></td>
<td width=100 style="background: yellow"><IMG SRC="blank.gif" width="100" height="1"></td>
<td width=20 style="background: black"<IMG SRC="blank.gif" width="20" height="1"></td>

<td width=20 style="background: white"><IMG SRC="blank.gif" width="20" height="1"></td>

<td width=20 style="background: black"><IMG SRC="blank.gif" width="20" height="1"></td>
<td width=100 style="background: blue"><IMG SRC="blank.gif" width="100" height="1"></td>
<td width=20 style="background: black"><IMG SRC="blank.gif" width="20" height="1"></td>

<td width=20 style="background: white"><IMG SRC="blank.gif" width="20" height="1"></td>

<td width=20 style="background: black"><IMG SRC="blank.gif" width="20" height="1"></td>
<td width=100 style="background: red"><IMG SRC="blank.gif" width="100" height="1"></td>
<td width=20 style="background: black"><IMG SRC="blank.gif" width="20" height="1">l</td>

<td width=20 style="background: white"><IMG SRC="blank.gif" width="20" height="1"></td>

<td width=20 style="background: black"><IMG SRC="blank.gif" width="20" height="1"></td>
<td width=100 style="background: green"><IMG SRC="blank.gif" width="100" height="1"></td>
<td width=20 style="background: black"><IMG SRC="blank.gif" width="20" height="1"></td>
</TR>
</TABLE>

</div>
</TD></TR></TABLE></BODY>
</HTML>
