<p>Test pageshow/pagehide event behavior when navigating back to an uncached page.</p>
<script>
if (window.testRunner) {
    testRunner.dumpAsText();
    testRunner.waitUntilDone();
}

window.onload = function(evt) {
    alert("window.onload");
    if (window.name == 'pageshow/pagehide') {
        // Returned back.
        window.name = "";
        window.onpagehide = null;
        setTimeout(function() { if (window.testRunner) testRunner.notifyDone(); }, 10);
    } else {
        window.name = "pageshow/pagehide";
        setTimeout('window.location = "../../resources/back.html"', 0);
    }
}

window.onunload = function() {}  // prevent caching

window.onpageshow = function(evt) {
    alert("window.onpageshow" + ", target = " + evt.target + ", persisted = " + evt.persisted);
}

window.onpagehide = function(evt) {
    alert("window.onpagehide" + ", target = " + evt.target + ", persisted = " + evt.persisted);
}

</script>
