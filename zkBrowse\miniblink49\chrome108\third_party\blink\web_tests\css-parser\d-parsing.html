<!DOCTYPE html>
<script src="../resources/testharness.js"></script>
<script src="../resources/testharnessreport.js"></script>
<script src="resources/property-parsing-test.js"></script>
<script>
// Verifies that d property and its value are properly parsed

assert_valid_value('d', 'path("M 0 0 L 100 100 M 100 200 L 200 200 Z L 300 300 Z")');
assert_valid_value('d', 'none');
assert_valid_value('d', 'path("")', 'none');
assert_invalid_value('d', 'path("M 20 30 A 60 70 80")');
</script>
