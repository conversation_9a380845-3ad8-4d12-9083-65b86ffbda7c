Verifies that html resources are previewed with the encoding set in their content-type header

Running: testUTF8
fetchScript: fetch('/devtools/network/resources/content-type-utf8.php')
iframe.src: data:text/html;charset=utf-8,utf8%20character:%20%E2%80%A6

Running: testISO
fetchScript: fetch('/devtools/network/resources/content-type-iso.php')
iframe.src: data:text/html;charset=iso-8859-1,iso-8859-1%20character:%20%C3%82%C2%A9

Running: testNoCharset
fetchScript: fetch('/devtools/network/resources/content-type-none.php')
iframe.src: data:text/html,no%20encoding

