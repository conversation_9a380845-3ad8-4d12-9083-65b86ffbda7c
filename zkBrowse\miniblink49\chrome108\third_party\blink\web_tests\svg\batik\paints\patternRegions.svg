<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN"
"http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">

<!--

   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

-->
<!-- ========================================================================= -->
<!-- This test file checks that the various combinations of units and viewbox  -->
<!-- work in Batik for patternRegions                                          -->
<!--                                                                           -->
<!-- <AUTHOR>                                                -->
<!-- @version $Id: patternRegions.svg 475477 2006-11-15 22:44:28Z cam $ -->
<!-- ========================================================================= -->

<?xml-stylesheet type="text/css" href="../resources/test.css" ?>

<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="body" width="450" height="500" viewBox="0 0 450 500">
<title>Pattern Tests</title>

    <text x="225" y="50" class="title">
        patternUnits / patternContentUnits 
    </text>
    <text x="225" y="75" class="title">
        pattern viewBox
    </text>

    <!-- ============================================================= -->
    <!-- Test content                                                  -->
    <!-- ============================================================= -->
    <g id="testContent" style="font-size:10">
        <defs>
        <!-- ============================================== -->
        <!-- patternUnits : default (objectBoundingBox)     -->
        <!-- patternContentUnits : default (userSpaceOnUse) -->
        <!-- viewBox : default (none)                       -->
        <!-- ============================================== -->
        <pattern id="pattern1" x="0" y="0" width=".75" height="50%">
            <circle cx="10" cy="10" r="10" style="fill:red" />
            <rect x="10" y="10" width="15" height="15" style="fill:green"/>
        </pattern>

        <!-- ============================================== -->
        <!-- patternUnits : objectBoundingBox               -->
        <!-- patternContentUnits : default (userSpaceOnUse) -->
        <!-- viewBox : default (none)                       -->
        <!-- ============================================== -->
        <pattern id="pattern2" patternUnits="objectBoundingBox" x="0" y="0" width=".75" height="50%">
            <circle cx="10" cy="10" r="10" style="fill:red" />
            <rect x="10" y="10" width="15" height="15" style="fill:green"/>
        </pattern>

        <!-- ============================================== -->
        <!-- patternUnits : userSpaceOnUse                  -->
        <!-- patternContentUnits : default (userSpaceOnUse) -->
        <!-- viewBox : default (none)                       -->
        <!-- ============================================== -->
        <pattern id="pattern3" 
                 patternUnits="userSpaceOnUse" 
                 x="0" y="0" width="30" height="15" overflow="visible">
            <circle cx="10" cy="10" r="10" style="fill:red" />
            <rect x="10" y="10" width="15" height="15" style="fill:green"/>
        </pattern>

        <!-- ============================================== -->
        <!-- patternUnits : default (objectBoundingBox)     -->
        <!-- patternContentUnits : objectBoundingBox        -->
        <!-- viewBox : default (none)                       -->
        <!-- ============================================== -->
        <pattern id="pattern4" 
                 patternContentUnits="objectBoundingBox"
                 x="0" y="0" width=".75" height=".5" overflow="visible">
            <circle cx="0.0556%" cy=".33333333" r=".25" style="fill:red" />
            <rect x=".25" y=".33333333" width="0.375" height=".5" style="fill:green"/>
        </pattern>

        <!-- ============================================== -->
        <!-- patternUnits : objectBoundingBox               -->
        <!-- patternContentUnits : objectBoundingBox        -->
        <!-- viewBox : default (none)                       -->
        <!-- ============================================== -->
        <pattern id="pattern5" 
                 patternUnits="objectBoundingBox"
                 patternContentUnits="objectBoundingBox"
                 x="0" y="0" width=".75" height=".5">
            <circle cx=".25" cy=".33333333" r=".25" style="fill:red" />
            <rect x=".25" y=".33333333" width="0.375" height=".5" style="fill:green"/>
        </pattern>

        <!-- ============================================== -->
        <!-- patternUnits : userSpaceOnUse                  -->
        <!-- patternContentUnits : objectBoundingBox        -->
        <!-- viewBox : default (none)                       -->
        <!-- ============================================== -->
        <pattern id="pattern6" 
                 patternUnits="userSpaceOnUse" 
                 patternContentUnits="objectBoundingBox"
                 x="0" y="0" width="30" height="15">
            <circle cx=".25" cy=".33333333" r=".25" style="fill:red" />
            <rect x=".25" y=".33333333" width="0.375" height=".5" style="fill:green"/>
        </pattern>

        <!-- ============================================== -->
        <!-- patternUnits : userSpaceOnUse                  -->
        <!-- patternContentUnits : objectBoundingBox        -->
        <!-- viewBox : set                                  -->
        <!-- Note patternContentUnits is ignored if viewBox -->
        <!-- is set.                                        -->
        <!-- ============================================== -->
        <pattern id="pattern7" 
                 patternUnits="userSpaceOnUse" 
                 patternContentUnits="objectBoundingBox"
                 x="0" y="0" width="30" height="15" 
                 viewBox="10 10 40 30" preserveAspectRatio="none">
            <circle cx="20" cy="20" r="20" style="fill:red" />
        </pattern>

        <!-- ============================================== -->
        <!-- patternUnits : userSpaceOnUse                  -->
        <!-- patternContentUnits : objectBoundingBox        -->
        <!-- viewBox : set                                  -->
        <!-- Note patternContentUnits is ignored if viewBox -->
        <!-- is set.                                        -->
        <!-- ============================================== -->
        <pattern id="pattern8" 
                 patternUnits="userSpaceOnUse" 
                 patternContentUnits="objectBoundingBox"
                 x="0" y="0" width="30" height="15" 
                 viewBox="10 10 40 30" preserveAspectRatio="xMinYMin meet">
            <circle cx="20" cy="20" r="20" style="fill:red" />
        </pattern>

        <radialGradient id="redToYellow" >
            <stop offset="0" stop-color="red" />
            <stop offset="1" stop-color="yellow" />
        </radialGradient>

        <!-- ============================================== -->
        <!-- patternUnits : userSpaceOnUse                  -->
        <!-- patternContentUnits : userSpaceOnUse           -->
        <!-- viewBox : set                                  -->
        <!-- Note patternContentUnits is ignored if viewBox -->
        <!-- is set.                                        -->
        <!-- ============================================== -->
        <pattern id="pattern9" 
                 patternUnits="userSpaceOnUse" 
                 patternContentUnits="objectBoundingBox" overflow="visible"
                 x="0" y="0" width="30" height="15" viewBox="10 10 40 40" preserveAspectRatio="xMinYMin slice">
            <circle cx="20" cy="20" r="40" style="fill:url(#redToYellow)" />
        </pattern>

    </defs>

    <g transform="translate(-5, 100)">

    <g id="p1" transform="translate(20, 20)">
        <rect x="0" y="0" width="40" height="30" style="fill:url(#pattern1); stroke:black;"/>
        <text y="-2">#pattern1</text>
        <g transform="translate(50, 9)">
            <text x="0" y="0">patternUnits=default</text>
            <text x="0" y="10">contentUnits=default</text>
            <text x="0" y="20">no viewbox</text>
        </g>
    </g>

    <g id="p2" transform="translate(20, 70)">
        <rect x="0" y="0" width="40" height="30" style="fill:url(#pattern2); stroke:black;"/>
        <text y="-2">#pattern2</text>
        <g transform="translate(50, 9)">
            <text x="0" y="0">patternUnits=objectBoundingBox</text>
            <text x="0" y="10">contentUnits=default</text>
        </g>
    </g>

    <g id="p3" transform="translate(20, 120)">
        <rect x="0" y="0" width="40" height="30" style="fill:url(#pattern3); stroke:black;"/>
        <text y="-2">#pattern3</text>
        <g transform="translate(50, 9)">
            <text>patternUnits=userSpaceOnUse</text>
            <text y="10">contentUnits=default</text>
            <text y="20">overflow=visible</text>
        </g>
    </g>

    <g id="p4" transform="translate(20, 170)"> 
        <rect x="0" y="0" width="40" height="30" style="fill:url(#pattern4); stroke:black;"/>
        <text y="-2">#pattern4</text>
        <g transform="translate(50, 9)">
            <text>patternUnits=default</text>
            <text y="10">contentUnits=objectBoundingBox</text>
            <text y="20">overflow=visible</text>
        </g>
    </g>

    <g id="p5" transform="translate(20, 220)">
        <rect x="0" y="0" width="40" height="30" style="fill:url(#pattern5); stroke:black;"/>
        <text y="-2">#pattern5</text>
        <g transform="translate(50, 9)">
            <text>patternUnits=objectBoundingBox</text>
            <text y="10">contentUnits=objectBoundingBox</text>
        </g>
    </g>

    <g id="p6" transform="translate(20, 270)">
        <rect x="0" y="0" width="40" height="30" style="fill:url(#pattern6); stroke:black;"/>
        <text y="-2">#pattern6</text>
        <g transform="translate(50, 9)">
            <text>patternUnits=userSpaceOnUse</text>
            <text y="10">contentUnits=objectBoundingBox</text>
        </g>
    </g>

    <g id="p7" transform="translate(225, 20)">
        <rect x="0" y="0" width="40" height="30" style="fill:url(#pattern7); stroke:black;"/>
        <text y="-2">#pattern7</text>
        <g transform="translate(50, 9)">
            <text>patternUnits=userSpaceOnUse</text>
            <text y="10">contentUnits=objectBoundingBox</text>
            <text y="20">viewBox preserveAspectRatio=none</text>
        </g>
    </g>

    <g id="p8" transform="translate(225, 70)">
        <rect x="0" y="0" width="40" height="30" style="fill:url(#pattern8); stroke:black;"/>
        <text y="-2" >#pattern8</text>
        <g transform="translate(50, 9)">
            <text>same as pattern#7 with</text>
            <text y="10">preserveAspectRatio=xMinYmin meet</text>
        </g>
    </g>

    <g id="p9" transform="translate(225, 120)">
        <rect x="0" y="0" width="40" height="30" style="fill:url(#pattern9); stroke:black;"/>
        <text y="-2" >#pattern9</text>
        <g transform="translate(50, 9)">
            <text y="0">preserveAspectRatio=xMinYmin slice</text>
            <text y="10">overflow=visible</text>
        </g>
    </g>

    </g>
</g>
    <!-- ============================================================= -->
    <!-- Batik sample mark                                             -->
    <!-- ============================================================= -->
    <use xlink:href="../resources/batikLogo.svg#Batik_Tag_Box" />
    
</svg>
