{"version": 3, "file": "wrapString.js", "sourceRoot": "", "sources": ["../../src/wrapString.ts"], "names": [], "mappings": ";;;;;;AAAA,4DAA+B;AAC/B,gEAAuC;AAEvC;;;;;;;GAOG;AACI,MAAM,UAAU,GAAG,CAAC,OAAe,EAAE,IAAY,EAAY,EAAE;IACpE,IAAI,YAAY,GAAG,OAAO,CAAC;IAE3B,MAAM,MAAM,GAAa,EAAE,CAAC;IAE5B,GAAG;QACD,MAAM,CAAC,IAAI,CAAC,IAAA,oBAAK,EAAC,YAAY,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;QAE1C,YAAY,GAAG,IAAA,oBAAK,EAAC,YAAY,EAAE,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;KACjD,QAAQ,IAAA,sBAAW,EAAC,YAAY,CAAC,EAAE;IAEpC,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AAZW,QAAA,UAAU,cAYrB"}