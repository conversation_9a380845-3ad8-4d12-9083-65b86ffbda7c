<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

 <head>

  <title>CSS Writing Modes Test: 'float: left' and 'vertical-rl' - block flow direction of block-level boxes</title>

  <link rel="author" title="Gérard Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" />
  <link rel="help" href="http://www.w3.org/TR/css-writing-modes-3/#block-flow" title="3.1 Block Flow Direction: the writing-mode property" />
  <link rel="match" href="block-flow-direction-001-ref.xht" />

  <meta content="ahem" name="flags" />
  <meta content="This test checks that left-floated boxes with 'writing-mode' set to 'vertical-rl' establish block formating contexts with a right-to-left block flow direction." name="assert" />

  <link rel="stylesheet" type="text/css" href="/fonts/ahem.css" />
  <style type="text/css"><![CDATA[
  body
    {
      color: yellow;
      font: 20px/1 Ahem;
    }

  div.floated-left
    {
      background-color: blue;
      border-bottom: blue solid 1em;
      border-left: blue solid 1em;
      border-top: blue solid 1em;
      float: left;
      writing-mode: vertical-rl;
    }

  div#right-border
    {
      border-right: blue solid 1em;
    }
  ]]></style>
 </head>

 <body>

  <div class="floated-left">

<!-- The right-most line of "P" --> <div>eeee&nbsp;&nbsp;</div>

<!-- The 2nd right-most line of "P" --> <div>f&nbsp; g&nbsp;&nbsp;</div>

<!-- The 3rd right-most line of "P" --> <div>h&nbsp; j&nbsp;&nbsp;</div>

<!-- The 4th right-most line of "P" --> <div>kkkkkkk</div>

  </div>

  <div class="floated-left">

<!-- The right-most line of "A" --> <div>YYYYYYY</div>

<!-- The 2nd right-most line of "A" --> <div>Z&nbsp; a&nbsp;&nbsp;</div>

<!-- The 3rd right-most line of "A" --> <div>b&nbsp; c&nbsp;&nbsp;</div>

<!-- The 4th right-most line of "A" --> <div>ddddddd</div>

  </div>

  <div class="floated-left">

<!-- The right-most line of left-most "S" --> <div>L&nbsp; MMMM</div>

<!-- The 2nd right-most line of left-most "S" --> <div>Q&nbsp; R&nbsp; S</div>

<!-- The 3rd right-most line of left-most "S" --> <div>T&nbsp; U&nbsp; V</div>

<!-- The 4th right-most line of left-most "S" --> <div>WWWW&nbsp; X</div>

  </div>

  <div class="floated-left" id="right-border">

<!-- The right-most line of right-most "S" --> <div>A&nbsp; BBBB</div>

<!-- The 2nd right-most line of right-most "S" --> <div>C&nbsp; D&nbsp; E</div>

<!-- The 3rd right-most line of right-most "S" --> <div>F&nbsp; G&nbsp; H</div>

<!-- The 4th right-most line of right-most "S" --> <div>JJJJ&nbsp; K</div>

  </div>

 </body>
</html>