<!DOCTYPE html>
<script src="../../resources/run-after-layout-and-paint.js"></script>
<style>
.a.b .c {
  fill: red;
}
.a .c {
  fill: green;
}
</style>
<svg width="50" height="50">
  <use xlink:href="#main"></use>
</svg>
<svg id="main" width="100" height="100" class="a b">
  <g>
    <use xlink:href="#r"></use>
  </g>
  <defs>
    <rect id="r" class="c" x="0" y="0" width="100" height="100"></rect>
  </defs>
</svg>
<script>
runAfterLayoutAndPaint(function() {
  main.classList.remove('b');
}, true);
</script>
