# Copyright 2014 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/chromeos/ui_mode.gni")
import("//build/config/chromeos/ui_mode.gni")
import("//build/config/features.gni")
import("//mojo/public/tools/bindings/mojom.gni")

# On android, BatteryManager mojo interface is implemented directly in Java.
if (!is_android) {
  source_set("battery") {
    visibility = [
      "//services/device:lib",
      "//services/device:tests",
    ]

    sources = [
      "battery_monitor_impl.cc",
      "battery_monitor_impl.h",
      "battery_status_manager.h",
      "battery_status_service.cc",
      "battery_status_service.h",
    ]

    public_deps = [ "//services/device/public/mojom" ]

    deps = [
      "//base",
      "//base/third_party/dynamic_annotations",
      "//mojo/public/cpp/bindings",
    ]

    if (is_chromeos) {
      configs += [ "//build/config/linux/dbus" ]
      deps += [
        "//chromeos/dbus/power",
        "//chromeos/dbus/power:power_manager_proto",
      ]
      sources += [ "battery_status_manager_chromeos.cc" ]
    } else if (is_linux && use_dbus) {
      configs += [ "//build/config/linux/dbus" ]
      deps += [ "//dbus" ]
      sources += [
        "battery_status_manager_linux-inl.h",
        "battery_status_manager_linux.cc",
        "battery_status_manager_linux.h",
      ]
    } else if (is_mac) {
      sources += [ "battery_status_manager_mac.cc" ]
      frameworks = [
        "CoreFoundation.framework",
        "IOKit.framework",
      ]
    } else if (is_win) {
      sources += [
        "battery_status_manager_win.cc",
        "battery_status_manager_win.h",
      ]
    } else {
      sources += [ "battery_status_manager_default.cc" ]
    }
  }
}
