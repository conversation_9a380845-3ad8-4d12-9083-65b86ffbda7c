<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN"
"http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">

<!--

   Copyright 2001-2002  The Apache Software Foundation 

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

-->
<!-- ========================================================================= -->
<!-- Test description here                                                     -->
<!--                                                                           -->
<!-- <AUTHOR>                                      -->
<!-- @version $Id: textProperties.svg,v 1.6 2004/08/18 07:12:23 vhardy Exp $                                                             -->
<!-- ========================================================================= -->
<?xml-stylesheet type="text/css" href="../resources/test.css" ?>

<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="450" height="500" viewBox="0 0 450 500">
<title>Text Properties</title>

    <!-- ============================================================= -->
    <!-- Test content                                                  -->
    <!-- ============================================================= -->

    <defs>

     <style type="text/css"><![CDATA[
       .Caption { font-size: 8px; font-family: dialog; font-weight: normal;
                  font-style: normal; stroke: none; stroke-width: 0; 
                  fill: black } ]]>
     </style>

     <filter id="gaussianBlur"  filterUnits="userSpaceOnUse" x="-100" y="-100" width="200" height="200" filterRes="200">
        <feGaussianBlur stdDeviation="2 2" x="-100" y="-50" width="200" height="100"/> 
     </filter>

     <text x="0" y="0" text-anchor="middle" id="sample">sample</text>

    </defs>

    <g id="testContent" text-anchor="middle">
        <text class="title" x="50%" y="10%" font-size="15" >
            Text Alignment, Geometry, References, and Spans</text>

        <g font-size="15">
            <line x1="50%" y1="14%" x2="50%" y2="28%" stroke="Gray" stroke-width="1"/>
            <text x="50%"  y="18%" text-anchor="start" fill="MidnightBlue">
               text-anchor="start"</text>
            <text x="50%"  y="22%" text-anchor="middle" fill="DarkRed">
               text-anchor="middle"</text>
            <text x="50%"  y="26%" text-anchor="end" fill="ForestGreen">
               text-anchor="end"</text>
        </g>       


        <g transform="translate(100, 180)">
            <text font-size="1cm">sample</text>
            <text y="15" style="Caption">font-size="1cm"</text>
        </g>       

        <g transform="translate(225, 180)">
            <text font-size="200%">sample</text>
            <text y="15" style="Caption">font-size="200%"</text>
        </g>       

        <g transform="translate(350, 180)">
            <text font-size="30pt">sample</text>
            <text y="15" style="Caption">font-size="28"</text>
        </g>       

        <g transform="translate(100, 225)">
            <text font-size="30"><tref xlink:href="#sample"/></text>
            <text y="15" style="Caption">referenced text via &lt;tref&gt;</text>
        </g>       

        <g transform="translate(225, 225)">
            <text font-size="30">
            sa<tspan font-family="Serif" font-weight="bold" fill="DarkRed"
            text-decoration="underline" >m</tspan>ple
            </text>
            <text y="15" style="Caption">use of &lt;tspan&gt;</text>
        </g>       

        <g transform="translate(350, 225)">
            <g font-size="30">
                <use xlink:href="#sample"/>
            </g>
            <text y="15" style="Caption">predefined text via &lt;use&gt;</text>
        </g>       
 
        <text x="50%" y="280" font-size="15" >
            Text Rendering Properties</text>

        <g transform="translate(100, 305)">
            <g font-size="30" transform="rotate(180)">
                <use xlink:href="#sample"/>
            </g>
            <text y="30" style="Caption">transform="rotate(180)"</text>
        </g>       

        <g transform="translate(225, 320)">
            <g font-size="30" opacity="0.5">
                <use xlink:href="#sample"/>
            </g>
            <text y="15" style="Caption">opacity="0.5"</text>
        </g>       

        <g transform="translate(350, 320)">
            <g font-size="30" fill="DarkRed">
                <use xlink:href="#sample"/>
            </g>
            <text y="15" style="Caption">fill="DarkRed"</text>
        </g>       

        <g transform="translate(100, 370)">
            <g font-size="30" filter="url(#gaussianBlur)">
                <use xlink:href="#sample"/>
            </g>
            <text y="15" style="Caption">filter="url(#gaussianBlur)"</text>
        </g>       

        <g transform="translate(225, 370)">
            <g font-size="30" fill="yellow" stroke="MidnightBlue">
                <use xlink:href="#sample"/>
            </g>
            <text y="15" style="Caption">fill="yellow";</text>
            <text y="25" style="Caption">stroke="MidnightBlue"</text>
        </g>       

<defs>
    <clipPath id="textClip">
        <text font-size="30">sample</text>
    </clipPath>
    <linearGradient id="gradient" gradientUnits="userSpaceOnUse"
     x1="-50" y1="-25" x2="50" y2="25">
        <stop offset="0%" style="stop-color:blue"/>
        <stop offset="100%" style="stop-color:green"/>
    </linearGradient>
</defs>
        <g transform="translate(350, 370)">
            <rect x="-50" y="-25" width="100" height="50" 
               fill="url(#gradient)" 
               clip-path="url(#textClip)"/>
            <text y="15" style="Caption">text as clip-path</text>
        </g>       

        <text x="150" y="440" font-size="60" 
          fill="BlancheDalmond" stroke="DarkRed" stroke-width="2"
          stroke-linejoin="miter" stroke-linecap="square">
         a</text>
        <text x="150" y="455" style="Caption">stroke-linejoin="miter"</text>
        <text x="150" y="465" style="Caption">stroke-linecap="square"</text>

        <text x="300" y="440" font-size="60" 
          fill="BlancheDalmond" stroke="DarkRed" stroke-width="2"
          stroke-linejoin="round" stroke-linecap="round">
         a</text>
        <text x="300" y="455" style="Caption">stroke-linejoin="round"</text>
        <text x="300" y="465" style="Caption">stroke-linecap="round"</text>

    </g>

    <!-- ============================================================= -->
    <!-- Batik sample mark                                             -->
    <!-- ============================================================= -->
    <use xlink:href="../resources/batikLogo.svg#Batik_Tag_Box" />
    
</svg>
