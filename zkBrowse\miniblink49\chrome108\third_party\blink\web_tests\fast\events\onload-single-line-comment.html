<!DOCTYPE html>
<html>
<head>
<script type="text/javascript">
function loaded()
{
    document.getElementById("result").innerHTML = "PASS";
    if (window.testRunner)
        testRunner.notifyDone();
}

if (window.testRunner) {
    testRunner.waitUntilDone();
    testRunner.dumpAsText()    
}
</script>
</head>
<body onload="loaded();//">
<p>This test checks whether single line comments are parsed correctly in event handlers.
<div id="result">FAIL</div>
</body>
</html>
