<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="800" height="600">
	<defs>
		<marker id="turn0.2" refY="5" viewBox="0 0 10 10" orient="72deg">
			<path d="M0 0l10 5 -10 5z"/>
		</marker>
		<marker id="turn0.5" refY="5" viewBox="0 0 10 10" orient="180deg">
			<path d="M0 0l10 5 -10 5z"/>
		</marker>
		<marker id="turn1" refY="5" viewBox="0 0 10 10" orient="360deg">
			<path d="M0 0l10 5 -10 5z"/>
		</marker>
		<marker id="turn1.7" refY="5" viewBox="0 0 10 10" orient="612deg">
			<path d="M0 0l10 5 -10 5z"/>
		</marker>
		<style>
			.test { 
				stroke: black;
				stroke-width: 10px;
			}
			
			#t1 { marker-end: url(#turn0.2); }
			#t2 { marker-end: url(#turn0.5); }
			#t3 { marker-end: url(#turn1); }
			#t4 { marker-end: url(#turn1.7); }
		</style>
	</defs>

	<path id="t1" class="test" d="M10 40h200" />
	<path id="t2" class="test" d="M10 80h200" />
	<path id="t3" class="test" d="M10 120h200" />
	<path id="t4" class="test" d="M10 160h200" />
</svg>
