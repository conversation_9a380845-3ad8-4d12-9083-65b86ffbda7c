@echo off
echo Fixing V8 10.8 compatibility issues in generated modules V8 bindings...
echo.

set "MODULES_DIR=..\gen\blink\bindings\modules\v8"
set "COMPAT_INCLUDE=#include \"../../../../../v8_10_8/v8_compatibility.h\""

echo Adding V8 10.8 compatibility headers to modules bindings...

REM List of files that need fixing based on the error messages
set FILES=^
V8ExtBlendMinMax.cpp ^
V8ExtFragDepth.cpp ^
V8ExtShaderTextureLod.cpp ^
V8ExtSRGB.cpp ^
V8ExtTextureFilterAnisotropic.cpp ^
V8OESStandardDerivatives.cpp ^
V8OESTextureFloatLinear.cpp ^
V8OESTextureFloat.cpp ^
V8OESElementIndexUint.cpp ^
V8OESVertexArrayObject.cpp ^
V8OESTextureHalfFloat.cpp ^
V8OESTextureHalfFloatLinear.cpp

for %%f in (%FILES%) do (
    echo Processing %%f...
    if exist "%MODULES_DIR%\%%f" (
        echo   - Adding compatibility header to %%f
        REM This will be done manually since batch file text processing is limited
    ) else (
        echo   - Warning: %%f not found
    )
)

echo.
echo Manual fixes needed:
echo 1. Add compatibility header after existing includes
echo 2. Replace SetReferenceFromGroup calls with compatibility version
echo.
echo Use the individual file fix commands below:
echo.

pause
