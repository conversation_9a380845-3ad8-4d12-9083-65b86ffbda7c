<!DOCTYPE html>
<meta charset="utf-8">
<title>TestDriver actions: element timing</title>
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<script src="/resources/testdriver.js"></script>
<script src="/resources/testdriver-actions.js"></script>
<script src="/resources/testdriver-vendor.js"></script>

<style>
div#test1, div#test2 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100px;
  height: 100px;
  background-color: blue;
}

div#test2 {
  display: none;
  left: -100px;
  background-color: green;
}
</style>

<div id="test1">
</div>

<div id="test2">
</div>

<script>
let events = [];

promise_test(async t => {
  let test1 = document.getElementById("test1");
  let test2 = document.getElementById("test2");
  test1.addEventListener("click",
    () => {
      test2.style.display = "block";
      test2.style.top = "100px";
      test2.style.left = "0"
    });
  test2.addEventListener("click",
    e => {
      events.push(e.clientX);
      events.push(e.clientY);
    });

  const waitCondition = new Promise((resolve, reject)=>{setTimeout(resolve, 5000);});
  const test1ClickWatcher = new EventWatcher(t, test1, ["click"], ()=>waitCondition);
  const test2ClickWatcher = new EventWatcher(t, test2, ["click"], ()=>waitCondition);
  let waitForClicks = Promise.all([test1ClickWatcher.wait_for(["click"]), test2ClickWatcher.wait_for(["click"])]);

  await new test_driver.Actions()
    .pointerMove(0, 0, {origin: test1})
    .pointerDown()
    .pointerUp()
    .send();
  await new test_driver.Actions()
      .pointerMove(0, 0, {origin: test2})
      .pointerDown()
      .pointerUp()
      .send();
  await waitForClicks;
  assert_array_equals(events, [50, 150])
});
</script>
