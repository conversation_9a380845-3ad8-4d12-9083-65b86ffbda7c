Tests that the content of resources with JSON MIME types can be accessed.
When loaded by asynchronous XHR requests (Bug 80684) or within iframes/documents.


Running: test1
request.url: http://127.0.0.1:8000/devtools/network/resources/json.php?type=application%2Fjson
request.type: xhr
request.mimeType: application/json
request.content: {"number": "42"}

request.url: http://127.0.0.1:8000/devtools/network/resources/json.php?type=application%2Fjson
request.type: document
request.mimeType: application/json
request.content: {"number": "42"}


Running: test2
request.url: http://127.0.0.1:8000/devtools/network/resources/json.php?type=application%2Fvnd.document%2Bjson
request.type: xhr
request.mimeType: application/vnd.document+json
request.content: {"number": "42"}

request.url: http://127.0.0.1:8000/devtools/network/resources/json.php?type=application%2Fvnd.document%2Bjson
request.type: document
request.mimeType: application/vnd.document+json
request.content: {"number": "42"}


