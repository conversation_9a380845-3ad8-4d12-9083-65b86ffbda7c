Tests calling cypto.subtle.importKey with bad parameters

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".

error is: TypeError: Key data must be a BufferSource for non-JWK formats
error is: TypeError: Key data must be a BufferSource for non-JWK formats
error is: NotSupportedError: Failed to execute 'importKey' on 'SubtleCrypto': Algorithm: Unrecognized name
error is: TypeError: Invalid keyFormat argument
error is: TypeError: Invalid keyUsages argument
error is: TypeError: Invalid keyFormat argument
error is: TypeError: Failed to execute 'importKey' on 'SubtleCrypto': HmacImportParams: hash: Missing or not an AlgorithmIdentifier
error is: NotSupportedError: Failed to execute 'importKey' on 'SubtleCrypto': SHA-1: Unsupported operation: importKey
error is: TypeError: Failed to execute 'importKey' on 'SubtleCrypto': The provided ArrayBufferView value must not be shared.
PASS successfullyParsed is true

TEST COMPLETE

