<!DOCTYPE html>
<html>
<head>
<script src="../resources/js-test.js"></script>
<script src="subtle/resources/common.js"></script>
</head>
<body>
<p id="description"></p>
<div id="console"></div>

<script>
description("Test that an ArrayBufferView with offset is processed correctly.");

jsTestIsAsync = true;

Promise.resolve(null).then(function() {
    var originalData = new Uint8Array([0xf, 0xf, 0xf, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 0xf, 0xf, 0xf]);
    var slicedData = new Uint8Array(originalData.buffer, 3, 11);
    return crypto.subtle.digest({name: 'sha-1'}, slicedData);
}).then(function(result) {
    digest = result;
    // Expected result for [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10].
    shouldBe("bytesToHexString(new Uint8Array(digest))", "'2c7e7c384f7829694282b1e3a6216def8082d055'");
    finishJSTest();
});
</script>

</body>
</html>
