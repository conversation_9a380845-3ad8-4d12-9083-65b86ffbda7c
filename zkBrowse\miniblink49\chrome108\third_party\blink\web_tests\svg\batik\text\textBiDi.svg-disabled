<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN"
"http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">

<!--

   Copyright 2001-2003  The Apache Software Foundation 

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

-->
<!-- ========================================================================= -->
<!-- Test description here                                                     -->
<!--                                                                           -->
<!-- <AUTHOR>                                      -->
<!-- @version $Id: textBiDi.svg,v 1.7 2004/08/18 07:12:21 vhardy Exp $                                                             -->
<!-- ========================================================================= -->
<?xml-stylesheet type="text/css" href="../resources/test.css" ?>

<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="450" height="500" viewBox="0 0 450 500">
    <!-- ============================================================= -->
    <!-- Test content                                                  -->
    <!-- ============================================================= -->

    <g id="testContent">
        <text class="title" x="50%" y="10%" font-size="15" text-anchor="middle" >
            BiDi Text / International Text</text>
        <text x="50%" y="25%" font-size="12.5" text-anchor="middle">Some text goes <tspan fill="blue" direction="rtl" unicode-bidi="bidi-override">(right to left)</tspan>, other text goes (left to right).</text>
      
        <text x="50%" y="30%" font-size="12.5" text-anchor="middle">Some <tspan fill="darkred" direction="rtl" unicode-bidi="embed">(embedded bidi)</tspan> text.</text>
     
      <text style="font-style:oblique;font-size:10px;text-anchor:middle" x="50%" y="35%">
               Text selection allows visually discontiguous 
               selections across bi-directional text.
        </text>
      <g font-family="dialog" font-size="15" text-anchor="start">
        <text id="latin" fill="MidnightBlue" x="10%" y="45%">latin:
         ABCDEFG
         abcdefghijklmnopqrstuvwxyz</text> 
        <text id="latin-extended" fill="Sienna" x="10%" y="50%">latin-extended: 
         &#x0e6;&#x0e7;&#x0e8;&#x0e9;&#x0ea;&#x0eb;&#x0ec;&#x0ed;&#x0ee;&#x0ef;&#x0f0;&#x0f1;&#x0f2;&#x0f3;&#x0f4;</text>
        <text id="cyrillic" fill="Indigo" x="10%" y="55%">cyrillic: 
         &#x411;&#x412;&#x413;&#x414;&#x415;&#x416;&#x417;&#x418;&#x419;&#x41a;&#x41b;&#x41c;&#x41d;&#x41e;&#x41f;&#x420;&#x421;&#x422;&#x423;&#x424;&#x425;&#x426;&#x427;</text>
        <text id="greek" fill="Maroon" x="10%" y="60%">greek:
         &#x391;&#x392;&#x393;&#x394; &#x3b1;&#x3b2;&#x3b3;&#x3b4;&#x3b5;&#x3b6;&#x3b7;&#x3b8;&#x3b9;&#x3ba;&#x3bb;&#x3bc;&#x3bd;&#x3be;&#x3bf;&#x3c0;&#x3c1;&#x3c2;&#x3c3;&#x3c4;&#x3c5;&#x3c6;&#x3c7;&#x3c8;&#x3c9;</text> 
        <text id="hebrew" fill="DarkOliveGreen" x="10%" y="65%">hebrew: 
         &#x5d0;&#x5d1;&#x5d2;&#x5d3;&#x5d4;&#x5d5;&#x5d6;&#x5d7;&#x5d8;&#x5d9;&#x5da;&#x5db;&#x5dc;&#x5dd;&#x5de;&#x5df;&#x5e0;&#x5e1;&#x5e2;&#x5e3;&#x5e4;&#x5e5;&#x5e6;&#x5e7;&#x5e8;&#x5e9;&#x5ea;</text>
        <text fill="DarkOliveGreen" x="10%" y="70%" 
	 >&#x5d0;&#x5d1;&#x5d2;&#x5d3;&#x5d4;&#x5d5;&#x5d6;&#x5d7;&#x5d8;&#x5d9;&#x5da;&#x5db;&#x5dc;&#x5dd;&#x5de;&#x5df;&#x5e0;&#x5e1;&#x5e2;&#x5e3;&#x5e4;&#x5e5;&#x5e6;&#x5e7;&#x5e8;&#x5e9;&#x5ea;</text>
        <text id="arabic" fill="DarkRed" x="10%" y="75%">arabic: 
         &#x621;&#x623;&#x624;&#x625;&#x626;&#x627;&#x628;&#x629;&#x62a;&#x62b;&#x62c;&#x62d;&#x62e;&#x62f;&#x630;&#x631;&#x632;&#x633;&#x634;&#x635;&#x636;&#x637;&#x638;&#x639;&#x640;&#x641;&#x642;&#x643;&#x644;&#x645;&#x646;&#x647;&#x648;&#x649;&#x650;&#x676;</text>
        <text fill="DarkRed" x="10%" y="80%"
	 >&#x621;&#x623;&#x624;&#x625;&#x626;&#x627;&#x628;&#x629;&#x62a;&#x62b;&#x62c;&#x62d;&#x62e;&#x62f;&#x630;&#x631;&#x632;&#x633;&#x634;&#x635;&#x636;&#x637;&#x638;&#x639;&#x640;&#x641;&#x642;&#x643;&#x644;&#x645;&#x646;&#x647;&#x648;&#x649;&#x650;&#x676;</text>
      </g>
    </g>

    <!-- ============================================================= -->
    <!-- Batik sample mark                                             -->
    <!-- ============================================================= -->
    <use xlink:href="../resources/batikLogo.svg#Batik_Tag_Box" />
    
</svg>
