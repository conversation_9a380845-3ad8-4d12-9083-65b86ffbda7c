<!DOCTYPE html>
<meta charset=utf-8>
<title>IDBCursor direction - index with keyrange</title>
<link rel="author" href="mailto:<EMAIL>" title="Odin <PERSON> O<PERSON>">
<link rel=help href="http://dvcs.w3.org/hg/IndexedDB/raw-file/tip/Overview.html#cursor-iteration-operation">
<link rel=assert title='If direction is "next", let found record be the first record in records which satisfy all of the following requirements'>
<link rel=assert title="If position is defined, and source is an index, the record's key is equal to position and the record's value is greater than object store position or the record's key is greater than position.">
<link rel=assert title='If direction is "prev", let found record be the last record in records which satisfy all of the following requirements'>
<link rel=assert title="If position is defined, and source is an index, the record's key is equal to position and the record's value is less than object store position or the record's key is less than position.">
<link rel=assert title="If range is defined, the record's key is in range.">
<link rel=assert title="If temp record is defined, let found record be the first record in records whose key is equal to temp record's key.">
<link rel=assert title="records is always sorted in ascending key order. In the case of source being an index, records is secondarily sorted in ascending value order.">
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<script src="resources/support.js"></script>

<script>
var records = [ 1337, "Alice", "Bob", "Bob", "Greg", "Åke", ["Anne"] ];
var cases = [
  {dir: 'next', expect: ['Alice:1', 'Bob:2', 'Bob:3', 'Greg:4']},
  {dir: 'prev', expect: ['Greg:4',  'Bob:3', 'Bob:2', 'Alice:1']},
  {dir: 'nextunique', expect: ['Alice:1', 'Bob:2', 'Greg:4']},
  {dir: 'prevunique', expect: ['Greg:4',  'Bob:2', 'Alice:1']}
];


cases.forEach(function(testcase) {
  var dir = testcase.dir;
  var expect = testcase.expect;
  indexeddb_test(
    function(t, db, tx) {
      var objStore = db.createObjectStore("test");
      objStore.createIndex("idx", "name");

      for (var i = 0; i < records.length; i++)
        objStore.add({ name: records[i] }, i);
    },
    function(t, db) {
      var count = 0;
      var rq = db.transaction("test").objectStore("test").index("idx").openCursor(IDBKeyRange.bound("AA", "ZZ"), dir);
      rq.onsuccess = t.step_func(function(e) {
        var cursor = e.target.result;
        if (!cursor) {
          assert_equals(count, expect.length, "cursor runs");
          t.done();
        }
        assert_equals(cursor.value.name + ":" + cursor.primaryKey, expect[count], "cursor.value");
        count++;
        cursor.continue();
      });
      rq.onerror = t.step_func(function(e) {
        e.preventDefault();
        e.stopPropagation();
        assert_unreached("rq.onerror - " + e.message);
      });
    },
    document.title + ' - ' + dir
  )
});
</script>
