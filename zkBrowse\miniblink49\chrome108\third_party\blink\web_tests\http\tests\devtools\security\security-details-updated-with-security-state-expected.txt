Tests that the security details for an origin are updated if its security state changes.

Sidebar Origins --------------------------------
Group: Main origin
Group: Secure origins
<SPAN >
    <SPAN class=url-scheme-unknown >
https
    </SPAN>
    <SPAN class=url-scheme-separator >
://
    </SPAN>
    <SPAN >
foo.test
    </SPAN>
</SPAN>
<SPAN class=url-scheme-unknown >
https
</SPAN>
<SPAN class=url-scheme-separator >
://
</SPAN>
<SPAN >
foo.test
</SPAN>
<SPAN >
    <SPAN class=url-scheme-secure >
https
    </SPAN>
    <SPAN class=url-scheme-separator >
://
    </SPAN>
    <SPAN >
ecdhe.foo.test
    </SPAN>
</SPAN>
<SPAN class=url-scheme-secure >
https
</SPAN>
<SPAN class=url-scheme-separator >
://
</SPAN>
<SPAN >
ecdhe.foo.test
</SPAN>
<SPAN >
    <SPAN class=url-scheme-secure >
https
    </SPAN>
    <SPAN class=url-scheme-separator >
://
    </SPAN>
    <SPAN >
tls13.foo.test
    </SPAN>
</SPAN>
<SPAN class=url-scheme-secure >
https
</SPAN>
<SPAN class=url-scheme-separator >
://
</SPAN>
<SPAN >
tls13.foo.test
</SPAN>
<SPAN >
    <SPAN class=url-scheme-secure >
https
    </SPAN>
    <SPAN class=url-scheme-separator >
://
    </SPAN>
    <SPAN >
ech.foo.test
    </SPAN>
</SPAN>
<SPAN class=url-scheme-secure >
https
</SPAN>
<SPAN class=url-scheme-separator >
://
</SPAN>
<SPAN >
ech.foo.test
</SPAN>
Group: Unknown / canceled
<SPAN >
    <SPAN class=url-scheme-unknown >
https
    </SPAN>
    <SPAN class=url-scheme-separator >
://
    </SPAN>
    <SPAN >
bar.test
    </SPAN>
</SPAN>
<SPAN class=url-scheme-unknown >
https
</SPAN>
<SPAN class=url-scheme-separator >
://
</SPAN>
<SPAN >
bar.test
</SPAN>
Origin view (https://foo.test) -----------------
<DIV class=widget vbox security-origin-view slot=insertion-point-main >
    <DIV class=title-section >
        <DIV class=title-section-header role=heading aria-level=1 >
Origin
        </DIV>
        <DIV class=origin-display >
            <SPAN class=security-property security-property-secure >
            </SPAN>
            <SPAN >
                <SPAN class=url-scheme-secure >
https
                </SPAN>
                <SPAN class=url-scheme-separator >
://
                </SPAN>
                <SPAN >
foo.test
                </SPAN>
            </SPAN>
        </DIV>
        <DIV class=view-network-button >
            <BUTTON class=text-button type=button role=link >
View requests in Network Panel
            </BUTTON>
        </DIV>
    </DIV>
    <DIV class=origin-view-section >
        <DIV class=origin-view-section-title role=heading aria-level=2 >
Connection
        </DIV>
        <TABLE class=details-table >
            <TR class=details-table-row >
                <TD >
Protocol
                </TD>
                <TD >
TLS 1.2
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
Key exchange
                </TD>
                <TD >
Key_Exchange
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
Cipher
                </TD>
                <TD >
Cypher with Mac
                </TD>
            </TR>
        </TABLE>
    </DIV>
    <DIV class=origin-view-section >
        <DIV class=origin-view-section-title role=heading aria-level=2 >
Certificate
        </DIV>
        <TABLE class=details-table >
            <TR class=details-table-row >
                <TD >
Subject
                </TD>
                <TD >
foo.test
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
SAN
                </TD>
                <TD >
                    <DIV >
                        <SPAN class=san-entry >
foo.test
                        </SPAN>
                        <SPAN class=san-entry >
*.test
                        </SPAN>
                    </DIV>
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
Valid from
                </TD>
                <TD >
Mon, 20 Mar 2017 08:53:20 GMT
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
Valid until
                </TD>
                <TD >
Wed, 18 May 2033 03:33:20 GMT
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
Issuer
                </TD>
                <TD >
Super CA
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
                </TD>
                <TD >
                    <BUTTON class=origin-button text-button type=button role=button >
Open full certificate details
                    </BUTTON>
                </TD>
            </TR>
        </TABLE>
    </DIV>
</DIV>
Origin view (https://ecdhe.foo.test) -----------
<DIV class=widget vbox security-origin-view slot=insertion-point-main >
    <DIV class=title-section >
        <DIV class=title-section-header role=heading aria-level=1 >
Origin
        </DIV>
        <DIV class=origin-display >
            <SPAN class=security-property security-property-secure >
            </SPAN>
            <SPAN >
                <SPAN class=url-scheme-secure >
https
                </SPAN>
                <SPAN class=url-scheme-separator >
://
                </SPAN>
                <SPAN >
ecdhe.foo.test
                </SPAN>
            </SPAN>
        </DIV>
        <DIV class=view-network-button >
            <BUTTON class=text-button type=button role=link >
View requests in Network Panel
            </BUTTON>
        </DIV>
    </DIV>
    <DIV class=origin-view-section >
        <DIV class=origin-view-section-title role=heading aria-level=2 >
Connection
        </DIV>
        <TABLE class=details-table >
            <TR class=details-table-row >
                <TD >
Protocol
                </TD>
                <TD >
TLS 1.2
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
Key exchange
                </TD>
                <TD >
ECDSA_RSA with X25519
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
Server signature
                </TD>
                <TD >
RSA-PSS with SHA-256
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
Cipher
                </TD>
                <TD >
AES-128-GCM
                </TD>
            </TR>
        </TABLE>
    </DIV>
    <DIV class=origin-view-section >
        <DIV class=origin-view-section-title role=heading aria-level=2 >
Certificate
        </DIV>
        <TABLE class=details-table >
            <TR class=details-table-row >
                <TD >
Subject
                </TD>
                <TD >
ecdhe.foo.test
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
SAN
                </TD>
                <TD >
                    <DIV >
                        <SPAN class=san-entry >
ecdhe.foo.test
                        </SPAN>
                    </DIV>
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
Valid from
                </TD>
                <TD >
Mon, 20 Mar 2017 08:53:20 GMT
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
Valid until
                </TD>
                <TD >
Wed, 18 May 2033 03:33:20 GMT
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
Issuer
                </TD>
                <TD >
Super CA
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
                </TD>
                <TD >
                    <BUTTON class=origin-button text-button type=button role=button >
Open full certificate details
                    </BUTTON>
                </TD>
            </TR>
        </TABLE>
    </DIV>
</DIV>
Origin view (https://tls13.foo.test) -----------
<DIV class=widget vbox security-origin-view slot=insertion-point-main >
    <DIV class=title-section >
        <DIV class=title-section-header role=heading aria-level=1 >
Origin
        </DIV>
        <DIV class=origin-display >
            <SPAN class=security-property security-property-secure >
            </SPAN>
            <SPAN >
                <SPAN class=url-scheme-secure >
https
                </SPAN>
                <SPAN class=url-scheme-separator >
://
                </SPAN>
                <SPAN >
tls13.foo.test
                </SPAN>
            </SPAN>
        </DIV>
        <DIV class=view-network-button >
            <BUTTON class=text-button type=button role=link >
View requests in Network Panel
            </BUTTON>
        </DIV>
    </DIV>
    <DIV class=origin-view-section >
        <DIV class=origin-view-section-title role=heading aria-level=2 >
Connection
        </DIV>
        <TABLE class=details-table >
            <TR class=details-table-row >
                <TD >
Protocol
                </TD>
                <TD >
TLS 1.3
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
Key exchange
                </TD>
                <TD >
X25519
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
Server signature
                </TD>
                <TD >
RSA-PSS with SHA-256
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
Cipher
                </TD>
                <TD >
AES-128-GCM
                </TD>
            </TR>
        </TABLE>
    </DIV>
    <DIV class=origin-view-section >
        <DIV class=origin-view-section-title role=heading aria-level=2 >
Certificate
        </DIV>
        <TABLE class=details-table >
            <TR class=details-table-row >
                <TD >
Subject
                </TD>
                <TD >
tls13.foo.test
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
SAN
                </TD>
                <TD >
                    <DIV >
                        <SPAN class=san-entry >
tls13.foo.test
                        </SPAN>
                    </DIV>
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
Valid from
                </TD>
                <TD >
Mon, 20 Mar 2017 08:53:20 GMT
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
Valid until
                </TD>
                <TD >
Wed, 18 May 2033 03:33:20 GMT
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
Issuer
                </TD>
                <TD >
Super CA
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
                </TD>
                <TD >
                    <BUTTON class=origin-button text-button type=button role=button >
Open full certificate details
                    </BUTTON>
                </TD>
            </TR>
        </TABLE>
    </DIV>
</DIV>
Origin view (https://ech.foo.test) -------------
<DIV class=widget vbox security-origin-view slot=insertion-point-main >
    <DIV class=title-section >
        <DIV class=title-section-header role=heading aria-level=1 >
Origin
        </DIV>
        <DIV class=origin-display >
            <SPAN class=security-property security-property-secure >
            </SPAN>
            <SPAN >
                <SPAN class=url-scheme-secure >
https
                </SPAN>
                <SPAN class=url-scheme-separator >
://
                </SPAN>
                <SPAN >
ech.foo.test
                </SPAN>
            </SPAN>
        </DIV>
        <DIV class=view-network-button >
            <BUTTON class=text-button type=button role=link >
View requests in Network Panel
            </BUTTON>
        </DIV>
    </DIV>
    <DIV class=origin-view-section >
        <DIV class=origin-view-section-title role=heading aria-level=2 >
Connection
        </DIV>
        <TABLE class=details-table >
            <TR class=details-table-row >
                <TD >
Protocol
                </TD>
                <TD >
TLS 1.3
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
Key exchange
                </TD>
                <TD >
X25519
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
Server signature
                </TD>
                <TD >
RSA-PSS with SHA-256
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
Cipher
                </TD>
                <TD >
AES-128-GCM
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
Encrypted ClientHello
                </TD>
                <TD >
enabled
                </TD>
            </TR>
        </TABLE>
    </DIV>
    <DIV class=origin-view-section >
        <DIV class=origin-view-section-title role=heading aria-level=2 >
Certificate
        </DIV>
        <TABLE class=details-table >
            <TR class=details-table-row >
                <TD >
Subject
                </TD>
                <TD >
ech.foo.test
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
SAN
                </TD>
                <TD >
                    <DIV >
                        <SPAN class=san-entry >
ech.foo.test
                        </SPAN>
                    </DIV>
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
Valid from
                </TD>
                <TD >
Mon, 20 Mar 2017 08:53:20 GMT
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
Valid until
                </TD>
                <TD >
Wed, 18 May 2033 03:33:20 GMT
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
Issuer
                </TD>
                <TD >
Super CA
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
                </TD>
                <TD >
                    <BUTTON class=origin-button text-button type=button role=button >
Open full certificate details
                    </BUTTON>
                </TD>
            </TR>
        </TABLE>
    </DIV>
</DIV>

