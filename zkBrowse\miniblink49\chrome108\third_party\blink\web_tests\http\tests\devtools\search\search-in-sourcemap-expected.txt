Tests single resource search in inspector page agent.


Searching for: "color: blue"
Search result #1: uiSourceCode.url = http://127.0.0.1:8000/devtools/search/resources/sourcemap-sass.scss
  search match #1: lineNumber = 1, lineContent = '    color: blue;'
Search result #2: uiSourceCode.url = http://127.0.0.1:8000/devtools/search/resources/sourcemap-style.css
  search match #1: lineNumber = 1, lineContent = '    color: blue;'

Searching for: "window.foo"
Search result #1: uiSourceCode.url = http://127.0.0.1:8000/devtools/search/resources/sourcemap-script.js
  search match #1: lineNumber = 3, lineContent = 'window.foo = console.log.bind(console, 'foo');'
Search result #2: uiSourceCode.url = http://127.0.0.1:8000/devtools/search/resources/sourcemap-typescript.ts
  search match #1: lineNumber = 3, lineContent = 'window.foo = console.log.bind(console, 'foo');'

