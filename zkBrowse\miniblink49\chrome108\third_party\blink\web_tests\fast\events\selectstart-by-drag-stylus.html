<!DOCTYPE HTML>
<script src="../../resources/testharness.js"></script>
<script src="../../resources/testharnessreport.js"></script>
<style>
  #content {
    font-size: 50px;
    padding: 10px;
  }
</style>
<span id="content">hello</span>
<script>
var span = document.getElementById('content');
span.focus();

test(function() {
  internals.settings.setBarrelButtonForDragEnabled(true);
  eventSender.dragMode = false;
  var y = span.offsetTop + span.offsetHeight / 2; 
  eventSender.mouseMoveTo(span.offsetLeft + 5, y, "", "pen");
  eventSender.mouseDown(2, "", "pen");
  eventSender.mouseMoveTo(span.offsetLeft + 50, y, "", "pen");
  // Allow right click (barrel) drag for selection. On other
  // platforms the context menu occurs on the mouse down so it
  // won't start a selection.
  const isWin = navigator.platform.indexOf('Win') == 0;
  assert_equals(!isWin, document.getSelection().isCollapsed);
}, "Selection caused by barrel button");

</script>