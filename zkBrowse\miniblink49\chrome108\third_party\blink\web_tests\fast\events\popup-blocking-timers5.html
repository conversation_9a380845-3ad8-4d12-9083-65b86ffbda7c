<!DOCTYPE html>
<head>
    <script src="../../resources/js-test.js"></script>
    <script src="../../resources/testdriver.js"></script>
    <script src="../../resources/testdriver-vendor.js"></script>
    <script>
        var newWindow;

        if (window.testRunner) {
            testRunner.dumpAsText();
            testRunner.waitUntilDone();
        }

        function clickHandler() {
            setTimeout(function() {
                newWindow = window.open("about:blank");
                self.focus();
                debug("Test calling window.open() with a 900 ms delay. A popup should be allowed.")
                shouldBeNonNull("newWindow");
                if (window.testRunner)
                    testRunner.notifyDone();
            }, 900);

            if (window.eventSender)
                eventSender.leapForward(900);
        }

        function clickButton() {
            var button = document.getElementById("test");
            test_driver.click(button);
        }
    </script>
</head>
<body onload="clickButton()">
    <button id="test" onclick="clickHandler()">Click Here</button>
    <div id="console"></div>
</body>
