// Copyright 2018 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

module audio.mojom;

// This interface is exposed by the audio service to allow tests to control
// the service's behavior. This interface should be exposed only in testing
// environments.
interface TestingApi {
  // Crash the audio-service process.
  Crash();
};
