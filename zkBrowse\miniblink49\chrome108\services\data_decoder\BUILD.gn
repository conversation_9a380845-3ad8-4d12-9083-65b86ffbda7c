# Copyright 2016 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/chromeos/ui_mode.gni")
import("//testing/libfuzzer/fuzzer_test.gni")

source_set("lib") {
  # This target should only be depeneded upon by components that host a
  # data_decoder service. Consumers should depend on a a target in the
  # public/ directory.
  visibility = [
    ":*",
    "public/cpp:*",
    "//chrome/utility",
    "//content/utility",
  ]

  sources = [
    "data_decoder_service.cc",
    "data_decoder_service.h",
    "gzipper.cc",
    "gzipper.h",
    "json_parser_impl.cc",
    "json_parser_impl.h",
    "web_bundler.cc",
    "web_bundler.h",
    "xml_parser.cc",
    "xml_parser.h",
  ]

  if (is_chromeos_ash) {
    sources += [
      "ble_scan_parser_impl.cc",
      "ble_scan_parser_impl.h",
    ]
  }

  configs += [ "//build/config/compiler:wexit_time_destructors" ]

  deps = [
    "//base",
    "//build:chromeos_buildflags",
    "//components/web_package",
    "//mojo/public/cpp/bindings",
    "//net",
    "//skia",
    "//third_party/libxml:libxml_utils",
    "//third_party/libxml:xml_reader",
    "//third_party/zlib/google:compression_utils",
    "//ui/gfx",
    "//ui/gfx/geometry",
  ]

  public_deps = [ "//services/data_decoder/public/mojom" ]

  if (!is_ios) {
    sources += [
      "image_decoder_impl.cc",
      "image_decoder_impl.h",
    ]

    deps += [ "//third_party/blink/public:blink" ]
  }
}

source_set("tests") {
  testonly = true

  sources = [
    "gzipper_unittest.cc",
    "public/cpp/data_decoder_unittest.cc",
    "public/cpp/json_sanitizer_unittest.cc",
    "public/cpp/safe_web_bundle_parser_unittest.cc",
    "public/cpp/safe_xml_parser_unittest.cc",
    "xml_parser_unittest.cc",
  ]

  if (is_chromeos_ash) {
    sources += [ "ble_scan_parser_impl_unittest.cc" ]
  }

  deps = [
    ":lib",
    "//base",
    "//base/test:test_support",
    "//services/data_decoder/public/cpp",
    "//services/data_decoder/public/cpp:test_support",
    "//skia",
    "//testing/gtest",
    "//ui/gfx",
  ]

  data = [ "//components/test/data/web_package/" ]

  if (!is_ios) {
    sources += [ "image_decoder_impl_unittest.cc" ]
    deps += [
      "//gin",
      "//gin:gin_test",
      "//third_party/blink/public:blink",
    ]
    data_deps = [ "//tools/v8_context_snapshot" ]
    configs += [
      "//tools/v8_context_snapshot:use_v8_context_snapshot",
      "//v8:external_startup_data",
    ]
  }
}

# This is a separate target because //third_party/libxml:libxml_utils
# wants to limit its visibility to a select number of target, but the
# "fuzzer_test" template expands on iOS to many targets that need to
# have the "deps" information propagated in order to support "fat"
# binaries.
source_set("xml_parser_fuzzer_deps") {
  visibility = [
    ":run_xml_parser_fuzzer",
    ":xml_parser_fuzzer",
    ":xml_parser_fuzzer_arch_executable",
    ":xml_parser_fuzzer_arch_executable_sources",
  ]
  public_deps = [
    ":lib",
    "//base",
    "//third_party/libxml:libxml_utils",
    "//third_party/libxml:xml_reader",
  ]
}

fuzzer_test("xml_parser_fuzzer") {
  sources = [ "xml_parser_fuzzer.cc" ]
  deps = [ ":xml_parser_fuzzer_deps" ]
  dict = "//third_party/libxml/fuzz/xml.dict"
  seed_corpus = "//third_party/libxml/fuzz/seed_corpus"
}

if (is_chromeos_ash) {
  fuzzer_test("ble_scan_parser_fuzzer") {
    sources = [ "ble_scan_parser_impl_fuzzer.cc" ]
    deps = [ ":lib" ]
  }
}
