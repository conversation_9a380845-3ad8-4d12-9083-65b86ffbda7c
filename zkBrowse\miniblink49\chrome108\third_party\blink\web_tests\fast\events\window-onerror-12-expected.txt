CONSOLE MESSAGE: 1
CONSOLE MESSAGE: 2
CONSOLE MESSAGE: 3
This test should trigger 'window.onerror' for the exception in the attribute handler, regardless of how it's set.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".

window.onerror: "Uncaught TypeError: Cannot read properties of null (reading 'mu')" at window-onerror-12.html (Line: 12, Column: 38)
Stack Trace:
TypeError: Cannot read properties of null (reading 'mu')
    at HTMLButtonElement.onclick window-onerror-12.html:12:38
    at window-onerror-12.html:33:24

Returning 'true': the error should not be reported in the console as an unhandled exception.



window.onerror: "Uncaught TypeError: Cannot read properties of null (reading 'mu')" at window-onerror-12.html (Line: 35, Column: 47)
Stack Trace:
TypeError: Cannot read properties of null (reading 'mu')
    at HTMLButtonElement.onclick window-onerror-12.html:35:47
    at window-onerror-12.html:33:24

Returning 'true': the error should not be reported in the console as an unhandled exception.



window.onerror: "Uncaught TypeError: Cannot read properties of null (reading 'mu')" at window-onerror-12.html (Line: 35, Column: 65)
Stack Trace:
TypeError: Cannot read properties of null (reading 'mu')
    at HTMLButtonElement.onclick window-onerror-12.html:35:65
    at window-onerror-12.html:33:24

Returning 'true': the error should not be reported in the console as an unhandled exception.



PASS successfullyParsed is true

TEST COMPLETE

Button. Button 2 Button 3
