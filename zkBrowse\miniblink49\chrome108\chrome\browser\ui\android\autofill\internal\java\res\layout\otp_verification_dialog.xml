<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright 2021 The Chromium Authors
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file.
-->
<org.chromium.chrome.browser.ui.autofill.OtpVerificationDialogView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

  <LinearLayout
      android:id="@+id/otp_verification_dialog_contents"
      android:layout_height="wrap_content"
      android:layout_width="match_parent"
      android:orientation="vertical"
      style="@style/AlertDialogContent">

    <EditText
        android:id="@+id/otp_input"
        tools:ignore="LabelFor"
        android:autofillHints="sms_otp"
        android:digits="**********"
        android:inputType="phone"
        android:layout_height="wrap_content"
        android:layout_width="match_parent"/>

    <TextView
        android:id="@+id/otp_error_message"
        android:layout_height="wrap_content"
        android:layout_width="match_parent"
        android:textAppearance="@style/TextAppearance.ErrorCaption"/>

    <TextView
        android:id="@+id/otp_resend_message"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/otp_verification_dialog_resend_message_margin_top"
        android:layout_width="match_parent"/>
    </LinearLayout>

  <include layout="@layout/progress_bar"
        android:id="@+id/progress_bar_overlay"
        android:layout_alignBottom="@+id/otp_verification_dialog_contents"
        android:layout_alignEnd="@+id/otp_verification_dialog_contents"
        android:layout_alignTop="@+id/otp_verification_dialog_contents"
        android:layout_alignStart="@+id/otp_verification_dialog_contents"
        android:layout_height="wrap_content"
        android:layout_width="wrap_content"
        android:visibility="gone"/>
</org.chromium.chrome.browser.ui.autofill.OtpVerificationDialogView>
