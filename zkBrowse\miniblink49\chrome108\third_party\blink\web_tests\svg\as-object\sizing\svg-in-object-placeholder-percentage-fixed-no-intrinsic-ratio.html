<!DOCTYPE html>
<title>SVG sizing: &lt;object></title>
<meta name="timeout" content="long">
<script src="../../../resources/testharness.js"></script>
<script src="../../../resources/testharnessreport.js"></script>
<style>
  #testContainer {
      position: absolute;
      left: 0;
      top: 0;
      width: 800px;
      height: 600px;
  }
</style>
<link rel="help" href="http://www.w3.org/TR/CSS2/visudet.html#inline-replaced-width">
<link rel="help" href="http://www.w3.org/TR/CSS2/visudet.html#inline-replaced-height">
<link rel="help" href="http://www.whatwg.org/specs/web-apps/current-work/#replaced-elements">
<link rel="help" href="http://www.whatwg.org/specs/web-apps/current-work/#attr-dim-width">
<link rel="help" href="http://www.w3.org/TR/SVG/coords.html#ViewportSpace">
<div id="log"></div>
<div id="testContainer"></div>
<script src="svg-in-object.js"></script>
<!-- Test generated by buildtests.py -->
<script>testSVGInObjectWithPlaceholder("50%", "100", null);</script>
