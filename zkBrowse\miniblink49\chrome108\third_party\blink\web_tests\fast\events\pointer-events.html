<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN"
  "http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
  <meta http-equiv="Content-type" content="text/html; charset=utf-8">
  <title>Test for pointer-events property</title>
  <style type="text/css" media="screen">
    p {
      position: absolute;
      width: 100px;
      height: 30px;
      margin: 0;
      padding: 0;
      font-size: 20px;
    }
  </style>
  <script>
    var pointerEventsValues = ['auto', 'none'];
  
    var topEdge = 120;
    
    function clickHit(identifierString) {
      var text = document.getElementById('text' + identifierString);
      text.textContent = '-HIT-';
    }
  
    function testContent() {
      if (window.testRunner)
        testRunner.dumpAsText();

      var yOffset = topEdge;
    
      var testContent = document.getElementById("test-content");
    
      for (var pi = 0; pi < pointerEventsValues.length; pi++) {
        var eventLabel = document.createElement('p');
        eventLabel.style.left = "10px";
        eventLabel.style.top = yOffset + "px";
        eventLabel.style.pointerEvents = pointerEventsValues[pi];
        eventLabel.textContent = pointerEventsValues[pi];
        eventLabel.setAttribute('onclick', 'clickHit("' + pointerEventsValues[pi] + '")');
        testContent.appendChild(eventLabel);
      
        var hit = document.createElement('p');
        hit.style.left = "140px";
        hit.style.top = yOffset + "px";
        hit.id = "text" + pointerEventsValues[pi];
        hit.appendChild(document.createTextNode("-miss-"));
              
        testContent.appendChild(hit);
            
        if (window.eventSender) {
          // Cell click
          eventSender.mouseMoveTo(15, yOffset + 10);
          eventSender.mouseDown();
          eventSender.mouseUp();
        }

        yOffset += 40;
      }
    }
  </script>
  
</head>
<body onload="testContent()">
  <h1>Testing the values of 'pointer-events' on HTML content</h1>
  <div>The labels on the left will be clicked and the result is shown to the right</div>
  <div id="test-content">
  </div>
</body>
</html>
