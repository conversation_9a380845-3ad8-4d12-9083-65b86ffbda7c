# Copyright 2020 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

static_library("test_support") {
  testonly = true

  sources = [
    "payment_request_platform_browsertest_base.cc",
    "payment_request_platform_browsertest_base.h",
    "payment_request_test_controller.h",
    "personal_data_manager_test_util.cc",
    "personal_data_manager_test_util.h",
    "test_event_waiter.cc",
    "test_event_waiter.h",
  ]

  deps = [
    "//chrome/browser",
    "//chrome/test:test_support",
    "//components/autofill/core/browser:test_support",
    "//components/payments/content",
    "//components/payments/content:utils",
    "//components/webauthn/core/browser",
    "//content/test:test_support",
    "//net:test_support",
  ]

  if (is_android) {
    sources += [
      "android/payment_request_test_bridge.cc",
      "android/payment_request_test_bridge.h",
      "payment_request_test_controller_android.cc",
    ]

    deps += [
      "//chrome/test:test_support_jni_headers",
      "//chrome/test:test_support_ui_android",
    ]
  }

  if (!is_android) {
    sources += [ "payment_request_test_controller_desktop.cc" ]

    deps += [
      "//chrome/test:test_support_ui",
      "//components/webauthn/content/browser",
    ]
  }
}
