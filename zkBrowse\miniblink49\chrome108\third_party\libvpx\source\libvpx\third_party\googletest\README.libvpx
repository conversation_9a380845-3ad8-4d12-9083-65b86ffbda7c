URL: https://github.com/google/googletest.git
Version: release-1.11.0
License: BSD
License File: LICENSE

Description:
Google's framework for writing C++ tests on a variety of platforms
(Linux, Mac OS X, Windows, Windows CE, Symbian, etc).  Based on the
xUnit architecture.  Supports automatic test discovery, a rich set of
assertions, user-defined assertions, death tests, fatal and non-fatal
failures, various options for running the tests, and XML test report
generation.

Local Modifications:
- Remove everything but:
  CONTRIBUTORS
  googletest/
   include
   README.md
   src
  LICENSE
