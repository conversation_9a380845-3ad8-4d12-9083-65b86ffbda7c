<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 1.4 Class as selector</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
body {overflow: hidden;}
.one {color: green;}
.1 {color: red;}
.a1 {color: green;}
P.two {color: purple;}</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.one {color: green;}
.1 {color: red;}
.a1 {color: green;}
P.two {color: purple;}
</PRE>
<HR>
<P class="one">
This sentence should be green.
</P>
<P class="1">
This sentence should be black, not red-- class selectors cannot begin with digits in CSS1.
</P>
<P class="a1">
This sentence should be green.
</P>
<P class="two">
This sentence should be purple.
</P>
<PRE class="two">This sentence should NOT be purple.
</PRE>
<UL>
<LI class="two">This sentence should NOT be purple.
</UL>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P class="one">
This sentence should be green.
</P>
<P class="1">
This sentence should be black, not red-- class selectors cannot begin with digits in CSS1.
</P>
<P class="a1">
This sentence should be green.
</P>
<P class="two">
This sentence should be purple.
</P>
<PRE class="two">This sentence should NOT be purple.
</PRE>
<UL>
<LI class="two">This sentence should NOT be purple.
</UL>
</TD></TR></TABLE></BODY>
</HTML>
