; Copyright 2011 The Chromium Authors
; Use of this source code is governed by a BSD-style license that can be
; found in the LICENSE file.
;
; This is the Sandbox configuration file used for safeguarding the utility
; process which is used for performing sandboxed operations that need to touch
; the filesystem like decoding theme images and unpacking extensions.
;
; This configuration locks everything down, except access to one configurable
; directory.  This is different from other sandbox configuration files where
; file system access is entireley restricted.

; *** The contents of common.sb are implicitly included here. ***

; No additional resource access needed.

; This is available in 10.15+, and rolled out as a Finch experiment.
(if (param-true? filter-syscalls-debug)
  (when (defined? 'syscall-unix)
    (deny syscall-unix (with send-signal SIGSYS))
    (allow syscall-unix
      (syscall-number SYS_psynch_cvwait)
      (syscall-number SYS_sendto)
      (syscall-number SYS_socketpair)
)))
