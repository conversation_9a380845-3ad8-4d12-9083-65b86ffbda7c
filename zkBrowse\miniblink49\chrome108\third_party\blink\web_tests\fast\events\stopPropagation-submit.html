<p>This test checks whether event.stopPropagation() prevents the
default action. It should not! If the default was prevented, you'll
see a link below and the text FAIL. On success you will see PASS (and
?x=0&y=0).
</p>
<form action="resources/stopPropagation-submit-target.html">
<input type="image" id="image1" onclick="event.stopPropagation()" value="click here">
</form>
<p>
FAIL
<script>
if (window.testRunner) {
    testRunner.dumpAsText();
    testRunner.waitUntilDone();
}

var submit = document.getElementById("image1");
submit.click();

</script>
