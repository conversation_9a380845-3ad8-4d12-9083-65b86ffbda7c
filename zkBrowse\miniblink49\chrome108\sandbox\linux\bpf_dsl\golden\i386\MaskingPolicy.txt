  1) LOAD 4  // Architecture
  2) if A == 0x40000003; then JMP 3 else JMP 24
  3) LOAD 0  // System call number
  4) if A & 0x40000000; then JMP 24 else JMP 5
  5) if A >= 0x2f; then JMP 6 else JMP 9
  6) if A >= 0x3a; then JMP 7 else JMP 8
  7) if A >= 0x401; then JMP 30 else JMP 29
  8) if A >= 0x39; then JMP 12 else JMP 29
  9) if A >= 0x18; then JMP 10 else JMP 11
 10) if A >= 0x2e; then JMP 17 else JMP 29
 11) if A >= 0x17; then JMP 22 else JMP 29
 12) LOAD 20  // Argument 0 (MSB)
 13) if A == 0x0; then JMP 14 else JMP 24
 14) LOAD 16  // Argument 0 (LSB)
 15) A := A & 0xa5
 16) if A == 0xa0; then JMP 28 else JMP 27
 17) LOAD 20  // Argument 0 (MSB)
 18) if A == 0x0; then JMP 19 else JMP 24
 19) LOAD 16  // Argument 0 (LSB)
 20) A := A & 0xf0
 21) if A == 0xf0; then JMP 28 else JMP 27
 22) LOAD 20  // Argument 0 (MSB)
 23) if A == 0x0; then JMP 25 else JMP 24
 24) RET 0x0  // Kill
 25) LOAD 16  // Argument 0 (LSB)
 26) if A & 0xf; then JMP 27 else JMP 28
 27) RET 0x5000d  // errno = 13
 28) RET 0x50016  // errno = 22
 29) RET 0x7fff0000  // Allowed
 30) RET 0x50026  // errno = 38
