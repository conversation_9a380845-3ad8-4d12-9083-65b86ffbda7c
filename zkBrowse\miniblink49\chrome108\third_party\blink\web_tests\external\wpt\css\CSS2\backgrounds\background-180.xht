<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background with (attachment position repeat color)</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="G<PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-04-17 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <link rel="match" href="background-030-ref.xht" />

        <meta name="assert" content="Background with (attachment position repeat color) sets the background of the element to the color specified.  Repeat, position, and attachment only apply if image is set." />
        <style type="text/css">
            div
            {
                background: scroll bottom repeat-x green;
                height: 200px;
                width: 200px;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is a <strong>filled green square</strong>.</p>
        <div></div>
    </body>
</html>