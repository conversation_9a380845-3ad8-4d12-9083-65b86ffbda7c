<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
    <head>
        <title>CSS Test: Word-spacing and white space with multiple non-breaking spaces</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/">
        <link rel="help" href="http://www.w3.org/TR/CSS21/text.html#spacing-props">
        <meta name="flags" content="ahem">
        <script src="../../resources/ahem.js"></script>
        <meta name="assert" content="The white space processing removes non-breaking space before 'word-spacing' is applied.">
        <style type="text/css">
            div
            {
                font: 16px/1em Ahem;
                white-space: pre;
            }
            #div2, #div3
            {
                background: black;
                display: inline-block;
                height: 1em;
                width: 1em;
            }
            #div3
            {
                margin-left: 12em;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there are only two boxes below.</p>
        <div id="div1">X            X</div>
        <div id="div2"></div><div id="div3"></div>
    </body>
</html>