<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.5.17 border-style</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
.one {border-style: dashed; border-color: black; border-width: thick;}
.two {border-style: groove; border-color: red; border-width: thick;}
.three {border-style: none; border-color: purple; border-width: thick;}</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.one {border-style: dashed; border-color: black; border-width: thick;}
.two {border-style: groove; border-color: red; border-width: thick;}
.three {border-style: none; border-color: purple; border-width: thick;}
</PRE>
<HR>
<P style="background-color: silver;">
This is an unstyled element, save for the background color, and it contains inline elements with classes of <SPAN class="one">class one</SPAN>, which will result in a dashed thick black border; <SPAN class="two">class two</SPAN>, which should result in a grooved thick purple border, and <SPAN class="three">class three</SPAN>, which should result in no border at all.  The line-height of the parent element should not change, on any line.
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P style="background-color: silver;">
This is an unstyled element, save for the background color, and it contains inline elements with classes of <SPAN class="one">class one</SPAN>, which will result in a dashed thick black border; <SPAN class="two">class two</SPAN>, which should result in a grooved thick purple border, and <SPAN class="three">class three</SPAN>, which should result in no border at all.  The line-height of the parent element should not change, on any line.
</P>
</TD></TR></TABLE></BODY>
</HTML>
