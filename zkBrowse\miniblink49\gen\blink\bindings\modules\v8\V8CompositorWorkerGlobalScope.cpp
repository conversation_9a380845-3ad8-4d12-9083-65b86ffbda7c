// Copyright 2014 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// This file has been auto-generated by code_generator_v8.py. DO NOT MODIFY!

#include "config.h"
#include "V8CompositorWorkerGlobalScope.h"

#include "gen/blink/modules/CompositorWorkerGlobalScopeCoreConstructors.h"
#include "gen/blink/modules/CompositorWorkerGlobalScopeModulesConstructors.h"
#include "bindings/core/v8/ExceptionState.h"
#include "bindings/core/v8/ScriptValue.h"
#include "bindings/core/v8/SerializedScriptValueFactory.h"
#include "bindings/core/v8/V8AbstractEventListener.h"
#include "bindings/core/v8/V8CompositorProxy.h"
#include "bindings/core/v8/V8DOMConfiguration.h"
#include "bindings/core/v8/V8EventListenerList.h"
#include "bindings/core/v8/V8FrameRequestCallback.h"
#include "bindings/core/v8/V8MessagePort.h"
#include "bindings/core/v8/V8ObjectConstructor.h"
#include "bindings/modules/v8/V8CompositorWorkerGlobalScope.h"
#include "core/dom/ContextFeatures.h"
#include "core/dom/DOMArrayBuffer.h"
#include "core/dom/Document.h"
#include "core/dom/MessagePort.h"
#include "platform/RuntimeEnabledFeatures.h"
#include "platform/TraceEvent.h"
#include "wtf/GetPtr.h"
#include "wtf/RefPtr.h"

// V8 10.8 compatibility layer
#if V8_MAJOR_VERSION >= 10
#include "../../../../../v8_10_8/v8_compatibility.h"
#endif

namespace blink {

// Suppress warning: global constructors, because struct WrapperTypeInfo is trivial
// and does not depend on another global objects.
#if defined(COMPONENT_BUILD) && defined(WIN32) && COMPILER(CLANG)
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wglobal-constructors"
#endif
const WrapperTypeInfo V8CompositorWorkerGlobalScope::wrapperTypeInfo = { gin::kEmbedderBlink, V8CompositorWorkerGlobalScope::domTemplate, V8CompositorWorkerGlobalScope::refObject, V8CompositorWorkerGlobalScope::derefObject, V8CompositorWorkerGlobalScope::trace, 0, 0, V8CompositorWorkerGlobalScope::preparePrototypeObject, V8CompositorWorkerGlobalScope::installConditionallyEnabledProperties, "CompositorWorkerGlobalScope", &V8WorkerGlobalScope::wrapperTypeInfo, WrapperTypeInfo::WrapperTypeObjectPrototype, WrapperTypeInfo::ObjectClassId, WrapperTypeInfo::InheritFromEventTarget, WrapperTypeInfo::Independent, WrapperTypeInfo::WillBeGarbageCollectedObject };
#if defined(COMPONENT_BUILD) && defined(WIN32) && COMPILER(CLANG)
#pragma clang diagnostic pop
#endif

// This static member must be declared by DEFINE_WRAPPERTYPEINFO in CompositorWorkerGlobalScope.h.
// For details, see the comment of DEFINE_WRAPPERTYPEINFO in
// bindings/core/v8/ScriptWrappable.h.
const WrapperTypeInfo& CompositorWorkerGlobalScope::s_wrapperTypeInfo = V8CompositorWorkerGlobalScope::wrapperTypeInfo;

namespace CompositorWorkerGlobalScopeV8Internal {

template<class CallbackInfo>
static bool CompositorWorkerGlobalScopeCreateDataProperty(v8::Local<v8::Name> name, v8::Local<v8::Value> v8Value, const CallbackInfo& info)
{
    ASSERT(info.This()->IsObject());
    return v8CallBoolean(v8::Local<v8::Object>::Cast(info.This())->CreateDataProperty(info.GetIsolate()->GetCurrentContext(), name, v8Value));
}

static void CompositorWorkerGlobalScopeConstructorAttributeSetterCallback(v8::Local<v8::Name>, v8::Local<v8::Value> v8Value, const v8::PropertyCallbackInfo<void>& info)
{
    TRACE_EVENT_SET_SAMPLING_STATE("blink", "DOMSetter");
    do {
        v8::Local<v8::Value> data = info.Data();
        ASSERT(data->IsExternal());
#if V8_MAJOR_VERSION >= 10
        V8PerContextData* perContextData = V8PerContextData::from(v8::ObjectCompat::CreationContext(info.Holder()));
#else
        V8PerContextData* perContextData = V8PerContextData::from(info.Holder()->CreationContext());
#endif
        if (!perContextData)
            break;
        const WrapperTypeInfo* wrapperTypeInfo = WrapperTypeInfo::unwrap(data);
        if (!wrapperTypeInfo)
            break;
        CompositorWorkerGlobalScopeCreateDataProperty(v8String(info.GetIsolate(), wrapperTypeInfo->interfaceName), v8Value, info);
    } while (false); // do ... while (false) just for use of break
    TRACE_EVENT_SET_SAMPLING_STATE("v8", "V8Execution");
}

static void onmessageAttributeGetter(const v8::FunctionCallbackInfo<v8::Value>& info)
{
    v8::Local<v8::Object> holder = info.Holder();
    CompositorWorkerGlobalScope* impl = V8CompositorWorkerGlobalScope::toImpl(holder);
    EventListener* cppValue(impl->onmessage());
    v8SetReturnValue(info, cppValue ? v8::Local<v8::Value>(V8AbstractEventListener::cast(cppValue)->getListenerObject(impl->executionContext())) : v8::Local<v8::Value>(v8::Null(info.GetIsolate())));
}

static void onmessageAttributeGetterCallback(const v8::FunctionCallbackInfo<v8::Value>& info)
{
    TRACE_EVENT_SET_SAMPLING_STATE("blink", "DOMGetter");
    CompositorWorkerGlobalScopeV8Internal::onmessageAttributeGetter(info);
    TRACE_EVENT_SET_SAMPLING_STATE("v8", "V8Execution");
}

static void onmessageAttributeSetter(v8::Local<v8::Value> v8Value, const v8::FunctionCallbackInfo<v8::Value>& info)
{
    v8::Local<v8::Object> holder = info.Holder();
    CompositorWorkerGlobalScope* impl = V8CompositorWorkerGlobalScope::toImpl(holder);
    moveEventListenerToNewWrapper(info.GetIsolate(), holder, impl->onmessage(), v8Value, V8CompositorWorkerGlobalScope::eventListenerCacheIndex);
    impl->setOnmessage(V8EventListenerList::getEventListener(ScriptState::current(info.GetIsolate()), v8Value, true, ListenerFindOrCreate));
}

static void onmessageAttributeSetterCallback(const v8::FunctionCallbackInfo<v8::Value>& info)
{
    v8::Local<v8::Value> v8Value = info[0];
    TRACE_EVENT_SET_SAMPLING_STATE("blink", "DOMSetter");
    CompositorWorkerGlobalScopeV8Internal::onmessageAttributeSetter(v8Value, info);
    TRACE_EVENT_SET_SAMPLING_STATE("v8", "V8Execution");
}

void postMessageImpl(const char* interfaceName, CompositorWorkerGlobalScope* instance, const v8::FunctionCallbackInfo<v8::Value>& info)
{
    ExceptionState exceptionState(ExceptionState::ExecutionContext, "postMessage", interfaceName, info.Holder(), info.GetIsolate());
    if (UNLIKELY(info.Length() < 1)) {
        setMinimumArityTypeError(exceptionState, 1, info.Length());
        exceptionState.throwIfNeeded();
        return;
    }
    OwnPtrWillBeRawPtr<MessagePortArray> ports = adoptPtrWillBeNoop(new MessagePortArray);
    ArrayBufferArray arrayBuffers;
    if (info.Length() > 1) {
        const int transferablesArgIndex = 1;
        if (!SerializedScriptValue::extractTransferables(info.GetIsolate(), info[transferablesArgIndex], transferablesArgIndex, *ports, arrayBuffers, exceptionState)) {
            exceptionState.throwIfNeeded();
            return;
        }
    }
    RefPtr<SerializedScriptValue> message = SerializedScriptValueFactory::instance().create(info.GetIsolate(), info[0], ports.get(), &arrayBuffers, exceptionState);
    if (exceptionState.throwIfNeeded())
        return;
    // FIXME: Only pass context/exceptionState if instance really requires it.
    ExecutionContext* context = currentExecutionContext(info.GetIsolate());
    instance->postMessage(context, message.release(), ports.get(), exceptionState);
    exceptionState.throwIfNeeded();
}

static void postMessageMethodCallback(const v8::FunctionCallbackInfo<v8::Value>& info)
{
    TRACE_EVENT_SET_SAMPLING_STATE("blink", "DOMMethod");
    postMessageImpl("CompositorWorkerGlobalScope", V8CompositorWorkerGlobalScope::toImpl(info.Holder()), info);
    TRACE_EVENT_SET_SAMPLING_STATE("v8", "V8Execution");
}

static void requestAnimationFrameMethod(const v8::FunctionCallbackInfo<v8::Value>& info)
{
    if (UNLIKELY(info.Length() < 1)) {
        V8ThrowException::throwException(createMinimumArityTypeErrorForMethod(info.GetIsolate(), "requestAnimationFrame", "CompositorWorkerGlobalScope", 1, info.Length()), info.GetIsolate());
        return;
    }
    CompositorWorkerGlobalScope* impl = V8CompositorWorkerGlobalScope::toImpl(info.Holder());
    FrameRequestCallback* callback;
    {
        if (info.Length() <= 0 || !info[0]->IsFunction()) {
            V8ThrowException::throwTypeError(info.GetIsolate(), ExceptionMessages::failedToExecute("requestAnimationFrame", "CompositorWorkerGlobalScope", "The callback provided as parameter 1 is not a function."));
            return;
        }
        callback = V8FrameRequestCallback::create(v8::Local<v8::Function>::Cast(info[0]), ScriptState::current(info.GetIsolate()));
    }
    v8SetReturnValueInt(info, impl->requestAnimationFrame(callback));
}

static void requestAnimationFrameMethodCallback(const v8::FunctionCallbackInfo<v8::Value>& info)
{
    TRACE_EVENT_SET_SAMPLING_STATE("blink", "DOMMethod");
    CompositorWorkerGlobalScopeV8Internal::requestAnimationFrameMethod(info);
    TRACE_EVENT_SET_SAMPLING_STATE("v8", "V8Execution");
}

static void cancelAnimationFrameMethod(const v8::FunctionCallbackInfo<v8::Value>& info)
{
    ExceptionState exceptionState(ExceptionState::ExecutionContext, "cancelAnimationFrame", "CompositorWorkerGlobalScope", info.Holder(), info.GetIsolate());
    if (UNLIKELY(info.Length() < 1)) {
        setMinimumArityTypeError(exceptionState, 1, info.Length());
        exceptionState.throwIfNeeded();
        return;
    }
    CompositorWorkerGlobalScope* impl = V8CompositorWorkerGlobalScope::toImpl(info.Holder());
    int handle;
    {
        handle = toInt32(info.GetIsolate(), info[0], NormalConversion, exceptionState);
        if (exceptionState.throwIfNeeded())
            return;
    }
    impl->cancelAnimationFrame(handle);
}

static void cancelAnimationFrameMethodCallback(const v8::FunctionCallbackInfo<v8::Value>& info)
{
    TRACE_EVENT_SET_SAMPLING_STATE("blink", "DOMMethod");
    CompositorWorkerGlobalScopeV8Internal::cancelAnimationFrameMethod(info);
    TRACE_EVENT_SET_SAMPLING_STATE("v8", "V8Execution");
}

} // namespace CompositorWorkerGlobalScopeV8Internal

static const V8DOMConfiguration::AccessorConfiguration V8CompositorWorkerGlobalScopeAccessors[] = {
    {"onmessage", CompositorWorkerGlobalScopeV8Internal::onmessageAttributeGetterCallback, CompositorWorkerGlobalScopeV8Internal::onmessageAttributeSetterCallback, 0, 0, 0, static_cast<v8::AccessControl>(v8::DEFAULT), static_cast<v8::PropertyAttribute>(v8::None), V8DOMConfiguration::ExposedToAllScripts, V8DOMConfiguration::OnPrototype, V8DOMConfiguration::CheckHolder},
};

static const V8DOMConfiguration::MethodConfiguration V8CompositorWorkerGlobalScopeMethods[] = {
    {"postMessage", CompositorWorkerGlobalScopeV8Internal::postMessageMethodCallback, 0, 1, V8DOMConfiguration::ExposedToAllScripts},
    {"requestAnimationFrame", CompositorWorkerGlobalScopeV8Internal::requestAnimationFrameMethodCallback, 0, 1, V8DOMConfiguration::ExposedToAllScripts},
    {"cancelAnimationFrame", CompositorWorkerGlobalScopeV8Internal::cancelAnimationFrameMethodCallback, 0, 1, V8DOMConfiguration::ExposedToAllScripts},
};

static void installV8CompositorWorkerGlobalScopeTemplate(v8::Local<v8::FunctionTemplate> functionTemplate, v8::Isolate* isolate)
{
    functionTemplate->ReadOnlyPrototype();

    v8::Local<v8::Signature> defaultSignature;
    if (!RuntimeEnabledFeatures::compositorWorkerEnabled())
        defaultSignature = V8DOMConfiguration::installDOMClassTemplate(isolate, functionTemplate, "CompositorWorkerGlobalScope", V8WorkerGlobalScope::domTemplate(isolate), V8CompositorWorkerGlobalScope::internalFieldCount, 0, 0, 0, 0, 0, 0);
    else
        defaultSignature = V8DOMConfiguration::installDOMClassTemplate(isolate, functionTemplate, "CompositorWorkerGlobalScope", V8WorkerGlobalScope::domTemplate(isolate), V8CompositorWorkerGlobalScope::internalFieldCount,
            0, 0,
            V8CompositorWorkerGlobalScopeAccessors, WTF_ARRAY_LENGTH(V8CompositorWorkerGlobalScopeAccessors),
            V8CompositorWorkerGlobalScopeMethods, WTF_ARRAY_LENGTH(V8CompositorWorkerGlobalScopeMethods));
    v8::Local<v8::ObjectTemplate> instanceTemplate = functionTemplate->InstanceTemplate();
    ALLOW_UNUSED_LOCAL(instanceTemplate);
    v8::Local<v8::ObjectTemplate> prototypeTemplate = functionTemplate->PrototypeTemplate();
    ALLOW_UNUSED_LOCAL(prototypeTemplate);
    if (RuntimeEnabledFeatures::compositorWorkerEnabled()) {
        static const V8DOMConfiguration::AttributeConfiguration attributeConfiguration =\
        {"CompositorProxy", v8ConstructorAttributeGetter, CompositorWorkerGlobalScopeV8Internal::CompositorWorkerGlobalScopeConstructorAttributeSetterCallback, 0, 0, const_cast<WrapperTypeInfo*>(&V8CompositorProxy::wrapperTypeInfo), static_cast<v8::AccessControl>(v8::DEFAULT), static_cast<v8::PropertyAttribute>(v8::DontEnum), V8DOMConfiguration::ExposedToAllScripts, V8DOMConfiguration::OnInstance, V8DOMConfiguration::CheckHolder};
        V8DOMConfiguration::installAttribute(isolate, instanceTemplate, prototypeTemplate, attributeConfiguration);
    }
    if (RuntimeEnabledFeatures::compositorWorkerEnabled()) {
        static const V8DOMConfiguration::AttributeConfiguration attributeConfiguration =\
        {"CompositorWorkerGlobalScope", v8ConstructorAttributeGetter, CompositorWorkerGlobalScopeV8Internal::CompositorWorkerGlobalScopeConstructorAttributeSetterCallback, 0, 0, const_cast<WrapperTypeInfo*>(&V8CompositorWorkerGlobalScope::wrapperTypeInfo), static_cast<v8::AccessControl>(v8::DEFAULT), static_cast<v8::PropertyAttribute>(v8::DontEnum), V8DOMConfiguration::ExposedToAllScripts, V8DOMConfiguration::OnInstance, V8DOMConfiguration::CheckHolder};
        V8DOMConfiguration::installAttribute(isolate, instanceTemplate, prototypeTemplate, attributeConfiguration);
    }
#if V8_MAJOR_VERSION >= 10
    v8::FunctionTemplateCompat::SetHiddenPrototype(functionTemplate, true);
#else
    functionTemplate->SetHiddenPrototype(true);
#endif

    // Custom toString template
#if V8_MAJOR_VERSION < 7
    functionTemplate->Set(v8AtomicString(isolate, "toString"), V8PerIsolateData::from(isolate)->toStringTemplate());
#endif
}

v8::Local<v8::FunctionTemplate> V8CompositorWorkerGlobalScope::domTemplate(v8::Isolate* isolate)
{
    return V8DOMConfiguration::domClassTemplate(isolate, const_cast<WrapperTypeInfo*>(&wrapperTypeInfo), installV8CompositorWorkerGlobalScopeTemplate);
}

bool V8CompositorWorkerGlobalScope::hasInstance(v8::Local<v8::Value> v8Value, v8::Isolate* isolate)
{
    return V8PerIsolateData::from(isolate)->hasInstance(&wrapperTypeInfo, v8Value);
}

v8::Local<v8::Object> V8CompositorWorkerGlobalScope::findInstanceInPrototypeChain(v8::Local<v8::Value> v8Value, v8::Isolate* isolate)
{
    return V8PerIsolateData::from(isolate)->findInstanceInPrototypeChain(&wrapperTypeInfo, v8Value);
}

CompositorWorkerGlobalScope* V8CompositorWorkerGlobalScope::toImplWithTypeCheck(v8::Isolate* isolate, v8::Local<v8::Value> value)
{
    return hasInstance(value, isolate) ? toImpl(v8::Local<v8::Object>::Cast(value)) : 0;
}

void V8CompositorWorkerGlobalScope::refObject(ScriptWrappable* scriptWrappable)
{
#if !ENABLE(OILPAN)
    scriptWrappable->toImpl<CompositorWorkerGlobalScope>()->ref();
#endif
}

void V8CompositorWorkerGlobalScope::derefObject(ScriptWrappable* scriptWrappable)
{
#if !ENABLE(OILPAN)
    scriptWrappable->toImpl<CompositorWorkerGlobalScope>()->deref();
#endif
}

} // namespace blink
