<!DOCTYPE html>
<title>createPattern() with SVG fragments</title>
<script>
    onload = function() {
        var context = document.getElementsByTagName('canvas')[0].getContext('2d');
        var images = document.getElementsByTagName('img');
        for (var i = 0; i < images.length; i++) {
            /* Draw image directly from DOM */
            var pattern = context.createPattern(images[i], "repeat");
            context.fillStyle = pattern;
            context.fillRect(i*60, i*60, 120, 120);
        }

        for (var i = 0; i < images.length; i++) {
            /* Draw new image with src from DOM */
            var newImage = new Image();
            newImage.src = images[i].src;
            var pattern = context.createPattern(newImage, "repeat");
            context.fillStyle = pattern;
            context.fillRect((i+3)*60, (i+3)*60, 120, 120);
        }

        while (images.length)
            document.body.removeChild(images.item(0));
    }
</script>
<canvas width="480" height="480"></canvas>
<img src="../css/resources/fragment-identifiers.svg#green">
<img src="../css/resources/fragment-identifiers.svg#red">
<img src="../css/resources/fragment-identifiers.svg#blue">
