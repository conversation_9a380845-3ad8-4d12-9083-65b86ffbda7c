<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN"
  "http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
  <meta http-equiv="Content-type" content="text/html; charset=utf-8">
  <title>Test for pointer-events property</title>
  <style type="text/css" media="screen">
    body { overflow: hidden; }

    .row {
      position: absolute;
      width: 300px;
      height: 100px;
      left: 10px;
    }

    .row1 { top: 140px; }
    .row2 { top: 240px; }
    .row3 { top: 340px; }
    .row4 { top: 440px; }
    .row5 { top: 540px; }
    .row6 { top: 140px; left: 340px;}
    .row7 { top: 240px; left: 340px;}
    .row8 { top: 340px; left: 340px;}
    .row9 { top: 440px; left: 340px;}
    .row10 { top: 540px; left: 340px; }

    .col {
      position: absolute;
      width: 80px;
      height: 80px;
      top: 0;
    }
    
    .col1 {
      left: 0px;
    }

    .col2 {
      left: 100px;
    }

    .col3 {
      left: 200px;
    }

    div, p {
      font-size: 16px;
    }

    div p {
      padding: 0;
      margin: 0;
      position: absolute;
      width: 80px;
      height: 80px;
    }
    
    #p1 {
      background: gray;
    }

    #p2 {
      background: gray;
      pointer-events: none;
    }

    #p3 {
      background: gray;
      pointer-events: none;
    }

    #p4 {
      pointer-events: auto;
    }

    #p5 {
      background: gray;
      pointer-events: auto;
    }

    #p6 {
      pointer-events: none;
    }

    #p7 { /* this is a div - not a p */
      pointer-events: auto;
      padding: 0;
      margin: 0;
      position: absolute;
      width: 80px;
      height: 80px;
    }

    #p8 {
      background: gray;
      pointer-events: none;
    }

    #p9 { /* this is a div - not a p */
      pointer-events: none;
      padding: 0;
      margin: 0;
      position: absolute;
      width: 80px;
      height: 80px;
    }

    #p10 {
      background: gray;
      pointer-events: auto;
    }

    #p11 {
      position: absolute;
      margin: 0;
      padding: 0;
      pointer-events: auto;
      width: 80px;
      height: 80px;
    }

    #p12 {
      position: absolute;
      margin: 0;
      padding: 0;
      pointer-events: none;
      width: 80px;
      height: 80px;
    }

    #p13 {
      background: gray;
      pointer-events: none;
    }
    
    #p14 {
      background: yellow;
      pointer-events: auto;
    }

    #p15 {
      background: gray;
      pointer-events: auto;
    }
    
    #p16 {
      background: yellow;
      pointer-events: none;
    }

    
  </style>
  <script>
    if (window.testRunner) {
      testRunner.waitUntilDone();
    }
  
    var pointerEventsValues = ['auto', 'none'];
  
    var topEdge = 140;
    
    function clickHit(event) {
      // iterate up to the containing row
      var parent = event.target;
      while (parent && (!parent.className || !parent.className.match(/^row/))) {
        parent = parent.parentNode;
      }
      if (parent && parent.className && parent.className.match(/^row/)) {
        var actual = parent.querySelector(".col3");
        if (actual && event.target.id)
          actual.innerText = event.target.id;
      }
    }

    function testContent() {
      
      // hit left side
      var yOffset = topEdge;
      for (var i=0; i < 5; i++) {
        if (window.eventSender) {
          eventSender.mouseMoveTo(15, yOffset + 5);
          eventSender.mouseDown();
          eventSender.mouseUp();
        }
        yOffset += 100;
      }

      // hit right side
      yOffset = topEdge;
      for (var i=0; i < 5; i++) {
        if (window.eventSender) {
          eventSender.mouseMoveTo(345, yOffset + 5);
          eventSender.mouseDown();
          eventSender.mouseUp();
        }
        yOffset += 100;
      }
      
      if (window.testRunner)
        testRunner.notifyDone();
    }
  </script>
  
</head>
<body onload="testContent()" onclick="clickHit(event)">
  <h1>Testing the values of 'pointer-events' on HTML content</h1>
  <p>This test is expected to be run from within DumpRenderTree. If running manually, click on
    the elements in the first column, making sure the actual result (3rd column) matches
    the expected result (2nd column). Repeat for click target (4th column), expected result (5th column)
    and actual result (6th column). Note: You should click on a yellow inline element if
    one exists, otherwise anywhere in the box.</p>

  <div class="row row1">
    <div class="col col1">
      <p id="p1">pointer-events auto</p>
    </div>
    <div class="col col2">p1</div>
    <div class="col col3">none</div>
  </div>

  <div class="row row2">
    <div class="col col1">
      <p id="p2">pointer-events none</p>
    </div>
    <div class="col col2">none</div>
    <div class="col col3">none</div>
  </div>

  <div class="row row3">
    <div class="col col1">
      <p id="p3">siblings - auto on top</p>
      <p id="p4"></p>
    </div>
    <div class="col col2">p4</div>
    <div class="col col3">none</div>
  </div>

  <div class="row row4">
    <div class="col col1">
      <p id="p5">siblings - none on top</p>
      <p id="p6"></p>
    </div>
    <div class="col col2">p5</div>
    <div class="col col3">none</div>
  </div>

  <div class="row row5">
    <div class="col col1">
      <div id="p7">
        <p id="p8">nested - auto outer</p>
      </div>
    </div>
    <div class="col col2">p7</div>
    <div class="col col3">none</div>
  </div>

  <div class="row row6">
    <div class="col col1">
      <div id="p9">
        <p id="p10">nested - none outer</p>
      </div>
    </div>
    <div class="col col2">p10</div>
    <div class="col col3">none</div>
  </div>

  <div class="row row7">
    <div class="col col1">
      <img id="p11" src="resources/abe.png">
    </div>
    <div class="col col2">p11</div>
    <div class="col col3">none</div>
  </div>

  <div class="row row8">
    <div class="col col1">
      <img id="p12" src="resources/abe.png">
    </div>
    <div class="col col2">none</div>
    <div class="col col3">none</div>
  </div>

  <div class="row row9">
    <div class="col col1">
      <p id="p13"><span id="p14">inline</span> has auto, parent none
      </p>
    </div>
    <div class="col col2">p14</div>
    <div class="col col3">none</div>
  </div>

  <div class="row row10">
    <div class="col col1">
      <p id="p15"><span id="p16">inline</span> has none, parent auto
      </p>
    </div>
    <div class="col col2">p15</div>
    <div class="col col3">none</div>
  </div>

</body>
</html>
