# Copyright 2021 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/android/rules.gni")

android_library("java") {
  sources = [
    "java/src/org/chromium/chrome/browser/management/ManagementCoordinator.java",
    "java/src/org/chromium/chrome/browser/management/ManagementMediator.java",
    "java/src/org/chromium/chrome/browser/management/ManagementPage.java",
    "java/src/org/chromium/chrome/browser/management/ManagementProperties.java",
    "java/src/org/chromium/chrome/browser/management/ManagementView.java",
    "java/src/org/chromium/chrome/browser/management/ManagementViewBinder.java",
  ]
  deps = [
    ":java_resources",
    "//chrome/browser/enterprise/util:java",
    "//chrome/browser/profiles/android:java",
    "//chrome/browser/tab:java",
    "//chrome/browser/ui/android/native_page:java",
    "//components/browser_ui/widget/android:java",
    "//components/embedder_support/android:util_java",
    "//content/public/android:content_full_java",
    "//third_party/androidx:androidx_annotation_annotation_java",
    "//ui/android:ui_no_recycler_view_java",
  ]
  resources_package = "org.chromium.chrome.browser.management"
}

android_resources("java_resources") {
  sources = [
    "java/res/drawable/enterprise_icon.xml",
    "java/res/drawable/enterprise_phone.xml",
    "java/res/drawable/enterprise_user_circle.xml",
    "java/res/layout/enterprise_management.xml",
    "java/res/values/dimens.xml",
  ]
  deps = [
    "//chrome/app:chromium_strings",
    "//chrome/app:google_chrome_strings",
    "//chrome/browser/ui/android/strings:ui_strings_grd",
    "//components/browser_ui/styles/android:java_resources",
    "//components/strings:components_locale_settings_grd",
  ]
}
