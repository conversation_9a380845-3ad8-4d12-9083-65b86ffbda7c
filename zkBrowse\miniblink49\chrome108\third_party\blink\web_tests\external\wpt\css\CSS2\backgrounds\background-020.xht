<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background with (attachment repeat)</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="Gérard Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-03-14 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
		<link rel="help" href="http://www.w3.org/TR/css3-background/#the-background-attachment" />
        <link rel="match" href="../reference/ref-nothing-below.xht" />

        <meta name="assert" content="Background with (attachment repeat) does not affect the background since image is not set." />
        <style type="text/css">
            div
            {
                background: fixed repeat-x;
                height: 1in;
                width: 1in;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is nothing below.</p>
        <div></div>
    </body>
</html>