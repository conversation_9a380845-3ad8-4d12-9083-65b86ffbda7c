# PowerShell script to fix V8 10.8 compatibility issues in modules bindings
param(
    [string]$ModulesDir = "..\gen\blink\bindings\modules\v8"
)

Write-Host "Fixing V8 10.8 compatibility issues in modules V8 bindings..." -ForegroundColor Green
Write-Host ""

# List of files that need fixing based on error messages
$files = @(
    "V8ExtFragDepth.cpp",
    "V8ExtShaderTextureLod.cpp", 
    "V8ExtSRGB.cpp",
    "V8ExtTextureFilterAnisotropic.cpp",
    "V8OESStandardDerivatives.cpp",
    "V8OESTextureFloatLinear.cpp",
    "V8OESTextureFloat.cpp",
    "V8OESElementIndexUint.cpp",
    "V8OESVertexArrayObject.cpp",
    "V8OESTextureHalfFloat.cpp",
    "V8OESTextureHalfFloatLinear.cpp"
)

$compatibilityHeader = @"
// V8 10.8 compatibility layer
#if V8_MAJOR_VERSION >= 10
#include "../../../../../v8_10_8/v8_compatibility.h"
#endif
"@

$setReferenceFromGroupFix = @"
#if V8_MAJOR_VERSION >= 10
        v8::IsolateCompat::SetReferenceFromGroup(isolate, v8::UniqueId(reinterpret_cast<intptr_t>(root)), wrapper);
#else
        isolate->SetReferenceFromGroup(v8::UniqueId(reinterpret_cast<intptr_t>(root)), wrapper);
#endif
"@

foreach ($file in $files) {
    $filePath = Join-Path $ModulesDir $file
    
    if (Test-Path $filePath) {
        Write-Host "Processing $file..." -ForegroundColor Yellow
        
        try {
            $content = Get-Content $filePath -Raw
            
            # Add compatibility header after wtf includes
            if ($content -notmatch "v8_compatibility\.h") {
                $content = $content -replace '(#include "wtf/RefPtr\.h")', "`$1`r`n`r`n$compatibilityHeader"
                Write-Host "  - Added compatibility header" -ForegroundColor Green
            }
            
            # Fix SetReferenceFromGroup calls
            if ($content -match 'isolate->SetReferenceFromGroup\(') {
                $content = $content -replace 'isolate->SetReferenceFromGroup\(v8::UniqueId\(reinterpret_cast<intptr_t>\(root\)\), wrapper\);', $setReferenceFromGroupFix
                Write-Host "  - Fixed SetReferenceFromGroup call" -ForegroundColor Green
            }
            
            # Write back to file
            Set-Content $filePath $content -NoNewline
            Write-Host "  - File updated successfully" -ForegroundColor Green
            
        } catch {
            Write-Host "  - Error processing file: $_" -ForegroundColor Red
        }
    } else {
        Write-Host "Warning: $file not found" -ForegroundColor Red
    }
    
    Write-Host ""
}

Write-Host "Batch fix completed!" -ForegroundColor Green
Write-Host "All modules V8 bindings have been updated for V8 10.8 compatibility." -ForegroundColor Green
