<html>
<body onload='startTest()'>

<script src="../../resources/js-test.js"></script>

<script>

description("This test checks that Page Visibility state values are correct and the event changes are fired correctly.");

var jsTestIsAsync = true;

function makePageVisible() {
    if (window.testRunner)
        testRunner.setMainWindowHidden(false);
}

function makePageHidden() {
    if (window.testRunner)
        testRunner.setMainWindowHidden(true);
}

function checkIsPageVisible() {
    shouldBeEqualToString("document.webkitVisibilityState", "visible");
    shouldBeFalse("document.webkitHidden");
}

function checkIsPageHidden() {
    shouldBeEqualToString("document.webkitVisibilityState", "hidden");
    shouldBeTrue("document.webkitHidden");
}

// We will try to change the visibility states as:
//  0 - visible. (Initial - i.e. on load).
//  1 - hidden (should fire event).
//  2 - hidden (no event).
//  3 - visible (should fire event).
var numVisibilityChanges = 0;

function startTest() {
    document.addEventListener(
        "webkitvisibilitychange", onVisibilityChange, false);
    checkIsPageVisible();
    numVisibilityChanges++;
    makePageHidden();
}

function onVisibilityChange() {
    if (numVisibilityChanges == 1) {
        checkIsPageHidden();
        numVisibilityChanges++;
        makePageHidden();
        checkIsPageHidden();
        numVisibilityChanges++;
        makePageVisible();
        return;
    } else if (numVisibilityChanges == 2) {
        testFailed("Invalid event fired on same state change.");
        finishJSTest();
        return;
    } else if (numVisibilityChanges == 3) {
        checkIsPageVisible();
        numVisibilityChanges++;
        finishJSTest();
        return;
    } else {
        testFailed("Too many visibility transitions");
        finishJSTest();
        return;
    }
}

</script>


</body>
</html>
