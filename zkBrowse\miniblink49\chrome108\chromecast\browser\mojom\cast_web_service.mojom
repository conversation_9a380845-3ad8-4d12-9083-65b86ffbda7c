// Copyright 2020 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

module chromecast.mojom;

import "chromecast/browser/webui/mojom/webui.mojom";
import "chromecast/browser/mojom/cast_content_window.mojom";
import "chromecast/browser/mojom/cast_web_contents.mojom";
import "mojo/public/mojom/base/time.mojom";
import "url/mojom/url.mojom";

// Background color for the WebContents window.
enum BackgroundColor {
  NONE,
  WHITE,
  BLACK,
  TRANSPARENT,
};

// Mirrored from content/public/common/media_playback_renderer_type.mojom.
enum RendererType {
  DEFAULT_RENDERER = 0,
  MOJO_RENDERER = 1,
  REMOTING_RENDERER = 2,
};

// Selects a prelaunch renderer pool, which restricts the number of app
// instances that can be prelaunched for a given type.
enum RendererPool {
  // Don't use a renderer pool for prelaunching. This means launching the
  // render process eagerly is un-restricted and will always succeed.
  NONE,
  // Pool for overlay apps, which allows up to one pre-cached site.
  OVERLAY,
};

// Gesture handler priority. If multiple handlers can process a gesture event,
// then the highest-priority handler is the one which receives the event.
enum GesturePriority {
  // Handler with NONE priority will never receive a gesture event.
  NONE = 0,
  ROOT_UI = 1,
  MAIN_ACTIVITY = 2,
  SETTINGS_UI = 3,
};

// Params for creating a CastWebView. Upon creation, CastWebContents and
// CastContentWindow remotes are provided for controlling the page and window
// respectively.
struct CastWebViewParams {
  // Cast Application/Activity ID for the web view.
  string activity_id = "";

  // Cast Application/Activity session ID.
  string session_id = "";

  // Cast Receiver SDK version of the application (if available).
  string sdk_version = "";

  // Grants user media access permissions for the web view. This is primarily
  // used for camera streaming.
  bool allow_media_access = false;

  // Force 720p resolution for the web view.
  bool force_720p_resolution = false;

  // Whether the web view is managed by the Assistant window manager. If
  // false, then the window is managed by the full-screen Aura window manager.
  bool managed = true;

  // Whether JS console logs should be appended to the device logs.
  bool log_js_console_messages = false;

  // Prefix for JS console logs. This can be used to help identify the source
  // of console log messages.
  string log_prefix = "";

  // Delays web view deletion after remote<CastWebContents> is disconnected. The
  // default value is zero, which means the web view will be deleted immediately
  // and synchronously.
  mojo_base.mojom.TimeDelta shutdown_delay;

  // Pool for pre-launched renderers.
  RendererPool renderer_pool = RendererPool.NONE;

  // Eagerly pre-launches a render process for |prelaunch_url| if it is valid.
  url.mojom.Url prelaunch_url;

  // Enable development mode for the CastWebContents. This allows certain
  // functionality for the WebContents, like remote debugging and debugging
  // interfaces.
  bool enabled_for_dev = false;

  // Chooses a media renderer for the WebContents.
  RendererType renderer_type = RendererType.DEFAULT_RENDERER;

  // Whether the WebContents is a root native window, or if it is embedded in
  // another WebContents (see Delegate::InnerContentsCreated()).
  bool is_root_window = false;

  // Whether inner WebContents events should be handled. If this is set to
  // true, then inner WebContents will automatically have a CastWebContents
  // created and notify the delegate.
  bool handle_inner_contents = false;

  // Construct internal media blocker and enable BlockMediaLoading().
  bool use_media_blocker = false;

  // Background color for the WebContents view. If not provided, the color
  // will fall back to the platform default.
  BackgroundColor background_color = BackgroundColor.NONE;

  // Enable WebSQL database for this CastWebContents.
  bool enable_websql = false;

  // Enable mixer audio support for this CastWebContents.
  bool enable_mixer_audio = false;

  // Whether to provide a URL filter applied to network requests for the
  // activity hosted by this CastWebContents.
  // No filters implies no restrictions.
  array<string>? url_filters;

  // Whether WebRTC peer connections are allowed to use legacy versions of the
  // TLS/DTLS protocols.
  bool webrtc_allow_legacy_tls_protocols = false;

  // Enable touch input for the CastContentWindow instance.
  bool enable_touch_input = false;

  // Enable if this CastContentWindow is for running a remote control app.
  bool is_remote_control_mode = false;

  // Turns on the device screen when the window is made visible.
  bool turn_on_screen = true;

  // Keeps the screen on and suppresses the screensaver while the app is visible.
  bool keep_screen_on = true;

  // Window's gesture priority for when it is visible.
  GesturePriority gesture_priority = GesturePriority.NONE;

  // Enables mojo and WebUi bindings permission for the page. Note that this
  // setting alone does not guarantee the bindings will be added, just that
  // they have the appropriate permissions.
  bool enable_webui_bindings_permission = false;
};

// Service interface for creating/managing web pages in the Cast Browser. The
// only user of this interface is the Cast Service, which will eventually live
// in its own process.
interface CastWebService {
  // Creates a new web view hosted by CastWebService. The lifetime of the web
  // view is scoped to the CastWebContents.
  CreateWebView(CastWebViewParams params,
                pending_receiver<CastWebContents> web_contents,
                pending_receiver<CastContentWindow> window);

  // Sets the WebUiClient for the Cast Browser. |hosts| is a list of hostnames
  // which the browser should create a Web UI for. See the WebUiClient interface
  // for details on how Web UIs are used in the Cast Browser.
  RegisterWebUiClient(pending_remote<WebUiClient> client,
                      array<string> hosts);

  // Write all DOM localStorage to disk.
  FlushDomLocalStorage();

  // Clears all localStorage data from the device. The response callback is
  // called when data deletion is done.
  ClearLocalStorage() => ();
};

