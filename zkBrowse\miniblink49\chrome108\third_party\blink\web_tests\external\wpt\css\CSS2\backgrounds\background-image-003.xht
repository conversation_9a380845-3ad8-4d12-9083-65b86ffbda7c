<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background-image set to 'inherit'</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="G<PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-04-21 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background-image" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <link rel="match" href="../reference/ref-filled-green-100px-square.xht" />

        <meta name="flags" content="image" />
        <meta name="assert" content="Background-image set to 'inherit' causes the container to use the background image of its parent." />
        <style type="text/css">
            #div1
            {
                background-image: url("support/green_box.png");
                height: 0;
                width: 0;
            }
            #test
            {
                background-color: red;
                background-image: inherit;
                height: 100px;
                width: 100px;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is a filled green square and <strong>no red</strong>.</p>
        <div id="div1">
            <div id="test"></div>
        </div>
    </body>
</html>