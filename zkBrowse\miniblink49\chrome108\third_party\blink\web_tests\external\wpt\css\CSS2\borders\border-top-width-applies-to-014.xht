<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Border-top-width applied to element with display inline-table</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="G<PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-12-06 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#propdef-border-top-width" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#border-width-properties" />
		<link rel="match" href="../reference/ref-filled-black-96px-square.xht" />

        <meta name="assert" content="The 'border-top-width' property applies to elements with a display of inline-table." />
        <style type="text/css">
            #table
            {
                border-top-style: solid;
                border-top-width: 1in;
                display: inline-table;
                table-layout: fixed;
                width: 1in;
            }
            .row
            {
                display: table-row;
            }
            .cell
            {
                display: table-cell;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is a filled black square.</p>

        <div id="table">
            <div class="row">
                <div class="cell"></div><div class="cell"></div>
            </div>

            <div class="row">
                <div class="cell"></div><div class="cell"></div>
            </div>
        </div>

    </body>
</html>