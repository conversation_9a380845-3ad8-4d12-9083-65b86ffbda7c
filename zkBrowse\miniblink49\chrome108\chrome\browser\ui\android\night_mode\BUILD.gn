# Copyright 2021 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/android/config.gni")
import("//build/config/android/rules.gni")
import("//chrome/browser/buildflags.gni")

android_library("java") {
  sources = [
    "java/src/org/chromium/chrome/browser/night_mode/AutoDarkFeedbackSource.java",
    "java/src/org/chromium/chrome/browser/night_mode/GlobalNightModeStateController.java",
    "java/src/org/chromium/chrome/browser/night_mode/GlobalNightModeStateProviderHolder.java",
    "java/src/org/chromium/chrome/browser/night_mode/NightModeMetrics.java",
    "java/src/org/chromium/chrome/browser/night_mode/NightModeStateProvider.java",
    "java/src/org/chromium/chrome/browser/night_mode/NightModeUtils.java",
    "java/src/org/chromium/chrome/browser/night_mode/PowerSavingModeMonitor.java",
    "java/src/org/chromium/chrome/browser/night_mode/RemoteViewsWithNightModeInflater.java",
    "java/src/org/chromium/chrome/browser/night_mode/SystemNightModeMonitor.java",
    "java/src/org/chromium/chrome/browser/night_mode/ThemeType.java",
    "java/src/org/chromium/chrome/browser/night_mode/WebContentsDarkModeController.java",
    "java/src/org/chromium/chrome/browser/night_mode/WebContentsDarkModeMessageController.java",
    "java/src/org/chromium/chrome/browser/night_mode/settings/RadioButtonGroupThemePreference.java",
    "java/src/org/chromium/chrome/browser/night_mode/settings/ThemeSettingsFragment.java",
  ]

  deps = [
    ":java_resources",
    "//base:base_java",
    "//chrome/browser/feature_engagement:java",
    "//chrome/browser/feedback/android:java",
    "//chrome/browser/flags:java",
    "//chrome/browser/preferences:java",
    "//chrome/browser/profiles/android:java",
    "//components/browser_ui/settings/android:java",
    "//components/browser_ui/site_settings/android:java",
    "//components/browser_ui/strings/android:browser_ui_strings_grd",
    "//components/browser_ui/widget/android:java",
    "//components/content_settings/android:content_settings_enums_java",
    "//components/content_settings/android:java",
    "//components/feature_engagement/public:public_java",
    "//components/messages/android:java",
    "//components/ukm/android:java",
    "//components/user_prefs/android:java",
    "//content/public/android:content_full_java",
    "//third_party/androidx:androidx_annotation_annotation_java",
    "//third_party/androidx:androidx_appcompat_appcompat_java",
    "//third_party/androidx:androidx_appcompat_appcompat_resources_java",
    "//third_party/androidx:androidx_preference_preference_java",
    "//ui/android:ui_no_recycler_view_java",
    "//ui/android:ui_utils_java",
    "//url:gurl_java",
  ]
  resources_package = "org.chromium.chrome.browser.night_mode"
}

android_resources("java_resources") {
  sources = [
    "java/res/layout/radio_button_group_theme_preference.xml",
    "java/res/xml/theme_preferences.xml",
  ]

  deps = [
    "//chrome/browser/ui/android/strings:ui_strings_grd",
    "//components/browser_ui/styles/android:java_resources",
  ]
}

android_library("night_mode_java_test_support") {
  testonly = true
  sources = [ "java/src/org/chromium/chrome/browser/night_mode/ChromeNightModeTestUtils.java" ]
  deps = [
    ":java",
    "//chrome/browser/preferences:java",
  ]
}

android_library("unit_device_javatests") {
  testonly = true
  sources = [ "java/src/org/chromium/chrome/browser/night_mode/settings/ThemeSettingsFragmentTest.java" ]

  deps = [
    ":java",
    ":java_resources",
    ":night_mode_java_test_support",
    "//base:base_java",
    "//base:base_java_test_support",
    "//chrome/browser/feature_engagement:java",
    "//chrome/browser/flags:java",
    "//chrome/browser/preferences:java",
    "//chrome/browser/profiles/android:java",
    "//chrome/test/android:chrome_java_test_support_common",
    "//components/browser_ui/settings/android:test_support_java",
    "//components/browser_ui/site_settings/android:java",
    "//components/browser_ui/widget/android:java",
    "//components/content_settings/android:content_settings_enums_java",
    "//components/feature_engagement/public:public_java",
    "//content/public/android:content_full_java",
    "//content/public/test/android:content_java_test_support",
    "//third_party/androidx:androidx_test_runner_java",
    "//third_party/junit",
    "//third_party/mockito:mockito_java",
    "//ui/android:ui_java_test_support",
  ]

  resources_package = "org.chromium.chrome.browser.night_mode"
}

robolectric_library("junit") {
  sources = [
    "java/src/org/chromium/chrome/browser/night_mode/AutoDarkFeedbackSourceUnitTest.java",
    "java/src/org/chromium/chrome/browser/night_mode/WebContentsDarkModeControllerUnitTest.java",
    "java/src/org/chromium/chrome/browser/night_mode/WebContentsDarkModeMessageControllerUnitTest.java",
  ]

  deps = [
    ":java",
    ":night_mode_java_test_support",
    "//base:base_java",
    "//base:base_java_test_support",
    "//base:base_junit_test_support",
    "//chrome/browser/feature_engagement:java",
    "//chrome/browser/feedback/android:java",
    "//chrome/browser/flags:java",
    "//chrome/browser/preferences:java",
    "//chrome/browser/profiles/android:java",
    "//chrome/test/android:chrome_java_unit_test_support",
    "//components/browser_ui/settings/android:java",
    "//components/browser_ui/site_settings/android:java",
    "//components/content_settings/android:content_settings_enums_java",
    "//components/content_settings/android:java",
    "//components/feature_engagement/public:public_java",
    "//components/messages/android:java",
    "//components/prefs/android:java",
    "//components/user_prefs/android:java",
    "//content/public/android:content_full_java",
    "//content/public/test/android:content_java_test_support",
    "//testing/android/junit:junit_test_support",
    "//third_party/androidx:androidx_test_runner_java",
    "//third_party/junit",
    "//third_party/mockito:mockito_java",
    "//ui/android:ui_java_test_support",
    "//ui/android:ui_junit_test_support",
    "//ui/android:ui_no_recycler_view_java",
    "//url:gurl_java",
  ]

  resources_package = "org.chromium.chrome.browser.night_mode"
}
