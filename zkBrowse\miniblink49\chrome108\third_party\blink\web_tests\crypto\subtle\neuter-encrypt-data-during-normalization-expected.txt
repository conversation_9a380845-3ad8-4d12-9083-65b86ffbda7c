Tests crypto.subtle.encrypt() using a BufferSource that is modified during algorithm normalization

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".

Importing key...

Encrypting (as a control group)...
PASS: Encryption should be [7649abac8119b246cee98e9b12e9197d5086cb9b507219ee95db113a917678b273bed6b8e3c1743b7116e69e222295163ff1caa1681fac09120eca307586e1a78cb82807230e1321d3fae00d18cc2012] and was

Encrypting again, using an algorithm that mutates the array buffer...
Accessed name property
Corrupted plainText
Neutering plainText...
PASS plainText.byteLength is 0
PASS: plainText should be [] and was
PASS: Encryption should be [7649abac8119b246cee98e9b12e9197d5086cb9b507219ee95db113a917678b273bed6b8e3c1743b7116e69e222295163ff1caa1681fac09120eca307586e1a78cb82807230e1321d3fae00d18cc2012] and was
PASS successfullyParsed is true

TEST COMPLETE

