<!DOCTYPE html>
<meta charset="utf-8">
<title>Actions in cross-origin iframe</title>
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<script src="/resources/testdriver.js"></script>
<script src="/resources/testdriver-vendor.js"></script>

<iframe src="https://{{host}}:{{ports[https][1]}}/infrastructure/testdriver/actions/crossOriginChild.html"></iframe>

<script>
setup({single_test: true});
addEventListener("message", (msg) => {
    if (msg.data === "PASS") {
        done();
    } else if (msg.data === "FAIL") {
        assert_unreached("actions failed")
    }
});
</script>
