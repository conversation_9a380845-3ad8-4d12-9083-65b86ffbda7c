<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 6.1 Length Units</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
.zero {margin-left: 0;}
.one {margin-left: 3em;}
.two {margin-left: 3ex;}
.three {margin-left: 36px;}
.four {margin-left: 0.5in;}
.five {margin-left: 1.27cm;}
.six {margin-left: 12.7mm;}
.seven {margin-left: 36pt;}
.eight {margin-left: 3pc;}
.nine {margin-left: +3pc;}
.ten {font-size: 40px; border-left: 1ex solid purple; background-color: aqua;}</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.zero {margin-left: 0;}
.one {margin-left: 3em;}
.two {margin-left: 3ex;}
.three {margin-left: 36px;}
.four {margin-left: 0.5in;}
.five {margin-left: 1.27cm;}
.six {margin-left: 12.7mm;}
.seven {margin-left: 36pt;}
.eight {margin-left: 3pc;}
.nine {margin-left: +3pc;}
.ten {font-size: 40px; border-left: 1ex solid purple; background-color: aqua;}
</PRE>
<HR>
<P class="zero">
This paragraph has no left margin. The following paragraphs have all been given a left margin and their left (outer) edges should therefore be appropriately shifted to the right of <EM>this</EM> paragraph's left edge.
</P>
<P class="one">
This paragraph should have a left margin of 3em.
</P>
<P class="two">
This paragraph should have a left margin of 3ex.
</P>
<P class="three">
This paragraph should have a left margin of 36 pixels.
</P>
<P class="four">
This paragraph should have a left margin of half an inch.
</P>
<P class="five">
This paragraph should have a left margin of 1.27cm.
</P>
<P class="six">
This paragraph should have a left margin of 12.7mm.
</P>
<P class="seven">
This paragraph should have a left margin of 36 points.
</P>
<P class="eight">
This paragraph should have a left margin of 3 picas.
</P>
<P class="nine">
This paragraph should have a left margin of 3 picas (the plus sign should make no difference).
</P>
<P class="ten">
This element has a <CODE>font-size</CODE> of <CODE>40px</CODE> and a <CODE>border-left</CODE> of <CODE>1ex solid purple</CODE>.  This should make the left border the same number of pixels as the lower-case 'x' in this element's font, as well as solid purple.
</P>

<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P class="zero">
This paragraph has no left margin. The following paragraphs have all been given a left margin and their left (outer) edges should therefore be appropriately shifted to the right of <EM>this</EM> paragraph's left edge.
</P>
<P class="one">
This paragraph should have a left margin of 3em.
</P>
<P class="two">
This paragraph should have a left margin of 3ex.
</P>
<P class="three">
This paragraph should have a left margin of 36 pixels.
</P>
<P class="four">
This paragraph should have a left margin of half an inch.
</P>
<P class="five">
This paragraph should have a left margin of 1.27cm.
</P>
<P class="six">
This paragraph should have a left margin of 12.7mm.
</P>
<P class="seven">
This paragraph should have a left margin of 36 points.
</P>
<P class="eight">
This paragraph should have a left margin of 3 picas.
</P>
<P class="nine">
This paragraph should have a left margin of 3 picas (the plus sign should make no difference).
</P>
<P class="ten">
This element has a <CODE>font-size</CODE> of <CODE>40px</CODE> and a <CODE>border-left</CODE> of <CODE>1ex solid purple</CODE>.  This should make the left border the same number of pixels as the lower-case 'x' in this element's font, as well as solid purple.
</P></TD></TR></TABLE></BODY>
</HTML>
