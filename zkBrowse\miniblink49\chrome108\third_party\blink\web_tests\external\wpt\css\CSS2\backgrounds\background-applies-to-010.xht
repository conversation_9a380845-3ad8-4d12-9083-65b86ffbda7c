<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background applied to elements with 'display' set to 'list-item'</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <meta name="assert" content="The 'background' property applies to elements with 'display' set to 'list-item'." />
        <style type="text/css">
            div
            {
                background: black;
                display: list-item;
                height: 1in;
                margin-left: 2em;
                width: 1in;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is a box below and there is a marker bullet on the left-hand side of the box.</p>
        <div></div>
    </body>
</html>