[
  Exposed=(Window,Worker),
  SecureContext
] interface StorageBucketManager {
    Promise<StorageBucket> open(DOMString name,
                                optional StorageBucketOptions options = {});
    Promise<sequence<DOMString>> keys();
    Promise<undefined> delete(DOMString name);
};

dictionary StorageBucketOptions {
  boolean persisted = false;
  StorageBucketDurability durability = "relaxed";
  unsigned long long? quota = null;
  DOMTimeStamp? expires = null;
};

enum StorageBucketDurability {
  "strict",
  "relaxed"
};

[
  Exposed=(Window,Worker),
  SecureContext
] interface StorageBucket {
  [Exposed=Window] Promise<boolean> persist();
  Promise<boolean> persisted();

  Promise<StorageEstimate> estimate();

  Promise<StorageBucketDurability> durability();

  Promise<undefined> setExpires(DOMTimeStamp expires);
  Promise<DOMTimeStamp> expires();
};
