include_rules = [
  # First, exclude everything.
  # Exclude a few dependencies that are included in the root DEPS and that we
  # don't need.
  # Sadly, there is no way to exclude all root DEPS since the root has no name.
  "-ipc",
  "-library_loaders",
  "-third_party",
  "-url",
  # Make sure that each subdirectory has to declare its dependencies in
  # sandbox/ explicitly.
  "-sandbox/linux",

  # Second, add what we want to allow.
  # Anything included from sandbox/linux must be declared after this line or in
  # a more specific DEPS file.
  # base/, build/, testing/, and optional.h are already included in the global
  # DEPS file, but be explicit.
  "+base",
  "+build",
  "+testing",
  "+sandbox/sandbox_export.h",
  # Everyone can use tests/
  "+sandbox/linux/tests",
  '+third_party/abseil-cpp/absl/types/optional.h',
]
