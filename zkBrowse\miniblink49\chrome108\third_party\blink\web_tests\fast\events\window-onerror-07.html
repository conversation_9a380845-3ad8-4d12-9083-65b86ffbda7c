<!DOCTYPE html>
<html>
<head>
    <script>
        window.isOnErrorTest = true;
    </script>
    <script src="../../resources/js-test.js"></script>
    <script src="resources/onerror-test.js"></script>
</head>
<body>
    <script>
        description("This tests that 'window.onerror' is called on the window object.");
        var thisInsideOnerror = null;
        window.onerror = function (message) {
            thisInsideOnerror = this;
            shouldBe('thisInsideOnerror', 'window');
        };

        throwException()
    </script>
</body>
</html>
