<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Border-style 'inset' color rendering</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="Gérard Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-06-24 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#border-style-properties" />
        <meta name="assert" content="The 'inset' 'border-style' uses the color defined for rendering the inset border." />
        <style type="text/css">
            div
            {
                border-top-color: green;
                border-top-style: inset;
                border-top-width: 1in;
            }
        </style>
    </head>
    <body>
        <p>Test passes if the box is a shade or shades of green.</p>
        <div></div>
    </body>
</html>