<html>
<head>
<script>
function print(message, color) 
{
    var paragraph = document.createElement("div");
    paragraph.appendChild(document.createTextNode(message));
    paragraph.style.fontFamily = "monospace";
    if (color)
        paragraph.style.color = color;
    document.getElementById("console").appendChild(paragraph);
}

function test() 
{
    if (window.testRunner) {
        testRunner.dumpAsText();
        testRunner.waitUntilDone();
        window.setTimeout("testRunner.notifyDone()", 5000); // prevent hang on failure
    }
        
    var div = document.createElement("div");
    div.setAttribute("onerror", "print('FAILED: dynamic script load error bubbled'); \
                                 if (window.testRunner) \
                                    testRunner.notifyDone();");
    
    var script = document.createElement("script");
    script.src="file:///GregTheBunny.js";
    div.appendChild(script);
    
    // force the script to load
    document.body.appendChild(div);
}

function scriptOnerror()
{
    print('PASS: script error handler invoked');

    // Allow event bubbling phase to complete.
    setTimeout(function() {
        if (window.testRunner)
            testRunner.notifyDone();
    }, 0);
}

window.onerror = function() {
    print('FAILED: window.onerror handler invoked');
}
</script>
</head>
<body onload="test();">
<hr>
<div id='console'></div>
<div onerror="print('FAILED: inline script load error bubbled')">
    <script src="file:///GregTheBunny.js" onerror="scriptOnerror()"></script>
</div>

</body>
</html>
