{"version": 3, "sources": ["out.js"], "names": ["foo", "console", "log", "baz"], "mappings": "YAAA,IAAIA,KAAM,CACVC,SAAQC,IAAIF,IACZ,IAAIG,KAAM,YACVF,SAAQC,IAAIC;;AAHZ,IAAI,GAAG,GAAG,CAAC,CAAC;AACZ,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACjB,IAAI,GAAG,GAAG,SAAN,GAAG,GAAe,EAAE,CAAC;AACzB,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC", "file": "input.js", "sourceRoot": ".", "sourcesContent": ["var foo = 1;\nconsole.log(foo);\nvar baz = function () {};\nconsole.log(baz);\n"]}