<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

 <head>

  <title>CSS Writing Modes Test: absolutely positioned non-replaced element - 'direction: rtl' and 'top', 'bottom' and 'height' are not 'auto' (overconstrained)</title>

  <link rel="author" title="Gérard Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" />
  <link rel="help" href="http://www.w3.org/TR/css-writing-modes-3/#vertical-layout" title="7.1 Principles of Layout in Vertical Writing Modes" />
  <link rel="help" href="http://www.w3.org/TR/CSS21/visudet.html#abs-non-replaced-height" title="10.6.4 Absolutely positioned, non-replaced elements" />
  <link rel="match" href="abs-pos-non-replaced-vlr-003-ref.xht" />

  <meta name="flags" content="ahem image" />
  <meta name="assert" content="When 'direction' is 'rtl' and 'top', 'bottom' and 'height' are not 'auto' and if the values are over-constrained, then ignore the value for 'bottom' and solve for that value." />

  <link rel="stylesheet" type="text/css" href="/fonts/ahem.css" />
  <style type="text/css"><![CDATA[
  div#containing-block
    {
      background: red url("support/bg-red-2col-3row-320x320.png");
      color: transparent;
      direction: rtl;
      font: 80px/1 Ahem;
      height: 320px;
      position: relative;
      width: 320px;
    }

  div#containing-block > span
    {
      background-color: red;
      bottom: 2em;
      color: green;
      height: 1em;
      position: absolute;
      top: 2em;
      writing-mode: vertical-rl;
    }

/*
"
If none of the three are 'auto': If both 'margin-top' and 'margin-bottom' are 'auto', solve the equation under the extra constraint that the two margins get equal values. If one of 'margin-top' or 'margin-bottom' is 'auto', solve the equation for that value. If the values are over-constrained, ignore the value for 'bottom' and solve for that value.
"

'top' + 'margin-top' + 'border-top-width' + 'padding-top' + 'height' + 'padding-bottom' + 'border-bottom-width' + 'margin-bottom' + 'bottom' = height of containing block

So:

    160px : top
  +
      0px : margin-top
  +
      0px : border-top-width
  +
      0px : padding-top
  +
     80px : height
  +
      0px : padding-bottom
  +
      0px : border-bottom-width
  +
      0px : margin-bottom
  +
    160px : bottom
    =====================
    320px : height of containing block

gives us

    160px : top
  +
      0px : margin-top
  +
      0px : border-top-width
  +
      0px : padding-top
  +
     80px : height
  +
      0px : padding-bottom
  +
      0px : border-bottom-width
  +
      0px : margin-bottom
  +
  (solve) : bottom
    =====================
    320px : height of containing block

And so computed bottom value must be 80px .
*/

  ]]></style>

 </head>

 <body>

  <p><img src="support/pass-cdts-abs-pos-non-replaced.png" width="246" height="36" alt="Image download support must be enabled" /></p>

  <div id="containing-block">1 2 34<span>X</span></div>

 </body>
</html>