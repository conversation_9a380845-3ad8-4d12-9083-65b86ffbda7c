<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright 2015 The Chromium Authors
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file.
-->

<!-- The location bar also know as URL bar -->
<merge
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <include layout="@layout/location_status" />

    <include
        android:id="@+id/url_bar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="3dp"
        android:layout_marginBottom="3dp"
        android:layout_marginStart="@dimen/location_bar_icon_width"
        android:layout_marginEnd="80dp"
        android:layout_gravity="center_vertical"
        android:nextFocusForward="@+id/menu_button"
        tools:ignore="InconsistentLayout"
        layout="@layout/url_bar"/>

    <include layout="@layout/url_action_container" />

</merge>
