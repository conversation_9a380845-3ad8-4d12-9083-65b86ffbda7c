// Copyright 2017 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef ANDROID_WEBVIEW_BROWSER_AW_RENDERER_PRIORITY_H_
#define ANDROID_WEBVIEW_BROWSER_AW_RENDERER_PRIORITY_H_

namespace android_webview {

// TODO(boliu): Remove INITIAL.
// GENERATED_JAVA_ENUM_PACKAGE: org.chromium.android_webview.renderer_priority
enum class RendererPriority { INITIAL = -1, WAIVED = 0, LOW = 1, HIGH = 2 };

}  // namespace android_webview

#endif  // ANDROID_WEBVIEW_BROWSER_AW_RENDERER_PRIORITY_H_
