<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright 2021 The Chromium Authors
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file.
-->
 <LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/progress_bar_overlay"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center"
    android:visibility="gone">

    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_height="@dimen/progress_bar_size"
        android:layout_marginBottom="@dimen/progress_bar_margin_bottom"
        android:layout_marginTop="@dimen/progress_bar_margin_top"
        android:layout_width="@dimen/progress_bar_size"/>

    <ImageView
        android:id="@+id/confirmation_icon"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/progress_bar_size"
        android:src="@drawable/checkmark_blue"
        android:visibility="gone"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/progress_bar_message"
        android:layout_height="wrap_content"
        android:layout_width="wrap_content"
        android:textAppearance="@style/TextAppearance.TextMediumThick.Accent1" />
    </LinearLayout>
