// GENERATED CONTENT - DO NOT EDIT
// Content was automatically extracted by <PERSON><PERSON> into webref
// (https://github.com/w3c/webref)
// Source: WebGL WEBGL_compressed_texture_astc Extension Specification (https://registry.khronos.org/webgl/extensions/WEBGL_compressed_texture_astc/)

[Exposed=(Window,Worker), LegacyNoInterfaceObject]
interface WEBGL_compressed_texture_astc {
    /* Compressed Texture Format */
    const GLenum COMPRESSED_RGBA_ASTC_4x4_KHR = 0x93B0;
    const GLenum COMPRESSED_RGBA_ASTC_5x4_KHR = 0x93B1;
    const GLenum COMPRESSED_RGBA_ASTC_5x5_KHR = 0x93B2;
    const GLenum COMPRESSED_RGBA_ASTC_6x5_KHR = 0x93B3;
    const GLenum COMPRESSED_RGBA_ASTC_6x6_KHR = 0x93B4;
    const GLenum COMPRESSED_RGBA_ASTC_8x5_KHR = 0x93B5;
    const GLenum COMPRESSED_RGBA_ASTC_8x6_KHR = 0x93B6;
    const GLenum COMPRESSED_RGBA_ASTC_8x8_KHR = 0x93B7;
    const GLenum COMPRESSED_RGBA_ASTC_10x5_KHR = 0x93B8;
    const GLenum COMPRESSED_RGBA_ASTC_10x6_KHR = 0x93B9;
    const GLenum COMPRESSED_RGBA_ASTC_10x8_KHR = 0x93BA;
    const GLenum COMPRESSED_RGBA_ASTC_10x10_KHR = 0x93BB;
    const GLenum COMPRESSED_RGBA_ASTC_12x10_KHR = 0x93BC;
    const GLenum COMPRESSED_RGBA_ASTC_12x12_KHR = 0x93BD;

    const GLenum COMPRESSED_SRGB8_ALPHA8_ASTC_4x4_KHR = 0x93D0;
    const GLenum COMPRESSED_SRGB8_ALPHA8_ASTC_5x4_KHR = 0x93D1;
    const GLenum COMPRESSED_SRGB8_ALPHA8_ASTC_5x5_KHR = 0x93D2;
    const GLenum COMPRESSED_SRGB8_ALPHA8_ASTC_6x5_KHR = 0x93D3;
    const GLenum COMPRESSED_SRGB8_ALPHA8_ASTC_6x6_KHR = 0x93D4;
    const GLenum COMPRESSED_SRGB8_ALPHA8_ASTC_8x5_KHR = 0x93D5;
    const GLenum COMPRESSED_SRGB8_ALPHA8_ASTC_8x6_KHR = 0x93D6;
    const GLenum COMPRESSED_SRGB8_ALPHA8_ASTC_8x8_KHR = 0x93D7;
    const GLenum COMPRESSED_SRGB8_ALPHA8_ASTC_10x5_KHR = 0x93D8;
    const GLenum COMPRESSED_SRGB8_ALPHA8_ASTC_10x6_KHR = 0x93D9;
    const GLenum COMPRESSED_SRGB8_ALPHA8_ASTC_10x8_KHR = 0x93DA;
    const GLenum COMPRESSED_SRGB8_ALPHA8_ASTC_10x10_KHR = 0x93DB;
    const GLenum COMPRESSED_SRGB8_ALPHA8_ASTC_12x10_KHR = 0x93DC;
    const GLenum COMPRESSED_SRGB8_ALPHA8_ASTC_12x12_KHR = 0x93DD;

    // Profile query support.
    sequence<DOMString> getSupportedProfiles();
};
