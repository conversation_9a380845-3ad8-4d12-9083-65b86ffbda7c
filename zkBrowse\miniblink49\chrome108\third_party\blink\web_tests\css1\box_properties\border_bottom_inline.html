<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.5.20 border-bottom</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
.one {border-bottom: purple double 10px;}
.two {border-bottom: purple thin solid;}
</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.one {border-bottom: purple double 10px;}
.two {border-bottom: purple thin solid;}

</PRE>
<HR>
<P style="background-color: silver;">
This is an unstyled element, save for the background color, and containing inline elements with classes of <SPAN class="one">class one</SPAN>, which should have a 10-pixel purple double bottom border; and <SPAN class="two">class two</SPAN>, which should have a thin solid purple bottom border.  The line-height of the parent element should not change on any line.
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P style="background-color: silver;">
This is an unstyled element, save for the background color, and containing inline elements with classes of <SPAN class="one">class one</SPAN>, which should have a 10-pixel purple double bottom border; and <SPAN class="two">class two</SPAN>, which should have a thin solid purple bottom border.  The line-height of the parent element should not change on any line.
</P>
</TD></TR></TABLE></BODY>
</HTML>
