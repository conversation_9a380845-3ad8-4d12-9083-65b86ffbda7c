<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

 <head>

  <title>CSS Test: Background-image - color visible in transparent parts of image</title>

  <link rel="author" title="<PERSON><PERSON>" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" />
  <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" title="14.2.1 Background properties" />

  <meta content="image" name="flags" />
  <meta content="A 'background-image' is rendered on top of 'background-color'. Therefore, the background-color (blue in this test) should be visible in the transparent parts of a background-image (orange text)." name="assert" />

  <style type="text/css"><![CDATA[
  div
  {
  background: blue url("support/orange-PASS-300x150.png");
  height: 150px;
  width: 300px;
  }
  ]]></style>

 </head>

 <body>

  <p>Test passes if there is the word "PASS" in orange inside a filled blue rectangle.</p>

  <div></div>

 </body>
</html>
