// Copyright 2020 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

import "oaidl.idl";
import "ocidl.idl";


// Private interfaces for the Chromium Updater.
// For documentation, see the similar types defined in updater::UpdateServiceInternal.

[
  object,
  dual,
  uuid(PLACEHOLDER-GUID-D272C794-2ACE-4584-B993-3B90C622BE65),
  helpstring("IUpdaterInternalCallback Interface"),
  pointer_default(unique)
]
interface IUpdaterInternalCallback : IUnknown {
  HRESULT Run([in] LONG result);
};

[
  object,
  dual,
  uuid(PLACEHOLDER-GUID-526DA036-9BD3-4697-865A-DA12D37DFFCA),
  helpstring("IUpdaterInternal Interface"),
  pointer_default(unique)
]
interface IUpdaterInternal : IUnknown {
  HRESULT Run([in] IUpdaterInternalCallback* callback);
  HRESULT InitializeUpdateService([in] IUpdaterInternalCallback* callback);
};

[
  uuid(PLACEHOLDER-GUID-C6CE92DB-72CA-42EF-8C98-6EE92481B3C9),
  version(1.0),
  helpstring("Chromium Updater internal type library.")
]
library UpdaterInternalLib {
  importlib("stdole2.tlb");

  [
    uuid(PLACEHOLDER-GUID-1F87FE2F-D6A9-4711-9D11-8187705F8457),
    helpstring("UpdaterInternal per-user Class")
  ]
  coclass UpdaterInternalUserClass
  {
    [default] interface IUnknown;
  }

  [
    uuid(PLACEHOLDER-GUID-4556BA55-517E-4F03-8016-331A43C269C9),
    helpstring("UpdaterInternal per-system Class")
  ]
  coclass UpdaterInternalSystemClass
  {
    [default] interface IUnknown;
  }

  interface IUpdaterInternal;
  interface IUpdaterInternalCallback;
};
