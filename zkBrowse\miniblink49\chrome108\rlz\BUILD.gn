# Copyright 2014 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# Note that this build file assumes rlz_use_chrome_net which is a condition in
# the GYP file, but is always true for Chrome builds.

import("//build/config/chromeos/ui_mode.gni")
import("//rlz/buildflags/buildflags.gni")
import("//testing/test.gni")

config("rlz_lib_config") {
  defines = [ "RLZ_NETWORK_IMPLEMENTATION_CHROME_NET" ]
}

source_set("rlz_utils") {
  sources = [
    "lib/assert.cc",
    "lib/assert.h",
    "lib/crc32.h",
    "lib/crc32_wrapper.cc",
    "lib/lib_values.cc",
    "lib/lib_values.h",
    "lib/net_response_check.cc",
    "lib/net_response_check.h",
    "lib/rlz_api.h",
    "lib/rlz_enums.h",
    "lib/string_utils.cc",
    "lib/string_utils.h",
  ]

  public_deps = [ "//base" ]

  deps = [
    "//build:chromeos_buildflags",
    "//third_party/zlib",
  ]
}

if (enable_rlz_support) {
  source_set("rlz_lib_no_network") {
    sources = [
      "lib/crc8.cc",
      "lib/crc8.h",
      "lib/machine_deal_win.h",
      "lib/machine_id.cc",
      "lib/machine_id.h",
      "lib/rlz_lib_clear.cc",
      "lib/rlz_lib_clear.h",
      "lib/rlz_value_store.h",
      "lib/supplementary_branding.cc",
      "lib/supplementary_branding.h",
      "lib/time_util.h",
    ]

    if (is_win) {
      sources += [
        "lib/time_util_win.cc",
        "win/lib/lib_mutex.cc",
        "win/lib/lib_mutex.h",
        "win/lib/machine_deal.cc",
        "win/lib/machine_deal.h",
        "win/lib/machine_id_win.cc",
        "win/lib/process_info.cc",
        "win/lib/process_info.h",
        "win/lib/registry_util.cc",
        "win/lib/registry_util.h",
        "win/lib/rlz_lib_win.cc",
        "win/lib/rlz_value_store_registry.cc",
        "win/lib/rlz_value_store_registry.h",
      ]
    } else {
      sources += [ "lib/time_util_base.cc" ]
    }

    deps = [
      ":rlz_utils",
      "//base",
      "//build:chromeos_buildflags",
    ]

    if (is_chromeos_ash) {
      sources += [
        "chromeos/lib/rlz_value_store_chromeos.cc",
        "chromeos/lib/rlz_value_store_chromeos.h",
      ]
      deps += [
        "//chromeos/ash/components/dbus",
        "//chromeos/ash/components/dbus/debug_daemon",
        "//chromeos/system",
      ]
    }

    if (is_apple) {
      sources += [
        "mac/lib/rlz_value_store_mac.h",
        "mac/lib/rlz_value_store_mac.mm",
      ]

      frameworks = [ "Foundation.framework" ]

      if (is_mac) {
        sources += [ "mac/lib/machine_id_mac.cc" ]
        frameworks += [ "IOKit.framework" ]
      }

      if (is_ios) {
        sources += [ "ios/lib/machine_id_ios.cc" ]
      }
    }

    if (is_posix) {
      sources += [
        "lib/recursive_cross_process_lock_posix.cc",
        "lib/recursive_cross_process_lock_posix.h",
      ]
    }
  }

  source_set("rlz_lib") {
    sources = [
      "lib/financial_ping.cc",
      "lib/financial_ping.h",
      "lib/rlz_lib.cc",
      "lib/rlz_lib.h",
    ]

    public_deps = [
      ":rlz_lib_no_network",
      "//base",
    ]

    deps = [
      ":rlz_utils",
      "//base/third_party/dynamic_annotations",
      "//build:chromeos_buildflags",
      "//net",
      "//services/network/public/cpp:cpp",
      "//services/network/public/mojom",
      "//url",
    ]

    if (is_chromeos_ash) {
      deps += [
        "//chromeos/ash/components/dbus",
        "//chromeos/system",
      ]
    }

    public_configs = [ ":rlz_lib_config" ]
  }

  source_set("test_support") {
    testonly = true
    sources = [
      "test/rlz_test_helpers.cc",
      "test/rlz_test_helpers.h",
    ]
    public_deps = [
      "//base",
      "//base/test:test_support",
      "//testing/gtest",
    ]
    deps = [
      ":rlz_lib",
      "//build:chromeos_buildflags",
    ]
    if (is_chromeos_ash) {
      public_deps += [ "//chromeos/system" ]
    }
  }

  test("rlz_unittests") {
    sources = [
      "lib/crc32_unittest.cc",
      "lib/crc8_unittest.cc",
      "lib/financial_ping_test.cc",
      "lib/lib_values_unittest.cc",
      "lib/machine_id_unittest.cc",
      "lib/rlz_lib_test.cc",
      "lib/string_utils_unittest.cc",
      "test/rlz_unittest_main.cc",
    ]

    if (is_win) {
      sources += [ "win/lib/machine_deal_test.cc" ]
    }

    deps = [
      ":rlz_lib",
      ":rlz_utils",
      ":test_support",
      "//base",
      "//build:chromeos_buildflags",
      "//mojo/core/embedder",
      "//net:test_support",
      "//services/network:test_support",
      "//testing/gmock",
      "//testing/gtest",
      "//third_party/zlib",
    ]
    if (is_chromeos_ash) {
      deps += [
        "//chromeos/ash/components/dbus:test_support",
        "//chromeos/ash/components/dbus/debug_daemon",
        "//chromeos/system",
      ]
    }
  }
}

if (!is_ios && !is_android) {
  executable("rlz_id") {
    sources = [ "examples/rlz_id.cc" ]
    deps = [
      ":rlz_lib",
      "//build/win:default_exe_manifest",
    ]
  }
}

if (is_win) {
  shared_library("rlz") {
    sources = [
      "win/dll/dll_main.cc",
      "win/dll/exports.cc",
    ]
    deps = [
      ":rlz_lib",
      ":rlz_utils",
      "//third_party/zlib",
    ]
  }
}
