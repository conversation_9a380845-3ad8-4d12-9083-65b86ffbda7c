<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
 <head>
  <title>CSS Test: CSS Parsing: Negative Border Widths</title>
  <link rel="author" title="<PERSON>" href="mailto:<EMAIL>"/>
  <link rel="reviewer" title="<PERSON><PERSON>" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-08-21 -->
  <link rel="alternate" href="http://www.hixie.ch/tests/adhoc/css/parsing/004.xml" type="application/xhtml+xml"/>
  <link rel="help" href="http://www.w3.org/TR/CSS21/syndata.html#length-units" />
  <link rel="match" href="border-width-010-ref.xht" />

  <meta name="flags" content="invalid"/>
  <style type="text/css">
   .test { border-color: green; border-width: 8px; border-style: solid; border: red solid -1px; display: block; }
  </style>
 </head>
 <body>
  <div class="test">
    Test passes if this sentence has a <strong>green border</strong>.
  </div>
 </body>
</html>
