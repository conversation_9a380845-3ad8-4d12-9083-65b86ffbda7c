<html>
<head>
<script>
if (window.testRunner)
  testRunner.dumpAsText();

function eventHandlerOne(event)
{
    if (event.stopImmediatePropagation)
        event.stopImmediatePropagation();
    else
        event.stopPropagation();
}

function eventHandlerTwo(event)
{
    document.getElementById('result').innerHTML = 'FAIL';
}
</script>
</head>
<body onload="document.forms[0].reset()">
<div id="result">PASS</div>
<form></form>
<script>
document.forms[0].addEventListener("reset", eventHandlerOne, true);
document.forms[0].addEventListener("reset", eventHandlerTwo, true);
</script>
</body>
</html>

