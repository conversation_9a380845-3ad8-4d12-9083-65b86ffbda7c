<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

 <head>

  <title>CSS Writing Modes Test: baseline-alignment of non-empty non-replaced inline-block element and 'vertical-rl'</title>

  <link rel="author" title="Gérard Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" />
  <link rel="help" href="http://www.w3.org/TR/css-writing-modes-3/#replaced-baselines" title="4.3 Atomic Inline Baselines" />
  <link rel="match" href="../reference/ref-filled-green-100px-square.xht" />

  <meta content="ahem" name="flags" />
  <meta content="This test checks that the baseline-alignment of a non-empty inline-block box in the line box with 'writing-mode' set to 'vertical-rl' is 'central' and not 'alphabetic'." name="assert" />

  <link rel="stylesheet" type="text/css" href="/fonts/ahem.css" />
  <style type="text/css"><![CDATA[
  div
    {
      background-color: red;
      color: green;
      font: 50px/1 Ahem; /* computes to 50px/50px */
      height: 2em; /* computes to 100px */
      writing-mode: vertical-rl;
    }

  span
    {
      display: inline-block;
    }
  ]]></style>
 </head>

 <body>

  <p>Test passes if there is a filled green square and <strong>no red</strong>.</p>

  <div>
    TL
    <span>FZ</span>
  </div>

 </body>
</html>
