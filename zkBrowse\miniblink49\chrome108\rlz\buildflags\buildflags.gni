# Copyright 2016 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/chrome_build.gni")
import("//build/config/chromeos/ui_mode.gni")

# Whether we are using the rlz library or not.  Platforms like Android send
# rlz codes for searches but do not use the library.
enable_rlz_support = is_win || is_apple || is_chromeos_ash

declare_args() {
  enable_rlz = is_chrome_branded && enable_rlz_support
}

assert(!enable_rlz || enable_rlz_support,
       "RLZ is only supported on Windows, Apple and ChromeOS Ash.")
