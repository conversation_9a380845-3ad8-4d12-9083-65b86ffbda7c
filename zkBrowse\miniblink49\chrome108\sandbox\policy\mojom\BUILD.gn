# Copyright 2021 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//chromeos/ash/components/assistant/assistant.gni")
import("//components/services/screen_ai/buildflags/features.gni")
import("//mojo/public/tools/bindings/mojom.gni")
import("//ppapi/buildflags/buildflags.gni")
import("//printing/buildflags/buildflags.gni")

mojom("mojom") {
  generate_java = true
  sources = [
    "context.mojom",
    "sandbox.mojom",
  ]

  enabled_features = []
  if (enable_oop_printing) {
    enabled_features += [ "enable_oop_printing" ]
  }
  if (enable_ppapi) {
    enabled_features += [ "enable_ppapi" ]
  }
  if (is_linux || is_chromeos) {
    enabled_features += [ "has_zygote" ]
  }
  if (is_linux || is_chromeos_ash) {
    enabled_features += [ "is_linux_or_chromeos_ash" ]
  }
  if (enable_cros_libassistant) {
    enabled_features += [ "enable_cros_libassistant" ]
  }
  if (enable_screen_ai_service) {
    enabled_features += [ "enable_screen_ai_service" ]
  }
}
