[input-events-get-target-ranges-non-collapsed-selection.tentative.html?Backspace]
  [Backspace at "<p>{ abc }</p>"]
    expected: FAIL

  [Backspace at "<p>abc   [</p><p>\]   def</p>"]
    expected: FAIL

  [Backspace at "<p>ab\[c </p><p>\] def</p>"]
    expected: FAIL

  [Backspace at "<p>abc \[</p><p>\] def</p>"]
    expected: FAIL

  [Backspace at "<p>abc \[</p><p> \]def</p>"]
    expected: FAIL

  [Backspace at "<p>abc\[<img>\]def</p>"]
    expected: FAIL

  [Backspace at "<div>abc \[<hr>\]def</div>"]
    expected: FAIL

  [Backspace at "<div>abc \[<hr>\] def</div>"]
    expected: FAIL

  [Backspace at "<div>abc {<hr>} def</div>"]
    expected: FAIL

  [Backspace at "<div>abc\[<p>\]def<br>ghi</p></div>"]
    expected: FAIL

  [Backspace at "<div>abc   [<p>\]   def<br>ghi</p></div>"]
    expected: FAIL

  [Backspace at "<div><p>abc   [</p>\]   def</div>"]
    expected: FAIL

  [Backspace at "<div>abc\[<ul><li>\]def</li></ul>ghi</div>"]
    expected: FAIL

  [Backspace at "<div>abc  [<ul><li>\] def </li></ul>  ghi</div>"]
    expected: FAIL

  [Backspace at "<div>abc <ul><li>  def  [</li></ul>\] ghi</div>"]
    expected: FAIL

  [Backspace at "<div>abc\[<ul><li>\]def</li><li>ghi</li></ul>jkl</div>"]
    expected: FAIL

  [Backspace at "<div>abc\[<ul><li>def</li><li>\]ghi</li></ul>jkl</div>"]
    expected: FAIL

  [Backspace at "<div>abc<ul><li>def\[</li><li>\]ghi</li></ul>jkl</div>"]
    expected: FAIL

  [Backspace at "<p>abc\[</p><p>}<br></p>"]
    expected: FAIL

  [Backspace at "<div><p>abc\[</p>\]def</div>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<div><p>abc   [</p>\]   def</div>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<div>abc\[<ul><li>\]def</li></ul>ghi</div>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<div>abc  [<ul><li>\] def </li></ul>  ghi</div>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<div>abc<ul><li>def\[</li></ul>\]ghi</div>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<div>abc <ul><li>  def  [</li></ul>\] ghi</div>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<div>abc\[<ul><li>\]def</li><li>ghi</li></ul>jkl</div>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<div>abc\[<ul><li>def</li><li>\]ghi</li></ul>jkl</div>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<div>abc<ul><li>def\[</li><li>\]ghi</li></ul>jkl</div>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<div>abc<ul><li>def</li><li>ghi\[</li></ul>\]jkl</div>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<div>abc<ul><li>def\[</li><li>ghi</li></ul>\]jkl</div>" - comparing innerHTML]
    expected: FAIL

[input-events-get-target-ranges-non-collapsed-selection.tentative.html?Delete]
  [Delete at "<p>{ abc }</p>"]
    expected: FAIL

  [Delete at "<p>abc   [</p><p>\]   def</p>"]
    expected: FAIL

  [Delete at "<p>ab\[c </p><p>\] def</p>"]
    expected: FAIL

  [Delete at "<p>abc \[</p><p>\] def</p>"]
    expected: FAIL

  [Delete at "<p>abc \[</p><p> \]def</p>"]
    expected: FAIL

  [Delete at "<p>abc\[<img>\]def</p>"]
    expected: FAIL

  [Delete at "<div>abc \[<hr>\]def</div>"]
    expected: FAIL

  [Delete at "<div>abc \[<hr>\] def</div>"]
    expected: FAIL

  [Delete at "<div>abc {<hr>} def</div>"]
    expected: FAIL

  [Delete at "<div>abc\[<p>\]def<br>ghi</p></div>"]
    expected: FAIL

  [Delete at "<div>abc   [<p>\]   def<br>ghi</p></div>"]
    expected: FAIL

  [Delete at "<div><p>abc   [</p>\]   def</div>"]
    expected: FAIL

  [Delete at "<div>abc\[<ul><li>\]def</li></ul>ghi</div>"]
    expected: FAIL

  [Delete at "<div>abc  [<ul><li>\] def </li></ul>  ghi</div>"]
    expected: FAIL

  [Delete at "<div>abc <ul><li>  def  [</li></ul>\] ghi</div>"]
    expected: FAIL

  [Delete at "<div>abc\[<ul><li>\]def</li><li>ghi</li></ul>jkl</div>"]
    expected: FAIL

  [Delete at "<div>abc\[<ul><li>def</li><li>\]ghi</li></ul>jkl</div>"]
    expected: FAIL

  [Delete at "<div>abc<ul><li>def\[</li><li>\]ghi</li></ul>jkl</div>"]
    expected: FAIL

  [Delete at "<p>abc\[</p><p>}<br></p>"]
    expected: FAIL

  [Delete at "<div><p>abc\[</p>\]def</div>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<div><p>abc   [</p>\]   def</div>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<div>abc\[<ul><li>\]def</li></ul>ghi</div>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<div>abc  [<ul><li>\] def </li></ul>  ghi</div>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<div>abc<ul><li>def\[</li></ul>\]ghi</div>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<div>abc <ul><li>  def  [</li></ul>\] ghi</div>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<div>abc\[<ul><li>\]def</li><li>ghi</li></ul>jkl</div>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<div>abc\[<ul><li>def</li><li>\]ghi</li></ul>jkl</div>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<div>abc<ul><li>def\[</li><li>\]ghi</li></ul>jkl</div>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<div>abc<ul><li>def</li><li>ghi\[</li></ul>\]jkl</div>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<div>abc<ul><li>def\[</li><li>ghi</li></ul>\]jkl</div>" - comparing innerHTML]
    expected: FAIL

[input-events-get-target-ranges-non-collapsed-selection.tentative.html?TypingA]
  [TypingA at "<p>{ abc }</p>"]
    expected: FAIL

  [TypingA at "<p>abc   [</p><p>\]   def</p>"]
    expected: FAIL

  [TypingA at "<p>ab\[c </p><p>\] def</p>"]
    expected: FAIL

  [TypingA at "<p>abc \[</p><p>\] def</p>"]
    expected: FAIL

  [TypingA at "<p>abc \[</p><p> \]def</p>"]
    expected: FAIL

  [TypingA at "<p>abc\[<img>\]def</p>"]
    expected: FAIL

  [TypingA at "<div>abc \[<hr>\]def</div>"]
    expected: FAIL

  [TypingA at "<div>abc \[<hr>\] def</div>"]
    expected: FAIL

  [TypingA at "<div>abc {<hr>} def</div>"]
    expected: FAIL

  [TypingA at "<div>abc\[<p>\]def<br>ghi</p></div>"]
    expected: FAIL

  [TypingA at "<div>abc   [<p>\]   def<br>ghi</p></div>"]
    expected: FAIL

  [TypingA at "<div><p>abc   [</p>\]   def</div>"]
    expected: FAIL

  [TypingA at "<div>abc\[<ul><li>\]def</li></ul>ghi</div>"]
    expected: FAIL

  [TypingA at "<div>abc  [<ul><li>\] def </li></ul>  ghi</div>"]
    expected: FAIL

  [TypingA at "<div>abc <ul><li>  def  [</li></ul>\] ghi</div>"]
    expected: FAIL

  [TypingA at "<div>abc\[<ul><li>\]def</li><li>ghi</li></ul>jkl</div>"]
    expected: FAIL

  [TypingA at "<div>abc\[<ul><li>def</li><li>\]ghi</li></ul>jkl</div>"]
    expected: FAIL

  [TypingA at "<div>abc<ul><li>def\[</li><li>\]ghi</li></ul>jkl</div>"]
    expected: FAIL

  [TypingA at "<p>{ abc }</p>" - comparing innerHTML]
    expected: FAIL

  [TypingA at "<div><p>abc\[</p>\]def</div>" - comparing innerHTML]
    expected: FAIL

  [TypingA at "<div><p>abc   [</p>\]   def</div>" - comparing innerHTML]
    expected: FAIL

  [TypingA at "<div>abc\[<ul><li>\]def</li></ul>ghi</div>" - comparing innerHTML]
    expected: FAIL

  [TypingA at "<div>abc  [<ul><li>\] def </li></ul>  ghi</div>" - comparing innerHTML]
    expected: FAIL

  [TypingA at "<div>abc<ul><li>def\[</li></ul>\]ghi</div>" - comparing innerHTML]
    expected: FAIL

  [TypingA at "<div>abc <ul><li>  def  [</li></ul>\] ghi</div>" - comparing innerHTML]
    expected: FAIL

  [TypingA at "<div>abc\[<ul><li>\]def</li><li>ghi</li></ul>jkl</div>" - comparing innerHTML]
    expected: FAIL

  [TypingA at "<div>abc\[<ul><li>def</li><li>\]ghi</li></ul>jkl</div>" - comparing innerHTML]
    expected: FAIL

  [TypingA at "<div>abc<ul><li>def\[</li><li>\]ghi</li></ul>jkl</div>" - comparing innerHTML]
    expected: FAIL

  [TypingA at "<div>abc<ul><li>def</li><li>ghi\[</li></ul>\]jkl</div>" - comparing innerHTML]
    expected: FAIL

  [TypingA at "<div>abc<ul><li>def\[</li><li>ghi</li></ul>\]jkl</div>" - comparing innerHTML]
    expected: FAIL
