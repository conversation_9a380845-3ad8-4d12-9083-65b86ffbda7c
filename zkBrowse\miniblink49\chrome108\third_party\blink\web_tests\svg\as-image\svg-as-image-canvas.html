<!DOCTYPE HTML>
<html>
<head>
    <title>Test for WK94377: canvas.context.drawImage() should draw SVG images at the correct scale.</title>
</head>
<body>
<div>
    This test passes if there is a 100 x 100 green rect.<br/>
    <canvas id="canvas" width="200" height="200"></canvas>
    <img id="image" width="200" height="200" style="visibility: hidden;" src="resources/100px-green-rect.svg"></img>
</div>
<script>
    function finishTest() {
        var image = document.getElementById("image");
        var canvas = document.getElementById("canvas");
        setTimeout(function() {
            var context = canvas.getContext('2d');
            // We draw the 200x200 image that contains a 100x100 green rect.
            context.drawImage(image, 0, 0, 200, 200);

            if (window.testRunner)
                testRunner.notifyDone();
        }, 1);
    }

    image.onload = function() {
        if (window.testRunner) {
            testRunner.waitUntilDone();
            testRunner.setBackingScaleFactor(2, finishTest);
        } else
            finishTest();
    }
</script>
</body>
</html>
