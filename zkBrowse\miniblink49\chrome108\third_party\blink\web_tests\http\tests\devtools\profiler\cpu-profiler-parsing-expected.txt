Tests profile ending with GC node is parsed correctly.
Profile tree:
(root) id:1 total:3 self:0 depth:-1
  (garbage collector) id:2 total:1 self:1 depth:0
  foo id:3 total:2 self:1 depth:0
    bar id:4 total:1 self:1 depth:1
samples: 4, 4, 3, 4, 2
timestamps: 1.5, 1.75, 2.75, 3, 4, 4.625
forEachFrame iterator structure:
+ 0 foo 1.5
  + 1 bar 1.5
  - 1 bar 1.5 1.25 1.25
  + 1 bar 3
    + 2 (garbage collector) 4
    - 2 (garbage collector) 4 0.625 0.625
  - 1 bar 3 1.625 1
- 0 foo 1.5 3.125 0.25

