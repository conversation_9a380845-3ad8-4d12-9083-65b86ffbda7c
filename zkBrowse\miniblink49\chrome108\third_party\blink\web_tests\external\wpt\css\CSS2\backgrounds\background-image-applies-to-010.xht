<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background-image applied to elements with 'display' set to 'list-item'</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="<PERSON><PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-04-21 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background-image" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <meta name="flags" content="image" />
        <meta name="assert" content="The 'background-image' property applies to elements with 'display' set to 'list-item'." />
        <style type="text/css">
            div
            {
                background-image: url('support/black15x15.png');
                display: list-item;
                height: 1in;
                margin-left: 2em;
                width: 1in;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is a filled black square and a marker bullet on its left-hand side.</p>
        <div></div>
    </body>
</html>