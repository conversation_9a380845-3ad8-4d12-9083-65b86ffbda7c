<!DOCTYPE html>
<title>
  Test for 'object-fit: cover' on SVG image w/ aspect ratio but no
  intrinsic size
</title>
<style type="text/css">
  img {
      background: red;
      width: 160px;
      height: 100px;
  }
</style>
<img src="data:image/svg+xml,
          <svg xmlns='http://www.w3.org/2000/svg'>
            <rect stroke-width='20' stroke='black'
               x='10' y='-10' width='140' height='140' fill='lime'/>
          </svg>">
