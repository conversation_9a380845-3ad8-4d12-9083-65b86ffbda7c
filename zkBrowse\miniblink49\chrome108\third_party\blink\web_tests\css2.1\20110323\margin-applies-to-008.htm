<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
    <head>
        <title>CSS Test: Margin applied to element with display inline</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/">
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#propdef-margin">
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#margin-properties">
        <meta name="flags" content="">
        <meta name="assert" content="The 'Margin' property applies to elements with a display of inline.">
        <style type="text/css">
            div
            {
                display: inline;
            }
            #div1
            {
                border-left: 5px solid blue;
                border-right: 5px solid blue;
            }
            div div
            {
                border-left: 5px solid orange;
                border-right: 5px solid orange;
                margin: 50px;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is space between the blue and orange lines below.</p>
        <div id="div1">
            <div>Filler Text</div>
        </div>
    </body>
</html>