Verify that search across all files omits filesystem uiSourceCodes with binding to network.


Running: waitForUISourceCodes

Running: dumpSearchResults
Search result #1: uiSourceCode.url = file:///var/www/devtools/persistence/resources/foo.js
  search match #1: lineNumber = 2, lineContent = 'window.foo = ()=>'foo';'
Search result #2: uiSourceCode.url = http://127.0.0.1:8000/devtools/persistence/resources/foo.js
  search match #1: lineNumber = 2, lineContent = 'window.foo = ()=>'foo';'

Running: addFileMapping

Running: dumpSearchResults
Search result #1: uiSourceCode.url = file:///var/www/devtools/persistence/resources/foo.js
  search match #1: lineNumber = 2, lineContent = 'window.foo = ()=>'foo';'

