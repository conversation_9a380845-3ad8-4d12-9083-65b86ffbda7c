ServiceWorkers must be shown correctly even if there is a redundant worker.

The first ServiceWorker is activated.
==== ServiceWorkersView ====
http://127.0.0.1:8000/devtools/service-workers/resources/service-worker-redundant-scope/
Network requests
Update
Unregister
Source
changing-worker.php
Received
Status
#N activated and is running
stop
Clients
Push
Push
Sync
Sync
Periodic Sync
Periodic Sync
Update Cycle
Version
Update Activity
Timeline
#N
Install
​
Start time
End time
#N
Wait
​
Start time
End time
#N
Activate
​
Start time
End time
============================
The second Serviceworker is installed.
==== ServiceWorkersView ====
http://127.0.0.1:8000/devtools/service-workers/resources/service-worker-redundant-scope/
Network requests
Update
Unregister
Source
changing-worker.php
Received
Status
#N activated and is running
stop
#N waiting to activate
skipWaiting
Received
Clients
Push
Push
Sync
Sync
Periodic Sync
Periodic Sync
Update Cycle
Version
Update Activity
Timeline
#N
Install
​
Start time
End time
#N
Wait
​
Start time
End time
#N
Activate
​
Start time
End time
============================
The first ServiceWorker worker became redundant and stopped.
==== ServiceWorkersView ====
http://127.0.0.1:8000/devtools/service-workers/resources/service-worker-redundant-scope/
Network requests
Update
Unregister
Source
changing-worker.php
Received
Status
#N activated and is running
stop
Clients
Push
Push
Sync
Sync
Periodic Sync
Periodic Sync
Update Cycle
Version
Update Activity
Timeline
#N
Install
​
Start time
End time
#N
Wait
​
Start time
End time
#N
Activate
​
Start time
End time
============================

