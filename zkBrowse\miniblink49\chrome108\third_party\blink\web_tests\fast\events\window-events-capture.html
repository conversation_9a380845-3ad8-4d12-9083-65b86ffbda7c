<html>
<head>
<script>

if (window.testRunner) {
    testRunner.dumpAsText();
    testRunner.waitUntilDone();
}
function test()
{
    if (window.eventSender) {
        eventSender.mouseMoveTo(52, 52);
        eventSender.mouseDown();
        eventSender.mouseUp();
    }
    window.setTimeout('finish()', 0);
}

function finish()
{
    if (window.testRunner)
        testRunner.notifyDone();
}
        
function log(text) {
    document.getElementById("result").innerHTML = document.getElementById("result").innerHTML + text + "<br>";
}

function windowClick(e) { 
    log("Capture: Window.onClick is called"); 
};

window.addEventListener("click", windowClick, true);
</script>

</head>
<body onload="test()">
    
    This tests that window events will fire in the capture phase.
    <div id="div2" style="position: absolute; top: 50; left: 50; border: solid thin" onclick="log('stopPropagation called.'); event.stopPropagation();">
        <a href="javascript:log('Default behavior for link')">Click here</a>
    </div>
        
    <div id="result" style="position: absolute; top: 100; left: 50;"><br></div>
</body>
</html>