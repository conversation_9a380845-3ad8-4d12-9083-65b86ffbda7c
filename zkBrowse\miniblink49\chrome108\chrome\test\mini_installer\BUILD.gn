# Copyright 2018 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

if (is_win) {
  files = get_path_info([
                          "argument_parser.py",
                          "chrome_helper.py",
                          "create_zip.py",
                          "config/chrome_beta_installed.prop",
                          "config/chrome_beta_no_pv.prop",
                          "config/chrome_beta_not_installed.prop",
                          "config/chrome_beta_not_inuse.prop",
                          "config/chrome_canary_installed.prop",
                          "config/chrome_canary_inuse.prop",
                          "config/chrome_canary_no_pv.prop",
                          "config/chrome_canary_not_installed.prop",
                          "config/chrome_canary_not_inuse.prop",
                          "config/chrome_dev_installed.prop",
                          "config/chrome_dev_no_pv.prop",
                          "config/chrome_dev_not_installed.prop",
                          "config/chrome_dev_not_inuse.prop",
                          "config/chrome_system_installed.prop",
                          "config/chrome_system_inuse.prop",
                          "config/chrome_system_no_pv.prop",
                          "config/chrome_system_not_installed.prop",
                          "config/chrome_system_not_inuse.prop",
                          "config/chrome_user_binaries_killed.prop",
                          "config/chrome_user_installed.prop",
                          "config/chrome_user_inuse.prop",
                          "config/chrome_user_killed.prop",
                          "config/chrome_user_no_pv.prop",
                          "config/chrome_user_not_installed.prop",
                          "config/chrome_user_not_inuse.prop",
                          "config/config.config",
                          "config/previous_chrome_canary_installed.prop",
                          "config/previous_chrome_system_installed.prop",
                          "config/previous_chrome_user_installed.prop",
                          "file_operations.py",
                          "installer_test.py",
                          "launch_chrome.py",
                          "process_operations.py",
                          "property_walker.py",
                          "quit_chrome.py",
                          "registry_operations.py",
                          "run_mini_installer_tests.py",
                          "test_chrome_with_chromedriver.py",
                          "test_page.html",
                          "uninstall_chrome.py",
                          "update_lastrun.py",
                          "variable_expander.py",
                          "ZIP_README.txt",
                          "zip_test_runner.bat",
                        ],
                        "abspath")

  group("mini_installer_tests") {
    testonly = true

    data_deps = [
      "//chrome/installer/mini_installer",
      "//chrome/installer/mini_installer:previous_version_mini_installer",
      "//chrome/test/chromedriver",
      "//testing:test_scripts_shared",
      "//third_party/catapult/third_party/typ",
    ]
    data = [
             "//testing/scripts/",
             "//third_party/depot_tools/download_from_google_storage.py",
             "//third_party/depot_tools/gsutil.py",
           ] + files
  }

  action("zip_mini_installer_tests") {
    testonly = true
    output_zip_file = "$root_out_dir/mini_installer_tests.zip"
    script = "create_zip.py"
    inputs = files
    outputs = [ output_zip_file ]
    args = [
      "--o",
      rebase_path(output_zip_file, root_build_dir),
      "--chromedriver-path",
      rebase_path("$root_out_dir/chromedriver.exe", root_build_dir),
    ]
    deps = [
      "//chrome/test/chromedriver",
      "//third_party/catapult/third_party/typ",
    ]
  }
}
