Tests crypto.subtle.encrypt() using a BufferSource that was neutered (prior to encrypt())

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".

Importing key...

Encrypting empty plaintext (as a control group)...
PASS: Encryption should be [c84af0b613435d5d9182801a9bd9320b] and was

Creating ArrayBuffer...
PASS plainText.byteLength is 1000
Neutering plainText...
PASS plainText.byteLength is 0

Encrypting neutered plaintext...
PASS: Encryption should be [c84af0b613435d5d9182801a9bd9320b] and was
PASS successfullyParsed is true

TEST COMPLETE

