Tests resources panel shows several resources with the same url if they were loaded with inspector already opened.

Resources Tree:
Frames
    top
        resource-tree-non-unique-url-iframe.html
            Images
                styles-non-unique-url.css
            Stylesheets
                styles-non-unique-url.css
            resource-tree-non-unique-url-iframe.html
        inspected-page.html
Sources:
-------- Setting mode: [frame]
top
  inspected-page.html
  resource-tree-non-unique-url-iframe.html
    resource-tree-non-unique-url-iframe.html
    styles-non-unique-url.css
Sources:
-------- Setting mode: [frame/domain]
top
  127.0.0.1:8000
    inspected-page.html
  resource-tree-non-unique-url-iframe.html
    127.0.0.1:8000
      resource-tree-non-unique-url-iframe.html
      styles-non-unique-url.css
Sources:
-------- Setting mode: [frame/domain/folder]
top
  127.0.0.1:8000
    devtools/resources
      inspected-page.html
  resource-tree-non-unique-url-iframe.html
    127.0.0.1:8000
      devtools/resource-tree/resources
        resource-tree-non-unique-url-iframe.html
        styles-non-unique-url.css
Sources:
-------- Setting mode: [domain]
127.0.0.1:8000
  inspected-page.html
  resource-tree-non-unique-url-iframe.html
  styles-non-unique-url.css
Sources:
-------- Setting mode: [domain/folder]
127.0.0.1:8000
  devtools
    resource-tree/resources
      resource-tree-non-unique-url-iframe.html
      styles-non-unique-url.css
    resources
      inspected-page.html

