<!DOCTYPE html>
<body>
<script src="../resources/testharness.js"></script>
<script src="../resources/testharnessreport.js"></script>
<script>
test(function() {
    for (var i = 0.0; i <= 1.0; i += 0.01) {
        var rgba = 'rgba(0, 0, 0, ' + parseFloat(i.toFixed(2)) + ')';
        document.body.style.color = rgba;
        assert_equals(document.body.style.color, rgba);
        assert_equals(getComputedStyle(document.body).color, rgba);
    }
}, 'Alpha values should parse and serialize to the same value to 2 decimal places');

test(function() {
    var testCases = [
      ['rgba(0, 0, 0, 0.501)', 'rgba(0, 0, 0, 0.5)'],
      ['rgba(0, 0, 0, 0.011)', 'rgba(0, 0, 0, 0.01)'],
      ['rgba(0, 0, 0, 0.0041)', 'rgba(0, 0, 0, 0.004)'],
      ['rgba(0, 0, 0, 0.01601)', 'rgba(0, 0, 0, 0.016)']
    ];
    for (var i = 0; i < testCases.length; ++i) {
        var rgba = testCases[i][0];
        var expected = testCases[i][1];
        document.body.style.color = rgba;
        assert_equals(document.body.style.color, expected);
        assert_equals(getComputedStyle(document.body).color, expected);
    }
}, 'Alpha values with three or more decimals should parse and serialize to ' +
   '1, 2, or 3 decimal places according to w3c spec');
</script>

