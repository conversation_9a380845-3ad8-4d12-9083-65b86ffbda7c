<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<script src="../resources/js-test.js"></script>
<script src="../resources/gc.js"></script>
</head>
<body>
<script>

description("Test that window.crypto.subtle wrapper preserves custom properties.");
jsTestIsAsync = true;

var anotherWindowCrypto;

function startTest()
{
    anotherWindowCrypto = frames[0].crypto;
    shouldBeDefined(anotherWindowCrypto);
    shouldBeDefined(anotherWindowCrypto.subtle);

    anotherWindowCrypto.subtle.foo = "bar";
    document.body.removeChild(document.getElementsByTagName("iframe")[0]);
    gc();
    setTimeout(continueTest, 10);
}

function continueTest()
{
    gc();
    setTimeout(finishTest, 10);
}

function finishTest()
{
    gc();
    shouldBe('anotherWindowCrypto.subtle.foo', '"bar"');
    finishJSTest();
}

window.onload = startTest;

</script>
<iframe src="about:blank"></iframe>
</body>
</html>
