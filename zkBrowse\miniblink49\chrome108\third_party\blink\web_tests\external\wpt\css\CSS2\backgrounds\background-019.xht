<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background with (attachment image)</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
		<link rel="help" href="http://www.w3.org/TR/css3-background/#the-background-attachment" />
        <meta name="flags" content="image interact" />
        <meta name="assert" content="Background with (attachment image) sets the background to the image specified, tiling it to cover the full area, and the background scrolls with the box." />
        <style type="text/css">
            #div1
            {
                height: 200px;
                overflow: scroll;
                width: 200px;
            }
            div div
            {
                background: scroll url("support/cat.png");
                height: 400px;
                width: 400px;
            }
        </style>
    </head>
    <body>
        <p>Test passes if the box below has a cat image tiled throughout it, and scrolling the box moves the cats as well.</p>
        <div id="div1">
            <div></div>
        </div>
    </body>
</html>