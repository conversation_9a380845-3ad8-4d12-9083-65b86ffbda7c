# Copyright 2014 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/android/resource_sizes.gni")
import("//build/config/android/config.gni")
import("//build/config/android/rules.gni")
import("//build/config/android/system_image.gni")
import("//build/config/python.gni")
import("//build/util/process_version.gni")
import("//chrome/android/chrome_common_shared_library.gni")
import("//chrome/android/chrome_java_resources.gni")
import("//chrome/android/chrome_public_apk_tmpl.gni")
import("//chrome/android/features/dev_ui/dev_ui_module.gni")
import("//chrome/android/features/start_surface/start_surface_java_sources.gni")
import("//chrome/android/features/tab_ui/buildflags.gni")
import("//chrome/android/features/tab_ui/tab_management_java_sources.gni")
import("//chrome/android/features/vr/public_vr_java_sources.gni")
import("//chrome/android/feed/feed_java_sources.gni")
import("//chrome/android/modules/chrome_bundle_tmpl.gni")
import("//chrome/android/modules/chrome_feature_module_tmpl.gni")
import("//chrome/android/monochrome_android_manifest_jinja_variables.gni")
import("//chrome/browser/commerce/price_tracking/android/java_sources.gni")
import("//chrome/browser/commerce/subscriptions/android/java_sources.gni")
import(
    "//chrome/browser/commerce/subscriptions/test/android/test_java_sources.gni")
import("//chrome/browser/password_manager/buildflags.gni")
import("//chrome/browser/share/android/java_sources.gni")
import("//chrome/chrome_paks.gni")
import("//chrome/common/features.gni")
import("//chrome/version.gni")
import("//device/vr/buildflags/buildflags.gni")
import("//printing/buildflags/buildflags.gni")
import("//testing/test.gni")
import("//third_party/icu/config.gni")
import("//third_party/protobuf/proto_library.gni")
import("//tools/resources/generate_resource_allowlist.gni")
import("//tools/v8_context_snapshot/v8_context_snapshot.gni")
import("//weblayer/variables.gni")
import("channel.gni")
import("java_sources.gni")
import("static_initializers.gni")
import("trichrome.gni")

if (android_64bit_target_cpu && skip_secondary_abi_for_cq) {
  assert(current_toolchain != android_secondary_abi_toolchain)
}

chrome_jni_registration_header =
    "$root_build_dir/gen/chrome/browser/android/chrome_jni_registration.h"
chrome_jni_for_test_registration_header = "$root_build_dir/gen/chrome/browser/android/chrome_jni_for_test_registration.h"

if (current_toolchain == default_toolchain) {
  _default_package = "org.chromium.chrome"
  if (use_stable_package_name_for_trichrome) {
    _default_package += ".stable"
  } else if (android_channel != "default") {
    # android-binary-size trybot may checks if an internal Chrome variant's
    # AndroidManifest is as expected by ensuring the differences between its
    # AndroidManifest and its upstream target's .AndroidManifest.expected file
    # are as expected. Upstream targets having a "org.chromium.chrome" package
    # name will cause the comparison to output many unnecessary differences.
    # See https://source.chromium.org/chromium/chromium/src/+/main:chrome/android/java/README.md
    _default_package += "." + android_channel
  }

  declare_args() {
    # Android package name to use when compiling the public chrome targets
    # (chrome_public_apk, monochrome_public_apk, etc. as well as the
    # *_bundle variants). This is particularly useful when using
    # monochrome_public_apk for WebView development, as the OS only accepts
    # WebView providers which declare one of a handful of package names. See
    # https://chromium.googlesource.com/chromium/src/+/HEAD/android_webview/docs/build-instructions.md#Changing-package-name
    # for details.
    chrome_public_manifest_package = _default_package
  }
  chrome_public_test_manifest_package = "org.chromium.chrome.tests"

  chrome_public_jinja_variables =
      default_chrome_public_jinja_variables +
      [ "manifest_package=$chrome_public_manifest_package" ]
  chrome_public_android_manifest =
      "$target_gen_dir/chrome_public_apk/AndroidManifest.xml"
  trichrome_library_android_manifest =
      "$target_gen_dir/trichrome_library_apk/AndroidManifest.xml"
  trichrome_library_64_32_android_manifest =
      "$target_gen_dir/trichrome_library_64_32_apk/AndroidManifest.xml"
  trichrome_library_64_android_manifest =
      "$target_gen_dir/trichrome_library_64_apk/AndroidManifest.xml"
  trichrome_library_32_android_manifest =
      "$target_gen_dir/trichrome_library_32_apk/AndroidManifest.xml"

  app_hooks_impl = "java/src/org/chromium/chrome/browser/AppHooksImpl.java"

  # Exclude it from JNI registration if VR is not enabled.
  chrome_jni_sources_exclusions = []
  if (!enable_vr) {
    chrome_jni_sources_exclusions += [ "//chrome/android/features/vr/java/src/org/chromium/chrome/browser/vr/VrModuleProvider.java" ]
  }

  # Chosen to match what CQ bots exist.
  _enable_libs_and_assets_verification =
      public_android_sdk && !is_component_build &&
      ((target_cpu == "arm" && android_channel == "stable") ||
       (target_cpu == "arm64" && android_channel == "default" &&
        !skip_secondary_abi_for_cq &&
        # Disable checks in official arm64 builds due to unwind assets.
        !is_official_build))

  # Having //clank present causes different flags because of how play services
  # is wired up.
  # The channel is required because manifest entries vary based on channel.
  _enable_manifest_verification =
      !is_java_debug && !enable_chrome_android_internal &&
      android_channel == "stable"

  jinja_template("chrome_public_android_manifest") {
    input = "java/AndroidManifest.xml"
    output = chrome_public_android_manifest
    variables = chrome_public_jinja_variables
    variables += [
      "min_sdk_version=$default_min_sdk_version",
      "target_sdk_version=$android_sdk_version",
    ]
  }

  jinja_template("chrome_public_android_feature_vr_manifest") {
    input = "features/vr/java/AndroidManifest.xml"
    output = feature_module_vr_android_manifest_path
    variables = chrome_public_jinja_variables
    variables += [
      "min_sdk_version=$default_min_sdk_version",
      "target_sdk_version=$android_sdk_version",
    ]
  }

  _use_stable_package_name_for_trichrome =
      use_stable_package_name_for_trichrome && android_channel == "beta"

  jinja_template("trichrome_library_android_manifest") {
    input = "java/AndroidManifest_trichrome_library.xml"
    output = trichrome_library_android_manifest
    if (_use_stable_package_name_for_trichrome) {
      _version_code = trichrome_beta_version_code
    } else {
      _version_code = trichrome_version_code
    }
    variables = trichrome_jinja_variables + [
                  "trichrome_version=$_version_code",
                  "manifest_package=$trichrome_library_package",
                ]
  }

  if (android_64bit_target_cpu) {
    jinja_template("trichrome_library_64_32_android_manifest") {
      input = "java/AndroidManifest_trichrome_library.xml"
      output = trichrome_library_64_32_android_manifest
      if (_use_stable_package_name_for_trichrome) {
        _version_code = trichrome_64_32_beta_version_code
      } else {
        _version_code = trichrome_64_32_version_code
      }
      variables = trichrome_jinja_variables + [
                    "trichrome_version=$_version_code",
                    "manifest_package=$trichrome_library_package",
                    "use32bitAbi=",
                  ]
    }
    jinja_template("trichrome_library_64_android_manifest") {
      input = "java/AndroidManifest_trichrome_library.xml"
      output = trichrome_library_64_android_manifest
      if (_use_stable_package_name_for_trichrome) {
        _version_code = trichrome_64_beta_version_code
      } else {
        _version_code = trichrome_64_version_code
      }
      variables = trichrome_jinja_variables + [
                    "trichrome_version=$_version_code",
                    "manifest_package=$trichrome_library_package",
                    "use32bitAbi=",
                  ]
    }
    jinja_template("trichrome_library_32_android_manifest") {
      input = "java/AndroidManifest_trichrome_library.xml"
      output = trichrome_library_32_android_manifest
      if (_use_stable_package_name_for_trichrome) {
        _version_code = trichrome_32_beta_version_code
      } else {
        _version_code = trichrome_32_version_code
      }
      variables = trichrome_jinja_variables + [
                    "trichrome_version=$_version_code",
                    "manifest_package=$trichrome_library_package",
                  ]
    }
  }

  generate_ui_locale_resources("ui_locale_string_resources") {
    ui_locales = platform_pak_locales
  }

  # Resources which are needed in the base module manifest's application tag
  # should be added here. These are split out from chrome_app_java_resources
  # because //chrome resources may be included in a DFM instead of the base
  # module.
  android_resources("chrome_base_module_resources") {
    sources = [
      "java/res_base/anim/no_anim.xml",
      "java/res_base/drawable-v26/ic_launcher.xml",
      "java/res_base/drawable-v26/ic_launcher_round.xml",
      "java/res_base/font/chrome_google_sans.xml",
      "java/res_base/font/chrome_google_sans_bold.xml",
      "java/res_base/font/chrome_google_sans_medium.xml",
      "java/res_base/values/ic_launcher_alias.xml",
      "java/res_base/values/ic_launcher_round_alias.xml",
      "java/res_base/values/values.xml",
      "java/res_base/xml/network_security_config.xml",
      "java/res_chromium_base/drawable/themed_app_icon.xml",
      "java/res_chromium_base/mipmap-hdpi/app_icon.png",
      "java/res_chromium_base/mipmap-hdpi/layered_app_icon.png",
      "java/res_chromium_base/mipmap-hdpi/layered_app_icon_background.png",
      "java/res_chromium_base/mipmap-mdpi/app_icon.png",
      "java/res_chromium_base/mipmap-mdpi/layered_app_icon.png",
      "java/res_chromium_base/mipmap-mdpi/layered_app_icon_background.png",
      "java/res_chromium_base/mipmap-nodpi/layered_app_icon_foreground.xml",
      "java/res_chromium_base/mipmap-xhdpi/app_icon.png",
      "java/res_chromium_base/mipmap-xhdpi/layered_app_icon.png",
      "java/res_chromium_base/mipmap-xhdpi/layered_app_icon_background.png",
      "java/res_chromium_base/mipmap-xxhdpi/app_icon.png",
      "java/res_chromium_base/mipmap-xxhdpi/layered_app_icon.png",
      "java/res_chromium_base/mipmap-xxhdpi/layered_app_icon_background.png",
      "java/res_chromium_base/mipmap-xxxhdpi/app_icon.png",
      "java/res_chromium_base/mipmap-xxxhdpi/layered_app_icon.png",
      "java/res_chromium_base/mipmap-xxxhdpi/layered_app_icon_background.png",
      "java/res_chromium_base/values/channel_constants.xml",
    ]
  }

  android_resources("chrome_app_java_resources") {
    sources = chrome_java_resources
    sources += [
      "//chrome/android/java/res_app/layout/main.xml",
      "//chrome/android/java/res_chromium/drawable-hdpi/fre_product_logo.png",
      "//chrome/android/java/res_chromium/drawable-hdpi/product_logo_name.png",
      "//chrome/android/java/res_chromium/drawable-mdpi/fre_product_logo.png",
      "//chrome/android/java/res_chromium/drawable-mdpi/product_logo_name.png",
      "//chrome/android/java/res_chromium/drawable-xhdpi/fre_product_logo.png",
      "//chrome/android/java/res_chromium/drawable-xhdpi/product_logo_name.png",
      "//chrome/android/java/res_chromium/drawable-xxhdpi/fre_product_logo.png",
      "//chrome/android/java/res_chromium/drawable-xxhdpi/product_logo_name.png",
      "//chrome/android/java/res_chromium/drawable-xxxhdpi/fre_product_logo.png",
      "//chrome/android/java/res_chromium/drawable-xxxhdpi/product_logo_name.png",
    ]

    deps = [
      ":chrome_base_module_resources",
      ":ui_locale_string_resources",
      "//chrome/android/webapk/libs/common:splash_resources",
      "//chrome/app:java_strings_grd",
      "//chrome/browser/download/android:java_resources",
      "//chrome/browser/feed/android:feed_java_resources",
      "//chrome/browser/feedback/android:java_resources",
      "//chrome/browser/image_descriptions:java_resources",
      "//chrome/browser/lens:java_resources",
      "//chrome/browser/password_check/android:java_resources",
      "//chrome/browser/password_manager/android:java_resources",
      "//chrome/browser/search_resumption:java_resources",
      "//chrome/browser/signin/services/android:java_resources",
      "//chrome/browser/tab:java_resources",
      "//chrome/browser/ui/android/appmenu:java_resources",
      "//chrome/browser/ui/android/favicon:java_resources",
      "//chrome/browser/ui/android/management:java_resources",
      "//chrome/browser/ui/android/omnibox:java_resources",
      "//chrome/browser/ui/android/quickactionsearchwidget:java_resources",
      "//chrome/browser/ui/android/signin:java_resources",
      "//chrome/browser/ui/android/strings:ui_strings_grd",
      "//chrome/browser/ui/android/theme:java_resources",
      "//chrome/browser/ui/android/toolbar:java_resources",
      "//chrome/browser/ui/android/webid/internal:java_resources",
      "//chrome/browser/ui/messages/android:java_resources",
      "//components/autofill/android:autofill_java_resources",
      "//components/autofill/android:autofill_payments_java_resources",
      "//components/blocked_content/android:java_resources",
      "//components/browser_ui/bottomsheet/android:java_resources",
      "//components/browser_ui/contacts_picker/android:java_resources",
      "//components/browser_ui/http_auth/android:java_resources",
      "//components/browser_ui/modaldialog/android:java_resources",
      "//components/browser_ui/settings/android:java_resources",
      "//components/browser_ui/strings/android:browser_ui_strings_grd",
      "//components/browser_ui/styles/android:java_resources",
      "//components/browser_ui/theme/android:java_resources",
      "//components/browser_ui/widget/android:java_resources",
      "//components/find_in_page/android:java_resources",
      "//components/javascript_dialogs/android:java_resources",
      "//components/media_router/browser/android:java_resources",
      "//components/messages/android:java_resources",
      "//components/omnibox/browser:java_resources",
      "//components/page_info/android:java_resources",
      "//components/payments/content/android:java_resources",
      "//components/permissions/android:java_resources",
      "//components/policy:app_restrictions_resources",
      "//components/strings:components_locale_settings_grd",
      "//components/strings:components_strings_grd",
      "//components/subresource_filter/android:java_resources",
      "//components/translate/content/android:java_resources",
      "//components/webapps/browser/android:java_resources",
      "//content/public/android:content_java_resources",
      "//third_party/android_deps:material_design_java",
      "//third_party/androidx:androidx_gridlayout_gridlayout_java",
      "//third_party/androidx:androidx_preference_preference_java",
    ]
  }

  if (enable_vr) {
    # TODO(crbug.com/935982): Currently, adding multiple resource folders to a
    # resource target may clobber some resources. Once that is fixed we should add
    # the VR resources to chrome_app_java_resources.
    android_resources("chrome_vr_java_resources") {
      # Overrides 'gvr_vr_mode_component' in //third_party/gvr-android-sdk:gvr_common_java
      # in tests.
      resource_overlay = true
      sources = [
        "//chrome/android/java/res_vr/values-v17/styles.xml",
        "//chrome/android/java/res_vr/values-vrheadset-v26/styles.xml",
        "//chrome/android/java/res_vr/values/values.xml",
      ]
      deps = [ ":chrome_app_java_resources" ]
    }
  }

  android_library("app_hooks_java") {
    sources = [ app_hooks_impl ]
    deps = [ ":chrome_java" ]
    jacoco_never_instrument = true
  }

  java_group("delegate_public_impl_java") {
    deps = [
      ":app_hooks_java",
      "//chrome/browser/feed/android:hooks_public_impl_java",
      "//chrome/browser/lens:delegate_public_impl_java",
      "//chrome/browser/locale:delegate_public_impl_java",
      "//chrome/browser/partnerbookmarks:delegate_public_impl_java",
      "//chrome/browser/partnercustomizations:delegate_public_impl_java",
      "//chrome/browser/password_manager/android:public_impl_java",
      "//chrome/browser/policy/android:delegate_public_impl_java",
      "//chrome/browser/supervised_user:parent_auth_delegate_impl_java",
      "//chrome/browser/touch_to_fill/android/internal:resource_provider_public_impl_java",
      "//components/autofill_assistant/android:autofill_assistant_public_impl_java",
      "//components/externalauth/android:google_delegate_public_impl_java",
      "//components/language/android:ulp_delegate_public_java",
    ]
  }

  java_cpp_template("vr_build_config") {
    sources = [ "//chrome/android/java/src/org/chromium/chrome/browser/vr/VrBuildConfig.template" ]
    if (enable_vr) {
      defines = [ "ENABLE_VR" ]
    }
  }

  android_library("chrome_java") {
    deps = [
      ":base_module_java",
      ":chrome_app_java_resources",
      ":chrome_public_android_manifest",
      ":chrome_public_apk_template_resources",
      ":update_proto_java",
      ":usage_stats_proto_java",
      "$google_play_services_package:google_play_services_auth_base_java",
      "$google_play_services_package:google_play_services_base_java",
      "$google_play_services_package:google_play_services_basement_java",
      "$google_play_services_package:google_play_services_cast_framework_java",
      "$google_play_services_package:google_play_services_cast_java",
      "$google_play_services_package:google_play_services_gcm_java",
      "$google_play_services_package:google_play_services_iid_java",
      "$google_play_services_package:google_play_services_tasks_java",
      "$google_play_services_package:google_play_services_vision_common_java",
      "$google_play_services_package:google_play_services_vision_java",
      "//base:jni_java",
      "//cc:cc_java",
      "//chrome/android/features/keyboard_accessory:public_java",
      "//chrome/android/features/start_surface:java_resources",
      "//chrome/android/features/start_surface:public_java",
      "//chrome/android/features/tab_ui:tab_suggestions_java",
      "//chrome/android/features/tab_ui/public:java",
      "//chrome/android/modules/cablev2_authenticator/public:java",
      "//chrome/android/modules/image_editor/provider:java",
      "//chrome/android/modules/stack_unwinder/provider:java",
      "//chrome/android/webapk/libs/client:client_java",
      "//chrome/android/webapk/libs/common:common_java",
      "//chrome/android/webapk/libs/common:splash_java",
      "//chrome/android/webapk/libs/runtime_library:webapk_service_aidl_java",
      "//chrome/browser/android/browserservices/constants:java",
      "//chrome/browser/android/browserservices/intents:java",
      "//chrome/browser/android/browserservices/metrics:java",
      "//chrome/browser/android/browserservices/verification:java",
      "//chrome/browser/android/crypto:java",
      "//chrome/browser/android/lifecycle:java",
      "//chrome/browser/android/messages:java",
      "//chrome/browser/android/webapps/launchpad:java",
      "//chrome/browser/back_press/android:java",
      "//chrome/browser/banners/android:java",
      "//chrome/browser/battery/android:java",
      "//chrome/browser/bluetooth/android:java",
      "//chrome/browser/browser_controls/android:java",
      "//chrome/browser/commerce/android:java",
      "//chrome/browser/commerce/merchant_viewer/android:java",
      "//chrome/browser/commerce/price_tracking/android:java",
      "//chrome/browser/commerce/price_tracking/proto:proto_java",
      "//chrome/browser/commerce/subscriptions/android:subscriptions_java",
      "//chrome/browser/consent_auditor/android:java",
      "//chrome/browser/contextmenu:java",
      "//chrome/browser/creator/android:java",
      "//chrome/browser/dependency_injection:java",
      "//chrome/browser/device:java",
      "//chrome/browser/download/android:factory_java",
      "//chrome/browser/download/android:file_provider_java",
      "//chrome/browser/download/android:java",
      "//chrome/browser/download/android:java_resources",
      "//chrome/browser/enterprise/util:java",
      "//chrome/browser/feature_engagement:java",
      "//chrome/browser/feature_guide/notifications:java",
      "//chrome/browser/feed/android:java",
      "//chrome/browser/feedback/android:java",
      "//chrome/browser/first_run/android:java",
      "//chrome/browser/flags:java",
      "//chrome/browser/fullscreen/android:java",
      "//chrome/browser/gsa:java",
      "//chrome/browser/history_clusters:java",
      "//chrome/browser/history_clusters:java_resources",
      "//chrome/browser/image_descriptions:java",
      "//chrome/browser/image_editor/public:java",
      "//chrome/browser/incognito:java",
      "//chrome/browser/language/android:base_module_java",
      "//chrome/browser/language/android:java",
      "//chrome/browser/lens:java",
      "//chrome/browser/locale:java",
      "//chrome/browser/metrics_settings/android:java",
      "//chrome/browser/notifications/chime/android:java",
      "//chrome/browser/offline_pages/android:java",
      "//chrome/browser/omaha/android:java",
      "//chrome/browser/optimization_guide/android:java",
      "//chrome/browser/page_annotations/android:java",
      "//chrome/browser/paint_preview/android:java",
      "//chrome/browser/partnerbookmarks:delegate_java",
      "//chrome/browser/partnercustomizations:delegate_java",
      "//chrome/browser/partnercustomizations:java",
      "//chrome/browser/password_check:public_java",
      "//chrome/browser/password_entry_edit:public_java",
      "//chrome/browser/password_manager/android:java",
      "//chrome/browser/password_manager/android:settings_interface_java",
      "//chrome/browser/policy/android:java",
      "//chrome/browser/preferences:java",
      "//chrome/browser/prefetch/android:java",
      "//chrome/browser/privacy:java",
      "//chrome/browser/privacy_guide/android:java",
      "//chrome/browser/privacy_sandbox/android:java",
      "//chrome/browser/profiles/android:java",
      "//chrome/browser/safe_browsing/android:java",
      "//chrome/browser/safety_check/android:java",
      "//chrome/browser/search_engines/android:java",
      "//chrome/browser/search_resumption:java",
      "//chrome/browser/segmentation_platform:factory_java",
      "//chrome/browser/selection/android:java",
      "//chrome/browser/settings:java",
      "//chrome/browser/share:java",
      "//chrome/browser/share/android:java_resources",
      "//chrome/browser/signin/services/android:java",
      "//chrome/browser/sync/android:java",
      "//chrome/browser/tab:java",
      "//chrome/browser/tab_group:java",
      "//chrome/browser/tabmodel:factory_java",
      "//chrome/browser/tabmodel:java",
      "//chrome/browser/tabpersistence:java",
      "//chrome/browser/thumbnail:java",
      "//chrome/browser/ui/android/appmenu:factory_java",
      "//chrome/browser/ui/android/appmenu:java",
      "//chrome/browser/ui/android/default_browser_promo:java",
      "//chrome/browser/ui/android/fast_checkout:java",
      "//chrome/browser/ui/android/favicon:java",
      "//chrome/browser/ui/android/layouts:java",
      "//chrome/browser/ui/android/layouts/glue:java",
      "//chrome/browser/ui/android/layouts/third_party/float_property:java",
      "//chrome/browser/ui/android/logo:java",
      "//chrome/browser/ui/android/management:java",
      "//chrome/browser/ui/android/multiwindow:java",
      "//chrome/browser/ui/android/native_page:java",
      "//chrome/browser/ui/android/night_mode:java",
      "//chrome/browser/ui/android/omnibox:java",
      "//chrome/browser/ui/android/page_info:java",
      "//chrome/browser/ui/android/quickactionsearchwidget:java",
      "//chrome/browser/ui/android/searchactivityutils:java",
      "//chrome/browser/ui/android/signin:java",
      "//chrome/browser/ui/android/theme:java",
      "//chrome/browser/ui/android/toolbar:java",
      "//chrome/browser/ui/messages/android:java",
      "//chrome/browser/uid/android:java",
      "//chrome/browser/usb/android:java",
      "//chrome/browser/user_education:java",
      "//chrome/browser/util:java",
      "//chrome/browser/version:java",
      "//chrome/browser/video_tutorials:factory_java",
      "//chrome/browser/video_tutorials:java",
      "//chrome/browser/webapps/android:java",
      "//chrome/browser/webauthn/android:java",
      "//chrome/browser/xsurface:java",
      "//components/autofill/android:autofill_java",
      "//components/autofill/android:prefeditor_autofill_java",
      "//components/autofill_assistant/android:public_dependencies_java",
      "//components/autofill_assistant/android:public_java",
      "//components/autofill_assistant/browser:proto_java",
      "//components/background_task_scheduler:background_task_scheduler_java",
      "//components/background_task_scheduler:background_task_scheduler_task_ids_java",
      "//components/bookmarks/common/android:bookmarks_java",
      "//components/browser_ui/accessibility/android:java",
      "//components/browser_ui/banners/android:java",
      "//components/browser_ui/bottomsheet/android:factory_java",
      "//components/browser_ui/bottomsheet/android:java",
      "//components/browser_ui/bottomsheet/android:manager_java",
      "//components/browser_ui/client_certificate/android:java",
      "//components/browser_ui/contacts_picker/android:java",
      "//components/browser_ui/display_cutout/android:java",
      "//components/browser_ui/http_auth/android:java",
      "//components/browser_ui/media/android:java",
      "//components/browser_ui/modaldialog/android:java",
      "//components/browser_ui/notifications/android:java",
      "//components/browser_ui/photo_picker/android:java",
      "//components/browser_ui/settings/android:java",
      "//components/browser_ui/share/android:java",
      "//components/browser_ui/site_settings/android:constants_java",
      "//components/browser_ui/site_settings/android:java",
      "//components/browser_ui/sms/android:java",
      "//components/browser_ui/styles/android:java",
      "//components/browser_ui/util/android:java",
      "//components/browser_ui/webshare/android:java",
      "//components/browser_ui/widget/android:java",
      "//components/commerce/core:proto_java",
      "//components/commerce/core/android:core_java",
      "//components/component_updater/android:background_task_update_scheduler_java",
      "//components/content_capture/android:java",
      "//components/content_settings/android:content_settings_enums_java",
      "//components/content_settings/android:java",
      "//components/crash/android:java",
      "//components/digital_asset_links/android:java",
      "//components/digital_goods/mojom:mojom_java",
      "//components/dom_distiller/content/browser/android:dom_distiller_content_java",
      "//components/dom_distiller/core/android:dom_distiller_core_java",
      "//components/dom_distiller/core/mojom:mojom_java",
      "//components/download/internal/background_service:internal_java",
      "//components/download/internal/common:internal_java",
      "//components/download/network:network_java",
      "//components/download/public/common:public_java",
      "//components/download/public/task:public_java",
      "//components/embedder_support/android:application_java",
      "//components/embedder_support/android:browser_context_java",
      "//components/embedder_support/android:content_view_java",
      "//components/embedder_support/android:context_menu_java",
      "//components/embedder_support/android:util_java",
      "//components/embedder_support/android:web_contents_delegate_java",
      "//components/external_intents/android:java",
      "//components/externalauth/android:java",
      "//components/favicon/android:java",
      "//components/feature_engagement:feature_engagement_java",
      "//components/feed/core/proto:proto_java_v2",
      "//components/feed/core/v2:feedv2_core_java",
      "//components/find_in_page/android:java",
      "//components/gcm_driver/android:gcm_driver_java",
      "//components/gcm_driver/instance_id/android:instance_id_driver_java",
      "//components/image_fetcher:java",
      "//components/infobars/android:infobar_android_enums_java",
      "//components/infobars/android:java",
      "//components/infobars/core:infobar_enums_java",
      "//components/installedapp/android:java",
      "//components/javascript_dialogs/android:java",
      "//components/language/android:java",
      "//components/location/android:location_java",
      "//components/location/android:settings_java",
      "//components/media_router/browser/android:java",
      "//components/messages/android:factory_java",
      "//components/messages/android:java",
      "//components/messages/android:manager_java",
      "//components/minidump_uploader:minidump_uploader_java",
      "//components/module_installer/android:module_installer_java",
      "//components/module_installer/android:module_interface_java",
      "//components/navigation_interception/android:navigation_interception_java",
      "//components/offline_items_collection/core:core_java",
      "//components/omnibox/browser:browser_java",
      "//components/optimization_guide/proto:optimization_guide_proto_java",
      "//components/page_info/android:java",
      "//components/page_info/android:page_info_action_enum_java",
      "//components/page_info/core:proto_java",
      "//components/paint_preview/browser/android:java",
      "//components/paint_preview/player/android:java",
      "//components/password_manager/core/browser:password_manager_java_enums",
      "//components/password_manager/core/common:password_manager_common_java_enums",
      "//components/payments/content/android:java",
      "//components/payments/content/android:service_java",
      "//components/payments/mojom:mojom_java",
      "//components/permissions/android:java",
      "//components/policy/android:policy_java",
      "//components/power_bookmarks/core:proto_java",
      "//components/prefs/android:java",
      "//components/profile_metrics:browser_profile_type_enum_java",
      "//components/query_tiles:java",
      "//components/safe_browsing/android:safe_browsing_java",
      "//components/schema_org/common:mojom_java",
      "//components/search_engines/android:java",
      "//components/security_interstitials/content/android:java",
      "//components/security_state/content/android:java",
      "//components/security_state/core:security_state_enums_java",
      "//components/segmentation_platform/public:public_java",
      "//components/send_tab_to_self:send_tab_to_self_java",
      "//components/signin/core/browser:signin_enums_java",
      "//components/signin/public/android:java",
      "//components/site_engagement/content/android:java",
      "//components/spellcheck/browser/android:java",
      "//components/strictmode/android:java",
      "//components/stylus_handwriting/android:java",
      "//components/subresource_filter/android:java",
      "//components/sync/android:sync_java",
      "//components/sync/protocol:protocol_java",
      "//components/sync_device_info:sync_device_info_java",
      "//components/thin_webview:factory_java",
      "//components/thin_webview:java",
      "//components/translate/content/android:java",
      "//components/translate/content/android:translate_android_enums_java",
      "//components/translate/core/common:translate_infobar_event_enum_java",
      "//components/ukm/android:java",
      "//components/url_formatter/android:url_formatter_java",
      "//components/user_prefs/android:java",
      "//components/variations:variations_java",
      "//components/variations/android:variations_java",
      "//components/version_info/android:version_constants_java",
      "//components/viz/common:common_java",
      "//components/viz/service:service_java",
      "//components/webapk/android/libs/client:java",
      "//components/webapk/android/libs/common:java",
      "//components/webapps/browser/android:java",
      "//components/webapps/common/android:webapk_install_java",
      "//components/webauthn/android:java",
      "//components/webrtc/android:java",
      "//components/webxr/android:ar_java_interfaces",
      "//content/public/android:content_java",
      "//content/public/common:common_java",
      "//device/gamepad:java",
      "//media/base/android:media_java",
      "//media/capture/content/android:screen_capture_java",
      "//media/capture/video/android:capture_java",
      "//media/midi:midi_java",
      "//mojo/public/java:bindings_java",
      "//mojo/public/java:system_java",
      "//mojo/public/java/system:system_impl_java",
      "//mojo/public/mojom/base:base_java",
      "//net/android:net_java",
      "//services/data_decoder/public/cpp/android:safe_json_java",
      "//services/device/public/java:device_feature_list_java",
      "//services/device/public/mojom:mojom_java",
      "//services/media_session/public/cpp/android:media_session_java",
      "//services/media_session/public/mojom:mojom_java",
      "//services/network/public/mojom:mojom_java",
      "//services/network/public/mojom:mojom_proxy_config_java",
      "//services/network/public/mojom:url_loader_base_java",
      "//services/service_manager/public/java:service_manager_java",
      "//services/service_manager/public/mojom:mojom_java",
      "//services/shape_detection:shape_detection_java",
      "//services/shape_detection/public/mojom:mojom_java",
      "//skia/public/mojom:mojom_java",
      "//third_party/android_deps:chromium_play_services_availability_java",
      "//third_party/android_deps:com_google_android_play_core_java",
      "//third_party/android_deps:com_google_code_findbugs_jsr305_java",
      "//third_party/android_deps:com_google_guava_listenablefuture_java",
      "//third_party/android_deps:dagger_java",
      "//third_party/android_deps:guava_android_java",
      "//third_party/android_deps:javax_inject_javax_inject_java",
      "//third_party/android_deps:material_design_java",
      "//third_party/android_deps:protobuf_lite_runtime_java",
      "//third_party/android_media:android_media_java",
      "//third_party/android_swipe_refresh:android_swipe_refresh_java",
      "//third_party/androidx:androidx_activity_activity_java",
      "//third_party/androidx:androidx_browser_browser_java",
      "//third_party/androidx:androidx_collection_collection_java",
      "//third_party/androidx:androidx_coordinatorlayout_coordinatorlayout_java",
      "//third_party/androidx:androidx_customview_customview_java",
      "//third_party/androidx:androidx_gridlayout_gridlayout_java",
      "//third_party/androidx:androidx_lifecycle_lifecycle_common_java8_java",
      "//third_party/androidx:androidx_lifecycle_lifecycle_runtime_java",
      "//third_party/androidx:androidx_localbroadcastmanager_localbroadcastmanager_java",
      "//third_party/androidx:androidx_mediarouter_mediarouter_java",
      "//third_party/androidx:androidx_preference_preference_java",
      "//third_party/androidx:androidx_vectordrawable_vectordrawable_animated_java",
      "//third_party/androidx:androidx_viewpager2_viewpager2_java",
      "//third_party/androidx:androidx_viewpager_viewpager_java",
      "//third_party/blink/public:blink_headers_java",
      "//third_party/blink/public/mojom:android_mojo_bindings_java",
      "//third_party/blink/public/mojom:mojom_platform_java",
      "//third_party/gif_player:gif_player_java",
      "//third_party/metrics_proto:metrics_proto_java",
      "//ui/android:ui_java",
      "//ui/base:features_java",
      "//ui/base/ime/mojom:mojom_java",
      "//ui/base/mojom:mojom_java",
      "//ui/gfx/geometry/mojom:mojom_java",
      "//url:gurl_java",
      "//url:origin_java",
      "//url/mojom:url_mojom_gurl_java",
    ]

    deps += feed_deps

    srcjar_deps = [
      ":autofill_verification_status_generated_enum",
      ":chrome_android_java_enums_srcjar",
      ":chrome_android_java_google_api_keys_srcjar",
      ":chrome_strict_mode_switch",
      ":resource_id_javagen",
      ":vr_build_config",
      "//chrome:instant_apps_reasons_enum_javagen",
      "//chrome:offline_pages_enum_javagen",
      "//chrome:partner_bookmarks_javagen",
      "//chrome:supervised_user_url_filter_enum_javagen",
      "//chrome/browser:screenshot_mode_enum",
      "//chrome/browser:sharing_dialog_type_generated_enum",
      "//chrome/browser:sharing_send_message_result_generated_enum",
      "//chrome/browser:survey_http_client_type_enum",
      "//chrome/browser/notifications/scheduler/public:jni_enums",
      "//chrome/browser/supervised_user/supervised_user_error_page:enums_srcjar",
      "//chrome/browser/ui:duplicate_download_enums_java",
      "//components/browsing_data/core:browsing_data_utils_java",
      "//components/browsing_data/core:clear_browsing_data_tab_java",
      "//components/contextual_search/core/browser:quick_action_category_enum_javagen",
      "//components/dom_distiller/core:distiller_type_java",
      "//components/ntp_tiles:ntp_tiles_enums_java",
      "//components/offline_pages/core:offline_page_model_enums_java",
      "//net:effective_connection_type_java",
    ]

    # From java_sources.gni.
    sources = chrome_java_sources + [ app_hooks_impl ]

    # Include sources from feed_java_sources.gni.
    sources += feed_java_sources
    srcjar_deps += feed_srcjar_deps

    sources += public_vr_java_sources

    # Include sources from public_tab_management_java_sources.gni.
    sources += public_tab_management_java_sources
    sources += start_surface_java_sources

    if (enable_arcore) {
      deps += [
        "//components/webxr/android:ar_java_base",
        "//components/webxr/android:webxr_android_enums_java",
      ]
    }

    if (enable_vr) {
      deps += [ ":chrome_vr_java_resources" ]
    }

    srcjar_deps += [ ":chrome_vr_android_java_enums_srcjar" ]

    # Add the actual implementation where necessary so that downstream targets
    # can provide their own implementations.
    jar_excluded_patterns = [ "*/AppHooksImpl.class" ]

    annotation_processor_deps = [
      "//base/android/jni_generator:jni_processor",
      "//components/module_installer/android:module_interface_processor",
      "//third_party/android_deps:dagger_processor",
    ]

    processor_args_javac = [ "dagger.fastInit=enabled" ]

    if (!is_java_debug) {
      if (!defined(proguard_configs)) {
        proguard_configs = []
      }
      proguard_enabled = true
      proguard_configs +=
          [ "//chrome/android/features/start_surface/proguard.flags" ]
    }

    resources_package = "org.chromium.chrome"

    # TODO(crbug/1022172): Instead of adding source files, add it as a separate
    # dependency when circular deps is resolved.
    sources += share_java_sources
    deps += share_java_deps

    # TODO(crbug/1186003): Instead of adding source files, add it as a separate
    # dependency when circular deps is resolved.
    sources += price_tracking_java_sources
    deps += price_tracking_java_deps

    # TODO(crbug/1210158): Instead of adding source files, add it as a separate
    # dependency when circular deps is resolved.
    sources += commerce_subscriptions_java_sources
    deps += commerce_subscriptions_java_deps

    if (enable_basic_printing) {
      deps += [ "//printing:printing_java" ]
    }

    # TODO(crbug.com/1174713): This is temporary to not break downstream targets.
    public_deps = [ "//chrome/browser/notifications:java" ]

    if (password_manager_use_internal_android_resources) {
      deps += [ "//clank/components/password_manager:java_resources" ]
    }

    if (enable_supervised_users) {
      deps +=
          [ "//chrome/browser/supervised_user:website_parent_approval_java" ]
    }
  }

  # Template for strict mode detection enabling/disabling so that proguard strips out
  # strict mode code when detection is disabled.
  java_cpp_template("chrome_strict_mode_switch") {
    sources = [
      "java/src/org/chromium/chrome/browser/ChromeStrictModeSwitch.template",
    ]

    # Strict mode detection is disabled for canary channel in order to catch bugs
    # related to strict mode detection being turned off early (before beta
    # channel). Strict mode detection is enabled for dev channel because dev
    # channel has more users than canary channel.
    if (is_java_debug || android_channel == "default" ||
        android_channel == "dev") {
      defines = [ "STRICT_MODE_CHECKING" ]
    }
  }

  generate_product_config_srcjar("chrome_product_config") {
    java_package = "org.chromium.chrome.browser"
  }

  # This is a list of all base module java dependencies. New features should be
  # added to this list.
  java_group("chrome_all_java") {
    deps = [
      ":chrome_java",
      "//chrome/android/features/keyboard_accessory:internal_java",
      "//chrome/browser/android/httpclient:java",
      "//chrome/browser/commerce/merchant_viewer/android:java",
      "//chrome/browser/content_creation/notes/internal/android:java",
      "//chrome/browser/content_creation/reactions/internal/android:java",
      "//chrome/browser/download/internal/android:java",
      "//chrome/browser/page_annotations/android:java",
      "//chrome/browser/password_check:internal_java",
      "//chrome/browser/password_edit_dialog/android:java",
      "//chrome/browser/password_entry_edit/android/internal:java",
      "//chrome/browser/tabmodel/internal:java",
      "//chrome/browser/touch_to_fill/android/internal:java",
      "//chrome/browser/touch_to_fill/payments/android/internal:java",
      "//chrome/browser/ui/android/appmenu/internal:java",
      "//chrome/browser/ui/android/autofill/internal:java",
      "//chrome/browser/ui/android/fast_checkout/internal:java",
      "//chrome/browser/ui/android/webid/internal:java",
      "//chrome/browser/video_tutorials/internal:java",
      "//components/browser_ui/bottomsheet/android/internal:java",
      "//components/messages/android/internal:java",
      "//components/segmentation_platform/internal:internal_java",
    ]

    if (disable_autofill_assistant_dfm) {
      deps += [ "//components/autofill_assistant/android:java" ]
    }

    if (disable_tab_ui_dfm) {
      deps += [
        "//chrome/android/features/tab_ui:java",
        "//chrome/android/features/tab_ui:module_desc_java",
      ]
    }
  }

  action_with_pydeps("chrome_android_java_google_api_keys_srcjar") {
    script = "//build/android/gyp/java_google_api_keys.py"
    outputs = [ "$target_gen_dir/$target_name.srcjar" ]
    args = [
      "--srcjar",
      rebase_path(outputs[0], root_build_dir),
    ]
  }

  java_cpp_enum("autofill_verification_status_generated_enum") {
    sources = [ "//components/autofill/core/browser/data_model/autofill_structured_address_component.h" ]
  }

  java_cpp_enum("chrome_android_java_enums_srcjar") {
    sources = [
      "//chrome/browser/android/customtabs/detached_resource_request.h",
      "//chrome/browser/android/explore_sites/explore_sites_bridge.h",
      "//chrome/browser/android/explore_sites/explore_sites_feature.h",
      "//chrome/browser/android/feedback/connectivity_checker.cc",
      "//chrome/browser/android/policy/policy_auditor.cc",
      "//chrome/browser/android/webapk/webapk_installer.h",
      "//chrome/browser/long_screenshots/long_screenshots_tab_service.h",
      "//chrome/browser/notifications/notification_handler.h",
      "//chrome/browser/notifications/notification_platform_bridge_android.cc",
    ]
  }

  java_cpp_enum("chrome_vr_android_java_enums_srcjar") {
    sources = [
      "//chrome/browser/android/vr/vrcore_install_helper.h",
      "//chrome/browser/vr/text_edit_action.h",
      "//chrome/browser/vr/ui_test_input.h",
      "//chrome/browser/vr/ui_unsupported_mode.h",
    ]
  }

  proto_java_library("update_proto_java") {
    proto_path = "java/src/org/chromium/chrome/browser/omaha/metrics"
    sources = [ "$proto_path/update_success_tracking.proto" ]
  }

  proto_java_library("usage_stats_proto_java") {
    proto_path = "../browser/android/usage_stats"
    sources = [ "$proto_path/website_event.proto" ]
  }

  java_cpp_template("resource_id_javagen") {
    sources = [ "java/ResourceId.template" ]
    inputs = [
      "../browser/android/resource_id.h",
      "//components/resources/android/blocked_content_resource_id.h",
      "//components/resources/android/page_info_resource_id.h",
      "//components/resources/android/permissions_resource_id.h",
      "//components/resources/android/sms_resource_id.h",
      "//components/resources/android/webxr_resource_id.h",
      "$root_gen_dir/device/vr/buildflags/buildflags.h",
    ]

    deps = [
      "//chrome/browser/password_manager:password_manager_buildflags",
      "//device/vr/buildflags",
    ]
  }

  robolectric_binary("chrome_junit_tests") {
    # Needed by androidx.test.core.app.ActivityScenario
    android_manifest = "//chrome/android/junit/AndroidManifest.xml"

    data_deps = [ "//testing/buildbot/filters:chrome_junit_tests_filters" ]

    package_name = chrome_public_manifest_package

    # From java_sources.gni.
    sources = chrome_junit_test_java_sources

    # Should not have any deps native targets since junit tests are java-only.
    assert_no_deps = [
      "//content",
      "//mojo/public/mojom/base",
    ]

    deps = [
      ":base_module_java",
      ":chrome_app_java_resources",
      ":chrome_java",
      ":chrome_jni_headers",
      ":chrome_public_android_manifest",
      ":delegate_public_impl_java",
      "$google_play_services_package:google_play_services_base_java",
      "$google_play_services_package:google_play_services_basement_java",
      "$google_play_services_package:google_play_services_cast_framework_java",
      "$google_play_services_package:google_play_services_cast_java",
      "$google_play_services_package:google_play_services_gcm_java",
      "//base:base_java_test_support",
      "//base:base_junit_test_support",
      "//base/test:test_support_java",
      "//build/config/android/test/classpath_order:junit_tests",
      "//cc:cc_java",
      "//chrome/android:update_proto_java",
      "//chrome/android:usage_stats_proto_java",
      "//chrome/android/features/android_library_factory:junit_tests",
      "//chrome/android/features/keyboard_accessory:internal_java",
      "//chrome/android/features/start_surface:java_resources",
      "//chrome/android/features/start_surface:public_java",
      "//chrome/android/features/tab_ui:java",
      "//chrome/android/features/tab_ui:tab_suggestions_java",
      "//chrome/android/features/tab_ui/public:java",
      "//chrome/android/modules/image_editor/provider:java",
      "//chrome/android/webapk/libs/client:client_java",
      "//chrome/android/webapk/libs/common:common_java",
      "//chrome/android/webapk/libs/common:splash_java",
      "//chrome/android/webapk/test:junit_test_support",
      "//chrome/browser/android/browserservices/constants:java",
      "//chrome/browser/android/browserservices/intents:java",
      "//chrome/browser/android/browserservices/intents:junit",
      "//chrome/browser/android/browserservices/metrics:java",
      "//chrome/browser/android/browserservices/verification:java",
      "//chrome/browser/android/browserservices/verification:junit_test_support",
      "//chrome/browser/android/crypto:java",
      "//chrome/browser/android/httpclient:junit_tests",
      "//chrome/browser/android/lifecycle:java",
      "//chrome/browser/android/webapps/launchpad:junit_tests",
      "//chrome/browser/back_press/android:java",
      "//chrome/browser/back_press/android:junit",
      "//chrome/browser/banners/android:java",
      "//chrome/browser/bluetooth/android:junit",
      "//chrome/browser/browser_controls/android:java",
      "//chrome/browser/browser_controls/android:junit",
      "//chrome/browser/commerce/android:java",
      "//chrome/browser/commerce/merchant_viewer/android:junit",
      "//chrome/browser/commerce/price_tracking/android:java",
      "//chrome/browser/commerce/price_tracking/proto:proto_java",
      "//chrome/browser/commerce/subscriptions/android:subscriptions_java",
      "//chrome/browser/contextmenu:java",
      "//chrome/browser/creator/android:java",
      "//chrome/browser/creator/android:junit",
      "//chrome/browser/dependency_injection:java",
      "//chrome/browser/device:java",
      "//chrome/browser/device:junit",
      "//chrome/browser/device_reauth/android:java",
      "//chrome/browser/download/android:java",
      "//chrome/browser/download/android:junit_tests",
      "//chrome/browser/download/internal/android:junit",
      "//chrome/browser/endpoint_fetcher:java",
      "//chrome/browser/enterprise/util:java",
      "//chrome/browser/enterprise/util:junit",
      "//chrome/browser/feature_engagement:java",
      "//chrome/browser/feed/android:java",
      "//chrome/browser/feed/android:junit",
      "//chrome/browser/feedback/android:java",
      "//chrome/browser/first_run/android:java",
      "//chrome/browser/flags:flags_junit_tests",
      "//chrome/browser/flags:java",
      "//chrome/browser/fullscreen/android:java",
      "//chrome/browser/gsa:java",
      "//chrome/browser/history_clusters:java",
      "//chrome/browser/image_descriptions:java",
      "//chrome/browser/image_editor/public:java",
      "//chrome/browser/incognito:incognito_junit_tests",
      "//chrome/browser/incognito:java",
      "//chrome/browser/language/android:junit",
      "//chrome/browser/lens:delegate_public_impl_java",
      "//chrome/browser/lens:java",
      "//chrome/browser/loading_modal/android:junit",
      "//chrome/browser/locale:java",
      "//chrome/browser/notifications:java",
      "//chrome/browser/notifications:junit_tests",
      "//chrome/browser/omaha/android:java",
      "//chrome/browser/optimization_guide/android:java",
      "//chrome/browser/page_annotations/test/android:junit",
      "//chrome/browser/partnercustomizations:java",
      "//chrome/browser/password_edit_dialog/android:junit",
      "//chrome/browser/password_entry_edit/android/internal:junit",
      "//chrome/browser/password_manager/android:java",
      "//chrome/browser/payments/android:junit",
      "//chrome/browser/policy/android:java",
      "//chrome/browser/policy/android:junit",
      "//chrome/browser/preferences:java",
      "//chrome/browser/preferences:preferences_junit_tests",
      "//chrome/browser/privacy_guide/android:java",
      "//chrome/browser/privacy_guide/android:junit",
      "//chrome/browser/profiles/android:java",
      "//chrome/browser/safety_check/android:java",
      "//chrome/browser/safety_check/android:junit",
      "//chrome/browser/search_engines/android:java",
      "//chrome/browser/search_resumption:junit",
      "//chrome/browser/segmentation_platform:factory_java",
      "//chrome/browser/share:java",
      "//chrome/browser/signin/services/android:java",
      "//chrome/browser/signin/services/android:junit",
      "//chrome/browser/sync/android:java",
      "//chrome/browser/tab:java",
      "//chrome/browser/tab:junit",
      "//chrome/browser/tab_group:java",
      "//chrome/browser/tab_group:junit",
      "//chrome/browser/tabmodel:factory_java",
      "//chrome/browser/tabmodel:java",
      "//chrome/browser/tabmodel:junit",
      "//chrome/browser/tabmodel/internal:java",
      "//chrome/browser/tabpersistence:junit",
      "//chrome/browser/thumbnail:java",
      "//chrome/browser/ui/android/appmenu:java",
      "//chrome/browser/ui/android/appmenu/internal:junit",
      "//chrome/browser/ui/android/autofill/internal:junit",
      "//chrome/browser/ui/android/default_browser_promo:java",
      "//chrome/browser/ui/android/default_browser_promo:junit",
      "//chrome/browser/ui/android/fast_checkout/internal:junit",
      "//chrome/browser/ui/android/favicon:java",
      "//chrome/browser/ui/android/layouts:java",
      "//chrome/browser/ui/android/layouts:junit",
      "//chrome/browser/ui/android/logo:java",
      "//chrome/browser/ui/android/logo:junit",
      "//chrome/browser/ui/android/multiwindow:java",
      "//chrome/browser/ui/android/multiwindow:junit",
      "//chrome/browser/ui/android/native_page:java",
      "//chrome/browser/ui/android/native_page:junit",
      "//chrome/browser/ui/android/night_mode:java",
      "//chrome/browser/ui/android/night_mode:junit",
      "//chrome/browser/ui/android/omnibox:java",
      "//chrome/browser/ui/android/omnibox:junit",
      "//chrome/browser/ui/android/quickactionsearchwidget:java",
      "//chrome/browser/ui/android/searchactivityutils:java",
      "//chrome/browser/ui/android/signin:java",
      "//chrome/browser/ui/android/signin:junit",
      "//chrome/browser/ui/android/theme:java",
      "//chrome/browser/ui/android/toolbar:java",
      "//chrome/browser/ui/android/toolbar:junit",
      "//chrome/browser/ui/android/webid/internal:junit",
      "//chrome/browser/ui/messages/android:java",
      "//chrome/browser/ui/messages/android:junit",
      "//chrome/browser/uid/android:java",
      "//chrome/browser/uid/android:junit",
      "//chrome/browser/usb/android:junit",
      "//chrome/browser/user_education:java",
      "//chrome/browser/util:java",
      "//chrome/browser/util:junit_tests",
      "//chrome/browser/version:java",
      "//chrome/browser/video_tutorials:factory_java",
      "//chrome/browser/video_tutorials:java",
      "//chrome/browser/video_tutorials:test_support_java",
      "//chrome/browser/video_tutorials/internal:junit",
      "//chrome/browser/webapps/android:java",
      "//chrome/browser/xsurface:java",
      "//chrome/test:sync_integration_test_support_java",
      "//chrome/test/android:chrome_java_unit_test_support",
      "//components/autofill/android:main_autofill_java",
      "//components/background_task_scheduler:background_task_scheduler_java",
      "//components/background_task_scheduler:background_task_scheduler_task_ids_java",
      "//components/bookmarks/common/android:bookmarks_java",
      "//components/browser_ui/accessibility/android:java",
      "//components/browser_ui/accessibility/android:junit",
      "//components/browser_ui/bottomsheet/android:java",
      "//components/browser_ui/display_cutout/android:java",
      "//components/browser_ui/media/android:java",
      "//components/browser_ui/media/android:java_resources",
      "//components/browser_ui/notifications/android:java",
      "//components/browser_ui/settings/android:java",
      "//components/browser_ui/share/android:java",
      "//components/browser_ui/site_settings/android:constants_java",
      "//components/browser_ui/site_settings/android:java",
      "//components/browser_ui/site_settings/android:junit",
      "//components/browser_ui/styles/android:java",
      "//components/browser_ui/test/android:test_support_java",
      "//components/browser_ui/util/android:java",
      "//components/browser_ui/widget/android:java",
      "//components/browser_ui/widget/android:test_support_java",
      "//components/commerce/core:proto_java",
      "//components/commerce/core/android:core_java",
      "//components/content_capture/android:java",
      "//components/content_settings/android:content_settings_enums_java",
      "//components/digital_asset_links/android:java",
      "//components/digital_asset_links/android:junit_test_support",
      "//components/digital_goods/mojom:mojom_java",
      "//components/dom_distiller/core/android:dom_distiller_core_java",
      "//components/embedder_support/android:content_view_java",
      "//components/embedder_support/android:context_menu_java",
      "//components/embedder_support/android:junit_test_support",
      "//components/embedder_support/android:util_java",
      "//components/externalauth/android:java",
      "//components/externalauth/android:junit",
      "//components/favicon/android:java",
      "//components/feature_engagement/public:public_java",
      "//components/feed/core/proto:proto_java_v2",
      "//components/feed/core/v2:feedv2_core_java",
      "//components/image_fetcher:java",
      "//components/infobars/android:java",
      "//components/messages/android:java",
      "//components/messages/android:manager_java",
      "//components/minidump_uploader:minidump_uploader_java",
      "//components/minidump_uploader:minidump_uploader_java_test_support",
      "//components/minidump_uploader:minidump_uploader_javatests",
      "//components/module_installer/android:module_installer_java",
      "//components/offline_items_collection/core:core_java",
      "//components/omnibox/browser:browser_java",
      "//components/omnibox/browser:junit",
      "//components/optimization_guide/proto:optimization_guide_proto_java",
      "//components/page_info/android:java",
      "//components/page_info/core:proto_java",
      "//components/paint_preview/player/android:java",
      "//components/payments/content/android:java",
      "//components/payments/content/android:service_java",
      "//components/payments/mojom:mojom_java",
      "//components/policy/android:policy_java",
      "//components/power_bookmarks/core:proto_java",
      "//components/prefs/android:java",
      "//components/profile_metrics:browser_profile_type_enum_java",
      "//components/schema_org/common:mojom_java",
      "//components/search_engines/android:java",
      "//components/security_state/content/android:java",
      "//components/security_state/core:security_state_enums_java",
      "//components/segmentation_platform/public:public_java",
      "//components/signin/core/browser:signin_enums_java",
      "//components/signin/public/android:java",
      "//components/signin/public/android:signin_java_test_support",
      "//components/sync/android:sync_java",
      "//components/sync_device_info:sync_device_info_java",
      "//components/translate/content/android:junit",
      "//components/ukm/android:java",
      "//components/url_formatter/android:url_formatter_java",
      "//components/user_prefs/android:java",
      "//components/variations/android:variations_java",
      "//components/version_info/android:version_constants_java",
      "//components/webapk/android/libs/client:java",
      "//components/webapk/android/libs/common:java",
      "//components/webapps/browser/android:java",
      "//content/public/android:content_java",
      "//content/public/common:common_java",
      "//content/public/test/android:content_java_test_support",
      "//mojo/public/java:bindings_java",
      "//mojo/public/java:system_java",
      "//mojo/public/mojom/base:base_java",
      "//net/android:net_java",
      "//services/device/public/mojom:mojom_java",
      "//services/media_session/public/cpp/android:media_session_java",
      "//services/media_session/public/mojom:mojom_java",
      "//services/service_manager/public/java:service_manager_java",
      "//third_party/android_deps:chromium_play_services_availability_shadows_java",
      "//third_party/android_deps:com_google_guava_listenablefuture_java",
      "//third_party/android_deps:com_googlecode_java_diff_utils_diffutils_java",
      "//third_party/android_deps:dagger_java",
      "//third_party/android_deps:espresso_java",
      "//third_party/android_deps:guava_android_java",
      "//third_party/android_deps:material_design_java",
      "//third_party/android_deps:protobuf_lite_runtime_java",
      "//third_party/android_support_test_runner:rules_java",
      "//third_party/android_support_test_runner:runner_java",
      "//third_party/androidx:androidx_activity_activity_java",
      "//third_party/androidx:androidx_appcompat_appcompat_java",
      "//third_party/androidx:androidx_browser_browser_java",
      "//third_party/androidx:androidx_collection_collection_java",
      "//third_party/androidx:androidx_lifecycle_lifecycle_runtime_java",
      "//third_party/androidx:androidx_mediarouter_mediarouter_java",
      "//third_party/androidx:androidx_swiperefreshlayout_swiperefreshlayout_java",
      "//third_party/androidx:androidx_test_core_java",
      "//third_party/androidx:androidx_test_ext_junit_java",
      "//third_party/androidx:androidx_test_runner_java",
      "//third_party/blink/public:blink_headers_java",
      "//third_party/blink/public/mojom:android_mojo_bindings_java",
      "//third_party/blink/public/mojom:mojom_core_java",
      "//third_party/blink/public/mojom:mojom_platform_java",
      "//third_party/gif_player:gif_player_java",
      "//third_party/google-truth:google_truth_java",
      "//third_party/hamcrest:hamcrest_java",
      "//ui/android:ui_java",
      "//ui/android:ui_java_test_support",
      "//ui/android:ui_junit_test_support",
      "//ui/base/ime/mojom:mojom_java",
      "//ui/base/mojom:mojom_java",
      "//url:gurl_java",
      "//url:gurl_junit_shadows",
      "//url:gurl_junit_test_support",
      "//url:gurl_junit_tests",
      "//url:origin_java",
      "//url/mojom:url_mojom_gurl_java",
    ]

    deps += chrome_junit_test_java_deps
  }

  generate_jni("chrome_test_util_jni_headers") {
    testonly = true
    sources = [
      "javatests/src/org/chromium/chrome/browser/FederatedIdentityTestUtils.java",
      "javatests/src/org/chromium/chrome/browser/customtabs/CustomTabsTestUtils.java",
    ]
  }

  # Files used by chrome integration and unit javatests.
  android_library("chrome_unit_test_util_java") {
    testonly = true

    sources = [
      "javatests/src/org/chromium/chrome/browser/compositor/bottombar/OverlayPanelManagerWrapper.java",
      "javatests/src/org/chromium/chrome/browser/customtabs/CustomTabsIntentTestUtils.java",
      "javatests/src/org/chromium/chrome/browser/download/MockDownloadNotificationService.java",
      "javatests/src/org/chromium/chrome/browser/download/TestDownloadDirectoryProvider.java",
      "javatests/src/org/chromium/chrome/browser/tabmodel/TestTabModelDirectory.java",
    ]

    deps = [
      "//base:base_java",
      "//base:base_java_test_support",
      "//chrome/android:chrome_java",
      "//chrome/browser/download/android:file_provider_java",
      "//chrome/browser/preferences:java",
      "//chrome/browser/profiles/android:java",
      "//components/offline_items_collection/core:core_java",
      "//content/public/android:content_main_dex_java",
      "//content/public/test/android:content_java_test_support",
      "//third_party/android_support_test_runner:runner_java",
      "//third_party/androidx:androidx_annotation_annotation_java",
      "//third_party/androidx:androidx_browser_browser_java",
      "//third_party/junit:junit",
      "//url:gurl_java",
    ]
  }

  # Files used for both chrome tests and VR/AR and autofill_assistant tests
  android_library("chrome_test_util_java") {
    testonly = true

    sources = [
      "javatests/src/org/chromium/chrome/browser/FederatedIdentityTestUtils.java",
      "javatests/src/org/chromium/chrome/browser/customtabs/CustomTabActivityTestRule.java",
      "javatests/src/org/chromium/chrome/browser/customtabs/CustomTabsTestUtils.java",
      "javatests/src/org/chromium/chrome/browser/directactions/FakeDirectActionReporter.java",
      "javatests/src/org/chromium/chrome/browser/modaldialog/ChromeModalDialogTestUtils.java",
      "javatests/src/org/chromium/chrome/browser/payments/PaymentRequestTestRule.java",
      "javatests/src/org/chromium/chrome/browser/webapps/TestFetchStorageCallback.java",
      "javatests/src/org/chromium/chrome/browser/webapps/WebappActivityTestRule.java",
    ]

    public_deps = [ ":chrome_unit_test_util_java" ]

    deps = [
      ":browser_java_test_support",
      "//base:base_java",
      "//base:base_java_test_support",
      "//base:jni_java",
      "//build/android:build_java",
      "//cc:cc_java",
      "//chrome/android:chrome_java",
      "//chrome/browser/android/browserservices/intents:java",
      "//chrome/browser/flags:java",
      "//chrome/browser/profiles/android:java",
      "//chrome/browser/tab:java",
      "//chrome/browser/ui/android/appmenu:java",
      "//chrome/test/android:chrome_java_integration_test_support",
      "//components/autofill/android:prefeditor_autofill_java",
      "//components/payments/content/android:java",
      "//components/payments/content/android:service_java",
      "//components/payments/mojom:mojom_java",
      "//content/public/android:content_java",
      "//content/public/test/android:content_java_test_support",
      "//net/android:net_java_test_support",
      "//third_party/android_deps:espresso_java",
      "//third_party/android_media:android_media_resources",
      "//third_party/android_support_test_runner:runner_java",
      "//third_party/androidx:androidx_annotation_annotation_java",
      "//third_party/androidx:androidx_browser_browser_java",
      "//third_party/blink/public/mojom:android_mojo_bindings_java",
      "//third_party/hamcrest:hamcrest_java",
      "//third_party/junit:junit",
      "//ui/android:ui_no_recycler_view_java",
      "//url:gurl_java",
    ]

    annotation_processor_deps = [ "//base/android/jni_generator:jni_processor" ]
  }

  android_library("chrome_unit_test_java") {
    testonly = true
    resources_package = "org.chromium.chrome.test"
    sources = [
      "javatests/src/org/chromium/chrome/browser/IntentFilterUnitTest.java",
      "javatests/src/org/chromium/chrome/browser/IntentHandlerUnitTest.java",
      "javatests/src/org/chromium/chrome/browser/autofill/AutofillUnitTest.java",
      "javatests/src/org/chromium/chrome/browser/bookmarks/BookmarkActionBarTest.java",
      "javatests/src/org/chromium/chrome/browser/bookmarks/BookmarkItemRowTest.java",
      "javatests/src/org/chromium/chrome/browser/bookmarks/PowerBookmarkShoppingItemRowTest.java",
      "javatests/src/org/chromium/chrome/browser/bookmarks/PowerBookmarkUtilsTest.java",
      "javatests/src/org/chromium/chrome/browser/compositor/bottombar/OverlayPanelBaseTest.java",
      "javatests/src/org/chromium/chrome/browser/compositor/bottombar/OverlayPanelEventFilterTest.java",
      "javatests/src/org/chromium/chrome/browser/compositor/bottombar/OverlayPanelManagerTest.java",
      "javatests/src/org/chromium/chrome/browser/contextmenu/ChromeContextMenuPopulatorTest.java",
      "javatests/src/org/chromium/chrome/browser/contextmenu/ContextMenuChipControllerTest.java",
      "javatests/src/org/chromium/chrome/browser/contextmenu/ContextMenuHeaderViewTest.java",
      "javatests/src/org/chromium/chrome/browser/contextmenu/ContextMenuItemViewTest.java",
      "javatests/src/org/chromium/chrome/browser/contextmenu/ContextMenuRenderTest.java",
      "javatests/src/org/chromium/chrome/browser/contextmenu/ContextMenuUtilsTest.java",
      "javatests/src/org/chromium/chrome/browser/crash/LogcatExtractionRunnableTest.java",
      "javatests/src/org/chromium/chrome/browser/crash/MinidumpUploadServiceTest.java",
      "javatests/src/org/chromium/chrome/browser/crypto/CipherFactoryTest.java",
      "javatests/src/org/chromium/chrome/browser/customtabs/CustomTabLaunchCauseMetricsTest.java",
      "javatests/src/org/chromium/chrome/browser/download/DownloadForegroundServiceManagerTest.java",
      "javatests/src/org/chromium/chrome/browser/download/DownloadForegroundServiceTest.java",
      "javatests/src/org/chromium/chrome/browser/download/DownloadNotificationServiceTest.java",
      "javatests/src/org/chromium/chrome/browser/download/SystemDownloadNotifierTest.java",
      "javatests/src/org/chromium/chrome/browser/download/dialogs/DownloadLocationDialogTest.java",
      "javatests/src/org/chromium/chrome/browser/externalnav/ExternalNavigationDelegateImplTest.java",
      "javatests/src/org/chromium/chrome/browser/externalnav/IntentWithRequestMetadataHandlerTest.java",
      "javatests/src/org/chromium/chrome/browser/feature_engagement/ScreenshotMonitorTest.java",
      "javatests/src/org/chromium/chrome/browser/firstrun/FirstRunUtilsTest.java",
      "javatests/src/org/chromium/chrome/browser/init/ChainedTasksTest.java",
      "javatests/src/org/chromium/chrome/browser/notifications/NotificationPlatformBridgeUnitTest.java",
      "javatests/src/org/chromium/chrome/browser/ntp/IncognitoDescriptionViewRenderTest.java",
      "javatests/src/org/chromium/chrome/browser/ntp/TitleUtilTest.java",
      "javatests/src/org/chromium/chrome/browser/status_indicator/StatusIndicatorViewBinderTest.java",
      "javatests/src/org/chromium/chrome/browser/tab/WebContentsStateBridgeTest.java",
      "javatests/src/org/chromium/chrome/browser/tab/state/FilePersistedTabDataStorageTest.java",
      "javatests/src/org/chromium/chrome/browser/tab/state/PersistedTabDataTest.java",
      "javatests/src/org/chromium/chrome/browser/tab/state/PriceDropMetricsLoggerTest.java",
      "javatests/src/org/chromium/chrome/browser/tab/state/StorePersistedTabDataTest.java",
      "javatests/src/org/chromium/chrome/browser/tabmodel/AsyncTabCreationParamsManagerTest.java",
      "javatests/src/org/chromium/chrome/browser/tabmodel/RestoreMigrateTest.java",
      "javatests/src/org/chromium/chrome/browser/tabmodel/TabPersistentStoreUnitTest.java",
      "javatests/src/org/chromium/chrome/browser/toolbar/HomeButtonTest.java",
      "javatests/src/org/chromium/chrome/browser/toolbar/ToolbarSecurityIconTest.java",
      "javatests/src/org/chromium/chrome/browser/webapps/WebappLaunchCauseMetricsTest.java",
    ]
    deps = [
      ":chrome_unit_test_util_java",
      "//base:base_java",
      "//base:base_java_test_support",
      "//chrome/android:base_module_java",
      "//chrome/android:chrome_java",
      "//chrome/android/features/autofill_assistant:unit_test_java",
      "//chrome/browser/android/browserservices/intents:java",
      "//chrome/browser/android/browserservices/verification:java",
      "//chrome/browser/android/crypto:java",
      "//chrome/browser/commerce/subscriptions/android:subscriptions_java",
      "//chrome/browser/contextmenu:java",
      "//chrome/browser/download/android:download_java_tests",
      "//chrome/browser/download/android:file_provider_java",
      "//chrome/browser/download/android:java",
      "//chrome/browser/endpoint_fetcher:java",
      "//chrome/browser/feature_engagement:java",
      "//chrome/browser/first_run/android:java",
      "//chrome/browser/flags:java",
      "//chrome/browser/incognito:java",
      "//chrome/browser/lens:java",
      "//chrome/browser/preferences:java",
      "//chrome/browser/profiles/android:java",
      "//chrome/browser/tab:java",
      "//chrome/browser/tabmodel:java",
      "//chrome/browser/tabpersistence:java",
      "//chrome/browser/ui/android/omnibox:java",
      "//chrome/browser/ui/android/theme:java",
      "//chrome/browser/ui/android/toolbar:java",
      "//chrome/browser/ui/messages/android:java",
      "//chrome/test/android:chrome_java_integration_test_support",
      "//chrome/test/android:chrome_java_unit_test_support",
      "//components/autofill/android:main_autofill_java",
      "//components/autofill_assistant/android:public_java",
      "//components/background_task_scheduler:background_task_scheduler_task_ids_java",
      "//components/bookmarks/common/android:bookmarks_java",
      "//components/browser_ui/modaldialog/android:java",
      "//components/browser_ui/notifications/android:java",
      "//components/browser_ui/settings/android:java",
      "//components/browser_ui/styles/android:java",
      "//components/browser_ui/util/android:java",
      "//components/browser_ui/widget/android:java",
      "//components/crash/android:java",
      "//components/embedder_support/android:context_menu_java",
      "//components/embedder_support/android:util_java",
      "//components/external_intents/android:java",
      "//components/externalauth/android:java",
      "//components/favicon/android:java",
      "//components/image_fetcher:java",
      "//components/minidump_uploader:minidump_uploader_java",
      "//components/minidump_uploader:minidump_uploader_java_test_support",
      "//components/offline_items_collection/core:core_java",
      "//components/payments/content/android:full_java",
      "//components/power_bookmarks/core:proto_java",
      "//components/prefs/android:java",
      "//components/search_engines/android:java",
      "//components/security_state/content/android:java",
      "//components/security_state/core:security_state_enums_java",
      "//components/signin/public/android:java",
      "//components/signin/public/android:signin_java_test_support",
      "//components/user_prefs/android:java",
      "//components/webapps/browser/android:java",
      "//content/public/android:content_full_java",
      "//content/public/android:content_java",
      "//content/public/android:content_main_dex_java",
      "//content/public/common:common_java",
      "//content/public/test/android:content_java_test_support",
      "//net/android:net_java",
      "//third_party/android_deps:espresso_java",
      "//third_party/android_deps:guava_android_java",
      "//third_party/android_sdk:android_test_base_java",
      "//third_party/android_support_test_runner:rules_java",
      "//third_party/android_support_test_runner:runner_java",
      "//third_party/androidx:androidx_annotation_annotation_java",
      "//third_party/androidx:androidx_browser_browser_java",
      "//third_party/androidx:androidx_core_core_java",
      "//third_party/androidx:androidx_test_runner_java",
      "//third_party/blink/public:blink_headers_java",
      "//third_party/hamcrest:hamcrest_core_java",
      "//third_party/hamcrest:hamcrest_library_java",
      "//third_party/junit",
      "//third_party/mockito:mockito_java",
      "//ui/android:ui_full_java",
      "//ui/android:ui_java_test_support",
      "//url:gurl_java",
      "//url:gurl_java_unit_tests",
      "//url:gurl_junit_test_support",
      "//url:origin_java",
    ]

    sources += share_unit_device_javatest_java_sources
    deps += share_unit_device_javatest_java_deps

    data = [ "//chrome/test/data/android/" ]
  }

  android_library("chrome_test_java") {
    testonly = true
    resources_package = "org.chromium.chrome.test"

    # From java_sources.gni.
    sources = chrome_test_java_sources

    deps = [
      ":base_module_java",
      ":browser_java_test_support",
      ":chrome_app_java_resources",
      ":chrome_test_util_java",
      ":chrome_test_util_jni",
      ":delegate_public_impl_java",
      "$google_play_services_package:google_play_services_base_java",
      "$google_play_services_package:google_play_services_basement_java",
      "$google_play_services_package:google_play_services_cast_framework_java",
      "$google_play_services_package:google_play_services_cast_java",
      "$google_play_services_package:google_play_services_gcm_java",
      "$google_play_services_package:google_play_services_iid_java",
      "$google_play_services_package:google_play_services_tasks_java",
      "//base:base_java_test_support",
      "//base/test:test_support_java",
      "//cc:cc_java",
      "//chrome/android:chrome_java",
      "//chrome/android/features/start_surface:java_resources",
      "//chrome/android/features/start_surface:public_java",
      "//chrome/android/features/tab_ui:java",
      "//chrome/android/features/tab_ui:java_resources",
      "//chrome/android/features/tab_ui:tab_suggestions_java",
      "//chrome/android/features/tab_ui:test_support_javalib",
      "//chrome/android/webapk/libs/client:client_java",
      "//chrome/android/webapk/libs/common:common_java",
      "//chrome/android/webapk/libs/runtime_library:webapk_service_aidl_java",
      "//chrome/browser/android/browserservices/constants:java",
      "//chrome/browser/android/browserservices/intents:java",
      "//chrome/browser/android/browserservices/verification:java",
      "//chrome/browser/android/browserservices/verification:javatests",
      "//chrome/browser/android/crypto:java",
      "//chrome/browser/android/httpclient:javatests",
      "//chrome/browser/android/lifecycle:java",
      "//chrome/browser/android/metrics:ukm_java_test_support",
      "//chrome/browser/android/metrics:ukm_javatests",
      "//chrome/browser/android/webapps/launchpad:java",
      "//chrome/browser/back_press/android:java",
      "//chrome/browser/banners/android:java",
      "//chrome/browser/browser_controls/android:java",
      "//chrome/browser/commerce/android:java",
      "//chrome/browser/commerce/android:javatests",
      "//chrome/browser/commerce/merchant_viewer/android:java",
      "//chrome/browser/commerce/merchant_viewer/android:javatests",
      "//chrome/browser/commerce/price_tracking/android:java",
      "//chrome/browser/commerce/subscriptions/android:subscriptions_java",
      "//chrome/browser/content_creation/notes/internal/android:java",
      "//chrome/browser/contextmenu:java",
      "//chrome/browser/dependency_injection:java",
      "//chrome/browser/device:java",
      "//chrome/browser/download/android:file_provider_java",
      "//chrome/browser/download/android:java",
      "//chrome/browser/endpoint_fetcher:java",
      "//chrome/browser/enterprise/util:java",
      "//chrome/browser/enterprise/util:test_support_java",
      "//chrome/browser/feature_engagement:java",
      "//chrome/browser/feed/android:java",
      "//chrome/browser/feed/android:javatests",
      "//chrome/browser/feedback/android:java",
      "//chrome/browser/first_run/android:java",
      "//chrome/browser/flags:java",
      "//chrome/browser/fullscreen/android:java",
      "//chrome/browser/gsa:java",
      "//chrome/browser/history_clusters:java",
      "//chrome/browser/image_descriptions:javatests",
      "//chrome/browser/incognito:incognito_java_tests",
      "//chrome/browser/incognito:java",
      "//chrome/browser/language/android:base_module_java",
      "//chrome/browser/language/android:java",
      "//chrome/browser/language/android:javatests",
      "//chrome/browser/lens:java",
      "//chrome/browser/locale:java",
      "//chrome/browser/notifications:java",
      "//chrome/browser/notifications:javatests",
      "//chrome/browser/offline_pages/android:java",
      "//chrome/browser/omaha/android:java",
      "//chrome/browser/optimization_guide/android:java",
      "//chrome/browser/optimization_guide/android:javatests",
      "//chrome/browser/page_annotations/android:java",
      "//chrome/browser/page_annotations/test/android:javatests",
      "//chrome/browser/paint_preview/android:java",
      "//chrome/browser/paint_preview/android:javatests",
      "//chrome/browser/partnercustomizations:delegate_java",
      "//chrome/browser/partnercustomizations:java",
      "//chrome/browser/partnercustomizations:test_support_java",
      "//chrome/browser/password_check:public_java",
      "//chrome/browser/password_entry_edit/android/internal:javatests",
      "//chrome/browser/password_manager/android:java",
      "//chrome/browser/password_manager/android:test_support_java",
      "//chrome/browser/policy/android:java",
      "//chrome/browser/preferences:java",
      "//chrome/browser/prefetch/android:java",
      "//chrome/browser/prefetch/android:javatests",
      "//chrome/browser/privacy_guide/android:java",
      "//chrome/browser/privacy_guide/android:javatests",
      "//chrome/browser/privacy_sandbox/android:java",
      "//chrome/browser/privacy_sandbox/android:javatests",
      "//chrome/browser/profiles/android:java",
      "//chrome/browser/safe_browsing/android:java",
      "//chrome/browser/safe_browsing/android:javatests",
      "//chrome/browser/safety_check/android:java",
      "//chrome/browser/safety_check/android:javatests",
      "//chrome/browser/search_engines/android:java",
      "//chrome/browser/selection/android:javatests",
      "//chrome/browser/settings:java",
      "//chrome/browser/settings:test_support_java",
      "//chrome/browser/share:java",
      "//chrome/browser/share/android:java_resources",
      "//chrome/browser/signin/services/android:java",
      "//chrome/browser/supervised_user:javatests",
      "//chrome/browser/supervised_user:test_support_java",
      "//chrome/browser/sync/android:java",
      "//chrome/browser/sync/test/android:test_support_java",
      "//chrome/browser/tab:critical_persisted_tab_data_flatbuffer_java",
      "//chrome/browser/tab:critical_persisted_tab_data_proto_java",
      "//chrome/browser/tab:java",
      "//chrome/browser/tab_group:java",
      "//chrome/browser/tabmodel:java",
      "//chrome/browser/tabmodel/internal:java",
      "//chrome/browser/tabpersistence:java",
      "//chrome/browser/thumbnail:java",
      "//chrome/browser/thumbnail:javatests",
      "//chrome/browser/ui/android/appmenu:java",
      "//chrome/browser/ui/android/appmenu/test:test_support_java",
      "//chrome/browser/ui/android/favicon:java",
      "//chrome/browser/ui/android/layouts:java",
      "//chrome/browser/ui/android/layouts/test:java",
      "//chrome/browser/ui/android/layouts/third_party/float_property:java",
      "//chrome/browser/ui/android/logo:java",
      "//chrome/browser/ui/android/multiwindow:javatests",
      "//chrome/browser/ui/android/native_page:java",
      "//chrome/browser/ui/android/night_mode:java",
      "//chrome/browser/ui/android/night_mode:night_mode_java_test_support",
      "//chrome/browser/ui/android/omnibox:java",
      "//chrome/browser/ui/android/page_info:java",
      "//chrome/browser/ui/android/quickactionsearchwidget:java",
      "//chrome/browser/ui/android/quickactionsearchwidget:javatests",
      "//chrome/browser/ui/android/searchactivityutils:java",
      "//chrome/browser/ui/android/signin:java",
      "//chrome/browser/ui/android/signin:javatests",
      "//chrome/browser/ui/android/theme:java",
      "//chrome/browser/ui/android/toolbar:java",
      "//chrome/browser/ui/messages/android:java",
      "//chrome/browser/uid/android:java",
      "//chrome/browser/util:java",
      "//chrome/browser/video_tutorials:test_support_java",
      "//chrome/browser/webapps/android:java",
      "//chrome/test:sync_integration_test_support_java",
      "//chrome/test:test_support_java",
      "//chrome/test/android:chrome_java_integration_test_support",
      "//chrome/test/android:chrome_java_test_pagecontroller",
      "//chrome/test/android/test_trusted_web_activity:test_trusted_web_activity_java",
      "//components/autofill/android:autofill_java",
      "//components/autofill/android:prefeditor_autofill_java",
      "//components/autofill_assistant/android:public_dependencies_java",
      "//components/autofill_assistant/android:public_java",
      "//components/background_task_scheduler:background_task_scheduler_java",
      "//components/background_task_scheduler:background_task_scheduler_task_ids_java",
      "//components/bookmarks/common/android:bookmarks_java",
      "//components/browser_ui/accessibility/android:java",
      "//components/browser_ui/bottomsheet/android:java",
      "//components/browser_ui/bottomsheet/android/test:java",
      "//components/browser_ui/display_cutout/android:java",
      "//components/browser_ui/media/android:java",
      "//components/browser_ui/modaldialog/android:java",
      "//components/browser_ui/modaldialog/android:test_support_java",
      "//components/browser_ui/notifications/android:java",
      "//components/browser_ui/notifications/android:test_support_java",
      "//components/browser_ui/settings/android:java",
      "//components/browser_ui/settings/android:test_support_java",
      "//components/browser_ui/share/android:java",
      "//components/browser_ui/share/android:javatests",
      "//components/browser_ui/site_settings/android:constants_java",
      "//components/browser_ui/site_settings/android:java",
      "//components/browser_ui/site_settings/android:javatests",
      "//components/browser_ui/sms/android:java",
      "//components/browser_ui/styles/android:java",
      "//components/browser_ui/util/android:java",
      "//components/browser_ui/widget/android:java",
      "//components/browser_ui/widget/android:test_support_java",
      "//components/commerce/core:proto_java",
      "//components/commerce/core/android:core_java",
      "//components/content_settings/android:content_settings_enums_java",
      "//components/content_settings/android:java",
      "//components/crash/android:java",
      "//components/digital_goods/mojom:mojom_java",
      "//components/dom_distiller/core/android:dom_distiller_core_java",
      "//components/dom_distiller/core/mojom:mojom_java",
      "//components/download/internal/background_service:internal_java",
      "//components/download/internal/common:internal_java",
      "//components/download/network:network_java",
      "//components/download/public/common:public_java",
      "//components/embedder_support/android:content_view_java",
      "//components/embedder_support/android:context_menu_java",
      "//components/embedder_support/android:simple_factory_key_java",
      "//components/embedder_support/android:util_java",
      "//components/embedder_support/android:web_contents_delegate_java",
      "//components/external_intents/android:java",
      "//components/external_intents/android:test_support_java",
      "//components/externalauth/android:java",
      "//components/favicon/android:java",
      "//components/feature_engagement:feature_engagement_java",
      "//components/feed/core/v2:feedv2_core_java",
      "//components/gcm_driver/android:gcm_driver_java",
      "//components/gcm_driver/instance_id/android:instance_id_driver_java",
      "//components/gcm_driver/instance_id/android:instance_id_driver_test_support_java",
      "//components/image_fetcher:java",
      "//components/infobars/android:java",
      "//components/infobars/core:infobar_enums_java",
      "//components/javascript_dialogs/android:java",
      "//components/language/android:java",
      "//components/language/android:javatests",
      "//components/location/android:location_java",
      "//components/media_router/browser/android:java",
      "//components/media_router/browser/android:test_support_java",
      "//components/messages/android:java",
      "//components/messages/android/internal:java",
      "//components/messages/android/test:test_support_java",
      "//components/metrics:metrics_java",
      "//components/minidump_uploader:minidump_uploader_java",
      "//components/minidump_uploader:minidump_uploader_java_test_support",
      "//components/navigation_interception/android:navigation_interception_java",
      "//components/offline_items_collection/core:core_java",
      "//components/offline_pages/core/prefetch:offline_prefetch_proto_java",
      "//components/omnibox/browser:browser_java",
      "//components/omnibox/browser:junit_test_support",
      "//components/optimization_guide/proto:optimization_guide_proto_java",
      "//components/page_info/android:java",
      "//components/page_info/android:page_info_action_enum_java",
      "//components/page_info/core:proto_java",
      "//components/paint_preview/player/android:java",
      "//components/paint_preview/player/android:javatests",
      "//components/password_manager/core/browser:password_manager_java_enums",
      "//components/password_manager/core/browser:unified_password_manager_proto_java",
      "//components/payments/content/android:java",
      "//components/payments/content/android:service_java",
      "//components/payments/mojom:mojom_java",
      "//components/permissions/android:java",
      "//components/policy/android:policy_java",
      "//components/policy/android:policy_java_test_support",
      "//components/power_bookmarks/core:proto_java",
      "//components/prefs/android:java",
      "//components/profile_metrics:browser_profile_type_enum_java",
      "//components/query_tiles:java",
      "//components/query_tiles:test_support_java",
      "//components/safe_browsing/android:safe_browsing_java",
      "//components/schema_org/common:mojom_java",
      "//components/search_engines/android:java",
      "//components/security_interstitials/content/android:java",
      "//components/security_state/content/android:java",
      "//components/security_state/core:security_state_enums_java",
      "//components/send_tab_to_self:send_tab_to_self_java",
      "//components/signin/core/browser:signin_enums_java",
      "//components/signin/public/android:java",
      "//components/signin/public/android:signin_java_test_support",
      "//components/site_engagement/content/android:java",
      "//components/sync/android:sync_java",
      "//components/sync/protocol:protocol_java",
      "//components/sync_device_info:sync_device_info_java",
      "//components/url_formatter/android:url_formatter_java",
      "//components/user_prefs/android:java",
      "//components/version_info/android:version_constants_java",
      "//components/webapk/android/libs/client:java",
      "//components/webapps/browser/android:java",
      "//components/webauthn/android:java",
      "//components/webauthn/android:test_support_java",
      "//content/public/android:content_java",
      "//content/public/common:common_java",
      "//content/public/test/android:content_java_test_support",
      "//media/base/android:java_switches",
      "//media/base/android:media_java",
      "//mojo/public/java:bindings_java",
      "//mojo/public/java:system_java",
      "//mojo/public/mojom/base:base_java",
      "//net/android:net_java",
      "//net/android:net_java_test_support",
      "//services:services_javatests",
      "//services/device/public/java:geolocation_java",
      "//services/device/public/java:geolocation_java_test_support",
      "//services/device/public/mojom:mojom_java",
      "//services/media_session/public/mojom:mojom_java",
      "//services/network/public/mojom:mojom_java",
      "//services/network/public/mojom:mojom_proxy_config_java",
      "//services/network/public/mojom:url_loader_base_java",
      "//services/service_manager/public/java:service_manager_java",
      "//third_party/android_deps:com_google_code_findbugs_jsr305_java",
      "//third_party/android_deps:com_google_flatbuffers_flatbuffers_java_java",
      "//third_party/android_deps:espresso_java",
      "//third_party/android_deps:material_design_java",
      "//third_party/android_deps:protobuf_lite_runtime_java",
      "//third_party/android_deps/local_modifications/preconditions/javatests",
      "//third_party/android_sdk:android_test_base_java",
      "//third_party/android_sdk:android_test_mock_java",
      "//third_party/android_sdk:android_test_runner_java",
      "//third_party/android_support_test_runner:rules_java",
      "//third_party/android_support_test_runner:runner_java",
      "//third_party/androidx:androidx_activity_activity_java",
      "//third_party/androidx:androidx_browser_browser_java",
      "//third_party/androidx:androidx_collection_collection_java",
      "//third_party/androidx:androidx_preference_preference_java",
      "//third_party/androidx:androidx_test_runner_java",
      "//third_party/androidx:androidx_test_uiautomator_uiautomator_java",
      "//third_party/androidx:androidx_viewpager2_viewpager2_java",
      "//third_party/androidx:androidx_viewpager_viewpager_java",
      "//third_party/blink/public:blink_headers_java",
      "//third_party/blink/public/common:common_java",
      "//third_party/blink/public/mojom:android_mojo_bindings_java",
      "//third_party/blink/public/mojom:mojom_core_java",
      "//third_party/blink/public/mojom:mojom_mhtml_load_result_java",
      "//third_party/blink/public/mojom:mojom_platform_java",
      "//third_party/hamcrest:hamcrest_java",
      "//third_party/junit",
      "//third_party/metrics_proto:metrics_proto_java",
      "//third_party/mockito:mockito_java",
      "//ui/android:clipboard_java_test_support",
      "//ui/android:ui_java",
      "//ui/android:ui_java_test_support",
      "//ui/base/ime/mojom:mojom_java",
      "//ui/base/mojom:mojom_java",
      "//url:gurl_java",
      "//url:gurl_junit_test_support",
      "//url:origin_java",
      "//url/mojom:url_mojom_gurl_java",
      "//url/mojom:url_mojom_origin_java",
    ]

    deps += feed_test_deps

    deps += commerce_subscriptions_java_test_deps

    if (enable_basic_printing) {
      deps += [ "//printing:printing_java" ]
    }

    srcjar_deps = [ "//chrome/browser:tos_dialog_behavior_generated_enum" ]

    data = [
      "//chrome/test/data/android/",
      "//chrome/test/data/autofill/",
      "//chrome/test/data/background_sync/",
      "//chrome/test/data/banners/",
      "//chrome/test/data/dom_distiller/",
      "//chrome/test/data/browsing_data/",
      "//chrome/test/data/encoding_tests/auto_detect/Big5_with_no_encoding_specified.html",
      "//chrome/test/data/geolocation/",
      "//chrome/test/data/google/",
      "//chrome/test/data/image_search/valid.png",
      "//chrome/test/data/media/",
      "//chrome/test/data/navigation_interception/",
      "//chrome/test/data/notifications/",
      "//chrome/test/data/offline_pages/",
      "//chrome/test/data/password/",
      "//chrome/test/data/popup_blocker/",
      "//chrome/test/data/portal/",
      "//chrome/test/data/push_messaging/",
      "//chrome/test/data/translate/",
      "//chrome/test/media_router/resources/",
      "//components/test/data/autofill/",
      "//components/test/data/payments/",
      "//content/test/data/browsing_data/",
      "//content/test/data/android/authenticator.html",
      "//content/test/data/android/auto_downloads_permissions.html",
      "//content/test/data/android/eme_permissions.html",
      "//content/test/data/android/geolocation.html",
      "//content/test/data/android/installedapp.html",
      "//content/test/data/android/media_permissions.html",
      "//content/test/data/android/midi_permissions.html",
      "//content/test/data/android/permission_navigation.html",
      "//content/test/data/android/webshare.html",
      "//content/test/data/android/webshare-apk.html",
      "//content/test/data/android/webshare-bmp.html",
      "//content/test/data/android/webshare-csv.html",
      "//content/test/data/android/webshare-dex.html",
      "//content/test/data/android/webshare-ogg.html",
      "//content/test/data/android/webshare-many.html",
      "//content/test/data/android/webshare-large.html",
      "//content/test/data/android/webshare-long.html",
      "//content/test/data/android/webshare-separator.html",
      "//content/test/data/media/bear.webm",
      "//content/test/data/media/getusermedia.html",
      "//content/test/data/media/session/",
      "//content/test/data/media/video-player.html",
      "//content/test/data/media/webrtc_test_utilities.js",
      "//media/test/data/bear.mp4",
      "//media/test/data/bear-vp8-webvtt.webm",
      "//media/test/data/bear-vp8a.webm",
      "//media/test/data/sfx.mp3",
    ]

    data_deps = [ "//ui/base:goldctl" ]
  }

  if (enable_vr || enable_arcore) {
    chrome_test_xr_java_deps = [
      ":chrome_test_util_java",
      "//base:base_java",
      "//base:base_java_test_support",
      "//chrome/android:chrome_java",
      "//chrome/android:delegate_public_impl_java",
      "//chrome/android/features/vr:java",
      "//chrome/browser/flags:java",
      "//chrome/browser/tab:java",
      "//chrome/browser/tabmodel:java",
      "//chrome/browser/ui/messages/android:java",
      "//chrome/browser/util:java",
      "//chrome/test/android:chrome_java_integration_test_support",
      "//components/embedder_support/android:util_java",
      "//components/permissions/android:java",
      "//components/policy/android:policy_java",
      "//content/public/android:content_java",
      "//content/public/test/android:content_java_test_support",
      "//net/android:net_java_test_support",
      "//third_party/android_support_test_runner:rules_java",
      "//third_party/android_support_test_runner:runner_java",
      "//third_party/androidx:androidx_annotation_annotation_java",
      "//third_party/androidx:androidx_browser_browser_java",
      "//third_party/androidx:androidx_test_runner_java",
      "//third_party/androidx:androidx_test_uiautomator_uiautomator_java",
      "//third_party/junit",
      "//ui/android:ui_java",
    ]

    # Files used for both VR and AR testing
    android_library("chrome_test_xr_java") {
      testonly = true

      sources = [
        "javatests/src/org/chromium/chrome/browser/vr/WebXrTestFramework.java",
        "javatests/src/org/chromium/chrome/browser/vr/XrTestFramework.java",
        "javatests/src/org/chromium/chrome/browser/vr/rules/ChromeTabbedActivityXrTestRule.java",
        "javatests/src/org/chromium/chrome/browser/vr/rules/CustomTabActivityXrTestRule.java",
        "javatests/src/org/chromium/chrome/browser/vr/rules/WebappActivityXrTestRule.java",
        "javatests/src/org/chromium/chrome/browser/vr/rules/XrActivityRestriction.java",
        "javatests/src/org/chromium/chrome/browser/vr/rules/XrActivityRestrictionRule.java",
        "javatests/src/org/chromium/chrome/browser/vr/rules/XrTestRule.java",
        "javatests/src/org/chromium/chrome/browser/vr/util/PermissionUtils.java",
        "javatests/src/org/chromium/chrome/browser/vr/util/XrTestRuleUtils.java",
      ]

      deps = chrome_test_xr_java_deps

      data = [
        "//chrome/test/data/android/feed/",
        "//chrome/test/data/xr/e2e_test_files/",
        "//third_party/blink/web_tests/resources/testharness.js",
      ]

      data_deps = [ "//ui/base:goldctl" ]
    }

    if (enable_vr) {
      # All files necessary for VR instrumentation tests
      android_library("chrome_test_vr_java") {
        testonly = true

        sources = [
          "javatests/src/org/chromium/chrome/browser/vr/EmulatedVrController.java",
          "javatests/src/org/chromium/chrome/browser/vr/TestVrShellDelegate.java",
          "javatests/src/org/chromium/chrome/browser/vr/VrDaydreamReadyModuleInstallTest.java",
          "javatests/src/org/chromium/chrome/browser/vr/VrInstallUpdateMessageTest.java",
          "javatests/src/org/chromium/chrome/browser/vr/WebXrVrDeviceTest.java",
          "javatests/src/org/chromium/chrome/browser/vr/WebXrVrInputTest.java",
          "javatests/src/org/chromium/chrome/browser/vr/WebXrVrPermissionTest.java",
          "javatests/src/org/chromium/chrome/browser/vr/WebXrVrPermissionTestFramework.java",
          "javatests/src/org/chromium/chrome/browser/vr/WebXrVrTabTest.java",
          "javatests/src/org/chromium/chrome/browser/vr/WebXrVrTestFramework.java",
          "javatests/src/org/chromium/chrome/browser/vr/WebXrVrTransitionTest.java",
          "javatests/src/org/chromium/chrome/browser/vr/mock/MockBrowserKeyboardInterface.java",
          "javatests/src/org/chromium/chrome/browser/vr/mock/MockVrCoreVersionChecker.java",
          "javatests/src/org/chromium/chrome/browser/vr/mock/MockVrDaydreamApi.java",
          "javatests/src/org/chromium/chrome/browser/vr/rules/ChromeTabbedActivityVrTestRule.java",
          "javatests/src/org/chromium/chrome/browser/vr/rules/CustomTabActivityVrTestRule.java",
          "javatests/src/org/chromium/chrome/browser/vr/rules/VrActivityRestrictionRule.java",
          "javatests/src/org/chromium/chrome/browser/vr/rules/VrModuleNotInstalled.java",
          "javatests/src/org/chromium/chrome/browser/vr/rules/VrTestRule.java",
          "javatests/src/org/chromium/chrome/browser/vr/rules/WebappActivityVrTestRule.java",
          "javatests/src/org/chromium/chrome/browser/vr/util/NativeUiUtils.java",
          "javatests/src/org/chromium/chrome/browser/vr/util/RenderTestUtils.java",
          "javatests/src/org/chromium/chrome/browser/vr/util/VrMessageUtils.java",
          "javatests/src/org/chromium/chrome/browser/vr/util/VrShellDelegateUtils.java",
          "javatests/src/org/chromium/chrome/browser/vr/util/VrTestRuleUtils.java",
          "javatests/src/org/chromium/chrome/browser/vr/util/VrTransitionUtils.java",
        ]

        deps = chrome_test_xr_java_deps + [
                 ":chrome_test_util_java",
                 "//chrome/android:chrome_test_xr_java",
                 "//chrome/browser/profiles/android:java",
                 "//chrome/browser/settings:java",
                 "//chrome/browser/tabmodel:java",
                 "//components/browser_ui/site_settings/android:java",
                 "//components/content_settings/android:content_settings_enums_java",
                 "//components/infobars/android:java",
                 "//components/messages/android:java",
                 "//components/messages/android/internal:java",
                 "//components/messages/android/test:test_support_java",
                 "//components/module_installer/android:module_installer_java",
                 "//third_party/gvr-android-sdk:controller_test_api_java",
                 "//third_party/gvr-android-sdk:gvr_common_java",
                 "//ui/android:ui_java_test_support",
               ]

        data = [
          "//chrome/android/shared_preference_files/test/",
          "//third_party/gvr-android-sdk/test-apks/",
        ]
      }
    }

    if (enable_arcore) {
      # All files necessary for AR instrumentation tests
      android_library("chrome_test_ar_java") {
        testonly = true

        sources = [
          "javatests/src/org/chromium/chrome/browser/vr/WebXrArAnchorsTest.java",
          "javatests/src/org/chromium/chrome/browser/vr/WebXrArCameraAccessTest.java",
          "javatests/src/org/chromium/chrome/browser/vr/WebXrArDepthSensingTest.java",
          "javatests/src/org/chromium/chrome/browser/vr/WebXrArHitTestTest.java",
          "javatests/src/org/chromium/chrome/browser/vr/WebXrArLightEstimationTest.java",
          "javatests/src/org/chromium/chrome/browser/vr/WebXrArSanityTest.java",
          "javatests/src/org/chromium/chrome/browser/vr/WebXrArSessionTest.java",
          "javatests/src/org/chromium/chrome/browser/vr/WebXrArTestFramework.java",
          "javatests/src/org/chromium/chrome/browser/vr/WebXrArViewportScaleTest.java",
          "javatests/src/org/chromium/chrome/browser/vr/rules/ArPlaybackFile.java",
          "javatests/src/org/chromium/chrome/browser/vr/rules/ArTestRule.java",
          "javatests/src/org/chromium/chrome/browser/vr/rules/ChromeTabbedActivityArTestRule.java",
          "javatests/src/org/chromium/chrome/browser/vr/rules/CustomTabActivityArTestRule.java",
          "javatests/src/org/chromium/chrome/browser/vr/rules/WebappActivityArTestRule.java",
          "javatests/src/org/chromium/chrome/browser/vr/util/ArTestRuleUtils.java",
        ]

        deps = chrome_test_xr_java_deps +
               [ "//chrome/android:chrome_test_xr_java" ]

        data = [
          "//chrome/test/data/xr/ar_playback_datasets/",
          "//third_party/arcore-android-sdk/test-apks/",
        ]
      }
    }
  }

  # Overrides icon / name defined in chrome_app_java_resources.
  android_resources("chrome_public_apk_resources") {
    resource_overlay = true
    sources = [
      "java/res_chromium/drawable-hdpi/fre_product_logo.png",
      "java/res_chromium/drawable-hdpi/product_logo_name.png",
      "java/res_chromium/drawable-mdpi/fre_product_logo.png",
      "java/res_chromium/drawable-mdpi/product_logo_name.png",
      "java/res_chromium/drawable-xhdpi/fre_product_logo.png",
      "java/res_chromium/drawable-xhdpi/product_logo_name.png",
      "java/res_chromium/drawable-xxhdpi/fre_product_logo.png",
      "java/res_chromium/drawable-xxhdpi/product_logo_name.png",
      "java/res_chromium/drawable-xxxhdpi/fre_product_logo.png",
      "java/res_chromium/drawable-xxxhdpi/product_logo_name.png",
    ]

    # Dep needed to ensure override works properly.
    deps = [ ":chrome_app_java_resources" ]
  }

  # Overrides icon / name defined in chrome_base_module_resources.
  android_resources("chrome_public_apk_base_module_resources") {
    resource_overlay = true
    sources = [
      "java/res_chromium_base/drawable/themed_app_icon.xml",
      "java/res_chromium_base/mipmap-hdpi/app_icon.png",
      "java/res_chromium_base/mipmap-hdpi/layered_app_icon.png",
      "java/res_chromium_base/mipmap-hdpi/layered_app_icon_background.png",
      "java/res_chromium_base/mipmap-mdpi/app_icon.png",
      "java/res_chromium_base/mipmap-mdpi/layered_app_icon.png",
      "java/res_chromium_base/mipmap-mdpi/layered_app_icon_background.png",
      "java/res_chromium_base/mipmap-nodpi/layered_app_icon_foreground.xml",
      "java/res_chromium_base/mipmap-xhdpi/app_icon.png",
      "java/res_chromium_base/mipmap-xhdpi/layered_app_icon.png",
      "java/res_chromium_base/mipmap-xhdpi/layered_app_icon_background.png",
      "java/res_chromium_base/mipmap-xxhdpi/app_icon.png",
      "java/res_chromium_base/mipmap-xxhdpi/layered_app_icon.png",
      "java/res_chromium_base/mipmap-xxhdpi/layered_app_icon_background.png",
      "java/res_chromium_base/mipmap-xxxhdpi/app_icon.png",
      "java/res_chromium_base/mipmap-xxxhdpi/layered_app_icon.png",
      "java/res_chromium_base/mipmap-xxxhdpi/layered_app_icon_background.png",
      "java/res_chromium_base/values/channel_constants.xml",
    ]

    # Dep needed to ensure override works properly.
    deps = [ ":chrome_base_module_resources" ]
  }

  version_resource_dir = "$target_gen_dir/templates/chrome_version_xml/res"
  version_resource_file = "$version_resource_dir/values/strings.xml"
  process_version("version_xml") {
    process_only = true
    template_file = "java/version_strings.xml.template"
    sources = [ "//chrome/VERSION" ]
    output = version_resource_file
  }

  android_resources("product_version_resources") {
    sources = [ version_resource_file ]
    deps = [ ":version_xml" ]
  }

  java_group("chrome_public_non_pak_assets") {
    deps = [
      "//chrome/android/webapk/libs/runtime_library:runtime_library_assets",
    ]
  }

  java_group("chrome_public_v8_assets") {
    if (use_v8_context_snapshot) {
      deps = [ "//tools/v8_context_snapshot:v8_context_snapshot_assets" ]
    } else {
      deps = [ "//v8:v8_external_startup_data_assets" ]
    }
  }

  jinja_template_resources("chrome_public_apk_template_resources") {
    resources = [
      "java/res_template/xml/file_paths.xml",
      "java/res_template/xml/launchershortcuts.xml",
      "java/res_template/xml/searchable.xml",
    ]
    res_dir = "java/res_template"
    variables = [ "manifest_package=$chrome_public_manifest_package" ]
  }

  jinja_template_resources("chrome_test_apk_template_resources") {
    resource_overlay = true
    resources = [
      "java/res_template/xml/file_paths.xml",
      "java/res_template/xml/launchershortcuts.xml",
      "java/res_template/xml/searchable.xml",
    ]
    res_dir = "java/res_template"

    # Add dep to ensure these override the ones in
    # chrome_public_apk_template_resources.
    deps = [ ":chrome_public_apk_template_resources" ]
    variables = [ "manifest_package=$chrome_public_test_manifest_package" ]
  }

  generate_jni_registration("chrome_jni_registration") {
    targets = [ ":chrome_public_base_module_java" ]
    header_output = chrome_jni_registration_header
    sources_exclusions = chrome_jni_sources_exclusions
  }

  # The test apks do not use chromium linker, but using manual JNI registration
  # helps ensure that the correct classes are marked with @MainDex.
  generate_jni_registration("chrome_jni_for_test_registration") {
    testonly = true
    targets = [ ":chrome_public_base_module_java_for_test" ]
    header_output = chrome_jni_for_test_registration_header
    sources_exclusions = chrome_jni_sources_exclusions
  }

  # This template instantiates targets responsible for generating pak
  # resources. The generated target names are derived from input variables.
  #
  # Variables:
  #    is_monochrome: If true, generate Monochrome targets rather than Chrome.
  #    is_trichrome: (Optional) Generate Trichrome targets that use monochrome
  #      library targets but don't include webview resources.
  #    is_bundle_module: (Optional) If true, generate targets for base bundle
  #      module.
  template("resource_packaging") {
    _is_monochrome = invoker.is_monochrome
    _is_trichrome = defined(invoker.is_trichrome) && invoker.is_trichrome
    _is_bundle_module =
        defined(invoker.is_bundle_module) && invoker.is_bundle_module

    if (_is_trichrome) {
      _variant = "trichrome_chrome"
    } else if (_is_monochrome) {
      _variant = "monochrome"
    } else {
      _variant = "chrome"
    }
    if (_is_bundle_module) {
      _variant += "_bundle_module"
    } else {
      _variant += "_apk"
    }

    if (enable_resource_allowlist_generation) {
      if (_is_trichrome || _is_monochrome) {
        _lib = "libmonochrome_pak_allowlist_inputs"
      } else {
        _lib = "libchrome_pak_allowlist_inputs"
      }
      _resource_allowlist_target = "${_variant}_resource_allowlist"
      _resource_allowlist_file =
          "$target_gen_dir/${_variant}_resource_allowlist.txt"
      _lib_path = "/lib.unstripped/" + _lib + shlib_extension

      generate_resource_allowlist(_resource_allowlist_target) {
        _fat_lib_toolchain = ""
        if (_is_monochrome || _is_trichrome) {
          # Always use the 32-bit library's allowlist since the 64-bit one is
          # webview-only.
          # TODO(agrieve): For 64-bit primary, using libmonochrome_64 would be
          #     more efficient.
          if (android_64bit_target_cpu && !skip_secondary_abi_for_cq) {
            _fat_lib_toolchain = android_secondary_abi_toolchain
          } else {
            _fat_lib_toolchain = current_toolchain
          }
        }
        deps = [ ":${_lib}($_fat_lib_toolchain)" ]

        inputs = [ get_label_info(deps[0], "root_out_dir") + _lib_path ]
        output = _resource_allowlist_file
      }

      # Use custom resource ID list instead of android_webview's compiler
      # resource allowlist because //android_webview:generate_webui_resources
      # and //android_webview:generate_components_resources use hand-written
      # resource allowlists.
      if (_is_monochrome) {
        _locale_allowlist_target = "${_variant}_locale_allowlist"
        _locale_allowlist_file =
            "$target_gen_dir/${_variant}_locale_allowlist.txt"
        _system_webview_locale_allowlist_target =
            "${_variant}_system_webview_locale_resource_allowlist"
        _system_webview_locale_allowlist_file =
            "$target_gen_dir/" +
            "${_variant}_system_webview_locale_resource_id_list.txt"

        action(_system_webview_locale_allowlist_target) {
          script = "//tools/grit/pak_util.py"

          _system_webview_en_US_locale_pak =
              "$root_out_dir/android_webview/locales/en-US.pak"

          inputs = [ _system_webview_en_US_locale_pak ]

          outputs = [ _system_webview_locale_allowlist_file ]

          deps = [ "//android_webview:repack_locales" ]

          args = [
            "list-id",
            "--output",
            rebase_path(_system_webview_locale_allowlist_file, root_build_dir),
            rebase_path(_system_webview_en_US_locale_pak, root_build_dir),
          ]
        }

        action(_locale_allowlist_target) {
          script = "//tools/resources/filter_resource_allowlist.py"

          inputs = [
            _resource_allowlist_file,
            _system_webview_locale_allowlist_file,
          ]

          outputs = [ _locale_allowlist_file ]

          deps = [
            ":$_resource_allowlist_target",
            ":$_system_webview_locale_allowlist_target",
            "//android_webview:system_webview_pak_allowlist",
          ]

          args = [
            "--input",
            rebase_path(_resource_allowlist_file, root_build_dir),
            "--filter",
            rebase_path(_system_webview_locale_allowlist_file, root_build_dir),
            "--output",
            rebase_path(_locale_allowlist_file, root_build_dir),
          ]
        }
      }
    }

    chrome_paks("${_variant}_paks") {
      output_dir = "$target_gen_dir/${_variant}_paks"
      deps = []

      additional_extra_paks = []
      if (_is_monochrome) {
        if (webview_includes_weblayer) {
          additional_extra_paks +=
              [ "$root_gen_dir/weblayer/weblayer_resources.pak" ]
          deps += [ "//weblayer:resources" ]
        }
      }
      if (!dfmify_dev_ui || !_is_bundle_module) {
        additional_extra_paks += [ "$root_gen_dir/chrome/dev_ui_resources.pak" ]
        deps += [ "//chrome/browser/resources:dev_ui_paks" ]
      }

      if (enable_resource_allowlist_generation) {
        repack_allowlist = _resource_allowlist_file
        deps += [ ":${_resource_allowlist_target}" ]
        if (_is_monochrome) {
          locale_allowlist = _locale_allowlist_file
          deps += [ ":$_locale_allowlist_target" ]
        }
      }
    }

    android_assets("${_variant}_locale_pak_assets") {
      disable_compression = true
      renaming_sources = []
      renaming_destinations = []

      foreach(_locale, platform_pak_locales) {
        renaming_sources +=
            [ "$target_gen_dir/${_variant}_paks/locales/$_locale.pak" ]
        renaming_destinations += [ "locales/$_locale.pak" ]
      }
      treat_as_locale_paks = true

      deps = [ ":${_variant}_paks" ]
    }

    # This target explicitly includes locale paks via deps.
    android_assets(target_name) {
      assert("${_variant}_pak_assets" == target_name)
      sources = [
        "$target_gen_dir/${_variant}_paks/chrome_100_percent.pak",
        "$target_gen_dir/${_variant}_paks/resources.pak",
      ]
      disable_compression = true

      deps = [
        ":${_variant}_locale_pak_assets",
        ":${_variant}_paks",
      ]
      if (_is_monochrome) {
        deps += [ "//android_webview:locale_pak_assets" ]
        if (webview_includes_weblayer && !_is_bundle_module) {
          deps += [ "//weblayer:locale_pak_assets" ]
        }
      }
    }
  }

  # Resource packaging varies with Monochrome and bundles because the pak
  # resource allowlist is derived from the native library.
  resource_packaging("chrome_apk_pak_assets") {
    is_monochrome = false
  }
  resource_packaging("chrome_bundle_module_pak_assets") {
    is_monochrome = false
    is_bundle_module = true
  }
  resource_packaging("monochrome_apk_pak_assets") {
    is_monochrome = true
  }
  resource_packaging("monochrome_bundle_module_pak_assets") {
    is_monochrome = true
    is_bundle_module = true
  }
  resource_packaging("trichrome_chrome_bundle_module_pak_assets") {
    is_monochrome = false
    is_trichrome = true
    is_bundle_module = true
  }

  # Java libraries that go into each public chrome APK and base module. The chrome
  # JNI registration is generated based on this target.
  # TODO(tiborg): Remove the following three groups once we have a APK / module
  # target that contain exactly the grouped java libraries.
  java_group("chrome_public_base_module_java") {
    deps = [
      ":chrome_all_java",
      ":delegate_public_impl_java",
    ]
  }

  # Exists separately from chrome_public_base_module_java_for_test to allow
  # downstream to depend on test support packages without needing to depend on
  # delegate_public_impl_java.
  java_group("chrome_public_base_module_java_test_support") {
    testonly = true
    deps = [
      ":browser_java_test_support",
      ":chrome_test_util_java",
      "//chrome/android/features/autofill_assistant:autofill_assistant_java_test_support",
      "//chrome/browser/android/metrics:ukm_java_test_support",
      "//chrome/browser/password_manager/android:test_support_java",
      "//chrome/browser/subresource_filter:subresource_filter_java_test_support",
      "//chrome/browser/supervised_user:test_support_java",
      "//chrome/browser/thumbnail:thumbnail_java_test_support",
      "//components/external_intents/android:test_support_java",
      "//components/minidump_uploader:minidump_uploader_java",
      "//components/paint_preview/player/android:player_java_test_support",
      "//content/public/test/android:content_java_test_support",
      "//ui/android:clipboard_java_test_support",
    ]
  }

  # Similar to chrome_public_base_module_java but for Java libraries that go into
  # the public chrome test APK.
  java_group("chrome_public_base_module_java_for_test") {
    testonly = true
    deps = [
      ":chrome_public_base_module_java",
      ":chrome_public_base_module_java_test_support",
    ]
  }

  # Dependencies that are common to any chrome_public derivative targets.
  _chrome_public_shared_deps = [
    ":chrome_public_apk_base_module_resources",
    ":chrome_public_apk_resources",
    ":chrome_public_base_module_java",
    ":chrome_public_non_pak_assets",
    ":chrome_public_v8_assets",
    "//third_party/icu:icu_assets",
  ]

  generate_jni("test_support_jni_headers") {
    testonly = true
    sources = [
      "javatests/src/org/chromium/chrome/browser/ServicificationBackgroundService.java",
      "javatests/src/org/chromium/chrome/browser/offlinepages/OfflineTestUtil.java",
      "javatests/src/org/chromium/chrome/browser/offlinepages/prefetch/PrefetchTestBridge.java",
      "javatests/src/org/chromium/chrome/browser/query_tiles/QueryTileFakeServer.java",
      "javatests/src/org/chromium/chrome/browser/sync/FakeServerHelper.java",
      "javatests/src/org/chromium/chrome/browser/test/MockCertVerifierRuleAndroid.java",
    ]
  }

  # Test support code that needs access to the browser.
  android_library("browser_java_test_support") {
    testonly = true
    sources = [
      "javatests/src/org/chromium/chrome/browser/ServicificationBackgroundService.java",
      "javatests/src/org/chromium/chrome/browser/offlinepages/OfflineTestUtil.java",
      "javatests/src/org/chromium/chrome/browser/offlinepages/prefetch/PrefetchTestBridge.java",
      "javatests/src/org/chromium/chrome/browser/query_tiles/QueryTileFakeServer.java",
      "javatests/src/org/chromium/chrome/browser/sync/FakeServerHelper.java",
      "javatests/src/org/chromium/chrome/browser/test/MockCertVerifierRuleAndroid.java",
    ]
    deps = [
      ":chrome_java",
      "$google_play_services_package:google_play_services_gcm_java",
      "//base:base_java",
      "//base:base_java_test_support",
      "//base:jni_java",
      "//build/android:build_java",
      "//chrome/browser/download/android:java",
      "//chrome/browser/prefetch/android:java",
      "//chrome/browser/profiles/android:java",
      "//chrome/browser/sync/android:java",
      "//chrome/browser/tab:java",
      "//components/offline_items_collection/core:core_java",
      "//components/sync/protocol:protocol_java",
      "//content/public/android:content_java",
      "//content/public/test/android:content_java_test_support",
      "//third_party/android_deps:protobuf_lite_runtime_java",
      "//third_party/androidx:androidx_annotation_annotation_java",
      "//third_party/junit",
      "//url:gurl_android_test_helper_java",
      "//url:gurl_java",
    ]

    annotation_processor_deps = [ "//base/android/jni_generator:jni_processor" ]
  }

  static_library("browser_test_support") {
    testonly = true
    sources = [
      "../browser/android/servicification_background_service_jni.cc",
      "../browser/android/ssl/mock_cert_verifier_rule_android.cc",
      "../browser/android/ssl/mock_cert_verifier_rule_android.h",
      "../browser/offline_pages/android/offline_test_util_jni.cc",
      "../browser/offline_pages/android/prefetch_test_bridge.cc",
      "../browser/query_tiles/query_tile_fake_server.cc",
      "../browser/sync/android/fake_server_helper_android.cc",
    ]
    deps = [
      ":test_support_jni_headers",
      "//chrome/browser",
      "//chrome/browser:browser_process",
      "//chrome/browser/thumbnail:test_support",
      "//components/offline_pages/core/background:test_support",
      "//components/query_tiles",
      "//components/query_tiles/test:test_support",
      "//components/sync:test_support",
      "//content/test:test_support",
      "//net:test_support",
      "//url:gurl_android",
      "//url:gurl_android_test_helper",
    ]
  }

  # Defines a target that derives from the chrome public application. This
  # can be either an APK or an app bundle module. This supports
  # chrome_public_xxx targets (for Android L-M). For Android N+, see instead
  # monochrome_public_apk_or_module_tmpl() below.
  #
  # Variables:
  #  target_type: Determines the final target type. Should be one of
  #    'android_apk', or 'android_app_bundle_module'.
  #  apk_name: For 'android_apk' target types, name of the final APK without
  #    an .apk suffix (e.g. 'ChromePublic').
  #  is_base_module: For 'android_app_bundle_module' target types only,
  #     set to true to indicate that this is a base application module
  #     (instead of a feature module).
  template("chrome_public_apk_or_module_tmpl") {
    _is_bundle_module = invoker.target_type == "android_app_bundle_module"
    chrome_public_common_apk_or_module_tmpl(target_name) {
      forward_variables_from(invoker,
                             [
                               "add_view_trace_events",
                               "apk_name",
                               "bundle_target",
                               "is_base_module",
                               "jni_registration_header",
                               "target_type",
                               "enable_lint",
                               "enable_multidex",
                               "lint_baseline_file",
                               "lint_suppressions_dep",
                               "lint_suppressions_file",
                             ])
      deps = _chrome_public_shared_deps

      if (_is_bundle_module) {
        deps += [ ":chrome_bundle_module_pak_assets" ]
      } else {
        deps += [ ":chrome_apk_pak_assets" ]
      }

      android_manifest = chrome_public_android_manifest
      android_manifest_dep = ":chrome_public_android_manifest"

      shared_libraries = [ ":libchrome" ]

      # Native libraries can be loaded directly from the APK using the
      # Chromium linker. However, we disable this for J-K due to an OEM-specific
      # platform bug, where overzealous SELinux settings prevent mapping some apk
      # file segments with PROT_EXEC (see http://crbug.com/398425). This was
      # fixed for Android L by adding proper CTS tests.
      load_library_from_apk = chromium_linker_supported

      version_name = chrome_version_name
    }
  }

  chrome_public_apk_or_module_tmpl("chrome_public_apk") {
    target_type = "android_apk"
    apk_name = "ChromePublic"
    enable_multidex = is_java_debug
  }

  chrome_public_apk_or_module_tmpl("chrome_modern_public_base_bundle_module") {
    target_type = "android_app_bundle_module"
    is_base_module = true
    add_view_trace_events = true
    bundle_target = ":chrome_modern_public_bundle"
  }

  android_library("monochrome_java") {
    deps = [
      ":base_module_java",
      ":base_monochrome_module_java",
      "//android_webview:android_webview_java",
      "//base:base_java",
      "//chrome/android:chrome_java",
      "//components/version_info/android:version_constants_java",
      "//content/public/android:content_java",
    ]
    sources = [
      "java/src/org/chromium/chrome/browser/MonochromeApplicationImpl.java",
    ]
  }

  # Monochrome equivalent of :base_module_java.
  android_library("base_monochrome_module_java") {
    sources = [ "java/src/org/chromium/chrome/browser/base/SplitMonochromeApplication.java" ]
    deps = [
      ":base_module_java",
      "//android_webview:android_webview_no_weblayer_java",
      "//base:base_java",
      "//build/android:build_java",
      "//components/version_info/android:version_constants_java",
      "//components/version_info/android:version_constants_java",
      "//content/public/android:content_java",
    ]
    if (webview_includes_weblayer) {
      deps += [ "//weblayer/browser/java:base_module_java" ]
    }
  }

  # Target for classes which should be in the base module, even when //chrome code
  # is in a DFM.
  android_library("base_module_java") {
    sources = [
      "java/src/com/google/ipc/invalidation/ticl/android2/channel/GcmRegistrationTaskService.java",
      "java/src/org/chromium/chrome/app/TrichromeZygotePreload.java",
      "java/src/org/chromium/chrome/browser/ChromeBackgroundService.java",
      "java/src/org/chromium/chrome/browser/ChromeBackupAgent.java",
      "java/src/org/chromium/chrome/browser/DeferredStartupHandler.java",
      "java/src/org/chromium/chrome/browser/app/bluetooth/BluetoothNotificationService.java",
      "java/src/org/chromium/chrome/browser/app/usb/UsbNotificationService.java",
      "java/src/org/chromium/chrome/browser/base/DexFixer.java",
      "java/src/org/chromium/chrome/browser/base/DexFixerReason.java",
      "java/src/org/chromium/chrome/browser/base/ServiceTracingProxyProvider.java",
      "java/src/org/chromium/chrome/browser/base/SplitChromeApplication.java",
      "java/src/org/chromium/chrome/browser/base/SplitCompatAppComponentFactory.java",
      "java/src/org/chromium/chrome/browser/base/SplitCompatApplication.java",
      "java/src/org/chromium/chrome/browser/base/SplitCompatBackupAgent.java",
      "java/src/org/chromium/chrome/browser/base/SplitCompatContentProvider.java",
      "java/src/org/chromium/chrome/browser/base/SplitCompatCustomTabsService.java",
      "java/src/org/chromium/chrome/browser/base/SplitCompatGcmListenerService.java",
      "java/src/org/chromium/chrome/browser/base/SplitCompatGcmTaskService.java",
      "java/src/org/chromium/chrome/browser/base/SplitCompatIntentService.java",
      "java/src/org/chromium/chrome/browser/base/SplitCompatJobService.java",
      "java/src/org/chromium/chrome/browser/base/SplitCompatMinidumpUploadJobService.java",
      "java/src/org/chromium/chrome/browser/base/SplitCompatRemoteViewsService.java",
      "java/src/org/chromium/chrome/browser/base/SplitCompatService.java",
      "java/src/org/chromium/chrome/browser/base/SplitPreloader.java",
      "java/src/org/chromium/chrome/browser/bookmarkswidget/BookmarkWidgetService.java",
      "java/src/org/chromium/chrome/browser/crash/ApplicationStatusTracker.java",
      "java/src/org/chromium/chrome/browser/crash/ChromeMinidumpUploadJobService.java",
      "java/src/org/chromium/chrome/browser/crash/FirebaseConfig.java",
      "java/src/org/chromium/chrome/browser/crash/MinidumpUploadService.java",
      "java/src/org/chromium/chrome/browser/customtabs/CustomTabsConnectionService.java",
      "java/src/org/chromium/chrome/browser/download/DownloadBroadcastManager.java",
      "java/src/org/chromium/chrome/browser/download/DownloadForegroundService.java",
      "java/src/org/chromium/chrome/browser/incognito/IncognitoNotificationService.java",
      "java/src/org/chromium/chrome/browser/media/MediaCaptureNotificationService.java",
      "java/src/org/chromium/chrome/browser/media/ui/ChromeMediaNotificationControllerServices.java",
      "java/src/org/chromium/chrome/browser/metrics/UmaUtils.java",
      "java/src/org/chromium/chrome/browser/notifications/NotificationJobService.java",
      "java/src/org/chromium/chrome/browser/notifications/NotificationService.java",
      "java/src/org/chromium/chrome/browser/omaha/OmahaClient.java",
      "java/src/org/chromium/chrome/browser/photo_picker/DecoderService.java",
      "java/src/org/chromium/chrome/browser/prerender/ChromePrerenderService.java",
      "java/src/org/chromium/chrome/browser/provider/ChromeBrowserProvider.java",
      "java/src/org/chromium/chrome/browser/services/gcm/ChromeGcmListenerService.java",
      "java/src/org/chromium/chrome/browser/services/gcm/GCMBackgroundService.java",
      "java/src/org/chromium/chrome/browser/services/gcm/InvalidationGcmUpstreamSender.java",
      "java/src/org/chromium/chrome/browser/tracing/TracingNotificationService.java",
      "java/src/org/chromium/chrome/browser/webapps/WebApkInstallCoordinatorService.java",
    ]
    deps = [
      ":chrome_base_module_resources",
      "$google_play_services_package:google_firebase_firebase_iid_java",
      "$google_play_services_package:google_firebase_firebase_messaging_java",
      "$google_play_services_package:google_play_services_gcm_java",
      "//base:base_java",
      "//base:jni_java",
      "//chrome/browser/download/android:file_provider_java",
      "//chrome/browser/flags:java",
      "//chrome/browser/language/android:base_module_java",
      "//chrome/browser/preferences:java",
      "//chrome/browser/util:java",
      "//chrome/browser/version:java",
      "//components/crash/android:java",
      "//components/embedder_support/android:application_java",
      "//components/media_router/browser/android:cast_options_provider_java",
      "//components/minidump_uploader:minidump_uploader_java",
      "//components/module_installer/android:module_installer_java",
      "//third_party/android_deps:com_google_android_play_core_java",
      "//third_party/androidx:androidx_annotation_annotation_java",
      "//third_party/androidx:androidx_collection_collection_java",
      "//ui/android:ui_no_recycler_view_java",

      # Deps needed for child processes.
      "//components/version_info/android:version_constants_java",
      "//components/viz/common:common_java",
      "//components/viz/service:service_java",
      "//content/public/android:content_main_dex_java",
      "//media/base/android:media_java",
      "//services/shape_detection:shape_detection_java",

      # Deps for DFMs.
      "//chrome/android/modules/stack_unwinder/provider:java",
      "//chrome/browser/test_dummy/internal:base_module_java",

      # Deps to pull services into base module.
      # TODO(crbug.com/1126301): Consider moving these to the chrome module to
      # reduce base dex size.
      "$google_play_services_package:google_play_services_cast_framework_java",
      "//components/background_task_scheduler:background_task_scheduler_java",
      "//components/payments/content/android:service_java",
      "//third_party/androidx:androidx_browser_browser_java",
    ]

    # More deps for DFMs.
    if (dfmify_dev_ui) {
      deps += [ "//chrome/android/modules/dev_ui/provider:java" ]
    }

    srcjar_deps = [ ":chrome_product_config" ]

    # If this throws an error, try depending on
    # //content/public/android:content_main_dex_java instead.
    assert_no_deps = [ "//content/public/android:content_full_java" ]

    # Add the actual implementation where necessary so that downstream targets
    # can provide their own implementations.
    jar_excluded_patterns = [ "*/ProductConfig.class" ]

    resources_package = "org.chromium.chrome.base"
    annotation_processor_deps = [ "//base/android/jni_generator:jni_processor" ]
  }

  # Defines a target that derives from the monochrome public application. This
  # can be either an APK or an app bundle module. Note that these only work
  # on Android N+ devices, see chrome_public_apk_or_module_tmpl() for a template
  # that supports generating targets for older Android releases.
  #
  # Variables:
  #   target_type: Either 'android_apk' or 'android_app_bundle_module'.
  #   apk_name: For APK target types, the final APK name without an .apk
  #     suffix (e.g. "MonochromePublic").
  #   is_base_module: For module target types, a boolean indicating whether
  #     this is a base bundle module (instead of a feature one).
  #   is_64_bit_browser: When compiling in a 64-bit configuration, a boolean
  #     indicating whether the browser is 64-bit or 32-bit.
  #   include_32_bit_webview: When compiling a 64-bit browser configuration, if
  #     true, a 32-bit WebView library will also be built and included.
  template("monochrome_public_apk_or_module_tmpl") {
    _android_manifest =
        "$target_gen_dir/manifest/${target_name}/AndroidManifest.xml"
    _split_android_manifest =
        "$target_gen_dir/manifest/${target_name}/AndroidManifest_split.xml"
    _is_trichrome =
        defined(invoker.use_trichrome_library) && invoker.use_trichrome_library
    _is_bundle = invoker.target_type == "android_app_bundle_module"

    # Generate the manifest here in the template, to avoid a growing collection
    # of manually-instantiated manifests.
    split_manifest_template("${target_name}__android_manifest") {
      definitions_in_split = _is_bundle
      includes = [ "java/AndroidManifest.xml" ]
      split_input = "java/AndroidManifest_split.xml"
      split_output = _split_android_manifest
      variables = chrome_public_jinja_variables +
                  [ "include_arcore_manifest_flag=$enable_arcore" ]
      if (_is_trichrome) {
        input = "java/AndroidManifest_trichrome_chrome.xml"
        variables += trichrome_jinja_variables

        if (android_64bit_target_cpu) {
          if (invoker.is_64_bit_browser) {
            if (invoker.include_32_bit_webview) {
              if (_use_stable_package_name_for_trichrome) {
                _version_code = trichrome_64_32_beta_version_code
              } else {
                _version_code = trichrome_64_32_version_code
              }
            } else {
              if (_use_stable_package_name_for_trichrome) {
                _version_code = trichrome_64_beta_version_code
              } else {
                _version_code = trichrome_64_version_code
              }
            }
          } else {
            if (invoker.include_64_bit_webview) {
              if (_use_stable_package_name_for_trichrome) {
                _version_code = trichrome_32_64_beta_version_code
              } else {
                _version_code = trichrome_32_64_version_code
              }
            } else {
              if (_use_stable_package_name_for_trichrome) {
                _version_code = trichrome_32_beta_version_code
              } else {
                _version_code = trichrome_32_version_code
              }
            }
          }
        } else {
          if (_use_stable_package_name_for_trichrome) {
            _version_code = trichrome_beta_version_code
          } else {
            _version_code = trichrome_version_code
          }
        }
        variables += [ "trichrome_version=$_version_code" ]
      } else {
        _arch = ""
        if (android_64bit_target_cpu && invoker.is_64_bit_browser) {
          _arch = "_64"
        }
        input = "java/AndroidManifest_monochrome.xml"
        includes += [ "//android_webview/nonembedded/java/AndroidManifest.xml" ]
        variables += monochrome_android_manifest_jinja_variables + [
                       "target_sdk_version=$android_sdk_version",
                       "webview_library=libmonochrome${_arch}.so",
                     ]
      }

      # 32-bit is the common case, so remove the ABI variable in the 64-case.
      if (android_64bit_target_cpu && invoker.is_64_bit_browser) {
        variables -= [ use_32bit_abi_jinja_variable ]
      }

      output = _android_manifest
    }

    monochrome_public_common_apk_or_module_tmpl(target_name) {
      forward_variables_from(invoker,
                             [
                               "add_view_trace_events",
                               "apk_name",
                               "bundle_target",
                               "expected_android_manifest",
                               "include_32_bit_webview",
                               "include_64_bit_webview",
                               "is_64_bit_browser",
                               "is_base_module",
                               "resource_ids_provider_dep",
                               "static_library_provider",
                               "static_library_synchronized_proguard",
                               "target_type",
                               "use_chromium_linker",
                               "use_modern_linker",
                               "use_trichrome_library",
                               "version_code",
                               "version_name",
                             ])
      android_manifest = _android_manifest
      android_manifest_dep = ":${target_name}__android_manifest"

      if (!_is_trichrome) {
        # Resource allowlist used when generating R.java files and causes
        # only the webview subset of resources to be marked as non-final.
        # Strings in this target will also be kept in the base apk rather than placed in the language splits.
        shared_resources_allowlist_target =
            "//android_webview:system_webview_no_weblayer_apk"

        # Ensure the localized resources for all locales are used, even when
        # a smaller set is specified through aapt_locale_allowlist.
        shared_resources_allowlist_locales = platform_pak_locales
      }

      deps = []
      if (_is_bundle) {
        deps += [
          "//chrome/android:chrome_base_module_resources",

          # deps in delegate_public_impl_java are put into the Chrome module, but the language deps
          # are needed by the base module.
          "//components/language/android:ulp_delegate_public_java",
        ]
      } else {
        deps += [ ":delegate_public_impl_java" ]
      }
      if (!_is_trichrome) {
        deps += [
          "//android_webview:platform_service_bridge_upstream_implementation_java",
          "//android_webview/nonembedded:icon_resources",
          "//android_webview/nonembedded:monochrome_devui_launcher_icon_resources",
        ]
        if (!_is_bundle) {
          deps += [ ":monochrome_java" ]
        }
        if (webview_includes_weblayer) {
          deps += [ "//weblayer/browser/java:upstream_java" ]
        }
      }
    }
  }

  if (android_64bit_target_cpu && skip_secondary_abi_for_cq) {
    group("trichrome_library_apk") {
      deps = [ ":trichrome_library_64_apk" ]
    }
    group("monochrome_public_apk") {
      deps = [ ":monochrome_64_public_apk" ]
    }
  } else {
    monochrome_public_apk_or_module_tmpl("monochrome_public_apk") {
      version_code = monochrome_version_code
      version_name = chrome_version_name
      apk_name = "MonochromePublic"
      target_type = "android_apk"
      if (android_64bit_target_cpu) {
        is_64_bit_browser = false
        include_64_bit_webview = true
      }
    }

    trichrome_library_apk_tmpl("trichrome_library_apk") {
      apk_name = "TrichromeLibrary"
      android_manifest = trichrome_library_android_manifest
      android_manifest_dep = ":trichrome_library_android_manifest"

      if (android_64bit_target_cpu) {
        is_64_bit_browser = false
        include_64_bit_webview = true
      }

      if (trichrome_synchronized_proguard) {
        webview_target = "//android_webview:trichrome_webview_apk"
        chrome_target = ":trichrome_chrome_bundle"
      }

      if (_enable_manifest_verification) {
        expected_android_manifest =
            "expectations/trichrome_library_apk.AndroidManifest.expected"
      }
      if (_enable_libs_and_assets_verification) {
        expected_libs_and_assets = "expectations/trichrome_library_apk.$target_cpu.libs_and_assets.expected"
      }
    }

    # Can be used to install compressed apks on system images.
    system_image_stub_apk("trichrome_library_system_stub_apk") {
      package_name = chrome_public_manifest_package
      stub_output = "$root_out_dir/apks/TrichromeLibrary-Stub.apk"
    }
  }

  if (android_64bit_target_cpu) {
    monochrome_public_apk_or_module_tmpl("monochrome_64_public_apk") {
      version_code = monochrome_version_code
      version_name = chrome_version_name
      apk_name = "MonochromePublic64"
      target_type = "android_apk"
      is_64_bit_browser = true
      include_32_bit_webview = false
    }

    trichrome_library_apk_tmpl("trichrome_library_64_apk") {
      apk_name = "TrichromeLibrary64"
      android_manifest = trichrome_library_64_android_manifest
      android_manifest_dep = ":trichrome_library_64_android_manifest"
      is_64_bit_browser = true
      include_32_bit_webview = false
    }

    if (!skip_secondary_abi_for_cq) {
      trichrome_library_apk_tmpl("trichrome_library_32_apk") {
        apk_name = "TrichromeLibrary32"
        android_manifest = trichrome_library_32_android_manifest
        android_manifest_dep = ":trichrome_library_32_android_manifest"
        is_64_bit_browser = false
        include_64_bit_webview = false
      }

      trichrome_library_apk_tmpl("trichrome_library_64_32_apk") {
        apk_name = "TrichromeLibrary6432"
        android_manifest = trichrome_library_64_32_android_manifest
        android_manifest_dep = ":trichrome_library_64_32_android_manifest"
        is_64_bit_browser = true
        include_32_bit_webview = true
      }
    }
  }

  chrome_public_unit_test_apk_manifest =
      "$root_gen_dir/chrome_public_unit_test_apk_manifest/AndroidManifest.xml"
  chrome_public_test_apk_manifest =
      "$root_gen_dir/chrome_public_test_apk_manifest/AndroidManifest.xml"
  chrome_public_test_vr_apk_manifest =
      "$root_gen_dir/chrome_public_test_vr_apk_manifest/AndroidManifest.xml"
  monochrome_public_test_ar_apk_manifest =
      "$root_gen_dir/monochrome_public_test_ar_apk_manifest/AndroidManifest.xml"

  jinja_template("chrome_public_unit_test_apk_manifest") {
    input = "javatests/AndroidManifest.xml"
    includes = [ "java/AndroidManifest.xml" ]
    output = chrome_public_unit_test_apk_manifest
    variables = default_chrome_public_jinja_variables
    variables += [
      "manifest_package=$chrome_public_test_manifest_package",
      "min_sdk_version=$default_min_sdk_version",
      "target_sdk_version=$android_sdk_version",
    ]
  }

  jinja_template("chrome_public_test_apk_manifest") {
    input = "javatests/AndroidManifest.xml"
    includes = [ "java/AndroidManifest.xml" ]
    output = chrome_public_test_apk_manifest
    variables = default_chrome_public_jinja_variables
    variables += [
      "manifest_package=$chrome_public_test_manifest_package",
      "min_sdk_version=$default_min_sdk_version",
      "target_sdk_version=$android_sdk_version",
    ]
  }

  jinja_template("chrome_public_test_vr_apk_manifest") {
    input = "javatests/AndroidManifest.xml"
    includes = [ "java/AndroidManifest.xml" ]
    output = chrome_public_test_vr_apk_manifest
    variables = chrome_public_jinja_variables
    variables += [
      "min_sdk_version=$default_min_sdk_version",
      "target_sdk_version=$android_sdk_version",
    ]
  }

  template("monochrome_public_test_ar_apk_manifest_tmpl") {
    jinja_template(target_name) {
      forward_variables_from(invoker, "*")
      input = "javatests/AndroidManifest_monochrome.xml"
      includes = [
        "java/AndroidManifest.xml",
        "java/AndroidManifest_monochrome.xml",
        "javatests/AndroidManifest.xml",
        "//android_webview/nonembedded/java/AndroidManifest.xml",
      ]
      variables =
          chrome_public_jinja_variables +
          monochrome_android_manifest_jinja_variables +
          [
            "target_sdk_version=$android_sdk_version",
            "test_manifest_package=$chrome_public_test_manifest_package",
            "webview_library=$webview_library_name",
            "include_arcore_manifest_flag=$enable_arcore",
          ]
    }
  }

  monochrome_public_test_ar_apk_manifest_tmpl(
      "monochrome_public_test_ar_apk_manifest") {
    output = monochrome_public_test_ar_apk_manifest
    webview_library_name = "libmonochrome.so"
  }

  if (android_64bit_target_cpu && skip_secondary_abi_for_cq) {
    monochrome_public_test_ar_64_apk_manifest = "$root_gen_dir/monochrome_public_test_ar_64_apk_manifest/AndroidManifest.xml"
    monochrome_public_test_ar_apk_manifest_tmpl(
        "monochrome_public_test_ar_64_apk_manifest") {
      output = monochrome_public_test_ar_64_apk_manifest
      webview_library_name = "libmonochrome_64.so"
    }
  }

  template("chrome_test_apk_tmpl") {
    chrome_public_common_apk_or_module_tmpl(target_name) {
      forward_variables_from(invoker,
                             [
                               "apk_name",
                               "android_manifest",
                               "android_manifest_dep",
                               "data_deps",
                               "enable_lint",
                               "enforce_resource_overlays_in_tests",
                               "extra_args",
                               "is_unit_test",
                               "shared_libraries",
                             ])

      testonly = true
      target_type = "instrumentation_test_apk"
      bundles_supported = true

      deps = _chrome_public_shared_deps + invoker.deps + [
               ":chrome_apk_pak_assets",
               ":chrome_public_base_module_java_for_test",
               "//third_party/android_sdk:android_test_base_java",
               "//third_party/android_sdk:android_test_mock_java",
               "//third_party/android_sdk:android_test_runner_java",
               "//third_party/android_support_test_runner:runner_java",
             ]
      if (add_unwind_tables_in_chrome_32bit_apk && current_cpu == "arm") {
        deps += [ ":libchromefortest_unwind_table_assets" ]
      }
      if (enable_vr) {
        # Contains VrFirstRunActivity, which is referenced by AndroidManifest.xml.
        deps += [ "//chrome/android/features/vr:java" ]
      }

      additional_apks = [ "//net/android:net_test_support_apk" ]
      if (defined(invoker.additional_apks)) {
        additional_apks += invoker.additional_apks
      }
      if (!is_java_debug) {
        proguard_enabled = true
        proguard_configs = [ "//chrome/android/proguard/apk_for_test.flags" ]
      }

      # TODO(crbug.com/993340): Update test goldens with webp versions of images.
      png_to_webp = false
    }
  }

  template("monochrome_test_apk_tmpl") {
    monochrome_public_common_apk_or_module_tmpl(target_name) {
      forward_variables_from(invoker,
                             [
                               "android_manifest",
                               "android_manifest_dep",
                               "apk_name",
                               "data_deps",
                               "is_64_bit_browser",
                               "include_64_bit_webview",
                               "include_32_bit_webview",
                               "loadable_modules",
                               "min_sdk_version",
                               "proguard_configs",
                               "secondary_abi_loadable_modules",
                               "shared_libraries",
                               "target_sdk_version",
                             ])

      testonly = true
      target_type = "instrumentation_test_apk"

      deps = _chrome_public_shared_deps + invoker.deps + [
               ":chrome_public_base_module_java_for_test",
               ":monochrome_apk_pak_assets",
               "//android_webview:platform_service_bridge_upstream_implementation_java",
               "//third_party/android_sdk:android_test_base_java",
               "//third_party/android_sdk:android_test_mock_java",
               "//third_party/android_sdk:android_test_runner_java",
               "//third_party/android_support_test_runner:runner_java",
             ]

      if (webview_includes_weblayer) {
        deps += [ "//weblayer/browser/java:upstream_java" ]
      }

      additional_apks = [ "//net/android:net_test_support_apk" ]
      if (defined(invoker.additional_apks)) {
        additional_apks += invoker.additional_apks
      }
      if (!is_java_debug) {
        if (!defined(proguard_configs)) {
          proguard_configs = []
        }
        proguard_enabled = true
        proguard_configs += [ "//chrome/android/proguard/apk_for_test.flags" ]
      }
    }
  }

  # As compared to chrome_public_test_apk, this target contains only unit tests
  # that require on device capabilities. These tests are smaller, more tightly
  # scoped, and do not leave lingering state after execution.
  # TODO(crbug.com/1238057): Set clear rules for what tests can be added here.
  chrome_test_apk_tmpl("chrome_public_unit_test_apk") {
    apk_name = "ChromePublicUnitTest"
    android_manifest = chrome_public_unit_test_apk_manifest
    android_manifest_dep = ":chrome_public_unit_test_apk_manifest"
    shared_libraries = [ ":libchromefortest" ]
    deps = [
      ":chrome_unit_test_java",
      "//build/config/android/test/resource_overlay:unit_device_javatests",
      "//chrome/android/features/keyboard_accessory:unit_device_javatests",
      "//chrome/android/features/tab_ui:unit_device_javatests",
      "//chrome/browser/back_press/android:unit_device_javatests",
      "//chrome/browser/content_creation/notes/internal/android:unit_device_javatests",
      "//chrome/browser/download/internal/android:unit_device_javatests",
      "//chrome/browser/image_descriptions:unit_device_javatests",
      "//chrome/browser/loading_modal/android:unit_device_javatests",
      "//chrome/browser/optimization_guide/android:unit_device_javatests",
      "//chrome/browser/partnercustomizations:unit_device_javatests",
      "//chrome/browser/password_edit_dialog/android:unit_device_javatests",
      "//chrome/browser/signin/services/android:unit_device_javatests",
      "//chrome/browser/thumbnail/generator:unit_device_javatests",
      "//chrome/browser/ui/android/appmenu/internal:unit_device_javatests",
      "//chrome/browser/ui/android/night_mode:unit_device_javatests",
      "//chrome/browser/ui/android/omnibox:unit_device_javatests",
      "//chrome/browser/ui/android/searchactivityutils:unit_device_javatests",
      "//chrome/browser/ui/android/signin:unit_device_javatests",
      "//chrome/browser/ui/messages/android:unit_device_javatests",
      "//chrome/browser/user_education:unit_device_javatests",
      "//chrome/browser/video_tutorials/internal:unit_device_javatests",
      "//components/browser_ui/bottomsheet/android/internal:unit_device_javatests",
      "//components/browser_ui/contacts_picker/android:unit_device_javatests",
      "//components/browser_ui/modaldialog/android:unit_device_javatests",
      "//components/browser_ui/photo_picker/android:unit_device_javatests",
      "//components/browser_ui/settings/android:unit_device_javatests",
      "//components/browser_ui/util/android:unit_device_javatests",
      "//components/browser_ui/widget/android:unit_device_javatests",
      "//components/embedder_support/android:embedder_support_javatests",
      "//components/external_intents/android:unit_device_javatests",
      "//components/infobars/android:unit_device_javatests",
      "//components/installedapp/android:unit_device_javatests",
      "//components/messages/android/internal:unit_device_javatests",
      "//components/payments/content/android:unit_device_javatests",
      "//components/signin/public/android:unit_device_javatests",
      "//components/strictmode/android:unit_device_javatests",
      "//components/url_formatter/android:unit_device_javatests",
      "//ui/android:ui_unit_device_javatests",
    ]

    data_deps = [
      "//testing/buildbot/filters:chrome_public_unit_test_apk_filters",
      "//ui/base:goldctl",
    ]

    # Required by //build/config/android/test/resource_overlay:javatests
    enforce_resource_overlays_in_tests = true

    # Causes tests that try to start the browser process to fail
    is_unit_test = true
  }

  # TODO(crbug.com/1238057): Rename to chrome_public_integration_test_apk
  chrome_test_apk_tmpl("chrome_public_test_apk") {
    # TODO(wnwen): Re-enable when new lint failures are disabled for test targets.
    #enable_lint = true

    apk_name = "ChromePublicTest"
    android_manifest = chrome_public_test_apk_manifest
    android_manifest_dep = ":chrome_public_test_apk_manifest"
    shared_libraries = [ ":libchromefortest" ]
    deps = [
      ":chrome_test_apk_template_resources",
      ":chrome_test_java",
      "//chrome/android/features/autofill_assistant:test_java",
      "//chrome/android/features/keyboard_accessory:test_java",
      "//chrome/browser/banners/android:javatests",
      "//chrome/browser/download/internal/android:javatests",
      "//chrome/browser/engagement/android:javatests",
      "//chrome/browser/flags:javatests",
      "//chrome/browser/password_check/android:test_java",
      "//chrome/browser/subresource_filter:subresource_filter_javatests",
      "//chrome/browser/touch_to_fill/android:test_java",
      "//chrome/browser/ui/android/fast_checkout/internal:javatests",
      "//chrome/browser/ui/android/omnibox:javatests",
      "//chrome/browser/ui/android/webid/internal:javatests",
    ]

    data_deps = [ "//testing/buildbot/filters:chrome_public_test_apk_filters" ]

    if (enable_chrome_android_internal) {
      data_deps +=
          [ "//clank/build/bot/filters:chrome_public_test_apk_filters" ]
    }

    additional_apks = [
      "//chrome/android/webapk/shell_apk:javatests_webapk",
      "//chrome/test/android/chrome_public_test_support:chrome_public_test_support_apk",
      "//components/media_router/test/android/media_router_test_support:media_router_test_support_apk",
    ]

    # TrustedWebActivityClientTest relies on sending a browsable Intent to an
    # ActivityWithDeepLink. On Android S+, for an app to receive a browsable
    # Intent it must have Digital Asset Link verification set up. This command
    # will associate the given package with the given website.
    extra_args = [
      "--approve-app-links",
      "org.chromium.chrome.tests.support:www.example.com",
    ]
  }

  if (enable_vr) {
    chrome_test_apk_tmpl("chrome_public_test_vr_apk") {
      apk_name = "ChromePublicTestVr"
      android_manifest = chrome_public_test_vr_apk_manifest
      android_manifest_dep = ":chrome_public_test_vr_apk_manifest"
      shared_libraries = [ ":libchromefortest" ]

      deps = [
        ":chrome_test_vr_java",
        ":vr_test_module_desc_java",
        "//third_party/android_sdk:android_test_mock_java",
      ]
    }

    # This is necessary since vr tests simulate a bundle build.
    module_desc_java("vr_test_module_desc_java") {
      module_name = "vr"
      load_native_on_get_impl = false
    }
  }

  if (enable_arcore) {
    monochrome_test_apk_tmpl("monochrome_public_test_ar_apk") {
      apk_name = "MonochromePublicTestAr"
      min_sdk_version = 24
      target_sdk_version = android_sdk_version

      android_manifest = monochrome_public_test_ar_apk_manifest
      android_manifest_dep = ":monochrome_public_test_ar_apk_manifest"
      if (android_64bit_target_cpu) {
        if (skip_secondary_abi_for_cq) {
          android_manifest = monochrome_public_test_ar_64_apk_manifest
          android_manifest_dep = ":monochrome_public_test_ar_64_apk_manifest"
          is_64_bit_browser = true
          include_32_bit_webview = false
        } else {
          is_64_bit_browser = false
          include_64_bit_webview = true
        }
      }

      # This is where we would add the shared_libraries entry for
      # :libchromefortest in the non-Monochrome version. However, doing so in the
      # Monochrome version causes Chrome to crash on startup due to being unable
      # to load the library, and looking at the libraries included in the APK
      # shows both libchromefortest and libmonochrome, when only one should be
      # present. The tests currently work fine with just libmonochrome, so keep
      # it this way until we actually need the test-only library. This may be
      # related to monochrome_public_common_apk_or_module_tmpl adding its own
      # shared libraries, but chrome_public_common_apk_or_module_tmpl not. See
      # https://crbug.com/974017.
      deps = [
        ":chrome_test_ar_java",
        "//third_party/android_sdk:android_test_mock_java",
      ]

      # Include ArCore files directly instead of using bundles.
      deps += [
        "//components/webxr/android:ar_java",
        "//third_party/arcore-android-sdk-client:com_google_ar_core_java",
        "//third_party/arcore-android-sdk-client:com_google_ar_core_java__ignored_manifest",
      ]

      _libarcore_dir = get_label_info(
                           "//third_party/arcore-android-sdk-client:com_google_ar_core_java($default_toolchain)",
                           "target_out_dir") + "/com_google_ar_core_java/jni"

      # We store this as a separate .so in the APK and only load as needed.
      if (android_64bit_target_cpu) {
        if (skip_secondary_abi_for_cq) {
          loadable_modules = [ "$_libarcore_dir/arm64-v8a/libarcore_sdk_c.so" ]
        } else {
          secondary_abi_loadable_modules =
              [ "$_libarcore_dir/armeabi-v7a/libarcore_sdk_c.so" ]
        }
      } else {
        loadable_modules = [ "$_libarcore_dir/armeabi-v7a/libarcore_sdk_c.so" ]
      }

      additional_apks = [ "//net/android:net_test_support_apk" ]
    }
  }

  # Chrome smoke test is a minimal test to ensure Chrome is not DOA.  It is
  # designed to be runnable against uninstrumented Chrome apks.
  android_test_apk("chrome_smoke_test_apk") {
    apk_name = "ChromeSmokeTest"
    android_manifest =
        "javatests/src/org/chromium/chrome/test/smoke/AndroidManifest.xml"
    target_sdk_version = android_sdk_version
    testonly = true
    sources =
        [ "javatests/src/org/chromium/chrome/test/smoke/ChromeSmokeTest.java" ]
    deps = [
      "//base:base_java_test_support",
      "//chrome/test/android:chrome_java_test_pagecontroller",
      "//content/public/test/android:content_java_test_support",
      "//third_party/android_support_test_runner:runner_java",
      "//third_party/androidx:androidx_test_runner_java",
      "//third_party/androidx:androidx_test_uiautomator_uiautomator_java",
      "//third_party/hamcrest:hamcrest_library_java",
      "//third_party/junit",
    ]
  }

  _common_smoke_test_args = [
    "--enable-breakpad-dump",
    "--use-apk-under-test-flags-file",

    # FRE is too flakey on some old devices https://crbug.com/1289733
    "--disable-fre",
  ]

  instrumentation_test_runner("chrome_public_smoke_test") {
    apk_under_test = ":chrome_public_apk"
    android_test_apk = ":chrome_smoke_test_apk"
    if (!is_java_debug) {
      proguard_mapping_path = "$root_build_dir/apks/ChromePublic.apk.mapping"
    }

    extra_args = _common_smoke_test_args
  }

  # Public webview targets don't work with non-public sdks.
  # https://crbug.com/1000763
  instrumentation_test_runner("monochrome_public_smoke_test") {
    if (android_64bit_target_cpu && skip_secondary_abi_for_cq) {
      apk_under_test = ":monochrome_64_public_apk"
      if (!is_java_debug) {
        proguard_mapping_path =
            "$root_build_dir/apks/MonochromePublic64.apk.mapping"
      }
    } else {
      apk_under_test = ":monochrome_public_apk"
      if (!is_java_debug) {
        proguard_mapping_path =
            "$root_build_dir/apks/MonochromePublic.apk.mapping"
      }
    }
    android_test_apk = ":chrome_smoke_test_apk"
    never_incremental = true
    extra_args = _common_smoke_test_args
  }

  android_test_apk("chrome_bundle_smoke_test_apk") {
    apk_name = "ChromeBundleSmokeTest"
    android_manifest = "javatests/src/org/chromium/chrome/test/smoke/AndroidManifest_bundle.xml"
    target_sdk_version = android_sdk_version
    testonly = true
    sources = [
      "javatests/src/org/chromium/chrome/test/smoke/ChromeBundleSmokeTest.java",
    ]

    # Used as test_apk for bundle smoke tests, which are also never_incremental.
    # The test_runner_script target does not support incremental test_apk when
    # the parent target is never_incremental.
    never_incremental = true
    deps = [
      "//base:base_java_test_support",
      "//chrome/test/android:chrome_java_test_pagecontroller",
      "//content/public/test/android:content_java_test_support",
      "//third_party/android_support_test_runner:runner_java",
      "//third_party/androidx:androidx_test_runner_java",
      "//third_party/junit",
    ]
  }

  _bundle_smoke_test_extra_args = [
    # Make extra args be passed through to the bundle under test (see below).
    "--use-apk-under-test-flags-file",

    # Enable breakpad dumps in order to detect Chrome crashes (because the
    # test intentionally does not set <instrumentation android:targetPackage> to
    # the bundle under test.)
    "--enable-breakpad-dump",

    # Chrome crashes at startup if strict mode is turned on.
    "--strict-mode=off",

    # These args are passed through to the bundle under test.
    "--enable-test-dummy-module",
    "--disable-fre",
  ]

  instrumentation_test_runner("chrome_modern_public_bundle_smoke_test") {
    apk_under_test = ":chrome_modern_public_bundle_apks"
    android_test_apk = ":chrome_bundle_smoke_test_apk"
    never_incremental = true
    modules = [ "test_dummy" ]
    if (!is_java_debug) {
      proguard_mapping_path =
          "$root_build_dir/apks/ChromeModernPublic.aab.mapping"
    }
    extra_args = _bundle_smoke_test_extra_args
  }

  instrumentation_test_runner("monochrome_public_bundle_smoke_test") {
    if (android_64bit_target_cpu && skip_secondary_abi_for_cq) {
      apk_under_test = "//chrome/android:monochrome_64_public_bundle_apks"
      if (!is_java_debug) {
        proguard_mapping_path =
            "$root_build_dir/apks/MonochromePublic64.aab.mapping"
      }
    } else {
      apk_under_test = "//chrome/android:monochrome_public_bundle_apks"
      if (!is_java_debug) {
        proguard_mapping_path =
            "$root_build_dir/apks/MonochromePublic.aab.mapping"
      }
    }
    android_test_apk = ":chrome_bundle_smoke_test_apk"
    never_incremental = true
    modules = [ "test_dummy" ]
    extra_args = _bundle_smoke_test_extra_args
  }

  if (defined(expected_static_initializer_count)) {
    action_with_pydeps("monochrome_static_initializers") {
      script = "//build/android/gyp/assert_static_initializers.py"
      if (android_64bit_target_cpu) {
        deps = [ ":monochrome_64_public_bundle" ]
        inputs = [ "$root_build_dir/apks/MonochromePublic64.aab" ]
      } else {
        deps = [ ":monochrome_public_bundle" ]
        inputs = [ "$root_build_dir/apks/MonochromePublic.aab" ]
      }
      outputs = [ "$target_gen_dir/$target_name.stamp" ]
      args = [
        "--expected-count=$expected_static_initializer_count",
        "--tool-prefix",
        rebase_path(android_tool_prefix, root_build_dir),
        "--touch",
        rebase_path(outputs[0], root_build_dir),
        rebase_path(inputs[0], root_build_dir),
      ]
    }
  }

  instrumentation_test_runner("trichrome_chrome_bundle_smoke_test") {
    android_test_apk = ":chrome_bundle_smoke_test_apk"
    never_incremental = true
    modules = [ "test_dummy" ]
    extra_args = _bundle_smoke_test_extra_args
    if (android_64bit_target_cpu && skip_secondary_abi_for_cq) {
      apk_under_test = "//chrome/android:trichrome_chrome_64_bundle_apks"
      additional_apks = [ "//chrome/android:trichrome_library_64_apk" ]
      if (!is_java_debug) {
        proguard_mapping_path =
            "$root_build_dir/apks/TrichromeChrome64.aab.mapping"
      }
    } else {
      apk_under_test = "//chrome/android:trichrome_chrome_bundle_apks"
      additional_apks = [ "//chrome/android:trichrome_library_apk" ]
      if (!is_java_debug) {
        proguard_mapping_path =
            "$root_build_dir/apks/TrichromeChrome.aab.mapping"
      }
    }
  }

  group("chrome_nocompile_tests") {
    # Tests which check that build errors are thrown when expected and that build
    # validation tools (ex: lint) do not get silently disabled.
    testonly = true

    # No-compile tests use an output directory dedicated to no-compile tests.
    # Put new test suites in //build/android/test/nocompile_gn if possible in
    # order to share the target output directory and avoid running 'gn gen'
    # for each android_nocompile_test_suite().
    deps = [
      "features/android_library_factory/test:android_lookup_dep_tests",
      "//build/android/test:android_nocompile_tests",
      "//tools/android/errorprone_plugin/test:errorprone_plugin_tests",
    ]
  }

  script_test("monochrome_webview_finch_smoke_tests") {
    script = "//testing/scripts/run_finch_smoke_tests_android.py"
    data_deps = [
      "//android_webview/tools/system_webview_shell:system_webview_shell_apk",
      "//chrome/android:monochrome_public_apk",
      "//third_party/blink/tools:wpt_tests_android_isolate",
    ]
  }

  script_test("chrome_public_wpt") {
    script = "//third_party/blink/tools/run_wpt_tests.py"
    args = [
      "--product",
      "chrome_android",
      "--browser-apk",
      "@WrappedPath(apks/ChromePublic.apk)",
      "-v",
    ]
    data_deps = [
      ":chrome_public_apk",
      "//chrome/test/chromedriver:chromedriver($host_toolchain)",
      "//third_party/blink/tools:wpt_tests_android_isolate",
    ]
  }

  chrome_bundle("chrome_modern_public_bundle") {
    base_module_target = ":chrome_modern_public_base_bundle_module"
    bundle_name = "ChromeModernPublic"
    compress_shared_libraries = true
    if (android_64bit_target_cpu) {
      is_64_bit_browser = true
      include_32_bit_webview = false
    }
    is_monochrome_or_trichrome = false
    add_view_trace_events = true
    manifest_package = chrome_public_manifest_package
    min_sdk_version = default_min_sdk_version
    module_descs = chrome_modern_module_descs
    version_code = chrome_modern_version_code

    if (_enable_libs_and_assets_verification) {
      expected_libs_and_assets = "expectations/chrome_modern_public_bundle.$target_cpu.libs_and_assets.expected"
    }
  }

  if (is_official_build) {
    # Used for binary size monitoring.
    create_app_bundle_minimal_apks("chrome_modern_public_minimal_apks") {
      deps = [ ":chrome_modern_public_bundle" ]
      bundle_path = "$root_build_dir/apks/ChromeModernPublic.aab"
    }

    android_resource_sizes_test(
        "resource_sizes_chrome_modern_public_minimal_apks") {
      file_path = "$root_build_dir/apks/ChromeModernPublic.minimal.apks"
      data_deps = [ ":chrome_modern_public_minimal_apks" ]
    }
  }

  template("monochrome_or_trichrome_public_bundle_tmpl") {
    _base_module_target_name = "${invoker.target_name}__base_bundle_module"
    _is_trichrome =
        defined(invoker.use_trichrome_library) && invoker.use_trichrome_library

    # Find the correct version code.
    if (_is_trichrome) {
      if (android_64bit_target_cpu) {
        if (invoker.is_64_bit_browser) {
          if (invoker.include_32_bit_webview) {
            if (_use_stable_package_name_for_trichrome) {
              _version_code = trichrome_64_32_beta_version_code
            } else {
              _version_code = trichrome_64_32_version_code
            }
          } else {
            if (_use_stable_package_name_for_trichrome) {
              _version_code = trichrome_64_beta_version_code
            } else {
              _version_code = trichrome_64_version_code
            }
          }
        } else {
          if (invoker.include_64_bit_webview) {
            if (_use_stable_package_name_for_trichrome) {
              _version_code = trichrome_32_64_beta_version_code
            } else {
              _version_code = trichrome_32_64_version_code
            }
          } else {
            if (_use_stable_package_name_for_trichrome) {
              _version_code = trichrome_32_beta_version_code
            } else {
              _version_code = trichrome_32_version_code
            }
          }
        }
      } else {
        if (_use_stable_package_name_for_trichrome) {
          _version_code = trichrome_beta_version_code
        } else {
          _version_code = trichrome_version_code
        }
      }
    } else {
      if (android_64bit_target_cpu) {
        if (invoker.is_64_bit_browser) {
          if (invoker.include_32_bit_webview) {
            _version_code = monochrome_64_32_version_code
          } else {
            _version_code = monochrome_64_version_code
          }
        } else {
          if (invoker.include_64_bit_webview) {
            _version_code = monochrome_32_64_version_code
          } else {
            _version_code = monochrome_32_version_code
          }
        }
      } else {
        _version_code = monochrome_version_code
      }
    }

    if (_is_trichrome) {
      _bundle_name = "TrichromeChrome${invoker.bundle_suffix}"
      _min_sdk_version = 29
      _module_descs = trichrome_module_descs
    } else {
      _bundle_name = "MonochromePublic${invoker.bundle_suffix}"
      _min_sdk_version = 24
      _module_descs = monochrome_module_descs
    }

    monochrome_public_apk_or_module_tmpl(_base_module_target_name) {
      forward_variables_from(invoker,
                             [
                               "add_view_trace_events",
                               "expected_android_manifest",
                               "is_64_bit_browser",
                               "include_32_bit_webview",
                               "include_64_bit_webview",
                               "static_library_provider",
                               "static_library_synchronized_proguard",
                               "resource_ids_provider_dep",
                               "use_trichrome_library",
                             ])
      target_type = "android_app_bundle_module"
      is_base_module = true
      version_code = _version_code

      if (defined(invoker.expected_android_manifest_template)) {
        expected_android_manifest =
            string_replace(invoker.expected_android_manifest_template,
                           "SPLIT_NAME",
                           "base")
      }

      if (!_is_trichrome ||
          !defined(invoker.static_library_synchronized_proguard) ||
          !invoker.static_library_synchronized_proguard) {
        bundle_target = ":${invoker.target_name}"
      }
    }

    chrome_bundle(target_name) {
      forward_variables_from(invoker,
                             [
                               "add_view_trace_events",
                               "enable_lint",
                               "include_32_bit_webview",
                               "include_64_bit_webview",
                               "is_64_bit_browser",
                               "lint_baseline_file",
                               "lint_min_sdk_version",
                               "lint_suppressions_file",
                               "static_library_provider",
                               "static_library_synchronized_proguard",
                               "expected_android_manifest_template",
                               "expected_libs_and_assets",
                               "expected_proguard_config",
                             ])
      base_module_target = ":$_base_module_target_name"
      bundle_name = _bundle_name
      is_monochrome_or_trichrome = true
      manifest_package = chrome_public_manifest_package
      min_sdk_version = _min_sdk_version
      module_descs = _module_descs
      version_code = _version_code
      chrome_deps = [ ":delegate_public_impl_java" ]
      if (!_is_trichrome) {
        chrome_deps += [ "//chrome/android:monochrome_java" ]
      }

      if (!is_java_debug) {
        proguard_android_sdk_dep = webview_framework_dep
      }
    }
  }

  group("android_lint") {
    if (android_64bit_target_cpu && skip_secondary_abi_for_cq) {
      assert(disable_android_lint)
    }
    if (!disable_android_lint) {
      deps = [ ":monochrome_public_bundle__lint" ]
      if (defined(additional_chrome_lint_targets)) {
        deps += additional_chrome_lint_targets
      }
    }
  }

  if (android_64bit_target_cpu && skip_secondary_abi_for_cq) {
    group("monochrome_public_bundle") {
      deps = [ ":monochrome_64_public_bundle" ]
    }
    group("trichrome_chrome_bundle") {
      deps = [ ":trichrome_chrome_64_bundle" ]
    }
  } else {
    # Public webview targets don't work with non-public sdks.
    # https://crbug.com/1000763
    monochrome_or_trichrome_public_bundle_tmpl("monochrome_public_bundle") {
      # Monochrome bundle is used as our unified lint target, so it needs to set the
      # lowest shipping minSdkVersion to catch all potential NewApi errors.
      lint_min_sdk_version = default_min_sdk_version
      enable_lint = true
      lint_baseline_file = "expectations/lint-baseline.xml"
      lint_suppressions_file = "expectations/lint-suppressions.xml"
      add_view_trace_events = true

      bundle_suffix = ""
      if (android_64bit_target_cpu) {
        is_64_bit_browser = false
        include_64_bit_webview = true
      }
      if (_enable_manifest_verification) {
        expected_android_manifest_template = "expectations/monochrome_public_bundle__SPLIT_NAME.AndroidManifest.expected"
        expected_proguard_config =
            "expectations/monochrome_public_bundle.proguard_flags.expected"
      }
      if (_enable_libs_and_assets_verification) {
        expected_libs_and_assets = "expectations/monochrome_public_bundle.$target_cpu.libs_and_assets.expected"
      }
    }

    if (is_official_build) {
      # Used for binary size monitoring.
      create_app_bundle_minimal_apks("monochrome_public_minimal_apks") {
        deps = [ ":monochrome_public_bundle" ]
        bundle_path = "$root_build_dir/apks/MonochromePublic.aab"
      }

      _minimal_apks_filename = "MonochromePublic.minimal.apks"
      android_resource_sizes_test(
          "resource_sizes_monochrome_public_minimal_apks") {
        file_path = "$root_build_dir/apks/${_minimal_apks_filename}"
        data_deps = [ ":monochrome_public_minimal_apks" ]
      }
    }

    monochrome_or_trichrome_public_bundle_tmpl("trichrome_chrome_bundle") {
      bundle_suffix = ""
      use_trichrome_library = true
      static_library_provider = ":trichrome_library_apk"
      add_view_trace_events = true
      if (!is_java_debug) {
        static_library_synchronized_proguard = trichrome_synchronized_proguard
        if (trichrome_synchronized_proguard) {
          resource_ids_provider_dep = "//android_webview:trichrome_webview_apk"
        }
      }
      if (android_64bit_target_cpu) {
        is_64_bit_browser = false
        include_64_bit_webview = true
      }

      if (_enable_manifest_verification) {
        # Monochrome verifies all bundle modules, so only check base module here.
        expected_android_manifest = "expectations/trichrome_chrome_bundle__base.AndroidManifest.expected"
      }
      if (_enable_libs_and_assets_verification) {
        expected_libs_and_assets = "expectations/trichrome_chrome_bundle.$target_cpu.libs_and_assets.expected"
      }
    }

    # Creates .zip of .apk splits suitable for the Android system image.
    system_image_apks("trichrome_chrome_system_zip") {
      apk_or_bundle_target = ":trichrome_chrome_bundle"
      input_apk_or_bundle = "$root_out_dir/apks/TrichromeChrome.aab"
      output = "$root_out_dir/apks/TrichromeChromeSystem.zip"
      stub_output = "$root_out_dir/apks/TrichromeChrome-Stub.apk"
    }

    # Combines all splits into a single .apk for the Android system image.
    system_image_apks("trichrome_chrome_system_apk") {
      apk_or_bundle_target = ":trichrome_chrome_bundle"
      input_apk_or_bundle = "$root_out_dir/apks/TrichromeChrome.aab"
      output = "$root_out_dir/apks/TrichromeChromeSystem.apk"
      fuse_apk = true
    }

    if (is_official_build) {
      _trichrome_library_basename = "TrichromeLibrary.apk"
      _trichrome_chrome_basename = "TrichromeChrome.minimal.apks"
      _trichrome_webview_basename = "TrichromeWebView.minimal.apks"
      _ssargs_filename = "Trichrome.ssargs"

      write_ssargs_trichrome("ssargs_trichrome") {
        ssargs_path = "$root_build_dir/apks/${_ssargs_filename}"
        trichrome_library_basename = _trichrome_library_basename
        trichrome_chrome_basename = _trichrome_chrome_basename
        trichrome_webview_basename = _trichrome_webview_basename
      }

      # Used for binary size monitoring.
      create_app_bundle_minimal_apks("trichrome_chrome_minimal_apks") {
        deps = [ ":trichrome_chrome_bundle" ]
        bundle_path = "$root_build_dir/apks/TrichromeChrome.aab"
      }

      group("trichrome_minimal_apks") {
        deps = [
          ":trichrome_chrome_minimal_apks",
          ":trichrome_library_apk",
          "//android_webview:trichrome_webview_minimal_apks",
        ]
      }

      android_resource_sizes_test("resource_sizes_trichrome") {
        apk_name = "Trichrome"
        trichrome_library_path =
            "$root_build_dir/apks/$_trichrome_library_basename"
        trichrome_chrome_path =
            "$root_build_dir/apks/$_trichrome_chrome_basename"
        trichrome_webview_path =
            "$root_build_dir/apks/$_trichrome_webview_basename"
        data_deps = [ ":trichrome_minimal_apks" ]
      }

      android_size_bot_config("resource_size_config_trichrome") {
        name = "Trichrome"
        mapping_files = [
          "apks/TrichromeChrome.aab.mapping",
          "apks/TrichromeWebView.aab.mapping",
        ]

        # Save mapping files since they are needed by:
        # 1) Checking for ForTesting methods.
        # 2) SuperSize adding disassembly to symbols.
        # Save apk and unstripped library because they are needed for collecting
        # disassembly of large symbols.
        archive_files = mapping_files + [
                          "apks/$_trichrome_library_basename",
                          "apks/$_trichrome_chrome_basename",
                          "apks/$_trichrome_webview_basename",
                          "lib.unstripped/libmonochrome__combined.so",
                        ]
        to_resource_sizes_py = {
          apk_name = "apks/resource_size_config_trichrome"
          trichrome_library = "apks/$_trichrome_library_basename"
          trichrome_chrome = "apks/$_trichrome_chrome_basename"
          trichrome_webview = "apks/$_trichrome_webview_basename"
        }
        supersize_input_file = "apks/$_ssargs_filename"
      }
    }
  }

  # Convenient target to build all Trichrome APK / bundles for testing.
  group("trichrome") {
    deps = [
      ":trichrome_chrome_bundle",
      ":trichrome_library_apk",
      "//android_webview:trichrome_webview_bundle",
    ]
  }

  if (android_64bit_target_cpu) {
    monochrome_or_trichrome_public_bundle_tmpl("monochrome_64_public_bundle") {
      bundle_suffix = "64"
      is_64_bit_browser = true
      include_32_bit_webview = false
    }

    monochrome_or_trichrome_public_bundle_tmpl("trichrome_chrome_64_bundle") {
      bundle_suffix = "64"
      is_64_bit_browser = true
      include_32_bit_webview = false
      use_trichrome_library = true
      static_library_provider = ":trichrome_library_64_apk"
    }

    if (!skip_secondary_abi_for_cq) {
      monochrome_or_trichrome_public_bundle_tmpl(
          "monochrome_32_public_bundle") {
        bundle_suffix = "32"
        is_64_bit_browser = false
        include_64_bit_webview = false
      }

      monochrome_or_trichrome_public_bundle_tmpl(
          "monochrome_64_32_public_bundle") {
        bundle_suffix = "6432"
        is_64_bit_browser = true
        include_32_bit_webview = true
      }

      monochrome_or_trichrome_public_bundle_tmpl("trichrome_chrome_32_bundle") {
        bundle_suffix = "32"
        is_64_bit_browser = false
        include_64_bit_webview = false
        use_trichrome_library = true
        static_library_provider = ":trichrome_library_32_apk"
      }
    }
  }
}

generate_jni("chrome_jni_headers") {
  sources = [
    # Files under a feature's public/ dir are included in chrome_java's source
    # files, so include these files in chrome_jni_headers.
    "java/src/org/chromium/chrome/browser/AppHooks.java",
    "java/src/org/chromium/chrome/browser/ApplicationLifetime.java",
    "java/src/org/chromium/chrome/browser/ChromeBackupAgentImpl.java",
    "java/src/org/chromium/chrome/browser/ChromeBackupWatcher.java",
    "java/src/org/chromium/chrome/browser/ChromePowerModeVoter.java",
    "java/src/org/chromium/chrome/browser/DevToolsServer.java",
    "java/src/org/chromium/chrome/browser/IntentHandler.java",
    "java/src/org/chromium/chrome/browser/NearOomMonitor.java",
    "java/src/org/chromium/chrome/browser/PlayServicesVersionInfo.java",
    "java/src/org/chromium/chrome/browser/ServiceTabLauncher.java",
    "java/src/org/chromium/chrome/browser/ShortcutHelper.java",
    "java/src/org/chromium/chrome/browser/WarmupManager.java",
    "java/src/org/chromium/chrome/browser/WebContentsFactory.java",
    "java/src/org/chromium/chrome/browser/about_settings/AboutSettingsBridge.java",
    "java/src/org/chromium/chrome/browser/announcement/AnnouncementNotificationManager.java",
    "java/src/org/chromium/chrome/browser/app/send_tab_to_self/SendTabToSelfNotificationReceiver.java",
    "java/src/org/chromium/chrome/browser/app/tab_activity_glue/ReparentingTask.java",
    "java/src/org/chromium/chrome/browser/app/video_tutorials/VideoTutorialsServiceUtils.java",
    "java/src/org/chromium/chrome/browser/autofill/AutofillExpirationDateFixFlowBridge.java",
    "java/src/org/chromium/chrome/browser/autofill/AutofillLogger.java",
    "java/src/org/chromium/chrome/browser/autofill/AutofillMessageConfirmFlowBridge.java",
    "java/src/org/chromium/chrome/browser/autofill/AutofillNameFixFlowBridge.java",
    "java/src/org/chromium/chrome/browser/autofill/AutofillPopupBridge.java",
    "java/src/org/chromium/chrome/browser/autofill/AutofillSnackbarController.java",
    "java/src/org/chromium/chrome/browser/autofill/CardUnmaskBridge.java",
    "java/src/org/chromium/chrome/browser/autofill/CreditCardScannerBridge.java",
    "java/src/org/chromium/chrome/browser/autofill/PersonalDataManager.java",
    "java/src/org/chromium/chrome/browser/autofill/PhoneNumberUtil.java",
    "java/src/org/chromium/chrome/browser/autofill/SaveUpdateAddressProfilePrompt.java",
    "java/src/org/chromium/chrome/browser/autofill/SaveUpdateAddressProfilePromptController.java",
    "java/src/org/chromium/chrome/browser/autofill/VirtualCardEnrollmentDelegate.java",
    "java/src/org/chromium/chrome/browser/autofill/VirtualCardEnrollmentDialogViewBridge.java",
    "java/src/org/chromium/chrome/browser/autofill/settings/AutofillPaymentMethodsDelegate.java",
    "java/src/org/chromium/chrome/browser/autofill/settings/AutofillProfileBridge.java",
    "java/src/org/chromium/chrome/browser/autofill/settings/VirtualCardEnrollmentFields.java",
    "java/src/org/chromium/chrome/browser/background_sync/BackgroundSyncBackgroundTask.java",
    "java/src/org/chromium/chrome/browser/background_sync/BackgroundSyncBackgroundTaskScheduler.java",
    "java/src/org/chromium/chrome/browser/background_sync/GooglePlayServicesChecker.java",
    "java/src/org/chromium/chrome/browser/background_sync/PeriodicBackgroundSyncChromeWakeUpTask.java",
    "java/src/org/chromium/chrome/browser/background_task_scheduler/ChromeBackgroundTaskFactory.java",
    "java/src/org/chromium/chrome/browser/background_task_scheduler/ProxyNativeTask.java",
    "java/src/org/chromium/chrome/browser/bookmarks/BookmarkBridge.java",
    "java/src/org/chromium/chrome/browser/browserservices/QualityEnforcer.java",
    "java/src/org/chromium/chrome/browser/browserservices/digitalgoods/SiteIsolator.java",
    "java/src/org/chromium/chrome/browser/browserservices/permissiondelegation/InstalledWebappBridge.java",
    "java/src/org/chromium/chrome/browser/browserservices/permissiondelegation/InstalledWebappGeolocationBridge.java",
    "java/src/org/chromium/chrome/browser/browsing_data/BrowsingDataBridge.java",
    "java/src/org/chromium/chrome/browser/browsing_data/BrowsingDataCounterBridge.java",
    "java/src/org/chromium/chrome/browser/browsing_data/UrlFilterBridge.java",
    "java/src/org/chromium/chrome/browser/complex_tasks/TaskTabHelper.java",
    "java/src/org/chromium/chrome/browser/compositor/CompositorView.java",
    "java/src/org/chromium/chrome/browser/compositor/LayerTitleCache.java",
    "java/src/org/chromium/chrome/browser/compositor/bottombar/OverlayPanelContent.java",
    "java/src/org/chromium/chrome/browser/compositor/layouts/content/TabContentManager.java",
    "java/src/org/chromium/chrome/browser/compositor/scene_layer/ContextualSearchSceneLayer.java",
    "java/src/org/chromium/chrome/browser/compositor/scene_layer/StaticTabSceneLayer.java",
    "java/src/org/chromium/chrome/browser/compositor/scene_layer/TabListSceneLayer.java",
    "java/src/org/chromium/chrome/browser/compositor/scene_layer/TabStripSceneLayer.java",
    "java/src/org/chromium/chrome/browser/compositor/scene_layer/ToolbarSwipeSceneLayer.java",
    "java/src/org/chromium/chrome/browser/content/ContentUtils.java",
    "java/src/org/chromium/chrome/browser/contextmenu/ContextMenuHelper.java",
    "java/src/org/chromium/chrome/browser/contextualsearch/ContextualSearchContext.java",
    "java/src/org/chromium/chrome/browser/contextualsearch/ContextualSearchManager.java",
    "java/src/org/chromium/chrome/browser/contextualsearch/ContextualSearchTabHelper.java",
    "java/src/org/chromium/chrome/browser/crash/MinidumpUploadServiceImpl.java",
    "java/src/org/chromium/chrome/browser/customtabs/CustomTabsConnection.java",
    "java/src/org/chromium/chrome/browser/customtabs/CustomTabsOpenTimeRecorder.java",
    "java/src/org/chromium/chrome/browser/customtabs/features/TabInteractionRecorder.java",
    "java/src/org/chromium/chrome/browser/datareduction/DataSaverOSSetting.java",
    "java/src/org/chromium/chrome/browser/device_dialog/ChromeBluetoothChooserAndroidDelegate.java",
    "java/src/org/chromium/chrome/browser/device_dialog/ChromeBluetoothScanningPromptAndroidDelegate.java",
    "java/src/org/chromium/chrome/browser/device_dialog/UsbChooserDialog.java",
    "java/src/org/chromium/chrome/browser/document/DocumentWebContentsDelegate.java",
    "java/src/org/chromium/chrome/browser/dom_distiller/DomDistillerServiceFactory.java",
    "java/src/org/chromium/chrome/browser/dom_distiller/DomDistillerTabUtils.java",
    "java/src/org/chromium/chrome/browser/dom_distiller/DomDistillerUIUtils.java",
    "java/src/org/chromium/chrome/browser/download/DownloadController.java",
    "java/src/org/chromium/chrome/browser/download/DownloadItem.java",
    "java/src/org/chromium/chrome/browser/download/DownloadManagerService.java",
    "java/src/org/chromium/chrome/browser/download/DownloadMessageBridge.java",
    "java/src/org/chromium/chrome/browser/download/DownloadUtils.java",
    "java/src/org/chromium/chrome/browser/download/DuplicateDownloadDialogBridge.java",
    "java/src/org/chromium/chrome/browser/download/service/DownloadBackgroundTask.java",
    "java/src/org/chromium/chrome/browser/download/service/DownloadTaskScheduler.java",
    "java/src/org/chromium/chrome/browser/explore_sites/ExploreSitesBridge.java",
    "java/src/org/chromium/chrome/browser/explore_sites/ExploreSitesCategory.java",
    "java/src/org/chromium/chrome/browser/explore_sites/ExploreSitesSite.java",
    "java/src/org/chromium/chrome/browser/feature_guide/notifications/FeatureNotificationGuideBridge.java",
    "java/src/org/chromium/chrome/browser/feature_guide/notifications/FeatureNotificationGuideServiceFactory.java",
    "java/src/org/chromium/chrome/browser/feedback/ConnectivityChecker.java",
    "java/src/org/chromium/chrome/browser/feedback/ScreenshotTask.java",
    "java/src/org/chromium/chrome/browser/firstrun/FirstRunUtils.java",
    "java/src/org/chromium/chrome/browser/flags/BadFlagsSnackbarManager.java",
    "java/src/org/chromium/chrome/browser/gesturenav/OverscrollSceneLayer.java",
    "java/src/org/chromium/chrome/browser/history/BrowsingHistoryBridge.java",
    "java/src/org/chromium/chrome/browser/history/HistoryDeletionBridge.java",
    "java/src/org/chromium/chrome/browser/history/HistoryDeletionInfo.java",
    "java/src/org/chromium/chrome/browser/historyreport/HistoryReportJniBridge.java",
    "java/src/org/chromium/chrome/browser/infobar/AutofillCreditCardFillingInfoBar.java",
    "java/src/org/chromium/chrome/browser/infobar/AutofillOfferNotificationInfoBar.java",
    "java/src/org/chromium/chrome/browser/infobar/AutofillSaveCardInfoBar.java",
    "java/src/org/chromium/chrome/browser/infobar/AutofillVirtualCardEnrollmentInfoBar.java",
    "java/src/org/chromium/chrome/browser/infobar/DuplicateDownloadInfoBar.java",
    "java/src/org/chromium/chrome/browser/infobar/FramebustBlockInfoBar.java",
    "java/src/org/chromium/chrome/browser/infobar/GeneratedPasswordSavedInfoBarDelegate.java",
    "java/src/org/chromium/chrome/browser/infobar/InfoBarContainer.java",
    "java/src/org/chromium/chrome/browser/infobar/InstantAppsInfoBar.java",
    "java/src/org/chromium/chrome/browser/infobar/InstantAppsInfoBarDelegate.java",
    "java/src/org/chromium/chrome/browser/infobar/KnownInterceptionDisclosureInfoBar.java",
    "java/src/org/chromium/chrome/browser/infobar/NearOomInfoBar.java",
    "java/src/org/chromium/chrome/browser/infobar/NearOomReductionInfoBar.java",
    "java/src/org/chromium/chrome/browser/infobar/PermissionInfoBar.java",
    "java/src/org/chromium/chrome/browser/infobar/ReaderModeInfoBar.java",
    "java/src/org/chromium/chrome/browser/infobar/SafetyTipInfoBar.java",
    "java/src/org/chromium/chrome/browser/infobar/SavePasswordInfoBar.java",
    "java/src/org/chromium/chrome/browser/infobar/SurveyInfoBar.java",
    "java/src/org/chromium/chrome/browser/infobar/TranslateCompactInfoBar.java",
    "java/src/org/chromium/chrome/browser/infobar/UpdatePasswordInfoBar.java",
    "java/src/org/chromium/chrome/browser/init/NativeStartupBridge.java",
    "java/src/org/chromium/chrome/browser/instantapps/InstantAppsMessageDelegate.java",
    "java/src/org/chromium/chrome/browser/instantapps/InstantAppsSettings.java",
    "java/src/org/chromium/chrome/browser/javascript/WebContextFetcher.java",
    "java/src/org/chromium/chrome/browser/lens/LensDebugBridge.java",
    "java/src/org/chromium/chrome/browser/lens/LensPolicyUtils.java",
    "java/src/org/chromium/chrome/browser/login/ChromeHttpAuthHandler.java",
    "java/src/org/chromium/chrome/browser/media/MediaCaptureDevicesDispatcherAndroid.java",
    "java/src/org/chromium/chrome/browser/media/PictureInPictureActivity.java",
    "java/src/org/chromium/chrome/browser/media/router/ChromeMediaRouterClient.java",
    "java/src/org/chromium/chrome/browser/metrics/LaunchMetrics.java",
    "java/src/org/chromium/chrome/browser/metrics/PageLoadMetrics.java",
    "java/src/org/chromium/chrome/browser/metrics/UmaSessionStats.java",
    "java/src/org/chromium/chrome/browser/metrics/UmaUtils.java",
    "java/src/org/chromium/chrome/browser/metrics/VariationsSession.java",
    "java/src/org/chromium/chrome/browser/mojo/ChromeInterfaceRegistrar.java",
    "java/src/org/chromium/chrome/browser/navigation_predictor/NavigationPredictorBridge.java",
    "java/src/org/chromium/chrome/browser/net/nqe/NetworkQualityProvider.java",
    "java/src/org/chromium/chrome/browser/notifications/ActionInfo.java",
    "java/src/org/chromium/chrome/browser/notifications/NotificationPlatformBridge.java",
    "java/src/org/chromium/chrome/browser/notifications/NotificationTriggerScheduler.java",
    "java/src/org/chromium/chrome/browser/notifications/scheduler/DisplayAgent.java",
    "java/src/org/chromium/chrome/browser/notifications/scheduler/NotificationSchedulerTask.java",
    "java/src/org/chromium/chrome/browser/ntp/ForeignSessionHelper.java",
    "java/src/org/chromium/chrome/browser/ntp/RecentTabsPagePrefs.java",
    "java/src/org/chromium/chrome/browser/ntp/RecentlyClosedBridge.java",
    "java/src/org/chromium/chrome/browser/offlinepages/AutoFetchNotifier.java",
    "java/src/org/chromium/chrome/browser/offlinepages/BackgroundSchedulerBridge.java",
    "java/src/org/chromium/chrome/browser/offlinepages/OfflinePageArchivePublisherBridge.java",
    "java/src/org/chromium/chrome/browser/offlinepages/OfflinePageBridge.java",
    "java/src/org/chromium/chrome/browser/offlinepages/PublishPageCallback.java",
    "java/src/org/chromium/chrome/browser/offlinepages/RequestCoordinatorBridge.java",
    "java/src/org/chromium/chrome/browser/offlinepages/SavePageRequest.java",
    "java/src/org/chromium/chrome/browser/offlinepages/downloads/OfflinePageDownloadBridge.java",
    "java/src/org/chromium/chrome/browser/offlinepages/prefetch/PrefetchBackgroundTask.java",
    "java/src/org/chromium/chrome/browser/offlinepages/prefetch/PrefetchBackgroundTaskScheduler.java",
    "java/src/org/chromium/chrome/browser/page_info/PageInfoAboutThisSiteController.java",
    "java/src/org/chromium/chrome/browser/partnerbookmarks/PartnerBookmarksReader.java",
    "java/src/org/chromium/chrome/browser/password_manager/AccountChooserDialog.java",
    "java/src/org/chromium/chrome/browser/password_manager/AutoSigninFirstRunDialog.java",
    "java/src/org/chromium/chrome/browser/password_manager/AutoSigninSnackbarController.java",
    "java/src/org/chromium/chrome/browser/password_manager/Credential.java",
    "java/src/org/chromium/chrome/browser/password_manager/CredentialLeakDialogBridge.java",
    "java/src/org/chromium/chrome/browser/password_manager/PasswordChangeLauncher.java",
    "java/src/org/chromium/chrome/browser/password_manager/PasswordCheckupLauncher.java",
    "java/src/org/chromium/chrome/browser/password_manager/PasswordGenerationDialogBridge.java",
    "java/src/org/chromium/chrome/browser/password_manager/PasswordGenerationPopupBridge.java",
    "java/src/org/chromium/chrome/browser/password_manager/PasswordManagerLauncher.java",
    "java/src/org/chromium/chrome/browser/password_manager/settings/PasswordUIView.java",
    "java/src/org/chromium/chrome/browser/permissions/NotificationBlockedDialog.java",
    "java/src/org/chromium/chrome/browser/permissions/PermissionSettingsBridge.java",
    "java/src/org/chromium/chrome/browser/permissions/PermissionUpdateRequester.java",
    "java/src/org/chromium/chrome/browser/policy/PolicyAuditor.java",
    "java/src/org/chromium/chrome/browser/policy/PolicyAuditorBridge.java",
    "java/src/org/chromium/chrome/browser/printing/TabPrinter.java",
    "java/src/org/chromium/chrome/browser/privacy/settings/PrivacyPreferencesManagerImpl.java",
    "java/src/org/chromium/chrome/browser/push_messaging/PushMessagingServiceObserver.java",
    "java/src/org/chromium/chrome/browser/query_tiles/QueryTileUtils.java",
    "java/src/org/chromium/chrome/browser/query_tiles/TileProviderFactory.java",
    "java/src/org/chromium/chrome/browser/query_tiles/TileServiceUtils.java",
    "java/src/org/chromium/chrome/browser/read_later/ReadingListBridge.java",
    "java/src/org/chromium/chrome/browser/renderer_host/ChromeNavigationUIData.java",
    "java/src/org/chromium/chrome/browser/resources/ResourceMapper.java",
    "java/src/org/chromium/chrome/browser/rlz/RevenueStats.java",
    "java/src/org/chromium/chrome/browser/rlz/RlzPingHandler.java",
    "java/src/org/chromium/chrome/browser/safe_browsing/SafeBrowsingPasswordReuseDialogBridge.java",
    "java/src/org/chromium/chrome/browser/safe_browsing/SafeBrowsingReferringAppBridge.java",
    "java/src/org/chromium/chrome/browser/safe_browsing/SafeBrowsingSettingsLauncher.java",
    "java/src/org/chromium/chrome/browser/segmentation_platform/ContextualPageActionController.java",
    "java/src/org/chromium/chrome/browser/sharing/SharingJNIBridge.java",
    "java/src/org/chromium/chrome/browser/sharing/SharingServiceProxy.java",
    "java/src/org/chromium/chrome/browser/sharing/click_to_call/ClickToCallMessageHandler.java",
    "java/src/org/chromium/chrome/browser/sharing/shared_clipboard/SharedClipboardMessageHandler.java",
    "java/src/org/chromium/chrome/browser/sharing/sms_fetcher/SmsFetcherMessageHandler.java",
    "java/src/org/chromium/chrome/browser/signin/SigninBridge.java",
    "java/src/org/chromium/chrome/browser/signin/SigninManagerImpl.java",
    "java/src/org/chromium/chrome/browser/site_settings/CookieControlsServiceBridge.java",
    "java/src/org/chromium/chrome/browser/status_indicator/StatusIndicatorSceneLayer.java",
    "java/src/org/chromium/chrome/browser/suggestions/mostvisited/MostVisitedSites.java",
    "java/src/org/chromium/chrome/browser/suggestions/mostvisited/MostVisitedSitesBridge.java",
    "java/src/org/chromium/chrome/browser/supervised_user/ChildAccountFeedbackReporter.java",
    "java/src/org/chromium/chrome/browser/supervised_user/ChildAccountService.java",
    "java/src/org/chromium/chrome/browser/survey/SurveyHttpClientBridge.java",
    "java/src/org/chromium/chrome/browser/sync/TrustedVaultClient.java",
    "java/src/org/chromium/chrome/browser/tab/TabBrowserControlsConstraintsHelper.java",
    "java/src/org/chromium/chrome/browser/tab/TabFavicon.java",
    "java/src/org/chromium/chrome/browser/tab/TabImpl.java",
    "java/src/org/chromium/chrome/browser/tab/TabUtils.java",
    "java/src/org/chromium/chrome/browser/tab/TabWebContentsDelegateAndroidImpl.java",
    "java/src/org/chromium/chrome/browser/tab/tab_restore/HistoricalTabSaverImpl.java",
    "java/src/org/chromium/chrome/browser/tabmodel/TabModelJniBridge.java",
    "java/src/org/chromium/chrome/browser/tabmodel/TabModelObserverJniBridge.java",
    "java/src/org/chromium/chrome/browser/usage_stats/NotificationSuspender.java",
    "java/src/org/chromium/chrome/browser/usage_stats/UsageStatsBridge.java",
    "java/src/org/chromium/chrome/browser/webapps/WebApkDataProvider.java",
    "java/src/org/chromium/chrome/browser/webapps/WebApkHandlerDelegate.java",
    "java/src/org/chromium/chrome/browser/webapps/WebApkInstallCoordinatorBridge.java",
    "java/src/org/chromium/chrome/browser/webapps/WebApkInstallService.java",
    "java/src/org/chromium/chrome/browser/webapps/WebApkInstaller.java",
    "java/src/org/chromium/chrome/browser/webapps/WebApkPostShareTargetNavigator.java",
    "java/src/org/chromium/chrome/browser/webapps/WebApkUpdateDataFetcher.java",
    "java/src/org/chromium/chrome/browser/webapps/WebApkUpdateManager.java",
    "java/src/org/chromium/chrome/browser/webapps/WebappRegistry.java",
  ]

  # Used for testing only, should not be shipped to end users.
  if (enable_offline_pages_harness) {
    sources += [ "java/src/org/chromium/chrome/browser/offlinepages/evaluation/OfflinePageEvaluationBridge.java" ]
  }

  if (enable_vr) {
    sources += [ "java/src/org/chromium/chrome/browser/component_updater/VrAssetsComponentInstaller.java" ]
  }
}

source_set("chrome_test_util_jni") {
  testonly = true
  sources = [
    "javatests/src/custom_tabs_test_utils.cc",
    "javatests/src/federated_identity_test_utils.cc",
  ]
  deps = [
    ":chrome_test_util_jni_headers",
    "//base",
    "//chrome/browser",
    "//chrome/browser/profiles:profile",
    "//components/variations",
    "//url:gurl_android",
    "//url:url",
  ]
}

# This is a list of all base module jni headers. New features should add their
# own jni target to this list.
group("jni_headers") {
  public_deps = [
    ":chrome_jni_headers",
    "//chrome/android/features/autofill_assistant:jni_headers_public",
    "//chrome/android/features/keyboard_accessory:jni_headers",
    "//chrome/android/features/start_surface:jni_headers",
    "//chrome/browser/android/browserservices/metrics:jni_headers",
    "//chrome/browser/android/browserservices/verification:jni_headers",
    "//chrome/browser/battery/android:jni_headers",
    "//chrome/browser/commerce/merchant_viewer/android:jni_headers",
    "//chrome/browser/commerce/price_tracking/android:jni_headers",
    "//chrome/browser/commerce/subscriptions/android:jni_headers",
    "//chrome/browser/contextmenu:jni_headers",
    "//chrome/browser/download/android:jni_headers",
    "//chrome/browser/enterprise/util:jni_headers",
    "//chrome/browser/feature_engagement:jni_headers",
    "//chrome/browser/flags:jni_headers",
    "//chrome/browser/history_clusters:jni_headers",
    "//chrome/browser/image_descriptions:jni_headers",
    "//chrome/browser/incognito:jni_headers",
    "//chrome/browser/locale:jni_headers",
    "//chrome/browser/preferences:jni_headers",
    "//chrome/browser/privacy:jni_headers",
    "//chrome/browser/profiles/android:jni_headers",
    "//chrome/browser/search_engines/android:jni_headers",
    "//chrome/browser/segmentation_platform:jni_headers",
    "//chrome/browser/tab:jni_headers",
    "//chrome/browser/touch_to_fill/android:jni_headers",
    "//chrome/browser/touch_to_fill/payments/android:jni_headers",
    "//chrome/browser/ui/android/fast_checkout:jni_headers",
    "//chrome/browser/ui/android/favicon:jni_headers",
    "//chrome/browser/ui/android/logo:jni_headers",
    "//chrome/browser/ui/android/omnibox:jni_headers",
    "//chrome/browser/ui/android/toolbar:jni_headers",
    "//chrome/browser/ui/android/webid:jni_headers",
    "//chrome/browser/ui/messages/android:jni_headers",
    "//chrome/browser/util:jni_headers",
    "//chrome/browser/webauthn/android:jni_headers",
    "//components/autofill_assistant/android:jni_headers",
    "//components/autofill_assistant/android:jni_headers_public",
    "//components/autofill_assistant/android:jni_headers_public_dependencies",
    "//components/digital_asset_links/android:jni_headers",
    "//components/image_fetcher:jni_headers",
    "//components/media_router/browser/android:jni_headers",
    "//components/ukm/android:jni_headers",
    "//components/webauthn/android:jni_headers",
  ]
}

# Chrome APK's native library.
template("libchrome_impl") {
  chrome_common_shared_library(target_name) {
    sources = [
      "../browser/android/chrome_entry_point.cc",
      chrome_jni_registration_header,
    ]
    deps = [ ":chrome_jni_registration($default_toolchain)" ]
    if (defined(invoker.deps)) {
      deps += invoker.deps
    }

    if (enable_vr) {
      deps += [ "//chrome/browser/android/vr:module_factory" ]
    }

    module_descs = chrome_modern_module_descs
    forward_variables_from(invoker, "*", [ "deps" ])
  }
}

if (enable_resource_allowlist_generation) {
  libchrome_impl("libchrome_pak_allowlist_inputs") {
    collect_inputs_only = true
  }
}

libchrome_impl("libchrome") {
  define_unwind_table_target =
      add_unwind_tables_in_chrome_32bit_apk && target_cpu == "arm"
  if (enable_resource_allowlist_generation) {
    # Make sure allowlist_inputs is built first so when concurrent_links == 1
    # it comes before the actual (very slow) link step.
    deps = [ ":libchrome_pak_allowlist_inputs" ]
  }
}

chrome_common_shared_library("libchromefortest") {
  testonly = true
  define_unwind_table_target =
      add_unwind_tables_in_chrome_32bit_apk && target_cpu == "arm"
  sources = [
    "../browser/android/chrome_entry_point_for_test.cc",
    chrome_jni_for_test_registration_header,
  ]
  deps = [
    ":browser_test_support",
    ":chrome_jni_for_test_registration($default_toolchain)",
    ":chrome_test_util_jni",
    "//base/test:test_support",
    "//chrome:chrome_android_core",
    "//chrome/browser/android/metrics:ukm_utils_for_test",
    "//chrome/browser/password_manager/android:test_support",
    "//chrome/browser/subresource_filter:android_test_support",
    "//chrome/browser/supervised_user:test_support",
    "//components/autofill_assistant/browser:android_test_support",
    "//components/crash/android:crash_android",
    "//components/external_intents/android:test_support",
    "//components/minidump_uploader",
    "//components/paint_preview/player/android:test_support",
    "//components/sync",
    "//content/public/test/android:content_native_test_support",
    "//content/test:test_support",
    "//ui/base/clipboard:clipboard_test_support",
  ]
  if (enable_vr) {
    deps += [ "//chrome/browser/android/vr:test_support" ]
  }

  # Make this a partitioned library, since some partitioned code is linked in
  # (otherwise, the library will warn at build time that it contains multiple
  # symbol tables). However, do not create the partitions, as they are not
  # required or packaged into the APK. This can be removed if LLD starts
  # supporting a "no partitions" argument (https://crbug.com/1021108).
  module_descs = []
}

# Monochrome equivalent of Chrome's APK or bundle library template.
template("libmonochrome_apk_or_bundle_tmpl") {
  chrome_common_shared_library(target_name) {
    sources = [ "../browser/android/monochrome_entry_point.cc" ]
    deps = [
      "//android_webview/lib",
      "//android_webview/nonembedded",
      "//components/crash/android:crashpad_main",
    ]
    if (defined(invoker.deps)) {
      deps += invoker.deps
    }

    if (webview_includes_weblayer) {
      defines = [ "WEBVIEW_INCLUDES_WEBLAYER" ]
      deps += [ "//weblayer:weblayer_lib" ]
    }

    if (enable_vr) {
      deps += [ "//chrome/browser/android/vr:module_factory" ]
    }

    is_monochrome = true
    module_descs = monochrome_module_descs

    forward_variables_from(invoker, "*", [ "deps" ])
  }
}

if (!android_64bit_target_cpu ||
    (!skip_secondary_abi_for_cq &&
     current_toolchain == android_secondary_abi_toolchain)) {
  # Avoiding a real link for this step allows the actual libmonochrome to
  # run at the same time as R8.
  libmonochrome_apk_or_bundle_tmpl("libmonochrome_pak_allowlist_inputs") {
    collect_inputs_only = true
  }

  libmonochrome_apk_or_bundle_tmpl("libmonochrome") {
    define_unwind_table_target =
        add_unwind_tables_in_chrome_32bit_apk && current_cpu == "arm"
    if (enable_resource_allowlist_generation) {
      # Make sure allowlist_inputs is built first so when concurrent_links == 1
      # it comes before the actual (very slow) link step.
      deps = [ ":libmonochrome_pak_allowlist_inputs" ]
    }
  }

  if (android_64bit_target_cpu && !skip_secondary_abi_for_cq) {
    group("monochrome_64_secondary_abi_lib") {
      public_deps = [ ":libmonochrome_64($android_secondary_abi_toolchain)" ]
    }
  }
} else {
  # 64-bit browser library targets (APK and bundle).
  libmonochrome_apk_or_bundle_tmpl("libmonochrome_64") {
  }

  # 32-bit browser library alias targets, pulled in by 64-bit WebView builds.
  if (android_64bit_target_cpu && !skip_secondary_abi_for_cq) {
    group("monochrome_secondary_abi_lib") {
      public_deps = [ ":libmonochrome($android_secondary_abi_toolchain)" ]
    }
  }
}

if (add_unwind_tables_in_chrome_32bit_apk) {
  if (defined(android_secondary_abi_toolchain)) {
    _toolchain = android_secondary_abi_toolchain
  } else {
    _toolchain = default_toolchain
  }
  _toolchain_out_dir = get_label_info(":foo($_toolchain)", "target_out_dir")

  template("chrome_unwind_assets") {
    android_assets(target_name) {
      forward_variables_from(invoker, [ "testonly" ])
      _libname = invoker.libname
      deps = [ ":${_libname}_unwind_table_v1($_toolchain)" ]
      sources = [ "$_toolchain_out_dir/${_libname}_unwind_table_v1/$unwind_table_asset_v1_filename" ]
      disable_compression = true
      if (use_android_unwinder_v2) {
        sources += [ "$_toolchain_out_dir/${_libname}_unwind_table_v2/$unwind_table_asset_v2_filename" ]
        deps += [ ":${_libname}_unwind_table_v2($_toolchain)" ]
      }
    }
  }
  chrome_unwind_assets("libmonochrome_unwind_table_assets") {
    libname = "libmonochrome"
  }

  # We create fat apks only with libmonochrome.
  if (target_cpu == "arm") {
    chrome_unwind_assets("libchrome_unwind_table_assets") {
      libname = "libchrome"
    }
    chrome_unwind_assets("libchromefortest_unwind_table_assets") {
      testonly = true
      libname = "libchromefortest"
    }
  }
}

# Used by android-binary-size trybot to validate expectations.
if (current_toolchain == default_toolchain &&
    (_enable_libs_and_assets_verification || _enable_manifest_verification)) {
  group("validate_expectations") {
    deps = []
    if (_enable_libs_and_assets_verification) {
      deps += [
        ":chrome_modern_public_bundle_validate_libs_and_assets",
        ":monochrome_public_bundle_validate_libs_and_assets",
        ":trichrome_chrome_bundle_validate_libs_and_assets",
        ":trichrome_library_apk_validate_libs_and_assets",
      ]
    }
    if (_enable_manifest_verification) {
      deps += [
        ":monochrome_public_bundle_validate_manifests",
        ":monochrome_public_bundle_validate_proguard_config",
        ":trichrome_chrome_bundle__base_bundle_module_validate_android_manifest",
        ":trichrome_library_apk_validate_android_manifest",
        "//android_webview:system_webview_base_bundle_module_validate_android_manifest",
        "//android_webview:trichrome_webview_base_bundle_module_validate_android_manifest",
      ]
    }
  }
}
