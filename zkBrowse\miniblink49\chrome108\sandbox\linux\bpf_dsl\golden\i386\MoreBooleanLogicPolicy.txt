  1) LOAD 4  // Architecture
  2) if A == 0x40000003; then JMP 3 else JMP 30
  3) LOAD 0  // System call number
  4) if A & 0x40000000; then JMP 30 else JMP 5
  5) if A >= 0xa5; then JMP 6 else JMP 7
  6) if A >= 0x401; then JMP 37 else JMP 36
  7) if A >= 0xa4; then JMP 8 else JMP 36
  8) LOAD 20  // Argument 0 (MSB)
  9) if A == 0x0; then JMP 10 else JMP 30
 10) LOAD 16  // Argument 0 (LSB)
 11) if A == 0x0; then JMP 35 else JMP 12
 12) LOAD 28  // Argument 1 (MSB)
 13) if A == 0x0; then JMP 14 else JMP 30
 14) LOAD 24  // Argument 1 (LSB)
 15) if A == 0x0; then JMP 35 else JMP 16
 16) LOAD 36  // Argument 2 (MSB)
 17) if A == 0x0; then JMP 18 else JMP 30
 18) LOAD 32  // Argument 2 (LSB)
 19) if A == 0x0; then JMP 35 else JMP 20
 20) LOAD 20  // Argument 0 (MSB)
 21) if A == 0x0; then JMP 22 else JMP 30
 22) LOAD 16  // Argument 0 (LSB)
 23) if A == 0x1; then JMP 24 else JMP 33
 24) LOAD 28  // Argument 1 (MSB)
 25) if A == 0x0; then JMP 26 else JMP 30
 26) LOAD 24  // Argument 1 (LSB)
 27) if A == 0x1; then JMP 28 else JMP 33
 28) LOAD 36  // Argument 2 (MSB)
 29) if A == 0x0; then JMP 31 else JMP 30
 30) RET 0x0  // Kill
 31) LOAD 32  // Argument 2 (LSB)
 32) if A == 0x1; then JMP 34 else JMP 33
 33) RET 0x50016  // errno = 22
 34) RET 0x5000b  // errno = 11
 35) RET 0x50001  // errno = 1
 36) RET 0x7fff0000  // Allowed
 37) RET 0x50026  // errno = 38
