<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background-attachment applied to elements with 'display' set to 'table-header-group'</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="<PERSON><PERSON>" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-04-20 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background-attachment" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <link rel="match" href="background-attachment-applies-to-001-ref.xht" />

        <meta name="flags" content="image" />
        <meta name="assert" content="The 'background-attachment' property applies to elements with 'display' set to 'table-header-group'." />
        <style type="text/css">
            p {font: 1em/1.25 serif;}

            #test
            {
                background-color: orange;
                background-image: url('support/blue96x96.png');
                background-attachment: fixed;
                background-repeat: repeat-x;
                display: table-header-group;
            }
            #table
            {
                display: table;
                table-layout: fixed;
            }
            #row
            {
                display: table-row;
            }
            #cell
            {
                display: table-cell;
                height: 2in;
                width: 1in;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is a short blue stripe<br />
        above a tall orange rectangle.</p>
        <div id="table">
            <div id="test">
                <div id="row">
                    <div id="cell"></div>
                </div>
            </div>
        </div>
    </body>
</html>
