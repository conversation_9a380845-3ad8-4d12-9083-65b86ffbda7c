<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background-position using a keyword and percentage value, center 100%</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="G<PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-04-25 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background-position" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <link rel="match" href="background-position-116-ref.xht" />

        <meta name="flags" content="image" />
        <meta name="assert" content="The 'background-position' property correctly applies a keyword and percentage value." />
        <style type="text/css">
            div
            {
                background-repeat: no-repeat;
                height: 3in;
                width: 3in;
            }
            #div1
            {
                background-image: url("support/background-position-bottom-center.png");
            }
            div div
            {
                background-image: url("support/blue96x96.png");
                background-position: center 100%;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is <strong>no red</strong>.</p>
        <div id="div1">
            <div></div>
        </div>
    </body>
</html>