// Copyright 2021 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include "sandbox/policy/linux/bpf_print_backend_policy_linux.h"

namespace sandbox {
namespace policy {

PrintBackendProcessPolicy::PrintBackendProcessPolicy() = default;
PrintBackendProcessPolicy::~PrintBackendProcessPolicy() = default;

}  // namespace policy
}  // namespace sandbox
