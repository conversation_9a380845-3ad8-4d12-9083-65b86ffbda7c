Verify navigator rendering with OOPIFs
Sources:
-------- Setting mode: [frame]
top
  page.html
  iframe.html
    iframe.html?second
    iframe-iframe2 (inner-iframe.html)
      inner-iframe.html?second
    inner-iframe.html
      inner-iframe.html?first
  page-iframe1 (iframe.html)
    iframe.html?first
    iframe-iframe1 (inner-iframe.html)
      inner-iframe.html?first
    inner-iframe.html
      inner-iframe.html?second
Sources:
-------- Setting mode: [frame/domain]
top
  127.0.0.1:8000
    page.html
  iframe.html
    devtools.oopif.test:8000
      iframe.html?second
    iframe-iframe2 (inner-iframe.html)
      devtools.oopif.test:8000
        inner-iframe.html?second
    inner-iframe.html
      127.0.0.1:8000
        inner-iframe.html?first
  page-iframe1 (iframe.html)
    127.0.0.1:8000
      iframe.html?first
    iframe-iframe1 (inner-iframe.html)
      127.0.0.1:8000
        inner-iframe.html?first
    inner-iframe.html
      devtools.oopif.test:8000
        inner-iframe.html?second
Sources:
-------- Setting mode: [frame/domain/folder]
top
  127.0.0.1:8000
    devtools/oopif/resources
      page.html
  iframe.html
    devtools.oopif.test:8000
      devtools/oopif/resources
        iframe.html?second
    iframe-iframe2 (inner-iframe.html)
      devtools.oopif.test:8000
        devtools/oopif/resources
          inner-iframe.html?second
    inner-iframe.html
      127.0.0.1:8000
        devtools/oopif/resources
          inner-iframe.html?first
  page-iframe1 (iframe.html)
    127.0.0.1:8000
      devtools/oopif/resources
        iframe.html?first
    iframe-iframe1 (inner-iframe.html)
      127.0.0.1:8000
        devtools/oopif/resources
          inner-iframe.html?first
    inner-iframe.html
      devtools.oopif.test:8000
        devtools/oopif/resources
          inner-iframe.html?second
Sources:
-------- Setting mode: [domain]
127.0.0.1:8000
  iframe.html?first
  inner-iframe.html?first
  page.html
iframe.html
  devtools.oopif.test:8000
    iframe.html?second
    inner-iframe.html?second
inner-iframe.html
  127.0.0.1:8000
    inner-iframe.html?first
inner-iframe.html
  devtools.oopif.test:8000
    inner-iframe.html?second
Sources:
-------- Setting mode: [domain/folder]
127.0.0.1:8000
  devtools/oopif/resources
    iframe.html?first
    inner-iframe.html?first
    page.html
iframe.html
  devtools.oopif.test:8000
    devtools/oopif/resources
      iframe.html?second
      inner-iframe.html?second
inner-iframe.html
  127.0.0.1:8000
    devtools/oopif/resources
      inner-iframe.html?first
inner-iframe.html
  devtools.oopif.test:8000
    devtools/oopif/resources
      inner-iframe.html?second

