<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.3.3 background-image</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
P {background-image: url(../resources/bg.gif);}
.one {background-image: none;}
</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>P {background-image: url(../resources/bg.gif);}
.one {background-image: none;}

</PRE>
<HR>
<P>
This sentence should be backed by an image-- a green grid pattern, in this case.  The background image should also tile along both axes, because no repeat direction is specified (specific tests for repeating are found elsewhere).
</P>
<P>
This sentence should be backed by a repeated green-grid image, as should the last three words <STRONG><SPAN class="one">in this sentence</SPAN></STRONG>.  If it is not, then <CODE>none</CODE> is interpreted incorrectly.  (<CODE>none</CODE> means that the element has no background image, allowing the parent to "shine through" by default; since the parent of the words "in this sentence" is the paragraph, then the paragraph's image should be visible.)
</P>
<P class="one">
This sentence should NOT be backed by a repeated green-grid image, allowing the page's background to "shine through" instead.
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P>
This sentence should be backed by an image-- a green grid pattern, in this case.  The background image should also tile along both axes, because no repeat direction is specified (specific tests for repeating are found elsewhere).
</P>
<P>
This sentence should be backed by a repeated green-grid image, as should the last three words <STRONG><SPAN class="one">in this sentence</SPAN></STRONG>.  If it is not, then <CODE>none</CODE> is interpreted incorrectly.  (<CODE>none</CODE> means that the element has no background image, allowing the parent to "shine through" by default; since the parent of the words "in this sentence" is the paragraph, then the paragraph's image should be visible.)
</P>
<P class="one">
This sentence should NOT be backed by a repeated green-grid image, allowing the page's background to "shine through" instead.
</P>
</TD></TR></TABLE></BODY>
</HTML>
