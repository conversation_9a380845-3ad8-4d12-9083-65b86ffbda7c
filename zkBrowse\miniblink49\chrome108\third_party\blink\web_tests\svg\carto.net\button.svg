<?xml version="1.0" encoding="iso-8859-1"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" [
<!ATTLIST svg
	xmlns:a3 CDATA #IMPLIED
	a3:scriptImplementation CDATA #IMPLIED
>
<!ATTLIST script
	a3:scriptImplementation CDATA #IMPLIED
>
]>
<?AdobeSVGViewer save="snapshot"?>
<svg viewBox="0 0 1024 768" xmlns="http://www.w3.org/2000/svg"
	xmlns:xlink="http://www.w3.org/1999/xlink"
	xmlns:a3="http://ns.adobe.com/AdobeSVGViewerExtension/3.0/" onload="initialize(evt);"
	zoomAndPan="disable">
	<script type="text/ecmascript" xlink:href="resources/helper_functions.js"/>
	<script type="text/ecmascript" xlink:href="resources/timer.js"/>
	<script type="text/ecmascript" xlink:href="resources/button.js"/>
	<script type="text/ecmascript"><![CDATA[
		var textbutton1;
		var textbutton2;
		var textbutton3;
		var textbutton4;
		var textbutton5;
		var textbutton7;
		var buttonMoveVertex;
		var ellipseButton;
		function initialize(evt) {
			//first a few styles
			var buttonTextStyles = {"font-family":"Arial,Helvetica","fill":"navy","font-size":12};
			var buttonStyles = {"fill":"lightsteelblue"};
			var shadeLightStyles = {"fill":"white"};
			var shadeDarkStyles = {"fill":"navy"};
			var buttonRoundStyles = {"fill":"lightsteelblue","rx":10,"ry":10};
			var shadeLightRoundStyles = {"fill":"white","rx":10,"ry":10};
			var shadeDarkRoundStyles = {"fill":"navy","rx":10,"ry":10};
			textbutton1 = new button("textbutton1","textbuttons",buttonPressed,"rect","Click me",undefined,100,100,100,30,buttonTextStyles,buttonStyles,shadeLightStyles,shadeDarkStyles,1);
			textbutton2 = new button("textbutton2","textbuttons",buttonPressed,"rect","Click me 2",undefined,220,100,100,30,buttonTextStyles,buttonStyles,shadeLightStyles,shadeDarkStyles,1);
			textbutton3 = new button("textbutton3","textbuttons",buttonPressed,"rect","Deactivate Button 2",undefined,340,100,130,30,buttonTextStyles,buttonStyles,shadeLightStyles,shadeDarkStyles,1);
			textbutton4 = new switchbutton("textbutton4","textbuttons",switchButtonPressed,"rect","Switch Button on",undefined,490,100,120,30,buttonTextStyles,buttonStyles,shadeLightStyles,shadeDarkStyles,1);
			textbutton5 = new button("textbutton5","textbuttons",buttonPressed,"rect","Remove Button 1",undefined,630,100,130,30,buttonTextStyles,buttonStyles,shadeLightStyles,shadeDarkStyles,1);
			textbutton6 = new button("textbutton6","textbuttons",buttonPressed,"rect","Switch Button 4 on",undefined,780,100,130,30,buttonTextStyles,buttonStyles,shadeLightStyles,shadeDarkStyles,1);
			textbutton7 = new button("textbutton7","textbuttons",buttonPressed,"rect","This is a multiline\ntextbutton with\nround corners",undefined,200,300,150,60,buttonTextStyles,buttonRoundStyles,shadeLightRoundStyles,shadeDarkRoundStyles,1);
			buttonMoveVertex = new switchbutton("buttonMoveVertex","imagebuttons",imageButtonPressed,"rect",undefined,"moveVertex",300,150,20,20,buttonTextStyles,buttonStyles,shadeLightStyles,shadeDarkStyles,1);
			ellipseButton = new switchbutton("ellipseButton","imagebuttons",imageButtonPressed,"ellipse",undefined,"moveVertex",350,145,25,25,buttonTextStyles,buttonStyles,shadeLightStyles,shadeDarkStyles,1);
		}
		function buttonPressed(buttonId,evt,textVal) {
			statusChange("button with id '"+buttonId+"' and text value of '"+textVal+"' was pressed");
			if (buttonId == "textbutton3") {
				if (textbutton2.activated) {
					textbutton2.deactivate();
					textbutton3.setTextValue("Activate Button 2");
				}
				else {
					textbutton2.activate();
					textbutton3.setTextValue("Deactivate Button 2");
				}
			}
			if (buttonId == "textbutton5") {
				textbutton1.removeButton();
				textbutton5.deactivate();
			}
			if (buttonId == "textbutton6") {
				if (textbutton4.getSwitchValue()) {
					textbutton4.setSwitchValue(false,true);
				}
				else {
					textbutton4.setSwitchValue(true,true);
				}
			}
		}
		function switchButtonPressed(buttonId,evt,onOrOff,buttonText) {
			if (buttonId == "textbutton4") {
				if (onOrOff) {
					textbutton4.setTextValue("Switch Button off");
					textbutton6.setTextValue("Switch Button 4 off");
				}
				else {
					textbutton4.setTextValue("Switch Button on");
					textbutton6.setTextValue("Switch Button 4 on");
				}
			}
		}
		function imageButtonPressed(buttonId,evt,onOrOff) {
			if (onOrOff) {
				statusChange("button with id '"+buttonId+"' was turned on, evt.type="+evt.type);
			}
			else {
				statusChange("button with id '"+buttonId+"' was turned off, evt.type="+evt.type);
			}
		}
		]]></script>
	<defs>
		<symbol id="moveVertex" overflow="visible">
			<g transform="scale(1.25)">
				<line x1="0" y1="0" x2="3" y2="7" stroke="black" stroke-width="1.75"/>
				<path fill-rule="evenodd" fill="white" stroke="black" stroke-width="1"
					d="M-3 -7L-3 3L0 0L4.25 -0.5z"/>
			</g>
		</symbol>
	</defs>
	<rect x="-1000" y="-1000" width="3000" height="3000" fill="burlywood" stroke="none"/>
	<g id="textbuttons"/>
	<g id="imagebuttons" />
	<text id="statusText" x="100" y="210" xml:space="preserve">StatusText</text>
	<text onclick="var newWidth = 140+Math.random()*50;var newHeight = 50+Math.random()*50;textbutton7.resize(newWidth,newHeight)" x="430" y="320" xml:space="preserve">Click this text to randomly resize the multiline textbutton (textbutton7)</text>
	<text onclick="var x = 175+Math.random()*50;var y = 275+Math.random()*50;textbutton7.moveTo(x,y)" x="430" y="340" xml:space="preserve">Click this text to randomly reposition the multiline textbutton (textbutton7)</text>
</svg>
