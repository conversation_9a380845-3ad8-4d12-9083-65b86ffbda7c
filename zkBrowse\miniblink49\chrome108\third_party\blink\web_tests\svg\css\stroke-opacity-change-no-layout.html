<!DOCTYPE html>
<script src="../../resources/js-test.js"></script>
<script>
if (window.testRunner)
  testRunner.dumpAsText();

onload = function() {
  document.body.offsetTop;
  if (window.internals)
    shouldBe("internals.needsLayoutCount()", "0", true);

  document.getElementById('rect').style.strokeOpacity = '0.5';
  if (window.internals) {
    shouldBe("internals.updateStyleAndReturnAffectedElementCount()", "1", true);
    shouldBe("internals.needsLayoutCount()", "0");
  }
};
</script>
Change of stroke-opacity should cause style recalc only, no layout.
<svg>
  <rect id="rect" x="10" y="10" width="100" height="100" stroke="green" stroke-width="10"/>
</svg>
