<!DOCTYPE html>
<script src="../../resources/js-test.js"></script>
<script>
description('Test tracking event handlers with a reused DOMWindow.');

if (window.testRunner) {
    testRunner.setPopupBlockingEnabled(false);
    testRunner.waitUntilDone();
}

// |window.open| returns a DOMWindow synchronously without waiting for the
// network load to finish. Once the load completes, we transition the existing
// window object to the document instead of creating a new one like we normally
// would. The purpose of this test is to ensure event handlers added on the
// window are still present after the window is reused with the new document.
// See DocumentLoader.cpp for details.
var newWindow = window.open('resources/conclude-test-in-parent.html');
newWindow.onscroll = function() {};

function concludeTest() {
    shouldBe('internals.scrollEventHandlerCount(newWindow.document)', '1');
    testRunner.notifyDone();
}
</script>
</body>
