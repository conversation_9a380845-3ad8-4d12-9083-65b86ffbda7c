<!DOCTYPE html>
<html>
<body>
<div id="container"></div>
<script>
if (window.testRunner)
    testRunner.dumpAsText();
window.onerror = function(message) { console.log("FAIL"); }

Object.defineProperty(Object.prototype, "prototype", {
    get: function() { console.log("FAIL"); },
    set: function() { console.log("FAIL"); }
});

Object.defineProperty(Object.prototype, "constructor", {
    get: function() { console.log("FAIL"); },
    set: function() { console.log("FAIL"); }
});

throw "PASS if this is reported as an uncaught exception.";
</script>
</body>
</html>
