<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>CSS Test: Opera Tests: Background: Test Twenty Three</title>
  <link rel="author" title="<PERSON>" href="mailto:<EMAIL>"/>
  <link rel="author" title="Elika J. Etemad" href="http://fantasai.inkedblade.net/contact"/>
  <meta content="The BODY and HTML backgrounds are drawn independently when each is specified, even if their specified values are the same." name="assert" /> <!-- I am not sure what the original intention of the test was, but it was wrong, so this is my attempt to fix it. -->
  <link rel="alternate" href="http://www.hixie.ch/tests/adhoc/css/background/23.html" type="text/html"/>
  <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background"/>
  <link rel="match" href="background-root-023-ref.xht" />

<style type="text/css"><![CDATA[
   html { background: url(support/60x60-gg-rr.png) 0 3em repeat-x; margin: 0; border: 0; padding: 0; }
   body { background: url(support/60x60-gg-rr.png) 0 3em repeat-x; margin: 0; border: 0; padding: 0; }
   p { margin-top: 30px; border-top: 30px solid transparent; height: 0; padding: 3em 1em 0; font-size: 1em; }
   div { position: fixed; top: 3em; height: 60px; background: orange; left: 0; width: 1em; }
]]></style>

</head>
<body>
<p>There should be a horizontal green bar crossing the page above this sentence, exactly the height of the vertical orange bar at its left end:</p>

<div></div>
</body>
</html>
