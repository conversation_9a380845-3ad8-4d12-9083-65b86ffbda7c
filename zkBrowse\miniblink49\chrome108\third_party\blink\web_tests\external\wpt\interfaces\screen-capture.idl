// GENERATED CONTENT - DO NOT EDIT
// Content was automatically extracted by <PERSON><PERSON> into webref
// (https://github.com/w3c/webref)
// Source: Screen Capture (https://w3c.github.io/mediacapture-screen-share/)

partial interface MediaDevices {
  Promise<MediaStream> getDisplayMedia(optional DisplayMediaStreamOptions options = {});
};

[Exposed=Window, SecureContext]
interface CaptureController {
  constructor();
  // TODO: Add setFocusBehavior() in a separate PR.
};

enum SelfCapturePreferenceEnum {
  "include",
  "exclude"
};

enum SystemAudioPreferenceEnum {
  "include",
  "exclude"
};

enum SurfaceSwitchingPreferenceEnum {
  "include",
  "exclude"
};

dictionary DisplayMediaStreamOptions {
  (boolean or MediaTrackConstraints) video = true;
  (boolean or MediaTrackConstraints) audio = false;
  CaptureController controller = null;
  SelfCapturePreferenceEnum selfBrowserSurface;
  SystemAudioPreferenceEnum systemAudio;
  SurfaceSwitchingPreferenceEnum surfaceSwitching;
};

partial dictionary MediaTrackSupportedConstraints {
  boolean displaySurface = true;
  boolean logicalSurface = true;
  boolean cursor = true;
  boolean restrictOwnAudio = true;
  boolean suppressLocalAudioPlayback = true;
};

partial dictionary MediaTrackConstraintSet {
  ConstrainDOMString displaySurface;
  ConstrainBoolean logicalSurface;
  ConstrainDOMString cursor;
  ConstrainBoolean restrictOwnAudio;
  ConstrainBoolean suppressLocalAudioPlayback;
};

partial dictionary MediaTrackSettings {
  DOMString displaySurface;
  boolean logicalSurface;
  DOMString cursor;
  boolean restrictOwnAudio;
};

partial dictionary MediaTrackCapabilities {
  DOMString displaySurface;
  boolean logicalSurface;
  sequence<DOMString> cursor;
};

enum DisplayCaptureSurfaceType {
  "monitor",
  "window",
  "browser"
};

enum CursorCaptureConstraint {
  "never",
  "always",
  "motion"
};
