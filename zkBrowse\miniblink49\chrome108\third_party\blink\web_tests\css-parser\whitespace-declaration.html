<!DOCTYPE html>
<script src="../resources/testharness.js"></script>
<script src="../resources/testharnessreport.js"></script>
<script src="../fast/css/getComputedStyle/resources/computed-style-listing.js"></script>
<div id="target"></div>
<script>
test(function() {
    var style = getComputedStyle(target);
    for (var i = 0; i < style.length; i++) {
        var property = style.item(i);
        assert_false(CSS.supports(property, ''), property + ":;");
        assert_false(CSS.supports(property, ' '), property + ": ;");
    }
}, 'Verify that property values consisting of only whitespace are not accepted.');
</script>
