<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.5.05 margin</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
body {overflow: hidden;}
.zero {background-color: silver; margin: 0;}
.one {margin: 0.5in; background-color: aqua;}
.two {margin: 25px; background-color: aqua;}
.three {margin: 5em; background-color: aqua;}
.four {margin: 25%; background-color: aqua;}
.five {margin: 25px;}
.six {margin: -10px; background-color: aqua;}</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.zero {background-color: silver; margin: 0;}
.one {margin: 0.5in; background-color: aqua;}
.two {margin: 25px; background-color: aqua;}
.three {margin: 5em; background-color: aqua;}
.four {margin: 25%; background-color: aqua;}
.five {margin: 25px;}
.six {margin: -10px; background-color: aqua;}
</PRE>
<HR>
<P>
NOTE: The following tests are separated by class-zero paragraphs, so as to prevent margin-collapsing as described in section 4.1.1 of the CSS1 specification.
</P>
<P class="zero">
This element has a class of zero.
</P>
<P class="one">
This sentence should have an overall margin of half an inch, which will require extra text in order to test.
</P>
<P class="zero">
This element has a class of zero.
</P>
<P class="two">
This sentence should have an overall margin of 25 pixels, which will require extra text in order to test.
</P>
<P class="zero">
This element has a class of zero.
</P>
<P class="three">
This sentence should have an overall margin of 5 em, which will require extra text in order to test.
</P>
<P class="zero">
This element has a class of zero.
</P>
<P class="four">
This sentence should have an overall margin of 25%, which is calculated with respect to the width of the parent element.  This will require extra text in order to test.
</P>
<P class="zero">
This element has a class of zero.
</P>
<UL class="two">
<LI>This list has a margin of 25px, and a light blue background.
<LI>Therefore, it ought to have such a margin.
<LI class="five">This list item has a margin of 25px, which should cause it to be offset in some fashion.
<LI>This list item has no special styles applied to it.
</UL>
<P class="zero">
This element has a class of zero.
</P>
<P class="six">
This paragraph has an overall margin of -10px, which should make it wider than usual as well as shift it upward and pull subsequent text up toward it, and a light blue background.  In all other respects, however, the element should be normal.  No styles have been applied to it besides the negative margin and the background color.
</P>
<P class="zero">
This element has a class of zero.
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P>
NOTE: The following tests are separated by class-zero paragraphs, so as to prevent margin-collapsing as described in section 4.1.1 of the CSS1 specification.
</P>
<P class="zero">
This element has a class of zero.
</P>
<P class="one">
This sentence should have an overall margin of half an inch, which will require extra text in order to test.
</P>
<P class="zero">
This element has a class of zero.
</P>
<P class="two">
This sentence should have an overall margin of 25 pixels, which will require extra text in order to test.
</P>
<P class="zero">
This element has a class of zero.
</P>
<P class="three">
This sentence should have an overall margin of 5 em, which will require extra text in order to test.
</P>
<P class="zero">
This element has a class of zero.
</P>
<P class="four">
This sentence should have an overall margin of 25%, which is calculated with respect to the width of the parent element.  This will require extra text in order to test.
</P>
<P class="zero">
This element has a class of zero.
</P>
<UL class="two">
<LI>This list has a margin of 25px, and a light blue background.
<LI>Therefore, it ought to have such a margin.
<LI class="five">This list item has a margin of 25px, which should cause it to be offset in some fashion.
<LI>This list item has no special styles applied to it.
</UL>
<P class="zero">
This element has a class of zero.
</P>
<P class="six">
This paragraph has an overall margin of -10px, which should make it wider than usual as well as shift it upward and pull subsequent text up toward it, and a light blue background.  In all other respects, however, the element should be normal.  No styles have been applied to it besides the negative margin and the background color.
</P>
<P class="zero">
This element has a class of zero.
</P>
</TD></TR></TABLE></BODY>
</HTML>
