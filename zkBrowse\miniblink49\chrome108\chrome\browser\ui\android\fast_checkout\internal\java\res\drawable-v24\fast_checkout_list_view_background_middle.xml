<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright 2022 The Chromium Authors
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file.
-->
<org.chromium.components.browser_ui.widget.SurfaceColorDrawable
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:shape="rectangle"
    app:surfaceElevation="@dimen/default_elevation_1">
    <corners android:radius="@dimen/fast_checkout_inner_corner_radius"/>
</org.chromium.components.browser_ui.widget.SurfaceColorDrawable>
