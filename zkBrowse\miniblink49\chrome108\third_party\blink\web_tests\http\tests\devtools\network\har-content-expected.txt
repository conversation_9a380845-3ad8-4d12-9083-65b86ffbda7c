Tests conversion of Inspector's resource representation into HAR format.

initiator.css:
    compression: 0
    mimeType: "text/css"
    size: 262
    text: ".image-background {\n    background-image: url(\"resource.php?type=image&random=1&size=200\");\n}\n\n.image-background-2 {\n    background-image: url(\"resource.php?type=image&random=1&size=300\");\n}\n\n@font-face {\n    font-family: Example;\n    src: url(\"example.ttf\");\n}\n"

binary.data:
    compression: 0
    encoding: "base64"
    mimeType: "application/octet-stream"
    size: 256
    text: "AAECAwQFBgcICQoLDA0ODxAREhMUFRYXGBkaGxwdHh8gISIjJCUmJygpKissLS4vMDEyMzQ1Njc4OTo7PD0+P0BBQkNERUZHSElKS0xNTk9QUVJTVFVWV1hZWltcXV5fYGFiY2RlZmdoaWprbG1ub3BxcnN0dXZ3eHl6e3x9fn+AgYKDhIWGh4iJiouMjY6PkJGSk5SVlpeYmZqbnJ2en6ChoqOkpaanqKmqq6ytrq+wsbKztLW2t7i5uru8vb6/wMHCw8TFxsfIycrLzM3Oz9DR0tPU1dbX2Nna29zd3t/g4eLj5OXm5+jp6uvs7e7v8PHy8/T19vf4+fr7/P3+/w=="

