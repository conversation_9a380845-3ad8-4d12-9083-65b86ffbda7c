<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

 <head>

  <title>CSS Reftest Reference</title>

  <link rel="author" title="Gérard Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" />

  <style type="text/css"><![CDATA[
  body {margin: 8px;}

  div
  {
  background-color: blue;
  height: 15px;
  margin-left: 93px;

  /*

  From left to right:

     2px : left border-spacing
  +
    25px : border-left of 1st cell
  +
     1px : padding-left of 1st cell
  +
     1px : padding-right of 1st cell
  +
    25px : border-right of 1st cell
  +
     2px : border-spacing between 1st and 2nd cell
  +
    25px : border-left of 2nd cell
  +
     1px : padding-left of 2nd cell
  +
     1px : padding-right of 2nd cell
  +
    25px : border-right of 2nd cell
  -
    15px : width of blue square
  ===============================
    93px

  */

  margin-top: 18px; /* margin-bottom of p + top border-spacing  */
  width: 15px;
  }
  ]]></style>

 </head>

 <body>

  <p>Test passes if there is <strong>one and only 1 small filled blue square</strong>.</p>

  <div></div>

 </body>
</html>
