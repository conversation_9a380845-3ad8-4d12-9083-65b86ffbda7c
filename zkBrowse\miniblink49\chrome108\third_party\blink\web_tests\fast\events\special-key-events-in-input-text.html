<html><head></head>
<body>
<p>This test verifies that the correct sequence of keyboard events is generated for a keypress for certain special keys. To test manually, press keys and compare results to other browsers.</p>
<input type="text"></input>
<div id="log"></div>

<script>
function log(msg) {
   document.getElementById("log").innerHTML+= msg + "<br>";
}

function logEvent(e) {
   var target = e.target;
   var type = e.type;

   var info = target.tagName + " - " + type + ' - ';
   if (type == "textInput") {
     info += e.data;
   } else if (type == "keydown" || type == "keypress" || type == "keyup") {
     info += [e.ctrl<PERSON><PERSON>, e.alt<PERSON><PERSON>, e.shift<PERSON><PERSON>, e.meta<PERSON>ey] +
         ' -' + e.key +
         '- ' + e.keyCode +
         ' - ' + e.charCode;
   }
   info += '. Value: "' + target.value + '".';
   log(info);
}

log("target - type - " + ["ctrl<PERSON><PERSON>", "alt<PERSON><PERSON>", "shift<PERSON>ey", "metaKey"]
       + ' - ' + "key"
       + ' - ' + "keyCode"
       + ' - ' + "charCode");

var input = document.getElementsByTagName("input")[0];
input.addEventListener('textInput', logEvent, false);
input.addEventListener('keypress', logEvent, false);
input.addEventListener('keydown', logEvent, false);
input.addEventListener('keyup', logEvent, false);
input.focus();

if (window.testRunner) {
   testRunner.dumpAsText();
   log("<br>Ampersand:");
   eventSender.keyDown("7", ["shiftKey"]);
   log("<br>Backspace:");
   eventSender.keyDown("Backspace", []);
   log("<br>Percent:");
   eventSender.keyDown("5", ["shiftKey"]);
   log("<br>Backspace:");
   eventSender.keyDown("Backspace", []);
   log("<br>Left parenthesis:");
   eventSender.keyDown("9", ["shiftKey"]);
   log("<br>Backspace:");
   eventSender.keyDown("Backspace", []);
   log("<br>Right parenthesis:");
   eventSender.keyDown("0", ["shiftKey"]);
   log("<br>Backspace:");
   eventSender.keyDown("Backspace", []);
   log("<br>Print screen:");
   eventSender.keyDown("PrintScreen", []);
  }
</script>
</body></html>
