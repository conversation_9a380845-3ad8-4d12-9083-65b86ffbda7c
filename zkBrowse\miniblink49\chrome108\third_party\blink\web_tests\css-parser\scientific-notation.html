<!doctype html>
<script src="../resources/testharness.js"></script>
<script src="../resources/testharnessreport.js"></script>
<script src="resources/property-parsing-test.js"></script>
<script>
assert_valid_value("opacity", "2e2", "200");
assert_valid_value("opacity", "2E2", "200");
assert_valid_value("opacity", "2e+2", "200");
assert_valid_value("opacity", "2E+2", "200");
assert_valid_value("opacity", "2e-2", "0.02");
assert_valid_value("opacity", "2E-2", "0.02");
assert_valid_value("opacity", "2e20", "2e+20");
assert_valid_value("opacity", "2E20", "2e+20");
assert_valid_value("opacity", "2e+20", "2e+20");
assert_valid_value("opacity", "2E+20", "2e+20");
assert_valid_value("opacity", "2e-20", "2e-20");
assert_valid_value("opacity", "2E-20", "2e-20");
assert_valid_value("opacity", "1e0", "1");
assert_valid_value("opacity", "1e+0", "1");
assert_valid_value("opacity", "1e-0", "1");
assert_valid_value("opacity", "1e10", "1e+10");
assert_valid_value("opacity", "1e+10", "1e+10");
assert_valid_value("opacity", "1e-10", "1e-10");
assert_valid_value("width", "2e2px", "200px");
assert_valid_value("width", "2E2px", "200px");
assert_valid_value("width", "2e+2px", "200px");
assert_valid_value("width", "2E+2px", "200px");
assert_valid_value("width", "2e-2px", "0.02px");
assert_valid_value("width", "2E-2px", "0.02px");
assert_valid_value("width", "2e20px", "2e+20px");
assert_valid_value("width", "2E20px", "2e+20px");
assert_valid_value("width", "2e+20px", "2e+20px");
assert_valid_value("width", "2E+20px", "2e+20px");
assert_valid_value("width", "2e-20px", "2e-20px");
assert_valid_value("width", "2E-20px", "2e-20px");
assert_valid_value("width", "1e0px", "1px");
assert_valid_value("width", "1e+0px", "1px");
assert_valid_value("width", "1e-0px", "1px");
assert_valid_value("width", "1e10px", "1e+10px");
assert_valid_value("width", "1e+10px", "1e+10px");
assert_valid_value("width", "1e-10px", "1e-10px");
assert_valid_value("width", "1e0em", "1em");
assert_valid_value("width", "1e+0em", "1em");
assert_valid_value("width", "1e-0em", "1em");
assert_valid_value("width", "1e10em", "1e+10em");
assert_valid_value("width", "1e+10em", "1e+10em");
assert_valid_value("width", "1e-10em", "1e-10em");
assert_valid_value("width", "1e0%", "1%");
assert_valid_value("width", "1e+0%", "1%");
assert_valid_value("width", "1e-0%", "1%");
assert_valid_value("width", "1e10%", "1e+10%");
assert_valid_value("width", "1e+10%", "1e+10%");
assert_valid_value("width", "1e-10%", "1e-10%");
// Large exponents (not representable). This is not necessarily the correct value.
assert_valid_value("opacity", "1e-600", "0");
assert_valid_value("width", "1e-600px", "0px");
assert_valid_value("width", "1e600px", "3.40282e+38px");
assert_valid_value("opacity", "1e600", "3.40282e+38");
assert_valid_value("transform", "translateX(1e600px)", "translateX(3.40282e+38px)");

assert_invalid_value("width", "1e+px");
assert_invalid_value("width", "1e-px");
assert_invalid_value("width", "1e1.0px");
assert_invalid_value("width", "1e10.0px");
assert_invalid_value("width", "1e1.0em");
assert_invalid_value("width", "1e10.0em");
</script>
