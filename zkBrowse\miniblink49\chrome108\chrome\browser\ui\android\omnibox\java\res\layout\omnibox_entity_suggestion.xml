<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright 2019 The Chromium Authors
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file.
-->
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical">

    <TextView
        android:id="@+id/entity_subject"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textAppearance="@style/TextAppearance.TextLarge.Primary"
        android:maxLines="1"
        android:ellipsize="end"
        android:textAlignment="viewStart" />

    <TextView
        android:id="@+id/entity_description"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textAppearance="@style/TextAppearance.TextMedium.Secondary"
        android:maxLines="1"
        android:visibility="gone"
        android:ellipsize="end"
        android:textAlignment="viewStart" />

</LinearLayout>
