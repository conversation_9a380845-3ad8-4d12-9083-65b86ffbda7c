#include <iostream>
#include <windows.h>
#include "../../v8_10_8/include/v8-version.h"

int main() {
    std::cout << "V8 Version Information:" << std::endl;
    std::cout << "Major: " << V8_MAJOR_VERSION << std::endl;
    std::cout << "Minor: " << V8_MINOR_VERSION << std::endl;
    std::cout << "Build: " << V8_BUILD_NUMBER << std::endl;
    std::cout << "Patch: " << V8_PATCH_LEVEL << std::endl;
    std::cout << "Version String: " << V8_VERSION_STRING << std::endl;
    
    return 0;
}
