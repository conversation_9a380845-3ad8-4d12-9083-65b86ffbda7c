<html>
<head>
    <style>
        ::-webkit-scrollbar {
            width: 0px;
            height: 0px;
        }
    </style>
    <script>
        window.enablePixelTesting = true;

        function scroll() {
            window.scrollTo(0, 100);
            var frame = document.getElementById('frame');
            frame.contentDocument.scrollingElement.scrollTop = 100;
            frame.contentDocument.scrollingElement.scrollLeft = 100;

            shouldBe("frame.contentDocument.scrollingElement.scrollTop", "100");
            shouldBe("frame.contentDocument.scrollingElement.scrollLeft", "100");
            shouldBe("frame.contentWindow.scrollX", "100");
            shouldBe("frame.contentWindow.scrollY", "100");
        }

        function scaleWithEventSender() {
            var scaleFactor = 0.5;
            if (window.internals) {
                internals.setPageScaleFactorLimits(scaleFactor, scaleFactor);
                internals.setPageScaleFactor(scaleFactor);
            }
        }

        function test() {
            scaleWithEventSender();
            scroll();
        }
    </script>
    <script src="../../resources/js-test.js"></script>
</head>
<body onload="test();" style="width: 4000px; height: 4000px">
    <iframe id="frame" style="width:400px; height: 400px; position: absolute; top: 100px;"></iframe>
    <div id="console"></div>

    <script>
        var frame = document.getElementById('frame');
        var doc = frame.contentDocument.open();
        doc.write("<style>::-webkit-scrollbar { width: 0; height: 0 }</style>");
        doc.write("<div style='left:0; top:0; width:100px; height:100px; position:absolute; background:yellow;'></div>");
        doc.write("<div style='left:100px; top:0; width:100px; height:100px; position:absolute; background:green;'></div>");
        doc.write("<div style='left:200px; top:0; width:100px; height:100px; position:absolute; background:blue;'></div>");
        doc.write("<div style='left:300px; top:0; width:100px; height:100px; position:absolute; background:green;'></div>");
        doc.write("<div style='left:400px; top:0; width:100px; height:100px; position:absolute; background:blue;'></div>");

        doc.write("<div style='left:0; top:100px; width:100px; height:100px; position:absolute; background: green;'></div>");
        doc.write("<div style='left:100px; top:100px; width:100px; height:100px; position:absolute; background:blue;'></div>");
        doc.write("<div style='left:200px; top:100px; width:100px; height:100px; position:absolute; background:green;'></div>");
        doc.write("<div style='left:300px; top:100px; width:100px; height:100px; position:absolute; background:blue;'></div>");
        doc.write("<div style='left:400px; top:100px; width:100px; height:100px; position:absolute; background:green;'></div>");
        doc.close();
        frame.contentDocument.body.style.width = "2000px";
        frame.contentDocument.body.style.height = "2000px";
    </script>
</body>
</html>
