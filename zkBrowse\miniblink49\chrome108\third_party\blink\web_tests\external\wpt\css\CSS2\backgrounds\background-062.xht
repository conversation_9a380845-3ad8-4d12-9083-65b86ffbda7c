<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background with (attachment color image)</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <meta name="flags" content="image interact" />
        <meta name="assert" content="Background with (attachment color image) sets the background to the image specified, repeated, and the image does not 'scroll' within the box." />
        <style type="text/css">
            #div1
            {
                height: 200px;
                overflow: scroll;
                width: 200px;
            }
            div div
            {
                background: fixed green url("support/cat.png");
                height: 300px;
                width: 300px;
            }
        </style>
    </head>
    <body>
        <p>Test passes if scrolling on the box below does not cause the cat image to move.</p>
        <div id="div1">
            <div></div>
        </div>
    </body>
</html>