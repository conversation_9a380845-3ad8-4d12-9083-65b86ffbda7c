<html>
<script>
var windowWasScrolled = false;
var doneTimeout;

function windowScrolled() {
    windowWasScrolled = true;
    document.getElementById('result').innerHTML = 'FAILURE: window.onscroll was called.'; 
    clearTimeout(doneTimeout);    
}

function divScrolled() {
    if (!windowWasScrolled)
        document.getElementById('result').innerHTML = 'SUCCESS: div.onscroll was called, but window.onscroll was not.'; 
    // Don't call notifyDone straight away, in case there's another scroll event coming/bubbling.
    doneTimeout = setTimeout(function() {

        // Don't pollute the test result with nonsense.
        document.getElementById('container').innerHTML = '';
        if (window.testRunner)
            testRunner.notifyDone();
        }, 100);    
}

function runTest() {
    var failure = false;
    
    if (window.testRunner) {
        testRunner.dumpAsText();
        testRunner.waitUntilDone();
    }
    
    var div = document.getElementById('container');
  
    div.onscroll = divScrolled;
    window.onscroll = windowScrolled;
    div.scrollTop = 1;
}

</script>
<body onload="runTest()">
<div>
    This tests that the scroll event does not bubble.
</div>
<div id="container" style="width:100px; height:200px;overflow:scroll">
    Quisque rhoncus, nibh quis condimentum malesuada, tortor dolor consequat turpis, sit amet lacinia lacus massa et nulla. In volutpat, metus sed interdum tempus, diam mi scelerisque ante, a imperdiet nunc purus sit amet sapien. Nam ac nisl non ipsum hendrerit ornare. Mauris vestibulum. Etiam ut sapien. Nunc in neque. Maecenas dictum cursus ipsum. Fusce elit est, feugiat in, accumsan aliquet, gravida eu, turpis. Curabitur sit amet turpis vitae erat facilisis hendrerit. Fusce quam mauris, ornare non, ultrices ut, sodales a, pede. Nunc blandit tempus pede. Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Sed arcu pede, tincidunt ut, lacinia id, iaculis pellentesque, lectus. Fusce sapien nisl, dapibus ac, venenatis at, accumsan eu, mi. Duis vehicula tincidunt quam. Nullam eleifend. Donec nec diam. Etiam sapien mauris, bibendum non, porttitor eu, varius interdum, tellus.
</div>

<div id="result">FAILURE</div>
</html>
