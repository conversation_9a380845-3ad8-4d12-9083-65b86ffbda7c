<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Border-style set using one value</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="<PERSON><PERSON>" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-06-24 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#border-style-properties" />
        <meta name="assert" content="Applying a single value to the border-style property applies the value to all sides of the element." />
        <style type="text/css">
            div
            {
                border-width: 5px;
                border-style: dotted;
                height: 1in;
            }
        </style>
    </head>
    <body>
        <p>Test passes if the border is the same dotted style on all edges.</p>
        <div></div>
    </body>
</html>