This test checks that we correctly update the scroll event handler count as event handlers are added and removed

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".

PASS internals.scrollEventHandlerCount(document) is 0
PASS internals.scrollEventHandlerCount(document) is 1
PASS internals.scrollEventHandlerCount(document) is 2
PASS internals.scrollEventHandlerCount(document) is 1
PASS internals.scrollEventHandlerCount(document) is 1
PASS internals.scrollEventHandlerCount(document) is 0
Test setting onscroll on the document.
PASS internals.scrollEventHandlerCount(document) is 0
PASS internals.scrollEventHandlerCount(document) is 1
PASS internals.scrollEventHandlerCount(document) is 1
PASS internals.scrollEventHandlerCount(document) is 0
Test that nested Documents' scroll handlers are properly tracked in their parent Document.
PASS internals.scrollEventHandlerCount(document) is 1
PASS internals.scrollEventHandlerCount(nestedDocument) is 2
PASS internals.scrollEventHandlerCount(document) is 2
PASS internals.scrollEventHandlerCount(nestedDocument) is 1
PASS internals.scrollEventHandlerCount(document) is 1
PASS internals.scrollEventHandlerCount(document) is 1
Test that detaching a nested Document with handlers works properly.
PASS internals.scrollEventHandlerCount(nestedDocument) is 2
PASS internals.scrollEventHandlerCount(document) is 2
PASS internals.scrollEventHandlerCount(document) is 0
Test moving event listeners from an unattached document to an attached one
PASS internals.scrollEventHandlerCount(doc) is 0
PASS internals.scrollEventHandlerCount(document) is 0
PASS internals.scrollEventHandlerCount(document) is 2
PASS internals.scrollEventHandlerCount(document) is 2
PASS internals.scrollEventHandlerCount(document) is 0
Test moving event listeners from an attached document to an unattached one
PASS internals.scrollEventHandlerCount(document) is 3
PASS internals.scrollEventHandlerCount(document) is 0
Test moving a scroll event listener between documents belonging to the same page
PASS internals.scrollEventHandlerCount(document) is 2
PASS internals.scrollEventHandlerCount(document) is 2
PASS internals.scrollEventHandlerCount(document) is 0
Test addEventListener/removeEventListener on the window.
PASS internals.scrollEventHandlerCount(document) is 0
PASS internals.scrollEventHandlerCount(document) is 1
PASS internals.scrollEventHandlerCount(document) is 2
PASS internals.scrollEventHandlerCount(document) is 1
PASS internals.scrollEventHandlerCount(document) is 1
PASS internals.scrollEventHandlerCount(document) is 0
Test setting onscroll on the window.
PASS internals.scrollEventHandlerCount(document) is 0
PASS internals.scrollEventHandlerCount(document) is 1
PASS internals.scrollEventHandlerCount(document) is 1
PASS internals.scrollEventHandlerCount(document) is 0
PASS successfullyParsed is true

TEST COMPLETE

