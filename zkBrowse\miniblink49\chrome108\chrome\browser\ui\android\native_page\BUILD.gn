# Copyright 2020 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.
import("//build/config/android/rules.gni")

android_library("java") {
  sources = [
    "java/src/org/chromium/chrome/browser/ui/native_page/BasicNativePage.java",
    "java/src/org/chromium/chrome/browser/ui/native_page/FrozenNativePage.java",
    "java/src/org/chromium/chrome/browser/ui/native_page/NativePage.java",
    "java/src/org/chromium/chrome/browser/ui/native_page/NativePageHost.java",
    "java/src/org/chromium/chrome/browser/ui/native_page/TouchEnabledDelegate.java",
  ]
  deps = [
    "//base:base_java",
    "//components/browser_ui/styles/android:java",
    "//components/embedder_support/android:util_java",
    "//content/public/android:content_java",
    "//third_party/androidx:androidx_annotation_annotation_java",
    "//url:gurl_java",
  ]
}

robolectric_library("junit") {
  sources = [
    "java/src/org/chromium/chrome/browser/ui/native_page/NativePageTest.java",
  ]

  deps = [
    ":java",
    "//base:base_junit_test_support",
    "//third_party/junit",
  ]
}
