# Copyright 2016 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//mojo/public/tools/bindings/mojom.gni")

# This should be merged into the ":cpp" target below once that target is
# converted to a component target. A component target is necessary for
# ServiceProvider because it exposes global storage.
component("service_provider") {
  public = [ "service_provider.h" ]

  sources = [ "service_provider.cc" ]

  public_deps = [
    "//base",
    "//mojo/public/cpp/bindings",
    "//services/data_decoder/public/mojom",
  ]

  defines = [ "IS_DATA_DECODER_PUBLIC_IMPL" ]
}

source_set("cpp") {
  public = [
    "data_decoder.h",
    "json_sanitizer.h",
  ]

  sources = [
    "data_decoder.cc",
    "json_sanitizer.cc",
  ]

  configs += [ "//build/config/compiler:wexit_time_destructors" ]

  public_deps = [
    ":safe_xml_parser",
    ":service_provider",
    "//base",
    "//services/data_decoder/public/mojom",
  ]
  deps = []

  if (is_android) {
    sources += [ "json_sanitizer_android.cc" ]

    deps +=
        [ "//services/data_decoder/public/cpp/android:safe_json_jni_headers" ]
  } else {
    sources += [ "json_sanitizer_non_android.cc" ]
  }

  if (is_ios) {
    # NOTE: We depend on this target here for iOS only, to support in-process
    # use of the service. Non-test targets in this directory should otherwise
    # NEVER depend on this target.
    deps = [ "//services/data_decoder:lib" ]
  } else {
    public += [
      "decode_image.h",
      "safe_web_bundle_parser.h",
    ]
    sources += [
      "decode_image.cc",
      "safe_web_bundle_parser.cc",
    ]
    deps += [ "//skia" ]
  }

  defines = [ "IS_DATA_DECODER_PUBLIC_IMPL" ]
}

source_set("safe_xml_parser") {
  public = [ "safe_xml_parser.h" ]

  sources = [ "safe_xml_parser.cc" ]

  public_deps = [ "//base" ]
  deps = [ "//services/data_decoder/public/mojom:mojom_xml_parser" ]
}

source_set("test_support") {
  testonly = true
  sources = [
    "test_support/in_process_data_decoder.cc",
    "test_support/in_process_data_decoder.h",
  ]

  public_deps = [
    ":cpp",
    "//base",
    "//services/data_decoder:lib",
  ]
}
