<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
    <head>
        <title>CSS Test: overflow applied to elements with 'display' set to 'inline'</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/">
        <link rel="help" href="http://www.w3.org/TR/CSS21/visufx.html#propdef-overflow">
        <link rel="help" href="http://www.w3.org/TR/CSS21/visufx.html#overflow">
        <meta name="flags" content="ahem">
        <script src="../../resources/ahem.js"></script>
        <meta name="assert" content="The 'overflow' property does not apply to elements with 'display' set to 'inline'.">
        <style type="text/css">
            #parent
            {
                border: 5px solid transparent;
                color: white;
                font: 20px/1em Ahem;
                width: 5em;
            }
            div div
            {
                display: inline;
                overflow: hidden;
                white-space: nowrap;
            }
            #span2
            {
                color: green;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is a green box below.</p>
        <div id="parent">
            <div><span>XXXXX</span><span id="span2">XXXXX</span></div>
        </div>
    </body>
</html>