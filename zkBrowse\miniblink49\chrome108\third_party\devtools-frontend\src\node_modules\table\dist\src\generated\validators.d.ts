declare function validate43(data: any, { instancePath, parentData, parentDataProperty, rootData }?: {
    instancePath?: string | undefined;
    parentData: any;
    parentDataProperty: any;
    rootData?: any;
}): boolean;
declare function validate86(data: any, { instancePath, parentData, parentDataProperty, rootData }?: {
    instancePath?: string | undefined;
    parentData: any;
    parentDataProperty: any;
    rootData?: any;
}): boolean;
export { validate43 as _config_json, validate86 as _streamConfig_json };
