<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background-color set to rgb() using percentages with a nominal value, rgb(40%, 40%, 40%)</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="<PERSON><PERSON>" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2013-04-08 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background-color" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <link rel="match" href="background-color-052-ref.xht" />
        <meta name="flags" content="ahem" />
        <meta name="assert" content="Background-color is set to rgb(40%, 40%, 40%)." />
        <link rel="stylesheet" type="text/css" href="/fonts/ahem.css" />
        <style type="text/css"><![CDATA[
            #test
            {
                height: 100px;
                width: 100px;
                background-color: rgb(40%, 40%, 40%);
                margin-bottom: 10px;
            }
            #reference
            {
                color: rgb(40%, 40%, 40%);
                font: 100px/1 Ahem;
            }
        ]]></style>
    </head>
    <body>
        <p>Test passes if there are 2 squares with the <strong>same color</strong>.</p>
        <div id="test"></div>
        <div id="reference">X</div>
    </body>
</html>