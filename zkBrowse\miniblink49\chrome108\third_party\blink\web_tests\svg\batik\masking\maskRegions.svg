<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN"
"http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">

<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at
   
        http://www.apache.org/licenses/LICENSE-2.0
   
   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->

<!-- ========================================================================= -->
<!-- Validates processing of mask regions                                      -->
<!--                                                                           -->
<!-- <AUTHOR>                                         -->
<!-- @version $Id: maskRegions.svg 475477 2006-11-15 22:44:28Z cam $ -->
<!-- ========================================================================= -->

<?xml-stylesheet type="text/css" href="../resources/test.css" ?>

<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="body" width="450" height="500" viewBox="0 0 450 500">
<title>Mask</title>
    <!-- ============================================================= -->
    <!-- Test content                                                  -->
    <!-- ============================================================= -->
    <g id="testContent" class="legend" text-anchor="middle">
        <text x="225" y="50" class="title">
        Mask Regions
        </text>

        <defs>
    
        <!-- ======================================== -->
        <!-- Masked object definition                 -->
        <!-- ======================================== -->
        <linearGradient id="maskedGradient" x1="0" x2="1" y1="0" y2="1" color-interpolation="linearRGB">
            <stop offset="0" stop-color="blue" />
            <stop offset="1" stop-color="red" />
        </linearGradient>

        <rect id="masked" width="100" height="80" fill="url(#maskedGradient)" />

        <!-- ======================================= -->
        <!-- maskUnits: default                      -->
        <!-- maskContentUnits: default               -->
        <!-- maskRegion: default (-.1, -.1, 1.2, 1.2)-->
        <!-- ======================================= -->
        <mask id="mask_1">
            <circle cx="25" cy="20" r="20" fill="#ffffff" />
            <circle cx="25" cy="60" r="20" fill="#ffffff" />
            <circle cx="75" cy="20" r="20" fill="#ffffff" />
            <circle cx="75" cy="60" r="20" fill="#ffffff" />
        </mask>

        <!-- ======================================= -->
        <!-- maskUnits: objectBoundingBox            -->
        <!-- maskContentUnits: default               -->
        <!-- maskRegion: (.25, .25, .5, .5)          -->
        <!-- ======================================= -->
        <mask id="mask_2" maskUnits="objectBoundingBox"
              x=".25" y="25%" width="50%" height=".5">
            <circle cx="25" cy="20" r="20" fill="#ffffff" />
            <circle cx="25" cy="60" r="20" fill="#ffffff" />
            <circle cx="75" cy="20" r="20" fill="#ffffff" />
            <circle cx="75" cy="60" r="20" fill="#ffffff" />
        </mask>

        <!-- ======================================= -->
        <!-- maskUnits: userSpaceOnUse               -->
        <!-- maskContentUnits: default               -->
        <!-- maskRegion: (25, 25, 50, 50)            -->
        <!-- ======================================= -->
        <mask id="mask_3" maskUnits="userSpaceOnUse" 
              x="25" y="20" width="11.11111111%" height="8%">
            <circle cx="25" cy="20" r="20" fill="#ffffff" />
            <circle cx="25" cy="60" r="20" fill="#ffffff" />
            <circle cx="75" cy="20" r="20" fill="#ffffff" />
            <circle cx="75" cy="60" r="20" fill="#ffffff" />
        </mask>

        <!-- ======================================= -->
        <!-- maskUnits: objectBoundingBox            -->
        <!-- maskContentUnits: default               -->
        <!-- maskRegion: (25, 25, 50, 50)            -->
        <!-- ======================================= -->
        <mask id="mask_4" maskUnits="objectBoundingBox" 
              maskContentUnits="objectBoundingBox"
              x="0" y="0" width="100%" height="100%">
            <circle cx=".25" cy=".2" r=".2" fill="#ffffff" />
            <circle cx=".25" cy=".5999999" r=".2" fill="#ffffff" />
            <circle cx=".749999" cy=".2" r=".2" fill="#ffffff" />
            <circle cx=".749999" cy=".5999999" r=".2" fill="#ffffff" />
        </mask>

        </defs>

        <g transform="translate(175, 100)">
            <use xlink:href="#masked" />
            <text x="50" y="-3">Masked</text>
        </g>

        <g transform="translate(85, 220)">
            <use xlink:href="#masked" mask="url(#mask_1)"/>
            <text x="50" y="-3">Mask 1</text>
        </g>

        <g transform="translate(85, 340)">
            <use xlink:href="#masked" mask="url(#mask_2)"/>
            <text x="50" y="-3">Mask 2</text>
        </g>

        <g transform="translate(260, 220)">
            <use xlink:href="#masked" mask="url(#mask_3)"/>
            <text x="50" y="-3">Mask 3</text>
        </g>

        <g transform="translate(260, 340)">
            <use xlink:href="#masked" mask="url(#mask_4)"/>
            <text x="50" y="-3">Mask 4</text>
        </g>

    </g>

    <!-- ============================================================= -->
    <!-- Batik sample mark                                             -->
    <!-- ============================================================= -->
    <use xlink:href="../resources/batikLogo.svg#Batik_Tag_Box" />
    
</svg>
