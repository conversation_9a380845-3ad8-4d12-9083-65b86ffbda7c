Tests bad algorithm inputs for AES-CTR

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".


Importing AES-CTR key...

encrypt() with normal data (control group)...
PASS: Encryption should be [1592076075] and was
Accessed counter
Accessed length
Neutering counter...
PASS counter.byteLength is 0
PASS: Encryption should be [1592076075] and was
PASS successfullyParsed is true

TEST COMPLETE

