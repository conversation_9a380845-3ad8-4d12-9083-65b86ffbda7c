<!DOCTYPE html>
<html>
<head>
</head>
<body>
    <p>Test that window.onerror and "error" event listeners from isolated world are
    invoked for uncaught exceptions in scripts running in isolate worlds as
    well as for exceptions in the main world.<a href="https://bugs.webkit.org/show_bug.cgi?id=8519">Bug 8519.</a>
    </p>
    <pre id="console"></pre>
    <script>
        var expectedRecordCount = 10;
        var recordCount = 0;
        document.getElementById("console").addEventListener("DOMNodeInserted", function(e) {
            if (++recordCount === expectedRecordCount && window.testRunner)
                testRunner.notifyDone();
        }, false);


        var throwExceptions = function(isolatedWorld)
        {
            window.addEventListener("load", function(e) {
                // Do the following call from load listener to make sure error in the setTimeout callback always happens after the error in this listener.
                setTimeout(function() {
                    throw new Error("Error in " + isolatedWorld + " setTimeout callback.");
                }, 0);
                throw new Error("Error in " + isolatedWorld + " load handler.");
            }, false);

            throw new Error("Error in "+ isolatedWorld + " inline script.");
        };

        var errorHandlers = function(isolatedWorld)
        {
            function lastUrlComponent(url) {
                return url ? url.match( /[^\/]+\/?$/ )[0] : url;
            }

            function log(msg) {
                var record = document.createElement("div");
                record.innerHTML = msg;
                document.getElementById("console").appendChild(record);
            }

            window.onerror = function(msg, url, line, column, error) {
                log(isolatedWorld + " window.onerror: " + msg + " at " + lastUrlComponent(url) + ", Line: " + line + ", Column: " + column, "*");
                if (error)
                    log("Error object present!");
                else
                    log("No error object present!");
                return true;
            }

            window.addEventListener("error", function(e) {
                var url = lastUrlComponent(e.filename);
                log(isolatedWorld + " error event listener: " + e.message + " at " + url + ":, Line: " + e.lineno, "*");
                if (e.error)
                    log("Error object present!");
                else
                    log("No error object present!");
                e.preventDefault();
            }, false);
        };

        if (window.testRunner) {
            testRunner.dumpAsText();
            testRunner.waitUntilDone();
            testRunner.evaluateScriptInIsolatedWorld(1, "(" + errorHandlers + ")('isolated world'); (" + throwExceptions + ")('isolated world')");
        }

        throwExceptions("main world");
    </script>
</body>
</html>
