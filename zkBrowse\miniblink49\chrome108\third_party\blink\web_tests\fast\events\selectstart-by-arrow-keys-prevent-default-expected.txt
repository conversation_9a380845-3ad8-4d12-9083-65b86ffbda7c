This test ensures selectstart event fires when selection is created by arrow key and script can prevent the selection change.

If running this test manually, click on the div ("Hello World") and try to select the text using arrow keys.
Expected result: SelectStart event will fire when user starts extending the selection, but due to script preventDefault it prevents the selection change.

Hello World
Check Right arrow + Shift: PASS
Check Right arrow + Shift + Control: PASS
Check End + Shift: PASS

