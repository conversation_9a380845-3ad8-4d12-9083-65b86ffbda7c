Verify that automapping works property when UISourceCodes come and go.


Running: addNetworkResource
Mapping has stabilized.

Running: addFileSystemUISourceCode
Binding created: {
       network: http://example.com/path/foo.js
    fileSystem: file:///var/www/scripts/foo.js
    exactMatch: true
}
Mapping has stabilized.

Running: removeNetworkUISourceCode
Binding removed: {
       network: http://example.com/path/foo.js
    fileSystem: file:///var/www/scripts/foo.js
    exactMatch: true
}
Mapping has stabilized.

Running: reAddNetworkUISourceCode
Binding created: {
       network: http://example.com/path/foo.js
    fileSystem: file:///var/www/scripts/foo.js
    exactMatch: true
}
Mapping has stabilized.

Running: removeFileSystem
Binding removed: {
       network: http://example.com/path/foo.js
    fileSystem: file:///var/www/scripts/foo.js
    exactMatch: true
}
Mapping has stabilized.

