# Copyright 2019 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/android/rules.gni")
import("//components/spellcheck/spellcheck_build_features.gni")
import("//printing/buildflags/buildflags.gni")

java_cpp_enum("browser_enums") {
  sources = [
    "aw_apk_type.h",
    "aw_contents_io_thread_client.cc",
    "aw_renderer_priority.h",
    "aw_settings.h",
    "permission/aw_permission_request.h",
    "safe_browsing/aw_safe_browsing_blocking_page.h",
    "safe_browsing/aw_url_checker_delegate_impl.h",
  ]
}

source_set("browser") {
  sources = [
    "android_protocol_handler.cc",
    "android_protocol_handler.h",
    "aw_apk_type.h",
    "aw_autofill_client.cc",
    "aw_autofill_client.h",
    "aw_browser_context.cc",
    "aw_browser_context.h",
    "aw_browser_main_parts.cc",
    "aw_browser_main_parts.h",
    "aw_browser_permission_request_delegate.h",
    "aw_browser_policy_connector.cc",
    "aw_browser_policy_connector.h",
    "aw_browser_process.cc",
    "aw_browser_process.h",
    "aw_browser_terminator.cc",
    "aw_browser_terminator.h",
    "aw_client_hints_controller_delegate.cc",
    "aw_client_hints_controller_delegate.h",
    "aw_content_browser_client.cc",
    "aw_content_browser_client.h",
    "aw_content_browser_client_receiver_bindings.cc",
    "aw_contents.cc",
    "aw_contents.h",
    "aw_contents_client_bridge.cc",
    "aw_contents_client_bridge.h",
    "aw_contents_io_thread_client.cc",
    "aw_contents_io_thread_client.h",
    "aw_contents_origin_matcher.cc",
    "aw_contents_origin_matcher.h",
    "aw_contents_statics.cc",
    "aw_cookie_access_policy.cc",
    "aw_cookie_access_policy.h",
    "aw_dark_mode.cc",
    "aw_dark_mode.h",
    "aw_debug.cc",
    "aw_devtools_manager_delegate.cc",
    "aw_devtools_manager_delegate.h",
    "aw_devtools_server.cc",
    "aw_devtools_server.h",
    "aw_download_manager_delegate.cc",
    "aw_download_manager_delegate.h",
    "aw_enterprise_authentication_app_link_manager.cc",
    "aw_enterprise_authentication_app_link_manager.h",
    "aw_feature_entries.cc",
    "aw_feature_entries.h",
    "aw_feature_list.cc",
    "aw_feature_list_creator.cc",
    "aw_feature_list_creator.h",
    "aw_field_trials.cc",
    "aw_field_trials.h",
    "aw_form_database.cc",
    "aw_form_database_service.cc",
    "aw_form_database_service.h",
    "aw_http_auth_handler.cc",
    "aw_http_auth_handler.h",
    "aw_javascript_dialog_manager.cc",
    "aw_javascript_dialog_manager.h",
    "aw_media_url_interceptor.cc",
    "aw_media_url_interceptor.h",
    "aw_metrics_service_client_delegate.cc",
    "aw_metrics_service_client_delegate.h",
    "aw_pac_processor.cc",
    "aw_pac_processor.h",
    "aw_pdf_exporter.cc",
    "aw_pdf_exporter.h",
    "aw_permission_manager.cc",
    "aw_permission_manager.h",
    "aw_print_manager.cc",
    "aw_print_manager.h",
    "aw_proxy_controller.cc",
    "aw_quota_manager_bridge.cc",
    "aw_quota_manager_bridge.h",
    "aw_quota_permission_context.cc",
    "aw_quota_permission_context.h",
    "aw_render_process.cc",
    "aw_render_process.h",
    "aw_render_process_gone_delegate.h",
    "aw_renderer_priority.h",
    "aw_resource_context.cc",
    "aw_resource_context.h",
    "aw_settings.cc",
    "aw_settings.h",
    "aw_speech_recognition_manager_delegate.cc",
    "aw_speech_recognition_manager_delegate.h",
    "aw_ssl_host_state_delegate.cc",
    "aw_ssl_host_state_delegate.h",
    "aw_web_contents_delegate.cc",
    "aw_web_contents_delegate.h",
    "aw_web_contents_view_delegate.cc",
    "aw_web_contents_view_delegate.h",
    "aw_web_ui_controller_factory.cc",
    "aw_web_ui_controller_factory.h",
    "component_updater/loader_policies/aw_apps_package_names_allowlist_component_loader_policy.cc",
    "component_updater/loader_policies/aw_apps_package_names_allowlist_component_loader_policy.h",
    "component_updater/loader_policies/empty_component_loader_policy.cc",
    "component_updater/loader_policies/empty_component_loader_policy.h",
    "component_updater/loader_policies/origin_trials_component_loader_policy.cc",
    "component_updater/loader_policies/origin_trials_component_loader_policy.h",
    "component_updater/origin_trials_component_loader.cc",
    "component_updater/origin_trials_component_loader.h",
    "component_updater/registration.cc",
    "component_updater/registration.h",
    "component_updater/trust_token_key_commitments_component_loader.cc",
    "component_updater/trust_token_key_commitments_component_loader.h",
    "cookie_manager.cc",
    "cookie_manager.h",
    "enterprise_authentication_app_link_policy_handler.cc",
    "enterprise_authentication_app_link_policy_handler.h",
    "find_helper.cc",
    "find_helper.h",
    "icon_helper.cc",
    "icon_helper.h",
    "js_java_interaction/aw_web_message_host_factory.cc",
    "js_java_interaction/aw_web_message_host_factory.h",
    "js_java_interaction/js_reply_proxy.cc",
    "js_java_interaction/js_reply_proxy.h",
    "network_service/aw_network_change_notifier.cc",
    "network_service/aw_network_change_notifier.h",
    "network_service/aw_network_change_notifier_factory.cc",
    "network_service/aw_network_change_notifier_factory.h",
    "network_service/aw_proxy_config_monitor.cc",
    "network_service/aw_proxy_config_monitor.h",
    "network_service/aw_proxying_restricted_cookie_manager.cc",
    "network_service/aw_proxying_restricted_cookie_manager.h",
    "network_service/aw_proxying_url_loader_factory.cc",
    "network_service/aw_proxying_url_loader_factory.h",
    "network_service/aw_url_loader_throttle.cc",
    "network_service/aw_url_loader_throttle.h",
    "network_service/aw_web_resource_intercept_response.cc",
    "network_service/aw_web_resource_intercept_response.h",
    "network_service/aw_web_resource_request.cc",
    "network_service/aw_web_resource_request.h",
    "network_service/net_helpers.cc",
    "network_service/net_helpers.h",
    "page_load_metrics/aw_page_load_metrics_memory_tracker_factory.cc",
    "page_load_metrics/aw_page_load_metrics_memory_tracker_factory.h",
    "page_load_metrics/aw_page_load_metrics_provider.cc",
    "page_load_metrics/aw_page_load_metrics_provider.h",
    "page_load_metrics/page_load_metrics_initialize.cc",
    "page_load_metrics/page_load_metrics_initialize.h",
    "permission/aw_permission_request.cc",
    "permission/aw_permission_request.h",
    "permission/aw_permission_request_delegate.cc",
    "permission/aw_permission_request_delegate.h",
    "permission/media_access_permission_request.cc",
    "permission/media_access_permission_request.h",
    "permission/permission_callback.h",
    "permission/permission_request_handler.cc",
    "permission/permission_request_handler.h",
    "permission/permission_request_handler_client.cc",
    "permission/permission_request_handler_client.h",
    "permission/simple_permission_request.cc",
    "permission/simple_permission_request.h",
    "popup_touch_handle_drawable.cc",
    "popup_touch_handle_drawable.h",
    "renderer_host/auto_login_parser.cc",
    "renderer_host/auto_login_parser.h",
    "renderer_host/aw_render_view_host_ext.cc",
    "renderer_host/aw_render_view_host_ext.h",
    "safe_browsing/aw_ping_manager_factory.cc",
    "safe_browsing/aw_ping_manager_factory.h",
    "safe_browsing/aw_safe_browsing_allowlist_manager.cc",
    "safe_browsing/aw_safe_browsing_allowlist_manager.h",
    "safe_browsing/aw_safe_browsing_blocking_page.cc",
    "safe_browsing/aw_safe_browsing_blocking_page.h",
    "safe_browsing/aw_safe_browsing_navigation_throttle.cc",
    "safe_browsing/aw_safe_browsing_navigation_throttle.h",
    "safe_browsing/aw_safe_browsing_ui_manager.cc",
    "safe_browsing/aw_safe_browsing_ui_manager.h",
    "safe_browsing/aw_url_checker_delegate_impl.cc",
    "safe_browsing/aw_url_checker_delegate_impl.h",
    "state_serializer.cc",
    "state_serializer.h",
    "tracing/aw_background_tracing_metrics_provider.cc",
    "tracing/aw_background_tracing_metrics_provider.h",
    "tracing/aw_trace_event_args_allowlist.cc",
    "tracing/aw_trace_event_args_allowlist.h",
    "tracing/aw_tracing_controller.cc",
    "tracing/aw_tracing_controller.h",
    "tracing/aw_tracing_delegate.cc",
    "tracing/aw_tracing_delegate.h",
    "tracing/background_tracing_field_trial.cc",
    "tracing/background_tracing_field_trial.h",
  ]

  deps = [
    "//android_webview:browser_jni_headers",
    "//android_webview:generate_components_strings",
    "//android_webview/browser/gfx",
    "//android_webview/browser/lifecycle",
    "//android_webview/browser/metrics",
    "//android_webview/browser/variations",
    "//android_webview/common",
    "//android_webview/common:mojom",
    "//android_webview/proto:aw_variations_seed_proto",
    "//base",
    "//components/android_autofill/browser:android",
    "//components/autofill/content/browser",
    "//components/cdm/browser",
    "//components/component_updater/android:embedded_component_loader",
    "//components/component_updater/android:loader_policies",
    "//components/component_updater/installer_policies",
    "//components/content_capture/android",
    "//components/content_capture/browser",
    "//components/embedder_support:browser_util",
    "//components/embedder_support:embedder_support",
    "//components/embedder_support/android:util",
    "//components/embedder_support/origin_trials",
    "//components/favicon_base:favicon_base",
    "//components/flags_ui",
    "//components/keyed_service/content",
    "//components/optimization_guide/core:bloomfilter",
    "//components/profile_metrics",
    "//components/strings:components_strings_grit",
    "//components/url_matcher",

    # Called via JNI in CrashpadMain
    "//components/crash/android:crashpad_main",
    "//components/crash/content/browser",
    "//components/crash/core/app",
    "//components/embedder_support/android:web_contents_delegate",
    "//components/embedder_support/android/metrics",
    "//components/google/core/common",
    "//components/heap_profiling/multi_process",
    "//components/js_injection/browser",
    "//components/js_injection/common",
    "//components/js_injection/common:common_mojom",
    "//components/metrics",
    "//components/metrics:component_metrics",
    "//components/minidump_uploader",
    "//components/navigation_interception",
    "//components/page_load_metrics/browser",
    "//components/permissions",
    "//components/policy:generated",
    "//components/policy/content/",
    "//components/policy/core/browser",
    "//components/pref_registry",
    "//components/prefs",
    "//components/safe_browsing/android:remote_database_manager",
    "//components/safe_browsing/content/browser",
    "//components/safe_browsing/content/browser/triggers",
    "//components/safe_browsing/content/browser/web_ui",
    "//components/safe_browsing/content/common:interfaces",
    "//components/safe_browsing/core/browser",
    "//components/safe_browsing/core/browser/db:database_manager",
    "//components/safe_browsing/core/browser/db:safebrowsing_proto",
    "//components/safe_browsing/core/common",
    "//components/security_interstitials/content:security_interstitial_page",
    "//components/security_interstitials/core",
    "//components/services/heap_profiling/public/cpp",
    "//components/spellcheck:buildflags",
    "//components/tracing:background_tracing_metrics_provider",
    "//components/tracing:background_tracing_utils",
    "//components/tracing:startup_tracing",
    "//components/url_formatter",
    "//components/user_prefs",
    "//components/variations",
    "//components/variations/service",
    "//components/version_info",
    "//components/version_info/android:channel_getter",
    "//components/visitedlink/browser",
    "//components/webdata/common",
    "//content/public/browser",
    "//media/mojo:buildflags",
    "//services/cert_verifier/public/mojom",
    "//services/network/public/mojom",
    "//services/proxy_resolver:lib",
    "//third_party/blink/public/common",
    "//third_party/blink/public/mojom:mojom_core",
    "//third_party/blink/public/mojom:mojom_platform",
    "//third_party/crashpad/crashpad/client",
    "//ui/android",
    "//ui/display/util:util",
    "//ui/gl",
    "//ui/resources",
    "//ui/touch_selection",
    "//url:gurl_android",
  ]

  if (enable_basic_printing) {
    deps += [
      "//components/printing/browser",
      "//components/printing/common",
      "//components/printing/common:mojo_interfaces",
      "//printing",
    ]
  }

  if (enable_spellcheck) {
    deps += [ "//components/spellcheck/browser" ]
  }

  configs += [
    "//tools/v8_context_snapshot:use_v8_context_snapshot",
    "//v8:external_startup_data",
  ]
}
