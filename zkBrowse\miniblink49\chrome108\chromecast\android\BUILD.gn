# Copyright 2015 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/android/abi.gni")
import("//build/config/sysroot.gni")
import("//build/partitioned_shared_library.gni")
import("//chromecast/chromecast.gni")
import("//media/media_options.gni")

# These targets shall only be referenced on Android builds.
assert(is_android)

# Deps shared by libcast_browser_android and libcast_shell_android.
group("common_apk_deps") {
  public_deps = [
    "//base",
    "//chromecast:cast_shell_jni_registration",
    "//chromecast:chromecast_buildflags",
    "//chromecast/app",
    "//chromecast/app:cast_crash_client",
    "//chromecast/base",
    "//chromecast/base:android_create_sys_info",
    "//chromecast/base:jni_headers",
    "//chromecast/base/metrics",
    "//chromecast/browser",
    "//chromecast/media/cma/backend/android:cast_media_android",
    "//components/crash/android:crash_android",
    "//components/minidump_uploader",
    "//components/module_installer/android:native",
    "//content/public/app",
    "//content/public/browser",
    "//skia",
  ]

  # Explicit dependencies required for JNI registration to be able to find the
  # native side functions.
  if (is_component_build) {
    public_deps += [
      "//device/bluetooth",
      "//device/gamepad",
      "//media/midi",
      "//ui/android",
      "//ui/events/devices",
      "//ui/shell_dialogs",
    ]
  }
}

shared_library("libcast_shell_android") {
  # TODO: Remove the ldflags after migrating away from protobuf_lite to
  # protobuf_full.
  ldflags = [ "-Wl,-z,muldefs" ]

  sources = [ "//chromecast/app/android/cast_jni_loader.cc" ]
  configs += [ "//chromecast:cast_config" ]
  configs -= [ "//build/config/android:hide_all_but_jni_onload" ]

  deps = [
    ":common_apk_deps",
    "//chromecast:cast_shell_lib",
  ]
}

shared_library("libcast_browser_android") {
  # TODO: Remove the ldflags after migrating away from protobuf_lite to
  # protobuf_full.
  ldflags = [ "-Wl,-z,muldefs" ]

  sources = [ "//chromecast/app/android/cast_jni_loader.cc" ]
  configs += [ "//chromecast:cast_config" ]
  configs -= [ "//build/config/android:hide_all_but_jni_onload" ]

  deps = [
    ":common_apk_deps",
    "//chromecast:cast_shell_lib_simple",
    "//components/module_installer/android:native",
  ]
}

group("native") {
  deps = [ ":cast_browser" ]
}

component("cast_browser") {
  # TODO: Remove the ldflags after migrating away from protobuf_lite to
  # protobuf_full.
  ldflags = [ "-Wl,-z,muldefs" ]

  sources = [ "//chromecast/app/android/cast_browser_module_entrypoint.cc" ]

  deps = [
    ":common_apk_deps",
    "//chromecast:cast_shell_lib_simple",
  ]

  cflags = [ "-fsymbol-partition=cast_browser_partition" ]
}

# These are all known //third_party/android_deps targets that chromecast
# internal code depends on. Reference these targets so if anyone wants to
# remove these targets upstream, they need to at least add someone from
# //chromecast OWNERS as a reviewer to make sure it's safe.
#
# This list was generated from chromecast/internal using:
#
# find | grep BUILD.gn | xargs grep -hi '//third_party/android_deps:' | \
# grep -v "deps =" | sed 's/ *//g' | sort | uniq
group("internal_android_deps") {
  testonly = true
  public_deps = [
    "//third_party/android_deps:com_android_support_design_java",
    "//third_party/android_deps:protobuf_lite_runtime_java",
    "//third_party/androidx:androidx_annotation_annotation_java",
    "//third_party/androidx:androidx_core_core_java",
    "//third_party/androidx:androidx_fragment_fragment_java",
    "//third_party/androidx:androidx_leanback_leanback_preference_java",
    "//third_party/androidx:androidx_legacy_legacy_support_v4_java",
    "//third_party/androidx:androidx_lifecycle_lifecycle_common_java",
    "//third_party/androidx:androidx_lifecycle_lifecycle_viewmodel_java",
    "//third_party/androidx:androidx_localbroadcastmanager_localbroadcastmanager_java",
    "//third_party/androidx:androidx_media_media_java",
    "//third_party/androidx:androidx_mediarouter_mediarouter_java",
    "//third_party/androidx:androidx_preference_preference_java",
    "//third_party/androidx:androidx_recyclerview_recyclerview_java",
    "//third_party/androidx:androidx_slice_slice_builders_java",
    "//third_party/androidx:androidx_slice_slice_core_java",
    "//third_party/androidx:androidx_test_core_java",
    "//third_party/androidx:androidx_tvprovider_tvprovider_java",
  ]
}

# Util to copy libc++_shared.so to APK.
copy("copy_libcxx_shared") {
  sources = [ "${sysroot}/usr/lib/${android_abi_target}/libc++_shared.so" ]
  outputs = [ "${root_out_dir}/libc++_shared.so" ]
  metadata = {
    shared_libraries = [ "${root_out_dir}/libc++_shared.so" ]
  }
}
