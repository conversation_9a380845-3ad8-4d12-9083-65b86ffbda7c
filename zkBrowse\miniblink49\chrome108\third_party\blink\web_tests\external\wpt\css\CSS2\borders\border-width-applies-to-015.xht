<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Border-width applied to element with display table-caption</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="G<PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-06-25 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#propdef-border-width" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#border-width-properties" />
		    <link rel="match" href="border-width-applies-to-009-ref.xht" />

        <meta name="assert" content="The 'border-width' property applies to elements with a display of table-caption." />
        <style type="text/css">
            #test
            {
                border-style: solid;
                border-width: 1in;
                display: table-caption;
                height: 1in;
                width: 1in;
            }
            #table
            {
                display: table;
            }
            #row
            {
                display: table-row;
            }
            #cell
            {
                display: table-cell;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is a large square with four black sides that have the same thickness. (Note: Such large square must be surrounding a smaller filled white square.)</p>
        <div id="table">
            <div id="test"></div>
            <div id="row">
                <div id="cell"></div>
            </div>
        </div>
    </body>
</html>