// Copyright 2020 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// TODO: crbug/1082836
// This file is currently manually tweaked for Chromium use. Find a
// way to automatically sync from source and make the following necessary
// changes:
//   1) Add "option optimize_for = LITE_RUNTIME;" to avoid link error.
//   2) Remove Google internal options:
//        "option java_api_version = 2;"
//        "option java_enable_dual_generate_mutable_api = true;"
//        "option cc_api_version = 2;"
//        "option java_use_javaproto2 = true;"
//        "option java_java5_enums = true;"
//   3) Update the copyright section.
//

syntax = "proto2";
option optimize_for = LITE_RUNTIME;
package wireless_android_enterprise_devicemanagement;

import "message_set.proto";

option java_package = "com.google.wireless.android.enterprise.devicemanagement";

// Disable ProtoBestPractices for enum_default_value, as the values in the enums
// below match the values as defined in Omaha:
//   http://cs/search/?q=f:omaha+f:generate.*admx+%5EADMX_POLICIES&m=25l&type=cs
// This also applies to boolean values from Omaha that are defined as enums
// here.
//
// ProtoBestPractices.ignore_findings = "enum_default_value"

enum UpdateValue {
  // Updates disabled
  UPDATES_DISABLED = 0;
  // Always allow updates (recommended)
  UPDATES_ENABLED = 1;
  // Manual updates only
  MANUAL_UPDATES_ONLY = 2;
  // Automatic silent updates only
  AUTOMATIC_UPDATES_ONLY = 3;
}

enum InstallValue {
  INSTALL_DISABLED = 0;
  INSTALL_ENABLED = 1;
}

enum RollbackToTargetVersionValue {
  ROLLBACK_TO_TARGET_VERSION_DISABLED = 0;
  ROLLBACK_TO_TARGET_VERSION_ENABLED = 1;
}

message UpdatesSuppressed {
  optional int64 start_hour = 1;
  optional int64 start_minute = 2;
  optional int64 duration_min = 3;
}

message ApplicationSettings {
  // Application GUID for use on Windows, e.g.
  // '{8A69D345-D564-463C-AFF1-A69D9E530F96}'.
  optional string app_guid = 1;
  // Bundle identifier for use on Mac, e.g. 'com.google.Chrome'.
  optional string bundle_identifier = 2;

  // Allow installation
  //
  // Specifies whether <app> can be installed using Google Update/Google
  // Installer.
  //
  // If this policy is not configured, <app> can be installed as specified by
  // "Allow installation default".
  optional InstallValue install = 3;

  // Update policy override
  //
  // Specifies how Google Update handles available <app> updates from Google.
  //
  // If this policy is not configured, Google Update handles available updates
  // as specified by "Update policy override default".
  //
  // Options:
  //  - Always allow updates: Updates are always applied when found, either by
  //  periodic update check or by a manual update check.
  //  - Manual updates only: Updates are only applied when the user does a
  //  manual update check. (Not all apps provide an interface  for this.)
  //  - Automatic silent updates only: Updates are only applied when they are
  //  found via the periodic update check.
  //  - Updates disabled: Never apply updates.
  //
  // If you select manual updates, you should periodically check for updates
  // using the application's manual update mechanism if available. If you
  // disable updates, you should periodically check for updates and distribute
  // them to users.
  optional UpdateValue update = 4;

  // Target version prefix override
  //
  // Specifies which version <app> should be updated to.
  //
  // When this policy is enabled, the app will be updated to the version
  // prefixed with this policy value.
  //
  // Some examples:
  // 1) Not configured: app will be updated to the latest version available.
  // 2) Policy value is set to "55.": the app will be updated to any minor
  // version of 55 (e.g., 55.24.34 or 55.60.2). 3) Policy value is "55.2.": the
  // app will be updated to any minor version of 55.2 (e.g., 55.2.34 or 55.2.2).
  // 4) Policy value is "55.24.34": the app will be updated to this specific
  // version only.
  optional string target_version_prefix = 5;

  // Rollback to Target version
  //
  // Specifies that Google Update should roll installations of <app> back to the
  // version indicated by "Target version prefix override".
  //
  // This policy setting has no effect unless "Target version prefix override"
  // is set.
  //
  // If this policy is not configured or is disabled, installs that have a
  // version higher than that specified by "Target version prefix override" will
  // be left as-is.
  //
  // If this policy is enabled, installs that have a version higher than that
  // specified by "Target version prefix override" will be downgraded to the
  // highest available version that matches the target version.
  optional RollbackToTargetVersionValue rollback_to_target_version = 6;

  // Gcpw specific application setting which contains a list of domains from
  // which the user is allowed to login.
  optional GcpwSpecificApplicationSettings gcpw_application_settings = 7;

  // Target Channel
  //
  // Specifies the channel to which <app> should be updated.
  //
  // When this policy is set, the binaries returned by Google Update will be the
  // binaries for the specified channel. If this policy is not set, the default
  // channel will be used.
  optional string target_channel = 8;
}

message GcpwSpecificApplicationSettings {
  // List of domains from which the user is allowed to login.
  repeated string domains_allowed_to_login = 1;
}

message OmahaSettingsClientProto {
  extend proto2.bridge.MessageSet {
    optional OmahaSettingsClientProto message_set_extension = 276737523;
  }

  reserved 1 to 10;

  // Auto-update check period override
  //
  // Minimum number of minutes between automatic update checks.
  //
  // Set the value to 0 if you want to disable all auto-update checks (not
  // recommended).
  optional int64 auto_update_check_period_minutes = 11;

  // Download URL class override
  //
  // If enabled, the Google Update server will attempt to provide cache-friendly
  // URLs for update payloads in its responses.
  optional string download_preference = 12;

  // Time period in each day to suppress auto-update check
  //
  // If this setting is enabled, update checks will be suppressed during each
  // day starting from Hour:Minute for a period of Duration (in minutes).
  // Duration does not account for daylight savings time. So for instance, if
  // the start time is 22:00, and with a duration of 480 minutes, the updates
  // will be suppressed for 8 hours regardless of whether daylight savings time
  // changes happen in between.
  optional UpdatesSuppressed updates_suppressed = 13;

  // Choose how to specify proxy server settings
  //
  // Allows you to specify the proxy server used by Google Update.
  //
  // If you choose to never use a proxy server and always connect directly, all
  // other options are ignored.
  //
  // If you choose to use system proxy settings or auto detect the proxy server,
  // all other options are ignored.
  //
  // If you choose fixed server proxy mode, you can specify further options in
  // 'Address or URL of proxy server'.
  //
  // If you choose to use a .pac proxy script, you must specify the URL to the
  // script in 'URL to a proxy .pac file'.
  //
  // Enum with string values; must be one of ['direct', 'auto_detect',
  // 'pac_script', 'fixed_servers', 'system'].
  optional string proxy_mode = 14;

  // Address or URL of proxy server
  //
  // You can specify the URL of the proxy server here.
  //
  // This policy only takes effect if you have selected manual proxy settings at
  // 'Choose how to specify proxy server settings'.
  optional string proxy_server = 15;

  // URL to a proxy .pac file
  //
  // You can specify a URL to a proxy .pac file here.
  //
  // This policy only takes effect if you have selected manual proxy settings at
  // 'Choose how to specify proxy server settings'.
  optional string proxy_pac_url = 16;

  // Allow installation default
  //
  // Specifies the default behavior for whether Google software can be installed
  // using Google Update/Google Installer.
  //
  // Can be overridden by the "Allow installation" for individual applications.
  //
  // Only affects installation of Google software using Google Update/Google
  // Installer. Cannot prevent running the application installer directly or
  // installation of Google software that does not use Google Update/Google
  // Installer for installation.
  optional InstallValue install_default = 17;

  // Update policy override default
  //
  // Specifies the default policy for software updates from Google.
  //
  // Can be overridden by the "Update policy override" for individual
  // applications.
  //
  // Options:
  //  - Always allow updates: Updates are always applied when found, either by
  //  periodic update check or by a manual update check.
  //  - Manual updates only: Updates are only applied when the user does a
  //  manual update check. (Not all apps provide an interface for this.)
  //  - Automatic silent updates only: Updates are only applied when they are
  //  found via the periodic update check.
  //  - Updates disabled: Never apply updates.
  //
  // If you select manual updates, you should periodically check for updates
  // using each application's manual update mechanism if available. If you
  // disable updates, you should periodically check for updates and distribute
  // them to users.
  //
  // Only affects updates for Google software that uses Google Update for
  // updates. Does not prevent auto-updates of Google software that does not use
  // Google Update for updates.
  //
  // Updates for Google Update are not affected by this setting; Google Update
  // will continue to update itself while it is installed.
  //
  // WARNING: Disabing updates will also prevent updates of any new Google
  // applications released in the future, possibly including dependencies for
  // future versions of installed applications.
  optional UpdateValue update_default = 18;

  // Application-specific settings.
  repeated ApplicationSettings application_settings = 19;
}
