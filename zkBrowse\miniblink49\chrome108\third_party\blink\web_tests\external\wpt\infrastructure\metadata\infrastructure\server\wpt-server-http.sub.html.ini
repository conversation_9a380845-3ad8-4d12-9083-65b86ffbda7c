[wpt-server-http.sub.html]
  [HTTPS protocol, punycode subdomain #1]
    expected:
      if product == "epiphany" or product == "webkit": FAIL

  [HTTPS protocol, www subdomain #1]
    expected:
      if product == "epiphany" or product == "webkit": FAIL

  [HTTPS protocol, www subdomain #2]
    expected:
      if product == "epiphany" or product == "webkit": FAIL

  [HTTPS protocol, www subdomain #3]
    expected:
      if product == "epiphany" or product == "webkit": FAIL

  [HTTPS protocol, punycode subdomain #2]
    expected:
      if product == "epiphany" or product == "webkit": FAIL

  [H2 protocol, no subdomain]
    expected:
      if python_version == 3: FAIL

  [H2 protocol, www subdomain #1]
    expected:
      if python_version == 3: FAIL

  [H2 protocol, www subdomain #2]
    expected:
      if python_version == 3: FAIL

  [H2 protocol, www subdomain #3]
    expected:
      if python_version == 3: FAIL

  [H2 protocol, punycode subdomain #1]
    expected:
      if python_version == 3: FAIL

  [H2 protocol, punycode subdomain #2]
    expected:
      if python_version == 3: FAIL

