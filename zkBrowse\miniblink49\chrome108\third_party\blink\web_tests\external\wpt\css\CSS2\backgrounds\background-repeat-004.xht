<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background-repeat set to 'no-repeat'</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="<PERSON><PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-05-04 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background-repeat" />
        <link rel="match" href="background-repeat-004-ref.xht" />

        <meta name="flags" content="image" />
        <meta name="assert" content="Background-repeat set to 'no-repeat' does not repeat the image." />
        <style type="text/css">
            div
            {
                background-color: black;
                background-image: url("support/blue15x15.png");
                background-repeat: no-repeat;
                height: 200px;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is a small filled blue square in the upper-left corner of the filled black rectangle.</p>
        <div></div>
    </body>
</html>