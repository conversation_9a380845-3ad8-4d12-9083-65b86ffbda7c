<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.3.5 background-attachment</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
BODY {
  background-image: url(../resources/bg.gif);
  background-repeat: repeat-x;
  background-attachment: fixed;
  overflow: hidden;
}
</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>BODY {background-image: url(../resources/bg.gif); background-repeat: repeat-x; background-attachment: fixed;}

</PRE>
<HR>
<P>
This document should have a green grid-pattern line across the top of the page (or at least a tiled background) which does NOT scroll with the document.  It should, instead, appear to be a fixed pattern past which the content scrolls, even when the end of the page is reached.  In addition, the default Test Suite background should NOT appear, as it's been overridden by the styles shown above.  I'll have to add a lot of extra text to the page in order to make all this something we can actually check.  Don't worry, I'll think of something.
</P>
<P>
In fact, why not the relevant section from the CSS1 specification?  A capital idea.
</P>
<HR>
<HR>
<H4>
 <A NAME="background-attachment">5.3.5 &nbsp;&nbsp; 'background-attachment'</A>
</H4>
<P>
<EM>Value:</EM> scroll | fixed<BR>
<EM>Initial:</EM> scroll<BR>
<EM>Applies to:</EM> all elements<BR>
<EM>Inherited:</EM> no<BR>
<EM>Percentage values:</EM> N/A<BR>
<P>
If a background image is specified, the value of 'background-attachment' determines if it is fixed with regard to the canvas or if it scrolls along with the content.
<PRE>
  BODY { 
    background: red url(pendant.gif);
    background-repeat: repeat-y;
    background-attachment: fixed;
  }
</PRE>
<P>
<EM>CSS1 core:</EM> UAs may treat 'fixed' as 'scroll'. However, it is recommended they interpret 'fixed' correctly, at least on the HTML and BODY elements, since there is no way for an author to provide an image only for those browsers that support 'fixed'.
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P>
This document should have a green grid-pattern line across the top of the page (or at least a tiled background) which does NOT scroll with the document.  It should, instead, appear to be a fixed pattern past which the content scrolls, even when the end of the page is reached.  In addition, the default Test Suite background should NOT appear, as it's been overridden by the styles shown above.  I'll have to add a lot of extra text to the page in order to make all this something we can actually check.  Don't worry, I'll think of something.
</P>
<P>
In fact, why not the relevant section from the CSS1 specification?  A capital idea.
</P>
<HR>
<HR>
<H4>
 <A NAME="background-attachment">5.3.5 &nbsp;&nbsp; 'background-attachment'</A>
</H4>
<P>
<EM>Value:</EM> scroll | fixed<BR>
<EM>Initial:</EM> scroll<BR>
<EM>Applies to:</EM> all elements<BR>
<EM>Inherited:</EM> no<BR>
<EM>Percentage values:</EM> N/A<BR>
<P>
If a background image is specified, the value of 'background-attachment' determines if it is fixed with regard to the canvas or if it scrolls along with the content.
<PRE>
  BODY { 
    background: red url(pendant.gif);
    background-repeat: repeat-y;
    background-attachment: fixed;
  }
</PRE>
<P>
<EM>CSS1 core:</EM> UAs may treat 'fixed' as 'scroll'. However, it is recommended they interpret 'fixed' correctly, at least on the HTML and BODY elements, since there is no way for an author to provide an image only for those browsers that support 'fixed'.
</P>
</TD></TR></TABLE></BODY>
</HTML>
