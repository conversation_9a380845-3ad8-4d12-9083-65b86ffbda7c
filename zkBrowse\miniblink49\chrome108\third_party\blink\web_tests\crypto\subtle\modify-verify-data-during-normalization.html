<!DOCTYPE html>
<html>
<head>
<script src="../../resources/js-test.js"></script>
<script src="resources/common.js"></script>
</head>
<body>
<p id="description"></p>
<div id="console"></div>

<script>
description("Tests crypto.subtle.verify() using a BufferSource that is modified during algorithm normalization");

jsTestIsAsync = true;

var publicKeyJSON = {
    kty: "RSA",
    alg: "RS256",
    n: Base64URL.stringify(hexStringToUint8Array("ADC0940AFECE7351D56A6D432210B3AA49D38566B03A9F102E4F198B2DA9D740728D01426A3A058B2B805A5F91D565D969FE318AD2D1ADA713F5A829CC8CDCF8C6CB4872068164063B6D651A2226CB97ED67E0FC6C702A473DB2D79A730F8738084A2EED74922C3A119D1D101B932C0E10FAB36815F66C0792BB640B1B4C59D062FBBEDAB3CC069A535195D70E4A06432CAF149C24A00353A0B99F7CF5B17273CB4E38421BD315127CF4B3DCB3D20A7C98CFAF1A0E398A55E347FA283CE7B39273259B1B2132DC18B0EB8AAE9F78EE525356B09DF39E090E76D7985B2B71E50AF85CA36CE91F8CCB2ABBD8A529D369890D98A2CA2825C4C2FF8B7FBF09E79C0B")),
    e: Base64URL.stringify(hexStringToUint8Array("010001")),
};

var data = asciiToUint8Array("Hello, world!");

var signature = hexStringToUint8Array("0fd9a8aef4cc1876c0b762545336c6d1fb315ae16ae4b5e4bf34d384d8585ea7a01e76ea09ee7f7ee8d1c122e7dd15b7c94a573b2aa07203e8d13bc6fd16156cd8e5f0c15a15dccb62d152127fca09882fb53bc3e60ab586d15b95cf411e3aab4a1c231a7e91aab09ee3d4b13d11e97505ddff77683470da510ee76e8bd530c56a85f901626a5a710f716f113dfe9cf6c473ee16fa248aea3480a1033abe30f4c1243289a661e64d7818b55698280688097135968c6d4b029496d85cab2a67e4696737781f70e4392c7df71bbd6c92465947f029a1de48160aced11b5721b1cd25039fe2c16c2b38de73df3b9a83e3ea755fd0cfe51ca06b61fadf6d84677f95");

var publicKey = null;

function corruptData()
{
    debug("Corrupting data...");
    data[0] = 0;
    data[1] = 0;
}

var extractable = true;

debug("Importing RSA public key...");
crypto.subtle.importKey("jwk", publicKeyJSON, { name: "RSASSA-PKCS1-v1_5", hash: "SHA-256" }, extractable, ["verify"]).then(function(result) {
    publicKey = result;

    // This algorithm has a custom getter that modifies |data|.
    var algorithm = {
        get name() {
            debug("Accessed name property");
            corruptData();
            return "RSASSA-PKCS1-v1_5";
        }
    };
    
    debug("\nVerifying the signature...");
    return crypto.subtle.verify(algorithm, publicKey, signature, data);
}).then(function(result) {
    // Despite modifying the data, verification should succeed.
    verificationResult = result;
    shouldBe("verificationResult", "true");

    debug("\nVerifying the signature (again)...");
    return crypto.subtle.verify("RSASSA-PKCS1-v1_5", publicKey, signature, data);
}).then(function(result) {
    // This time around expect verification to have failed.
    verificationResult = result;
    shouldBe("verificationResult", "false");
}).then(finishJSTest, failAndFinishJSTest);
</script>

</body>
</html>
