<html>
<head>
    <script>
    function init() {
        if (!window.internals)
            return;

        internals.setPageScaleFactor(2);
    }
    window.onload = init;
    </script>
<style>
    ::-webkit-scrollbar {
        width: 0px;
        height: 0px;
    }
</style>
</head>
<p>This test passes if both of the circles have sharp edges.</p>
<p>To run manually, use full-page zoom to increase the page scale factor.</p>
<img style="width: 128px;" src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='128' height='128'><circle fill='black' cx='64' cy='64' r='64'/></svg>">
<img style="width: 128px;" src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 128 128'><circle fill='black' cx='64' cy='64' r='64'/></svg>">

</html>
