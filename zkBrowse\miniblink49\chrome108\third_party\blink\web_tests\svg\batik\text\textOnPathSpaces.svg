<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN"
"http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">

<!--

   Copyright 2001  The Apache Software Foundation 

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

-->
<!-- ========================================================================= -->
<!-- Tests various text on a path                                              -->
<!--                                                                           -->
<!-- <AUTHOR>                                      -->
<!-- @version $Id: textOnPathSpaces.svg,v 1.4 2004/08/18 07:12:23 vhardy Exp $        -->
<!-- ========================================================================= -->
<?xml-stylesheet type="text/css" href="../resources/test.css" ?>  

<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="body" width="450" height="500" viewBox="0 0 450 500">
<title>Text on a path with spaces test</title>

    <style type="text/css"><![CDATA[
        
    ]]></style>

    <g id="content">

        <text class="title" x="50%" y="40">Text on a path with adjusted spacing</text>

        <defs>
	      <path id="Path1" style="fill:none; stroke:blue;" transform="scale(0.15,0.15)"
                  d="M 100 200 C 200 100 300 0 400 100 C 500 200 600 300 700 200 C 800 100 900 100 900 100"/>
            <path id="Path2" style="fill:none; stroke:blue;" transform="scale(0.30,0.50)"
                  d="M 100 100 C100 0 400 00 400 100"/>

        </defs>


        <g transform="translate(0,70)">
            <use xlink:href="#Path1" style="fill:none; stroke:blue; stroke-width:2"/>
	      <text font-size="20">
                <textPath xlink:href="#Path1"><tspan>sample</tspan></textPath>
            </text>
		<text font-size="10" x="35" y="60">default spacing</text>
         </g>

	  <g transform="translate(150,70)">
            <use xlink:href="#Path1" style="fill:none; stroke:blue; stroke-width:2"/>
	      <text font-size="20">
                <textPath xlink:href="#Path1"><tspan x="10,30,50,75,95,110" y="110">sample</tspan></textPath>
            </text>
            <text font-size="10" x="15" y="60">tspan x="10,30,50,75,95,110"</text>
            <text font-size="10" x="35" y="70">y="110"</text>
         </g>
        
         <g transform="translate(300,70)">
            <use xlink:href="#Path1" style="fill:none; stroke:blue; stroke-width:2"/>
	      <text font-size="20">
                <textPath xlink:href="#Path1"><tspan dx="0,10,10,10,10,10">sample</tspan></textPath>
            </text>
		<text font-size="10" x="15" y="60">tspan dx="0,10,10,10,10,10"</text>
         </g>


         <g transform="translate(0,160)">
            <use xlink:href="#Path1" style="fill:none; stroke:blue; stroke-width:2"/>
	      <text font-size="20">
                <textPath xlink:href="#Path1" textLength="140">sample</textPath>
            </text>
            <text font-size="10" x="35" y="60">textLength="140"</text>
         </g>


         <g transform="translate(150,160)">
            <use xlink:href="#Path1" style="fill:none; stroke:blue; stroke-width:2"/>
	      <text font-size="20">
                <textPath xlink:href="#Path1" textLength="70">sample</textPath>
            </text>
            <text font-size="10" x="35" y="60">textLength="70"</text>
         </g>

        <g transform="translate(0,250)">
            <use xlink:href="#Path1" style="fill:none; stroke:blue; stroke-width:2"/>
	      <text font-size="20">
                <textPath xlink:href="#Path1" textLength="140" lengthAdjust="spacingAndGlyphs">sample</textPath>
            </text>
            <text font-size="10" x="35" y="60">textLength="140"</text>
            <text font-size="10" x="35" y="70">lengthAdjust=</text>
            <text font-size="10" x="35" y="80">"spacingAndGlyphs"</text>
         </g>

         <g transform="translate(150,250)">
            <use xlink:href="#Path1" style="fill:none; stroke:blue; stroke-width:2"/>
	      <text font-size="20">
                <textPath xlink:href="#Path1" textLength="50" lengthAdjust="spacingAndGlyphs">sample</textPath>
            </text>
            <text font-size="10" x="35" y="60">textLength="50"</text>
            <text font-size="10" x="35" y="70">lengthAdjust=</text>
            <text font-size="10" x="35" y="80">"spacingAndGlyphs"</text>
         </g>

         <g transform="translate(0,350)">
            <use xlink:href="#Path1" style="fill:none; stroke:blue; stroke-width:2"/>
	      <text font-size="20">
                <textPath xlink:href="#Path1" letter-spacing="-3">sample</textPath>
            </text>
            <text font-size="10" x="35" y="60">letter-spacing="-3"</text>
         </g>

         <g transform="translate(150,350)">
            <use xlink:href="#Path1" style="fill:none; stroke:blue; stroke-width:2"/>
	      <text font-size="20">
                <textPath xlink:href="#Path1" textLength="140" word-spacing="2em">sample sample</textPath>
            </text>
            <text font-size="10" x="35" y="60">textLength="140"</text>
            <text font-size="10" x="35" y="70">word-spacing="2em"</text>
         </g>

          <g transform="translate(300,350)">
            <use xlink:href="#Path1" style="fill:none; stroke:blue; stroke-width:2"/>
	      <text font-size="20">
                <textPath xlink:href="#Path1" textLength="140" lengthAdjust="spacingAndGlyphs" word-spacing="-5">sample sample</textPath>
            </text>
            <text font-size="10" x="35" y="60">textLength="140"</text>
            <text font-size="10" x="35" y="70">word-spacing="-5"</text>
            <text font-size="10" x="35" y="80">lengthAdjust=</text>
            <text font-size="10" x="35" y="90">"spacingAndGlyphs"</text>
         </g>





    </g>

    <!-- ============================================================= -->
    <!-- Batik sample mark                                             -->
    <!-- ============================================================= -->
    <use xlink:href="../resources/batikLogo.svg#Batik_Tag_Box" />

</svg>

