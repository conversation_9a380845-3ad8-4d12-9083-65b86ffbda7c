<!DOCTYPE html>
<html>
<head>
<script src="../../resources/testharness.js"></script>
<script src="../../resources/testharnessreport.js"></script>
</head>
<body>
<div style="height: 100px; overflow: scroll;">
  <select multiple size="3" id="listbox">
    <option>item 1</option>
    <option>item 2</option>
    <option>item 3</option>
  </select>
  <div style="height: 100px;">
  </div>
</div>
<div id="log"></div>
<script>
test(function() {
    var changeEventFired = false;
    var listbox = document.getElementById("listbox");
    listbox.addEventListener("change", function() {
        changeEventFired = true;
    });
    if (!window.eventSender)
        return;
    var x = listbox.offsetLeft + listbox.offsetWidth / 2;
    var y = listbox.offsetTop + 5;
    eventSender.dragMode = false;
    eventSender.mouseMoveTo(x, y);
    eventSender.mouseDown();
    // Move outside of listbox and then release mouse.
    eventSender.mouseMoveTo(x + 1, y);
    eventSender.mouseMoveTo(x + listbox.offsetWidth, y);
    eventSender.mouseUp();
    assert_true(changeEventFired, "changeEventFired");
}, "Event listener on the select element should be triggered.");
</script>
</body>
</html>
