<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Border-width set using four values</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="<PERSON><PERSON>" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-06-26 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#border-width-properties" />
		<link rel="match" href="border-width-shorthand-004-ref.xht" />

        <meta name="assert" content="Applying four values to the border-width property applies the values top, right, bottom, left, respectively." />
        <style type="text/css">
            div
            {
                border-width: 3px 10px 25px 50px;
                border-style: solid;
                height: 1in;
            }
        </style>
    </head>
    <body>
        <p>Test passes if the four borders are all different widths and they get wider starting from the top, to the right, to the bottom and to the left.</p>
        <div></div>
    </body>
</html>