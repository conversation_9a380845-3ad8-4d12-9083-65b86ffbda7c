<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 4.3 Replaced Elements</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
IMG.one {display: inline;}
IMG.two {display: block;}
IMG.three {display: block;
           margin-right: auto; margin-left: auto; width: auto;}
IMG.four {display: block;
          margin-right: auto; margin-left: auto; width: 50%;}
IMG.five {display: block;
          margin-right: 0; margin-left: auto; width: 50%;}</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>IMG.one {display: inline;}
IMG.two {display: block;}
IMG.three {display: block;
           margin-right: auto; margin-left: auto; width: auto;}
IMG.four {display: block;
          margin-right: auto; margin-left: auto; width: 50%;}
IMG.five {display: block;
          margin-right: 0; margin-left: auto; width: 50%;}
</PRE>
<HR>
<P><IMG CLASS="one" SRC="../resources/oransqr.gif" ALT="[Image]">The image at the
beginning of this sentence should be a 15px square.</P>

<IMG CLASS="two" SRC="../resources/oransqr.gif" ALT="[Image]">
<P>The above image should be a 15px square with the same left edge as this text.</P>

<IMG CLASS="three" SRC="../resources/oransqr.gif" ALT="[Image]">
<P>The above image should be a 15px square aligned at the center.</P>

<IMG CLASS="four" SRC="../resources/oransqr.gif" ALT="[Image]">
<P>The above image should be a square resized so its width is 50% of
the its parent element, and centered horizontally within the parent element:  the document body in the first half, and the table in the second.</P>

<IMG CLASS="five" SRC="../resources/oransqr.gif" ALT="[Image]">
<P>The above image should be a square resized so its width is 50% of
its parent element, and aligned at the right edge of the parent element:  the document body in the first half, and the table in the second.</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P><IMG CLASS="one" SRC="../resources/oransqr.gif" ALT="[Image]">The image at the
beginning of this sentence should be a 15px square.</P>

<IMG CLASS="two" SRC="../resources/oransqr.gif" ALT="[Image]">
<P>The above image should be a 15px square with the same left edge as this text.</P>

<IMG CLASS="three" SRC="../resources/oransqr.gif" ALT="[Image]">
<P>The above image should be a 15px square aligned at the center.</P>

<IMG CLASS="four" SRC="../resources/oransqr.gif" ALT="[Image]">
<P>The above image should be a square resized so its width is 50% of
the its parent element, and centered horizontally within the parent element:  the document body in the first half, and the table in the second.</P>

<IMG CLASS="five" SRC="../resources/oransqr.gif" ALT="[Image]">
<P>The above image should be a square resized so its width is 50% of
its parent element, and aligned at the right edge of the parent element:  the document body in the first half, and the table in the second.</P>
</TD></TR></TABLE></BODY>
</HTML>
