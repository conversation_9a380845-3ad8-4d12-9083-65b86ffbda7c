// GENERATED CONTENT - DO NOT EDIT
// Content was automatically extracted by <PERSON><PERSON> into webref
// (https://github.com/w3c/webref)
// Source: WebGL WEBGL_compressed_texture_etc Extension Specification (https://registry.khronos.org/webgl/extensions/WEBGL_compressed_texture_etc/)

[Exposed=(Window,Worker), LegacyNoInterfaceObject]
interface WEBGL_compressed_texture_etc {
    /* Compressed Texture Formats */
    const GLenum COMPRESSED_R11_EAC                        = 0x9270;
    const GLenum COMPRESSED_SIGNED_R11_EAC                 = 0x9271;
    const GLenum COMPRESSED_RG11_EAC                       = 0x9272;
    const GLenum COMPRESSED_SIGNED_RG11_EAC                = 0x9273;
    const GLenum COMPRESSED_RGB8_ETC2                      = 0x9274;
    const GLenum COMPRESSED_SRGB8_ETC2                     = 0x9275;
    const GLenum COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2  = 0x9276;
    const GLenum COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2 = 0x9277;
    const GLenum COMPRESSED_RGBA8_ETC2_EAC                 = 0x9278;
    const GLenum COMPRESSED_SRGB8_ALPHA8_ETC2_EAC          = 0x9279;
};
