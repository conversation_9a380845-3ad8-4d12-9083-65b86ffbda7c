<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8"/>
<title>direction: div override ltr</title>

<link rel="author" title="<PERSON>" href='mailto:<EMAIL>'/>
<link rel="help" href='http://www.w3.org/TR/css-writing-modes-3/#text-direction'/>
<link rel="match" href='reference/block-override-isolate-002.html'/>
<meta name="assert" content='If unicode-bidi:isolate-override is applied to a block element, characters are displayed strictly in sequence according to the direction property.'/>
<style type="text/css">
.test { direction: ltr; unicode-bidi: isolate-override; }

 /* the following styles are not part of the test */
.test, .ref { font-size: 150%; border: 1px solid orange; margin: 10px; width: 10em; padding: 5px; clear: both; }
input { margin: 5px; }
@font-face {
    font-family: 'ezra_silregular';
    src: url('/fonts/sileot-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;
    }
.test, .ref { font-family: ezra_silregular, serif; }
</style>
</head>
<body>
<p class="instructions" dir="ltr">Test passes if the two boxes are identical.</p>


<!--Notes:
Key to entities used below:
        &#x5d0; ... &#x5d5; - The first six Hebrew letters (strongly RTL).
        &#x202d; - The LRO (left-to-right-override) formatting character.
        &#x202c; - The PDF (pop directional formatting) formatting character; closes LRO.
-->


<div dir="rtl">
<div class="test">&gt; ab &gt; &#x5d2;&#x5d3; &gt; ef &gt;</div>

<div class="ref" dir="ltr">&#x202d;&gt; ab &gt; &#x5d2;&#x5d3; &gt; ef &gt;&#x202c;</div>
</div>






</body></html>