<!DOCTYPE html>
<div id="region" style="width:100px; height:100px; position:absolute; left:0px; top:0px;"></div>
<script src="../../resources/js-test.js"></script>
<script>

var eventLog = "";

function appendEventLog(e)
{
  if (!window.eventSender)
    return;

  if (eventLog != "")
    eventLog += " ";

  eventLog += e.type;
}

function clearEventLog()
{
  eventLog = "";
}

function testEvents(settingFlag, expectedString)
{
  internals.settings.setShowContextMenuOnMouseUp(settingFlag);
  debug("setShowContextMenuOnMouseUp setting = " + settingFlag);
  if (window.eventSender) {
    eventSender.mouseMoveTo(50, 50);
    eventSender.mouseDown(2);
    eventSender.mouseUp(2);
  }
  shouldBeEqualToString("eventLog", expectedString);
  debug("");
  clearEventLog();
}

var region = document.getElementById("region");

region.addEventListener("mousedown", appendEventLog, false);
region.addEventListener("mouseup", appendEventLog, false);
region.addEventListener("contextmenu", appendEventLog, false);

testEvents(false, "mousedown contextmenu mouseup");
testEvents(true, "mousedown mouseup contextmenu");

</script>
