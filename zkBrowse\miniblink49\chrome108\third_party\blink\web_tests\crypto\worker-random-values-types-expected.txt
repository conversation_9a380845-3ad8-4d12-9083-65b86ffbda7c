[Worker] Tests which types are valid for crypto.randomValues.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".

Starting worker: random-values-types.js
PASS [Worker] 'crypto' in self is true
PASS [Worker] 'getRandomValues' in self.crypto is true
PASS [Worker] crypto.getRandomValues() threw exception TypeError: Failed to execute 'getRandomValues' on 'Crypto': 1 argument required, but only 0 present..
PASS [Worker] crypto.getRandomValues(undefined) threw exception TypeError: Failed to execute 'getRandomValues' on 'Crypto': parameter 1 is not of type 'ArrayBufferView'..
PASS [Worker] crypto.getRandomValues(null) threw exception TypeError: Failed to execute 'getRandomValues' on 'Crypto': parameter 1 is not of type 'ArrayBufferView'..
PASS [Worker] random = crypto.getRandomValues(new Uint8Array(3)) is defined.
PASS [Worker] random is an instance of Uint8Array
PASS [Worker] view = new Uint8Array(3) is defined.
PASS [Worker] random = crypto.getRandomValues(view) is defined.
PASS [Worker] random is view
PASS [Worker] random = crypto.getRandomValues(new Int8Array(3)) is defined.
PASS [Worker] random is an instance of Int8Array
PASS [Worker] view = new Int8Array(3) is defined.
PASS [Worker] random = crypto.getRandomValues(view) is defined.
PASS [Worker] random is view
PASS [Worker] random = crypto.getRandomValues(new Uint8ClampedArray(3)) is defined.
PASS [Worker] random is an instance of Uint8ClampedArray
PASS [Worker] view = new Uint8ClampedArray(3) is defined.
PASS [Worker] random = crypto.getRandomValues(view) is defined.
PASS [Worker] random is view
PASS [Worker] random = crypto.getRandomValues(new Uint16Array(3)) is defined.
PASS [Worker] random is an instance of Uint16Array
PASS [Worker] view = new Uint16Array(3) is defined.
PASS [Worker] random = crypto.getRandomValues(view) is defined.
PASS [Worker] random is view
PASS [Worker] random = crypto.getRandomValues(new Int16Array(3)) is defined.
PASS [Worker] random is an instance of Int16Array
PASS [Worker] view = new Int16Array(3) is defined.
PASS [Worker] random = crypto.getRandomValues(view) is defined.
PASS [Worker] random is view
PASS [Worker] random = crypto.getRandomValues(new Uint32Array(3)) is defined.
PASS [Worker] random is an instance of Uint32Array
PASS [Worker] view = new Uint32Array(3) is defined.
PASS [Worker] random = crypto.getRandomValues(view) is defined.
PASS [Worker] random is view
PASS [Worker] random = crypto.getRandomValues(new Int32Array(3)) is defined.
PASS [Worker] random is an instance of Int32Array
PASS [Worker] view = new Int32Array(3) is defined.
PASS [Worker] random = crypto.getRandomValues(view) is defined.
PASS [Worker] random is view
PASS [Worker] crypto.getRandomValues(new Float32Array(3)) threw exception TypeMismatchError: Failed to execute 'getRandomValues' on 'Crypto': The provided ArrayBufferView is of type 'Float32', which is not an integer array type..
PASS [Worker] crypto.getRandomValues(new Float64Array(3)) threw exception TypeMismatchError: Failed to execute 'getRandomValues' on 'Crypto': The provided ArrayBufferView is of type 'Float64', which is not an integer array type..
PASS [Worker] buffer = new Uint8Array(32) is defined.
PASS [Worker] buffer.buffer is defined.
PASS [Worker] view = new DataView(buffer.buffer) is defined.
PASS [Worker] crypto.getRandomValues(view) threw exception TypeMismatchError: Failed to execute 'getRandomValues' on 'Crypto': The provided ArrayBufferView is of type 'DataView', which is not an integer array type..
PASS successfullyParsed is true

TEST COMPLETE

