@echo off
echo V8 10.8 Modules Bindings Fix Summary
echo ====================================
echo.

set "MODULES_DIR=..\gen\blink\bindings\modules\v8"

echo Checking which modules V8 bindings have been fixed...
echo.

REM List of files that were reported with errors
set FILES=V8ExtBlendMinMax.cpp V8ExtFragDepth.cpp V8ExtShaderTextureLod.cpp V8ExtSRGB.cpp V8ExtTextureFilterAnisotropic.cpp V8OESStandardDerivatives.cpp V8OESTextureFloatLinear.cpp V8OESTextureFloat.cpp V8OESElementIndexUint.cpp V8OESVertexArrayObject.cpp V8OESTextureHalfFloat.cpp V8OESTextureHalfFloatLinear.cpp

echo Fixed files status:
echo.

for %%f in (%FILES%) do (
    if exist "%MODULES_DIR%\%%f" (
        findstr /C:"v8_compatibility.h" "%MODULES_DIR%\%%f" >nul
        if !ERRORLEVEL! EQU 0 (
            echo ✓ %%f - FIXED ^(compatibility header added^)
        ) else (
            echo ✗ %%f - NOT FIXED ^(missing compatibility header^)
        )
    ) else (
        echo ? %%f - FILE NOT FOUND
    )
)

echo.
echo Remaining files to fix manually:
echo.

REM Check for remaining files that need fixing
for %%f in (%FILES%) do (
    if exist "%MODULES_DIR%\%%f" (
        findstr /C:"v8_compatibility.h" "%MODULES_DIR%\%%f" >nul
        if !ERRORLEVEL! NEQ 0 (
            echo - %%f
        )
    )
)

echo.
echo To fix remaining files, run one of these scripts:
echo - fix_remaining_modules.bat
echo - final_modules_fix.ps1
echo.

echo Summary:
echo - These files contain SetReferenceFromGroup calls that need V8 10.8 compatibility
echo - Each file needs the compatibility header and conditional compilation
echo - The fixes ensure compatibility between V8 7.5 and V8 10.8 APIs
echo.

pause
