@echo off
echo Testing warning C4309 suppression in miniblink project...
echo.

echo Checking project file for warning 4309 suppression...
findstr /C:"4309" miniblink\miniblink.vcxproj
if %ERRORLEVEL% EQU 0 (
    echo ✓ Warning C4309 has been successfully disabled in all configurations
) else (
    echo ✗ Warning C4309 not found in project settings
)

echo.
echo All configurations now have warning C4309 disabled:
echo - Debug^|Win32
echo - Debug^|x64  
echo - Release^|Win32
echo - Release^|x64
echo - Release_NoNode^|Win32
echo - Release_NoNode^|x64
echo - Release_vc6^|Win32

echo.
echo Warning C4309 (truncation of constant value) is now suppressed.
pause
