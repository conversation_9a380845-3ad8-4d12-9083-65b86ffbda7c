<html>
    <head>
        <script src="../../resources/testharness.js"></script>
        <script src="../../resources/testharnessreport.js"></script>
    </head>
    <body>
        <textarea id="test" width="100" onfocus="openPopup()" onblur="openPopup()"></textarea>

        <script>
            var win;
            function openPopup() {
                win = window.open("about:blank", "blank");
                assert_equals(win, null);
            }

            if (window.testRunner) {
                testRunner.setPopupBlockingEnabled(true);
            }

            test(() => document.getElementById("test").focus(), 'focus');
            test(() => document.getElementById("test").blur(), 'blur');
        </script>
    </body>
</html>
