<script>
var clicks = 0;
var console = document.getElementById('console');
var x, y;

function log(message) {
    var console = document.getElementById('console');
    var text = document.createTextNode(message);
    console.appendChild(text);
    console.appendChild(document.createElement("br"));
}

function clickHandler(element) {
    log("onclick: " + element);
}

function clickCounter() {
    clicks++;
}

function runTest() {

    if (!testRunner) {
        document.write("Cannot be run interactively");
        return;
    }
    
    testRunner.dumpAsText();

    var ul = document.getElementById("ul");
    var li = document.getElementById("li");
    var span = document.getElementById("span");
    
    y = li.offsetTop + li.offsetHeight / 2;

    li.style.listStylePosition = "inside";
    
    // WebCore renders an inside list marker right up against the left edge of the bounding box of the list item.
    // This x position should put us over the list marker 
    x = li.offsetLeft + 40;

    // Set the left margin of the span so that the text inside it overlaps the list marker.  Clicking on a spot over
    // the list marker should cause the following onclick fire order: span, li, ul
    span.style.marginLeft = -100;
    log("The onclick fire order should be: span, li, ul");
    eventSender.mouseMoveTo(x, y);
    eventSender.mouseDown();
    eventSender.mouseUp();

    // Set the left margin of the span so that the text inside it overlaps the list marker.  Clicking on a spot over
    // the list marker should cause the following onclick fire order: li, ul
    span.style.marginLeft = 0;
    log("The onclick fire order should be: li, ul");
    eventSender.mouseMoveTo(x, y);
    eventSender.mouseDown();
    eventSender.mouseUp();

    li.style.listStylePosition = "outside";
    // WebCore renders an outside list marker at offsetLeft minus an offset, which is equal on a
    // magic number, 7, plus the ascent of the font * 2/3.  Since we can't get the ascent from JS, just make sure that
    // the list marker is hit at some point between the left edge of the list item and the left edge of the list.
    
    li.onclick = clickCounter;
    ul.onclick = 0;
    
    for (x = ul.offsetLeft; x < li.offsetLeft; x += 5) {
        eventSender.mouseMoveTo(x, y);
        eventSender.mouseDown();
        eventSender.mouseUp();   
    }
    
    if (clicks == 0)
        log("Failure, the list marker wasn't hit during any of the mouse clicks between the left edge of the list item and the left edge of the list.");
}

</script>

<style>
li {
    background-color: blue;
    list-style-type: square;
    list-style-position: outside;
    font-size: 70px;
    color: white;
    padding: 0;
    margin: 0;
}
ul {
    background-color: red;
    padding: 0;
    margin: 0;
    padding-left: 70px;
}
</style>

<ul id="ul" onclick="clickHandler('ul');">
<li id="li" onclick="clickHandler('li');"><span id="span" onclick="clickHandler('span')">text</span></li>
</ul>

<p>This is a testcase for hit testing over list markers.  It uses the eventSender to do mouse clicks and programmatically adjusts the left margin of the span inside the list item and the list-style-position: of the list item.</p>

<p id="console"></p>

<script>
runTest();
</script>