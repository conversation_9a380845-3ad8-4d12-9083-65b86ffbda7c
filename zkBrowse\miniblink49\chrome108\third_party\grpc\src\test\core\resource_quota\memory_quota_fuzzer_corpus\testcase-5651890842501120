actions {
  allocator: 15
  allocation: 15
  create_quota {
  }
}
actions {
  allocation: 72
  create_allocator {
  }
}
actions {
  set_quota_size: 0
}
actions {
  quota: 655360
  post_reclaimer {
    pass: DESTRUCTIVE
    msg {
      actions {
        allocation: 1024
        post_reclaimer {
          pass: DESTRUCTIVE
          msg {
            actions {
              quota: 83
              allocation: 4
              create_quota {
              }
            }
            actions {
              post_reclaimer {
                msg {
                  actions {
                    quota: 83
                    allocation: 4
                    rebind_quota {
                    }
                  }
                  actions {
                    delete_quota {
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
actions {
  quota: 32840
  allocation: 72
  flush_exec_ctx {
  }
}
