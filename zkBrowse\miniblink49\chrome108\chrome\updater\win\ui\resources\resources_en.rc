// Copyright 2020 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include "chrome/updater/win/ui/resources/resources.grh"

#include <winres.h>
#include <winresrc.h>

LANGUAGE LANG_ENGLISH, SUBLANG_ENGLISH_US

// Icon with lowest ID value placed first to ensure application icon
// remains consistent on all systems.
IDI_APP   ICON    "chrome/updater/win/ui/resources/google_update.ico"

IDB_ERROR_ILLUSTRATION BITMAP  "chrome/updater/win/ui/resources/error.bmp"
IDB_CHROME             BITMAP  "chrome/updater/win/ui/resources/chrome.bmp"

IDD_PROGRESS DIALOGEX 0, 0, 433, 187
STYLE DS_SETFONT | WS_DLGFRAME | WS_POPUP | WS_SYSMENU | WS_MINIMIZEBOX
FONT 8, "MS Shell Dlg", 0, 0, 0x0
BEGIN
    CTEXT       "",IDC_TITLE_BAR_SPACER,0,0,1,34, WS_VISIBLE
    PUSHBUTTON  "",IDC_GET_HELP,321,157,46,19,BS_FLAT | WS_VISIBLE
    PUSHBUTTON  "",IDC_CLOSE,374,157,46,19,BS_FLAT | WS_VISIBLE
    PUSHBUTTON  "",IDC_BUTTON1,171,157,121,19,BS_FLAT | WS_VISIBLE
    PUSHBUTTON  "",IDC_BUTTON2,299,157,121,19,BS_FLAT | WS_VISIBLE
    CONTROL     "",IDC_PROGRESS,"msctls_progress32",WS_VISIBLE,30,100,373,10
    CTEXT       "",IDC_INSTALLER_STATE_TEXT,15,55,403,45,WS_VISIBLE
    CTEXT       "",IDC_INFO_TEXT,15,150,403,38,WS_VISIBLE
    CTEXT       "",IDC_PAUSE_RESUME_TEXT,375,82,48,38,SS_NOTIFY | WS_VISIBLE
    CTEXT       "",IDC_COMPLETE_TEXT,15,55,403,84,SS_NOTIFY | WS_VISIBLE
    CTEXT       "",IDC_ERROR_TEXT,15,113,403,43,SS_NOTIFY | WS_VISIBLE
    CONTROL     IDB_ERROR_ILLUSTRATION,IDC_ERROR_ILLUSTRATION,"Static",SS_BITMAP | SS_CENTERIMAGE | WS_VISIBLE,189,53,66,40
    CONTROL     "",IDC_APP_BITMAP,"Static",SS_BITMAP | SS_CENTERIMAGE | WS_VISIBLE,185,139,63,17
END

IDD_INSTALL_STOPPED DIALOGEX 0, 0, 260, 80
STYLE DS_SETFONT | WS_DLGFRAME | WS_POPUP
FONT 8, "MS Shell Dlg", 0, 0, 0x0
BEGIN
    CTEXT           "",IDC_TITLE_BAR_SPACER,0,0,1,15,WS_VISIBLE
    DEFPUSHBUTTON   "",IDOK,161,53,90,19,BS_FLAT
    PUSHBUTTON      "",IDCANCEL,64,53,90,19,BS_FLAT
    CTEXT           "",IDC_INSTALL_STOPPED_TEXT,7,17,244,34
END

IDD_YES_NO DIALOGEX 0, 0, 260, 80
STYLE DS_SETFONT | WS_DLGFRAME | WS_POPUP
FONT 8, "MS Shell Dlg", 0, 0, 0x0
BEGIN
    CTEXT           "",IDC_TITLE_BAR_SPACER,0,0,1,15,~WS_VISIBLE
    DEFPUSHBUTTON   "",IDOK,152,53,46,19,BS_FLAT
    PUSHBUTTON      "",IDCANCEL,205,53,46,19,BS_FLAT
    CTEXT           "",IDC_YES_NO_TEXT,7,17,244,34
END

