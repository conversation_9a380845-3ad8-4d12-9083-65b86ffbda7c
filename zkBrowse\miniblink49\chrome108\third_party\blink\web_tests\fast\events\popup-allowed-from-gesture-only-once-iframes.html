<!DOCTYPE html>
<html>
<script src="../../resources/testdriver.js"></script>
<script src="../../resources/testdriver-vendor.js"></script>
    <body>
        <p>
            Test that we don't create a new user gesture indicator every time
            an event is passed to an iframe.
        </p>
        <p>
            To run, click on the iframe. The test passes, if only one window
            is opened in response to the click.
        </p>
        <iframe id="iframe" src="about:blank"></iframe>
        <div id="console"></div>
        <script>
            var iframe = document.getElementById("iframe");
            iframe.contentDocument.body.onclick = function() {
                window.open("about:blank", "window1");
                window.open("about:blank", "window2");
                if (window.testRunner) {
                    if (testRunner.windowCount() == windowCount + 1)
                        document.getElementById("console").innerText = "PASSED";
                    else
                        document.getElementById("console").innerText = "FAILED";
                    testRunner.notifyDone();
                }
            }

            if (window.testRunner) {
                testRunner.dumpAsText();
                testRunner.waitUntilDone();
                windowCount = testRunner.windowCount();

                var frame = document.getElementById("iframe");
                test_driver.click(frame)
            }
        </script>
    </body>
  </html>
