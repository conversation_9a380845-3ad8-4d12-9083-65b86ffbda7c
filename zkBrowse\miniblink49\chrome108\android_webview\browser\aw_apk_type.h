// Copyright 2020 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef ANDROID_WEBVIEW_BROWSER_AW_APK_TYPE_H_
#define ANDROID_WEBVIEW_BROWSER_AW_APK_TYPE_H_

namespace android_webview {

// GENERATED_JAVA_ENUM_PACKAGE: org.chromium.android_webview
enum class ApkType { STANDALONE = 0, MONOCHROME = 1, TRICHROME = 2 };

}  // namespace android_webview

#endif  // ANDROID_WEBVIEW_BROWSER_AW_APK_TYPE_H_
