<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background-color set to hex with 6 digits with a red set to the maximum plus one value of #fg0000 which is invalid</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="<PERSON><PERSON>" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2013-04-08 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background-color" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <link rel="match" href="background-color-013-ref.xht" />

        <meta name="flags" content="invalid" />
        <meta name="assert" content="Background-color set to #fg0000 is invalid and falls back to the initial value." />
        <style type="text/css">
            #test
            {
                height: 100px;
            }
            #wrapper
            {
                background-color: green;
            }
            #test
            {
                background-color: #fg0000;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is a wide filled green rectangle.</p>
        <div id="wrapper">
            <div id="test"></div>
        </div>
    </body>
</html>