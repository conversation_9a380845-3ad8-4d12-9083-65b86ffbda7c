<!DOCTYPE html>
<title>DataTransfer.prototype.setDragImage type checking of image argument</title>
<script src="../../resources/testharness.js"></script>
<script src="../../resources/testharnessreport.js"></script>
<div id="dragger" draggable="true">Drag to run test</div>
<script>
async_test(function () {
  var dragger = document.getElementById("dragger");
  dragger.addEventListener("dragstart", this.step_func_done(function(event) {
    assert_throws_js(TypeError, function() {
      event.dataTransfer.setDragImage(null, 0, 0);
    }, "throw if image argument is null");
    assert_throws_js(TypeError, function() {
      event.dataTransfer.setDragImage(dragger.firstChild, 0, 0);
    }, "throw if image argument is a Text node");
    // A generic non-HTML/SVG Element should not throw.
    event.dataTransfer.setDragImage(document.createElementNS(null, "x"), 0, 0);
    // Remove dragger to hide the manual instructions in expectations.
    dragger.remove();
  }));

  if (!window.eventSender)
    return;

  var x = dragger.offsetLeft + dragger.offsetWidth / 2;
  var y = dragger.offsetTop + dragger.offsetHeight / 2;

  eventSender.mouseMoveTo(x, y);
  eventSender.mouseDown();
  eventSender.leapForward(100);
  eventSender.mouseMoveTo(x + 100, y + 100);
  eventSender.mouseUp();
});
</script>
