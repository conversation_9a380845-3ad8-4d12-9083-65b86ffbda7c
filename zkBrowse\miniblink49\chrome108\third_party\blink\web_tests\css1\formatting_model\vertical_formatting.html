<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 4.1.1 Vertical Formatting</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
P.one {margin-bottom: 2cm; padding-bottom: 0;}
P.two {margin-top: 2cm; padding-top: 0;}
P.three {margin-top: 0; padding-top: 0;}
P.four {margin-top: -1cm; margin-bottom: 0;
        padding-top: 0; padding-bottom: 0;}
DIV.five {margin-top: 1cm; margin-bottom: 1cm;
          padding-top: 1cm; padding-bottom: 0;}
P.six {margin-top: 1cm; margin-bottom: 1cm;
       padding-top: 0; padding-bottom: 0;}
P.seven {margin-top: 1cm; padding-top: 0;}
P.eight {margin-bottom: -1cm; padding-bottom: 2cm;}
P.nine {margin-top: -1cm; padding-top: 1cm;
        padding-bottom: 0; margin-bottom: 1cm;}
P.ten {margin-top: 1cm;padding-top: 0;
       float: left;width: 50%;}
P.eleven {margin-top: 1cm; padding-top: 0; clear: none;}
P.twelve {margin-bottom: 0; padding-bottom: 1cm; clear: both;}
P.thirteen {margin-top: 0; padding-top: 1cm;}
TABLE {clear: both;}
</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>P.one {margin-bottom: 2cm; padding-bottom: 0;}
P.two {margin-top: 2cm; padding-top: 0;}
P.three {margin-top: 0; padding-top: 0;}
P.four {margin-top: -1cm; margin-bottom: 0;
        padding-top: 0; padding-bottom: 0;}
DIV.five {margin-top: 1cm; margin-bottom: 1cm;
          padding-top: 1cm; padding-bottom: 0;}
P.six {margin-top: 1cm; margin-bottom: 1cm;
       padding-top: 0; padding-bottom: 0;}
P.seven {margin-top: 1cm; padding-top: 0;}
P.eight {margin-bottom: -1cm; padding-bottom: 2cm;}
P.nine {margin-top: -1cm; padding-top: 1cm;
        padding-bottom: 0; margin-bottom: 1cm;}
P.ten {margin-top: 1cm;padding-top: 0;
       float: left;width: 50%;}
P.eleven {margin-top: 1cm; padding-top: 0; clear: none;}
P.twelve {margin-bottom: 0; padding-bottom: 1cm; clear: both;}
P.thirteen {margin-top: 0; padding-top: 1cm;}
TABLE {clear: both;}

</PRE>
<HR>
<P CLASS="one">There should be a two-centimeter margin between this paragraph and the next, because adjacent vertical margins should collapse to the maximum of the margins.</P>

<P CLASS="two">This is another paragraph.</P>

<P CLASS="one">There should be a two-centimeter margin between this paragraph and the next.</P>

<P CLASS="three">This is another paragraph.</P>

<P CLASS="one">There should be a one-centimeter margin between this paragraph and the next, because when there is one negative margin, the two margins should be added (the minus sign should be kept).</P>

<P CLASS="four">This is another paragraph.</P>

<DIV CLASS="five">
<P CLASS="six">
There should be three centimeters between this text and the text above,
but only one centimeter between this text and the text below, because
vertical margins of nested elements should collapse only if there is no
border or padding between the margins.
</P></DIV>

<P CLASS="seven">This is more text.</P>

<P CLASS="eight">There should be two centimeters between this paragraph and the one below, because negative margins collapse to a negative margin with the largest absolute value of the margins collapsed.</P>

<P CLASS="nine">This is a paragraph, which I should make very long so that you can easily see how much space there is between it and the one below it and to the right.</P>

<P CLASS="ten">There should be two centimeters between this paragraph and the one above it, since margins do not collapse on floating elements.</P>

<P CLASS="eleven">There should be one centimeter between this paragraph and the (non-floating) one above it, since the float should not effect the paragraph spacing.</P>

<P CLASS="twelve">There should be two centimeters of padding between
this paragraph and the one below.  Padding does not collapse, and there
is 1cm of padding on each side.</P>

<P CLASS="thirteen">This is the next paragraph.</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P CLASS="one">There should be a two-centimeter margin between this paragraph and the next, because adjacent vertical margins should collapse to the maximum of the margins.</P>

<P CLASS="two">This is another paragraph.</P>

<P CLASS="one">There should be a two-centimeter margin between this paragraph and the next.</P>

<P CLASS="three">This is another paragraph.</P>

<P CLASS="one">There should be a one-centimeter margin between this paragraph and the next, because when there is one negative margin, the two margins should be added (the minus sign should be kept).</P>

<P CLASS="four">This is another paragraph.</P>

<DIV CLASS="five">
<P CLASS="six">
There should be three centimeters between this text and the text above,
but only one centimeter between this text and the text below, because
vertical margins of nested elements should collapse only if there is no
border or padding between the margins.
</P></DIV>

<P CLASS="seven">This is more text.</P>

<P CLASS="eight">There should be two centimeters between this paragraph and the one below, because negative margins collapse to a negative margin with the largest absolute value of the margins collapsed.</P>

<P CLASS="nine">This is a paragraph, which I should make very long so that you can easily see how much space there is between it and the one below it and to the right.</P>

<P CLASS="ten">There should be two centimeters between this paragraph and the one above it, since margins do not collapse on floating elements.</P>

<P CLASS="eleven">There should be one centimeter between this paragraph and the (non-floating) one above it, since the float should not effect the paragraph spacing.</P>

<P CLASS="twelve">There should be two centimeters of padding between
this paragraph and the one below.  Padding does not collapse, and there
is 1cm of padding on each side.</P>

<P CLASS="thirteen">This is the next paragraph.</P>
</TD></TR></TABLE></BODY>
</HTML>
