<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

 <head>

  <title>CSS Writing Modes Test: position absolute and 'vertical-rl' - 'left' and 'right' are 'auto', 'width' is not 'auto' with 'direction: rtl' in initial containing block</title>

  <link rel="author" title="Gérard Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" />
  <link rel="help" href="http://www.w3.org/TR/css-writing-modes-3/#vertical-layout" title="7.1 Principles of Layout in Vertical Writing Modes" />
  <link rel="match" href="abs-pos-non-replaced-icb-vrl-004-ref.xht" />

  <meta content="image" name="flags" />
  <meta content="This test checks that when the initial containing block's writing-mode is 'horizontal-tb' and its 'direction' is 'rtl', then an absolutely positioned box (with 'left' and 'right' are 'auto', 'width' is not 'auto') whose containing block is the initial containing block and whose direction is 'rtl' must set right to static position and then solve for 'left'. Whether such absolutely positioned box's 'writing-mode' is vertical or not is irrelevant." name="assert" />

  <style type="text/css"><![CDATA[
  html
    {
      direction: rtl;
    }

  img
    {
      vertical-align: top;
    }

  div#green-overlapping-test
    {
      background-color: green;
      border-left: green solid 35px;
      border-right: green solid 15px;
      height: 100px;
      left: auto;
      position: absolute;
      right: auto;
      width: 50px;
      writing-mode: vertical-rl;
    }

  /*
  "
  2. 'left' and 'right' are 'auto' and 'width' is not 'auto', then if the 'direction' property of the element establishing the static-position containing block is 'rtl' set 'right' to the static position (...) Then solve for 'left' (if 'direction is 'rtl').
  "
  10.3.7 Absolutely positioned, non-replaced elements
  http://www.w3.org/TR/CSS21/visudet.html#abs-non-replaced-width

  So:

           auto : left
        +
            0px : margin-left
        +
           35px : border-left-width
        +
            0px : padding-left
        +
           50px : width
        +
            0px : padding-right
        +
           15px : border-right-width
        +
            0px : margin-right
        +
           auto : right (set to static position)
        ====================
                : width of containing block (width of Initial Containing Block)

  becomes

          solve : left
        +
            0px : margin-left
        +
           35px : border-left-width
        +
            0px : padding-left
        +
           50px : width
        +
            0px : padding-right
        +
           15px : border-right-width
        +
            0px : margin-right
        +
            8px : right (static position)
        ====================
                : width of containing block (width of Initial Containing Block)
  */

  div#red-overlapped-reference
    {
      background-color: red;
      height: 100px;
      width: 100px;
    }
  ]]></style>
 </head>

 <body>

  <p><img src="support/pass-cdts-abs-pos-non-replaced.png" width="246" height="36" alt="Image download support must be enabled" /></p>

  <!--
  The image says:
  Test passes if there is a filled green square and <strong>no red</strong>.
  -->

  <div id="green-overlapping-test"></div>

  <div id="red-overlapped-reference"></div>

 </body>
</html>