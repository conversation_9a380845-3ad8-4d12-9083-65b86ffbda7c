#!/usr/bin/env python3
# Copyright 2022 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

STRING_IDS = [
    'IDS_FRIENDLY_COMPANY_NAME',
    'IDS_NO_UPDATE_RESPONSE',
    'IDS_INSTALL_UPDATER_FAILED',
    'IDS_INSTALLER_DISPLAY_NAME',
    'IDS_CLOSE_BUTTON',
    'IDS_MINIMIZE_BUTTON',
    'IDS_INITIALIZING',
    'IDS_WAITING_TO_CONNECT',
    'IDS_DOWNLOADING_SHORT',
    'IDS_DOWNLOADING_LONG',
    'IDS_DOWNLOADING_VERY_LONG',
    'IDS_DOWNLOADING_COMPLETED',
    'IDS_DOWNLOADING',
    'IDS_WAITING_TO_INSTALL',
    'IDS_INSTALLING',
    'IDS_CANCELING',
    'IDS_TEXT_RESTART_BROWSER',
    'IDS_TEXT_RESTART_ALL_BROWSERS',
    'IDS_TEXT_RESTART_COMPUTER',
    'IDS_UPDATER_CLOSE',
    'IDS_RESTART_NOW',
    'IDS_RESTART_LATER',
    'IDS_GET_HELP_TEXT',
    'IDS_INSTALLATION_STOPPED_WINDOW_TITLE',
    'IDS_INSTALL_STOPPED',
    'IDS_RESUME_INSTALLATION',
    'IDS_CANCEL_INSTALLATION',
    'IDS_SPLASH_SCREEN_MESSAGE',
    'IDS_BUNDLE_INSTALLED_SUCCESSFULLY',
    'IDS_INSTALL_OS_NOT_SUPPORTED',
]
