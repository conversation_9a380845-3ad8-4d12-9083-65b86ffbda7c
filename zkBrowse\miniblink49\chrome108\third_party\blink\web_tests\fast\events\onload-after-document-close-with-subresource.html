<p>This test verifies that the load event doesn't fire until subresource content has loaded, even if you manually call document.close beforehand. See <a href="http://bugs.webkit.org/show_bug.cgi?id=13241">bug 13241</a>.</p>
<hr>
<pre id="console"></pre>

<iframe></iframe>

<script>
function log(s)
{
    document.getElementById("console").appendChild(document.createTextNode(s + "\n"));
}

function getDimensions()
{
    var image = frames[0].document.images[0];
    return "{ " + image.width + " x " + image.height + " }";
}

function beforeOnload()
{
    log("dimensions before onload (should be { 0 x 0 }, otherwise the test will falsely pass): " + getDimensions());
}

function afterOnload()
{
    log("dimensions during onload (should be { 215 x 174 }, otherwise onload fired too early): " + getDimensions());
    if (window.testRunner)
        testRunner.notifyDone();
}

function runTest()
{
    if (window.testRunner) {
        testRunner.dumpAsText();

        // Quirkily, the FrameLoadDelegate API considers our <iframe> fully loaded 
        // once it has loaded its initial empty document. So, if we want DumpRenderTree 
        // to wait for the load event caused by our document.close() before dumping,
        // we need to tell it so explicitly.
        testRunner.waitUntilDone();
    }

    var doc = frames[0].document;
    doc.open();
    doc.write("<body onload=\"parent.afterOnload()\">");
    // Append a random query string to force a non-cached load.
    doc.write("<img src=\"resources/onload-image.png?" + Math.random() + "\">");
    doc.write("<script\>parent.beforeOnload();</script\>");
    doc.write("</body>");
    doc.close();
}

runTest();
</script>
