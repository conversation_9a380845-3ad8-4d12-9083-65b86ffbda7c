<!DOCTYPE html>
<script src='../../resources/gesture-util.js'></script>
<script src="../../resources/testharness.js"></script>
<script src="../../resources/testharnessreport.js"></script>
<!-- This div is 200 pixels high. The content results in scrool bars on
  both edges, resulting in an effective content area of 185 x 185 on
  linux. The paging context overlap is 24 pixels. So one page of scroll
  moves the content by 185 - 24 -= 161 pixels. -->
<div id="overflow" style="border:2px solid black;overflow:auto;height:200px;width:200px;">
    <div style="height:300px;width:600px">
      <div style="background-color:red;height:300px;width:300px;position:relative;left:0px;top:0px"></div>
      <div style="background-color:green;height:300px;width:300px;position:relative;left:300px;top:-300px"></div>
    </div>
    <div style="height:300px;width:600px">
      <div style="background-color:blue;height:300px;width:300px;position:relative;left:0px;top:0px"></div>
      <div style="background-color:yellow;height:300px;width:300px;position:relative;left:300px;top:-300px"></div>
    </div>
</div>

<script>
var givenScrollTop = 1;
var givenScrollLeft = 2;
var expectedScrollTop = 161;
var expectedScrollLeft = 161;
var last_event = null;
var source = GestureSourceType.MOUSE_INPUT;
const numTicksX = givenScrollLeft / pixelsPerTick();
const numTicksY = givenScrollTop / pixelsPerTick();
const expectedWheelDeltaX = numTicksX * LEGACY_MOUSE_WHEEL_TICK_MULTIPLIER;
const expectedWheelDeltaY = numTicksY * LEGACY_MOUSE_WHEEL_TICK_MULTIPLIER;

function mousewheelHandler(e) {
    assert_equals(last_event, null);
    last_event = e;
}

promise_test(async () => {
    var overflowElement = document.getElementById("overflow");
    overflowElement.addEventListener("mousewheel", mousewheelHandler, false);
    var ele_center = elementCenter(overflowElement);
    // Wheel event will scroll by one page
    await smoothScrollWithXY(
      givenScrollLeft, givenScrollTop, ele_center.x, ele_center.y, source,
      SPEED_INSTANT, false, true
    );
    await waitForAnimationEndTimeBased(() => {
      return overflowElement.scrollLeft;
    });
    await waitForAnimationEndTimeBased(() => {
      return overflowElement.scrollTop;
    });

    // There is some minor inconsistency between main and compositor threads in
    // which direction they round so allow up to one pixel of slack.
    assert_approx_equals(
      overflowElement.scrollLeft, expectedScrollLeft, 1
    );
    assert_approx_equals(
      overflowElement.scrollTop, expectedScrollTop, 1
    );

    // When a user operates the device for scrolling to down, wheelDeltaX is negative, otherwise positive.
    // https://developer.mozilla.org/en-US/docs/Web/API/Element/mousewheel_event.
    assert_equals(last_event.wheelDeltaX, -Math.floor(expectedWheelDeltaX));
    assert_equals(last_event.wheelDeltaY, -Math.floor(expectedWheelDeltaY));
    assert_equals(last_event.wheelDelta, -Math.floor(expectedWheelDeltaY));
}, 'This test checks one page of scroll on both x and y on div moves the content by 87.5% of the scroller size.');
</script>
