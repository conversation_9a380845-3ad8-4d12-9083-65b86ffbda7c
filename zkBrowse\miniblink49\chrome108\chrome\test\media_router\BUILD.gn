# Copyright 2015 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

declare_args() {
  # Many of the targets defined in this file have dependencies that cannot be
  # satisfied without additional downstream resources. Explicitly guard them
  # them behind this flag so that we do not attempt to build or isolate targets
  # in public chromium checkouts.
  enable_downstream_media_tests = false
}

source_set("browser_tests") {
  testonly = true
  defines = [ "HAS_OUT_OF_PROC_TEST_RUNNER" ]
  sources = [
    "media_router_cast_ui_for_test.cc",
    "media_router_cast_ui_for_test.h",
    "media_router_e2e_browsertest.cc",
    "media_router_e2e_browsertest.h",
    "media_router_e2e_ui_browsertest.cc",
    "media_router_gmc_ui_for_test.cc",
    "media_router_gmc_ui_for_test.h",
    "media_router_integration_browsertest.cc",
    "media_router_integration_browsertest.h",
    "media_router_integration_ui_browsertest.cc",
    "media_router_one_ua_integration_browsertest.cc",
    "media_router_ui_for_test_base.cc",
    "media_router_ui_for_test_base.h",
  ]
  deps = [
    ":test_support",
    "//build:chromeos_buildflags",
    "//chrome/app:generated_resources",
    "//chrome/browser",
    "//chrome/browser/media/router",
    "//chrome/browser/media/router:media_router_feature",
    "//chrome/browser/media/router:test_support",
    "//chrome/browser/ui",
    "//chrome/browser/ui/views:test_support",
    "//chrome/common",
    "//chrome/test:test_support",
    "//chrome/test:test_support_ui",
    "//components/global_media_controls",
    "//components/media_router/common",
    "//components/policy/core/browser",
    "//components/policy/core/common:test_support",
    "//components/sessions",
    "//content/public/browser",
    "//content/test:test_support",
    "//media:test_support",
    "//net",
    "//net:test_support",
    "//testing/gtest",
    "//ui/base",
    "//ui/events:test_support",
    "//ui/views:test_support",
    "//url",
  ]
  data_deps = [ ":browser_test_resources" ]
}

copy("browser_test_resources") {
  testonly = true
  sources = [
    "resources/basic_test.html",
    "resources/close_route_with_error_on_send.json",
    "resources/common.js",
    "resources/fail_create_route.json",
    "resources/fail_reconnect_session.html",
    "resources/fail_reconnect_session.json",
    "resources/local_media_sink.json",
    "resources/local_media_sink_route_fail.json",
    "resources/no_presentation_receiver.html",
    "resources/no_provider.json",
    "resources/no_sinks.json",
    "resources/no_supported_sinks.json",
    "resources/presentation_receiver.html",
    "resources/route_creation_timed_out.json",
  ]
  outputs = [
    "$root_out_dir/media_router/browser_test_resources/{{source_file_part}}",
  ]
}

# Run separately from the Chromium waterfall. See README.md for details.
if (enable_downstream_media_tests) {
  group("media_router_e2e_tests") {
    testonly = true
    data_deps = [
      ":browser_test_resources",
      ":e2e_test_resources",
      "//chrome/test:browser_tests",
    ]
    data = [ "internal/" ]
  }
}

copy("e2e_test_resources") {
  testonly = true
  sources = [
    "../../../media/test/data/bear-vp9.webm",
    "../../../media/test/data/player.html",
  ]
  outputs = [
    "$root_out_dir/media_router/browser_test_resources/{{source_file_part}}",
  ]
}

# Run separately.  TODO: Documentation on how to run
if (enable_downstream_media_tests) {
  group("swarming_tests") {
    testonly = true
    data_deps = [
      ":browser_test_resources",
      "//chrome/test:browser_tests",
    ]
    data = [ "internal/" ]
  }
}

source_set("test_support") {
  testonly = true
  sources = [
    "test_media_sinks_observer.cc",
    "test_media_sinks_observer.h",
  ]
  public_deps = [ "//base" ]
  deps = [
    "//chrome/browser/media/router",
    "//chrome/browser/media/router:test_support",
    "//chrome/common:constants",
    "//chrome/test:test_support",
    "//chrome/test:test_support_ui",
    "//content/public/common",
    "//content/test:test_support",
    "//testing/gtest",
  ]
}
