<!DOCTYPE html>
<script src='../../resources/testharness.js'></script>
<script src='../../resources/testharnessreport.js'></script>
<p>This test ensures drag-n-drop does start when extending an existing selecting with shift + mouse drag, starting over a link.</p>
<span id='span' style='font-size: 30px; padding: 10px;'>Some text with a <a href=#>link in the end of sentence</a>.</span>
<div id="log"></div>

<script>
test(function() {
    assert_not_equals(window.testRunner, undefined, 'Requires testRunner.');
    assert_not_equals(window.eventSender, undefined, 'Requires eventSender.');

    var span = document.getElementById('span');
    span.focus();

    var dragStartCount = 0;
    document.addEventListener('dragstart', function (event) { dragStartCount++; });

    testRunner.dumpAsText();

    var y = span.offsetTop + span.offsetHeight / 2;
    eventSender.mouseMoveTo(span.offsetLeft + 5, y);
    eventSender.mouseDown();
    eventSender.leapForward(200);
    eventSender.mouseUp();

    eventSender.mouseMoveTo(span.offsetLeft + span.offsetWidth / 4, y);
    eventSender.mouseDown(0, ['shiftKey']);
    eventSender.leapForward(200);
    eventSender.mouseUp();

    eventSender.mouseMoveTo(span.offsetLeft + span.offsetWidth * .75 , y);
    eventSender.mouseDown(0, ['shiftKey']);
    eventSender.leapForward(200);
    eventSender.mouseMoveTo(span.offsetLeft + span.offsetWidth * .75, y + 120);
    eventSender.leapForward(200);
    eventSender.mouseUp();

    assert_equals(dragStartCount, 1);
});
</script>
