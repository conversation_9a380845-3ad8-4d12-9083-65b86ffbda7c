<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN"
"http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">

<!--

   Copyright 2001  The Apache Software Foundation 

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

-->
<!-- ====================================================================== -->
<!-- Tests various text on a path                                           -->
<!--                                                                        -->
<!-- <AUTHOR>                                   -->
<!-- @version $Id: longTextOnPath.svg,v 1.4 2004/08/18 07:12:21 vhardy Exp $   -->
<!-- ====================================================================== -->
<?xml-stylesheet type="text/css" href="../resources/test.css" ?>  

<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="body" width="450" height="500" viewBox="0 0 450 500">
<title>Long text on a path test for selection</title>

    <g id="content">

        <text class="title" x="50%" y="40">Long text on a path test for selection</text>

        <defs>
	      <path id="Path" style="fill:none; stroke:blue;" 
                    d="M 25 150 h 400 m -400 20 h 400 m -400 20 h 400 m -400 20 h 400 m -400 20 h 400 m -400 20 h 400 m -400 20 h 400 m -400 20 h 400 m -400 20 h 400 m -400 20 h 400 m -400 20 h 400 m -400 20 h 400 m -400 20 h 400 m -400 20 h 400 m -400 20 h 400 m -400 20"/>

        </defs>

        <use xlink:href="#Path" fill="none" stroke="blue" />
        <rect x="20" y="100" width="205" height="375" fill="rgb(200,200,255)"/>
	<text font-size="20" style="text-anchor:start">
            <textPath xlink:href="#Path" startOffset="0%">This is an example of a very long string that is split across multiple lines via the textPath tag. The purpose of this test is to ensure that text-selection can keep up even when relatively large numbers of characters are part of the selection. Since I haven't reached the end yet, let me keep going with more really quite useless text just to see if we hit a limit where it really starts to slow things down.  However if it hasn't slowed down by now I'm guessing that it won't slow down at all.  But you never know for sure until you try it, so I am, and here is the result.  Selection does bog down a little near the end but it's redraw related.</textPath>
        </text>
    </g>

    <!-- ============================================================= -->
    <!-- Batik sample mark                                             -->
    <!-- ============================================================= -->
    <use xlink:href="../resources/batikLogo.svg#Batik_Tag_Box" />
</svg>

