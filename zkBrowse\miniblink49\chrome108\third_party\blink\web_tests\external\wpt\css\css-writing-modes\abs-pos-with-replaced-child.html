<!DOCTYPE html>
<link rel="match" href="../reference/ref-filled-green-100px-square-only.html">
<link rel="help" href="https://drafts.csswg.org/css-sizing-3/#intrinsic-sizes">
<link rel="help" href="https://drafts.csswg.org/css-writing-modes-3/">
<meta name="assert" content="This test checks that the intrinsic size of an absolute positioned element is calculated correctly, with a replaced child that has a percentage block-size." />
<p>Test passes if there is a filled green square.</p>
<div style="position: relative; width: 200px; height: 100px;">
  <div style="position: absolute; background: green; writing-mode: vertical-rl; width: 50%;">
    <canvas style="width: 100%;" width=1 height=1></canvas>
  </div>
</div>
