<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background on html and body element</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="G<PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-04-21 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <meta name="assert" content="Background of the html element is the canvas's background even if body background is set." />
        <style type="text/css">
            html
            {
                background: green;
            }
            body
            {
                background: blue;
                color: white;
            }
        </style>
    </head>
    <body>
         <p>Test passes if the background of the entire page is green and there is a blue bar.</p>
    </body>
</html>
