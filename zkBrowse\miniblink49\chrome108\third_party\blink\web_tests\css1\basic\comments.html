<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 1.7 Comments</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
/* This is a CSS1 comment. */
.one {color: green;} /* Another comment */
/* The following should not be used:
.two {color: red;} */
.three {color: green; /* color: red; */}
/**
.four {color: red;} */
.five {color: green;}
/**/
.six {color: green;}
/*********/
.seven {color: green;}
/* a comment **/
.eight {color: green;}
</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>/* This is a CSS1 comment. */
.one {color: green;} /* Another comment */
/* The following should not be used:
.two {color: red;} */
.three {color: green; /* color: red; */}
/**
.four {color: red;} */
.five {color: green;}
/**/
.six {color: green;}
/*********/
.seven {color: green;}
/* a comment **/
.eight {color: green;}

</PRE>
<HR>
<P class="one">
This sentence should be green.
</P>
<P class="two">
This sentence should be black.
</P>
<P class="three">
This sentence should be green.
</P>
<P class="four">
This sentence should be black.
</P>
<P class="five">
This sentence should be green.
</P>
<P class="six">
This sentence should be green.
</P>
<P class="seven">
This sentence should be green.
</P>
<P class="eight">
This sentence should be green.
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P class="one">
This sentence should be green.
</P>
<P class="two">
This sentence should be black.
</P>
<P class="three">
This sentence should be green.
</P>
<P class="four">
This sentence should be black.
</P>
<P class="five">
This sentence should be green.
</P>
<P class="six">
This sentence should be green.
</P>
<P class="seven">
This sentence should be green.
</P>
<P class="eight">
This sentence should be green.
</P>
</TD></TR></TABLE></BODY>
</HTML>
