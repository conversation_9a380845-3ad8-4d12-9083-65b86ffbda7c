// Copyright 2021 gRPC authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package binder_transport_fuzzer;

message Binder {}

message Value {
  oneof data_type {
    int32 i32 = 1;
    int64 i64 = 2;
    bytes byte_array = 3;
    // Strings in Parcel could also contain non UTF-8 data so we use bytes
    // to represent string here
    bytes str = 4;
    Binder binder = 5;
  }
}

message Parcel {
  repeated Value values = 1;

  // Simulates the return value of AParcel_getDataSize
  // (The value generated by protobuf libprotobuf-mutator might not always make sense
  // but the transport implementation should handle that)
  int32 data_size = 2;
}

enum TransactionCode {
  INVALID = 0;
  SETUP_TRANSPORT = 1;
  SHUTDOWN_TRANSPORT = 2;
  ACKNOWLEDGE_BYTES = 3;
  PING = 4;
  PING_RESPONSE = 5;
}

message Transaction {
  TransactionCode code = 1;
  int32 uid = 2;
  Parcel parcel = 3;
}

// Special parcel that used for setting up transport.
// TODO(mingcl): Consider also fuzzing the setup transport code path
message SetupTransportParcel {
  int32 version = 1;

  // Simulates the return value of AParcel_getDataSize
  // (The value generated by protobuf libprotobuf-mutator might not always make sense
  // but the transport implementation should handle that)
  int32 data_size = 2;
}

message SetupTransportTransaction {
  int32 uid = 1;
  SetupTransportParcel parcel = 2;
}

message IncomingParcels {
  SetupTransportTransaction setup_transport_transaction = 1;
  repeated Transaction transactions = 2;
}

message Input {
  IncomingParcels incoming_parcels = 1;
}

