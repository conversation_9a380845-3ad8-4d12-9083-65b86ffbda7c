<html>

<head>
<script>

if (window.testRunner) {
    testRunner.dumpAsText();
    testRunner.waitUntilDone();
}

window.addEventListener('beforeunload', before, false);
window.onunload = unload;

function before()
{
    alert( "before unload");
}

function unload()
{
    alert( "unload" );
    window.removeEventListener('beforeunload', before, false);
}

function load()
{
    location = "resources/onunload-clears-onbeforeunload-success.html";
}

</script>
</head>

<body onload="load()">
start document
</body>

</html>
