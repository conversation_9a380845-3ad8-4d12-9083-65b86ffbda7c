Ensure that simulated click on label elements uses the original mouse coordinates.

 Click Me
 Click Me
PASS: event.clientX is same for both real and simulated event.
PASS: event.clientY is same for both real and simulated event.
PASS: event.layerX is same for both real and simulated event.
PASS: event.layerY is same for both real and simulated event.
PASS: event.pageX is same for both real and simulated event.
PASS: event.pageY is same for both real and simulated event.
PASS: event.screenX is same for both real and simulated event.
PASS: event.screenY is same for both real and simulated event.
PASS: event.x is same for both real and simulated event.
PASS: event.y is same for both real and simulated event.

PASS: event.clientX is same for both real and simulated event.
PASS: event.clientY is same for both real and simulated event.
PASS: event.layerX is same for both real and simulated event.
PASS: event.layerY is same for both real and simulated event.
PASS: event.pageX is same for both real and simulated event.
PASS: event.pageY is same for both real and simulated event.
PASS: event.screenX is same for both real and simulated event.
PASS: event.screenY is same for both real and simulated event.
PASS: event.x is same for both real and simulated event.
PASS: event.y is same for both real and simulated event.

