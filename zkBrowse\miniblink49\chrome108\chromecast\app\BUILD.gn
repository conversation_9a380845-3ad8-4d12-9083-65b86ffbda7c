# Copyright 2015 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//chromecast/chromecast.gni")
import("//testing/test.gni")
import("//tools/grit/grit_rule.gni")

cast_source_set("app") {
  sources = [
    "cast_main_delegate.cc",
    "cast_main_delegate.h",
  ]

  public_deps = [ "//base" ]

  deps = [
    ":cast_crash_client",
    "//chromecast:chromecast_buildflags",
    "//chromecast/base",
    "//chromecast/browser",
    "//chromecast/common",
    "//chromecast/common:resource_delegate",
    "//chromecast/gpu",
    "//chromecast/renderer",
    "//chromecast/utility",
    "//components/crash/core/common:crash_key",
    "//content/public/app",
    "//content/public/browser",
    "//content/public/common",
    "//ui/base",
  ]

  if (!is_fuchsia) {
    # TODO(crbug.com/1226159): Complete crash reporting integration on Fuchsia.
    deps += [ "//components/crash/core/app" ]
  }
}

cast_source_set("cast_crash_client") {
  sources = []

  deps = [
    "//base",
    "//chromecast/base",
    "//chromecast/common",
    "//content/public/common",
  ]

  if (!is_fuchsia) {
    deps += [
      "//chromecast/crash",
      "//components/crash/core/app",
    ]
  }

  if (is_linux || is_chromeos) {
    sources += [
      "linux/cast_crash_reporter_client.cc",
      "linux/cast_crash_reporter_client.h",
    ]
  }

  if (is_android) {
    sources += [
      "android/cast_crash_reporter_client_android.cc",
      "android/cast_crash_reporter_client_android.h",
      "android/crash_handler.cc",
      "android/crash_handler.h",
    ]
    deps += [
      "//chromecast/base:android_create_sys_info",
      "//chromecast/base:cast_version",
      "//chromecast/browser:jni_headers",
    ]
  }
}

cast_source_set("test_support") {
  testonly = true
  sources = [ "cast_test_launcher.cc" ]

  deps = [
    ":app",
    "//base",
    "//base/test:test_support",
    "//chromecast/base",
    "//content/test:test_support",
    "//ipc",
    "//mojo/core/embedder",
  ]
}

cast_source_set("unittests") {
  testonly = true
  sources = []

  if (is_linux || is_chromeos) {
    sources += [ "linux/cast_crash_reporter_client_unittest.cc" ]
  }

  deps = [
    "//base",
    "//base/test:test_support",
    "//chromecast/base:test_support",
    "//testing/gtest",
  ]

  if (!is_fuchsia) {
    deps += [
      ":cast_crash_client",
      "//chromecast/crash",
      "//chromecast/crash:test_support",
    ]
  }
}

grit("resources") {
  source = "//chromecast/app/resources/shell_resources.grd"

  outputs = [
    "grit/shell_resources.h",
    "shell_resources.pak",
  ]
}

grit("chromecast_settings") {
  source = "//chromecast/app/resources/chromecast_settings.grd"

  outputs = [ "grit/chromecast_settings.h" ]
  foreach(locale, cast_locales) {
    outputs += [ "chromecast_settings_${locale}.pak" ]
  }

  deps = [ ":verify_cast_locales" ]
}

# This target ensures that Chromecast developers are notified when locales
# change. If this target is breaking the build, the CAST_LOCALES list in
# //chromecast/app/verify_cast_locales.py must be updated to match
# |cast_locales|. Please see that file for more details. This action will be
# run on fresh builds, and whenever |cast_locales| is updated.
action("verify_cast_locales") {
  script = "//chromecast/app/verify_cast_locales.py"

  # This file will be stamped on success, preventing an unnecessary rebuild.
  stamp_file = "$target_gen_dir/cast_locales_verified"
  args = [
    "-s",
    rebase_path(stamp_file, "$root_out_dir"),
  ]

  args += cast_locales

  outputs = [ stamp_file ]
}
