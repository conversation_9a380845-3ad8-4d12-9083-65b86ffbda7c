Tests that the sidebar origin list disappears and appers when an interstitial is shown or hidden.

Before interstitial is shown:
<DIV >
    <#document-fragment >
        <STYLE >
        </STYLE>
        <STYLE >
        </STYLE>
        <STYLE >
        </STYLE>
        <STYLE >
        </STYLE>
        <STYLE >
        </STYLE>
        <DIV class=tree-outline-disclosure >
            <OL class=tree-outline role=tree tabindex=-1 >
                <LI role=treeitem class=security-main-view-sidebar-tree-item selected force-white-icons title=Overview tabindex=0 aria-selected=true >
                    <DIV class=selection fill >
                    </DIV>
                    <SPAN class=tree-element-title >
                    </SPAN>
                    <DIV class=icon lock-icon lock-icon-unknown >
                    </DIV>
                    <SPAN class=title >
Overview
                    </SPAN>
                </LI>
                <OL class=children role=group >
                </OL>
                <LI title=Main origin role=treeitem class=parent always-parent expanded security-sidebar-origins aria-expanded=true >
                    <DIV class=selection fill >
                    </DIV>
                    <SPAN class=tree-element-title >
Main origin
                    </SPAN>
                </LI>
                <OL class=children expanded role=group aria-label=Main origin >
                    <LI title=Reload to view details role=treeitem class=security-main-view-reload-message >
                        <DIV class=selection fill >
                        </DIV>
                        <SPAN class=tree-element-title >
Reload to view details
                        </SPAN>
                    </LI>
                    <OL class=children role=group >
                    </OL>
                </OL>
                <LI title=Non-secure origins role=treeitem class=parent always-parent expanded security-sidebar-origins hidden aria-expanded=true >
                    <DIV class=selection fill >
                    </DIV>
                    <SPAN class=tree-element-title >
Non-secure origins
                    </SPAN>
                </LI>
                <OL class=children expanded hidden role=group aria-label=Non-secure origins >
                </OL>
                <LI title=Secure origins role=treeitem class=parent always-parent expanded security-sidebar-origins aria-expanded=true >
                    <DIV class=selection fill >
                    </DIV>
                    <SPAN class=tree-element-title >
Secure origins
                    </SPAN>
                </LI>
                <OL class=children expanded role=group aria-label=Secure origins >
                    <LI role=treeitem class=security-sidebar-tree-item title=https://foo.test >
                        <DIV class=selection fill >
                        </DIV>
                        <SPAN class=tree-element-title >
                        </SPAN>
                        <DIV class=icon security-property security-property-secure >
                        </DIV>
                        <SPAN >
                            <SPAN class=url-scheme-secure >
https
                            </SPAN>
                            <SPAN class=url-scheme-separator >
://
                            </SPAN>
                            <SPAN >
foo.test
                            </SPAN>
                        </SPAN>
                    </LI>
                    <OL class=children role=group >
                    </OL>
                    <LI role=treeitem class=security-sidebar-tree-item title=https://bar.test >
                        <DIV class=selection fill >
                        </DIV>
                        <SPAN class=tree-element-title >
                        </SPAN>
                        <DIV class=icon security-property security-property-secure >
                        </DIV>
                        <SPAN >
                            <SPAN class=url-scheme-secure >
https
                            </SPAN>
                            <SPAN class=url-scheme-separator >
://
                            </SPAN>
                            <SPAN >
bar.test
                            </SPAN>
                        </SPAN>
                    </LI>
                    <OL class=children role=group >
                    </OL>
                </OL>
                <LI title=Unknown / canceled role=treeitem class=parent always-parent expanded security-sidebar-origins hidden aria-expanded=true >
                    <DIV class=selection fill >
                    </DIV>
                    <SPAN class=tree-element-title >
Unknown / canceled
                    </SPAN>
                </LI>
                <OL class=children expanded hidden role=group aria-label=Unknown / canceled >
                </OL>
            </OL>
        </DIV>
    </#document-fragment>
</DIV>
After interstitial is shown:
<DIV >
    <#document-fragment >
        <STYLE >
        </STYLE>
        <STYLE >
        </STYLE>
        <STYLE >
        </STYLE>
        <STYLE >
        </STYLE>
        <STYLE >
        </STYLE>
        <DIV class=tree-outline-disclosure >
            <OL class=tree-outline role=tree tabindex=-1 >
                <LI role=treeitem class=security-main-view-sidebar-tree-item selected force-white-icons title=Overview tabindex=0 aria-selected=true >
                    <DIV class=selection fill >
                    </DIV>
                    <SPAN class=tree-element-title >
                    </SPAN>
                    <DIV class=icon lock-icon lock-icon-unknown >
                    </DIV>
                    <SPAN class=title >
Overview
                    </SPAN>
                </LI>
                <OL class=children role=group >
                </OL>
                <LI title=Main origin role=treeitem class=parent always-parent expanded security-sidebar-origins hidden aria-expanded=true >
                    <DIV class=selection fill >
                    </DIV>
                    <SPAN class=tree-element-title >
Main origin
                    </SPAN>
                </LI>
                <OL class=children expanded hidden role=group aria-label=Main origin >
                    <LI title=Reload to view details role=treeitem class=security-main-view-reload-message >
                        <DIV class=selection fill >
                        </DIV>
                        <SPAN class=tree-element-title >
Reload to view details
                        </SPAN>
                    </LI>
                    <OL class=children role=group >
                    </OL>
                </OL>
                <LI title=Non-secure origins role=treeitem class=parent always-parent expanded security-sidebar-origins hidden aria-expanded=true >
                    <DIV class=selection fill >
                    </DIV>
                    <SPAN class=tree-element-title >
Non-secure origins
                    </SPAN>
                </LI>
                <OL class=children expanded hidden role=group aria-label=Non-secure origins >
                </OL>
                <LI title=Secure origins role=treeitem class=parent always-parent expanded security-sidebar-origins hidden aria-expanded=true >
                    <DIV class=selection fill >
                    </DIV>
                    <SPAN class=tree-element-title >
Secure origins
                    </SPAN>
                </LI>
                <OL class=children expanded hidden role=group aria-label=Secure origins >
                    <LI role=treeitem class=security-sidebar-tree-item title=https://foo.test >
                        <DIV class=selection fill >
                        </DIV>
                        <SPAN class=tree-element-title >
                        </SPAN>
                        <DIV class=icon security-property security-property-secure >
                        </DIV>
                        <SPAN >
                            <SPAN class=url-scheme-secure >
https
                            </SPAN>
                            <SPAN class=url-scheme-separator >
://
                            </SPAN>
                            <SPAN >
foo.test
                            </SPAN>
                        </SPAN>
                    </LI>
                    <OL class=children role=group >
                    </OL>
                    <LI role=treeitem class=security-sidebar-tree-item title=https://bar.test >
                        <DIV class=selection fill >
                        </DIV>
                        <SPAN class=tree-element-title >
                        </SPAN>
                        <DIV class=icon security-property security-property-secure >
                        </DIV>
                        <SPAN >
                            <SPAN class=url-scheme-secure >
https
                            </SPAN>
                            <SPAN class=url-scheme-separator >
://
                            </SPAN>
                            <SPAN >
bar.test
                            </SPAN>
                        </SPAN>
                    </LI>
                    <OL class=children role=group >
                    </OL>
                </OL>
                <LI title=Unknown / canceled role=treeitem class=parent always-parent expanded security-sidebar-origins hidden aria-expanded=true >
                    <DIV class=selection fill >
                    </DIV>
                    <SPAN class=tree-element-title >
Unknown / canceled
                    </SPAN>
                </LI>
                <OL class=children expanded hidden role=group aria-label=Unknown / canceled >
                </OL>
            </OL>
        </DIV>
    </#document-fragment>
</DIV>
After interstitial is hidden:
<DIV >
    <#document-fragment >
        <STYLE >
        </STYLE>
        <STYLE >
        </STYLE>
        <STYLE >
        </STYLE>
        <STYLE >
        </STYLE>
        <STYLE >
        </STYLE>
        <DIV class=tree-outline-disclosure >
            <OL class=tree-outline role=tree tabindex=-1 >
                <LI role=treeitem class=security-main-view-sidebar-tree-item selected force-white-icons title=Overview tabindex=0 aria-selected=true >
                    <DIV class=selection fill >
                    </DIV>
                    <SPAN class=tree-element-title >
                    </SPAN>
                    <DIV class=icon lock-icon lock-icon-unknown >
                    </DIV>
                    <SPAN class=title >
Overview
                    </SPAN>
                </LI>
                <OL class=children role=group >
                </OL>
                <LI title=Main origin role=treeitem class=parent always-parent expanded security-sidebar-origins aria-expanded=true >
                    <DIV class=selection fill >
                    </DIV>
                    <SPAN class=tree-element-title >
Main origin
                    </SPAN>
                </LI>
                <OL class=children expanded role=group aria-label=Main origin >
                    <LI title=Reload to view details role=treeitem class=security-main-view-reload-message >
                        <DIV class=selection fill >
                        </DIV>
                        <SPAN class=tree-element-title >
Reload to view details
                        </SPAN>
                    </LI>
                    <OL class=children role=group >
                    </OL>
                </OL>
                <LI title=Non-secure origins role=treeitem class=parent always-parent expanded security-sidebar-origins aria-expanded=true >
                    <DIV class=selection fill >
                    </DIV>
                    <SPAN class=tree-element-title >
Non-secure origins
                    </SPAN>
                </LI>
                <OL class=children expanded role=group aria-label=Non-secure origins >
                </OL>
                <LI title=Secure origins role=treeitem class=parent always-parent expanded security-sidebar-origins aria-expanded=true >
                    <DIV class=selection fill >
                    </DIV>
                    <SPAN class=tree-element-title >
Secure origins
                    </SPAN>
                </LI>
                <OL class=children expanded role=group aria-label=Secure origins >
                    <LI role=treeitem class=security-sidebar-tree-item title=https://foo.test >
                        <DIV class=selection fill >
                        </DIV>
                        <SPAN class=tree-element-title >
                        </SPAN>
                        <DIV class=icon security-property security-property-secure >
                        </DIV>
                        <SPAN >
                            <SPAN class=url-scheme-secure >
https
                            </SPAN>
                            <SPAN class=url-scheme-separator >
://
                            </SPAN>
                            <SPAN >
foo.test
                            </SPAN>
                        </SPAN>
                    </LI>
                    <OL class=children role=group >
                    </OL>
                    <LI role=treeitem class=security-sidebar-tree-item title=https://bar.test >
                        <DIV class=selection fill >
                        </DIV>
                        <SPAN class=tree-element-title >
                        </SPAN>
                        <DIV class=icon security-property security-property-secure >
                        </DIV>
                        <SPAN >
                            <SPAN class=url-scheme-secure >
https
                            </SPAN>
                            <SPAN class=url-scheme-separator >
://
                            </SPAN>
                            <SPAN >
bar.test
                            </SPAN>
                        </SPAN>
                    </LI>
                    <OL class=children role=group >
                    </OL>
                </OL>
                <LI title=Unknown / canceled role=treeitem class=parent always-parent expanded security-sidebar-origins aria-expanded=true >
                    <DIV class=selection fill >
                    </DIV>
                    <SPAN class=tree-element-title >
Unknown / canceled
                    </SPAN>
                </LI>
                <OL class=children expanded role=group aria-label=Unknown / canceled >
                </OL>
            </OL>
        </DIV>
    </#document-fragment>
</DIV>

