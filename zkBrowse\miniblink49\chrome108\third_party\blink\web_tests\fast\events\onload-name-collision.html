<html>
<body onload="onload()">
This tests that an onload handler named 'onload' is called properly.
You should see "PASS" below.

<div id='result'>FAIL: this text should be replaced on success.<div>
<script>
if (window.testRunner)
  testRunner.dumpAsText();
function onload() {
  var result = document.getElementById('result');
  result.innerHTML='PASS: onload function gets called.';
}
</script>
</body>
</html>
