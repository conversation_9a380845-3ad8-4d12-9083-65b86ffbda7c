<!DOCTYPE html>
<html>
<head>
    <style>
        div.block { height: 400px; border: 1px solid black; margin:10px; }
    </style>
</head>
<body>
    <div>
        Following actions must not emit resize events: page load, dynamic content generation, and page scaling.<br/>

        The spec DOM Level 2 Events states that the resize event occurs when document view size (a.k.a layout size) is changed. Refer to http://www.w3.org/TR/DOM-Level-2-Events/events.html<br/>
        However, showing/hiding scrollbars shouldn't be considered a layout size change. Refer to webkit.org/b/80242<br/>
    </div>
    <div id=expandingblock>
    </div>
    <pre id="console"></pre>
    <script src="../../resources/js-test.js"></script>
    <script>
        var resizeEventCount = 0;
        window.onresize = function() {
            resizeEventCount++;
        }

        if (window.testRunner) {
            testRunner.dumpAsText();
            testRunner.waitUntilDone();
        }
        function test() {
            setTimeout(showScrollbar, 20);
        }
        // Add many div blocks to increase document height more than view height.
        function showScrollbar() {
            for (var i = 0; i < 10; i++) {
                var el = document.createElement('div');
                el.setAttribute('class','block');
                document.getElementById('expandingblock').appendChild(el);
            }
            setTimeout(scalePage, 20);
        }
        function scalePage() {
            if (window.internals)
                internals.setPageScaleFactor(3);
            setTimeout(finish, 20);
        }
        function finish() {
            // No resize events are acceptable.
            shouldBe("resizeEventCount", "0");
            if (window.testRunner)
                testRunner.notifyDone();
        }
        window.onload = test;
    </script>
</body>
</html>
