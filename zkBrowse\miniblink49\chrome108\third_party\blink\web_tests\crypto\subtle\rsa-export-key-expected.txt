Test exporting an RSA key.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".


Importing a JWK key...
error is: TypeError: Invalid keyFormat argument
error is: TypeError: Invalid keyFormat argument
error is: TypeError: Invalid keyFormat argument
error is: TypeError: Invalid keyFormat argument
error is: TypeError: Invalid keyFormat argument

Exporting the key as JWK...
PASS exportedJWK.kty is 'RSA'
PASS exportedJWK.n is publicKeyJSON.n
PASS exportedJWK.e is publicKeyJSON.e
PASS exportedJWK.alg is 'RS256'
PASS exportedJWK.ext is true
PASS exportedJWK.use is undefined
PASS exportedJWK.key_ops is ['verify']
PASS successfullyParsed is true

TEST COMPLETE

