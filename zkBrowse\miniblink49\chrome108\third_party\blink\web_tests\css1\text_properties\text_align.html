<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.4.6 text-align</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
.one {text-align: left;}
.two {text-align: right;}
.three {text-align: center;}
.four {text-align: justify;}
</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.one {text-align: left;}
.two {text-align: right;}
.three {text-align: center;}
.four {text-align: justify;}

</PRE>
<HR>
<P class="one">
This sentence should be left-justified.
</P>
<P class="two">
This sentence should be right-justified.
</P>
<P class="three">
This sentence should be centered.
</P>
<P class="four">
This sentence should be fully justified, which means that the right and left margins of this paragraph should line up, no matter how long the paragraph becomes; the exception, of course, is the last line, which should be left-justified in Western languages.
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P class="one">
This sentence should be left-justified.
</P>
<P class="two">
This sentence should be right-justified.
</P>
<P class="three">
This sentence should be centered.
</P>
<P class="four">
This sentence should be fully justified, which means that the right and left margins of this paragraph should line up, no matter how long the paragraph becomes; the exception, of course, is the last line, which should be left-justified in Western languages.
</P>
</TD></TR></TABLE></BODY>
</HTML>
