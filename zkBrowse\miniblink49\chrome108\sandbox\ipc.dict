# Copyright 2017 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

"\x01a\x00\x00\x00"
"\x02a\x00\x00\x00b\x00\x00\x00"
"\x01\x00\x00\x00a"
"\x02\x00\x00\x00a\x00\x00\x00b"
# WCHAR_TYPE length 1ch offset 4 and 8.
"\x00\x00\x00\x01\x00\x00\x00\x04\x00\x00\x00\x04"
"\x00\x00\x00\x01\x00\x00\x00\x08\x00\x00\x00\x04"
# UINT32_TYPE offset 4 and 8.
"\x02\x04\x04"
"\x02\x08\x04"
# wchar string (4 bytes).
"a\x00\x00\x00"
"\x00\x00\x00a"
# WCHAR_TYPE
"\x00\x00\x00\x01"
# 32-bit offset
"\x00\x00\x00\x04"
# 64-bit offset
"\x00\x00\x00\x08"

