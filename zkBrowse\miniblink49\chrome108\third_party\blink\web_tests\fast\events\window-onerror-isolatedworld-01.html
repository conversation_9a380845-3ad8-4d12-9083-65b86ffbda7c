<!DOCTYPE html>
<html>
<head>
    <script>
        window.jsTestIsAsync = true;
        window.isOnErrorTest = true;
    </script>
    <script src="../../resources/js-test.js"></script>
    <script src="resources/onerror-test.js"></script>
</head>
<body>
    <script>
        description('Test that window.onerror and "error" event listeners from main world are not invoked for uncaught exceptions in scripts running in isolate worlds, but only for exceptions in the main world.');

        var onerrorsHandled = 0;
        function onErrorCallback(errorsHandled) {
            onerrorsHandled++;
            if (onerrorsHandled > 3)
                testFailed("Only main-world exceptions should be caught by onerror handlers.");

            if (errorsHandled === 6)
                finishJSTest();
        }

        var errorEventsHandled = 0;
        function errorEventCallback(errorsHandled) {
            errorEventsHandled++;
            if (errorEventsHandled > 3) {
                testFailed("Only main-world exceptions should be caught by ErrorEvent listeners.");
            }

            if (errorsHandled === 6)
                finishJSTest();
        }

        dumpOnErrorArgumentValuesAndReturn(true, onErrorCallback);
        dumpErrorEventAndPreventDefault(errorEventCallback);

        var exceptions = function(worldType)
        {
            window.addEventListener("load", function(e) {
                // Do the following call from load listener to make sure error in the setTimeout callback always happens after the error in this listener.
                setTimeout(function() {
                    throw new Error("Error in " + worldType + " world setTimeout callback.");
                }, 0);
                throw new Error("Error in " + worldType + " world load handler.");
            }, false);

            throw new Error("Error in " + worldType + " world inline script.");
        }

        if (window.testRunner)
            testRunner.evaluateScriptInIsolatedWorld(1, "(" + exceptions + ")('isolated')");

        exceptions("main");

    </script>
</body>
</html>
