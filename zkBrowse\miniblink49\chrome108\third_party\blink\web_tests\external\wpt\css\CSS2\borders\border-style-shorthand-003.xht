<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Border-style set using three values</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="<PERSON><PERSON>" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-06-24 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#border-color-properties" />
        <meta name="assert" content="Applying three values to the border-style property applies the first value to the top the second value to the left and right and the third value to the bottom." />
        <style type="text/css">
            div
            {
                border-width: 10px;
                border-style: dotted double dashed;
                height: 1in;
            }
        </style>
    </head>
    <body>
        <p>Test passes if the top border is dotted, the left and right borders are a double line, and the bottom border is dashed.</p>
        <div></div>
    </body>
</html>