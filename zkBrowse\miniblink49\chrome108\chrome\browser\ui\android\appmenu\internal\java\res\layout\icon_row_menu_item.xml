<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright 2016 The Chromium Authors
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file.
-->
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="?android:attr/listPreferredItemHeightSmall"
    android:layout_gravity="top|start"
    android:orientation="horizontal">

    <org.chromium.ui.widget.ChromeImageButton
        android:id="@+id/button_one"
        style="@style/OverflowMenuButton" />

    <org.chromium.ui.widget.ChromeImageButton
        android:id="@+id/button_two"
        style="@style/OverflowMenuButton" />

    <org.chromium.ui.widget.ChromeImageButton
        android:id="@+id/button_three"
        style="@style/OverflowMenuButton" />

    <org.chromium.ui.widget.ChromeImageButton
        android:id="@+id/button_four"
        style="@style/OverflowMenuButton" />

    <org.chromium.ui.widget.ChromeImageButton
        android:id="@+id/button_five"
        style="@style/OverflowMenuButton" />
</LinearLayout>
