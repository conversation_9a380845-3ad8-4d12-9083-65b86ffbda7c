<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Border-width applied to element with display inline-block</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="G<PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-12-08 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#propdef-border-width" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#border-width-properties" />
		<link rel="match" href="border-width-applies-to-009-ref.xht" />

        <meta name="assert" content="The 'border-width' property applies to elements with a display of inline-block." />
        <style type="text/css">
            span#inline-block
            {
                border-style: solid;
                border-width: 1in;
                display: inline-block;
                width: 1in;
            }

            span.block-descendant
            {
                color: white;
                display: block;
                height: 0.5in;
            }

        </style>
    </head>
    <body>
        <p>Test passes if there is a large square with four black sides that have the same thickness. (Note: Such large square must be surrounding a smaller filled white square.)</p>

        <div>
            <span id="inline-block">
				        <span class="block-descendant">a</span>
				        <span class="block-descendant">b</span>
        	  </span>
        </div>

    </body>
</html>
