[
  Exposed=Window,
  SecureContext,
  ImplementedAs=SubApps
] partial interface Navigator {
  [SameObject, RuntimeEnabled=DesktopPWAsSubApps] readonly attribute SubApps subApps;
};

[
  Exposed=Window,
  SecureContext,
  RuntimeEnabled=DesktopPWAsSubApps
] interface SubApps {
  [CallWith=ScriptState, RaisesException] Promise<undefined> add(DOMString install_url);
  [CallWith=ScriptState, RaisesException] Promise<FrozenArray<DOMString>> list();
  [CallWith=ScriptState, RaisesException] Promise<undefined> remove(DOMString app_id);
};
