<!DOCTYPE html>
<html>
<head>
<script src="../../resources/js-test.js"></script>
<script src="resources/common.js"></script>
</head>
<body>
<p id="description"></p>
<div id="console"></div>

<script>
description("Tests generating AES keys");

jsTestIsAsync = true;

// Tests the 24 permutations of keys generated by:
//   kPossibleAlgorithms x kPossibleExtractable x kPossibleKeyUsages x kPossibleKeyLengths
//
// For practical reasons these tests are not exhaustive.

var kPossibleAlgorithms = ['AES-CBC', 'AES-GCM'];
var kPossibleExtractable = [true, false];
var kPossibleKeyUsages = [['encrypt'], ['decrypt', 'wrapKey'], ['encrypt', 'wrapKey', 'unwrapKey']];
var kPossibleKeyLengths = [128, 256];

// Set of all key data generated so far.
var allKeyDataGenerated = {};

function runTest(algorithmName, extractable, keyUsages, keyLengthBits)
{
    var genAlgorithm = { name: algorithmName, length: keyLengthBits };

    var results = {};

    var promise = crypto.subtle.generateKey(genAlgorithm, extractable, keyUsages).then(function(result) {
        generatedKey = result;

        shouldEvaluateAs("generatedKey.extractable", extractable);
        shouldEvaluateAs("generatedKey.algorithm.name", algorithmName);
        shouldEvaluateAs("generatedKey.algorithm.length", keyLengthBits);
        shouldEvaluateAs("generatedKey.usages.join(',')", keyUsages.join(","));

        if (extractable)
            return crypto.subtle.exportKey('raw', generatedKey);
    });

    if (extractable) {
        promise = promise.then(function(result) {
            keyData = result;
            shouldEvaluateAs("keyData.byteLength", keyLengthBits / 8);

            var keyDataHex = bytesToHexString(keyData);

            // It is very unlikely to generate two identical keys, so
            // assume if that happens something is broken.
            // (8 extractable keys are generated for each bit length).
            if (allKeyDataGenerated[keyDataHex]) {
                testFailed("Generated identical key data: " + keyDataHex);
            } else {
                allKeyDataGenerated[keyDataHex] = true;
                testPassed("Generated unique key data of length: " + (keyData.byteLength * 8) + " bits");
            }
        });
    }

    return promise;
}

var lastPromise = Promise.resolve(null);

kPossibleAlgorithms.forEach(function(algorithmName) {
    kPossibleExtractable.forEach(function(extractable) {
        kPossibleKeyUsages.forEach(function(keyUsages) {
            kPossibleKeyLengths.forEach(function(keyLengthBits) {
                lastPromise = lastPromise.then(runTest.bind(null, algorithmName, extractable, keyUsages, keyLengthBits));
            });
        });
    });
});

lastPromise.then(finishJSTest, failAndFinishJSTest);

</script>

</body>
</html>
