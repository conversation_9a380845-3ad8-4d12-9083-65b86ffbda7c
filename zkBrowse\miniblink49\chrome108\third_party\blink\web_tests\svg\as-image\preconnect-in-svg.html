<!doctype html>
<script src="../../resources/testharness.js"></script>
<script src="../../resources/testharnessreport.js"></script>
<img src="data:image/svg+xml,
  <svg xmlns='http://www.w3.org/2000/svg' xmlns:h='http://www.w3.org/1999/xhtml'>
    <h:link rel='preconnect' href='http://example.com'/>
  </svg>">
<script>
  // Test for https://crbug.com/1069289. The test passes if it does not crash.
  test(() => {}, 'Preconnect request in SVG image does not crash.');
</script>
