  1) LOAD 4  // Architecture
  2) if A == 0x40000003; then JMP 3 else JMP 28
  3) LOAD 0  // System call number
  4) if A & 0x40000000; then JMP 28 else JMP 5
  5) if A >= 0x38; then JMP 6 else JMP 7
  6) if A >= 0x401; then JMP 34 else JMP 33
  7) if A >= 0x37; then JMP 8 else JMP 33
  8) LOAD 28  // Argument 1 (MSB)
  9) if A == 0x0; then JMP 10 else JMP 28
 10) LOAD 24  // Argument 1 (LSB)
 11) if A == 0x3; then JMP 32 else JMP 12
 12) LOAD 28  // Argument 1 (MSB)
 13) if A == 0x0; then JMP 14 else JMP 28
 14) LOAD 24  // Argument 1 (LSB)
 15) if A == 0x1; then JMP 32 else JMP 16
 16) LOAD 28  // Argument 1 (MSB)
 17) if A == 0x0; then JMP 18 else JMP 28
 18) LOAD 24  // Argument 1 (LSB)
 19) if A == 0x2; then JMP 26 else JMP 20
 20) LOAD 28  // Argument 1 (MSB)
 21) if A == 0x0; then JMP 22 else JMP 28
 22) LOAD 24  // Argument 1 (LSB)
 23) if A == 0x4; then JMP 25 else JMP 24
 24) RET 0x5000d  // errno = 13
 25) RET 0x50001  // errno = 1
 26) LOAD 36  // Argument 2 (MSB)
 27) if A == 0x0; then JMP 29 else JMP 28
 28) RET 0x0  // Kill
 29) LOAD 32  // Argument 2 (LSB)
 30) if A == 0x80000; then JMP 33 else JMP 31
 31) RET 0x50016  // errno = 22
 32) RET 0x50002  // errno = 2
 33) RET 0x7fff0000  // Allowed
 34) RET 0x50026  // errno = 38
