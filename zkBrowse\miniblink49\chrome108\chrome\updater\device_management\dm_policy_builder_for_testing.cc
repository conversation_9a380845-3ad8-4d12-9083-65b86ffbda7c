// Copyright 2020 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include "chrome/updater/device_management/dm_policy_builder_for_testing.h"

#include <stdint.h>
#include <utility>

#include "base/strings/string_util.h"
#include "chrome/updater/device_management/dm_cached_policy_info.h"
#include "chrome/updater/protos/omaha_settings.pb.h"
#include "chrome/updater/unittest_util.h"
#include "components/policy/proto/device_management_backend.pb.h"
#include "crypto/rsa_private_key.h"
#include "crypto/signature_creator.h"

namespace updater {

namespace {

// A test signing key raw bytes in DER-encoded PKCS8 format.
const uint8_t kSigningKey1[] = {
    0x30, 0x82, 0x01, 0x55, 0x02, 0x01, 0x00, 0x30, 0x0d, 0x06, 0x09, 0x2a,
    0x86, 0x48, 0x86, 0xf7, 0x0d, 0x01, 0x01, 0x01, 0x05, 0x00, 0x04, 0x82,
    0x01, 0x3f, 0x30, 0x82, 0x01, 0x3b, 0x02, 0x01, 0x00, 0x02, 0x41, 0x00,
    0xd9, 0xcd, 0xca, 0xcd, 0xc3, 0xea, 0xbe, 0x72, 0x79, 0x1c, 0x29, 0x37,
    0x39, 0x99, 0x1f, 0xd4, 0xb3, 0x0e, 0xf0, 0x7b, 0x78, 0x77, 0x0e, 0x05,
    0x3b, 0x65, 0x34, 0x12, 0x62, 0xaf, 0xa6, 0x8d, 0x33, 0xce, 0x78, 0xf8,
    0x47, 0x05, 0x1d, 0x98, 0xaa, 0x1b, 0x1f, 0x50, 0x05, 0x5b, 0x3c, 0x19,
    0x3f, 0x80, 0x83, 0x63, 0x63, 0x3a, 0xec, 0xcb, 0x2e, 0x90, 0x4f, 0xf5,
    0x26, 0x76, 0xf1, 0xd5, 0x02, 0x03, 0x01, 0x00, 0x01, 0x02, 0x40, 0x64,
    0x29, 0xc2, 0xd9, 0x6b, 0xfe, 0xf9, 0x84, 0x75, 0x73, 0xe0, 0xf4, 0x77,
    0xb5, 0x96, 0xb0, 0xdf, 0x83, 0xc0, 0x4e, 0x57, 0xf1, 0x10, 0x6e, 0x91,
    0x89, 0x12, 0x30, 0x5e, 0x57, 0xff, 0x14, 0x59, 0x5f, 0x18, 0x86, 0x4e,
    0x4b, 0x17, 0x56, 0xfc, 0x8d, 0x40, 0xdd, 0x74, 0x65, 0xd3, 0xff, 0x67,
    0x64, 0xcb, 0x9c, 0xb4, 0x14, 0x8a, 0x06, 0xb7, 0x13, 0x45, 0x94, 0x16,
    0x7d, 0x3f, 0xe1, 0x02, 0x21, 0x00, 0xf6, 0x0f, 0x31, 0x6d, 0x06, 0xcc,
    0x3b, 0xa0, 0x44, 0x1f, 0xf5, 0xc2, 0x45, 0x2b, 0x10, 0x6c, 0xf9, 0x6f,
    0x8f, 0x87, 0x3d, 0xc0, 0x3b, 0x55, 0x13, 0x37, 0x80, 0xcd, 0x9f, 0xe1,
    0xb7, 0xd9, 0x02, 0x21, 0x00, 0xe2, 0x9a, 0x5f, 0xbf, 0x95, 0x74, 0xb5,
    0x7a, 0x6a, 0xa6, 0x97, 0xbd, 0x75, 0x8c, 0x97, 0x18, 0x24, 0xd6, 0x09,
    0xcd, 0xdc, 0xb5, 0x94, 0xbf, 0xe2, 0x78, 0xaa, 0x20, 0x47, 0x9f, 0x68,
    0x5d, 0x02, 0x21, 0x00, 0xaf, 0x8f, 0x97, 0x8c, 0x5a, 0xd5, 0x4d, 0x95,
    0xc4, 0x05, 0xa9, 0xab, 0xba, 0xfe, 0x46, 0xf1, 0xf9, 0xe7, 0x07, 0x59,
    0x4f, 0x4d, 0xe1, 0x07, 0x8a, 0x76, 0x87, 0x88, 0x2f, 0x13, 0x35, 0xc1,
    0x02, 0x20, 0x24, 0xc3, 0xd9, 0x2f, 0x13, 0x47, 0x99, 0x3e, 0x20, 0x59,
    0xa1, 0x1a, 0xeb, 0x1c, 0x81, 0x53, 0x38, 0x7e, 0xc5, 0x9e, 0x71, 0xe5,
    0xc0, 0x19, 0x95, 0xdb, 0xef, 0xf6, 0x46, 0xc8, 0x95, 0x3d, 0x02, 0x21,
    0x00, 0xaa, 0xb1, 0xff, 0x8a, 0xa2, 0xb2, 0x2b, 0xef, 0x9a, 0x83, 0x3f,
    0xc5, 0xbc, 0xd4, 0x6a, 0x07, 0xe8, 0xc7, 0x0b, 0x2e, 0xd4, 0x0f, 0xf8,
    0x98, 0x68, 0xe1, 0x04, 0xa8, 0x92, 0xd0, 0x10, 0xaa,
};

// SHA256 signature of kSigningKey for "example.com" domain.
const uint8_t kSigningKey1Signature[] = {
    0x97, 0xEB, 0x13, 0xE6, 0x6C, 0xE2, 0x7A, 0x2F, 0xC6, 0x6E, 0x68, 0x8F,
    0xED, 0x5B, 0x51, 0x08, 0x27, 0xF0, 0xA5, 0x97, 0x20, 0xEE, 0xE2, 0x9B,
    0x5B, 0x63, 0xA5, 0x9C, 0xAE, 0x41, 0xFD, 0x34, 0xC4, 0x2E, 0xEB, 0x63,
    0x10, 0x80, 0x0C, 0x74, 0x77, 0x6E, 0x34, 0x1C, 0x1B, 0x3B, 0x8E, 0x2A,
    0x3A, 0x7F, 0xF9, 0x73, 0xB6, 0x2B, 0xB6, 0x45, 0xDB, 0x05, 0xE8, 0x5A,
    0x68, 0x36, 0x05, 0x3C, 0x62, 0x3A, 0x6C, 0x64, 0xDB, 0x0E, 0x61, 0xBD,
    0x29, 0x1C, 0x61, 0x4B, 0xE0, 0xDA, 0x07, 0xBA, 0x29, 0x81, 0xF0, 0x90,
    0x58, 0xB8, 0xBB, 0xF4, 0x69, 0xFF, 0x8F, 0x2B, 0x4A, 0x2D, 0x98, 0x51,
    0x37, 0xF5, 0x52, 0xCB, 0xE3, 0xC4, 0x6D, 0xEC, 0xEA, 0x32, 0x2D, 0xDD,
    0xD7, 0xFC, 0x43, 0xC6, 0x54, 0xE1, 0xC1, 0x66, 0x43, 0x37, 0x09, 0xE1,
    0xBF, 0xD1, 0x11, 0xFC, 0xDB, 0xBF, 0xDF, 0x66, 0x53, 0x8F, 0x38, 0x2D,
    0xAA, 0x89, 0xD2, 0x9F, 0x60, 0x90, 0xB7, 0x05, 0xC2, 0x20, 0x82, 0xE6,
    0xE0, 0x57, 0x55, 0xFF, 0x5F, 0xC1, 0x76, 0x66, 0x46, 0xF8, 0x67, 0xB8,
    0x8B, 0x81, 0x53, 0xA9, 0x8B, 0x48, 0x9E, 0x2A, 0xF9, 0x60, 0x57, 0xBA,
    0xD7, 0x52, 0x97, 0x53, 0xF0, 0x2F, 0x78, 0x68, 0x50, 0x18, 0x12, 0x00,
    0x5E, 0x8E, 0x2A, 0x62, 0x0D, 0x48, 0xA9, 0xB5, 0x6B, 0xBC, 0xA0, 0x52,
    0x53, 0xD7, 0x65, 0x23, 0xA4, 0xA5, 0xF5, 0x32, 0x49, 0x2D, 0xB2, 0x77,
    0x2C, 0x66, 0x97, 0xBA, 0x58, 0xE0, 0x16, 0x1C, 0x8C, 0x02, 0x5D, 0xE0,
    0x73, 0x2E, 0xDF, 0xB4, 0x2F, 0x4C, 0xA2, 0x11, 0x26, 0xC1, 0xAF, 0xAC,
    0x73, 0xBC, 0xB6, 0x98, 0xE0, 0x20, 0x61, 0x0E, 0x52, 0x4A, 0x6C, 0x80,
    0xB5, 0x0C, 0x10, 0x80, 0x09, 0x17, 0xF4, 0x9D, 0xFE, 0xB5, 0xFC, 0x63,
    0x9A, 0x80, 0x3F, 0x76,
};

// Another (new) test signing key raw bytes in DER-encoded PKCS8 format.
const uint8_t kSigningKey2[] = {
    0x30, 0x82, 0x01, 0x54, 0x02, 0x01, 0x00, 0x30, 0x0d, 0x06, 0x09, 0x2a,
    0x86, 0x48, 0x86, 0xf7, 0x0d, 0x01, 0x01, 0x01, 0x05, 0x00, 0x04, 0x82,
    0x01, 0x3e, 0x30, 0x82, 0x01, 0x3a, 0x02, 0x01, 0x00, 0x02, 0x41, 0x00,
    0x99, 0x98, 0x6b, 0x79, 0x5d, 0x38, 0x33, 0x79, 0x27, 0x0a, 0x2e, 0xb0,
    0x89, 0xba, 0xf8, 0xf6, 0x80, 0xde, 0xb0, 0x79, 0xf2, 0xd4, 0x6d, 0xf7,
    0x3c, 0xa3, 0x97, 0xf6, 0x4a, 0x3c, 0xa5, 0xcc, 0x40, 0x8a, 0xef, 0x59,
    0xaa, 0xc2, 0x82, 0x8f, 0xbc, 0x0d, 0x5b, 0x63, 0xc6, 0xaa, 0x72, 0xe2,
    0xf3, 0x57, 0xdd, 0x74, 0x00, 0xb0, 0x42, 0xd6, 0x27, 0xe7, 0x17, 0x61,
    0x0a, 0xdc, 0xc1, 0xf7, 0x02, 0x03, 0x01, 0x00, 0x01, 0x02, 0x40, 0x34,
    0xcf, 0xc9, 0xb4, 0x73, 0x2f, 0x0d, 0xd3, 0xcc, 0x6e, 0x9d, 0xdb, 0x29,
    0xa0, 0x56, 0x56, 0x3b, 0xbd, 0x56, 0x24, 0xb8, 0x2f, 0xfe, 0x97, 0x92,
    0x0c, 0x16, 0x06, 0x23, 0x44, 0x73, 0x25, 0x1d, 0x65, 0xf4, 0xda, 0x77,
    0xe7, 0x91, 0x2e, 0x91, 0x05, 0x10, 0xc1, 0x1b, 0x39, 0x5e, 0xb2, 0xf7,
    0xbd, 0x14, 0x19, 0xcb, 0x6b, 0xc3, 0xa9, 0xe8, 0x91, 0xf7, 0xa7, 0xa9,
    0x90, 0x08, 0x51, 0x02, 0x21, 0x00, 0xcc, 0x9e, 0x03, 0x54, 0x8f, 0x24,
    0xde, 0x90, 0x25, 0xec, 0x21, 0xaf, 0xe6, 0x27, 0x2a, 0x16, 0x42, 0x74,
    0xda, 0xf8, 0x84, 0xc4, 0x8c, 0x1e, 0x86, 0x12, 0x04, 0x5c, 0x17, 0x01,
    0xea, 0x9d, 0x02, 0x21, 0x00, 0xc0, 0x2a, 0x6c, 0xe9, 0xa1, 0x1a, 0x41,
    0x11, 0x94, 0x50, 0xf7, 0x1a, 0xd3, 0xbc, 0xf3, 0xa2, 0xf8, 0x46, 0xbc,
    0x26, 0x77, 0x78, 0xef, 0xc0, 0x54, 0xec, 0x22, 0x3f, 0x2c, 0x57, 0xe0,
    0xa3, 0x02, 0x20, 0x31, 0xf2, 0xc8, 0xa1, 0x55, 0xa8, 0x0c, 0x64, 0x67,
    0xbd, 0x72, 0xa3, 0xbb, 0xad, 0x07, 0xcb, 0x13, 0x41, 0xef, 0x4a, 0x07,
    0x2e, 0xeb, 0x7d, 0x70, 0x00, 0xe9, 0xeb, 0x88, 0xfa, 0x40, 0xc9, 0x02,
    0x20, 0x3a, 0xe0, 0xc4, 0xde, 0x10, 0x6e, 0x6a, 0xe1, 0x68, 0x00, 0x26,
    0xb6, 0x21, 0x8a, 0x13, 0x5c, 0x2b, 0x96, 0x00, 0xb0, 0x08, 0x8b, 0x15,
    0x6a, 0x68, 0x9a, 0xb1, 0x23, 0x8a, 0x02, 0xa2, 0xe1, 0x02, 0x21, 0x00,
    0xa3, 0xf2, 0x2d, 0x55, 0xc1, 0x6d, 0x40, 0xfa, 0x1d, 0xf7, 0xba, 0x86,
    0xef, 0x50, 0x98, 0xfc, 0xee, 0x09, 0xcc, 0xe7, 0x22, 0xb9, 0x4e, 0x80,
    0x32, 0x1a, 0x6b, 0xb3, 0x5f, 0x35, 0xbd, 0xf3,
};

// SHA256 signature of kSigningKey2 for "example.com" domain.
const uint8_t kSigningKey2Signature[] = {
    0x70, 0xED, 0x27, 0x42, 0x34, 0x69, 0xB6, 0x47, 0x9E, 0x7C, 0xA0, 0xF0,
    0xE5, 0x0A, 0x49, 0x49, 0x00, 0xDA, 0xBC, 0x70, 0x01, 0xC5, 0x4B, 0xDB,
    0x47, 0xD5, 0xAF, 0xA1, 0xAD, 0xB7, 0xE4, 0xE1, 0xBD, 0x5A, 0x1C, 0x35,
    0x44, 0x5A, 0xAA, 0xDB, 0x27, 0xBA, 0xA4, 0xA9, 0xC8, 0xDD, 0xEC, 0xD6,
    0xEB, 0xFE, 0xDB, 0xE0, 0x03, 0x5C, 0xA6, 0x2E, 0x5A, 0xEC, 0x75, 0x79,
    0xB8, 0x5F, 0x0A, 0xEE, 0x05, 0xB2, 0x61, 0xDC, 0x58, 0xF0, 0xD1, 0xCB,
    0x7B, 0x2A, 0xDB, 0xC1, 0x7C, 0x60, 0xE6, 0x3E, 0x87, 0x02, 0x61, 0xE6,
    0x90, 0xFD, 0x54, 0x65, 0xC7, 0xFF, 0x74, 0x09, 0xD6, 0xAA, 0x8E, 0xDC,
    0x5B, 0xC8, 0x38, 0x0C, 0x84, 0x0E, 0x84, 0x2E, 0x37, 0x2A, 0x4B, 0xDE,
    0x31, 0x82, 0x76, 0x1E, 0x77, 0xA5, 0xC1, 0xD5, 0xED, 0xFF, 0xBC, 0xEA,
    0x91, 0xB7, 0xBC, 0xFF, 0x76, 0x23, 0xE2, 0x78, 0x63, 0x01, 0x47, 0x80,
    0x47, 0x1F, 0x3A, 0x49, 0xBF, 0x0D, 0xCF, 0x27, 0x70, 0x92, 0xBB, 0xEA,
    0xB3, 0x92, 0x70, 0xFF, 0x1E, 0x4B, 0x1B, 0xE0, 0x4E, 0x0C, 0x4C, 0x6B,
    0x5D, 0x77, 0x06, 0xBB, 0xFB, 0x9B, 0x0E, 0x55, 0xB8, 0x8A, 0xF2, 0x45,
    0xA9, 0xF3, 0x54, 0x3D, 0x0C, 0xAC, 0xA8, 0x15, 0xD2, 0x31, 0x8D, 0x97,
    0x08, 0x73, 0xC9, 0x0F, 0x1D, 0xDE, 0x10, 0x22, 0xC6, 0x55, 0x53, 0x7F,
    0x7C, 0x50, 0x16, 0x5A, 0x08, 0xCC, 0x1C, 0x53, 0x9B, 0x02, 0xB8, 0x80,
    0xB7, 0x46, 0xF5, 0xF1, 0xC7, 0x3D, 0x36, 0xBD, 0x26, 0x02, 0xDE, 0x10,
    0xAB, 0x5A, 0x03, 0xCD, 0x67, 0x00, 0x1C, 0x23, 0xC7, 0x13, 0xEE, 0x5D,
    0xAF, 0xC5, 0x1F, 0xE3, 0xA0, 0x54, 0xAC, 0xC2, 0xC9, 0x44, 0xD4, 0x4A,
    0x09, 0x8E, 0xEB, 0xAE, 0xCA, 0x08, 0x8A, 0x7F, 0x41, 0x7B, 0xD8, 0x2C,
    0xDD, 0x6F, 0x80, 0xC3,
};

}  // namespace

std::unique_ptr<DMSigningKeyForTesting> GetTestKey1() {
  constexpr int kFakeKeyVersion = 5;
  return std::make_unique<DMSigningKeyForTesting>(
      kSigningKey1, sizeof(kSigningKey1), kSigningKey1Signature,
      sizeof(kSigningKey1Signature), kFakeKeyVersion, "example.com");
}

std::unique_ptr<DMSigningKeyForTesting> GetTestKey2() {
  constexpr int kFakeKeyVersion = 7;
  return std::make_unique<DMSigningKeyForTesting>(
      kSigningKey2, sizeof(kSigningKey2), kSigningKey2Signature,
      sizeof(kSigningKey2Signature), kFakeKeyVersion, "example.com");
}

std::unique_ptr<
    ::wireless_android_enterprise_devicemanagement::OmahaSettingsClientProto>
GetDefaultTestingOmahaPolicyProto() {
  auto omaha_settings =
      std::make_unique<::wireless_android_enterprise_devicemanagement::
                           OmahaSettingsClientProto>();

  omaha_settings->set_auto_update_check_period_minutes(111);
  omaha_settings->mutable_updates_suppressed()->set_start_hour(9);
  omaha_settings->mutable_updates_suppressed()->set_start_minute(30);
  omaha_settings->mutable_updates_suppressed()->set_duration_min(120);
  omaha_settings->set_download_preference("cacheable");
  omaha_settings->set_proxy_mode("pac_script");
  omaha_settings->set_proxy_pac_url("foo.c/proxy.pa");
  omaha_settings->set_install_default(
      ::wireless_android_enterprise_devicemanagement::INSTALL_ENABLED);
  omaha_settings->set_update_default(
      ::wireless_android_enterprise_devicemanagement::MANUAL_UPDATES_ONLY);

  ::wireless_android_enterprise_devicemanagement::ApplicationSettings app;
  app.set_app_guid(test::kChromeAppId);
  app.set_install(
      ::wireless_android_enterprise_devicemanagement::INSTALL_DISABLED);
  app.set_update(
      ::wireless_android_enterprise_devicemanagement::AUTOMATIC_UPDATES_ONLY);
  app.set_target_version_prefix("81.");
  app.set_rollback_to_target_version(
      ::wireless_android_enterprise_devicemanagement::
          ROLLBACK_TO_TARGET_VERSION_ENABLED);
  app.set_target_channel("beta");
  omaha_settings->mutable_application_settings()->Add(std::move(app));

  return omaha_settings;
}

std::unique_ptr<::enterprise_management::DeviceManagementResponse>
GetDefaultTestingPolicyFetchDMResponse(
    bool first_request,
    bool rotate_to_new_key,
    DMPolicyBuilderForTesting::SigningOption signing_option) {
  std::unique_ptr<DMPolicyBuilderForTesting> policy_builder =
      DMPolicyBuilderForTesting::CreateInstanceWithOptions(
          first_request, rotate_to_new_key, signing_option);
  DMPolicyMap policy_map;
  policy_map.emplace("google/machine-level-omaha",
                     GetDefaultTestingOmahaPolicyProto()->SerializeAsString());
  return policy_builder->BuildDMResponseForPolicies(policy_map);
}

DMSigningKeyForTesting::DMSigningKeyForTesting(const uint8_t key_data[],
                                               size_t key_data_length,
                                               const uint8_t key_signature[],
                                               size_t key_signature_length,
                                               int key_version,
                                               const std::string& domain)
    : key_data_(key_data, key_data + key_data_length),
      key_signature_(reinterpret_cast<const char*>(key_signature),
                     key_signature_length),
      key_version_(key_version),
      key_signature_domain_(domain) {}

DMSigningKeyForTesting::~DMSigningKeyForTesting() = default;

std::string DMSigningKeyForTesting::GetPublicKeyString() const {
  std::vector<uint8_t> public_key;
  std::unique_ptr<crypto::RSAPrivateKey> private_key =
      crypto::RSAPrivateKey::CreateFromPrivateKeyInfo(key_data_);
  private_key->ExportPublicKey(&public_key);
  return std::string(reinterpret_cast<const char*>(public_key.data()),
                     public_key.size());
}

void DMSigningKeyForTesting::SignData(const std::string& data,
                                      std::string* signature) const {
  std::unique_ptr<crypto::RSAPrivateKey> private_key =
      crypto::RSAPrivateKey::CreateFromPrivateKeyInfo(key_data_);
  std::unique_ptr<crypto::SignatureCreator> signature_creator(
      crypto::SignatureCreator::Create(private_key.get(),
                                       crypto::SignatureCreator::SHA256));
  signature_creator->Update(reinterpret_cast<const uint8_t*>(data.c_str()),
                            data.size());
  std::vector<uint8_t> signature_bytes;
  signature_creator->Final(&signature_bytes);

  signature->assign(reinterpret_cast<const char*>(signature_bytes.data()),
                    signature_bytes.size());
}

DMPolicyBuilderForTesting::DMPolicyBuilderForTesting(
    const std::string& dm_token,
    const std::string& user_name,
    const std::string& device_id,
    std::unique_ptr<DMSigningKeyForTesting> signing_key,
    std::unique_ptr<DMSigningKeyForTesting> new_signing_key,
    SigningOption signing_option)
    : dm_token_(dm_token),
      user_name_(user_name),
      device_id_(device_id),
      signing_option_(signing_option),
      signing_key_(std::move(signing_key)),
      new_signing_key_(std::move(new_signing_key)) {}

DMPolicyBuilderForTesting::~DMPolicyBuilderForTesting() = default;

// static
std::unique_ptr<DMPolicyBuilderForTesting>
DMPolicyBuilderForTesting::CreateInstanceWithOptions(
    bool first_request,
    bool rotate_to_new_key,
    SigningOption signing_option) {
  std::unique_ptr<DMSigningKeyForTesting> signing_key;
  std::unique_ptr<DMSigningKeyForTesting> new_signing_key;

  if (first_request) {
    new_signing_key = GetTestKey1();
  } else {
    signing_key = GetTestKey1();
    if (rotate_to_new_key) {
      new_signing_key = GetTestKey2();
    }
  }

  return std::make_unique<DMPolicyBuilderForTesting>(
      "test-dm-token", "<EMAIL>", "test-device-id",
      std::move(signing_key), std::move(new_signing_key), signing_option);
}

void DMPolicyBuilderForTesting::FillPolicyFetchResponseWithPayload(
    enterprise_management::PolicyFetchResponse* policy_response,
    const std::string& policy_type,
    const std::string& policy_payload) const {
  const DMSigningKeyForTesting* signing_key = signing_key_.get();
  if (new_signing_key_) {
    signing_key = new_signing_key_.get();

    // Attach the new public key and its signature to the policy response.
    std::string new_public_key_string = new_signing_key_->GetPublicKeyString();
    policy_response->set_new_public_key(new_public_key_string);
    policy_response->set_new_public_key_verification_signature_deprecated(
        new_signing_key_->GetPublicKeySignature());
    if (signing_key_) {
      if (signing_option_ == SigningOption::kTamperKeySignature) {
        *policy_response->mutable_new_public_key_signature() = "bad-key-sig";
      } else {
        signing_key_->SignData(
            policy_response->new_public_key(),
            policy_response->mutable_new_public_key_signature());
      }
    }

    // Attach the new public key verification data to policy response.
    enterprise_management::PublicKeyVerificationData key_verif_data;
    key_verif_data.set_new_public_key(new_public_key_string);
    key_verif_data.set_domain(new_signing_key_->GetPublicKeySignatureDomain());

    policy_response->set_new_public_key_verification_data(
        key_verif_data.SerializeAsString());
    policy_response->set_new_public_key_verification_data_signature(
        new_signing_key_->GetPublicKeySignature());
  } else {
    policy_response->clear_new_public_key();
    policy_response->clear_new_public_key_verification_data();
    policy_response->clear_new_public_key_verification_data_signature();
    policy_response->clear_new_public_key_verification_signature_deprecated();
  }

  enterprise_management::PolicyData policy_data;
  policy_data.set_policy_type(policy_type);
  policy_data.set_timestamp(time(nullptr));
  policy_data.set_request_token(dm_token_);
  if (signing_key->has_key_version()) {
    policy_data.set_public_key_version(signing_key->key_version());
  }
  policy_data.set_username(user_name_);
  policy_data.set_device_id(device_id_);
  policy_data.set_policy_value(policy_payload);
  policy_data.SerializeToString(policy_response->mutable_policy_data());

  // PolicyData signature.
  if (signing_option_ == SigningOption::kTamperDataSignature) {
    *policy_response->mutable_policy_data_signature() = "bad-data-sig";
  } else {
    signing_key->SignData(policy_response->policy_data(),
                          policy_response->mutable_policy_data_signature());
  }
}

std::string DMPolicyBuilderForTesting::GetResponseBlobForPolicyPayload(
    const std::string& policy_type,
    const std::string& policy_payload) const {
  enterprise_management::PolicyFetchResponse policy_response;
  FillPolicyFetchResponseWithPayload(&policy_response, policy_type,
                                     policy_payload);
  return policy_response.SerializeAsString();
}

std::unique_ptr<::enterprise_management::DeviceManagementResponse>
DMPolicyBuilderForTesting::BuildDMResponseForPolicies(
    const base::flat_map<std::string, std::string>& policies) const {
  auto dm_response =
      std::make_unique<::enterprise_management::DeviceManagementResponse>();

  for (const auto& policy : policies) {
    FillPolicyFetchResponseWithPayload(
        dm_response->mutable_policy_response()->add_responses(), policy.first,
        policy.second);
  }
  return dm_response;
}

}  // namespace updater
