<html>
<head>
<script>
function dragEnter(div)
{
    div.innerText = "Success: ";
}
</script>
</head>
<body contenteditable="true"><span>
<span id="elementToDrag">Text</span>
<div id="dragTarget" class="dragTarget" ondragenter="dragEnter(this);" style="width: 300px; height: 300px; border: 1px solid gray;""></div></span>
<p>This automated layout test checks to see that ondragenter events are being sent.</p>
<script>
function runTest()
{
    if (!window.testRunner)
        return;
        
    testRunner.waitUntilDone();
    testRunner.dumpAsText();
    
    // Find the element to drag
    var elementToDrag = document.getElementById("elementToDrag");
    var x = elementToDrag.offsetLeft + elementToDrag.offsetWidth / 2;
    var y = elementToDrag.offsetTop + elementToDrag.offsetHeight / 2;
    // Double click on the element to select its text
    eventSender.mouseMoveTo(x, y);
    eventSender.mouseDown();
    eventSender.mouseUp();
    eventSender.mouseDown();
    eventSender.mouseUp();
    
    // Wait a moment so that the next mouseDown will kick off a drag, instead of a triple-click
    eventSender.leapForward(1300);
    eventSender.mouseDown();
    // Wait a moment so that the mouseDown will kick off a drag instead of starting a new selection.
    eventSender.leapForward(400);

    // Drag to the middle of the destination element
    var dragTarget = document.getElementById("dragTarget");
    x = dragTarget.offsetLeft + dragTarget.offsetWidth / 2;
    y = dragTarget.offsetTop + dragTarget.offsetHeight / 2;
    eventSender.mouseMoveTo(x, y);
    eventSender.mouseUp();

    testRunner.notifyDone();
}

runTest();
</script>

</body>
</html>
