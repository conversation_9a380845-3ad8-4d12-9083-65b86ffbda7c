<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

 <head>

  <title>CSS Reftest Reference</title>
  <link rel="author" title="Gérard Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" />

  <style type="text/css"><![CDATA[
  body
  {
  background-color: navy;
  border: blue solid;
  color: white;
  margin: 1em;
  padding: 2em;
  }

  div
  {
  background-color: green;
  border: lime solid;
  padding: 3em;
  }

  strong
  {
  margin: 0em 1em;
  padding: 1em;
  }
  ]]></style>

 </head>

 <body>

  <div>This paragraph should be a box that is green with a green border, which should be in a navy box with a bright blue border. The rest of the page should also be navy. <strong>The outer sea of blue should extend to the edges of the viewport, but the boxes should just wrap around this text and not grow to match the viewport.</strong></div>

 </body>
</html>
