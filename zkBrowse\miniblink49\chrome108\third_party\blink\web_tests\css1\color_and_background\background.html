<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.3.7 background</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
BODY {background: green url(../resources/oransqr.gif) repeat-x center top fixed;}
.one {background: lime url(../resources/oransqr.gif) repeat-y 100% 0%;}
.two {background: lime url(../resources/oransqr.gif) repeat-y center top;}
.three {background: lime url(../resources/oransqr.gif) repeat-x left top;}</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>BODY {background: green url(../resources/oransqr.gif) repeat-x center top fixed;}
.one {background: lime url(../resources/oransqr.gif) repeat-y 100% 0%;}
.two {background: lime url(../resources/oransqr.gif) repeat-y center top;}
.three {background: lime url(../resources/oransqr.gif) repeat-x left top;}
</PRE>
<HR>
<P>
This document should have a green background with an orange strip running across the entire top of the page, since <CODE>repeat-x</CODE> indicates tiling in both directions of the x-axis.  Furthermore, the strip should be fixed in place.  I'll have to add extra text at the end of this page to make it long enough to scroll conveniently.
</P>
<P class="one">
This paragraph should have a lime background and an orange strip which starts at the top right and runs to the bottom.  Therefore, extra text would be in order, so that we can intelligently evaluate the performance of your browser in handling these declarations.  Hey, I didn't say the page would be pretty, did I?
</P>
<P class="two">
This paragraph should have a lime background and an orange strip which starts at the center top and runs to the bottom.  Therefore, extra text would be in order, so that we can intelligently evaluate the performance of your browser in handling these declarations.  Hey, I didn't say the page would be pretty, did I?
</P>
<P class="three">
This paragraph should have a lime background and an orange strip which starts at the top left and runs to the top right.  Therefore, extra text would be in order, so that we can intelligently evaluate the performance of your browser in handling these declarations.  Hey, I didn't say the page would be pretty, did I?
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P>
This document should have a green background with an orange strip running across the entire top of the page, since <CODE>repeat-x</CODE> indicates tiling in both directions of the x-axis.  Furthermore, the strip should be fixed in place.  I'll have to add extra text at the end of this page to make it long enough to scroll conveniently.
</P>
<P class="one">
This paragraph should have a lime background and an orange strip which starts at the top right and runs to the bottom.  Therefore, extra text would be in order, so that we can intelligently evaluate the performance of your browser in handling these declarations.  Hey, I didn't say the page would be pretty, did I?
</P>
<P class="two">
This paragraph should have a lime background and an orange strip which starts at the center top and runs to the bottom.  Therefore, extra text would be in order, so that we can intelligently evaluate the performance of your browser in handling these declarations.  Hey, I didn't say the page would be pretty, did I?
</P>
<P class="three">
This paragraph should have a lime background and an orange strip which starts at the top left and runs to the top right.  Therefore, extra text would be in order, so that we can intelligently evaluate the performance of your browser in handling these declarations.  Hey, I didn't say the page would be pretty, did I?
</P>
</TD></TR></TABLE></BODY>
</HTML>
