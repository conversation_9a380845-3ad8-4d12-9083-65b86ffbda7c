<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background with (color image position)</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <meta name="flags" content="image" />
        <meta name="assert" content="Background with (color image position) sets the background of the element to the image specified. Since the initial value for background-repeat is repeat the position designates where the image tiles from and color is completely overlapped by the placements of the image." />
        <style type="text/css">
            div
            {
                background: green url("support/cat.png") right top;
                height: 198px;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is a box filled with cat images and the one in the right top corner is not cut off.</p>
        <div></div>
    </body>
</html>