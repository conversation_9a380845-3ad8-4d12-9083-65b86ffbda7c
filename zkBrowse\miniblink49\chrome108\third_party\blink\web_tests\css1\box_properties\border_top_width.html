<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.5.11 border-top-width</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
.zero {background-color: silver; border-top-width: 0;}
.one {border-top-width: 25px; border-style: solid;}
.two {border-top-width: thick; border-style: solid;}
.three {border-top-width: medium; border-style: solid;}
.four {border-top-width: thin; border-style: solid;}
.five {border-top-width: 25px;}</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.zero {background-color: silver; border-top-width: 0;}
.one {border-top-width: 25px; border-style: solid;}
.two {border-top-width: thick; border-style: solid;}
.three {border-top-width: medium; border-style: solid;}
.four {border-top-width: thin; border-style: solid;}
.five {border-top-width: 25px;}
</PRE>
<HR>
<P>
(These will only work if <CODE>border-style</CODE> is supported.)
</P>
<P class="zero">
This element has a class of zero.
</P>
<P class="one">
This paragraph should have a top border width of 25 pixels.
</P>
<P class="two">
This paragraph should have a thick top border width.
</P>
<P class="three">
This paragraph should have a medium top border width.
</P>
<P class="four">
This paragraph should have a thin top border width.
</P>
<P class="five">
This paragraph should have no border and no extra "padding" on its top side, as no <CODE>border-style</CODE> was set.
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P>
(These will only work if <CODE>border-style</CODE> is supported.)
</P>
<P class="zero">
This element has a class of zero.
</P>
<P class="one">
This paragraph should have a top border width of 25 pixels.
</P>
<P class="two">
This paragraph should have a thick top border width.
</P>
<P class="three">
This paragraph should have a medium top border width.
</P>
<P class="four">
This paragraph should have a thin top border width.
</P>
<P class="five">
This paragraph should have no border and no extra "padding" on its top side, as no <CODE>border-style</CODE> was set.
</P>
</TD></TR></TABLE></BODY>
</HTML>
