// Copyright 2017 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

module audio.mojom;

import "media/mojo/mojom/audio_parameters.mojom";
import "services/audio/public/mojom/audio_device_description.mojom";

// Provides information about audio system.
interface SystemInfo {
  // Replies with parameters for a specified input device (empty parameters if
  // the device is not found).
  GetInputStreamParameters(string device_id)
      => (media.mojom.AudioParameters? params);

  // Replies with parameters for a specified output device (empty parameters if
  // the device is not found).
  GetOutputStreamParameters(string device_id)
      => (media.mojom.AudioParameters? params);

  // Whether or not the system has input audio devices.
  HasInputDevices() => (bool has_input_devices);

  // Whether or not the system has output audio devices.
  HasOutputDevices() => (bool has_output_devices);

  // Replies with descriptions of input audio devices.
  GetInputDeviceDescriptions()
      => (array<audio.mojom.AudioDeviceDescription> device_descriptions);

  // Replies with descriptions of output audio devices.
  GetOutputDeviceDescriptions()
      => (array<audio.mojom.AudioDeviceDescription> device_descriptions);

  // Replies with an id of an output device associated with a specified input
  // device (empty if there is no association).
  GetAssociatedOutputDeviceID(string input_device_id)
      => (string? associated_output_device_id);

  // Replies with audio parameters for the specified input device (empty if
  // device is not found) and the device ID of the associated output device
  // (empty if there is no association).
  // This is a conjunction of GetInputStreamParameters() and
  // GetAssociatedOutputDeviceID() used to save on round trips.
  // TODO(olka, guido): consider getting rid of it.
  GetInputDeviceInfo(string input_device_id)
      => (media.mojom.AudioParameters? input_params,
          string? associated_output_device_id);
};
