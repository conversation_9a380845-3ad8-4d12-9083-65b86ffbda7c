<!DOCTYPE html>
<html>
<body>
<p>This test ensures WebKit does not fire click event on a node that has been removed in mouseup event.
To manually test, click a box below.</p>
<div id="test"><img src="" width="100" height="100" border="1" id="target" onmouseup="mouseup()" onclick="test.innerHTML = 'FAIL';"></div>
<script>

var test = document.getElementById('test');
var target = document.getElementById('target');

function mouseup() {
    test.appendChild(document.createTextNode('PASS'));
    target.parentNode.removeChild(target);
}

if (window.testRunner) {
    testRunner.dumpAsText();
    if (!window.eventSender)
        test.innerHTML = 'FAIL - this test requires eventSender';
    else {
        eventSender.mouseMoveTo(target.offsetLeft + target.offsetWidth / 2, target.offsetTop + target.offsetHeight / 2);
        eventSender.mouseDown();
        eventSender.leapForward(200);
        eventSender.mouseUp();
    }
}

</script>
</body>
</html>
