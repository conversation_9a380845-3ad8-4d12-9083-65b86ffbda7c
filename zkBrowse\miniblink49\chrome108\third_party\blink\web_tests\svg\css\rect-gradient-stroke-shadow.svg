<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" >
    <defs>
        <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stop-color="red"/>
            <stop offset="100%" stop-color="blue"/>
        </linearGradient>
        <filter id="shadow" width="200%" height="200%">
            <feOffset result="offOut" in="SourceAlpha" dx="75" dy="75"/>
            <feGaussianBlur result="blurOut" in="offOut" stdDeviation="10"/>
            <feBlend in="SourceGraphic" in2="blurOut" mode="normal"/>
        </filter>
    </defs>
    <rect x="100" y="100" width="300" height="300" style="fill:rgba(0,0,0,0); stroke-width:50; stroke:url(#gradient); filter:url(#shadow)"/>
</svg>
