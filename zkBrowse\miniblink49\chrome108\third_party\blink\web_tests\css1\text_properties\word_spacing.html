<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.4.1 word-spacing</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
.one {word-spacing: 0.3in;}
.two {word-spacing: 0.5cm;}
.three {word-spacing: 5mm;}
.four {word-spacing: 3pt;}
.five {word-spacing: 0.25pc;}
.six {word-spacing: 1em;}
.seven {word-spacing: 1ex;}
.eight {word-spacing: 5px;}
.nine {word-spacing: normal;}
.ten {word-spacing: 300%;}
.eleven {word-spacing: -0.2em;}
</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.one {word-spacing: 0.3in;}
.two {word-spacing: 0.5cm;}
.three {word-spacing: 5mm;}
.four {word-spacing: 3pt;}
.five {word-spacing: 0.25pc;}
.six {word-spacing: 1em;}
.seven {word-spacing: 1ex;}
.eight {word-spacing: 5px;}
.nine {word-spacing: normal;}
.ten {word-spacing: 300%;}
.eleven {word-spacing: -0.2em;}

</PRE>
<HR>
<P class="one">
This words in this sentence should have extra space between them.
</P>
<P class="two">
This words in this sentence should have extra space between them.
</P>
<P class="three">
This words in this sentence should have extra space between them.
</P>
<P class="four">
This words in this sentence should have extra space between them.
</P>
<P class="five">
This words in this sentence should have extra space between them.
</P>
<P class="six">
This words in this sentence should have extra space between them.
</P>
<P class="seven">
This words in this sentence should have extra space between them.
</P>
<P class="eight">
This words in this sentence should have extra space between them, but the last few words in the sentence <SPAN class="nine">should have normal spacing</SPAN>.
</P>
<P class="ten">
This sentence should have normal word-spacing, since percentage values are not allowed on this property.
</P>
<P class="eleven">
This words in this sentence should have reduced space between them, since negative values are allowed on this property.
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P class="one">
This words in this sentence should have extra space between them.
</P>
<P class="two">
This words in this sentence should have extra space between them.
</P>
<P class="three">
This words in this sentence should have extra space between them.
</P>
<P class="four">
This words in this sentence should have extra space between them.
</P>
<P class="five">
This words in this sentence should have extra space between them.
</P>
<P class="six">
This words in this sentence should have extra space between them.
</P>
<P class="seven">
This words in this sentence should have extra space between them.
</P>
<P class="eight">
This words in this sentence should have extra space between them, but the last few words in the sentence <SPAN class="nine">should have normal spacing</SPAN>.
</P>
<P class="ten">
This sentence should have normal word-spacing, since percentage values are not allowed on this property.
</P>
<P class="eleven">
This words in this sentence should have reduced space between them, since negative values are allowed on this property.
</P>
</TD></TR></TABLE></BODY>
</HTML>
