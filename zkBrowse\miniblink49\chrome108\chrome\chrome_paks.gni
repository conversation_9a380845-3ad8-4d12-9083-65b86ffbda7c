# Copyright 2016 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//ash/ambient/resources/resources.gni")
import("//build/config/chromebox_for_meetings/buildflags.gni")
import("//build/config/chromeos/ui_mode.gni")
import("//build/config/locales.gni")
import("//chrome/browser/buildflags.gni")
import("//chrome/common/features.gni")
import("//extensions/buildflags/buildflags.gni")
import("//pdf/features.gni")
import("//ui/base/ui_features.gni")
import("chrome_repack_locales.gni")

# Generates a rule to repack a set of resources, substituting a given string
# in for the percentage (e.g. "100", "200"). It generates the repacked files in
# the "gen" directory, and then introduces a copy rule to copy it to the root
# build directory.
#
# Argument:
#   percent [required]
#      String to substitute for the percentage.
template("chrome_repack_percent") {
  percent = invoker.percent

  repack(target_name) {
    forward_variables_from(invoker,
                           [
                             "copy_data_to_bundle",
                             "mark_as_data",
                             "repack_allowlist",
                             "visibility",
                           ])

    # All sources should also have deps for completeness.
    sources = [
      "$root_gen_dir/chrome/renderer_resources_${percent}_percent.pak",
      "$root_gen_dir/chrome/theme_resources_${percent}_percent.pak",
      "$root_gen_dir/components/components_resources_${percent}_percent.pak",
      "$root_gen_dir/third_party/blink/public/resources/blink_scaled_resources_${percent}_percent.pak",
      "$root_gen_dir/ui/resources/ui_resources_${percent}_percent.pak",
    ]

    deps = [
      "//chrome/app/theme:theme_resources",
      "//chrome/renderer:resources",
      "//components/resources",
      "//components/strings",
      "//net:net_resources",
      "//third_party/blink/public:scaled_resources_${percent}_percent",
      "//ui/resources",
    ]

    if (defined(invoker.deps)) {
      deps += invoker.deps
    }

    if (toolkit_views) {
      sources += [ "$root_gen_dir/ui/views/resources/views_resources_${percent}_percent.pak" ]
      deps += [ "//ui/views/resources" ]
    }
    if (is_chromeos_ash) {
      sources += [
        "$root_gen_dir/ash/login/resources/login_resources_${percent}_percent.pak",
        "$root_gen_dir/chrome/app_icon_resources_${percent}_percent.pak",
        "$root_gen_dir/ui/chromeos/resources/ui_chromeos_resources_${percent}_percent.pak",
      ]
      deps += [
        "//ash/login/resources",
        "//chrome/browser/resources/chromeos:app_icon_resources",
        "//ui/chromeos/resources",
      ]
    }
    if (enable_extensions) {
      sources += [ "$root_gen_dir/extensions/extensions_browser_resources_${percent}_percent.pak" ]
      deps += [ "//extensions:extensions_browser_resources" ]
    }

    output = "${invoker.output_dir}/chrome_${percent}_percent.pak"
  }
}

template("chrome_extra_paks") {
  repack(target_name) {
    forward_variables_from(invoker,
                           [
                             "copy_data_to_bundle",
                             "mark_as_data",
                             "repack_allowlist",
                             "visibility",
                           ])
    output = "${invoker.output_dir}/resources.pak"
    sources = [
      "$root_gen_dir/base/tracing/protos/tracing_proto_resources.pak",
      "$root_gen_dir/chrome/browser_resources.pak",
      "$root_gen_dir/chrome/chrome_unscaled_resources.pak",
      "$root_gen_dir/chrome/common_resources.pak",
      "$root_gen_dir/chrome/segmentation_internals_resources.pak",
      "$root_gen_dir/components/autofill/core/browser/autofill_address_rewriter_resources.pak",
      "$root_gen_dir/components/components_resources.pak",
      "$root_gen_dir/content/content_resources.pak",
      "$root_gen_dir/content/indexed_db_resources.pak",
      "$root_gen_dir/content/quota_internals_resources.pak",
      "$root_gen_dir/mojo/public/js/mojo_bindings_resources.pak",
      "$root_gen_dir/net/net_resources.pak",
      "$root_gen_dir/skia/skia_resources.pak",
      "$root_gen_dir/third_party/blink/public/resources/blink_resources.pak",
      "$root_gen_dir/third_party/blink/public/resources/inspector_overlay_resources.pak",
      "$root_gen_dir/ui/resources/webui_generated_resources.pak",
    ]
    deps = [
      "//base/tracing/protos:chrome_track_event_resources",
      "//chrome/app/theme:chrome_unscaled_resources",
      "//chrome/browser:resources",
      "//chrome/browser/resources/segmentation_internals:resources",
      "//chrome/common:resources",
      "//components/autofill/core/browser:autofill_address_rewriter_resources",
      "//components/history_clusters/history_clusters_internals/resources",
      "//components/metrics/debug:resources",
      "//components/optimization_guide/optimization_guide_internals/resources",
      "//components/resources",
      "//content:content_resources",
      "//content/browser/resources/indexed_db:resources",
      "//content/browser/resources/quota:resources",
      "//mojo/public/js:resources",
      "//net:net_resources",
      "//skia:skia_resources",
      "//third_party/blink/public:devtools_inspector_resources",
      "//third_party/blink/public:resources",
      "//ui/resources",
    ]
    if (defined(invoker.deps)) {
      deps += invoker.deps
    }
    if (defined(invoker.additional_paks)) {
      sources += invoker.additional_paks
    }

    if (!is_android) {
      # New paks should be added here by default.
      sources += [
        "$root_gen_dir/chrome/access_code_cast_resources.pak",
        "$root_gen_dir/chrome/app_service_internals_resources.pak",
        "$root_gen_dir/chrome/bookmarks_resources.pak",
        "$root_gen_dir/chrome/commander_resources.pak",
        "$root_gen_dir/chrome/component_extension_resources.pak",
        "$root_gen_dir/chrome/dev_ui_resources.pak",
        "$root_gen_dir/chrome/downloads_resources.pak",
        "$root_gen_dir/chrome/feed_resources.pak",
        "$root_gen_dir/chrome/feedback_resources.pak",
        "$root_gen_dir/chrome/gaia_auth_host_resources.pak",
        "$root_gen_dir/chrome/history_resources.pak",
        "$root_gen_dir/chrome/identity_internals_resources.pak",
        "$root_gen_dir/chrome/image_editor_resources.pak",
        "$root_gen_dir/chrome/image_editor_untrusted_resources.pak",
        "$root_gen_dir/chrome/inline_login_resources.pak",
        "$root_gen_dir/chrome/management_resources.pak",
        "$root_gen_dir/chrome/new_tab_page_instant_resources.pak",
        "$root_gen_dir/chrome/new_tab_page_resources.pak",
        "$root_gen_dir/chrome/new_tab_page_third_party_resources.pak",
        "$root_gen_dir/chrome/password_manager_resources.pak",
        "$root_gen_dir/chrome/privacy_sandbox_resources.pak",
        "$root_gen_dir/chrome/profile_internals_resources.pak",
        "$root_gen_dir/chrome/settings_resources.pak",
        "$root_gen_dir/chrome/side_panel_customize_chrome_resources.pak",
        "$root_gen_dir/chrome/side_panel_resources.pak",
        "$root_gen_dir/chrome/signin_resources.pak",
        "$root_gen_dir/chrome/support_tool_resources.pak",
        "$root_gen_dir/chrome/tab_search_resources.pak",
        "$root_gen_dir/chrome/webui_gallery_resources.pak",
        "$root_gen_dir/chrome/whats_new_resources.pak",
        "$root_gen_dir/content/browser/devtools/devtools_resources.pak",
        "$root_gen_dir/content/browser/tracing/tracing_resources.pak",
      ]
      deps += [
        "//chrome/browser/resources:component_extension_resources",
        "//chrome/browser/resources:dev_ui_paks",
        "//chrome/browser/resources/access_code_cast:resources",
        "//chrome/browser/resources/app_service_internals:resources",
        "//chrome/browser/resources/bookmarks:resources",
        "//chrome/browser/resources/commander:resources",
        "//chrome/browser/resources/downloads:resources",
        "//chrome/browser/resources/feed:resources",
        "//chrome/browser/resources/feedback:resources",
        "//chrome/browser/resources/gaia_auth_host:resources",
        "//chrome/browser/resources/history:resources",
        "//chrome/browser/resources/identity_internals:resources",
        "//chrome/browser/resources/image_editor:trusted_resources",
        "//chrome/browser/resources/image_editor:untrusted_resources",
        "//chrome/browser/resources/inline_login:resources",
        "//chrome/browser/resources/management:resources",
        "//chrome/browser/resources/new_tab_page:resources",
        "//chrome/browser/resources/new_tab_page_instant:resources",
        "//chrome/browser/resources/new_tab_page_third_party:resources",
        "//chrome/browser/resources/password_manager:resources",
        "//chrome/browser/resources/privacy_sandbox:resources",
        "//chrome/browser/resources/profile_internals:resources",
        "//chrome/browser/resources/settings:resources",
        "//chrome/browser/resources/side_panel:resources",
        "//chrome/browser/resources/side_panel/customize_chrome:resources",
        "//chrome/browser/resources/signin:resources",
        "//chrome/browser/resources/support_tool:resources",
        "//chrome/browser/resources/tab_search:resources",
        "//chrome/browser/resources/webui_gallery:resources",
        "//chrome/browser/resources/whats_new:resources",
        "//content/browser/devtools:devtools_resources",
        "//content/browser/tracing:resources",
      ]
      if (is_chrome_branded) {
        sources += [
          "$root_gen_dir/chrome/media_router_feedback_resources.pak",
          "$root_gen_dir/chrome/preinstalled_web_apps_resources.pak",
        ]
        deps += [
          "//chrome/browser/resources:preinstalled_web_apps_resources",
          "//chrome/browser/resources/media_router/cast_feedback:resources",
        ]
      }
    }
    if (is_chromeos_ash) {
      sources += [
        "$root_gen_dir/ash/public/cpp/resources/ash_public_unscaled_resources.pak",
        "$root_gen_dir/ash/webui/ash_camera_app_resources.pak",
        "$root_gen_dir/ash/webui/ash_color_internals_resources.pak",
        "$root_gen_dir/ash/webui/ash_demo_mode_app_resources.pak",
        "$root_gen_dir/ash/webui/ash_diagnostics_app_resources.pak",
        "$root_gen_dir/ash/webui/ash_eche_app_resources.pak",
        "$root_gen_dir/ash/webui/ash_eche_bundle_resources.pak",
        "$root_gen_dir/ash/webui/ash_face_ml_app_bundle_resources.pak",
        "$root_gen_dir/ash/webui/ash_face_ml_app_resources.pak",
        "$root_gen_dir/ash/webui/ash_face_ml_app_untrusted_resources.pak",
        "$root_gen_dir/ash/webui/ash_firmware_update_app_resources.pak",
        "$root_gen_dir/ash/webui/ash_guest_os_installer_resources.pak",
        "$root_gen_dir/ash/webui/ash_help_app_resources.pak",
        "$root_gen_dir/ash/webui/ash_media_app_resources.pak",
        "$root_gen_dir/ash/webui/ash_multidevice_debug_resources.pak",
        "$root_gen_dir/ash/webui/ash_os_feedback_resources.pak",
        "$root_gen_dir/ash/webui/ash_os_feedback_untrusted_resources.pak",
        "$root_gen_dir/ash/webui/ash_personalization_app_resources.pak",
        "$root_gen_dir/ash/webui/ash_print_management_resources.pak",
        "$root_gen_dir/ash/webui/ash_projector_annotator_trusted_resources.pak",
        "$root_gen_dir/ash/webui/ash_projector_annotator_untrusted_resources.pak",
        "$root_gen_dir/ash/webui/ash_projector_app_trusted_resources.pak",
        "$root_gen_dir/ash/webui/ash_projector_app_untrusted_resources.pak",
        "$root_gen_dir/ash/webui/ash_projector_common_resources.pak",
        "$root_gen_dir/ash/webui/ash_scanning_app_resources.pak",
        "$root_gen_dir/ash/webui/ash_shimless_rma_resources.pak",
        "$root_gen_dir/ash/webui/ash_shortcut_customization_app_resources.pak",
        "$root_gen_dir/ash/webui/ash_system_extensions_internals_resources.pak",
        "$root_gen_dir/ash/webui/connectivity_diagnostics_resources.pak",
        "$root_gen_dir/ash/webui/file_manager/resources/file_manager_swa_resources.pak",
        "$root_gen_dir/ash/webui/file_manager/untrusted_resources/file_manager_untrusted_resources.pak",
        "$root_gen_dir/chrome/arc_account_picker_resources.pak",
        "$root_gen_dir/chrome/assistant_optin_resources.pak",
        "$root_gen_dir/chrome/audio_resources.pak",
        "$root_gen_dir/chrome/bluetooth_pairing_dialog_resources.pak",
        "$root_gen_dir/chrome/browser/supervised_user/supervised_user_unscaled_resources.pak",
        "$root_gen_dir/chrome/cloud_upload_resources.pak",
        "$root_gen_dir/chrome/desk_api_resources.pak",
        "$root_gen_dir/chrome/emoji_picker_resources.pak",
        "$root_gen_dir/chrome/gaia_action_buttons_resources.pak",
        "$root_gen_dir/chrome/internet_config_dialog_resources.pak",
        "$root_gen_dir/chrome/internet_detail_dialog_resources.pak",
        "$root_gen_dir/chrome/launcher_internals_resources.pak",
        "$root_gen_dir/chrome/manage_mirrorsync_resources.pak",
        "$root_gen_dir/chrome/multidevice_internals_resources.pak",
        "$root_gen_dir/chrome/multidevice_setup_resources.pak",
        "$root_gen_dir/chrome/nearby_internals_resources.pak",
        "$root_gen_dir/chrome/nearby_share_dialog_resources.pak",
        "$root_gen_dir/chrome/network_ui_resources.pak",
        "$root_gen_dir/chrome/notification_tester_resources.pak",
        "$root_gen_dir/chrome/oobe_conditional_resources.pak",
        "$root_gen_dir/chrome/oobe_unconditional_resources.pak",
        "$root_gen_dir/chrome/os_settings_resources.pak",
        "$root_gen_dir/chrome/password_change_resources.pak",
        "$root_gen_dir/chromeos/ash/ash_resources.pak",
        "$root_gen_dir/chromeos/chromeos_help_app_bundle_resources.pak",
        "$root_gen_dir/chromeos/chromeos_help_app_kids_magazine_bundle_resources.pak",
        "$root_gen_dir/chromeos/chromeos_media_app_bundle_resources.pak",
        "$root_gen_dir/chromeos/chromeos_projector_app_bundle_resources.pak",
        "$root_gen_dir/chromeos/chromeos_resources.pak",
        "$root_gen_dir/components/arc/input_overlay_resources.pak",
        "$root_gen_dir/ui/file_manager/file_manager_gen_resources.pak",
        "$root_gen_dir/ui/file_manager/file_manager_resources.pak",
      ]
      deps += [
        "//ash/components/arc/input_overlay/resources",
        "//ash/public/cpp/resources:ash_public_unscaled_resources",
        "//ash/webui/file_manager/resources:file_manager_swa_resources",
        "//ash/webui/file_manager/untrusted_resources:file_manager_untrusted_resources",
        "//ash/webui/guest_os_installer/resources:resources",
        "//ash/webui/print_management/resources:resources",
        "//ash/webui/resources:camera_app_resources",
        "//ash/webui/resources:color_internals_resources",
        "//ash/webui/resources:connectivity_diagnostics_resources",
        "//ash/webui/resources:demo_mode_app_resources",
        "//ash/webui/resources:diagnostics_app_resources",
        "//ash/webui/resources:eche_app_resources",
        "//ash/webui/resources:eche_bundle_resources",
        "//ash/webui/resources:face_ml_app_bundle_resources",
        "//ash/webui/resources:face_ml_app_resources",
        "//ash/webui/resources:face_ml_app_resources",
        "//ash/webui/resources:face_ml_app_untrusted_resources",
        "//ash/webui/resources:firmware_update_app_resources",
        "//ash/webui/resources:help_app_bundle_resources",
        "//ash/webui/resources:help_app_kids_magazine_bundle_resources",
        "//ash/webui/resources:help_app_resources",
        "//ash/webui/resources:media_app_bundle_resources",
        "//ash/webui/resources:media_app_resources",
        "//ash/webui/resources:multidevice_debug_resources",
        "//ash/webui/resources:os_feedback_resources",
        "//ash/webui/resources:os_feedback_untrusted_resources",
        "//ash/webui/resources:personalization_app_resources",
        "//ash/webui/resources:projector_annotator_trusted_resources",
        "//ash/webui/resources:projector_annotator_untrusted_resources",
        "//ash/webui/resources:projector_app_bundle_resources",
        "//ash/webui/resources:projector_app_trusted_resources",
        "//ash/webui/resources:projector_app_untrusted_resources",
        "//ash/webui/resources:projector_common_resources",
        "//ash/webui/resources:scanning_app_resources",
        "//ash/webui/resources:shimless_rma_resources",
        "//ash/webui/resources:shortcut_customization_app_resources",
        "//ash/webui/resources:system_extensions_internals_resources",
        "//chrome/browser/resources:bluetooth_pairing_dialog_resources",
        "//chrome/browser/resources:internet_config_dialog_resources",
        "//chrome/browser/resources:internet_detail_dialog_resources",
        "//chrome/browser/resources/chromeos:multidevice_setup_resources",
        "//chrome/browser/resources/chromeos/arc_account_picker:resources",
        "//chrome/browser/resources/chromeos/assistant_optin:resources",
        "//chrome/browser/resources/chromeos/audio:resources",
        "//chrome/browser/resources/chromeos/cloud_upload:resources",
        "//chrome/browser/resources/chromeos/desk_api:resources",
        "//chrome/browser/resources/chromeos/emoji_picker:resources",
        "//chrome/browser/resources/chromeos/gaia_action_buttons:resources",
        "//chrome/browser/resources/chromeos/launcher_internals:resources",
        "//chrome/browser/resources/chromeos/login:conditional_resources",
        "//chrome/browser/resources/chromeos/login:unconditional_resources",
        "//chrome/browser/resources/chromeos/manage_mirrorsync:resources",
        "//chrome/browser/resources/chromeos/multidevice_internals:resources",
        "//chrome/browser/resources/chromeos/network_ui:resources",
        "//chrome/browser/resources/chromeos/notification_tester:resources",
        "//chrome/browser/resources/chromeos/password_change:resources",
        "//chrome/browser/resources/nearby_internals:resources",
        "//chrome/browser/resources/nearby_share:resources",
        "//chrome/browser/resources/settings/chromeos:resources",
        "//chrome/browser/supervised_user:supervised_user_unscaled_resources",
        "//chromeos/ash/resources",
        "//chromeos/resources",
        "//ui/file_manager:file_manager_gen_resources",
        "//ui/file_manager:resources",
      ]

      if (!is_official_build) {
        sources += [
          "$root_gen_dir/ash/webui/ash_sample_system_web_app_resources.pak",
          "$root_gen_dir/ash/webui/ash_sample_system_web_app_untrusted_resources.pak",
        ]
        deps += [
          "//ash/webui/resources:sample_system_web_app_resources",
          "//ash/webui/resources:sample_system_web_app_untrusted_resources",
        ]
      }

      if (include_ash_ambient_animation_resources) {
        sources += [ "$root_gen_dir/ash/ambient/resources/ash_ambient_lottie_resources.pak" ]
        deps += [ "//ash/ambient/resources:lottie_resources" ]
      }
    }
    if (is_chromeos) {
      sources += [ "$root_gen_dir/chrome/common/chromeos/extensions/chromeos_system_extensions_resources.pak" ]
      deps += [ "//chrome/common/chromeos/extensions:resources" ]
    }
    if (is_linux || is_chromeos) {
      sources += [ "$root_gen_dir/chrome/webui_js_error_resources.pak" ]
      deps += [ "//chrome/browser/resources/webui_js_error:resources" ]
    }
    if (!is_android && !is_chromeos_ash) {
      sources += [
        "$root_gen_dir/chrome/intro_resources.pak",
        "$root_gen_dir/chrome/profile_picker_resources.pak",
        "$root_gen_dir/chrome/welcome_resources.pak",
      ]
      deps += [
        "//chrome/browser/resources/intro:resources",
        "//chrome/browser/resources/signin/profile_picker:resources",
        "//chrome/browser/resources/welcome:resources",
      ]
    }

    if (is_win || is_mac || is_linux || is_fuchsia) {
      sources += [
        "$root_gen_dir/chrome/app_home_resources.pak",
        "$root_gen_dir/chrome/apps_resources.pak",
      ]
      deps += [
        "//chrome/browser/resources/app_home:resources",
        "//chrome/browser/resources/ntp4:apps_resources",
      ]
    }

    if (is_win || is_mac || is_linux) {
      sources += [ "$root_gen_dir/chrome/app_settings_resources.pak" ]
      deps += [ "//chrome/browser/resources/app_settings:resources" ]
    }

    if (is_win || is_mac || is_linux || is_chromeos_ash) {
      sources += [ "$root_gen_dir/chrome/connectors_internals_resources.pak" ]
      deps += [ "//chrome/browser/resources/connectors_internals:resources" ]
    }

    if (is_win || is_mac || is_linux || is_chromeos_lacros || is_fuchsia) {
      sources += [ "$root_gen_dir/chrome/browser_switch_resources.pak" ]
      deps += [ "//chrome/browser/resources/browser_switch:resources" ]
    }

    if (is_win || is_mac || is_linux || is_chromeos || is_fuchsia) {
      sources += [ "$root_gen_dir/chrome/discards_resources.pak" ]
      deps += [ "//chrome/browser/resources/discards:resources" ]
    }

    if (enable_extensions) {
      sources += [
        "$root_gen_dir/chrome/extensions_resources.pak",
        "$root_gen_dir/chrome/sync_file_system_internals_resources.pak",
        "$root_gen_dir/extensions/extensions_renderer_resources.pak",
        "$root_gen_dir/extensions/extensions_resources.pak",
      ]
      deps += [
        "//chrome/browser/resources/extensions:resources",
        "//chrome/browser/resources/sync_file_system_internals:resources",
        "//extensions:extensions_resources",
      ]
    }
    if (enable_pdf) {
      sources += [ "$root_gen_dir/chrome/pdf_resources.pak" ]
      deps += [ "//chrome/browser/resources/pdf:resources" ]
    }
    if (enable_print_preview) {
      sources += [ "$root_gen_dir/chrome/print_preview_resources.pak" ]
      deps += [ "//chrome/browser/resources/print_preview:resources" ]
    }
    if (enable_webui_tab_strip) {
      sources += [ "$root_gen_dir/chrome/tab_strip_resources.pak" ]
      deps += [ "//chrome/browser/resources/tab_strip:resources" ]
    }
    if (is_cfm) {
      sources += [ "$root_gen_dir/chrome/cfm_network_settings_resources.pak" ]
      deps += [
        "//chrome/browser/resources/chromeos/chromebox_for_meetings:resources",
      ]
    }
  }
}

# Defines repack() targets used by Chrome. Specifically:
# * chrome_100_percent.pak
# * chrome_200_percent.pak (optionally)
# * resources.pak
# * locale .pak files
#
# Parameters:
#   output_dir [required]: Directory to output .pak files. Locale .pak files
#       will always be place in $output_dir/locales
#   additional_extra_paks: List of extra .pak sources for resources.pak.
#   locale_allowlist: if set, override repack_allowlist for locale .pak files.
#   files_to_hash: if set, a list of pak file names to generate SHA256 hashes
#     for.
#   copy_data_to_bundle:
#   deps:
#   mark_as_data:
#   output_dir:
#   public_deps:
#   repack_allowlist:
#   visibility:
#     Normal meanings.
#
template("chrome_paks") {
  chrome_repack_percent("${target_name}_100_percent") {
    percent = "100"
    forward_variables_from(invoker,
                           [
                             "copy_data_to_bundle",
                             "deps",
                             "mark_as_data",
                             "output_dir",
                             "repack_allowlist",
                             "visibility",
                           ])
  }

  if (enable_hidpi) {
    chrome_repack_percent("${target_name}_200_percent") {
      percent = "200"
      forward_variables_from(invoker,
                             [
                               "copy_data_to_bundle",
                               "deps",
                               "mark_as_data",
                               "output_dir",
                               "repack_allowlist",
                               "visibility",
                             ])
    }
  }

  chrome_extra_paks("${target_name}_extra") {
    forward_variables_from(invoker,
                           [
                             "copy_data_to_bundle",
                             "deps",
                             "mark_as_data",
                             "output_dir",
                             "repack_allowlist",
                             "visibility",
                           ])
    if (defined(invoker.additional_extra_paks)) {
      additional_paks = invoker.additional_extra_paks
    }
  }

  chrome_repack_locales("${target_name}_locales") {
    forward_variables_from(invoker,
                           [
                             "copy_data_to_bundle",
                             "deps",
                             "mark_as_data",
                             "visibility",
                           ])
    if (defined(invoker.locale_allowlist)) {
      repack_allowlist = invoker.locale_allowlist
    } else if (defined(invoker.repack_allowlist)) {
      repack_allowlist = invoker.repack_allowlist
    }

    input_locales = platform_pak_locales
    output_dir = "${invoker.output_dir}/locales"

    if (is_mac) {
      output_locales = locales_as_apple_outputs
    } else {
      output_locales = platform_pak_locales
    }
  }

  group(target_name) {
    forward_variables_from(invoker, [ "deps" ])
    public_deps = [
      ":${target_name}_100_percent",
      ":${target_name}_extra",
      ":${target_name}_locales",
    ]
    if (enable_hidpi) {
      public_deps += [ ":${target_name}_200_percent" ]
    }
    if (defined(invoker.public_deps)) {
      public_deps += invoker.public_deps
    }
  }

  if (defined(invoker.files_to_hash)) {
    _prefix = "$target_gen_dir/app/${target_name}_integrity"
    _integrity_outputs = [
      "$_prefix.cc",
      "$_prefix.h",
    ]

    action("${target_name}_integrity_hash") {
      script = "tools/build/sha256_file.py"
      outputs = _integrity_outputs
      inputs = []
      foreach(file, invoker.files_to_hash) {
        inputs += [ "${invoker.output_dir}/$file" ]
      }

      args = rebase_path([ _prefix ] + inputs, root_build_dir)

      deps = [ ":${invoker.target_name}" ]
    }

    source_set("${target_name}_integrity") {
      sources = _integrity_outputs
      deps = [ ":${target_name}_hash" ]
    }
  }
}
