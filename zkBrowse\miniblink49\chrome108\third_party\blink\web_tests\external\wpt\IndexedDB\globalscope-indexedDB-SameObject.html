<!doctype html>
<meta charset=utf-8>
<title>IndexedDB: Verify [SameObject] behavior of the global scope's indexedDB attribute</title>
<meta name="help" href="https://w3c.github.io/IndexedDB/#dom-windoworworkerglobalscope-indexeddb">
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<script>

test(t => {
  assert_equals(self.indexedDB, self.indexedDB,
                'Attribute should yield the same object each time');

}, 'indexedDB is [SameObject]');

</script>
