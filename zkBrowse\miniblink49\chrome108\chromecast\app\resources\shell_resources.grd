<?xml version="1.0" encoding="UTF-8"?>
<grit latest_public_release="0" current_release="1" output_all_resource_defines="false">
  <outputs>
    <output filename="grit/shell_resources.h" type="rc_header">
      <emit emit_type='prepend'></emit>
    </output>
    <output filename="shell_resources.pak" type="data_package" />
  </outputs>
  <translations />
  <release seq="1">
    <includes>
      <include name="IDR_CAST_SHELL_DEVTOOLS_DISCOVERY_PAGE" file="shell_devtools_discovery_page.html" type="BINDATA" />
      <include name="IDR_CAST_SHELL_SPEAKER_ICON" file="shell_speaker_icon.png" type="BINDATA" />
    </includes>
  </release>
</grit>
