<!DOCTYPE html>
<html>
<head>
<script src="../../resources/js-test.js"></script>
<script src="resources/common.js"></script>
</head>
<body>
<p id="description"></p>
<div id="console"></div>

<script>
description("Test exporting an AES key.");

jsTestIsAsync = true;

var extractable = true;
var nonExtractable = false;

var jwkKey = {
    kty: "oct",
    k: "jnOw99oOZFLIEPMrgJB55WL46tJSLGt7jnOw99oOZFI"
};

Promise.resolve(null).then(function(result) {
    return crypto.subtle.exportKey("raw");
}).then(failAndFinishJSTest, function(result) {
    logError(result);
    return crypto.subtle.exportKey("raw", null)
}).then(failAndFinishJSTest, function(result) {
    logError(result);
    return crypto.subtle.exportKey("raw", undefined);
}).then(failAndFinishJSTest, function(result) {
    logError(result);
    return crypto.subtle.exportKey("raw", {});
}).then(failAndFinishJSTest, function(result) {
    logError(result);
    return crypto.subtle.exportKey("raw", 1);
}).then(failAndFinishJSTest, function(result) {
    logError(result);
    debug("\nImporting a JWK key...");

    return crypto.subtle.importKey("jwk", jwkKey, {name: "AES-CBC"}, extractable, ['encrypt', 'decrypt', 'wrapKey', 'unwrapKey']);
}).then(function(result) {
    key = result;

    return crypto.subtle.exportKey(null, key);
}).then(failAndFinishJSTest, function(result) {
    logError(result);
    return crypto.subtle.exportKey(undefined, key);
}).then(failAndFinishJSTest, function(result) {
    logError(result);
    return crypto.subtle.exportKey({}, key);
}).then(failAndFinishJSTest, function(result) {
    logError(result);
    return crypto.subtle.exportKey("", key);
}).then(failAndFinishJSTest, function(result) {
    logError(result);
    return crypto.subtle.exportKey("foobar", key);
}).then(failAndFinishJSTest, function(result) {
    logError(result);

    debug("\nExporting the key as raw data...");
    return crypto.subtle.exportKey("raw", key);
}).then(function(result) {
    exportedData = result;
    shouldBe("bytesToHexString(new Uint8Array(exportedData))", "'8e73b0f7da0e6452c810f32b809079e562f8ead2522c6b7b8e73b0f7da0e6452'");

    debug("Exporting the key as JWK...");
    return crypto.subtle.exportKey("jwk", key);
}).then(function(result) {
    exportedJWK = result;
    shouldBe("exportedJWK.kty", "'oct'");
    shouldBe("exportedJWK.k", "'jnOw99oOZFLIEPMrgJB55WL46tJSLGt7jnOw99oOZFI'");
    shouldBe("exportedJWK.alg", "'A256CBC'");
    shouldBe("exportedJWK.ext", "true");
    shouldBe("exportedJWK.use", "undefined");
    shouldBe("exportedJWK.key_ops", "['encrypt', 'decrypt', 'wrapKey', 'unwrapKey']");

    debug("\nImporting a key that's not extractable...");
    return crypto.subtle.importKey("jwk", jwkKey, {name: "AES-CBC"}, nonExtractable, ['encrypt', 'decrypt', 'wrapKey', 'unwrapKey'])
}).then(function(result) {
    key = result;

    debug("\nTrying to export as raw...");
    return crypto.subtle.exportKey("raw", key);
}, failAndFinishJSTest).then(function(result) {
    testFailed("Promise wasn't rejected");
    finishJSTest();
}, function(result) {
    testPassed("Rejected, as expected");
    logError(result);

    debug("Trying to export as jwk...");
    return crypto.subtle.exportKey("jwk", key);
}).then(function(result) {
    testFailed("Promise wasn't rejected");
    finishJSTest();
}, function(result) {
    testPassed("Rejected, as expected");
    logError(result);

    finishJSTest();
});
</script>

</body>
</html>
