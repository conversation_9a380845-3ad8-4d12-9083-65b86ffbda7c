<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 7.4.0-alpha05" type="baseline" client="" dependencies="true" name="" variant="all" version="7.4.0-alpha05">

    <issue
        id="LintError"
        message="../../chromecast/android/lint-baseline.xml (relative to /usr/local/google/home/<USER>/z1/src/out/Cast) does not exist"
        errorLine1="  &lt;baseline file=&quot;../../chromecast/android/lint-baseline.xml&quot;/>"
        errorLine2="  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="project.xml"
            line="5"
            column="3"/>
    </issue>

    <issue
        id="MissingVersion"
        message="Should set `android:versionCode` to specify the application version"
        errorLine1="&lt;manifest xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot; package=&quot;org.chromium.chromecast.shell&quot;>"
        errorLine2=" ~~~~~~~~">
        <location
            file="gen/chromecast/cast_shell_apk__lint/gen/cast_shell_manifest/AndroidManifest.xml"
            line="2"
            column="2"/>
    </issue>

    <issue
        id="MissingVersion"
        message="Should set `android:versionName` to specify the application version"
        errorLine1="&lt;manifest xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot; package=&quot;org.chromium.chromecast.shell&quot;>"
        errorLine2=" ~~~~~~~~">
        <location
            file="gen/chromecast/cast_shell_apk__lint/gen/cast_shell_manifest/AndroidManifest.xml"
            line="2"
            column="2"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `#FFFFFF` with a theme that also paints a background (inferred theme is `@style/CastShellTheme`)"
        errorLine1="    android:background=&quot;#FFFFFF&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="$SRC/chromecast/browser/android/apk/res/layout/cast_web_contents_activity.xml"
            line="12"
            column="5"/>
    </issue>

    <issue
        id="RtlEnabled"
        message="The project references RTL attributes, but does not explicitly enable or disable RTL support with `android:supportsRtl` in the manifest">
        <location
            file="gen/chromecast/cast_shell_apk__lint/gen/cast_shell_manifest/AndroidManifest.xml"/>
    </issue>

</issues>
