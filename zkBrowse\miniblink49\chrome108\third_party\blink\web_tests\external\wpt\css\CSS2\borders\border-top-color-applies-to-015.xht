<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Border-top-color applied to element with display table-caption</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="Gérard Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-12-05 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#propdef-border-top-color" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#border-color-properties" />
        <link rel="match" href="border-bottom-applies-to-001-ref.xht" />

        <meta name="assert" content="The 'border-top-color' property applies to elements with a display of table-caption." />
        <style type="text/css">
            #test
            {
                border-top-width: 3px;
                border-top-style: solid;
                border-top-color: green;
                display: table-caption;
                width: 1in;
            }
            #table
            {
                display: table;
            }
            #row
            {
                display: table-row;
            }
            #cell
            {
                display: table-cell;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is a short horizontal green line.</p>
        <div id="table">
            <div id="test"></div>
            <div id="row">
                <div id="cell"></div>
            </div>
        </div>
    </body>
</html>