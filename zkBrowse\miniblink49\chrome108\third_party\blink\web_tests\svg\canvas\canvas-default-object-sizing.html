<!DOCTYPE html>
<title>Sizing SVG image when drawn to canvas</title>
<script>
function createCanvasWithImage(imgSrc, drawFunc)
{
    var canvas = document.createElement('canvas');
    canvas.width = 100;
    canvas.height = 100;
    var img = document.createElement('img');
    img.src = imgSrc;
    img.onload = function() {
        drawFunc(canvas.getContext('2d'), img);
        document.documentElement.removeChild(img);
    }
    document.documentElement.appendChild(img);
    document.documentElement.appendChild(canvas);
}

createCanvasWithImage(
    'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 10"><circle cx="5" cy="5" r="5" fill="blue"/></svg>',
    function(ctx, img) {
        ctx.drawImage(img, 0, 0);
    });

createCanvasWithImage(
    'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 10"><circle cx="10" cy="5" r="5" fill="blue"/></svg>',
    function(ctx, img) {
        ctx.drawImage(img, 0, 0);
    });

createCanvasWithImage(
    'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 20"><circle cx="5" cy="10" r="5" fill="blue"/></svg>',
    function(ctx, img) {
        ctx.drawImage(img, 0, 0);
    });

createCanvasWithImage(
    'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 10"><circle cx="10" cy="5" r="5" fill="blue"/></svg>',
    function(ctx, img) {
        ctx.drawImage(img, 0, 0, 100, 100);
    });

createCanvasWithImage(
    'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 20"><circle cx="5" cy="10" r="5" fill="blue"/></svg>',
    function(ctx, img) {
        ctx.drawImage(img, 0, 0, 100, 100);
    });

createCanvasWithImage(
    'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="50" viewBox="0 0 10 20"><circle cx="5" cy="10" r="5" fill="blue"/></svg>',
    function(ctx, img) {
        ctx.drawImage(img, 0, 0);
    });

createCanvasWithImage(
    'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" height="50" viewBox="0 0 20 10"><circle cx="10" cy="5" r="5" fill="blue"/></svg>',
    function(ctx, img) {
        ctx.drawImage(img, 0, 0);
    });

createCanvasWithImage(
    'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="50"><circle cx="50" cy="25" r="25" fill="blue"/></svg>',
    function(ctx, img) {
        ctx.drawImage(img, 0, 0);
    });

createCanvasWithImage(
    'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 10"><circle cx="5" cy="5" r="5" fill="blue"/></svg>',
    function(ctx, img) {
        ctx.drawImage(img, 0, 0, 50, 50, 0, 0, 50, 50);
        ctx.drawImage(img, 50, 50, 50, 50, 50, 50, 50, 50);
        ctx.drawImage(img, 0, 50, 50, 50, 0, 50, 50, 50);
        ctx.drawImage(img, 50, 0, 50, 50, 50, 0, 50, 50);
    });

createCanvasWithImage(
    'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 10"><circle cx="5" cy="5" r="5" fill="blue"/></svg>',
    function(ctx, img) {
        var pattern = ctx.createPattern(img, "repeat");
        ctx.fillStyle = pattern;
        ctx.fillRect(0, 0, 100, 100);
    });

createCanvasWithImage(
    'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 10"><circle cx="10" cy="5" r="5" fill="blue"/></svg>',
    function(ctx, img) {
        var pattern = ctx.createPattern(img, "repeat");
        ctx.fillStyle = pattern;
        ctx.fillRect(0, 0, 100, 100);
    });

createCanvasWithImage(
    'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg"><circle cx="50" cy="50" r="50" fill="blue"/></svg>',
    function(ctx, img) {
        var pattern = ctx.createPattern(img, "repeat");
        ctx.fillStyle = pattern;
        ctx.fillRect(0, 0, 100, 100);
    });
</script>
