Tests RemoteObject.getProperties.


Running: testSetUp

Running: testGetterAndSetter
{
    enumerable : true
    getter : {
        description : "get foo() { return 1; }"
        objectId : <string>
        type : "function"
    }
    isOwn : true
    name : "foo"
    private : false
    setter : {
        description : "set foo(value) { }"
        objectId : <string>
        type : "function"
    }
    symbol : undefined
    synthetic : false
    syntheticSetter : undefined
    value : undefined
    wasThrown : false
    webIdl : undefined
    writable : false
}

Running: testGetterOnly
{
    enumerable : true
    getter : {
        description : "get foo() { return 1; }"
        objectId : <string>
        type : "function"
    }
    isOwn : true
    name : "foo"
    private : false
    setter : undefined
    symbol : undefined
    synthetic : false
    syntheticSetter : undefined
    value : undefined
    wasThrown : false
    webIdl : undefined
    writable : false
}

