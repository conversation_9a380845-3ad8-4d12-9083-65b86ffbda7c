<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 6.3 Color Units</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
BODY {color: black;}
.one {color: #080;}
.two {color: #008000;}
.three {color: rgb(0,128,0);}
.four {color: rgb(0%,50%,0%);}
.five {color: rgb(0.0%,50.0%,0.0%);}
.six {color: green;}
.seven {color: invalidValue;}
.eight {color: rgb(0,128,1280);}
.nine {color: rgb(0,128,255);}
.ten {color: rgb(50%,-500%,60%);}
.eleven {color: rgb(50%,0%,60%);}</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>BODY {color: black;}
.one {color: #080;}
.two {color: #008000;}
.three {color: rgb(0,128,0);}
.four {color: rgb(0%,50%,0%);}
.five {color: rgb(0.0%,50.0%,0.0%);}
.six {color: green;}
.seven {color: invalidValue;}
.eight {color: rgb(0,128,1280);}
.nine {color: rgb(0,128,255);}
.ten {color: rgb(50%,-500%,60%);}
.eleven {color: rgb(50%,0%,60%);}
</PRE>
<HR>
<P>
This sentence should be black.
</P>
<P class="one">
This sentence should be green, although slightly different from those that follow.
</P>
<P class="two">
This sentence should be green.
</P>
<P class="three">
This sentence should be green.
</P>
<P class="four">
This sentence should be green.
</P>
<P class="five">
This sentence should be green.
</P>
<P class="six">
This sentence should be green.
</P>
<P class="seven">
This sentence should be black, because the value given for class <CODE>.seven</CODE> is invalid.
</P>
<P class="eight">
This sentence should be a shade of blue-green which, on a typical RGB computer display, exactly matches the next paragraph.
</P>
<P class="nine">
This sentence should be a shade of blue-green which, on a typical RGB computer display, exactly matches the previous paragraph.
</P>
<P class="ten">
This sentence should be a shade of purple which, on a typical RGB computer display, exactly matches the next paragraph.
</P>
<P class="eleven">
This sentence should be a shade of purple which, on a typical RGB computer display, exactly matches the previous paragraph.
</P>
<P>
This sentence should be black.
</P>
<P STYLE="color: #080;">
This sentence should be a slightly different green, and used the <TT>style</TT> attribute.
</P>
<P STYLE="color: #008000;">
This sentence should be green, and used the <TT>style</TT> attribute.
</P>
<P STYLE="color: rgb(0,128,0);">
This sentence should be green, and used the <TT>style</TT> attribute.
</P>
<P STYLE="color: rgb(0%,50%,0%);">
This sentence should be green, and used the <TT>style</TT> attribute.
</P>
<P STYLE="color: rgb(0.0%,50.0%,0.0%);">
This sentence should be green, and used the <TT>style</TT> attribute.
</P>
<P STYLE="color: green;">
This sentence should be green, and used the <TT>style</TT> attribute.
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P>
This sentence should be black.
</P>
<P class="one">
This sentence should be green, although slightly different from those that follow.
</P>
<P class="two">
This sentence should be green.
</P>
<P class="three">
This sentence should be green.
</P>
<P class="four">
This sentence should be green.
</P>
<P class="five">
This sentence should be green.
</P>
<P class="six">
This sentence should be green.
</P>
<P class="seven">
This sentence should be black, because the value given for class <CODE>.seven</CODE> is invalid.
</P>
<P class="eight">
This sentence should be a shade of blue-green which, on a typical RGB computer display, exactly matches the next paragraph.
</P>
<P class="nine">
This sentence should be a shade of blue-green which, on a typical RGB computer display, exactly matches the previous paragraph.
</P>
<P class="ten">
This sentence should be a shade of purple which, on a typical RGB computer display, exactly matches the next paragraph.
</P>
<P class="eleven">
This sentence should be a shade of purple which, on a typical RGB computer display, exactly matches the previous paragraph.
</P>
<P>
This sentence should be black.
</P>
<P STYLE="color: #080;">
This sentence should be a slightly different green, and used the <TT>style</TT> attribute.
</P>
<P STYLE="color: #008000;">
This sentence should be green, and used the <TT>style</TT> attribute.
</P>
<P STYLE="color: rgb(0,128,0);">
This sentence should be green, and used the <TT>style</TT> attribute.
</P>
<P STYLE="color: rgb(0%,50%,0%);">
This sentence should be green, and used the <TT>style</TT> attribute.
</P>
<P STYLE="color: rgb(0.0%,50.0%,0.0%);">
This sentence should be green, and used the <TT>style</TT> attribute.
</P>
<P STYLE="color: green;">
This sentence should be green, and used the <TT>style</TT> attribute.
</P>
</TD></TR></TABLE></BODY>
</HTML>
