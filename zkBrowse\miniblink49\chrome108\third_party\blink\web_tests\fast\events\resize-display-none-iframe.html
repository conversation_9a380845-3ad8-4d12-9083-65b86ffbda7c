<!DOCTYPE html>
<script src="../../resources/testharness.js"></script>
<script src="../../resources/testharnessreport.js"></script>
<iframe id="ifr" width="50" height="40" style="display: none" srcdoc="x"></iframe>
<script>

var test = async_test("iframe fires resize event when displayed");
var raf = requestAnimationFrame;
var raf2 = (f) => { raf(() => { raf(f); })}
var innerWindow = ifr.contentWindow;
var width = 0;
var height = 0;

onload = () => {
  // Add event listener after the srcdoc has been loaded, but before it is displayed.
  innerWindow.onresize = () => {
    width = innerWindow.innerWidth;
    height = innerWindow.innerHeight;
  };

  raf2(() => {
    assert_true(innerWindow.innerWidth == 0);
    assert_true(innerWindow.innerHeight == 0);

    // Displaying the iframe changes its viewport from 0x0 to 50x40.
    // This should fire a resize event.
    ifr.style.display = "block";

    raf2(() => {
      test.step(() => {
        assert_true(width == 50);
        assert_true(height == 40);
      });
      test.done();
    });
  });
};

</script>
