// Copyright 2017 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

(async function() {
  TestRunner.addResult(`Tests fetch() network resource payload is not corrupted by transcoding.\n`);
  await TestRunner.loadTestModule('network_test_runner');
  await TestRunner.showPanel('network');

  var payload = '\u201AFoo\u201B';

  NetworkTestRunner.recordNetwork();
  NetworkTestRunner.makeFetch('resources/resource.php?foo', {method: 'POST', body: payload}, step2);

  async function step2() {
    var request = NetworkTestRunner.networkRequests().pop();
    TestRunner.addResult(request.url());
    TestRunner.assertEquals('foo', request.queryString(), 'Unexpected resource query.');
    TestRunner.assertEquals(payload, await request.requestFormData(), 'Payload corrupted.');
    TestRunner.completeTest();
  }
})();
