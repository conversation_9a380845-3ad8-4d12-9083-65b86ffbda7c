<!DOCTYPE html>
<html>
    <script src="../../resources/testdriver.js"></script>
    <script src="../../resources/testdriver-vendor.js"></script>
    <body>
        <p>
            Test that a forwarded user gesture can be consumed by any timeout,
            not just the first.
        </p>
        <button id="button" onclick="openPopup()">Click Here</button>
        <div id="console"></div>
        <script>
            function timer1() {
            }
            function timer2() {
                var win = window.open("about:blank", "window");
                if (!win)
                    document.getElementById("console").innerText = "FAILED";
                else
                    document.getElementById("console").innerText = "PASSED";
                if (window.testRunner)
                    testRunner.notifyDone();
            }

            function openPopup() {
                setTimeout(timer1, 0);
                setTimeout(timer2, 10);
            }

            if (window.testRunner) {
                testRunner.dumpAsText();
                testRunner.waitUntilDone();

                var button = document.getElementById("button");
                test_driver.click(button);
            }
        </script>
    </body>
</html>
