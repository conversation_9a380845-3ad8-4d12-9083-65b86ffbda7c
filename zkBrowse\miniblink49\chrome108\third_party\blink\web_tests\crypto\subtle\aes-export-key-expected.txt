Test exporting an AES key.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".

error is: TypeError: Failed to execute 'exportKey' on 'SubtleCrypto': 2 arguments required, but only 1 present.
error is: TypeError: Failed to execute 'exportKey' on 'SubtleCrypto': parameter 2 is not of type 'CryptoKey'.
error is: TypeError: Failed to execute 'exportKey' on 'SubtleCrypto': parameter 2 is not of type 'CryptoKey'.
error is: TypeError: Failed to execute 'exportKey' on 'SubtleCrypto': parameter 2 is not of type 'CryptoKey'.
error is: TypeError: Failed to execute 'exportKey' on 'SubtleCrypto': parameter 2 is not of type 'CryptoKey'.

Importing a JWK key...
error is: TypeError: Invalid keyFormat argument
error is: TypeError: Invalid keyFormat argument
error is: TypeError: Invalid keyFormat argument
error is: TypeError: Invalid keyFormat argument
error is: TypeError: Invalid keyFormat argument

Exporting the key as raw data...
PASS bytesToHexString(new Uint8Array(exportedData)) is '8e73b0f7da0e6452c810f32b809079e562f8ead2522c6b7b8e73b0f7da0e6452'
Exporting the key as JWK...
PASS exportedJWK.kty is 'oct'
PASS exportedJWK.k is 'jnOw99oOZFLIEPMrgJB55WL46tJSLGt7jnOw99oOZFI'
PASS exportedJWK.alg is 'A256CBC'
PASS exportedJWK.ext is true
PASS exportedJWK.use is undefined
PASS exportedJWK.key_ops is ['encrypt', 'decrypt', 'wrapKey', 'unwrapKey']

Importing a key that's not extractable...

Trying to export as raw...
PASS Rejected, as expected
error is: InvalidAccessError: key is not extractable
Trying to export as jwk...
PASS Rejected, as expected
error is: InvalidAccessError: key is not extractable
PASS successfullyParsed is true

TEST COMPLETE

