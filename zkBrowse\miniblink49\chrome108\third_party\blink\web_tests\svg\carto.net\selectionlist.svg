<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" [
]>
<?AdobeSVGViewer save="snapshot"?>
<svg width="100%" height="100%" viewBox="0 0 1024 768" onload="initMap(evt)"
	xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
	zoomAndPan="disable">
	<script type="text/ecmascript" xlink:href="resources/helper_functions.js"/>
	<script type="text/ecmascript" xlink:href="resources/timer.js"/>
	<script type="text/ecmascript" xlink:href="resources/mapApp.js"/>
	<script type="text/ecmascript" xlink:href="resources/initSelectionList.js"/>
	<script type="text/ecmascript" xlink:href="resources/selectionList.js"/>
	<rect x="-1000" y="-1000" width="3000" height="3000" fill="white" stroke="none"/>
	<g fill="dimgray" font-family="Arial,Helvetica" font-size="11px">
		<text x="350" y="60" font-weight="bold" font-size="13px">Selectionlist/Drop-Down List Demo</text>
		<a xlink:href="index.shtml">
			<text x="350" y="80">Link to Documentation</text>
		</a>
		<text x="350" y="112" onclick="var newWidth = 155 + Math.random()*30; selRoses.resize(newWidth)">Click on this text to randomly resize the selectionList</text>
		<text x="350" y="127" onclick="var newX = 10 + Math.random()*60; selRoses.moveTo(newX,100)">Click on this text to randomly move the selectionList (x-axis)</text>
		<text x="50" y="475" id="rosename">Butterscotch</text>
		<text x="150" y="400">Loading Rose Image, Please be patient!</text>
		<text x="240" y="747" onclick="removeSelFlowers()">Click this text to remove the Flowers
				selectionList<tspan x="240" dy="15">This list does not react on user input</tspan></text>
		<!-- Labels -->
		<text x="240" y="62">Fruits</text>
		<text x="240" y="112">Roses</text>
		<text x="240" y="732">Flowers, this selectionList opens above the box</text>
		<text x="765" y="60">Select a base value to generate<tspan x="765" dy="15">random nrs for selnumber2</tspan></text>
		<text x="765" y="112">This is selnumber2</text>
	</g>
	<image id="roseimage" x="50" y="150" width="410" height="307"
		xlink:href="images/butterscotch.jpg"/>

	<!-- Containers to hold new Graphics of SelectionLists, the position within the File is important -->
	<g transform="translate(550,20),rotate(30)">
		<text x="220" y="212" fill="dimgray" font-family="Arial,Helvetica" font-size="11px"
			>Communities of Kt. Aargau (Switzerland)<tspan x="220" dy="20">Note that one can also
				transform the group containing the selection list</tspan><tspan x="220" dy="20">This
				selectionlist does not react on user input (functionToCall is a undefined
				value)</tspan><tspan x="220" dy="20" onclick="toggleAargauOpenMode()">Click on this
				text to toggle "above"/"below" mode to open the box either to top or
		bottom</tspan></text>
	</g>
	<g id="selectionLists">
		<g id="communitiesAarau" transform="translate(550,20),rotate(30)"/>
		<g id="flowers"/>
		<g id="roses"/>
		<g id="fruits"/>
		<g id="selnumbers"/>
	</g>
</svg>
