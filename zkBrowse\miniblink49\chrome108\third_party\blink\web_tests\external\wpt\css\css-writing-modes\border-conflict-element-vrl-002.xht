<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

 <head>

  <title>CSS Writing Modes Test: Border conflict resolution - adjacent cells with same border styles in a 'vertical-rl' table with 'direction: ltr' (basic)</title>

  <link rel="author" title="G<PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" />
  <link rel="help" href="http://www.w3.org/TR/css-writing-modes-3/#logical-directions" title="6.2 Flow-relative Directions" />
  <link rel="help" href="http://www.w3.org/TR/CSS21/tables.html#border-conflict-resolution" title="17.6.2.1 Border conflict resolution" />
  <link rel="match" href="../reference/ref-filled-green-100px-square.xht" />

  <meta content="When two adjacent cells have the same 'border-width' value and the same 'border-style' value in a 'border-collapse: collapse' table, then the color of the border of the cell closest to line-left side wins (if the table's 'direction' is 'ltr'; line-right side, if it is 'rtl') and the color of the border of the cell closest to block-start side wins." name="assert" />

  <style type="text/css"><![CDATA[
  table
    {
      border-collapse: collapse;
      direction: ltr;
      writing-mode: vertical-rl;
    }

  td
    {
      padding: 0px;
    }

  td#five
    {
      border-bottom-color: green;
      border-bottom-style: solid;
      border-bottom-width: 100px;
    }

  td#six
    {
      border-top-color: red;
      border-top-style: solid;
      border-top-width: 100px;
      width: 100px;
    }
  ]]></style>

 </head>

 <body>

  <p>Test passes if there is a filled green square and <strong>no red</strong>.</p>

  <table>

    <tr>
      <td></td> <td></td> <td></td>
    </tr>

    <tr>
      <td></td> <td id="five"></td> <td id="six"></td>
    </tr>

    <tr>
      <td></td> <td></td> <td></td>
    </tr>

  </table>

 </body>
</html>
