<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>CSS Test: Background propagation on &lt;body&gt; and &lt;html&gt; - propagation vs. image</title>
  <link rel="author" title="<PERSON>" href="mailto:<EMAIL>"/>
  <link rel="alternate" href="http://www.hixie.ch/tests/adhoc/css/background/07.html" type="text/html"/>
  <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background"/>
  <link rel="match" href="background-root-010-ref.xht" />

  <meta name="flags" content="image"/>
<style type="text/css"><![CDATA[
   body { border: solid lime; background: green url(support/square-purple.png) repeat-y top left; color: white; }
   html { border: solid blue; background: transparent url(support/square-white.png); color: yellow; }
   :link, :visited { color: inherit; background: transparent; }
   * { margin: 1em; padding: 1em; }
]]></style>

</head>
<body>
<p>This text should in a green box with a green border, on the inside
left of which there should be a vertical line of purple tiles. The
box should be surrounded by a blue rectangle drawn on a background of
white tiles that covers the entire rest of the page.
</p>


</body>
</html>
