<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background-repeat set to 'repeat'</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="G<PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-05-04 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background-repeat" />
        <link rel="match" href="background-repeat-001-ref.xht" />

        <meta name="flags" content="image" />
        <meta name="assert" content="Background-repeat set to 'repeat' causes the background image for the element to tile horizontally and vertically." />
        <style type="text/css">
            div
            {
                background-image: url("support/blue15x15.png");
                background-repeat: repeat;
                border: 5px solid black;
                height: 200px;
            }
        </style>
    </head>
    <body>
        <p>Test passes if the area inside the black rectangle is blue.</p>
        <div></div>
    </body>
</html>