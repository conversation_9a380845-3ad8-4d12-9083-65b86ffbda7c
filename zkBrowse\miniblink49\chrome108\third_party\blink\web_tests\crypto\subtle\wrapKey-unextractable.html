<!DOCTYPE html>
<html>
<head>
<script src="../../resources/js-test.js"></script>
<script src="resources/common.js"></script>
</head>
<body>
<p id="description"></p>
<div id="console"></div>

<script>
description("Tests that an unextractable key cannot be wrapped.");

jsTestIsAsync = true;

function importWrappingKey()
{
    var data = new Uint8Array(16);
    var extractable = true;
    var keyUsages = ['wrapKey'];

    return crypto.subtle.importKey('raw', data, {name: 'AES-CBC'}, extractable, keyUsages);
}

function importUnextractableKeyToWrap()
{
    var data = new Uint8Array(16);
    var extractable = false;
    var keyUsages = ['sign'];

    return crypto.subtle.importKey('raw', data, {name: 'HMAC', hash: {name: 'SHA-1'}}, extractable, keyUsages);
}

importWrappingKey().then(function(result) {
    wrappingKey = result;
    return importUnextractableKeyToWrap();
}).then(function(result) {
    key = result;

    shouldEvaluateAs("key.extractable", false);

    wrapAlgorithm = {name: 'aes-cbc', iv: new Uint8Array(16)};
    return crypto.subtle.wrapKey('raw', key, wrappingKey, wrapAlgorithm);
}).then(failAndFinishJSTest, function(result) {
    logError(result);
}).then(finishJSTest, failAndFinishJSTest);

</script>

</body>
</html>
