<!DOCTYPE html>
<html>
<head>
    <script>
        window.isOnErrorTest = true;
    </script>
    <script src="../../resources/js-test.js"></script>
    <script src="resources/onerror-test.js"></script>
</head>
<body>
    <script>
        description("This test should trigger 'window.onerror', and successfully handle the error.");
        dumpOnErrorArgumentValuesAndReturn(true);
        hahaha_good_luck_finding_me(); // caught by window.onerror
    </script>
</body>
</html>
