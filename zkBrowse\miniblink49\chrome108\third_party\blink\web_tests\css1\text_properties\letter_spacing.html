<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.4.2 letter-spacing</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
.one {letter-spacing: 0.3in;}
.two {letter-spacing: 0.5cm;}
.three {letter-spacing: 5mm;}
.four {letter-spacing: 3pt;}
.five {letter-spacing: 0.25pc;}
.six {letter-spacing: 1em;}
.seven {letter-spacing: 1ex;}
.eight {letter-spacing: 5px;}
.nine {letter-spacing: normal;}
.ten {letter-spacing: 300%;}
.eleven {letter-spacing: -0.1em;}</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.one {letter-spacing: 0.3in;}
.two {letter-spacing: 0.5cm;}
.three {letter-spacing: 5mm;}
.four {letter-spacing: 3pt;}
.five {letter-spacing: 0.25pc;}
.six {letter-spacing: 1em;}
.seven {letter-spacing: 1ex;}
.eight {letter-spacing: 5px;}
.nine {letter-spacing: normal;}
.ten {letter-spacing: 300%;}
.eleven {letter-spacing: -0.1em;}
</PRE>
<HR>
<P class="one">
This letters in this sentence should have extra space between them.
</P>
<P class="two">
This letters in this sentence should have extra space between them.
</P>
<P class="three">
This letters in this sentence should have extra space between them.
</P>
<P class="four">
This letters in this sentence should have extra space between them.
</P>
<P class="five">
This letters in this sentence should have extra space between them.
</P>
<P class="six">
This letters in this sentence should have extra space between them.
</P>
<P class="seven">
This letters in this sentence should have extra space between them.
</P>
<P class="eight">
This letters in this sentence should have extra space between them, but the last few words in the sentence <SPAN class="nine">should show normal spacing</SPAN>.
</P>
<P class="ten">
This letters in this sentence should have normal space between them, since percentage values are not allowed on this property.
</P>
<P class="eleven">
This letters in this sentence should have reduced space between them, since negative values are allowed on this property.
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P class="one">
This letters in this sentence should have extra space between them.
</P>
<P class="two">
This letters in this sentence should have extra space between them.
</P>
<P class="three">
This letters in this sentence should have extra space between them.
</P>
<P class="four">
This letters in this sentence should have extra space between them.
</P>
<P class="five">
This letters in this sentence should have extra space between them.
</P>
<P class="six">
This letters in this sentence should have extra space between them.
</P>
<P class="seven">
This letters in this sentence should have extra space between them.
</P>
<P class="eight">
This letters in this sentence should have extra space between them, but the last few words in the sentence <SPAN class="nine">should show normal spacing</SPAN>.
</P>
<P class="ten">
This letters in this sentence should have normal space between them, since percentage values are not allowed on this property.
</P>
<P class="eleven">
This letters in this sentence should have reduced space between them, since negative values are allowed on this property.
</P>
</TD></TR></TABLE></BODY>
</HTML>
