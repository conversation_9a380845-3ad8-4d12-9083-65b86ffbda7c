@echo off
echo Testing V8 10.8 Debug Win32 Build Configuration
echo ==============================================

REM Set Visual Studio environment for Win32
call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Enterprise\VC\Auxiliary\Build\vcvars32.bat"
set VisualStudioVersion=16.0

echo.
echo Checking V8 10.8 files...
if not exist "..\v8_10_8\v8.dll" (
    echo ERROR: V8 10.8 DLL files not found!
    pause
    exit /b 1
)

echo V8 10.8 files found:
dir "..\v8_10_8\v8*.dll" /B
dir "..\v8_10_8\v8*.lib" /B

echo.
echo Creating output directory...
if not exist "..\out\Debug" mkdir "..\out\Debug"

echo.
echo Copying V8 10.8 DLL files to output directory...
copy "..\v8_10_8\v8.dll" "..\out\Debug\" /Y
copy "..\v8_10_8\v8_libbase.dll" "..\out\Debug\" /Y
copy "..\v8_10_8\v8_libplatform.dll" "..\out\Debug\" /Y

echo.
echo Building Debug|Win32 configuration with V8 10.8, C++17 and complete generated bindings compatibility...
echo This includes fixes for SetReferenceFromGroup, CreationContext, SetHiddenPrototype, and all generated V8 bindings...
devenv miniblink.sln /build "Debug|Win32" /Out ../out/Debug/build_v8_10_8.log /Project miniblink

echo.
echo Build completed. Check ../out/Debug/build_v8_10_8.log for details.
echo.
echo Output files:
if exist "..\out\Debug\miniblink_d.dll" (
    echo SUCCESS: miniblink_d.dll created
    dir "..\out\Debug\miniblink_d.dll"
) else (
    echo ERROR: miniblink_d.dll not found
)

pause
