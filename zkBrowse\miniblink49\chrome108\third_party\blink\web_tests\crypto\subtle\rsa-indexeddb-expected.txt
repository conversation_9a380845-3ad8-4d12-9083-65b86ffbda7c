Test storing a private RSA key in IndexedDB, and retrieving it.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".

Put key into database successfully
PASS retrievedKey.type is 'private'
PASS retrievedKey.extractable is true
PASS retrievedKey.algorithm.name is 'RSASSA-PKCS1-v1_5'
PASS retrievedKey.algorithm.modulusLength is 2048
PASS bytesToHexString(retrievedKey.algorithm.publicExponent) is '010001'
PASS retrievedKey.usages is ["sign"]
PASS successfullyParsed is true

TEST COMPLETE

