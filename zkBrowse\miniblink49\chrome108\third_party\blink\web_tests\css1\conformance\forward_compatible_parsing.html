<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 7.1 Forward-Compatible Parsing</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
P.one {color: green; rotation: 70deg;}
P.oneb {color: green;}
P.oneb {color: invalidValue;}
P.two {background-color: inherit;}
H1 + P.three {color: blue;}
P.four + H1 {color: red;}
P.five {background-color: "red";}
P.sixa {border-width: medium; border-style: solid;}
P.sixb {border-width: funny; border-style: solid;}
P.sixc {border-width: 50zu; border-style: solid;}
P.sixd {border-width: px; border-style: solid;}
@three-dee {
 @background-lighting {
  azimuth: 30deg;
  elevation: 190deg;
  }
 P.seven { color: red }
 }
P.eight {COLOR: GREEN;}
OL:wait {color: maroon;}
P.ten:first-child {color: maroon;}
UL:lang(fr) {color: gray;}
BLOCKQUOTE[href] {color: navy;}
ACRONYM[href="foo"] {color: purple;}
ADDRESS[href~="foo"] {color: purple;}
SPAN[lang|="fr"] {color: #c37;}
@media tty {
 H1 {color: red;}
 P.sixteen {color: red;}
 }
@three-dee {
 P.seventeen {color: red }
 }
P.eighteena {text-decoration: underline overline line-through diagonal;
            font: bold highlighted 100% sans-serif;}
P.eighteenb {text-decoration: underline overline line-through diagonal;
            font: bold highlighted 100% serif;}
EM, P.nineteena ! EM, STRONG {font-size: 200%; }

// UL.nineteenb,
P.nineteenb {color: red;}

P.twentya {rotation-code: "}"; color: blue;} 
P.twentyb {rotation-code: "\"}\""; color: green;}
P.twentyonea {rotation-code: '}'; color: purple;} 
P.twentyoneb {rotation-code: '\'}\''; color: green;}
P.twentytwo {
 type-display: @threedee {rotation-code: '}';};
 color: green;
 }
P.twentythree {text-indent: 0.5in;}
 color: maroon
P.twentyfour {color: red;}
</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>P.one {color: green; rotation: 70deg;}
P.oneb {color: green;}
P.oneb {color: invalidValue;}
P.two {background-color: inherit;}
H1 + P.three {color: blue;}
P.four + H1 {color: red;}
P.five {background-color: "red";}
P.sixa {border-width: medium; border-style: solid;}
P.sixb {border-width: funny; border-style: solid;}
P.sixc {border-width: 50zu; border-style: solid;}
P.sixd {border-width: px; border-style: solid;}
@three-dee {
 @background-lighting {
  azimuth: 30deg;
  elevation: 190deg;
  }
 P.seven { color: red }
 }
P.eight {COLOR: GREEN;}
OL:wait {color: maroon;}
P.ten:first-child {color: maroon;}
UL:lang(fr) {color: gray;}
BLOCKQUOTE[href] {color: navy;}
ACRONYM[href="foo"] {color: purple;}
ADDRESS[href~="foo"] {color: purple;}
SPAN[lang|="fr"] {color: #c37;}
@media tty {
 H1 {color: red;}
 P.sixteen {color: red;}
 }
@three-dee {
 P.seventeen {color: red }
 }
P.eighteena {text-decoration: underline overline line-through diagonal;
            font: bold highlighted 100% sans-serif;}
P.eighteenb {text-decoration: underline overline line-through diagonal;
            font: bold highlighted 100% serif;}
EM, P.nineteena ! EM, STRONG {font-size: 200%; }

// UL.nineteenb,
P.nineteenb {color: red;}

P.twentya {rotation-code: "}"; color: blue;} 
P.twentyb {rotation-code: "\"}\""; color: green;}
P.twentyonea {rotation-code: '}'; color: purple;} 
P.twentyoneb {rotation-code: '\'}\''; color: green;}
P.twentytwo {
 type-display: @threedee {rotation-code: '}';};
 color: green;
 }
P.twentythree {text-indent: 0.5in;}
 color: maroon
P.twentyfour {color: red;}

</PRE>
<HR>
<P CLASS="one">
This paragraph should be green, because only the rotation should be ignored.
</P>
<P CLASS="oneb">
This paragraph should be green, since error-checking should be done before calculating specificity.
</P>
<P CLASS="two">
This paragraph should have a solid gray background (or a white grid), because in CSS1, <CODE>inherit</CODE> is an invalid keyword, and in CSS2, it will cause the gray background (not the white grid) to be inherited.
</P>
<P CLASS="three">
This paragraph should be black, since in CSS1, the selector is invalid, and
in CSS2, it does not apply.
</P>
<P CLASS="four">
This paragraph should be black, since in CSS1, the selector is invalid, and
in CSS2, it does not apply.
</P>
<P CLASS="five">
This paragraph should have a white background, since keywords cannot be quoted.
</P>
<P CLASS="sixa">
This paragraph should have a medium-width border around it (the same as the next three paragraphs).  This should cause the user agent to use the default value of 'medium'.
</P>
<P CLASS="sixb">
This paragraph should have a medium-width border around it (the same as the previous and the next two paragraphs), because the border-width is invalid.  This should cause the user agent to use the default value of 'medium'.
</P>
<P CLASS="sixc">
This paragraph should have a medium-width border around it (the same as the next and the previous two paragraphs), because the border-width units are invalid, and therefore the border-width should be ignored.  This should cause the user agent to use the default value of 'medium'.
</P>
<P CLASS="sixd">
This paragraph should have a medium-width border around it (the same as the previous three paragraphs), because the border-width does not have any value to go with its pixel unit, and is therefore invalid.  This should cause the user agent to use the default value of 'medium'.
</P>
<P CLASS="seven">
This paragraph should be black, because the style declaration that applies
to it is within an invalid at-rule.
</P>
<P CLASS="eight">
This paragraph should be green.  CSS is case-insensitive, unless required
to be case sensitive due to interaction with other standards (e.g., font
names or URLs.)
</P>

<OL>
<LI>This ordered list item should be black, because the declaration has
an invalid pseudo-class selector.</LI>
</OL>

<P CLASS="ten">
This paragraph should be black, because, in CSS1, :first-child is an
invalid pseudo-class, and in CSS2, this paragraph is not the first child.
</P>

<UL>
<LI>This unordered list item should be black, because, according to CSS1, the selector is invalid, and according to CSS2, the selector should not apply.</LI> 
</UL>

<BLOCKQUOTE>This blockquote should be black, because, according to CSS1, the selector is invalid, and according to CSS2, the selector should not apply.</BLOCKQUOTE>

<P><ACRONYM>This acronym should be black, because, according to CSS1, the selector is invalid, and according to CSS2, the selector should not
apply.</ACRONYM></P>

<ADDRESS>This address should be black, because, according to CSS1,
the selector is invalid, and according to CSS2, the selector should not
apply.</ADDRESS>

<P><SPAN>This span should be black, because, according to CSS1,
the selector is invalid, and according to CSS2, the selector should not
apply.</SPAN></P>

<P CLASS="sixteen">
This paragraph should be black, because the style declaration that applies to it is within an invalid at-rule.  However, it is valid in CSS2, so if this is being viewed on a tty browser that supports CSS2, it should be red.
</P>
<P CLASS="seventeen">
This paragraph should be black, because the style declaration that applies to it is within an invalid at-rule.
</P>
<P CLASS="eighteena">
The text of this paragraph should be normal (that is, it should not be
underlined, overlined, stricken, or bold), because <CODE>diagonal</CODE> is not a valid keyword, making the <CODE>text-decoration</CODE> invalid.  In addition, <CODE>highlighted</CODE> is not a valid part of the <CODE>font</CODE> property, making it invalid.   Therefore, this paragraph's font should be the UA default, and match the next paragraph.  If this is not the case, then the <CODE>font</CODE> declaration is being improperly parsed.
</P>
<P CLASS="eighteenb">
The text of this paragraph should be normal (that is, it should not be
underlined, overlined, stricken, or bold), because <CODE>diagonal</CODE> is not a valid keyword, making the <CODE>text-decoration</CODE> invalid.  In addition, <CODE>highlighted</CODE> is not a valid part of the <CODE>font</CODE> property, making it invalid.   Therefore, this paragraph's font should be the UA default, and match the previous paragraph.  If this is not the case, then the <CODE>font</CODE> declaration is being improperly parsed.
</P>
<P CLASS="nineteena">
The text of this paragraph should be normal size because the selector
is invalid.  The <EM>emphasized text</EM> and the <STRONG>strong
text</STRONG> within it should also be normal size, since the
entire ruleset should be skipped (since in some future version of
CSS, there could be an operator within the selector that has higher
precedence than a comma).
</P>
<P CLASS="nineteenb">
This paragraph should be black, because the line before the declaration
is <STRONG>not</STRONG> a comment and therefore the selector for
P.nineteenb is invalid.
</P>
<P CLASS="twentya">
This paragraph should be blue, because only the first declaration is invalid.
</P>
<P CLASS="twentyb">
This paragraph should be green, because only the first declaration is invalid.
</P>
<P CLASS="twentyonea">
This paragraph should be purple, because only the first declaration is invalid.
</P>
<P CLASS="twentyoneb">
This paragraph should be green, because only the first declaration is invalid.
</P>
<P CLASS="twentytwo">
This paragraph should be green, because only the first declaration is invalid.
</P>
<P CLASS="twentythree">
This paragraph should be indented half an inch, but it should not
be maroon.  It should be black.
</P>
<P CLASS="twentyfour">
This paragraph should be black, because the color declaration after
the previous ruleset should be considered part of the selector for
this ruleset, and this ruleset therefore has an invalid selector
and should not be applied.
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P CLASS="one">
This paragraph should be green, because only the rotation should be ignored.
</P>
<P CLASS="oneb">
This paragraph should be green, since error-checking should be done before calculating specificity.
</P>
<P CLASS="two">
This paragraph should have a solid gray background (or a white grid), because in CSS1, <CODE>inherit</CODE> is an invalid keyword, and in CSS2, it will cause the gray background (not the white grid) to be inherited.
</P>
<P CLASS="three">
This paragraph should be black, since in CSS1, the selector is invalid, and
in CSS2, it does not apply.
</P>
<P CLASS="four">
This paragraph should be black, since in CSS1, the selector is invalid, and
in CSS2, it does not apply.
</P>
<P CLASS="five">
This paragraph should have a white background, since keywords cannot be quoted.
</P>
<P CLASS="sixa">
This paragraph should have a medium-width border around it (the same as the next three paragraphs).  This should cause the user agent to use the default value of 'medium'.
</P>
<P CLASS="sixb">
This paragraph should have a medium-width border around it (the same as the previous and the next two paragraphs), because the border-width is invalid.  This should cause the user agent to use the default value of 'medium'.
</P>
<P CLASS="sixc">
This paragraph should have a medium-width border around it (the same as the next and the previous two paragraphs), because the border-width units are invalid, and therefore the border-width should be ignored.  This should cause the user agent to use the default value of 'medium'.
</P>
<P CLASS="sixd">
This paragraph should have a medium-width border around it (the same as the previous three paragraphs), because the border-width does not have any value to go with its pixel unit, and is therefore invalid.  This should cause the user agent to use the default value of 'medium'.
</P>
<P CLASS="seven">
This paragraph should be black, because the style declaration that applies
to it is within an invalid at-rule.
</P>
<P CLASS="eight">
This paragraph should be green.  CSS is case-insensitive, unless required
to be case sensitive due to interaction with other standards (e.g., font
names or URLs.)
</P>

<OL>
<LI>This ordered list item should be black, because the declaration has
an invalid pseudo-class selector.</LI>
</OL>

<P CLASS="ten">
This paragraph should be black, because, in CSS1, :first-child is an
invalid pseudo-class, and in CSS2, this paragraph is not the first child.
</P>

<UL>
<LI>This unordered list item should be black, because, according to CSS1, the selector is invalid, and according to CSS2, the selector should not apply.</LI> 
</UL>

<BLOCKQUOTE>This blockquote should be black, because, according to CSS1, the selector is invalid, and according to CSS2, the selector should not apply.</BLOCKQUOTE>

<P><ACRONYM>This acronym should be black, because, according to CSS1, the selector is invalid, and according to CSS2, the selector should not
apply.</ACRONYM></P>

<ADDRESS>This address should be black, because, according to CSS1,
the selector is invalid, and according to CSS2, the selector should not
apply.</ADDRESS>

<P><SPAN>This span should be black, because, according to CSS1,
the selector is invalid, and according to CSS2, the selector should not
apply.</SPAN></P>

<P CLASS="sixteen">
This paragraph should be black, because the style declaration that applies to it is within an invalid at-rule.  However, it is valid in CSS2, so if this is being viewed on a tty browser that supports CSS2, it should be red.
</P>
<P CLASS="seventeen">
This paragraph should be black, because the style declaration that applies to it is within an invalid at-rule.
</P>
<P CLASS="eighteena">
The text of this paragraph should be normal (that is, it should not be
underlined, overlined, stricken, or bold), because <CODE>diagonal</CODE> is not a valid keyword, making the <CODE>text-decoration</CODE> invalid.  In addition, <CODE>highlighted</CODE> is not a valid part of the <CODE>font</CODE> property, making it invalid.   Therefore, this paragraph's font should be the UA default, and match the next paragraph.  If this is not the case, then the <CODE>font</CODE> declaration is being improperly parsed.
</P>
<P CLASS="eighteenb">
The text of this paragraph should be normal (that is, it should not be
underlined, overlined, stricken, or bold), because <CODE>diagonal</CODE> is not a valid keyword, making the <CODE>text-decoration</CODE> invalid.  In addition, <CODE>highlighted</CODE> is not a valid part of the <CODE>font</CODE> property, making it invalid.   Therefore, this paragraph's font should be the UA default, and match the previous paragraph.  If this is not the case, then the <CODE>font</CODE> declaration is being improperly parsed.
</P>
<P CLASS="nineteena">
The text of this paragraph should be normal size because the selector
is invalid.  The <EM>emphasized text</EM> and the <STRONG>strong
text</STRONG> within it should also be normal size, since the
entire ruleset should be skipped (since in some future version of
CSS, there could be an operator within the selector that has higher
precedence than a comma).
</P>
<P CLASS="nineteenb">
This paragraph should be black, because the line before the declaration
is <STRONG>not</STRONG> a comment and therefore the selector for
P.nineteenb is invalid.
</P>
<P CLASS="twentya">
This paragraph should be blue, because only the first declaration is invalid.
</P>
<P CLASS="twentyb">
This paragraph should be green, because only the first declaration is invalid.
</P>
<P CLASS="twentyonea">
This paragraph should be purple, because only the first declaration is invalid.
</P>
<P CLASS="twentyoneb">
This paragraph should be green, because only the first declaration is invalid.
</P>
<P CLASS="twentytwo">
This paragraph should be green, because only the first declaration is invalid.
</P>
<P CLASS="twentythree">
This paragraph should be indented half an inch, but it should not
be maroon.  It should be black.
</P>
<P CLASS="twentyfour">
This paragraph should be black, because the color declaration after
the previous ruleset should be considered part of the selector for
this ruleset, and this ruleset therefore has an invalid selector
and should not be applied.
</P>
</TD></TR></TABLE></BODY>
</HTML>
