<!DOCTYPE html>
<html>
<head>
<style>
div, span {
    border: 1px solid black;
};
</style>
<script>
function log(str)
{
    var result = document.getElementById('result');
    result.appendChild(document.createTextNode(str));
    result.appendChild(document.createElement('br'));
}
function dragDrop(target)
{
    log('Starting drag...');
    eventSender.beginDragWithFiles(['resources/notify-done.html']);
    eventSender.leapForward(100);
    eventSender.mouseMoveTo(target.offsetLeft + target.offsetWidth / 2,
                            target.offsetTop + target.offsetHeight / 2);
    eventSender.mouseUp();
}
window.onload = function()
{
    var drop1 = document.getElementById('drop1');
    var drop2 = document.getElementById('drop2');

    document.body.addEventListener('dragover', function () {
    }, false);

    drop1.addEventListener('dragover', function(e) {
        e.preventDefault();
    }, false);
    drop1.addEventListener('drop', function(e) {
        e.preventDefault();
        log('Drop received in drop target 1');
    }, false);

    drop2.addEventListener('dragover', function() {
    }, false);
    drop2.addEventListener('drop', function(e) {
        e.preventDefault();
        log('FAIL: Drop unexpectedly received in drop target 2');
    }, false);

    if (!window.testRunner)
        return;
    testRunner.dumpAsText();
    testRunner.waitUntilDone();
    // The line below is necessary in order for preventing the default in
    // beforeunload handler to work properly. crbug.com/802982
    testRunner.setShouldStayOnPageAfterHandlingBeforeUnload(true);

    window.addEventListener('beforeunload', function (e) {
        log('FAIL: navigation should have occurred in new tab');
        testRunner.notifyDone();
        e.returnValue = "foo";
    });

    dragDrop(drop1);
    dragDrop(drop2);
}
</script>
</head>
<body>
<p>To run this test manually, drag a file to one of the two boxes below.
<div id="drop1">Dropping in drop target 1 should result in a drop event.</div>
<div id="drop2">Dropping in drop target 2 should NOT result in a drop event (new tab will navigate).</div>
<div id="result">
</div>
</body>
</html>
