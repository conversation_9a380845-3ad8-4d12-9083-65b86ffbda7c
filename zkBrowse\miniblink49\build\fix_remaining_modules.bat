@echo off
echo Fixing remaining modules V8 bindings for V8 10.8 compatibility...
echo.

set "MODULES_DIR=..\gen\blink\bindings\modules\v8"

echo Processing remaining files...

REM Fix V8ExtShaderTextureLod.cpp
if exist "%MODULES_DIR%\V8ExtShaderTextureLod.cpp" (
    echo Fixing V8ExtShaderTextureLod.cpp...
    powershell -Command "& { $content = Get-Content '%MODULES_DIR%\V8ExtShaderTextureLod.cpp' -Raw; if ($content -notmatch 'v8_compatibility\.h') { $content = $content -replace '(#include \"wtf/RefPtr\.h\")', '$1`r`n`r`n// V8 10.8 compatibility layer`r`n#if V8_MAJOR_VERSION >= 10`r`n#include \"../../../../../v8_10_8/v8_compatibility.h\"`r`n#endif' }; $content = $content -replace 'isolate->SetReferenceFromGroup\(v8::UniqueId\(reinterpret_cast<intptr_t>\(root\)\), wrapper\);', '#if V8_MAJOR_VERSION >= 10`r`n        v8::IsolateCompat::SetReferenceFromGroup(isolate, v8::UniqueId(reinterpret_cast<intptr_t>(root)), wrapper);`r`n#else`r`n        isolate->SetReferenceFromGroup(v8::UniqueId(reinterpret_cast<intptr_t>(root)), wrapper);`r`n#endif'; Set-Content '%MODULES_DIR%\V8ExtShaderTextureLod.cpp' $content -NoNewline }"
)

REM Fix V8ExtSRGB.cpp
if exist "%MODULES_DIR%\V8ExtSRGB.cpp" (
    echo Fixing V8ExtSRGB.cpp...
    powershell -Command "& { $content = Get-Content '%MODULES_DIR%\V8ExtSRGB.cpp' -Raw; if ($content -notmatch 'v8_compatibility\.h') { $content = $content -replace '(#include \"wtf/RefPtr\.h\")', '$1`r`n`r`n// V8 10.8 compatibility layer`r`n#if V8_MAJOR_VERSION >= 10`r`n#include \"../../../../../v8_10_8/v8_compatibility.h\"`r`n#endif' }; $content = $content -replace 'isolate->SetReferenceFromGroup\(v8::UniqueId\(reinterpret_cast<intptr_t>\(root\)\), wrapper\);', '#if V8_MAJOR_VERSION >= 10`r`n        v8::IsolateCompat::SetReferenceFromGroup(isolate, v8::UniqueId(reinterpret_cast<intptr_t>(root)), wrapper);`r`n#else`r`n        isolate->SetReferenceFromGroup(v8::UniqueId(reinterpret_cast<intptr_t>(root)), wrapper);`r`n#endif'; Set-Content '%MODULES_DIR%\V8ExtSRGB.cpp' $content -NoNewline }"
)

REM Fix V8ExtTextureFilterAnisotropic.cpp
if exist "%MODULES_DIR%\V8ExtTextureFilterAnisotropic.cpp" (
    echo Fixing V8ExtTextureFilterAnisotropic.cpp...
    powershell -Command "& { $content = Get-Content '%MODULES_DIR%\V8ExtTextureFilterAnisotropic.cpp' -Raw; if ($content -notmatch 'v8_compatibility\.h') { $content = $content -replace '(#include \"wtf/RefPtr\.h\")', '$1`r`n`r`n// V8 10.8 compatibility layer`r`n#if V8_MAJOR_VERSION >= 10`r`n#include \"../../../../../v8_10_8/v8_compatibility.h\"`r`n#endif' }; $content = $content -replace 'isolate->SetReferenceFromGroup\(v8::UniqueId\(reinterpret_cast<intptr_t>\(root\)\), wrapper\);', '#if V8_MAJOR_VERSION >= 10`r`n        v8::IsolateCompat::SetReferenceFromGroup(isolate, v8::UniqueId(reinterpret_cast<intptr_t>(root)), wrapper);`r`n#else`r`n        isolate->SetReferenceFromGroup(v8::UniqueId(reinterpret_cast<intptr_t>(root)), wrapper);`r`n#endif'; Set-Content '%MODULES_DIR%\V8ExtTextureFilterAnisotropic.cpp' $content -NoNewline }"
)

REM Fix V8OESStandardDerivatives.cpp
if exist "%MODULES_DIR%\V8OESStandardDerivatives.cpp" (
    echo Fixing V8OESStandardDerivatives.cpp...
    powershell -Command "& { $content = Get-Content '%MODULES_DIR%\V8OESStandardDerivatives.cpp' -Raw; if ($content -notmatch 'v8_compatibility\.h') { $content = $content -replace '(#include \"wtf/RefPtr\.h\")', '$1`r`n`r`n// V8 10.8 compatibility layer`r`n#if V8_MAJOR_VERSION >= 10`r`n#include \"../../../../../v8_10_8/v8_compatibility.h\"`r`n#endif' }; $content = $content -replace 'isolate->SetReferenceFromGroup\(v8::UniqueId\(reinterpret_cast<intptr_t>\(root\)\), wrapper\);', '#if V8_MAJOR_VERSION >= 10`r`n        v8::IsolateCompat::SetReferenceFromGroup(isolate, v8::UniqueId(reinterpret_cast<intptr_t>(root)), wrapper);`r`n#else`r`n        isolate->SetReferenceFromGroup(v8::UniqueId(reinterpret_cast<intptr_t>(root)), wrapper);`r`n#endif'; Set-Content '%MODULES_DIR%\V8OESStandardDerivatives.cpp' $content -NoNewline }"
)

REM Fix V8OESTextureFloatLinear.cpp
if exist "%MODULES_DIR%\V8OESTextureFloatLinear.cpp" (
    echo Fixing V8OESTextureFloatLinear.cpp...
    powershell -Command "& { $content = Get-Content '%MODULES_DIR%\V8OESTextureFloatLinear.cpp' -Raw; if ($content -notmatch 'v8_compatibility\.h') { $content = $content -replace '(#include \"wtf/RefPtr\.h\")', '$1`r`n`r`n// V8 10.8 compatibility layer`r`n#if V8_MAJOR_VERSION >= 10`r`n#include \"../../../../../v8_10_8/v8_compatibility.h\"`r`n#endif' }; $content = $content -replace 'isolate->SetReferenceFromGroup\(v8::UniqueId\(reinterpret_cast<intptr_t>\(root\)\), wrapper\);', '#if V8_MAJOR_VERSION >= 10`r`n        v8::IsolateCompat::SetReferenceFromGroup(isolate, v8::UniqueId(reinterpret_cast<intptr_t>(root)), wrapper);`r`n#else`r`n        isolate->SetReferenceFromGroup(v8::UniqueId(reinterpret_cast<intptr_t>(root)), wrapper);`r`n#endif'; Set-Content '%MODULES_DIR%\V8OESTextureFloatLinear.cpp' $content -NoNewline }"
)

REM Fix V8OESTextureFloat.cpp
if exist "%MODULES_DIR%\V8OESTextureFloat.cpp" (
    echo Fixing V8OESTextureFloat.cpp...
    powershell -Command "& { $content = Get-Content '%MODULES_DIR%\V8OESTextureFloat.cpp' -Raw; if ($content -notmatch 'v8_compatibility\.h') { $content = $content -replace '(#include \"wtf/RefPtr\.h\")', '$1`r`n`r`n// V8 10.8 compatibility layer`r`n#if V8_MAJOR_VERSION >= 10`r`n#include \"../../../../../v8_10_8/v8_compatibility.h\"`r`n#endif' }; $content = $content -replace 'isolate->SetReferenceFromGroup\(v8::UniqueId\(reinterpret_cast<intptr_t>\(root\)\), wrapper\);', '#if V8_MAJOR_VERSION >= 10`r`n        v8::IsolateCompat::SetReferenceFromGroup(isolate, v8::UniqueId(reinterpret_cast<intptr_t>(root)), wrapper);`r`n#else`r`n        isolate->SetReferenceFromGroup(v8::UniqueId(reinterpret_cast<intptr_t>(root)), wrapper);`r`n#endif'; Set-Content '%MODULES_DIR%\V8OESTextureFloat.cpp' $content -NoNewline }"
)

REM Fix V8OESElementIndexUint.cpp
if exist "%MODULES_DIR%\V8OESElementIndexUint.cpp" (
    echo Fixing V8OESElementIndexUint.cpp...
    powershell -Command "& { $content = Get-Content '%MODULES_DIR%\V8OESElementIndexUint.cpp' -Raw; if ($content -notmatch 'v8_compatibility\.h') { $content = $content -replace '(#include \"wtf/RefPtr\.h\")', '$1`r`n`r`n// V8 10.8 compatibility layer`r`n#if V8_MAJOR_VERSION >= 10`r`n#include \"../../../../../v8_10_8/v8_compatibility.h\"`r`n#endif' }; $content = $content -replace 'isolate->SetReferenceFromGroup\(v8::UniqueId\(reinterpret_cast<intptr_t>\(root\)\), wrapper\);', '#if V8_MAJOR_VERSION >= 10`r`n        v8::IsolateCompat::SetReferenceFromGroup(isolate, v8::UniqueId(reinterpret_cast<intptr_t>(root)), wrapper);`r`n#else`r`n        isolate->SetReferenceFromGroup(v8::UniqueId(reinterpret_cast<intptr_t>(root)), wrapper);`r`n#endif'; Set-Content '%MODULES_DIR%\V8OESElementIndexUint.cpp' $content -NoNewline }"
)

REM Fix V8OESTextureHalfFloat.cpp
if exist "%MODULES_DIR%\V8OESTextureHalfFloat.cpp" (
    echo Fixing V8OESTextureHalfFloat.cpp...
    powershell -Command "& { $content = Get-Content '%MODULES_DIR%\V8OESTextureHalfFloat.cpp' -Raw; if ($content -notmatch 'v8_compatibility\.h') { $content = $content -replace '(#include \"wtf/RefPtr\.h\")', '$1`r`n`r`n// V8 10.8 compatibility layer`r`n#if V8_MAJOR_VERSION >= 10`r`n#include \"../../../../../v8_10_8/v8_compatibility.h\"`r`n#endif' }; $content = $content -replace 'isolate->SetReferenceFromGroup\(v8::UniqueId\(reinterpret_cast<intptr_t>\(root\)\), wrapper\);', '#if V8_MAJOR_VERSION >= 10`r`n        v8::IsolateCompat::SetReferenceFromGroup(isolate, v8::UniqueId(reinterpret_cast<intptr_t>(root)), wrapper);`r`n#else`r`n        isolate->SetReferenceFromGroup(v8::UniqueId(reinterpret_cast<intptr_t>(root)), wrapper);`r`n#endif'; Set-Content '%MODULES_DIR%\V8OESTextureHalfFloat.cpp' $content -NoNewline }"
)

REM Fix V8OESTextureHalfFloatLinear.cpp
if exist "%MODULES_DIR%\V8OESTextureHalfFloatLinear.cpp" (
    echo Fixing V8OESTextureHalfFloatLinear.cpp...
    powershell -Command "& { $content = Get-Content '%MODULES_DIR%\V8OESTextureHalfFloatLinear.cpp' -Raw; if ($content -notmatch 'v8_compatibility\.h') { $content = $content -replace '(#include \"wtf/RefPtr\.h\")', '$1`r`n`r`n// V8 10.8 compatibility layer`r`n#if V8_MAJOR_VERSION >= 10`r`n#include \"../../../../../v8_10_8/v8_compatibility.h\"`r`n#endif' }; $content = $content -replace 'isolate->SetReferenceFromGroup\(v8::UniqueId\(reinterpret_cast<intptr_t>\(root\)\), wrapper\);', '#if V8_MAJOR_VERSION >= 10`r`n        v8::IsolateCompat::SetReferenceFromGroup(isolate, v8::UniqueId(reinterpret_cast<intptr_t>(root)), wrapper);`r`n#else`r`n        isolate->SetReferenceFromGroup(v8::UniqueId(reinterpret_cast<intptr_t>(root)), wrapper);`r`n#endif'; Set-Content '%MODULES_DIR%\V8OESTextureHalfFloatLinear.cpp' $content -NoNewline }"
)

echo.
echo All remaining modules V8 bindings have been fixed for V8 10.8 compatibility!
echo.
echo Summary of fixes applied:
echo - Added V8 10.8 compatibility header includes
echo - Replaced SetReferenceFromGroup calls with compatibility version
echo.
pause
