<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright 2022 The Chromium Authors
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file.
-->
<FrameLayout
    android:id="@+id/fast_checkout_footer_item"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_height="wrap_content"
    android:layout_width="match_parent"
    android:paddingTop="@dimen/fast_checkout_detail_sheet_padding_vertical"
    android:paddingBottom="@dimen/fast_checkout_detail_sheet_padding_vertical">

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/fast_checkout_detail_sheet_footer_icon_margin_horizontal"
        android:layout_gravity="center_vertical"
        android:importantForAccessibility="no"
        app:srcCompat="@drawable/ic_add"
        app:tint="?attr/colorPrimary" />
    <TextView
        android:id="@+id/fast_checkout_add_new_item_label"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="56dp"
        android:textAppearance="@style/TextAppearance.TextMediumThick.Accent1" />
</FrameLayout>
