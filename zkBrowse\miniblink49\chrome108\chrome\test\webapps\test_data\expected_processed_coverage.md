# Generated from the first test:
| MWLC | state_change_a(<PERSON>) |  check_a(<PERSON>) |
| MWLC | state_change_a(<PERSON>) |  check_b(<PERSON>, <PERSON>) |
| MWLC | state_change_b(<PERSON>, <PERSON>) |  check_a(<PERSON>) |
| MWLC | state_change_b(<PERSON>, <PERSON>) |  check_b(<PERSON>, <PERSON>) |


# Generated from the second test:
| MWLC | state_change_a(<PERSON>) |  check_b(<PERSON>, <PERSON>) |

# Generated from the third test:
| C | state_change_a(<PERSON>) |  state_change_b(<PERSON>, Red) |  check_a(<PERSON>) |

# Generated from the fourth test:
| C | state_change_a(<PERSON>) |  state_change_a(<PERSON>) |  check_b(<PERSON>, <PERSON>) |

# Generated from fifth test
| MWLC | state_change_a(<PERSON>) | check_a(<PERSON>) |
| MWLC | state_change_a(<PERSON>) | check_a(<PERSON>) |