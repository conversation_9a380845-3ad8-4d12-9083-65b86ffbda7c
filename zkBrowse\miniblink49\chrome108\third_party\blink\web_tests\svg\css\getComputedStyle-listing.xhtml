<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
  <script src="../../resources/js-test.js"></script>
  <script src="../../fast/css/getComputedStyle/resources/computed-style-listing.js"></script>
  <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <rect id="target" width="100" height="100" fill="green" />
  </svg>
  <script>
    description("This test documents all computed styles on an SVG rect element.");
    listGetComputedStyle(document.getElementById('target'));
  </script>
</html>