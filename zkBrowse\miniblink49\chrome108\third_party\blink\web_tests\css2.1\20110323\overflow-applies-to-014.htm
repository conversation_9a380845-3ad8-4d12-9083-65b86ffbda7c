<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
    <head>
        <title>CSS Test: overflow applied to elements with 'display' set to 'inline-table'</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/">
        <link rel="help" href="http://www.w3.org/TR/CSS21/visufx.html#propdef-overflow">
        <link rel="help" href="http://www.w3.org/TR/CSS21/visufx.html#overflow">
        <meta name="flags" content="ahem">
        <script src="../../resources/ahem.js"></script>
        <meta name="assert" content="The 'overflow' property does apply to elements with 'display' set to 'inline-table'.">
        <style type="text/css">
            #table
            {
                border: 5px solid transparent;
                color: white;
                display: inline-table;
                font: 20px/1em Ahem;
                height: 5em;
                overflow: hidden;
                table-layout: fixed;
                width: 5em;
            }
            #row
            {
                display: table-row;
            }
            #cell
            {
                display: table-cell;
                white-space: nowrap;
            }
            #span2
            {
                color: red;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is no red visible on the page.</p>
        <div id="table">
            <div id="row">
                <div id="cell"><span>XXXXX</span><span id="span2">XXXXX</span></div>
            </div>
        </div>
    </body>
</html>