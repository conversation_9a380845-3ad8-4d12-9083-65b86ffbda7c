<!DOCTYPE html>
<html>
<head>
<script src="../../resources/js-test.js"></script>
<script src="../../resources/gesture-util.js"></script>
<style type="text/css">
::-webkit-scrollbar {
    width: 0px;
    height: 0px;
}
</style>
</head>
<body style="margin:0" onload="runTest();">
<p>
    Tests that page shouldn't scroll when you hit space key on input field
    and 'textInput' event was canceled.
</p>
<div id="console"></div>

<input type="text" id="txt" />
<div style="height: 2000px;"></div>

<script type="text/javascript">
var txt = document.getElementById('txt');
txt.addEventListener('textInput', function(e) {
    if (e.data === ' ') {
        e.preventDefault();
    }
}, false);

async function focusAndPressSpace()
{
    document.getElementById('txt').focus();
    // Leave some space for PageUp
    document.scrollingElement.scrollTop = window.innerHeight / 2;

    eventSender.keyDown("PageUp", []);
    // Space key shouldn't cancel PageUp
    eventSender.keyDown(" ", []);

    await waitForCompositorCommit();
    await waitForAnimationEndTimeBased(() => { return document.scrollingElement.scrollTop; });

    // PageUp should finish
    shouldBecomeEqual("document.scrollingElement.scrollTop", "0", finishJSTest);
}

jsTestIsAsync = true;

async function runTest()
{
    if (window.eventSender) {
        focusAndPressSpace();
    }
}
</script>

</body>
</html>
