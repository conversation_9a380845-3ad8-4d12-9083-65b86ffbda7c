<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background-position using 'ex' units with a nominal value and a plus sign, +7.5ex</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background-position" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <meta name="flags" content="ahem image" />
        <meta name="assert" content="The 'background-position' property correctly applies a nominal length value in 'ex' units with a plus sign." />
        <link rel="stylesheet" type="text/css" href="/fonts/ahem.css" />
        <style type="text/css">
            div
            {
                font: 20px/1 Ahem;
                height: 1in;
            }
            #div1
            {
                border: 3px solid orange;
                width: 135px;
            }
            div div
            {
                background-image: url("support/black15x15.png");
                background-position: +7.5ex;
                background-repeat: no-repeat;
                width: 140px;
            }
        </style>
    </head>
    <body>
        <p>Test passes if the black square below is vertically centered and on the right inner edge of the orange box.</p>
        <div id="div1">
            <div></div>
        </div>
    </body>
</html>