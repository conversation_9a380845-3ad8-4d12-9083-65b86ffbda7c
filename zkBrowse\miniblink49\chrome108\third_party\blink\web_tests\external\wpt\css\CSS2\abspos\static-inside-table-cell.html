<!DOCTYPE html>
<title>Static position inside table cell</title>
<link rel="author" title="Aleks Totic" href="<EMAIL>">
<link rel="help" href="https://www.w3.org/TR/CSS22/visudet.html#abs-non-replaced-width" title="10.3.7 Absolutely positioned, non-replaced elements">
<link rel="match" href="../../reference/ref-filled-green-100px-square.xht">
<p>Test passes if there is a filled green square and <strong>no red</strong>.</p>
<div id="container" style="position:relative;">
  <div id="changeMe" style="height:100px;"></div>
  <div style="display:table-cell;">
    <div style="position:absolute; width:100px; height:100px; background:green;"></div>
    <div style="width:100px; height:100px; background:red;"></div>
  </div>
</div>
<script>
  document.body.offsetTop;
  document.querySelector('#changeMe').style.height = "auto";
</script>
