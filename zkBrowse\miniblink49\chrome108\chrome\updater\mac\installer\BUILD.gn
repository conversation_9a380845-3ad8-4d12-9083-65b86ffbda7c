# Copyright 2022 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/zip.gni")
import("//chrome/updater/branding.gni")

# This zip is a resource for the macOS metainstaller.
zip("updater_zip") {
  output = "$target_gen_dir/updater.zip"
  inputs = [
    "$root_build_dir/${updater_product_full_name}.app/Contents/Info.plist",
    "$root_build_dir/${updater_product_full_name}.app/Contents/MacOS/${updater_product_full_name}",
    "$root_build_dir/${updater_product_full_name}.app/Contents/Helpers/$keystone_app_name.bundle/Contents/MacOS/$keystone_app_name",
    "$root_build_dir/${updater_product_full_name}.app/Contents/Helpers/$keystone_app_name.bundle/Contents/Helpers/ksinstall",
    "$root_build_dir/${updater_product_full_name}.app/Contents/Helpers/$keystone_app_name.bundle/Contents/Helpers/ksadmin",
    "$root_build_dir/${updater_product_full_name}.app/Contents/Helpers/$keystone_app_name.bundle/Contents/Resources/${keystone_app_name}Agent.app/Contents/MacOS/${keystone_app_name}Agent",
    "$root_build_dir/${updater_product_full_name}.app/Contents/Helpers/$keystone_app_name.bundle/Contents/Resources/${keystone_app_name}Agent.app/Contents/Info.plist",
    "$root_build_dir/${updater_product_full_name}.app/Contents/Helpers/$keystone_app_name.bundle/Contents/Info.plist",
    "$root_build_dir/${updater_product_full_name}.app/Contents/PkgInfo",
  ]
  deps = [ "//chrome/updater/mac:updater_bundle" ]
}

executable("metainstaller") {
  output_name = "UpdaterSetup"

  sources = [ "main.mm" ]
  deps = [
    ":updater_zip",
    "//base",
    "//chrome/updater:base",
    "//chrome/updater:constants_prod",
    "//third_party/zlib/google:zip",
  ]

  inputs = [ "$target_gen_dir/updater.zip" ]

  ldflags = [
    "-sectcreate",
    "__DATA",
    "__updater_zip",
    rebase_path(inputs[0], root_out_dir),
  ]
}
