<html><body>
This test does the following:</br>
1. Navigate to an anchor link.  WebKit will ensure that the anchor remains visible as other objects load around it and scripts execute.</br>
2. The div containing the anchor link will be scrolled via PageDown.  This simulated user action should stop us locking the screen to where the anchor is.</br>
3. Force a repaint. If the lock to the anchor was properly released, the scroll caused by the PageDown will not be reverted and parentDiv.scrollTop will be greater than 600px (the offset of the anchor). If we return to the anchor, the test has failed.</br>
<a id="startLink" href="#anchor">Go to anchor</a>
<div id="console"></div>
<div id="parentDiv" style="width:500; height:300; overflow:auto">
<div style="height: 600px"></div>
<a id="anchor"></a>
<div style="height: 800px"></div>
</div>
<script>

if (window.testRunner) {
    testRunner.dumpAsText();
    testRunner.waitUntilDone();
}

if (window.internals) {
    internals.settings.setScrollAnimatorEnabled(false);
}

if (window.eventSender) {
    // Lock to the anchor inside the div
    var target = document.getElementById("startLink");
    eventSender.mouseMoveTo(target.offsetLeft + 2, target.offsetTop + 2);
    eventSender.mouseDown();
    eventSender.mouseUp();

    // Perform a page down to be handled by div, should release anchor lock
    var divInfo = document.getElementById("parentDiv");
    eventSender.mouseMoveTo(divInfo.offsetLeft + 2, divInfo.offsetTop + 2);
    eventSender.mouseDown();
    eventSender.keyDown("PageDown");
}

// Force layout. If the scrollbar gets reset during layout, the test has failed
document.getElementById("startLink").offsetHeight += 1;

var scrollTop = document.getElementById("parentDiv").scrollTop;
var line = document.createElement('div');
if (scrollTop > 600)
    line.appendChild(document.createTextNode("PASS: scrollTop is greater than 600px"));
else
    line.appendChild(document.createTextNode("FAIL: scrollTop is " + scrollTop+ "px, but should be more than 600px"));
document.getElementById('console').appendChild(line);

if (window.testRunner)
    testRunner.notifyDone();

</script></body></html>
