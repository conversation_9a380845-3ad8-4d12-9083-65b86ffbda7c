<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

 <head>

  <title>CSS Reftest Reference</title>

  <link rel="author" title="Gérard Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" />

  <style type="text/css"><![CDATA[
  div
  {
  border: black solid medium;
  line-height: 96px;
  width: 96px;
  }

  img
  {
  padding-left: 81px;
  vertical-align: bottom;
  }
  ]]></style>

 </head>

 <body>

  <p>Test passes if there is 1 and only 1 small filled blue square and if it is located in the lower-right corner of a hollow black square.</p>

  <div><img src="support/blue15x15.png" alt="Image download support must be enabled" /></div>

 </body>
</html>
