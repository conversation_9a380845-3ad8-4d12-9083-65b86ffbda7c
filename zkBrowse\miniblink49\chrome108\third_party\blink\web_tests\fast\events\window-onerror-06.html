<!DOCTYPE html>
<html>
<head>
    <script>
        window.isOnErrorTest = true;
    </script>
    <script src="../../resources/js-test.js"></script>
    <script src="resources/onerror-test.js"></script>
</head>
<body>
    <script>
        description("This test should trigger 'window.onerror' only once, without diving into horrible recursion.");

        function callback(errorNumber) {
            if (errorNumber > 1)
                testFailed("window.onerror should only be called once in this test.");
            throwException("Nested exception.");
        }

        dumpOnErrorArgumentValuesAndReturn(true, callback);

        throwException("Original exception.");
    </script>
</body>
</html>
