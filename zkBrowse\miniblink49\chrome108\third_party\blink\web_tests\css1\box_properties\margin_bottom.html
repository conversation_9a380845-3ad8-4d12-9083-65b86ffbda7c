<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.5.03 margin-bottom</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
.zero {background-color: silver; margin-bottom: 0;}
.one {margin-bottom: 0.5in; background-color: aqua;}
.two {margin-bottom: 25px; background-color: aqua;}
.three {margin-bottom: 5em; background-color: aqua;}
.four {margin-bottom: 25%; background-color: aqua;}
.five {margin-bottom: 25px;}
.six {margin-bottom: -10px; background-color: aqua;}
P, UL {margin-top: 0;}
</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.zero {background-color: silver; margin-bottom: 0;}
.one {margin-bottom: 0.5in; background-color: aqua;}
.two {margin-bottom: 25px; background-color: aqua;}
.three {margin-bottom: 5em; background-color: aqua;}
.four {margin-bottom: 25%; background-color: aqua;}
.five {margin-bottom: 25px;}
.six {margin-bottom: -10px; background-color: aqua;}
P, UL {margin-top: 0;}

</PRE>
<HR>
<P class="zero">
This element has a class of zero.
</P>
<P class="one">
This sentence should have a bottom margin of half an inch, which will require extra text in order to make sure that the margin isn't applied to each line.
</P>
<P class="two">
This sentence should have a bottom margin of 25 pixels, which will require extra text in order to make sure that the margin isn't applied to each line.
</P>
<P class="three">
This sentence should have a bottom margin of 5 em, which will require extra text in order to make sure that the margin isn't applied to each line.
</P>
<P class="four">
This element should have a bottom margin of 25%, which will require extra text in order to make sure that the margin isn't applied to each line.
</P>
<P class="zero">
This element has a class of zero.
</P>
<P class="zero">
This element also has a class of zero.
</P>
<UL class="two">
<LI>This list has a margin-bottom of 25px, and a light blue background.
<LI>Therefore, it ought to have such a margin.
<LI class="five">This list item has a bottom margin of 25px, which should cause it to be offset in some fashion.
<LI>This list item has no special styles applied to it.
</UL>
<P class="six">
This paragraph has a bottom margin of -10px, which should cause elements after it to be shifted "upward" on the page, and no top margin.  No other styles have been applied to it besides a light blue background color.  In all other respects, the element should be normal.
</P>
<P class="zero">
This element has a class of zero.
</P>
<P class="zero">
This element also has a class of zero.
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P class="zero">
This element has a class of zero.
</P>
<P class="one">
This sentence should have a bottom margin of half an inch, which will require extra text in order to make sure that the margin isn't applied to each line.
</P>
<P class="two">
This sentence should have a bottom margin of 25 pixels, which will require extra text in order to make sure that the margin isn't applied to each line.
</P>
<P class="three">
This sentence should have a bottom margin of 5 em, which will require extra text in order to make sure that the margin isn't applied to each line.
</P>
<P class="four">
This element should have a bottom margin of 25%, which will require extra text in order to make sure that the margin isn't applied to each line.
</P>
<P class="zero">
This element has a class of zero.
</P>
<P class="zero">
This element also has a class of zero.
</P>
<UL class="two">
<LI>This list has a margin-bottom of 25px, and a light blue background.
<LI>Therefore, it ought to have such a margin.
<LI class="five">This list item has a bottom margin of 25px, which should cause it to be offset in some fashion.
<LI>This list item has no special styles applied to it.
</UL>
<P class="six">
This paragraph has a bottom margin of -10px, which should cause elements after it to be shifted "upward" on the page, and no top margin.  No other styles have been applied to it besides a light blue background color.  In all other respects, the element should be normal.
</P>
<P class="zero">
This element has a class of zero.
</P>
<P class="zero">
This element also has a class of zero.
</P>
</TD></TR></TABLE></BODY>
</HTML>
