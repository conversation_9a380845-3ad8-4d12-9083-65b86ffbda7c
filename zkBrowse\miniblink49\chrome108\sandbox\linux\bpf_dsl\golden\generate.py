# Copyright 2015 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import os
import sys

arches = ['i386', 'x86-64']

goldens = {}

for fn in sys.argv[2:]:
  dir, name = fn.split('/')[-2:]
  name = name.rstrip('.txt')
  golden = goldens.setdefault(name, [None] * len(arches))
  idx = arches.index(dir)
  golden[idx] = open(fn).read()

with open(sys.argv[1], 'w') as f:
  f.write("""// Generated by sandbox/linux/bpf_dsl/golden/generate.py

#ifndef SANDBOX_LINUX_BPF_DSL_GOLDEN_GOLDEN_FILES_H_
#define SANDBOX_LINUX_BPF_DSL_GOLDEN_GOLDEN_FILES_H_

namespace sandbox {
namespace bpf_dsl {
namespace golden {

struct Golden {
  const char* i386_dump;
  const char* x86_64_dump;
};

""")

  for name, datas in sorted(goldens.items()):
    f.write("const Golden k%s = {\n" % name)
    for data in datas:
      if data is None:
        f.write("  nullptr,\n")
      else:
        f.write("  \"%s\",\n" % data.replace("\n", "\\n\\\n"))
    f.write("};\n\n")

  f.write("""\
}  // namespace golden
}  // namespace bpf_dsl
}  // namespace sandbox

#endif  // SANDBOX_LINUX_BPF_DSL_GOLDEN_GOLDEN_FILES_H_
""")
