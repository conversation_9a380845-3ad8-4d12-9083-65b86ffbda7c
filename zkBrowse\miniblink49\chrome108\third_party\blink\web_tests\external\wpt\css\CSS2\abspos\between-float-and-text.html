<!DOCTYPE html>
<title>Auto-positioned abspos after text, before float</title>
<link rel="author" title="Morten Stenshorne" href="<EMAIL>">
<link rel="help" href="https://www.w3.org/TR/CSS22/visudet.html#abs-non-replaced-width" title="10.3.7 Absolutely positioned, non-replaced elements">
<link rel="match" href="../../reference/ref-filled-green-200px-square.html">
<p>Test passes if there is a filled green square and <strong>no red</strong>.</p>
<div style="line-height:20px; margin-top:-20px;">
  &nbsp;
  <div style="position:absolute; width:200px; height:200px; background:green;"></div>
  <div style="float:left; margin-top:20px; width:200px; height:200px; background:red;"></div>
</div>
