<div id="target" style="overflow: auto; height: 200px; width: 200px;">
    <div onmouseover="mouseOver(event)" style="margin: 300px 0; width: 100px; height: 100px; background-color: blue;"></div>
</div>
<div id="result">Test needs DumpRenderTree</div>
<script>
    function mouseOver(event)
    {
        document.getElementById("result").innerText = "PASS";
        testRunner.notifyDone();
    }

    if (window.testRunner) {
        testRunner.dumpAsText();
        testRunner.waitUntilDone();

        onload = function()
        {
            document.getElementById("target").scrollTop = 250;
            eventSender.mouseMoveTo(50, 100);
        };
    }
</script>
