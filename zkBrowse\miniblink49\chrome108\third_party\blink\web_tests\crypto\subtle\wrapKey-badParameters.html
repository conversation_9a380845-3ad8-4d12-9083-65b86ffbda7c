<!DOCTYPE html>
<html>
<head>
<script src="../../resources/js-test.js"></script>
<script src="resources/common.js"></script>
</head>
<body>
<p id="description"></p>
<div id="console"></div>

<script>
description("Tests calls to wrap<PERSON>ey() with bad inputs.");

jsTestIsAsync = true;

function importWrappingKey()
{
    var data = new Uint8Array(16);
    var extractable = true;
    var keyUsages = ['wrapKey'];

    return crypto.subtle.importKey('raw', data, {name: 'AES-CBC'}, extractable, keyUsages);
}

function importKeyToWrap()
{
    var data = new Uint8Array(16);
    var extractable = true;
    var keyUsages = ['sign'];

    return crypto.subtle.importKey('raw', data, {name: 'HMAC', hash: {name: 'SHA-1'}}, extractable, keyUsages);
}

importWrappingKey().then(function(result) {
    wrappingKey = result;
    return importKeyToWrap();
}).then(function(result) {
    key = result;

    wrapAlgorithm = {name: 'aes-cbc', iv: new Uint8Array(16)};

    // Invalid key
    return crypto.subtle.wrapKey('raw', 1, wrappingKey, wrapAlgorithm);
}).then(failAndFinishJSTest, function(result) {
    logError(result);

    // Invalid wrappingKey
    return crypto.subtle.wrapKey('raw', key, '', wrapAlgorithm);
}).then(failAndFinishJSTest, function(result) {
    logError(result);

    // Invalid wrapAlgorithm
    return crypto.subtle.wrapKey('raw', key, wrappingKey, undefined);
}).then(failAndFinishJSTest, function(result) {
    logError(result);

    // Invalid format for wrapKey
    return crypto.subtle.wrapKey('bad-format', key, wrappingKey, wrapAlgorithm);
}).then(failAndFinishJSTest, function(result) {
    logError(result);

    // SHA-1 isn't a valid wrapKey algorithm.
    return crypto.subtle.wrapKey('raw', key, wrappingKey, {name: 'SHA-1'});
}).then(failAndFinishJSTest, function(result) {
    logError(result);

    // Wrap algorithm doesn't match the wrapping key's algorithm (AES-CBC key
    // with AES-CTR wrap algorithm)
    aesCtrAlgorithm = {name: 'AES-CTR', counter: new Uint8Array(16), length: 0};
    return crypto.subtle.wrapKey('raw', key, wrappingKey, aesCtrAlgorithm);
}).then(failAndFinishJSTest, function(result) {
    logError(result);
}).then(finishJSTest, failAndFinishJSTest);

</script>

</body>
</html>
