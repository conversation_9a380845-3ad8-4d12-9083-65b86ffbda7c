Tests that the panel transitions to the overview view when navigating to an interstitial. Regression test for https://crbug.com/638601

Before selecting origin view:
<DIV class=widget vbox security-main-view >
    <DIV class=security-summary >
        <DIV class=security-summary-section-title role=heading aria-level=1 >
Security overview
        </DIV>
        <DIV class=lock-spectrum >
            <DIV class=lock-icon lock-icon-secure title=Secure >
            </DIV>
            <DIV class=lock-icon lock-icon-neutral title=Info >
            </DIV>
            <DIV class=lock-icon lock-icon-insecure title=Not secure >
            </DIV>
        </DIV>
        <DIV class=triangle-pointer-container >
            <DIV class=triangle-pointer-wrapper >
                <DIV class=triangle-pointer >
                </DIV>
            </DIV>
        </DIV>
        <DIV class=security-summary-text role=heading aria-level=2 >
        </DIV>
    </DIV>
    <DIV class=security-explanation-list security-explanations-main >
    </DIV>
    <DIV class=security-explanation-list security-explanations-extra >
    </DIV>
</DIV>
Panel on origin view before interstitial:
<DIV class=widget vbox security-origin-view slot=insertion-point-main >
    <DIV class=title-section >
        <DIV class=title-section-header role=heading aria-level=1 >
Origin
        </DIV>
        <DIV class=origin-display >
            <SPAN class=security-property security-property-insecure >
            </SPAN>
            <SPAN >
                <SPAN class=url-scheme-insecure >
http
                </SPAN>
                <SPAN class=url-scheme-separator >
://
                </SPAN>
                <SPAN >
foo.test
                </SPAN>
            </SPAN>
        </DIV>
        <DIV class=view-network-button >
            <BUTTON class=text-button type=button role=link >
View requests in Network Panel
            </BUTTON>
        </DIV>
    </DIV>
    <DIV class=origin-view-section >
        <DIV class=origin-view-section-title role=heading aria-level=2 >
Not secure
        </DIV>
        <DIV >
Your connection to this origin is not secure.
        </DIV>
    </DIV>
</DIV>
After interstitial is shown:
<DIV class=widget vbox security-main-view >
    <DIV class=security-summary >
        <DIV class=security-summary-section-title role=heading aria-level=1 >
Security overview
        </DIV>
        <DIV class=lock-spectrum >
            <DIV class=lock-icon lock-icon-secure title=Secure >
            </DIV>
            <DIV class=lock-icon lock-icon-neutral title=Info >
            </DIV>
            <DIV class=lock-icon lock-icon-insecure title=Not secure >
            </DIV>
        </DIV>
        <DIV class=triangle-pointer-container >
            <DIV class=triangle-pointer-wrapper >
                <DIV class=triangle-pointer >
                </DIV>
            </DIV>
        </DIV>
        <DIV class=security-summary-text role=heading aria-level=2 >
        </DIV>
    </DIV>
    <DIV class=security-explanation-list security-explanations-main >
    </DIV>
    <DIV class=security-explanation-list security-explanations-extra >
    </DIV>
</DIV>

