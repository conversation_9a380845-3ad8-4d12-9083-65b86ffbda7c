<html>
<script>
testRunner.disableMockScreenOrientation();

var windowOrientationChangeEvent = false;
var screenOrientationChangeEvent = false;

window.addEventListener("orientationchange", function() { windowOrientationChangeEvent = true; maybeLog(); });
screen.orientation.addEventListener("change", function() { screenOrientationChangeEvent = true; maybeLog(); });

function dump()
{
    return "angle: " + screen.orientation.angle + "; type: " + screen.orientation.type;
}

function maybeLog()
{
    if (windowOrientationChangeEvent && screenOrientationChangeEvent) {
        windowOrientationChangeEvent = false;
        screenOrientationChangeEvent = false;
        console.log(dump());
    }
}
</script>
</html>