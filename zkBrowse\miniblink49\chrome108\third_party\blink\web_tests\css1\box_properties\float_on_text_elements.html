<!DOCTYPE HTML>
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.5.25 float</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
P { margin: 0; padding: 0; text-align: justify;}
.one {float: left; width: 50%; background-color: yellow; padding: 0; margin: 0;}
.two {float: left; background-color: yellow; width: 100%;
      margin: 0; padding: 0; border-width: 0;}
.three {float: none;}
.four {float: left; font-size: 200%; font-weight: bold;
    width: 1.5em; background-color: silver;}
.five {float: right; width: 20em;
      background-color: yellow; border: 3px solid red;
      padding: 5px; margin: 10px;}
.six {float: right; width: 20em;
      background-color: yellow; border: none;
      padding: 5px; margin: 10px;}
.seven {float: right; width: 50%;
       background-color: yellow; border: 1px solid gray;}
.eight {float: left; width: 25%;
        background-color: #66CCFF;
        padding: 0; margin: 0}
.nine {float: right; width: 25%;
       background-color: yellow;
       padding: 0; margin: 0}
</STYLE>

</HEAD>

<BODY><P class="one">This paragraph is of class "one". It has a width of 50%
and is floated to the left.</P>

<P>This paragraph should start on the right side of a yellow box which contains the previous paragraph. Since the text of this element is much longer than the text in the previous element, the text will wrap around the yellow box. There is no padding, border or margins on this and the previous element, so the text of the two elements should be very close to each other.</P>

<BR clear="all"><HR>

<P class="two">This paragraph is of class "two". Since the width has been set to 100%, it should automatically be as wide as its parent element allows it to be. Therefore, even though the element is floated, there is no room for other content on the sides and a orange square image should be seen AFTER the paragraph, not next to it. A yellow background has been added to this paragraph for diagnostic purposes. </P>

<IMG SRC="../resources/oransqr.gif" class="three" alt="[Image]">
<BR clear="all"><HR>

<P class="one">This paragraph is floated to the left and the orange square image should appear to the right of the paragraph. This paragraph has a yellow background and no padding, margin or border. The right edge of this yellow box should be horizontally aligned with the left edge of the yellow box undernearth.</P>

<IMG SRC="../resources/oransqr.gif" class="three" alt="[Image]">
<BR clear="all"><HR>

<P class="one" STYLE="float: right">This paragraph is floated to the right (using a STYLE attribute) and the orange square image should appear to the left of the paragraph. This paragraph has a yellow background and no padding, margin or border. The left edge of this yellow box should be horizonally aligned with the right edge of the yellow box above.</P>

<IMG SRC="../resources/oransqr.gif" class="three" alt="[Image]">
<BR clear="all"><HR>

<P><SPAN class="four">T</SPAN>he first letter (a "T") of this paragraph should float left and be twice the font-size of the rest of the paragraph, as well as bold, with a content width of 1.5em and a background-color of silver.  The top of the big letter "T" should be vertically aligned with the top of the first line of this paragraph. This is commonly known as "drop-cap".</P>

<BR clear="all"><HR>

<P class="five">This paragraph should be floated to the right, sort of like a 'sidebar' in a magazine article. Its width is 20em so the box should not be reformatted when the size of the viewport is changed (e.g. when the window is resized). The background color of the element is yellow, and there should be a 3px solid red border outside a 5px wide padding. Also, the element has a 10px wide margin around it where the blue background of the paragraph in the normal flow should shine through.</P>

<P STYLE="background-color: #66CCFF;">This paragraph is not floating. If there is enough room, the textual content of the paragraph should appear on the left side of the yellow "sidebar" on the right.  The content of this element should flow around the floated element.  However, the floated element may or may not be obscured by the blue background of this element, as the specification does not say which is drawn "on top."  Even if the floated element is obscured, it still forces the content of this element to flow around it.  If the floated element is not obscured, then the blue rectangle of this paragraph should extend 10px above and to the right of the sidebar's red border, due to the margin styles set for the floated element.</P>

<BR clear="all"><HR>

<DIV STYLE="background-color: #66CCFF; padding: 0px; border: solid red 4px;">
    <DIV CLASS="six">

      <P>This paragraph is placed inside a DIV element which is floated to the right. The width of the DIV element is 20em. The background is yellow and there is a 5px padding, a 10px margin and no border. Since it is floated, the yellow box should be rendered on top of the background and borders of adjacent non-floated elements. To the left of this yellow box there should be a short paragraph with a blue background and a red border. The yellow box should be rendered on top of the bottom red border. I.e., the bottom red border will appear broken where it's overlaid by the yellow rectangle.</P>

    </DIV>
    <P>See description in the box on the right side</P>
  </DIV>

<BR clear="all"><HR>

    <DIV CLASS="eight">
      <P>This paragraph is inside a DIV which is floated left. Its
 background is blue and the width is 25%.</P>
    </DIV>

    <DIV CLASS="nine">
      <P>This paragraph is inside a DIV which is floated right. Its
 background is yellow and the width is 25%.</P>
    </DIV>

    <P>This paragraph should appear between a blue box (on the left)
      and a yellow box (on the right).</P>

<BR clear="all"><HR>

    <DIV CLASS="eight" STYLE="width: 75%">

      <DIV CLASS="nine" STYLE="margin-right: 10px">
        <P>See description in the box on the left side.</P>
      </DIV>

      <P>This paragraph is inside a DIV which is floated left. The
      background of the DIV element is blue and its width is 75%. This
      text should all be inside the blue rectangle. The blue DIV
      element has another DIV element as a child. It has a yellow
      background color and is floated to the right. Since it is a
      child of the blue DIV, the yellow DIV should appear inside the
      blue rectangle. Due to it being floated to the right and having
      a 10px right margin, the yellow rectange should have a 10px blue
      stripe on its right side.</P>

    </DIV>

<BR clear="all"><HR>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P class="one">This paragraph is of class "one". It has a width of 50%
and is floated to the left.</P>

<P>This paragraph should start on the right side of a yellow box which contains the previous paragraph. Since the text of this element is much longer than the text in the previous element, the text will wrap around the yellow box. There is no padding, border or margins on this and the previous element, so the text of the two elements should be very close to each other.</P>

<BR clear="all"><HR>

<P class="two">This paragraph is of class "two". Since the width has been set to 100%, it should automatically be as wide as its parent element allows it to be. Therefore, even though the element is floated, there is no room for other content on the sides and a orange square image should be seen AFTER the paragraph, not next to it. A yellow background has been added to this paragraph for diagnostic purposes. </P>

<IMG SRC="../resources/oransqr.gif" class="three" alt="[Image]">
<BR clear="all"><HR>

<P class="one">This paragraph is floated to the left and the orange square image should appear to the right of the paragraph. This paragraph has a yellow background and no padding, margin or border. The right edge of this yellow box should be horizontally aligned with the left edge of the yellow box undernearth.</P>

<IMG SRC="../resources/oransqr.gif" class="three" alt="[Image]">
<BR clear="all"><HR>

<P class="one" STYLE="float: right">This paragraph is floated to the right (using a STYLE attribute) and the orange square image should appear to the left of the paragraph. This paragraph has a yellow background and no padding, margin or border. The left edge of this yellow box should be horizonally aligned with the right edge of the yellow box above.</P>

<IMG SRC="../resources/oransqr.gif" class="three" alt="[Image]">
<BR clear="all"><HR>

<P><SPAN class="four">T</SPAN>he first letter (a "T") of this paragraph should float left and be twice the font-size of the rest of the paragraph, as well as bold, with a content width of 1.5em and a background-color of silver.  The top of the big letter "T" should be vertically aligned with the top of the first line of this paragraph. This is commonly known as "drop-cap".</P>

<BR clear="all"><HR>

<P class="five">This paragraph should be floated to the right, sort of like a 'sidebar' in a magazine article. Its width is 20em so the box should not be reformatted when the size of the viewport is changed (e.g. when the window is resized). The background color of the element is yellow, and there should be a 3px solid red border outside a 5px wide padding. Also, the element has a 10px wide margin around it where the blue background of the paragraph in the normal flow should shine through.</P>

<P STYLE="background-color: #66CCFF;">This paragraph is not floating. If there is enough room, the textual content of the paragraph should appear on the left side of the yellow "sidebar" on the right.  The content of this element should flow around the floated element.  However, the floated element may or may not be obscured by the blue background of this element, as the specification does not say which is drawn "on top."  Even if the floated element is obscured, it still forces the content of this element to flow around it.  If the floated element is not obscured, then the blue rectangle of this paragraph should extend 10px above and to the right of the sidebar's red border, due to the margin styles set for the floated element.</P>

<BR clear="all"><HR>

<DIV STYLE="background-color: #66CCFF; padding: 0px; border: solid red 4px;">
    <DIV CLASS="six">

      <P>This paragraph is placed inside a DIV element which is floated to the right. The width of the DIV element is 20em. The background is yellow and there is a 5px padding, a 10px margin and no border. Since it is floated, the yellow box should be rendered on top of the background and borders of adjacent non-floated elements. To the left of this yellow box there should be a short paragraph with a blue background and a red border. The yellow box should be rendered on top of the bottom red border. I.e., the bottom red border will appear broken where it's overlaid by the yellow rectangle.</P>

    </DIV>
    <P>See description in the box on the right side</P>
  </DIV>

<BR clear="all"><HR>

    <DIV CLASS="eight">
      <P>This paragraph is inside a DIV which is floated left. Its
 background is blue and the width is 25%.</P>
    </DIV>

    <DIV CLASS="nine">
      <P>This paragraph is inside a DIV which is floated right. Its
 background is yellow and the width is 25%.</P>
    </DIV>

    <P>This paragraph should appear between a blue box (on the left)
      and a yellow box (on the right).</P>

<BR clear="all"><HR>

    <DIV CLASS="eight" STYLE="width: 75%">

      <DIV CLASS="nine" STYLE="margin-right: 10px">
        <P>See description in the box on the left side.</P>
      </DIV>

      <P>This paragraph is inside a DIV which is floated left. The
      background of the DIV element is blue and its width is 75%. This
      text should all be inside the blue rectangle. The blue DIV
      element has another DIV element as a child. It has a yellow
      background color and is floated to the right. Since it is a
      child of the blue DIV, the yellow DIV should appear inside the
      blue rectangle. Due to it being floated to the right and having
      a 10px right margin, the yellow rectange should have a 10px blue
      stripe on its right side.</P>

    </DIV>

<BR clear="all"><HR>
</TD></TR></TABLE></BODY>
</HTML>
