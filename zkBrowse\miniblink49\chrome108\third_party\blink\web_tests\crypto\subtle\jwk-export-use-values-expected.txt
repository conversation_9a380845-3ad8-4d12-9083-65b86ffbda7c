Test exporting keys with various usages to JWK.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".

encrypt:
PASS jwk.use is undefined
PASS jwk.key_ops is ["encrypt"]


decrypt:
PASS jwk.use is undefined
PASS jwk.key_ops is ["decrypt"]


encrypt,decrypt:
PASS jwk.use is undefined
PASS jwk.key_ops is ["encrypt","decrypt"]


wrapKey:
PASS jwk.use is undefined
PASS jwk.key_ops is ["wrapKey"]


unwrapKey:
PASS jwk.use is undefined
PASS jwk.key_ops is ["unwrapKey"]


wrapKey,unwrapKey:
PASS jwk.use is undefined
PASS jwk.key_ops is ["wrapKey","unwrapKey"]


encrypt,decrypt,wrapKey:
PASS jwk.use is undefined
PASS jwk.key_ops is ["encrypt","decrypt","wrapKey"]


encrypt,decrypt,wrapKey,unwrapKey:
PASS jwk.use is undefined
PASS jwk.key_ops is ["encrypt","decrypt","wrapKey","unwrapKey"]


sign:
PASS jwk.use is undefined
PASS jwk.key_ops is ["sign"]


verify:
PASS jwk.use is undefined
PASS jwk.key_ops is ["verify"]


sign,verify:
PASS jwk.use is undefined
PASS jwk.key_ops is ["sign","verify"]


PASS successfullyParsed is true

TEST COMPLETE

