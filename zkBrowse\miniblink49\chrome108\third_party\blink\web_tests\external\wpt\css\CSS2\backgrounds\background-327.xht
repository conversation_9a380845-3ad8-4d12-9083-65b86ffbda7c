<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background with inherit inheriting two values</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="G<PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-04-17 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <link rel="match" href="background-326-ref.xht" />

        <meta name="flags" content="image" />
        <meta name="assert" content="Background set to 'inherit' works when inheriting two values." />
        <style type="text/css">
            #wrapper
            {
                background: black url('support/green15x15.png');
            }
            #test
            {
                background: red;
                background: inherit;
                height: 1in;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is <strong>no red</strong>.</p>
        <div id="wrapper">
            <div id="test"></div>
        </div>
    </body>
</html>