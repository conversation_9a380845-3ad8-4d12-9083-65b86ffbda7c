<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN"
"http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">

<!--

   Copyright 2001  The Apache Software Foundation 

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

-->
<!-- ========================================================================= -->
<!-- Validates that CDATA sections in &lt;text&gt; elements are supported.     -->
<!--                                                                           -->
<!-- <AUTHOR>                                             -->
<!-- @version $Id: textPCDATA.svg,v 1.4 2004/08/18 07:12:23 vhardy Exp $        -->
<!-- ========================================================================= -->
<?xml-stylesheet type="text/css" href="../resources/test.css" ?>

<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="450" height="500" viewBox="0 0 450 500">

    <g id="content">

        <text class="title" x="50%" y="30">Character Data on &lt;text&gt;</text>

        <!-- Simple case: CDATA only -->
        <g class="label" text-anchor="middle" transform="translate(225, 120)">
            <text>CDATA only</text>
            <text fill="crimson" y="20"><![CDATA[<text> with a CDATA section]]></text>
        </g>

        <!-- text, CDATA, text -->
        <g class="label" text-anchor="middle" transform="translate(225, 180)">
            <text>text, CDATA, text</text>
            <text fill="crimson" y="20" >Text first, then <![CDATA[CDATA section in <text>]]>, text again</text>
        </g>

        <!-- text, CDATA, tspan, CDATA, text -->
        <g class="label" text-anchor="middle" transform="translate(225, 240)">
            <text>text, CDATA, tspan, CDATA, text</text>
            <text fill="crimson" y="20">text, <![CDATA[CDATA section in <text>]]>, <tspan fill="gold">tspan w/<![CDATA[CDATA section in <tspan>]]></tspan>, text again</text>
        </g>

    </g>
</svg>
