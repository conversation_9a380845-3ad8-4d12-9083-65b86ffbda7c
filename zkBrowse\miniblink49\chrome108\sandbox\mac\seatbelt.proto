// Copyright 2017 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

syntax = "proto2";
option optimize_for = LITE_RUNTIME;

package sandbox.mac;

// Optional fields are considered best practice, but since the client and server
// come from the same code base, this uses required fields to save on
// validation.

message SandboxPolicy {
  required string profile = 1;
  map<string, string> params = 2;
}
