<html>
<head>
<script src="../../resources/js-test.js"></script>
<body>
    <div id="test">
        <input type="text">
        <input type="text"value="foo">
        <textarea></textarea>
        <textarea>foo</textarea>
        <input type="text">
    </div>
    <div id="console"></div>
</body>
<script>

    var BEFORE = 1;
    var AFTER = 2;

    function simulateTextEntry(element, text, opt_clear)
    {
        if (!window.eventSender)
            return null;
            
        var firedEvent = false;
        function listener(event) {
            firedEvent = true;
        }
        element.addEventListener('change', listener, false);
        element.focus();
        if (opt_clear & BEFORE) {
            element.select();
            eventSender.keyDown('Delete');
        }
        for (var i = 0; i < text.length; i++) {
            eventSender.keyDown(text.charAt(i));
        }
        if (opt_clear & AFTER) {
            element.select();
            eventSender.keyDown('Delete');
        }
        element.blur();
        element.removeEventListener('change', listener, false);
        return firedEvent;
    }


    function setTextValue(element, text)
    {
        var firedEvent = false;
        function listener(event) {
            firedEvent = true;
        }
        element.addEventListener('change', listener, false);
        element.focus();
        element.value = text;
        element.blur();
        element.removeEventListener('change', listener, false);
        return firedEvent;
    }

    var elements = document.getElementById('test').getElementsByTagName('*');

    shouldBe("simulateTextEntry(elements[0], '', BEFORE);", "false");
    shouldBe("simulateTextEntry(elements[0], 'fo', BEFORE);", "true");
    shouldBe("simulateTextEntry(elements[0], 'o');", "true");
    shouldBe("simulateTextEntry(elements[0], 'foo', BEFORE);", "false");
    shouldBe("simulateTextEntry(elements[0], 'foo', BEFORE);", "false");
    shouldBe("simulateTextEntry(elements[0], ' ');", "true");
    shouldBe("simulateTextEntry(elements[0], 'foo bar', BEFORE);", "true");
    shouldBe("simulateTextEntry(elements[0], 'foo bar', BEFORE);", "false");
    shouldBe("setTextValue(elements[0], 'foo');", "false");
    shouldBe("simulateTextEntry(elements[0], 'foo bar', BEFORE);", "true");

    shouldBe("simulateTextEntry(elements[1], '', BEFORE);", "true");
    shouldBe("simulateTextEntry(elements[1], 'fo', BEFORE);", "true");
    shouldBe("simulateTextEntry(elements[1], 'o');", "true");
    shouldBe("simulateTextEntry(elements[1], 'foo', BEFORE);", "false");

    shouldBe("simulateTextEntry(elements[2], 'wee', AFTER);", "false");
    shouldBe("simulateTextEntry(elements[2], 'foo', BEFORE | AFTER);", "false");
    shouldBe("simulateTextEntry(elements[2], 'fo', BEFORE);", "true");
    shouldBe("simulateTextEntry(elements[2], 'o');", "true");
    shouldBe("simulateTextEntry(elements[2], 'foo', BEFORE);", "false");

    shouldBe("simulateTextEntry(elements[3], 'foo', BEFORE);", "false");
    shouldBe("simulateTextEntry(elements[3], 'foo', BEFORE);", "false");
    shouldBe("setTextValue(elements[3], '');", "false");
    shouldBe("simulateTextEntry(elements[3], 'fo', BEFORE);", "true");
    shouldBe("simulateTextEntry(elements[3], 'o');", "true");
    shouldBe("simulateTextEntry(elements[3], 'foo', BEFORE);", "false");

    shouldBe("simulateTextEntry(elements[4], 'foo', AFTER);", "false");
    shouldBe("simulateTextEntry(elements[4], 'foo');", "true");
    shouldBe("simulateTextEntry(elements[4], 'foo', BEFORE);", "false");
    shouldBe("simulateTextEntry(elements[4], 'foo', BEFORE | AFTER);", "true");
    shouldBe("simulateTextEntry(elements[4], '', AFTER);", "false");
    shouldBe("simulateTextEntry(elements[4], 'foo', AFTER);", "false");

    // Hide test elements after run to avoid whitespace differences across platforms in the results.
    document.getElementById('test').style.display = 'none';
</script>
</html> 
