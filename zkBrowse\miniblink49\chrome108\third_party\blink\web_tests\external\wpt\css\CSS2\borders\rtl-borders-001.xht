<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Borders drawn in visual order even when direction set to right-to-left</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="G<PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-06-26 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#bidi-box-model" />
        <meta name="assert" content="Borders are drawn in visual order depending on the direction of content." />
        <style type="text/css">
            div
            {
                width: 0.9in;
            }
            span
            {
                border-left: solid blue;
                border-right: solid orange;
                direction: rtl;
            }
        </style>
    </head>
    <body>
        <p>Test passes if the first line of "Filler Text" has an orange border on its right, and the last line of "Filler Text" has a blue border on its left.</p>
        <div>
            <span>Filler Text Filler Text Filler Text</span>
        </div>
    </body>
</html>