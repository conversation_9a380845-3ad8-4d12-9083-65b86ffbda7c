This test should trigger 'window.onerror', and successfully handle the error.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".

window.onerror: "[object Event]" at undefined (Line: undefined, Column: undefined)
No stack trace.
Returning 'true': the error should not be reported in the console as an unhandled exception.



PASS successfullyParsed is true

TEST COMPLETE

