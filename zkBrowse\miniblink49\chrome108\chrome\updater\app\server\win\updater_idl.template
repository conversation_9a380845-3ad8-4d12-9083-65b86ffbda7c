// Copyright 2019 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

import "oaidl.idl";
import "ocidl.idl";


// Public interfaces for the Chromium Updater.
// For documentation, see the similar types defined in updater::UpdateService.

[
  object,
  dual,
  uuid(PLACEHOLDER-GUID-46ACF70B-AC13-406D-B53B-B2C4BF091FF6),
  helpstring("IUpdateState Interface"),
  pointer_default(unique)
]
interface IUpdateState : IUnknown {
  [propget] HRESULT state([out, retval] LONG*);
  [propget] HRESULT appId([out, retval] BSTR*);
  [propget] HRESULT nextVersion([out, retval] BSTR*);
  [propget] HRESULT downloadedBytes([out, retval] LONGLONG*);
  [propget] HRESULT totalBytes([out, retval] LONGLONG*);
  [propget] HRESULT installProgress([out, retval] LONG*);
  [propget] HRESULT errorCategory([out, retval] LONG*);
  [propget] HRESULT errorCode([out, retval] LONG*);
  [propget] HRESULT extraCode1([out, retval] LONG*);
  [propget] HRESULT installerText([out, retval] BSTR*);
  [propget] HRESULT installerCommandLine([out, retval] BSTR*);
};

[
  object,
  dual,
  uuid(PLACEHOLDER-GUID-2FCD14AF-B645-4351-8359-E80A0E202A0B),
  helpstring("ICompleteStatus Interface"),
  pointer_default(unique)
]
interface ICompleteStatus : IUnknown {
  [propget] HRESULT statusCode([out, retval] LONG*);
  [propget] HRESULT statusMessage([out, retval] BSTR*);
};

[
  object,
  oleautomation,
  uuid(PLACEHOLDER-GUID-7B416CFD-4216-4FD6-BD83-7C586054676E),
  helpstring("IUpdaterObserver Interface"),
  pointer_default(unique)
]
interface IUpdaterObserver : IUnknown {
  HRESULT OnStateChange([in] IUpdateState* update_state);
  HRESULT OnComplete([in] ICompleteStatus* status);
};

[
  object,
  dual,
  uuid(PLACEHOLDER-GUID-8BAB6F84-AD67-4819-B846-CC890880FD3B),
  helpstring("IUpdaterCallback Interface"),
  pointer_default(unique)
]
interface IUpdaterCallback : IUnknown {
  HRESULT Run([in] LONG result);
};

[
  object,
  dual,
  uuid(PLACEHOLDER-GUID-63B8FFB1-5314-48C9-9C57-93EC8BC6184B),
  helpstring("IUpdater Interface"),
  pointer_default(unique)
]
interface IUpdater : IUnknown {
  HRESULT GetVersion([out, retval] BSTR* version);
  HRESULT FetchPolicies([in] IUpdaterCallback* callback);
  HRESULT CheckForUpdate([in, string] const WCHAR* app_id);
  HRESULT RegisterApp([in, string] const WCHAR* app_id,
                      [in, string] const WCHAR* brand_code,
                      [in, string] const WCHAR* brand_path,
                      [in, string] const WCHAR* tag,
                      [in, string] const WCHAR* version,
                      [in, string] const WCHAR* existence_checker_path,
                      [in] IUpdaterCallback* callback);
  HRESULT RunPeriodicTasks([in] IUpdaterCallback* callback);
  HRESULT Update([in, string] const WCHAR* app_id,
                 [in, string] const WCHAR* install_data_index,
                 [in] LONG priority,
                 [in] BOOL same_version_update_allowed,
                 [in] IUpdaterObserver* observer);
  HRESULT UpdateAll([in] IUpdaterObserver * observer);
  HRESULT Install([in, string] const WCHAR* app_id,
                  [in, string] const WCHAR* brand_code,
                  [in, string] const WCHAR* brand_path,
                  [in, string] const WCHAR* tag,
                  [in, string] const WCHAR* version,
                  [in, string] const WCHAR* existence_checker_path,
                  [in, string] const WCHAR* client_install_data,
                  [in, string] const WCHAR* install_data_index,
                  [in] LONG priority,
                  [in] IUpdaterObserver* observer);
  HRESULT CancelInstalls([in, string] const WCHAR* app_id);
  HRESULT RunInstaller(
      [in, string] const WCHAR* app_id,
      [in, string] const WCHAR* installer_path,
      [in, string] const WCHAR* install_args,
      [in, string] const WCHAR* install_data,
      [in, string] const WCHAR* install_settings,
      [in] IUpdaterObserver* observer);
  };

[
  uuid(PLACEHOLDER-GUID-69464FF0-D9EC-4037-A35F-8AE4358106CC),
  version(1.0),
  helpstring("Chromium Updater type library")
]
library UpdaterLib {
  importlib("stdole2.tlb");

  [
    uuid(PLACEHOLDER-GUID-158428a4-6014-4978-83ba-9fad0dabe791),
    helpstring("Updater Class for per-user applications")
  ]
  coclass UpdaterUserClass
  {
    [default] interface IUnknown;
  }

  [
    uuid(PLACEHOLDER-GUID-415FD747-D79E-42D7-93AC-1BA6E5FD4E93),
    helpstring("Updater Class for per-system applications")
  ]
  coclass UpdaterSystemClass
  {
    [default] interface IUnknown;
  }

  interface ICompleteStatus;
  interface IUpdater;
  interface IUpdaterObserver;
  interface IUpdateState;
};
