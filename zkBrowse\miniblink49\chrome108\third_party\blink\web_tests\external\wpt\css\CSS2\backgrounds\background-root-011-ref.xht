<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

 <head>

  <title>CSS Reftest Reference</title>

  <link rel="author" title="Gérard Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" />

  <style type="text/css"><![CDATA[
  html {background-color: transparent;}

  body
  {
  background-color: transparent;
  border: blue solid;
  margin: 1em;
  padding: 2em;
  }

  div
  {
  border: lime solid;
  padding: 2em;
  }

  div > div
  {
  background-color: white;
  border: none;
  padding: 1em;
  }
  ]]></style>

 </head>

 <body>

  <div>
    <div>Enclosing this text should be a solid lime border around which
there should be a solid blue one. The backgrounds are undefined.</div>
  </div>

 </body>
</html>
