<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN"
"http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">

<!--

   Copyright 2001-2002  The Apache Software Foundation 

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

-->
<!-- ========================================================================= -->
<!-- Test description here                                                     -->
<!--                                                                           -->
<!-- <AUTHOR>                                      -->
<!-- @version $Id: textLayout.svg,v 1.6 2004/08/18 07:12:21 vhardy Exp $                                                             -->
<!-- ========================================================================= -->
<?xml-stylesheet type="text/css" href="../resources/test.css" ?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="450" height="500" viewBox="0 0 450 500">
<title>Text Layout Properties</title>

    <!-- ============================================================= -->
    <!-- Test content                                                  -->
    <!-- ============================================================= -->

    <defs>
     <style type="text/css"><![CDATA[
       .Caption { font-size: 8px; font-family: dialog; font-weight: normal;
                  font-style: normal; stroke: none; stroke-width: 0; 
                  fill: black } ]]>
     </style>

     <style type="text/css"><![CDATA[
       .SerifPlain { font-size: 24px; font-family: Serif; font-weight: normal;
                  font-style: normal; stroke: none; stroke-width: 0; 
                  fill: black } ]]>
     </style>

     <text x="0" y="0" text-anchor="start" id="sample">sample</text>

    </defs>

    <g id="testContent" text-anchor="start" font-size="30" class="SerifPlain">
        <text text-anchor="middle" class="title" x="50%" y="10%" font-size="15" >
            Text Layout Properties</text>
       
       <text x="50%" y="15%" text-anchor="middle" font-size="10">Explicit Glyph Positioning</text>

        <g>
            <text><tspan x="40,60,80,105,125,140" y="110">sample</tspan></text>
            <text x="40" y="125" class="Caption">tspan x="40,60,80,105,125,140"</text>
        </g>

        <g>
            <text x="185" y="110">sample</text>
            <text x="185" y="125" class="Caption">Serif, default spacing</text>
        </g>

        <g>
            <text x="290" y="110"><tspan dx="0,10,10,10,10,10">sample</tspan></text>
            <text x="290" y="125" class="Caption">x="175" dx="0,10,10,10,10,10"
</text>
        </g>

       <text x="50%" y="30%" text-anchor="middle" font-size="10">Letter Spacing Adjustment and Explicit Length Specification</text>

        <g>
            <text x="40" y="180" textLength="120">sample</text>
            <text x="40" y="195" class="Caption">textLength="120"</text>
        </g>

        <g>
            <text x="185" y="180" textLength="70">sample</text>
            <text x="185" y="195" class="Caption">textLength="70"</text>
        </g>

        <g>
            <text x="40" y="225" textLength="120" lengthAdjust="spacingAndGlyphs">sample</text>
            <text x="40" y="240" class="Caption">textLength="120",
            <tspan x="40" dy="10">lengthAdjust="spacingAndGlyphs"</tspan></text>
        </g>

        <g>
            <text x="185" y="225" textLength="70" lengthAdjust="spacingAndGlyphs">sample</text>
            <text x="185" y="240" class="Caption">textLength="70"                          <tspan x="185" dy="10">lengthAdjust="spacingAndGlyphs"</tspan></text>
        </g>

        <g>
            <text x="40" y="275" letter-spacing="0.3em">sample</text>
            <text x="40" y="290" class="Caption">letter-spacing="0.3em"</text>        
        </g>

        <g>
            <text x="185" y="275" letter-spacing="-3">sample</text>
            <text x="185" y="290" class="Caption">letter-spacing="-3"</text>
       </g>

        <g>
            <text x="290" y="275" letter-spacing="10">sample</text>
            <text x="290" y="290" class="Caption">letter-spacing="10"</text>
        </g>

       <text x="50%" y="64%" text-anchor="middle" font-size="10">Word Spacing Adjustment</text>

        <g font-size="15">
            <text x="40" y="350" textLength="350" word-spacing="3em">Wide separation between words</text>
            <text x="40" y="365" class="Caption">textLength="350" word-spacing="3em"</text>
        </g>

        <g font-size="15">
            <text x="40" y="395" textLength="350" word-spacing="-5">Narrow separation between words</text>
            <text x="40" y="410" class="Caption">textLength="350" word-spacing="-5"</text>
        </g>

        <g font-size="15">
            <text x="40" y="440" textLength="350" lengthAdjust="spacingAndGlyphs" word-spacing="-5">Narrow separation between words</text>
            <text x="40" y="455" class="Caption">textLength="350" lengthAdjust="spacingAndGlyphs" word-spacing="-5"</text>
        </g>
    </g>

    <!-- ============================================================= -->
    <!-- Batik sample mark                                             -->
    <!-- ============================================================= -->
    <use xlink:href="../resources/batikLogo.svg#Batik_Tag_Box" />
    
</svg>
