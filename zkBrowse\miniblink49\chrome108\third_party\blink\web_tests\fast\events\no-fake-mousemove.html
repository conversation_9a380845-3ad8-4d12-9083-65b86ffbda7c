<!DOCTYPE html>
<script src='../../resources/testharness.js'></script>
<script src='../../resources/testharnessreport.js'></script>
<style type="text/css">
#test {
    width: 250px;
    height: 250px;
    background-color: red;
}
</style>

<body>
<div id="test">

<script type="text/javascript">
var mouse_move_count = 0;
var last_mouse_position_x = 0;
var last_mouse_position_y = 0;
var test = document.getElementById("test");
var targetRect = test.getBoundingClientRect();
var offset = 100;
var x = targetRect.left + offset;
var y = targetRect.top + offset;

function mouseMoveHandler(event) {
    mouse_move_count++;
    test.style.display = 'none';
    document.elementFromPoint(event.clientX, event.clientY);
    test.style.display = 'block';
    testNoMouseMove.step(function () {
        assert_not_equals(last_mouse_position_x, event.clientX);
        assert_not_equals(last_mouse_position_y, event.clientY);
    });
    last_mouse_position_x = event.clientX;
    last_mouse_position_y = event.clientY;
}

function callbackValidMouseMove() {
    testNoMouseMove.step(function () {
        assert_equals(1, mouse_move_count);
    });
    testNoMouseMove.done();
}

function testNoMouseMoveActions() {
    if (window.chrome && chrome.gpuBenchmarking) {
        var pointerActions =
            [{source: "mouse",
              actions: [
                { name: "pointerMove", x: x, y: y },
                { name: 'pause', duration: 1000 }]}];
        chrome.gpuBenchmarking.pointerActionSequence(pointerActions, callbackValidMouseMove);
    }
}

var testNoMouseMove = async_test('Tests that no mousemove event is fired after the mouse moves over to the red area and does not move any more.');
test.addEventListener('mousemove', mouseMoveHandler);
testNoMouseMoveActions();

</script>
</body>
