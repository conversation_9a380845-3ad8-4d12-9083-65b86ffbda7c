Tests WebInspector.RemoveObject.setPropertyValue implementation.


Running: testSetUp

Running: testSetPrimitive

Running: testSetHandle

Running: testSetUndefined

Running: testSetZero

Running: testSetNull

Running: testSetEmptyString

Running: testSetException
exception

Running: testSetNonFiniteNumbers

Running: testNegativeZero

Running: testReleaseObjectIsCalled
runtime-setPropertyValue.js:14 ===== Initial =====
runtime-setPropertyValue.js:15 {"foo":1}
runtime-setPropertyValue.js:16 
runtime-setPropertyValue.js:14 ===== Set primitive =====
runtime-setPropertyValue.js:15 {"foo":2}
runtime-setPropertyValue.js:16 
runtime-setPropertyValue.js:14 ===== Set handle =====
runtime-setPropertyValue.js:15 {"foo":{"bar":2}}
runtime-setPropertyValue.js:16 
runtime-setPropertyValue.js:14 ===== Set undefined =====
runtime-setPropertyValue.js:15 {}
runtime-setPropertyValue.js:16 
runtime-setPropertyValue.js:14 ===== Set zero =====
runtime-setPropertyValue.js:15 {"foo":0}
runtime-setPropertyValue.js:16 
runtime-setPropertyValue.js:14 ===== Set null =====
runtime-setPropertyValue.js:15 {"foo":null}
runtime-setPropertyValue.js:16 
runtime-setPropertyValue.js:14 ===== Set empty string =====
runtime-setPropertyValue.js:15 {"foo":""}
runtime-setPropertyValue.js:16 
runtime-setPropertyValue.js:14 ===== Set exception =====
runtime-setPropertyValue.js:15 {"foo":""}
runtime-setPropertyValue.js:16 
runtime-setPropertyValue.js:14 ===== Set non-finite numbers =====
runtime-setPropertyValue.js:15 {"foo":"NaN","foo1":"Infinity","foo2":"-Infinity"}
runtime-setPropertyValue.js:16 
runtime-setPropertyValue.js:28 ===== Checking negative zero =====
runtime-setPropertyValue.js:29 1/-0 = -Infinity

