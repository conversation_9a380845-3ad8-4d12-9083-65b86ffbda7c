<!DOCTYPE html>
<meta charset="utf-8">
<title>TestDriver actions: two touch points with one moving one pause</title>
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<script src="/resources/testdriver.js"></script>
<script src="/resources/testdriver-actions.js"></script>
<script src="/resources/testdriver-vendor.js"></script>
<script src="touchEvents.js"></script>

<style>
div#test1{
  position: fixed;
  touch-action: none;
  top: 0;
  left: 0;
  width: 100px;
  height: 100px;
  background-color: blue;
}

</style>

<div id="test1">
</div>

<script>

promise_test(async t => {
  const test1 = document.getElementById("test1");
  const events = [];
  addPointerEventListeners(t, test1, events);
  await new test_driver.Actions()
      .addPointer("touchPointer1", "touch")
      .addPointer("touchPointer2", "touch")
      .pointerMove(0, 0, {origin: test1, sourceName: "touchPointer1"})
      .pointerMove(10, 0, {origin: test1, sourceName: "touchPointer2"})
      .pointerDown({sourceName: "touchPointer1"})
      .pointerDown({sourceName: "touchPointer2"})
      .pause(0, "pointer", {sourceName: "touchPointer1"})
      .pointerMove(0, 10, {origin: test1, sourceName: "touchPointer2"})
      .pointerUp({sourceName: "touchPointer1"})
      .pointerUp({sourceName: "touchPointer2"})
      .send();

  eventEquals(events[0], {type: "pointerdown", pointerId: 2, clientX: 50, clientY: 50});
  eventEquals(events[1], {type: "pointerdown", pointerId: 3, clientX: 60, clientY: 50});
  // Allow one or two pointermove events
  let index = 3;
  const moveEvents = [events[2]];
  if (events[3].type === "pointermove") {
    index += 1;
    moveEvents.push(events[3]);
  }
  for (const event of moveEvents) {
    if (event.pointerId === 2) {
      eventEquals(event, {type: "pointermove", clientX: 50, clientY: 50});
    } else {
      eventEquals(event, {type: "pointermove", pointerId: 3, clientX: 50, clientY: 60});
    }
  }
  let remainingEvents = events.slice(index);
  assert_equals(remainingEvents.length, 2);
  eventEquals(remainingEvents[0], {type: "pointerup", pointerId: 2, clientX: 50, clientY: 50});
  eventEquals(remainingEvents[1], {type: "pointerup", pointerId: 3, clientX: 50, clientY: 60});
});
</script>
