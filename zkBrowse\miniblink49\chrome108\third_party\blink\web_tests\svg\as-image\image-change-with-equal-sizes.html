<!DOCTYPE HTML>
<html>
<body>
  crbug.com/269946: This test passes if there are two green squares with the same sizes.<br/>
  <img id="image" width="100px" height="100px">
  <img id="reference" width="100px" height="100px">

  <script>
    if (window.testRunner)
      testRunner.waitUntilDone();

    // base64 encoded SVG image of a 200x200 green square.
    var greenSquare = 'data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+DQo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjIwMCIgaGVpZ2h0PSIyMDAiPg0KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJncmVlbiIvPg0KPC9zdmc+DQo=';

    document.getElementById('reference').src = greenSquare;
    var image = document.getElementById('image');
    image.src = greenSquare;

    // Change the image to an external image with dimensions 200x200.
    image.src = 'resources/200x200-blue-rect.svg';
    image.onload = function() {
      setTimeout(function() {
        // Change the image back to a 200x200 green square and verify that
        // the original dimensions are restored.
        image.src = greenSquare;
        if (window.testRunner)
          testRunner.notifyDone();
      }, 0);
    }
  </script>
</body>
</html>

