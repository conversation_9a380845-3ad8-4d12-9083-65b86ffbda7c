// GENERATED CONTENT - DO NOT EDIT
// Content was automatically extracted by <PERSON><PERSON> into webref
// (https://github.com/w3c/webref)
// Source: Scalable Video Coding (SVC) Extension for WebRTC (https://w3c.github.io/webrtc-svc/)

partial dictionary RTCRtpEncodingParameters {
             DOMString scalabilityMode;
};

partial dictionary RTCRtpCodecCapability {
             sequence<DOMString> scalabilityModes;
};
