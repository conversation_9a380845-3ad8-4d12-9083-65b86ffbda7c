# Copyright 2020 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/chromeos/ui_mode.gni")
import("//net/features.gni")

source_set("lib") {
  sources = [
    "cert_verifier_creation.cc",
    "cert_verifier_creation.h",
    "cert_verifier_service.cc",
    "cert_verifier_service.h",
    "cert_verifier_service_factory.cc",
    "cert_verifier_service_factory.h",
  ]

  if (trial_comparison_cert_verifier_supported) {
    sources += [
      "trial_comparison_cert_verifier_mojo.cc",
      "trial_comparison_cert_verifier_mojo.h",
    ]
  }

  deps = [
    "//base",
    "//build:chromeos_buildflags",
    "//net",
    "//net/cert:root_store_proto_lite",
    "//services/cert_verifier/cert_net_url_loader",
    "//services/network/public/mojom",
  ]

  public_deps = [
    "//services/cert_verifier/public/mojom",
    "//services/network/public/mojom",
  ]
}

source_set("tests") {
  testonly = true

  sources = [
    "cert_verifier_service_factory_unittest.cc",
    "cert_verifier_service_unittest.cc",
    "integration_tests/network_context_unittest.cc",
    "integration_tests/network_service_unittest.cc",
    "integration_tests/ssl_config_service_mojo_unittest.cc",
  ]

  if (trial_comparison_cert_verifier_supported) {
    sources += [ "trial_comparison_cert_verifier_mojo_unittest.cc" ]
  }

  deps = [
    ":lib",
    "//base",
    "//base/test:test_support",
    "//build:chromeos_buildflags",
    "//crypto:test_support",
    "//net",
    "//net:test_support",
    "//net/cert:root_store_proto_lite",
    "//services/cert_verifier/cert_net_url_loader:cert_net_url_loader",
    "//services/cert_verifier/public/mojom",
    "//services/network:network_service",
    "//services/network:test_support",
    "//services/network/public/cpp/cert_verifier:mojo_cert_verifier",
    "//services/network/public/mojom",
    "//testing/gtest",
  ]
}
