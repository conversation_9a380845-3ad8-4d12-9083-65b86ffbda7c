<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.2.7 font</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
BODY {font-size: 12px;}
.one {font: italic small-caps 13pt Helvetica;}
.two {font: 150%/150% serif;}
.three {font: 150%/150% sans-serif;}
.four {font: small/200% cursive;}
.five {font: italic small-caps 900 150%/150% sans-serif;}
.six {font: italic small-caps 100 150%/300% sans-serif;}
.seven {font: italic small-caps 900 150%/2em monospace;}
.eight {font: italic small-caps 500 150%/1in sans-serif;}
.nine {font: oblique normal 700 18px/200% sans-serif;}
.ten {font: normal 400 80%/2.5 sans-serif;}
SPAN.color {background-color: silver;}</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>BODY {font-size: 12px;}
.one {font: italic small-caps 13pt Helvetica;}
.two {font: 150%/150% serif;}
.three {font: 150%/150% sans-serif;}
.four {font: small/200% cursive;}
.five {font: italic small-caps 900 150%/150% sans-serif;}
.six {font: italic small-caps 100 150%/300% sans-serif;}
.seven {font: italic small-caps 900 150%/2em monospace;}
.eight {font: italic small-caps 500 150%/1in sans-serif;}
.nine {font: oblique normal 700 18px/200% sans-serif;}
.ten {font: normal 400 80%/2.5 sans-serif;}
SPAN.color {background-color: silver;}
</PRE>
<HR>
<P>
This element is unstyled, and should inherit a font-size of 12px from the BODY element.  This is the "base font size" referred to in the following tests.
</P>
<P class="one">
This element should be 13pt. Helvetica which is in small-cap italics.
</P>
<P class="two">
This element should be in a serif font.  Its font-size should be 150% the base font size, and its line-height should 150% of that value (18px and 27px, respectively).  Extra text is included for the purposes of testing this more effectively.
</P>
<P class="three">
This element should be in a sans-serif font.  Its font-size should be 150% the base font size, and its line-height should 150% of that value (18px and 27px, respectively).  Extra text is included for the purposes of testing this more effectively.
</P>
<P class="four">
This element should be in a cursive font, 'small' in size, with a line-height 200% the height of the text's actual size.  For example, if the font-size value <CODE>small</CODE> is calculated at 10px, then the line-height should be 20px.  The actual value of the font-size is UA-dependent.  Extra text is included for the purposes of testing this more effectively.
</P>
<P class="five">
This element should be in a sans-serif font, italicized and small caps, with a weight of 900.  Its font-size should be 150% the base font size, and its line-height should be 150% of that value (18px and 27px, respectively).  Extra text is included for the purposes of testing this more effectively.
</P>
<P class="six">
This element should be in a sans-serif font, italicized and small caps, with a weight of 100.  Its font-size should be 150% the base font size, and its line-height should be 300% of that value (18px and 54px, respectively).  Extra text is included for the purposes of testing this more effectively.
</P>
<P class="seven">
This element should be in a monospace font, italicized and small caps, with a weight of 900.  Its font-size should be 150% the base font size, and its line-height should be 2em, or twice the element's font size (18px and 36px, respectively).  Extra text is included for the purposes of testing this more effectively.
</P>
<P class="eight">
This element should be in a sans-serif font, italicized and small caps, with a weight of 500.  Its font-size should be 150% the base font size, or 18px, and its line-height should be 1in.  Extra text is included for the purposes of testing this more effectively.
</P>
<P class="nine">
This element should be in a sans-serif font, oblique and not small-caps, with a weight of 700.  Its font-size should be 18 pixels, and its line-height should be 36px (200% this element's font size).  Extra text is included for the purposes of testing this more effectively.
</P>
<P class="ten">
This element should be in a sans-serif font, with a weight of 400.  Its font-size should be 80% of 12px, or 9.6px, and its line-height shoud be 2.5 times that, or 24px.  Extra text is included for the purposes of testing this more effectively.
</P>

<P class="six">
<SPAN class="color">
This element should be in a sans-serif font, italicized and small caps, with a weight of 100.  Its font-size should be 150% the base font size, and its line-height should be 300% of that value (18px and 54px, respectively). The text should have a silver background. The background color has been set on an inline element and should therefore only cover the text, not the interline spacing.</SPAN>
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P>
This element is unstyled, and should inherit a font-size of 12px from the BODY element.  This is the "base font size" referred to in the following tests.
</P>
<P class="one">
This element should be 13pt. Helvetica which is in small-cap italics.
</P>
<P class="two">
This element should be in a serif font.  Its font-size should be 150% the base font size, and its line-height should 150% of that value (18px and 27px, respectively).  Extra text is included for the purposes of testing this more effectively.
</P>
<P class="three">
This element should be in a sans-serif font.  Its font-size should be 150% the base font size, and its line-height should 150% of that value (18px and 27px, respectively).  Extra text is included for the purposes of testing this more effectively.
</P>
<P class="four">
This element should be in a cursive font, 'small' in size, with a line-height 200% the height of the text's actual size.  For example, if the font-size value <CODE>small</CODE> is calculated at 10px, then the line-height should be 20px.  The actual value of the font-size is UA-dependent.  Extra text is included for the purposes of testing this more effectively.
</P>
<P class="five">
This element should be in a sans-serif font, italicized and small caps, with a weight of 900.  Its font-size should be 150% the base font size, and its line-height should be 150% of that value (18px and 27px, respectively).  Extra text is included for the purposes of testing this more effectively.
</P>
<P class="six">
This element should be in a sans-serif font, italicized and small caps, with a weight of 100.  Its font-size should be 150% the base font size, and its line-height should be 300% of that value (18px and 54px, respectively).  Extra text is included for the purposes of testing this more effectively.
</P>
<P class="seven">
This element should be in a monospace font, italicized and small caps, with a weight of 900.  Its font-size should be 150% the base font size, and its line-height should be 2em, or twice the element's font size (18px and 36px, respectively).  Extra text is included for the purposes of testing this more effectively.
</P>
<P class="eight">
This element should be in a sans-serif font, italicized and small caps, with a weight of 500.  Its font-size should be 150% the base font size, or 18px, and its line-height should be 1in.  Extra text is included for the purposes of testing this more effectively.
</P>
<P class="nine">
This element should be in a sans-serif font, oblique and not small-caps, with a weight of 700.  Its font-size should be 18 pixels, and its line-height should be 36px (200% this element's font size).  Extra text is included for the purposes of testing this more effectively.
</P>
<P class="ten">
This element should be in a sans-serif font, with a weight of 400.  Its font-size should be 80% of 12px, or 9.6px, and its line-height shoud be 2.5 times that, or 24px.  Extra text is included for the purposes of testing this more effectively.
</P>

<P class="six">
<SPAN class="color">
This element should be in a sans-serif font, italicized and small caps, with a weight of 100.  Its font-size should be 150% the base font size, and its line-height should be 300% of that value (18px and 54px, respectively). The text should have a silver background. The background color has been set on an inline element and should therefore only cover the text, not the interline spacing.</SPAN>
</P>
</TD></TR></TABLE></BODY>
</HTML>
