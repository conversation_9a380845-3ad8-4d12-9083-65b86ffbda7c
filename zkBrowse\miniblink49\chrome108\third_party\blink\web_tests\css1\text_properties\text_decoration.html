<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.4.3 text-decoration</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
.one {text-decoration: underline;}
.two {text-decoration: overline;}
.three {text-decoration: line-through;}
.four {text-decoration: blink;}
B.five {text-decoration: none;}
.six {text-decoration: underline overline;}
.seven {text-decoration: underline overline line-through;}
</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.one {text-decoration: underline;}
.two {text-decoration: overline;}
.three {text-decoration: line-through;}
.four {text-decoration: blink;}
B.five {text-decoration: none;}
.six {text-decoration: underline overline;}
.seven {text-decoration: underline overline line-through;}

</PRE>
<HR>
<P class="one">
This sentence should be underlined.
</P>
<P class="two">
This sentence should be overlined.
</P>
<P class="three">
This sentence should have stricken text (linethrough).
</P>
<P class="four">
This element should be blinking.  (It is not required, however, that UAs support this behavior.)
</P>

<P class="one">
The text in this element should be underlined.  The boldfaced text in this element <B class="five">should also be underlined</B>.  This is because the parent's underline will 'span' the boldfaced text, even if the inline element has no underline of its own.
</P>

<P class="six">
This sentence should be underlined and overlined.
</P>
<P class="seven">
This sentence should be underlined, overlined, and stricken.
</P>
<P class="seven">
</P>
<P>
There should be nothing visible between this sentence and the one above (there is an empty paragraph element with class of seven).
</P>
<P class="one">
Text decorations only apply to the text of an element, so the image at the end of this sentence should <EM>not</EM> be overlined: <IMG SRC="../resources/oransqr.gif" class="two" alt="[Image]">.  The underline of the parent element should hold true beneath the image, however, since text-decoration 'spans' child elements.
</P>
<P style="color: green;" class="one">
The underlining <SPAN style="color: blue;">in this sentence</SPAN> should be green, no matter what the <SPAN style="color: black;">text color may be</SPAN>.
</P>
<P class="one">
The colors of the <SPAN style="color: purple;">underlining</SPAN> in <SPAN style="color: blue;">this sentence</SPAN> should be <SPAN style="color: gray;">the same as that of the parent text</SPAN> (that is, the first word in the sentence, which should be black).
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P class="one">
This sentence should be underlined.
</P>
<P class="two">
This sentence should be overlined.
</P>
<P class="three">
This sentence should have stricken text (linethrough).
</P>
<P class="four">
This element should be blinking.  (It is not required, however, that UAs support this behavior.)
</P>

<P class="one">
The text in this element should be underlined.  The boldfaced text in this element <B class="five">should also be underlined</B>.  This is because the parent's underline will 'span' the boldfaced text, even if the inline element has no underline of its own.
</P>

<P class="six">
This sentence should be underlined and overlined.
</P>
<P class="seven">
This sentence should be underlined, overlined, and stricken.
</P>
<P class="seven">
</P>
<P>
There should be nothing visible between this sentence and the one above (there is an empty paragraph element with class of seven).
</P>
<P class="one">
Text decorations only apply to the text of an element, so the image at the end of this sentence should <EM>not</EM> be overlined: <IMG SRC="../resources/oransqr.gif" class="two" alt="[Image]">.  The underline of the parent element should hold true beneath the image, however, since text-decoration 'spans' child elements.
</P>
<P style="color: green;" class="one">
The underlining <SPAN style="color: blue;">in this sentence</SPAN> should be green, no matter what the <SPAN style="color: black;">text color may be</SPAN>.
</P>
<P class="one">
The colors of the <SPAN style="color: purple;">underlining</SPAN> in <SPAN style="color: blue;">this sentence</SPAN> should be <SPAN style="color: gray;">the same as that of the parent text</SPAN> (that is, the first word in the sentence, which should be black).
</P>
</TD></TR></TABLE></BODY>
</HTML>
