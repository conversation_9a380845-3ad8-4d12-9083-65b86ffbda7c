#!/usr/bin/env python
# Copyright 2017 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

from __future__ import print_function

import os
import sys

"""Pack MacOS sandbox seatbelt .sb files as C-style strings, escaping
quotes and backslashes as needed.
"""

header = '// Generated by package_sb_file.py. Do not edit !!!\n\n'
namespace = 'namespace sandbox {\nnamespace policy{\n\n'
namespace_end = '\n}  // namespace policy\n}  // namespace sandbox\n'
h_include = '#include "sandbox/policy/export.h"\n'
h_definition = ('SANDBOX_POLICY_EXPORT\n' +
                'extern const char kSeatbeltPolicyString_%s[];\n\n')
cc_include = '#include "sandbox/policy/mac/%s.sb.h"\n'
cc_definition = 'const char kSeatbeltPolicyString_%s[] = \n'
cc_definition_end = '"";\n'  # Add "" so the definition has some content
                             # (the empty string) if the sb file is empty.

def escape_for_c(line):
  if line and line[0] == ';':
    return ''
  return line.replace('\\', '\\\\').replace('\"', '\\\"')

def pack_file(argv):
  if len(argv) != 2:
    print >> sys.stderr, 'usage: package_sb_file.py input_filename output_dir'
    return 1
  input_filename = argv[0]
  output_directory = argv[1]
  input_basename = os.path.basename(input_filename)
  (module_name, module_ext) = os.path.splitext(input_basename)
  output_h_file = output_directory + '/' + input_basename + '.h'
  output_cc_file = output_directory + '/' + input_basename + '.cc'
  try:
    with open(input_filename, 'r') as infile:
      with open(output_h_file, 'w') as outfile:
        outfile.write(header)
        outfile.write(h_include)
        outfile.write(namespace)
        outfile.write(h_definition % module_name)
        outfile.write(namespace_end)
      with open(output_cc_file, 'w') as outfile:
        outfile.write(header)
        outfile.write(cc_include % module_name)
        outfile.write(namespace)
        outfile.write(cc_definition % module_name)
        for line in infile:
          escaped_line = escape_for_c(line.rstrip())
          if escaped_line:
            outfile.write('    "' + escaped_line + '\\n"\n')
        outfile.write(cc_definition_end)
        outfile.write(namespace_end)
  except IOError:
    print('Failed to process %s' % input_filename, file=sys.stderr)
    return 1
  return 0

if __name__ == '__main__':
  sys.exit(pack_file(sys.argv[1:]))
