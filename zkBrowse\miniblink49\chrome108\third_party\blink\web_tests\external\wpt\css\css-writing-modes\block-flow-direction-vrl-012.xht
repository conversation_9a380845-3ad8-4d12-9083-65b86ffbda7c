<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

 <head>

  <title>CSS Writing Modes Test: inline-block and 'vertical-rl' - block flow direction of block-level boxes</title>

  <link rel="author" title="Gérard Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" />
  <link rel="help" href="http://www.w3.org/TR/css-writing-modes-3/#block-flow" title="3.1 Block Flow Direction: the writing-mode property" />
  <link rel="match" href="block-flow-direction-001-ref.xht" />

  <meta content="ahem" name="flags" />
  <meta content="This test checks that an inline-block with its 'writing-mode' set to 'vertical-rl' establishes a block formating context with a right-to-left block flow direction." name="assert" />

  <link rel="stylesheet" type="text/css" href="/fonts/ahem.css" />
  <style type="text/css"><![CDATA[
  body
    {
      color: yellow;
      font: 20px/1 Ahem;
    }

  div#inline-block
    {
      background-color: blue;
      border-top: blue solid 1em;
      display: inline-block;
      height: 8em;
      vertical-align: top;
  /*
  Why 'vertical-align: top' ?
  See
  http://lists.w3.org/Archives/Public/public-css-testsuite/2014Dec/0013.html
  for explanations
  */
      writing-mode: vertical-rl;
    }

  span
    {
      border-right: blue solid 1em;
      display: block;
    }

  span#left-border
    {
      border-left: blue solid 1em;
    }
  ]]></style>
 </head>

 <body>

  <div>

    <div id="inline-block">

<!-- The right-most "S" --> <span>A&nbsp; BBBB C&nbsp; D&nbsp; E F&nbsp; G&nbsp; H JJJJ&nbsp; K</span>

<!-- The left-most "S" --> <span>L&nbsp; MMMM Q&nbsp; R&nbsp; S T&nbsp; U&nbsp; V WWWW&nbsp; X</span>

<!-- The "A" --> <span>YYYYYYY Z&nbsp; a&nbsp;&nbsp; b&nbsp; c&nbsp;&nbsp; ddddddd</span>

<!-- The "P" --> <span id="left-border">eeee&nbsp;&nbsp; f&nbsp; g&nbsp;&nbsp; h&nbsp; j&nbsp;&nbsp; kkkkkkk</span>

    </div>

  </div>

 </body>
</html>