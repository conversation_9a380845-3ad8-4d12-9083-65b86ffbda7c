<!DOCTYPE html>
<html>
<head>
<script src="../../resources/js-test.js"></script>
<script src="resources/common.js"></script>
</head>
<body>
<p id="description"></p>
<div id="console"></div>

<script>
description("Tests crypto.subtle.encrypt() using a BufferSource that is modified during algorithm normalization");

jsTestIsAsync = true;

data = null;
key = null;

var keyData = hexStringToUint8Array("2b7e151628aed2a6abf7158809cf4f3c");
var iv = hexStringToUint8Array("000102030405060708090a0b0c0d0e0f");
var kPlainText = "6bc1bee22e409f96e93d7e117393172aae2d8a571e03ac9c9eb76fac45af8e5130c81c46a35ce411e5fbc1191a0a52eff69f2445df4f9b17ad2b417be66c3710";
var kExpectedCipherTextHex = "7649abac8119b246cee98e9b12e9197d5086cb9b507219ee95db113a917678b273bed6b8e3c1743b7116e69e222295163ff1caa1681fac09120eca307586e1a78cb82807230e1321d3fae00d18cc2012";
var plainText = hexStringToUint8Array(kPlainText);
// This is the plaintext resulting from corruptPlainText.
var kModifiedPlainText = "0000bee22e409f96e93d7e117393172aae2d8a571e03ac9c9eb76fac45af8e5130c81c46a35ce411e5fbc1191a0a52eff69f2445df4f9b17ad2b417be66c3710";

function corruptPlainText()
{
    debug("Corrupted plainText");
    plainText[0] = 0;
    plainText[1] = 0;
}

var extractable = true;
var usages = ['encrypt', 'decrypt'];

debug("Importing key...");
crypto.subtle.importKey('raw', keyData, "aes-cbc", extractable, usages).then(function(result) {
    key = result;
    
    debug("\nEncrypting (as a control group)...");
    return crypto.subtle.encrypt({name: "aes-cbc", iv: iv}, key, plainText);
}).then(function(result) {
    bytesShouldMatchHexString("Encryption", kExpectedCipherTextHex, result);

    // This algorithm has custom getters that modifies plainText.
    var algorithm = {
        get name() {
            debug("Accessed name property");
            corruptPlainText();
            return 'aes-cbc';
        },

        get iv() {
            debug("Accessed iv property");
            corruptPlainText();
            return iv;
        }
    };

    debug("\nEncrypting again, using an algorithm that mutates the array buffer...");
    return crypto.subtle.encrypt(algorithm, key, plainText);
}).then(function(result) {
    bytesShouldMatchHexString("plainText", kModifiedPlainText, plainText);
    bytesShouldMatchHexString("Encryption", kExpectedCipherTextHex, result);
}).then(finishJSTest, failAndFinishJSTest);
</script>

</body>
</html>
