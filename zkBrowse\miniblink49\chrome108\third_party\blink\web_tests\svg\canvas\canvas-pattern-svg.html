<!DOCTYPE html>
<html lang="en">
 <head>
  <title>Test for WK113420: SVG-based patterns should be drawn correctly</title>
  <style> canvas { border:solid #000 } </style>
  <script>
   window.onload = function(){
    var i = new Image();
    i.src = "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='50' height='50'><circle cx='10' cy='10' r='10' fill='green' /></svg>";
    i.onload = function() {
      var ctx = document.getElementsByTagName('canvas')[0].getContext('2d');

      var pattern = ctx.createPattern(i, "repeat");
      ctx.fillStyle = pattern;
      ctx.translate(10, 10);
      ctx.strokeRect(0, 0, 150, 150);
      ctx.fillRect(0, 0, 150, 150);

      var patternX = ctx.createPattern(i, "repeat-x");
      ctx.fillStyle = patternX;
      ctx.translate(0, 160);
      ctx.strokeRect(0, 0, 150, 150);
      ctx.fillRect(0, 0, 150, 150);

      var patternN = ctx.createPattern(i, "no-repeat");
      ctx.fillStyle = patternN;
      ctx.translate(160, 0);
      ctx.strokeRect(0, 0, 150, 150);
      ctx.fillRect(0, 0, 150, 150);

      var patternY = ctx.createPattern(i, "repeat-y");
      ctx.fillStyle = patternY;
      ctx.translate(0, -160);
      ctx.strokeRect(0, 0, 150, 150);
      ctx.fillRect(0, 0, 150, 150);
    }
   }
  </script>
 </head>
 <body>
  <p>There should be one big square below containing four squares. Top left square should be filled with 3 rows of 3 circles. Top right square should have one column of 3 circles. The bottom left square should have one row with three circles. The bottom right square should have one circle in the top-left corner.</p>
  <p><canvas height="330" width="330"></canvas></p>
 </body>
</html>
