<!doctype html>
<title>HTML styles</title>
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<style>
#parent {
  display: none;
}

div.b {
  all: initial;
  direction: initial;
  unicode-bidi: isolate;
  display: block;
}

div.c {
  background: red;
  background: initial;
}

span.b {
  all: initial;
  direction: initial;
  unicode-bidi: initial;
  display: inline;
}
</style>
<div id="parent">
  <div class="a"></div>
  <div class="b"></div>
  <div class="c"></div>
  <span class="a"></span>
  <span class="b"></span>
  <p></p>
  <ul>
    <li>
  </ul>
  <ol>
    <li>
  </ol>
  <table>
    <tbody>
      <tr>
        <td>
  </table>
</div>
<script>
test(function() {
  assert_true('all' in document.documentElement.style);
}, "(pre-req for comparison tests) all CSS short-hand supported");

test(function() {
  assert_in_array(window.getComputedStyle(document.querySelector("div.c")).backgroundColor,
                  ["rgba(0, 0, 0, 0)", "transparent"]);
}, "(pre-req for comparison tests) initial CSS value supported");

test(function() {
  var a = document.querySelector("div.a");
  var b = document.querySelector("div.b");

  var a_styles = window.getComputedStyle(a);
  var b_styles = window.getComputedStyle(b);

  assert_equals(a_styles.length, b_styles.length, "Same properties on both div.a and div.b");

  for (var i = 0; i < a_styles.length; i++) {
    var property = a_styles[i];
    assert_equals(property, b_styles[i], "Same property on div.a and div.b");
    if (property !== "unicode-bidi") {
      assert_equals(a_styles[property], b_styles[property], "Different value for " + property);
    }
  }
}, "Compare CSS div definitions (only valid if pre-reqs pass)");

test(function() {
  var a = document.querySelector("span.a");
  var b = document.querySelector("span.b");

  var a_styles = window.getComputedStyle(a);
  var b_styles = window.getComputedStyle(b);

  assert_equals(a_styles.length, b_styles.length, "Same properties on both span.a and span.b");

  for (var i = 0; i < a_styles.length; i++) {
    var property = a_styles[i];
    assert_equals(property, b_styles[i], "Same property on span.a and span.b");
    assert_equals(a_styles[property], b_styles[property], "Different value for " + property);
  }
}, "Compare CSS span definitions (only valid if pre-reqs pass)");

test(function() {
  var p = document.getElementsByTagName("p")[0];
  var styles = window.getComputedStyle(p);
  assert_equals(styles["display"], "block");
}, "p is display: block");

test(function() {
  var ul_li = document.querySelector("ul > li");
  var styles = window.getComputedStyle(ul_li);
  assert_equals(styles["display"], "list-item");
}, "ul > li is display: list-item");

test(function() {
  var ol_li = document.querySelector("ol > li");
  var styles = window.getComputedStyle(ol_li);
  assert_equals(styles["display"], "list-item");
}, "ol > li is display: list-item");

test(function() {
  var table = document.getElementsByTagName("table")[0];
  var styles = window.getComputedStyle(table);
  assert_equals(styles["display"], "table");
}, "table is display: table");

test(function() {
  var tbody = document.getElementsByTagName("tbody")[0];
  var styles = window.getComputedStyle(tbody);
  assert_equals(styles["display"], "table-row-group");
}, "tbody is display: table-row-group");

test(function() {
  var tr = document.getElementsByTagName("tr")[0];
  var styles = window.getComputedStyle(tr);
  assert_equals(styles["display"], "table-row");
}, "tr is display: table-row");

test(function() {
  var td = document.getElementsByTagName("td")[0];
  var styles = window.getComputedStyle(td);
  assert_equals(styles["display"], "table-cell");
}, "td is display: table-cell");
</script>
