<!DOCTYPE html>
<html>
<head>
<style>

.scroll {
    height: 100px;
    overflow: auto;
}

.filler {
    height: 200px;
}

</style>
</head>
<body>
<p>Ensure that simulated click on label elements uses the original mouse coordinates.</p>
<input type="checkbox" id="target" onclick="validateEventProperties(event)">
<label for="target" onclick="storeEvent(event)">Click Me</label>

<div class="scroll">
    <div class="filler"></div>
    <input type="radio" id="target2" onclick="validateEventProperties(event)">
    <label for="target2" onclick="storeEvent(event)">Click Me</label>
</div>

<pre id="out"></pre>
<script>

if (window.testRunner)
    testRunner.dumpAsText();

function clickOn(element)
{
    if (!window.eventSender)
        return;
    var rect = element.getBoundingClientRect();
    var x = Math.floor(rect.left + rect.width / 2);
    var y = Math.floor(rect.top + rect.height / 2);
    eventSender.mouseMoveTo(x, y);
    eventSender.mouseDown();
    eventSender.mouseUp();
}

function print(s)
{
    document.getElementById('out').textContent += s + '\n';
}

var realClickEvent, simulatedEvent;

function storeEvent(event)
{
    realClickEvent = event;
}

function validateEventProperty(name)
{
    if (realClickEvent[name] === simulatedEvent[name])
        print('PASS: event.' + name + ' is same for both real and simulated event.');
    else
        print('FAIL: event.' + name + ' is ' + simulatedEvent[name] + ', expected ' + realClickEvent[name]);
}

function validateEventProperties(event)
{
    simulatedEvent = event;

    validateEventProperty('clientX');
    validateEventProperty('clientY');
    validateEventProperty('layerX');
    validateEventProperty('layerY');
    validateEventProperty('pageX');
    validateEventProperty('pageY');
    validateEventProperty('screenX');
    validateEventProperty('screenY');
    validateEventProperty('x');
    validateEventProperty('y');
}

clickOn(document.querySelector('label'));

print('');
document.querySelector('.scroll').scrollTop = 999;
clickOn(document.querySelector('.scroll label'));

</script>
</body>
</html>
