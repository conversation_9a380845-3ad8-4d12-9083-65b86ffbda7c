  1) LOAD 4  // Architecture
  2) if A == 0xc000003e; then JMP 3 else JMP 26
  3) LOAD 0  // System call number
  4) if A & 0x40000000; then JMP 26 else JMP 5
  5) if A >= 0x79; then JMP 6 else JMP 8
  6) if A >= 0x7a; then JMP 7 else JMP 10
  7) if A >= 0x401; then JMP 28 else JMP 27
  8) if A >= 0x69; then JMP 9 else JMP 27
  9) if A >= 0x6a; then JMP 27 else JMP 19
 10) LOAD 20  // Argument 0 (MSB)
 11) if A == 0x0; then JMP 15 else JMP 12
 12) if A == 0xffffffff; then JMP 13 else JMP 26
 13) LOAD 16  // Argument 0 (LSB)
 14) if A & 0x80000000; then JMP 15 else JMP 26
 15) LOAD 16  // Argument 0 (LSB)
 16) if A == 0x0; then JMP 18 else JMP 17
 17) RET 0x50016  // errno = 22
 18) RET 0x50001  // errno = 1
 19) LOAD 20  // Argument 0 (MSB)
 20) if A == 0x0; then JMP 24 else JMP 21
 21) if A == 0xffffffff; then JMP 22 else JMP 26
 22) LOAD 16  // Argument 0 (LSB)
 23) if A & 0x80000000; then JMP 24 else JMP 26
 24) LOAD 16  // Argument 0 (LSB)
 25) if A == 0x2a; then JMP 27 else JMP 26
 26) RET 0x0  // Kill
 27) RET 0x7fff0000  // Allowed
 28) RET 0x50026  // errno = 38
