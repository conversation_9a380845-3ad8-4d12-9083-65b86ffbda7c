<!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML//EN">
<html>
<head>
<script src="../../resources/js-test.js"></script>
<body id="body">

<map name="mymap">
<area id="area1" shape="circle" coords="70,84,51" href="1">
<area id="area2" shape="rect" coords="25,180,125,280" href="2">
<area id="area3" shape="poly" coords="153,106,186,225,340,193,315,81,304,167" href="3">
<area id="area4" shape="rect" coords="420,19,478,278" nohref>
<area id="area5" shape="circle" coords="220,150,100" href="4">
<area id="area6" shape="default" coords="0,0,500,300" href="5">
<area id="area7" shape="rect" coords="1, 1, 10, 10" tabindex=-1 href="6">
</map>

<img src="resources/abe.png" width="500" height="300" alt="Image Map" usemap="#mymap" ismap>


<p id="description"></p>
<div id="console"></div>

<script>

    description("This tests that links in a image map are able to be reached through keyboard access and tabbing.");

    eventSender.keyDown('\t');
    shouldBe("document.activeElement.id", "'area1'");

    eventSender.keyDown('\t');
    shouldBe("document.activeElement.id", "'area2'");

    eventSender.keyDown('\t');
    shouldBe("document.activeElement.id", "'area3'");

    // We'll skip over area4 because its nohref
    eventSender.keyDown('\t');
    shouldBe("document.activeElement.id", "'area5'");

    eventSender.keyDown('\t');
    shouldBe("document.activeElement.id", "'area6'");

    // We'll skip over area7 because its tabindex=-1. On the Mac, the wrap around should give 
    // area1. On GTK, we will go to the body element.
    eventSender.keyDown('\t');
    shouldBe("document.activeElement.id == 'area1' || document.activeElement.id == 'body'", "true");
</script>

</body>
</html>
