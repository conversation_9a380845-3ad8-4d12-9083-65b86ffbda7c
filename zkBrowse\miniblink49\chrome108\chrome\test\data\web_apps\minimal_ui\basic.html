<!DOCTYPE html>
<html>
<head>
  <title>Site B</title>
    <script src="/web_apps/test_utils.js"></script>
    <script>
      // If a "manifest=/path/to/manifest.json" query argument is provided to
      // the URL accessing this page, that path is injected as the manifest tag.
      // Otherwise, "basic.json" is used as the manifest tag.
      addManifestLinkTag();
    </script>
</head>
<body onload="startWorker('/web_apps/minimal_ui/service_worker.js', { scope: '/web_apps/minimal_ui/' })">
  <h1>Site B</h1>
  <div>
    <p>
      This site is used for dPWA integration tests, and is subject to modification to support that framework. See
      <a
        href="https://chromium.googlesource.com/chromium/src/+/main/docs/webapps/integration-testing-framework.md">https://chromium.googlesource.com/chromium/src/+/main/docs/webapps/integration-testing-framework.md</a>
    </p>
  </div>
</body>
</html>
