# Copyright 2022 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/android/rules.gni")

android_library("java") {
  deps = [
    "//base:base_java",
    "//base:jni_java",
    "//build/android:build_java",
    "//chrome/android:chrome_java",
    "//chrome/browser/touch_to_fill/payments/android:public_java",
    "//components/browser_ui/bottomsheet/android:java",
    "//third_party/androidx:androidx_annotation_annotation_java",
    "//ui/android:ui_java",
  ]

  sources = [
    "java/src/org/chromium/chrome/browser/touch_to_fill/payments/TouchToFillCreditCardControllerBridge.java",
    "java/src/org/chromium/chrome/browser/touch_to_fill/payments/TouchToFillCreditCardCoordinator.java",
    "java/src/org/chromium/chrome/browser/touch_to_fill/payments/TouchToFillCreditCardMediator.java",
    "java/src/org/chromium/chrome/browser/touch_to_fill/payments/TouchToFillCreditCardProperties.java",
    "java/src/org/chromium/chrome/browser/touch_to_fill/payments/TouchToFillCreditCardView.java",
    "java/src/org/chromium/chrome/browser/touch_to_fill/payments/TouchToFillCreditCardViewBinder.java",
    "java/src/org/chromium/chrome/browser/touch_to_fill/payments/TouchToFillCreditCardViewBridge.java",
  ]

  annotation_processor_deps = [ "//base/android/jni_generator:jni_processor" ]
}
