; Copyright 2018 The Chromium Authors
; Use of this source code is governed by a BSD-style license that can be
; found in the LICENSE file.

; --- The contents of common.sb implicitly included here. ---

; Injected parameters.
(define network-service-storage-paths-count "NETWORK_SERVICE_STORAGE_PATHS_COUNT")
(define network-service-storage-path-n "NETWORK_SERVICE_STORAGE_PATH_")
(define network-service-test-certs-dir "NETWORK_SERVICE_TEST_CERTS_DIR")

; Allow access to the [0,N) storage location paths.
(let ((count (string->number (param network-service-storage-paths-count))))
  (let loop ((i 0))
    (if (< i count)
      (begin
        (allow file* (subpath
          (param (string-append network-service-storage-path-n (number->string i)))))
        (loop (+ i 1))))))

; DNS configuration watcher entries. This is a nesty mess of symlinks.
(allow file-read*
  (path "/")
  (path "/etc")
  (path "/etc/hosts")
  (path "/etc/resolv.conf")
  (path "/private")
  (path "/private/etc")
  (path "/private/etc/hosts")
  (path "/private/etc/resolv.conf")
  (path "/private/var")
  (path "/private/var/run")
  (path "/private/var/run/resolv.conf")
  (path "/var")
  (path "/var/run")
)

; Certificate databases.
(allow file-read*
  (path "/Library/Preferences/com.apple.security.plist")
  (path (user-homedir-path "/Library/Preferences/com.apple.security.plist"))
  ; https://crbug.com/1024000
  (path (user-homedir-path "/Library/Preferences/com.apple.security.revocation.plist"))
  (subpath "/Library/Keychains")
  (subpath "/System/Library/Keychains")
  (subpath "/System/Library/Security")
  (subpath "/private/var/db/mds")
  (subpath (user-homedir-path "/Library/Keychains"))
)
(allow file-read* file-write*
  (subpath (param darwin-user-cache-dir))
  (subpath (param darwin-user-temp-dir))
)
(if (param-defined? network-service-test-certs-dir)
  (allow file-read* (subpath (param network-service-test-certs-dir))))

; Network socket access.
(allow network-outbound
  (control-name "com.apple.netsrc")
  (literal "/private/var/run/mDNSResponder")
  (remote tcp)
  (remote udp)
)
(allow network-bind network-inbound
  (local tcp)
  (local udp)
)

; DNS resolution.
(allow system-socket
  (require-all (socket-domain AF_SYSTEM)
               (socket-protocol 2)) ; SYSPROTO_CONTROL
  (socket-domain AF_ROUTE)
)

; Distributed notifications memory.
(allow ipc-posix-shm-read-data
  (ipc-posix-name "apple.shm.notification_center")
)

; Notification data from the security server database.
(allow ipc-posix-shm
  (ipc-posix-name "com.apple.AppleDatabaseChanged")
)

(allow mach-lookup
  ; Set backup exclusion on cache files.
  (global-name "com.apple.backupd.sandbox.xpc")

  ; Used to look up the _CS_DARWIN_USER_CACHE_DIR in the sandbox.
  (global-name "com.apple.bsd.dirhelper")
  (global-name "com.apple.system.opendirectoryd.membership")

  ; Allow notifications of DNS changes.
  (global-name "com.apple.system.notification_center")

  ; Communicate with the security server for TLS certificate information.
  (global-name "com.apple.SecurityServer")
  (global-name "com.apple.networkd") ; https://crbug.com/1024000
  (global-name "com.apple.ocspd")
  (global-name "com.apple.trustd.agent")

  ; Read network configuration.
  (global-name "com.apple.SystemConfiguration.DNSConfiguration")
  (global-name "com.apple.SystemConfiguration.configd")
)

(allow sysctl-read
  (sysctl-name-regex #"^net.routetable")
)

; Kerberos support. This should be removed after GSS is moved out of the
; network service. https://crbug.com/1017830
(allow mach-lookup
  (global-name "com.apple.GSSCred") ; https://crbug.com/1134449
  (global-name "org.h5l.kcm")
)
(allow file-read*
  (path "/private/etc/krb5.conf")
  (subpath "/System/Library/KerberosPlugins") ; https://crbug.com/1134449
)
