<html>
<head>
<script language="javascript">
function log(s)
{
    document.getElementById("console").appendChild(document.createTextNode(s + "\n"));
}

function mouseOver(event)
{
    if (event === null) {
        log("FAIL: After MouseEvent, event is null");
    } else {
        log("PASS: After MouseEvent, event is valid");
    }
}

function runTest() {
    var target = document.getElementById("target");
    var newEvent = document.createEvent("MouseEvent");
    newEvent.initMouseEvent("mouseover", false, false, window, 0, 10, 10, 10, 10, false, false, false, false, 0, target);
    target.dispatchEvent(newEvent);

    if (window.testRunner) {
        testRunner.dumpAsText();
    }
}

</script>
</head>
<body onLoad="runTest();">
<div id="target" onMouseOver="mouseOver(event);">This test will trigger a mouse over of this element.  
We will then check to see if event was properly set (i.e., not null).
This is in response to a v8 bug that can break window.event exposure if the client tries to manually set event.</div>
<div id="console"></div>
<script>
if (!(event === undefined)) {
    log("FAIL: event does not begin as undefined");
}
event = null;
if (!(event === null)) {
    log("FAIL: event was not successfully set to null");
}
</script>
</body>
</html>
