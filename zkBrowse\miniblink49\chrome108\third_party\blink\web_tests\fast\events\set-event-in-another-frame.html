<html>
<head>
<script>
if (window.testRunner) {
    testRunner.dumpAsText();
    testRunner.waitUntilDone();
}

function log(msg) {
    var line = document.createElement('div');
    line.appendChild(document.createTextNode(msg));
    document.getElementById('console').appendChild(line);
}

function getEvent() {
    var target = frames[1].window;
    var newEvent = frames[1].document.createEvent("MouseEvent");
    newEvent.initMouseEvent("mouseover", false, false, window, 0, 10, 10, 10, 10, false, false, false, false, 0, target);
    target.dispatchEvent(newEvent);
}


var testStarted = false;
function doTest() {
  if (testStarted)
      return;

  testStarted = true;
  frames[0].location = "javascript:" +
      "window.myeventnull = function() { parent.frames[1].window.event = null; };" +
      "window.myeventreal = function() { parent.frames[1].window.event = parent.getEvent(); };" +
      "parent.log('Helpers loaded!\\n');" +
      "parent.setTimeout('continueTest()', 10);";
}

function continueTest() {
    log('--- Test begins ---\n');
    log('window.event = ' + frames[1].window.event + '\n');
    log('--- After window.event = null ---\n');
    frames[0].myeventnull();
    log('window.event = ' + frames[1].window.event + '\n');
    log('--- After MouseEvent completes ---\n');
    frames[0].myeventreal();
    log('window.event = ' + frames[1].window.event + '\n');
    log('--- Test ends ---\n');

    testRunner.notifyDone();
}
</script>
</head>
<body onload="doTest()">
<iframe src="about:blank"></iframe>
<iframe src=""></iframe>
<div id="console"></div>
</body>
</html>
