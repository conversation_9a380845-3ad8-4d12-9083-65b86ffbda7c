<html>
  <head>
    <script>
        if (window.testRunner)
        {
            testRunner.dumpAsText();
            testRunner.waitUntilDone();
        }
        window.onload = function ()
        {
            var element = document.getElementById("test");
            element.onchange = function() { element.size = 30; }
            element.focus();
            if (window.testRunner)
            {
                // This triggers selection of second option in the select and press Enter.
                eventSender.keyDown("e");
                eventSender.keyDown("\r", []);
                // This triggers repaint.
                document.body.offsetTop;
                
                document.getElementById("log").innerHTML = "<span style='color: green;'>PASS:</span> Did not crash";
                testRunner.notifyDone();
            }
         }
    </script>
  </head>
  <body>
    This test is to ensure that we do not crash when onchange handler changes the select from a menu list to a list box.
    <p id="log"><span style='color: red;'>FAIL:</span> Did not complete test or not running inside DumpRenderTree</p>
    <select id="test">
      <option selected>abcd</option>
      <option>efgh</option>
    </select>
  </body>
</html>

