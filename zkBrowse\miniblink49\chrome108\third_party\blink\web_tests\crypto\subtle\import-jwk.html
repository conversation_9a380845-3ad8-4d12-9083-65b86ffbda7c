<!DOCTYPE html>
<html>
<head>
<script src="../../resources/js-test.js"></script>
<script src="resources/common.js"></script>
</head>
<body>
<p id="description"></p>
<div id="console"></div>

<script>
description("Test error handling for JWK import.");

jsTestIsAsync = true;

var extractable = true;
var nonExtractable = false;

var hmac256 = {name: "HMAC", hash: {name: "sha-256"}};

Promise.resolve(null).then(function(result) {
    // 'null' treated as a Dictionary with default values - an empty dictionary
    return crypto.subtle.importKey("jwk", null, {name: "aes-cbc"}, extractable, ["encrypt"]);
}).then(failAndFinishJSTest, function(result) {
    logError(result);
    // 'undefined' treated as a Dictionary with default values - an empty dictionary
    return crypto.subtle.importKey("jwk", undefined, {name: "aes-cbc"}, extractable, ["encrypt"]);
}).then(failAndFinishJSTest, function(result) {
    logError(result);
    return crypto.subtle.importKey("jwk", {}, {name: "aes-cbc"}, extractable, ["encrypt"]);
}).then(failAndFinishJSTest, function(result) {
    logError(result);
    // Unknown/invalid JWK values.
    return crypto.subtle.importKey("jwk", { "kty": "foobar", "alg": "HS256", "use": "sig", "k": "ahjkn23387fgnsibf23qsvahjkn37387fgnsibf23qs" }, hmac256, extractable, ["sign"]);
}).then(failAndFinishJSTest, function(result) {
    logError(result);
    return crypto.subtle.importKey("jwk", { "kty": "oct", "alg": "foobar", "use": "enc", "k": "ahjkn23387fgnsibf23qsvahjkn37387fgnsibf23qs" }, {name: "aes-cbc"}, extractable, ["encrypt"]);
}).then(failAndFinishJSTest, function(result) {
    logError(result);
    // Algorithm mismatch.
    return crypto.subtle.importKey("jwk", { "kty": "oct", "alg": "HS256", "use": "enc", "ext": false, "k": "ahjkn23387fgnsibf23qsvahjkn37387fgnsibf23qs" }, {name: "AES-cbc"}, nonExtractable, ["encrypt"]);
}).then(failAndFinishJSTest, function(result) {
    logError(result);
    return crypto.subtle.importKey("jwk", { "kty": "oct", "alg": "HS256", "use": "sig", "ext": false, "k": "ahjkn23387fgnsibf23qsvahjkn37387fgnsibf23qs" }, { name: "hmac", hash: {name: "sha-1"} }, nonExtractable, ["sign"]);
}).then(failAndFinishJSTest, function(result) {
    logError(result);
    // No key data.
    return crypto.subtle.importKey("jwk", { "kty": "oct", "alg": "HS256" }, hmac256, extractable, ["sign"]);
}).then(failAndFinishJSTest, function(result) {
    logError(result);
    return crypto.subtle.importKey("jwk", { "kty": "oct", "alg": "A128CBC" }, {name: "aes-cbc"}, extractable, ["encrypt"]);
}).then(failAndFinishJSTest, function(result) {
    logError(result);
    return crypto.subtle.importKey("jwk", { "kty": "oct", "alg": "A128CBC", "use": "enc", "ext": false, "k": "1234" }, {name: "aes-cbc"}, nonExtractable, ["encrypt"]);
}).then(failAndFinishJSTest, function(result) {
    logError(result);
    return crypto.subtle.importKey("jwk", { "kty": "oct", "alg": "A128CBC", "use": "enc", "ext": false, "k": "ahjkn23387fgnsibf23qsvahjkn37387fgnsibf23qs" }, {name: "aes-cbc"}, nonExtractable, ["encrypt"]);
}).then(failAndFinishJSTest, function(result) {
    logError(result);
    debug("\nimportKey() with 'k' member containing '+' and '/' characters...");
    return crypto.subtle.importKey("jwk", { "kty": "oct", "alg": "HS256", "use": "sig", "ext": false, "k": "ahjkn23387f+nsibf23qsvahjkn37387fgnsibf23qs" }, hmac256, nonExtractable, ["sign"]);
}).then(failAndFinishJSTest, function(result) {
    logError(result);
    // Incorrect data types.
    return crypto.subtle.importKey("jwk", { "kty": 1, "alg": "HS256", "use": "sig", "ext": false, "k": "ahjkn23387fgnsibf23qsvahjkn37387fgnsibf23qs" }, hmac256, nonExtractable, ["sign"]);
}).then(failAndFinishJSTest, function(result) {
    logError(result);
    return crypto.subtle.importKey("jwk", { "kty": "oct", "alg": 1, "use": "enc", "ext": false, "k": "ahjkn23387fgnsibf23qsvahjkn37387fgnsibf23qs" }, {name: "aes-cbc"}, nonExtractable, ["encrypt"]);
}).then(failAndFinishJSTest, function(result) {
    logError(result);
    return crypto.subtle.importKey("jwk", { "kty": "oct", "alg": "HS256", "use": 1, "ext": false, "k": "ahjkn23387fgnsibf23qsvahjkn37387fgnsibf23qs" }, hmac256, nonExtractable, ["sign"]);
}).then(failAndFinishJSTest, function(result) {
    logError(result);
    // ext is recognized as a boolean even though it is a string.
    return crypto.subtle.importKey("jwk", { "kty": "oct", "alg": "HS256", "use": "sig", "ext": "false", "k": "ahjkn23387fgnsibf23qsvahjkn37387fgnsibf23qs" }, hmac256, nonExtractable, ["sign"]);
}).then(function(result) {
    debug("Boolean JWK property passed as a string and worked");
    // k is recognized as a string even though it is a number.
    return crypto.subtle.importKey("jwk", { "kty": "oct", "alg": "HS256", "use": "sig", "ext": false, "k": 1258 }, hmac256, nonExtractable, ["sign"]);
}).then(function(result) {
    debug("String JWK property passed as a number and worked");
}).then(finishJSTest, failAndFinishJSTest);
</script>

</body>
</html>
