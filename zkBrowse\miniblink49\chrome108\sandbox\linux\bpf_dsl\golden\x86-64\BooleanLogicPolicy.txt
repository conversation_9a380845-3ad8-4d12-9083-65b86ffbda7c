  1) LOAD 4  // Architecture
  2) if A == 0xc000003e; then JMP 3 else JMP 34
  3) LOAD 0  // System call number
  4) if A & 0x40000000; then JMP 34 else JMP 5
  5) if A >= 0x36; then JMP 6 else JMP 7
  6) if A >= 0x401; then JMP 40 else JMP 39
  7) if A >= 0x35; then JMP 8 else JMP 39
  8) LOAD 20  // Argument 0 (MSB)
  9) if A == 0x0; then JMP 13 else JMP 10
 10) if A == 0xffffffff; then JMP 11 else JMP 34
 11) LOAD 16  // Argument 0 (LSB)
 12) if A & 0x80000000; then JMP 13 else JMP 34
 13) LOAD 16  // Argument 0 (LSB)
 14) if A == 0x1; then JMP 15 else JMP 37
 15) LOAD 28  // Argument 1 (MSB)
 16) if A == 0x0; then JMP 20 else JMP 17
 17) if A == 0xffffffff; then JMP 18 else JMP 34
 18) LOAD 24  // Argument 1 (LSB)
 19) if A & 0x80000000; then JMP 20 else JMP 34
 20) LOAD 24  // Argument 1 (LSB)
 21) if A == 0x1; then JMP 29 else JMP 22
 22) LOAD 28  // Argument 1 (MSB)
 23) if A == 0x0; then JMP 27 else JMP 24
 24) if A == 0xffffffff; then JMP 25 else JMP 34
 25) LOAD 24  // Argument 1 (LSB)
 26) if A & 0x80000000; then JMP 27 else JMP 34
 27) LOAD 24  // Argument 1 (LSB)
 28) if A == 0x2; then JMP 29 else JMP 37
 29) LOAD 36  // Argument 2 (MSB)
 30) if A == 0x0; then JMP 35 else JMP 31
 31) if A == 0xffffffff; then JMP 32 else JMP 34
 32) LOAD 32  // Argument 2 (LSB)
 33) if A & 0x80000000; then JMP 35 else JMP 34
 34) RET 0x0  // Kill
 35) LOAD 32  // Argument 2 (LSB)
 36) if A == 0x0; then JMP 38 else JMP 37
 37) RET 0x50016  // errno = 22
 38) RET 0x50001  // errno = 1
 39) RET 0x7fff0000  // Allowed
 40) RET 0x50026  // errno = 38
