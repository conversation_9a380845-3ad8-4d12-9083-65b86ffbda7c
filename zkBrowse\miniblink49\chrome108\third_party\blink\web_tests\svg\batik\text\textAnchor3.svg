<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 20010904//EN" 
"http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">

<!--

   Copyright 2001  The Apache Software Foundation 

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

-->
<!-- ====================================================================== -->
<!-- Tests text-anchor on multiple text chunks on single tspan element      -->
<!--                                                                        -->
<!-- <AUTHOR>                                               -->
<!-- @version $Id: textAnchor3.svg,v 1.4 2005/01/03 10:48:05 deweese Exp $  -->
<!-- ====================================================================== -->
<?xml-stylesheet type="text/css" href="../resources/test.css" ?>  

<svg width="450" height="500" viewBox="0 0 450 500"
     xmlns="http://www.w3.org/2000/svg" 
     xmlns:xlink="http://www.w3.org/1999/xlink" >

<text class="title" x="50%" y="30">text-anchor on a single &lt;tspan&gt;</text>

<g id="testContent">

<!-- ################################################################# -->
<!-- table                                                             -->
<!-- ################################################################# -->
<g>
	<rect x="75" y="50"  width="300" height="20" style="fill:black; stroke:black" />
	<rect x="75" y="70" width="300" height="40" style="fill:#eee; stroke:black" />
	<rect x="75" y="110" width="300" height="40" style="fill:white; stroke:black" />
	<rect x="75" y="150" width="300" height="40" style="fill:#eee; stroke:black" />
	<text x="90" y="64" style="fill:white;font-size:14px">x attributed tspan with extra text</text>
	<g style="stroke:lightsteelblue; fill:none">
		<line x1="150" y1="70" x2="150" y2="190" />
		<line x1="190" y1="70" x2="190" y2="190" />
		<line x1="230" y1="70" x2="230" y2="190" />
		<line x1="270" y1="70" x2="270" y2="190" />
		<line x1="310" y1="70" x2="310" y2="190" />
	</g>
</g>

<g transform="translate(0 150)">
	<rect x="75" y="50"  width="300" height="20" style="fill:black; stroke:black" />
	<rect x="75" y="70" width="300" height="40" style="fill:#eee; stroke:black" />
	<rect x="75" y="110" width="300" height="40" style="fill:white; stroke:black" />
	<rect x="75" y="150" width="300" height="40" style="fill:#eee; stroke:black" />
	<text x="90" y="64" style="fill:white;font-size:14px">x attribute for some chars</text>
	<g style="stroke:lightsteelblue; fill:none">
		<line x1="150" y1="70" x2="150" y2="190" />
		<line x1="190" y1="70" x2="190" y2="190" />
		<line x1="230" y1="70" x2="230" y2="190" />
	</g>
</g>

<g transform="translate(0 300)">
	<rect x="75" y="50"  width="300" height="20" style="fill:black; stroke:black" />
	<rect x="75" y="70" width="300" height="40" style="fill:#eee; stroke:black" />
	<rect x="75" y="110" width="300" height="40" style="fill:white; stroke:black" />
	<rect x="75" y="150" width="300" height="40" style="fill:#eee; stroke:black" />
	<text x="90" y="64" style="fill:white;font-size:14px">x &amp; y attributes with inheritance</text>
	<g style="stroke:lightsteelblue; fill:none">
		<line x1="150" y1="70" x2="150" y2="190" />
		<line x1="190" y1="70" x2="190" y2="190" />
		<line x1="230" y1="70" x2="230" y2="190" />
		<line x1="270" y1="70" x2="270" y2="190" />
	</g>
</g>

<!-- ################################################################# -->
<!-- x attribute defines new text chunks extra text after tspan        -->
<!-- ################################################################# -->

<text transform="translate(150  94)"><tspan x="0 40 80 120 160" y="0 0 0 0 10" style="text-anchor:start">Batik</tspan><tspan fill="crimson"> rules!</tspan></text>
<text transform="translate(150 134)"><tspan x="0 40 80 120 160" y="0 0 0 0 10" style="text-anchor:middle">Batik</tspan><tspan fill="crimson"> rules!</tspan></text>
<text transform="translate(150 174)"><tspan x="0 40 80 120 160" y="0 0 0 0 10" style="text-anchor:end">Batik</tspan><tspan fill="crimson"> rules!</tspan></text>

<!-- ################################################################# -->
<!-- x attribute defines new text chunks for only some chars           -->
<!-- ################################################################# -->
<text transform="translate(150 240)"><tspan x="0 40 80" style="text-anchor:start">Batik</tspan></text>
<text transform="translate(150 280)"><tspan x="0 40 80" style="text-anchor:middle">Batik</tspan></text>
<text transform="translate(150 320)"><tspan x="0 40 80" style="text-anchor:end">Batik</tspan></text>

<!-- ################################################################# -->
<!-- both x and y attributes define new text chunks                    -->
<!-- ################################################################# -->
<text transform="translate(150 390)" x="0 0 0 120"><tspan x="0 40 80" y="0 10 0 10" style="text-anchor:start">Batik</tspan><tspan fill="crimson"> rules!</tspan></text>
<text transform="translate(150 430)" x="0 0 0 120"><tspan x="0 40 80" y="0 10 0 10" style="text-anchor:middle">Batik</tspan><tspan fill="crimson"> rules!</tspan></text>
<text transform="translate(150 470)" x="0 0 0 120"><tspan x="0 40 80" y="0 10 0 10" style="text-anchor:end">Batik</tspan><tspan fill="crimson"> rules!</tspan></text>

</g>

</svg>
