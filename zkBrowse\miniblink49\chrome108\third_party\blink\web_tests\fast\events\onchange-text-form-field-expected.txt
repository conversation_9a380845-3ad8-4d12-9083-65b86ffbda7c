PASS simulateTextEntry(elements[0], '', BEFORE); is false
PASS simulateTextEntry(elements[0], 'fo', BEFORE); is true
PASS simulateTextEntry(elements[0], 'o'); is true
PASS simulateTextEntry(elements[0], 'foo', BEFORE); is false
PASS simulateTextEntry(elements[0], 'foo', BEFORE); is false
PASS simulateTextEntry(elements[0], ' '); is true
PASS simulateTextEntry(elements[0], 'foo bar', BEFORE); is true
PASS simulateTextEntry(elements[0], 'foo bar', BEFORE); is false
PASS setTextValue(elements[0], 'foo'); is false
PASS simulateTextEntry(elements[0], 'foo bar', BEFORE); is true
PASS simulateTextEntry(elements[1], '', BEFORE); is true
PASS simulateTextEntry(elements[1], 'fo', BEFORE); is true
PASS simulateTextEntry(elements[1], 'o'); is true
PASS simulateTextEntry(elements[1], 'foo', BEFORE); is false
PASS simulateTextEntry(elements[2], 'wee', AFTER); is false
PASS simulateTextEntry(elements[2], 'foo', BEFORE | AFTER); is false
PASS simulateTextEntry(elements[2], 'fo', BEFORE); is true
PASS simulateTextEntry(elements[2], 'o'); is true
PASS simulateTextEntry(elements[2], 'foo', BEFORE); is false
PASS simulateTextEntry(elements[3], 'foo', BEFORE); is false
PASS simulateTextEntry(elements[3], 'foo', BEFORE); is false
PASS setTextValue(elements[3], ''); is false
PASS simulateTextEntry(elements[3], 'fo', BEFORE); is true
PASS simulateTextEntry(elements[3], 'o'); is true
PASS simulateTextEntry(elements[3], 'foo', BEFORE); is false
PASS simulateTextEntry(elements[4], 'foo', AFTER); is false
PASS simulateTextEntry(elements[4], 'foo'); is true
PASS simulateTextEntry(elements[4], 'foo', BEFORE); is false
PASS simulateTextEntry(elements[4], 'foo', BEFORE | AFTER); is true
PASS simulateTextEntry(elements[4], '', AFTER); is false
PASS simulateTextEntry(elements[4], 'foo', AFTER); is false
PASS successfullyParsed is true

TEST COMPLETE

