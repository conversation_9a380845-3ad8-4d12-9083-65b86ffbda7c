Test exporting a public EC key.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".


Importing a SPKI key...

Exporting the key as JWK...
PASS exportedJWK.kty is "EC"
PASS exportedJWK.crv is "P-256"
PASS exportedJWK.x is "nLDPaTA9r8dh1ORoe07PA55tNKuWSvgIENjVWKSo1vc"
PASS exportedJWK.y is "LVEjOheIkgqG7gihlix576MX-3h54pfa0hRtuZX6HHg"
PASS exportedJWK.alg is undefined
PASS exportedJWK.ext is true
PASS exportedJWK.key_ops is ['verify']
PASS exportedJWK.use is undefined

Exporting the key as SPKI...
PASS: exportedSpki should be [3059301306072a8648ce3d020106082a8648ce3d030107034200049cb0cf69303dafc761d4e4687b4ecf039e6d34ab964af80810d8d558a4a8d6f72d51233a1788920a86ee08a1962c79efa317fb7879e297dad2146db995fa1c78] and was
PASS successfullyParsed is true

TEST COMPLETE

