<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

 <head>

  <title>CSS Reftest Reference</title>
  <link rel="author" title="Gérard Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" />

  <style type="text/css"><![CDATA[
  body
  {
  background: white url(support/square-teal.png) repeat-x center 20px;
  border: blue solid 3px;
  color: white;
  margin: 17px;
  padding: 17px;
  }

  div
  {
  background: green url(support/square-purple.png) center top no-repeat;
  border: lime solid;
  padding: 2em;
  }
  ]]></style>

 </head>

 <body>

  <div>There should be a horizontal line of teal tiles positioned between the top border of the blue box and the top border of the bright green box, and extending all the way past the sides of the blue box to the left and right edges of the page. One of those tiles must be centered horizontally within the page, so that it is exactly above the purple square that appears at the top of the inner green box.</div>

 </body>
</html>
