# Copyright 2022 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/chromeos/ui_mode.gni")

declare_args() {
  # Used to enable the Accessibility Service. Override this in
  # gn args on supported platforms (see below).
  enable_accessibility_service = false
}

declare_args() {
  # Shortcut for only allowing the service to run on Chrome OS Ash, Windows,
  # Mac, and Linux when the buildflag above is enabled.
  # This is exposed with the macro ENABLE_ACCESSIBILITY_SERVICE.
  enable_accessibility_service_internal =
      enable_accessibility_service &&
      (is_chromeos_ash || is_linux || is_mac || is_windows)
}
