<!DOCTYPE html>
<html>
<head>
<script src="../../resources/js-test.js"></script>
<script src="resources/common.js"></script>
</head>
<body>
<p id="description"></p>
<div id="console"></div>

<script>
description("Tests calls to unwrapKey() with bad inputs.");

jsTestIsAsync = true;

function importUnwrappingKey()
{
    var data = new Uint8Array(16);
    var extractable = true;
    var keyUsages = ['unwrapKey'];

    return crypto.subtle.importKey('raw', data, {name: 'AES-CBC'}, extractable, keyUsages);
}

importUnwrappingKey().then(function(result) {
    wrappedKey = new Uint8Array(100);
    unwrappingKey = result;
    unwrapAlgorithm = {name: 'aes-cbc', iv: new Uint8Array(16)};
    unwrappedKeyAlgorithm = unwrapAlgorithm;
    extractable = true;
    keyUsages = ['encrypt'];

    // Invalid wrappedKey
    return crypto.subtle.unwrapKey('raw', null, unwrappingKey, unwrapAlgorithm, unwrappedKeyAlgorithm, extractable, keyUsages);
}).then(failAndFinishJSTest, function(result) {
    logError(result);

    // Invalid unwrappingKey
    return crypto.subtle.unwrapKey('raw', wrappedKey, 'hi', unwrapAlgorithm, unwrappedKeyAlgorithm, extractable, keyUsages);
}).then(failAndFinishJSTest, function(result) {
    logError(result);

    // Invalid keyUsages (also, unwrappedKeyAlgorithm is set to null).
    return crypto.subtle.unwrapKey('raw', wrappedKey, unwrappingKey, unwrapAlgorithm, null, extractable, 9);
}).then(failAndFinishJSTest, function(result) {
    logError(result);

    // Invalid unwrapAlgorithm
    return crypto.subtle.unwrapKey('raw', wrappedKey, unwrappingKey, null, unwrappedKeyAlgorithm, extractable, keyUsages);
}).then(failAndFinishJSTest, function(result) {
    logError(result);

    // Invalid unwrappedKeyAlgorithm (specified but bad).
    return crypto.subtle.unwrapKey('raw', wrappedKey, unwrappingKey, unwrapAlgorithm, 3, extractable, keyUsages);
}).then(failAndFinishJSTest, function(result) {
    logError(result);

    // Invalid format
    return crypto.subtle.unwrapKey('bad-format', wrappedKey, unwrappingKey, unwrapAlgorithm, unwrappedKeyAlgorithm, extractable, keyUsages);
}).then(failAndFinishJSTest, function(result) {
    logError(result);

    // SHA-1 isn't a valid unwrapKey algorithm.
    return crypto.subtle.unwrapKey('raw', wrappedKey, unwrappingKey, {name: 'SHA-1'}, unwrappedKeyAlgorithm, extractable, keyUsages);
}).then(failAndFinishJSTest, function(result) {
    logError(result);

    // Mismatch between the unwrappingKey's algorithm and unwrapAlgorithm.
    aesCtrAlgorithm = {name: 'AES-CTR', counter: new Uint8Array(16), length: 0};
    return crypto.subtle.unwrapKey('raw', wrappedKey, unwrappingKey, aesCtrAlgorithm, unwrappedKeyAlgorithm, extractable, keyUsages);
}).then(failAndFinishJSTest, function(result) {
    logError(result);
}).then(finishJSTest, failAndFinishJSTest);

</script>

</body>
</html>
