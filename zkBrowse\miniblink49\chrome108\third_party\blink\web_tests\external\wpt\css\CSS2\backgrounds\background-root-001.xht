<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background on root element</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="G<PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-05-06 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <link rel="match" href="background-root-001-ref.xht" />

        <meta name="assert" content="Background of the root covers the entire canvas." />
        <style type="text/css">
            html
            {
                background: green;
                color: white;
            }
        </style>
    </head>
    <body>
        <p>Test passes if the background of this entire page is green.</p>
    </body>
</html>