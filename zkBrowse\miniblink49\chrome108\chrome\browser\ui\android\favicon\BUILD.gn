# Copyright 2020 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/android/rules.gni")

android_library("java") {
  sources = [
    "java/src/org/chromium/chrome/browser/ui/favicon/FaviconHelper.java",
    "java/src/org/chromium/chrome/browser/ui/favicon/FaviconUtils.java",
  ]

  deps = [
    ":java_resources",
    "//base:base_java",
    "//base:jni_java",
    "//build/android:build_java",
    "//chrome/browser/profiles/android:java",
    "//chrome/browser/util:java",
    "//components/browser_ui/util/android:java",
    "//components/browser_ui/widget/android:java",
    "//components/embedder_support/android:util_java",
    "//components/url_formatter/android:url_formatter_java",
    "//content/public/android:content_java",
    "//third_party/androidx:androidx_annotation_annotation_java",
    "//third_party/androidx:androidx_core_core_java",
    "//ui/android:ui_java",
    "//url:gurl_java",
  ]

  resources_package = "org.chromium.chrome.browser.ui.favicon"

  annotation_processor_deps = [ "//base/android/jni_generator:jni_processor" ]
}

generate_jni("jni_headers") {
  sources =
      [ "java/src/org/chromium/chrome/browser/ui/favicon/FaviconHelper.java" ]
}

android_resources("java_resources") {
  sources = [
    "java/res/drawable-hdpi/chromelogo16.png",
    "java/res/drawable-hdpi/default_favicon.png",
    "java/res/drawable-mdpi/chromelogo16.png",
    "java/res/drawable-mdpi/default_favicon.png",
    "java/res/drawable-xhdpi/chromelogo16.png",
    "java/res/drawable-xhdpi/default_favicon.png",
    "java/res/drawable-xxhdpi/chromelogo16.png",
    "java/res/drawable-xxhdpi/default_favicon.png",
    "java/res/drawable-xxxhdpi/chromelogo16.png",
    "java/res/drawable-xxxhdpi/default_favicon.png",
  ]

  deps = [
    "//components/browser_ui/styles/android:java_resources",
    "//ui/android:ui_java_resources",
  ]
}
