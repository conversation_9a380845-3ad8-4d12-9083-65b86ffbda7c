<html>
  <style>
    @font-face {
      font-family: test;
      src: url(../custom/resources/ABCFont.svg) format("svg");
    }
  </style>
  <style>
    body {
      font-family: test;
      text-rendering: optimizelegibility;
    }
  </style>
  <script>
    window.onload = function() {
      if (window.testRunner) {
        testRunner.waitUntilDone();
        testRunner.dumpAsText();
      }

      setTimeout(function () {
        if (window.eventSender) {
          eventSender.mouseMoveTo(10, 10);
          eventSender.mouseDown();
          eventSender.mouseMoveTo(100, 10);
          eventSender.mouseUp();
	    };
        if (window.testRunner) {
          testRunner.notifyDone();
        }
	  }, 10);
    }
  </script>
  <body>
    <div id="output_div">
      <p id="text_in_svg_font">This test passes if selecting the text does not crash.</p>
    </div>
  </body>
</html>