<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

 <head>

  <title>CSS Writing Modes Test: position absolute and 'vertical-lr' - overconstrained values with 'direction: ltr' in initial containing block</title>

  <link rel="author" title="Gérard Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" />
  <link rel="help" href="http://www.w3.org/TR/css-writing-modes-3/#vertical-layout" title="7.1 Principles of Layout in Vertical Writing Modes" />
  <link rel="match" href="abs-pos-non-replaced-icb-vrl-008-ref.xht" />

  <meta content="This test checks that when the initial containing block's writing-mode is 'horizontal-tb' and its 'direction' is 'ltr', then an absolutely positioned box (with overconstrained 'left', 'width' and 'right' values) whose containing block is the initial containing block must ignore the value for 'right' and solve for that value. Whether such absolutely positioned box's 'writing-mode' is vertical or not is irrelevant." name="assert" />

  <style type="text/css"><![CDATA[
  object#overlapping-green
    {
      height: 116px;
      width: 500px;
      vertical-align: top;
    }

  div#red-overlapped-reference
    {
      background-color: red;
      bottom: 116px;
      height: 100px;
      left: 300px;
      position: relative;
      width: 100px;
      z-index: -1;
    }
  ]]></style>
 </head>

 <body>

  <p>Test passes if there is a filled green square and <strong>no red</strong>.</p>

  <div><object data="support/embedded-doc-abs-pos-non-replaced-icb-vlr-031.html" type="text/html" id="overlapping-green">This test requires a browser with capability to embed an HTML document thanks to the HTML &lt;object&gt; element.</object></div>

  <div id="red-overlapped-reference"></div>

 </body>
</html>