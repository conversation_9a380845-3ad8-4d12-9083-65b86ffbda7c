# Copyright 2014 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/android/config.gni")
import("//build/config/android/rules.gni")

android_library("battery_monitor_java") {
  visibility = [ "//services/device:*" ]
  sources = [
    "java/src/org/chromium/device/battery/BatteryMonitorFactory.java",
    "java/src/org/chromium/device/battery/BatteryMonitorImpl.java",
    "java/src/org/chromium/device/battery/BatteryStatusManager.java",
  ]

  deps = [
    "//base:base_java",
    "//mojo/public/java:bindings_java",
    "//mojo/public/java:system_java",
    "//services/device/public/mojom:mojom_java",
    "//services/service_manager/public/java:service_manager_java",
    "//third_party/android_deps:com_google_code_findbugs_jsr305_java",
    "//third_party/androidx:androidx_annotation_annotation_java",
  ]
}
