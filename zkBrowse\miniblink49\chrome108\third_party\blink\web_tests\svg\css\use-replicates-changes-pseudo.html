<!DOCTYPE html>
<script src="../../resources/run-after-layout-and-paint.js"></script>
<style>
#inputValid:valid + svg #r {
    fill: green;
}
#r {
  fill: red;
}
</style>
<input id="inputValid" required></input>
<svg width="100" height="100">
  <use xlink:href="#g" transform="translate(50,50)"></use>
  <g id="g">
    <rect id="r" x="0" y="0" width="50" height="50"></rect>
  </g>
</svg>
<script>
runAfterLayoutAndPaint(function() {
  inputValid.removeAttribute("required");
}, true);
</script>
