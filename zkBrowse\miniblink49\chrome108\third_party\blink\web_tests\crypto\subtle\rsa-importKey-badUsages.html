<!DOCTYPE html>
<html>
<head>
<script src="../../resources/js-test.js"></script>
<script src="resources/common.js"></script>
<script src="resources/keys.js"></script>
</head>
<body>
<p id="description"></p>
<div id="console"></div>

<script>
description("Tests that importing keys with invalid usages should fail with SyntaxError.");

jsTestIsAsync = true;

function importPkcs8ForDecryption()
{
    var keyData = hexStringToUint8Array(kKeyData.rsa1.pkcs8);
    var usages = ['decrypt'];
    var extractable = false;
    var algorithm = {name: 'RSASSA-PKCS1-v1_5', hash: {name: 'sha-1'}};

    return crypto.subtle.importKey('pkcs8', keyData, algorithm, extractable, usages);
}

function importSpkiForEncryption()
{
    var keyData = hexStringToUint8Array(kKeyData.rsa1.spki);
    var usages = ['encrypt'];
    var extractable = false;
    var algorithm = {name: 'RSASSA-PKCS1-v1_5', hash: {name: 'sha-1'}};

    return crypto.subtle.importKey('spki', keyData, algorithm, extractable, usages);
}

function importJwkPublicKeyForSigning()
{
    var keyData = kKeyData.jwkRSAPublicKeyJSON;
    var usages = ['sign'];
    var extractable = false;
    var algorithm = {name: 'RSASSA-PKCS1-v1_5', hash: {name: "sha-1"}};

    return crypto.subtle.importKey('jwk', keyData, algorithm, extractable, usages);
}

function importJwkPrivateKeyForEncryption()
{
    var keyData = kKeyData.jwkRSAPrivateKeyJSON;
    var usages = ['encrypt'];
    var extractable = false;
    var algorithm = {name: 'RSASSA-PKCS1-v1_5', hash: {name: "sha-1"}};

    return crypto.subtle.importKey('jwk', keyData, algorithm, extractable, usages);
}

importPkcs8ForDecryption().then(failAndFinishJSTest, function(result) {
    logError(result);

    return importSpkiForEncryption();
}).then(failAndFinishJSTest, function(result) {
    logError(result);

    return importJwkPublicKeyForSigning();
}).then(failAndFinishJSTest, function(result) {
    logError(result);

    return importJwkPrivateKeyForEncryption();
}).then(failAndFinishJSTest, function(result) {
    logError(result);
}).then(finishJSTest, failAndFinishJSTest);
</script>

</body>
</html>
