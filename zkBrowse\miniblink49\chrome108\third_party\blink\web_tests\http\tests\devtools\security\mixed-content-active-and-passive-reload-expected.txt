Tests that the active and passive mixed content explanations prompt the user to refresh when there are no recorded requests, and link to the network panel when there are recorded requests.


Before Refresh --------------
<DIV class=security-explanation security-explanation-insecure >
    <DIV class=security-property security-property-insecure >
    </DIV>
    <DIV class=security-explanation-text >
        <DIV class=security-explanation-title >
            <SPAN >
Resources - 
            </SPAN>
            <SPAN class=security-explanation-title-insecure >
active mixed content
            </SPAN>
        </DIV>
        <DIV >
You have recently allowed non-secure content (such as scripts or iframes) to run on this site.
        </DIV>
        <DIV class=security-mixed-content >
Reload the page to record requests for HTTP resources.
        </DIV>
    </DIV>
</DIV>
<DIV class=security-explanation security-explanation-neutral >
    <DIV class=security-property security-property-neutral >
    </DIV>
    <DIV class=security-explanation-text >
        <DIV class=security-explanation-title >
            <SPAN >
Resources - 
            </SPAN>
            <SPAN class=security-explanation-title-neutral >
mixed content
            </SPAN>
        </DIV>
        <DIV >
This page includes HTTP resources.
        </DIV>
        <DIV class=security-mixed-content >
Reload the page to record requests for HTTP resources.
        </DIV>
    </DIV>
</DIV>

Refresh --------------
<DIV class=security-explanation security-explanation-insecure >
    <DIV class=security-property security-property-insecure >
    </DIV>
    <DIV class=security-explanation-text >
        <DIV class=security-explanation-title >
            <SPAN >
Resources - 
            </SPAN>
            <SPAN class=security-explanation-title-insecure >
active mixed content
            </SPAN>
        </DIV>
        <DIV >
You have recently allowed non-secure content (such as scripts or iframes) to run on this site.
        </DIV>
        <DIV class=security-mixed-content devtools-link role=link tabindex=0 >
View 1 request in Network Panel
        </DIV>
    </DIV>
</DIV>
<DIV class=security-explanation security-explanation-neutral >
    <DIV class=security-property security-property-neutral >
    </DIV>
    <DIV class=security-explanation-text >
        <DIV class=security-explanation-title >
            <SPAN >
Resources - 
            </SPAN>
            <SPAN class=security-explanation-title-neutral >
mixed content
            </SPAN>
        </DIV>
        <DIV >
This page includes HTTP resources.
        </DIV>
        <DIV class=security-mixed-content devtools-link role=link tabindex=0 >
View 1 request in Network Panel
        </DIV>
    </DIV>
</DIV>

