<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 1.6 Contextual selectors</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
P {color: navy; font-family: serif;}
HTML BODY TABLE P {color: purple; font-family: sans-serif;}
EM, UL LI LI {color: green;}
</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>P {color: navy; font-family: serif;}
HTML BODY TABLE P {color: purple; font-family: sans-serif;}
EM, UL LI LI {color: green;}

</PRE>
<HR>
<P>
This sentence should be navy serif in the first half of the page, but purple and sans-serif in the table.
</P>
<P>
This sentence should be normal for its section, except for the last word, which should be <EM>green</EM>.
</P>
<UL>
<LI><EM>Hello.</EM>  The first "hello" should be green, but this part should be black.
<UL>
<LI>This should be green.
</UL>
</UL>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P>
This sentence should be navy serif in the first half of the page, but purple and sans-serif in the table.
</P>
<P>
This sentence should be normal for its section, except for the last word, which should be <EM>green</EM>.
</P>
<UL>
<LI><EM>Hello.</EM>  The first "hello" should be green, but this part should be black.
<UL>
<LI>This should be green.
</UL>
</UL>
</TD></TR></TABLE></BODY>
</HTML>
