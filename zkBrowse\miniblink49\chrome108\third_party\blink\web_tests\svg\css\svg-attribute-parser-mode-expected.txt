Test strict color parsing on SVG presentation attributes.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".

PASS document.defaultView.getComputedStyle(rect, null).fill is "rgb(0, 0, 0)"
PASS document.defaultView.getComputedStyle(rect, null).fill is "rgb(0, 128, 0)"
PASS document.defaultView.getComputedStyle(rect, null).fill is "rgb(0, 0, 0)"
PASS document.defaultView.getComputedStyle(rect, null).fill is "rgb(0, 128, 0)"
PASS document.defaultView.getComputedStyle(rect, null).fill is "rgb(0, 0, 0)"
PASS document.defaultView.getComputedStyle(rect, null).fill is "rgb(0, 128, 0)"
PASS document.defaultView.getComputedStyle(rect, null).fill is "rgb(0, 0, 0)"
PASS document.defaultView.getComputedStyle(rect, null).fill is "rgb(0, 128, 0)"
PASS document.defaultView.getComputedStyle(rect, null).fill is "rgb(0, 0, 0)"
PASS document.defaultView.getComputedStyle(rect, null).fill is "rgb(0, 128, 0)"
PASS document.defaultView.getComputedStyle(rect, null).fill is "rgb(0, 0, 0)"
PASS document.defaultView.getComputedStyle(rect, null).fill is "rgb(0, 128, 0)"
PASS document.defaultView.getComputedStyle(rect, null).fill is "url(\"#reference\")"
PASS document.defaultView.getComputedStyle(rect, null).fill is "rgb(0, 128, 0)"
PASS document.defaultView.getComputedStyle(rect, null).fill is "url(\"#reference\") rgb(0, 128, 0)"
PASS document.defaultView.getComputedStyle(rect, null).fill is "rgb(0, 128, 0)"
PASS document.defaultView.getComputedStyle(rect, null).stroke is "none"
PASS document.defaultView.getComputedStyle(rect, null).stroke is "rgb(0, 128, 0)"
PASS document.defaultView.getComputedStyle(rect, null).stroke is "none"
PASS document.defaultView.getComputedStyle(rect, null).stroke is "rgb(0, 128, 0)"
PASS document.defaultView.getComputedStyle(rect, null).stroke is "none"
PASS document.defaultView.getComputedStyle(rect, null).stroke is "rgb(0, 128, 0)"
PASS document.defaultView.getComputedStyle(rect, null).stroke is "none"
PASS document.defaultView.getComputedStyle(rect, null).stroke is "rgb(0, 128, 0)"
PASS document.defaultView.getComputedStyle(rect, null).stroke is "none"
PASS document.defaultView.getComputedStyle(rect, null).stroke is "rgb(0, 128, 0)"
PASS document.defaultView.getComputedStyle(rect, null).stroke is "none"
PASS document.defaultView.getComputedStyle(rect, null).stroke is "rgb(0, 128, 0)"
PASS document.defaultView.getComputedStyle(rect, null).stroke is "url(\"#reference\")"
PASS document.defaultView.getComputedStyle(rect, null).stroke is "rgb(0, 128, 0)"
PASS document.defaultView.getComputedStyle(rect, null).stroke is "url(\"#reference\") rgb(0, 128, 0)"
PASS document.defaultView.getComputedStyle(rect, null).stroke is "rgb(0, 128, 0)"
PASS document.defaultView.getComputedStyle(rect, null).color is "rgb(0, 0, 0)"
PASS document.defaultView.getComputedStyle(rect, null).color is "rgb(0, 128, 0)"
PASS document.defaultView.getComputedStyle(rect, null).color is "rgb(0, 0, 0)"
PASS document.defaultView.getComputedStyle(rect, null).color is "rgb(0, 128, 0)"
PASS document.defaultView.getComputedStyle(rect, null).color is "rgb(0, 0, 0)"
PASS document.defaultView.getComputedStyle(rect, null).color is "rgb(0, 128, 0)"
PASS document.defaultView.getComputedStyle(rect, null).color is "rgb(0, 0, 0)"
PASS document.defaultView.getComputedStyle(rect, null).color is "rgb(0, 128, 0)"
PASS document.defaultView.getComputedStyle(rect, null).color is "rgb(0, 0, 0)"
PASS document.defaultView.getComputedStyle(rect, null).color is "rgb(0, 128, 0)"
PASS document.defaultView.getComputedStyle(rect, null).color is "rgb(0, 0, 0)"
PASS document.defaultView.getComputedStyle(rect, null).color is "rgb(0, 128, 0)"
PASS document.defaultView.getComputedStyle(rect, null).stopColor is "rgb(0, 0, 0)"
PASS document.defaultView.getComputedStyle(rect, null).stopColor is "rgb(0, 128, 0)"
PASS document.defaultView.getComputedStyle(rect, null).stopColor is "rgb(0, 0, 0)"
PASS document.defaultView.getComputedStyle(rect, null).stopColor is "rgb(0, 128, 0)"
PASS document.defaultView.getComputedStyle(rect, null).stopColor is "rgb(0, 0, 0)"
PASS document.defaultView.getComputedStyle(rect, null).stopColor is "rgb(0, 128, 0)"
PASS document.defaultView.getComputedStyle(rect, null).stopColor is "rgb(0, 0, 0)"
PASS document.defaultView.getComputedStyle(rect, null).stopColor is "rgb(0, 128, 0)"
PASS document.defaultView.getComputedStyle(rect, null).stopColor is "rgb(0, 0, 0)"
PASS document.defaultView.getComputedStyle(rect, null).stopColor is "rgb(0, 128, 0)"
PASS document.defaultView.getComputedStyle(rect, null).stopColor is "rgb(0, 0, 0)"
PASS document.defaultView.getComputedStyle(rect, null).stopColor is "rgb(0, 128, 0)"
PASS document.defaultView.getComputedStyle(rect, null).floodColor is "rgb(0, 0, 0)"
PASS document.defaultView.getComputedStyle(rect, null).floodColor is "rgb(0, 128, 0)"
PASS document.defaultView.getComputedStyle(rect, null).floodColor is "rgb(0, 0, 0)"
PASS document.defaultView.getComputedStyle(rect, null).floodColor is "rgb(0, 128, 0)"
PASS document.defaultView.getComputedStyle(rect, null).floodColor is "rgb(0, 0, 0)"
PASS document.defaultView.getComputedStyle(rect, null).floodColor is "rgb(0, 128, 0)"
PASS document.defaultView.getComputedStyle(rect, null).floodColor is "rgb(0, 0, 0)"
PASS document.defaultView.getComputedStyle(rect, null).floodColor is "rgb(0, 128, 0)"
PASS document.defaultView.getComputedStyle(rect, null).floodColor is "rgb(0, 0, 0)"
PASS document.defaultView.getComputedStyle(rect, null).floodColor is "rgb(0, 128, 0)"
PASS document.defaultView.getComputedStyle(rect, null).floodColor is "rgb(0, 0, 0)"
PASS document.defaultView.getComputedStyle(rect, null).floodColor is "rgb(0, 128, 0)"
PASS successfullyParsed is true

TEST COMPLETE

