// Copyright 2016 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

module data_decoder.mojom;

import "mojo/public/mojom/base/big_buffer.mojom";
import "mojo/public/mojom/base/time.mojom";
import "skia/public/mojom/bitmap.mojom";
import "ui/gfx/geometry/mojom/geometry.mojom";

enum ImageCodec {
  kDefault,
  // Only valid for Ash. Backed by libpng so requires that image is a PNG.
  kPng,
};

struct AnimationFrame {
  // TODO(https://crbug.com/838706): Cnonsider replacing individual Bitmap
  // structs with a new type that uses a single BigBuffer for all frame data.
  skia.mojom.InlineBitmap bitmap;
  mojo_base.mojom.TimeDelta duration;
};

interface ImageDecoder {
  // Decodes image data to a raw skia bitmap.
  //
  // If the total size of the decoded image data in bytes exceeds
  // |max_size_in_bytes| and |shrink_to_fit| is true, the image is halved
  // successively until its total size no longer exceeds |max_size_in_bytes|.
  //
  // If the total size of the decoded image data in bytes exceeds
  // |max_size_in_bytes| and |shrink_to_fit| is false, this is treated as a
  // decoding failure and the |decoded_image| response is null.
  DecodeImage(mojo_base.mojom.BigBuffer encoded_data, ImageCodec codec,
              bool shrink_to_fit, int64 max_size_in_bytes,
              gfx.mojom.Size desired_image_frame_size)
      => (mojo_base.mojom.TimeDelta decoding_duration,
          skia.mojom.BitmapN32? decoded_image);

  // Decodes the image in |encoded_data|. This will return all frames in the
  // image and assumes it is an animation (instead of say, a multi-sized image).
  //
  // If the total size of the decoded image data in bytes exceeds
  // |max_size_in_bytes| and |shrink_to_fit| is true, the image is halved
  // successively until its total size no longer exceeds |max_size_in_bytes|.
  //
  // If the total size of the decoded image data in bytes exceeds
  // |max_size_in_bytes| and |shrink_to_fit| is false, this is treated as a
  // decoding failure and the |decoded_image| response is null.
  DecodeAnimation(mojo_base.mojom.BigBuffer encoded_data, bool shrink_to_fit,
                  int64 max_size_in_bytes)
      => (array<AnimationFrame> decoded_image);
};
