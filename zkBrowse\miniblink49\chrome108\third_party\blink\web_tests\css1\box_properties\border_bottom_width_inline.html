<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.5.13 border-bottom-width</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
.one {border-bottom-width: 25px; border-style: solid;}
.two {border-bottom-width: thin; border-style: solid;}
.three {border-bottom-width: 25px;}</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.one {border-bottom-width: 25px; border-style: solid;}
.two {border-bottom-width: thin; border-style: solid;}
.three {border-bottom-width: 25px;}
</PRE>
<HR>
<P class="one">
This element has a class of <TT>one</TT>.  However, it contains an <SPAN class="two">inline element of class <TT>two</TT></SPAN>, which should result in a thin solid border on the bottom side of each box in the inline element (and the UA's default border on the other three sides).  There is also an <SPAN class="three">inline element of class <TT>three</TT></SPAN>, which should have no bottom border width or visible border because no border style was set.
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P class="one">
This element has a class of <TT>one</TT>.  However, it contains an <SPAN class="two">inline element of class <TT>two</TT></SPAN>, which should result in a thin solid border on the bottom side of each box in the inline element (and the UA's default border on the other three sides).  There is also an <SPAN class="three">inline element of class <TT>three</TT></SPAN>, which should have no bottom border width or visible border because no border style was set.
</P>
</TD></TR></TABLE></BODY>
</HTML>
