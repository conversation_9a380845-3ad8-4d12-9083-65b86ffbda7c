<!DOCTYPE html>
<title>IDBCursor.continue() - index - iterate to the next record</title>
<link rel="author" title="Microsoft" href="http://www.microsoft.com">
<link rel=help href="http://dvcs.w3.org/hg/IndexedDB/raw-file/tip/Overview.html#widl-IDBCursor-continue-void-any-key">
<link rel=assert title="Otherwise this method runs the steps for asynchronously executing a request.">
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<script src="resources/support.js"></script>

<script>
    var db,
      count = 0,
      t = async_test(),
      records = [ { pKey: "primaryKey_0",   iKey: "indexKey_0" },
                  { pKey: "primaryKey_1",   iKey: "indexKey_1" },
                  { pKey: "primaryKey_1-2", iKey: "indexKey_1" } ];

    var open_rq = createdb(t);
    open_rq.onupgradeneeded = function(e) {
        db = e.target.result;
        var objStore = db.createObjectStore("test", { keyPath:"pKey" });

        objStore.createIndex("index", "iKey");

        for (var i = 0; i < records.length; i++)
            objStore.add(records[i]);
    };

    open_rq.onsuccess = function(e) {
        var cursor_rq = db.transaction("test")
                          .objectStore("test")
                          .index("index")
                          .openCursor();

        cursor_rq.onsuccess = t.step_func(function(e) {
            var cursor = e.target.result;
            if (!cursor) {
                assert_equals(count, records.length, "cursor run count");
                t.done();
            }

            var record = cursor.value;
            assert_equals(record.pKey, records[count].pKey, "primary key");
            assert_equals(record.iKey, records[count].iKey, "index key");

            cursor.continue();
            count++;
        });
    };
</script>

<div id="log"></div>
