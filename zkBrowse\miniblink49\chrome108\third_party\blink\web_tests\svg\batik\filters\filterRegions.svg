<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN"
"http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">

<!--

   Copyright 2001  The Apache Software Foundation 

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

-->
<!-- ========================================================================= -->
<!-- This tests validates the filter region processing for the top level       -->
<!-- filter nodes and filter primitives. For each test, a filter is invoked    -->
<!-- to render in a region specified by the filter region. In addition, a      -->
<!-- control rectangle, bounding the expected filter region is rendered on top -->
<!-- of the filter.                                                            -->
<!--                                                                           -->
<!-- <AUTHOR>                                         -->
<!-- @version $Id: filterRegions.svg,v 1.5 2004/08/18 07:11:50 vhardy Exp $    -->
<!-- ========================================================================= -->

<?xml-stylesheet type="text/css" href="../resources/test.css" ?>

<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="body" width="450" height="500" viewBox="0 0 450 500">
<title>FilterRegions test</title>

    <text x="225" y="40" class="title">
        Filter Regions
     </text>

    <style type="text/css">
        <![CDATA[
            .filterRegionControl {
                fill: none;
                stroke: black;
                stroke-opacity: .5;
                stroke-width: 1;
                
            }

            .filterRegionControlLabel {
                fill: black;
                font-family: Verdana;
                font-size: 10px;
                text-anchor: middle;
            }
        ]]>
    </style>

    <defs>
        <!-- ======================================================== -->
        <!-- ======================================================== -->
        <!-- userSpaceOnUser for filter chain and for filterPrimitive -->
        <!-- ======================================================== -->
        <!-- ======================================================== -->

        <!-- ======================================================== -->
        <!-- filterRegion_0                                           -->
        <!--   chain region: [objectBoundingBox], undefined           -->
        <!--   flood region: [userSpaceOnUse], undefined              -->
        <!--                                                          -->
        <!-- Expected fill region: (0, 0, 450, 450) in user space     -->
        <!-- ======================================================== -->
        <filter id="filterRegion_0" >
            <feFlood style="flood-color:red;" />
        </filter>

        <!-- ======================================================== -->
        <!-- filterRegion_1                                           -->
        <!--   chain region: userSpaceOnUse, (20, 30, 50, 40)         -->
        <!--   flood region: userSpaceOnUse, undefined                -->
        <!--                                                          -->
        <!-- Expected fill region: (20, 30, 50, 40) in user space     -->
        <!-- ======================================================== -->
        <filter id="filterRegion_1" filterUnits="userSpaceOnUse" 
                                    primitiveUnits="userSpaceOnUse"
                                    x="20" y="30" width="50" height="40">
            <feFlood style="flood-color:red" />
        </filter>

        <!-- ======================================================== -->
        <!-- filterRegion_2                                           -->
        <!--   chain region: userSpaceOnUse, (20, 30, 50, 40)         -->
        <!--   flood region: userSpaceOnUse, (25, 35, 50, 40)         -->
        <!--                                                          -->
        <!-- Expected fill region: (25, 35, 45, 35) in user space     -->
        <!-- ======================================================== -->
        <filter id="filterRegion_2" filterUnits="userSpaceOnUse" 
                                    primitiveUnits="userSpaceOnUse"
                                    x="20" y="30" width="50" height="40">
            <feFlood style="flood-color:red" x="25" y="35" width="50" height="40"/>
        </filter>

        <!-- ======================================================== -->
        <!-- filterRegion_3                                           -->
        <!--   chain region: userSpaceOnUse, (20, 30, 50, 40)         -->
        <!--   flood region: userSpaceOnUse, (XX, XX, XX, 20)         -->
        <!--                                                          -->
        <!-- Expected fill region: (20, 30, 50, 20) in user space     -->
        <!-- ======================================================== -->
        <filter id="filterRegion_3" filterUnits="userSpaceOnUse" 
                                    primitiveUnits="userSpaceOnUse"
                                    x="20" y="30" width="50" height="40">
            <feFlood style="flood-color:red" height="20"/>
        </filter>

        <!-- ======================================================== -->
        <!-- filterRegion_4                                           -->
        <!--   chain region: userSpaceOnUse, (20, 30, 50, 40)         -->
        <!--   flood region: userSpaceOnUse, (XX, XX, 25, XX)         -->
        <!--                                                          -->
        <!-- Expected fill region: (20, 30, 25, 40) in user space     -->
        <!-- ======================================================== -->
        <filter id="filterRegion_4" filterUnits="userSpaceOnUse" 
                                    primitiveUnits="userSpaceOnUse"
                                    x="20" y="30" width="50" height="40">
            <feFlood style="flood-color:red" width="25"/>
        </filter>

        <!-- ======================================================== -->
        <!-- filterRegion_5                                           -->
        <!--   chain region: userSpaceOnUse, (20, 30, 50, 40)         -->
        <!--   flood region: userSpaceOnUse, (XX, 10, XX, XX)         -->
        <!--                                                          -->
        <!-- Expected fill region: (20, 30, 50, 20) in user space     -->
        <!-- ======================================================== -->
        <filter id="filterRegion_5" filterUnits="userSpaceOnUse" 
                                    primitiveUnits="userSpaceOnUse"
                                    x="20" y="30" width="50" height="40">
            <feFlood style="flood-color:red" y="10"/>
        </filter>
        
        <!-- ======================================================== -->
        <!-- filterRegion_6                                           -->
        <!--   chain region: userSpaceOnUse, (20, 30, 50, 40)         -->
        <!--   flood region: userSpaceOnUse, (10, XX, XX, XX)         -->
        <!--                                                          -->
        <!-- Expected fill region: (20, 30, 40, 40) in user space     -->
        <!-- ======================================================== -->
        <filter id="filterRegion_6" filterUnits="userSpaceOnUse" 
                                    primitiveUnits="userSpaceOnUse"
                                    x="20" y="30" width="50" height="40">
            <feFlood style="flood-color:red" x="10"/>
        </filter>
        

        <!-- ======================================================== -->
        <!-- ======================================================== -->
        <!-- userSpaceOnUse for filter chain and object bounding box  -->
        <!-- for filter primitive                                     -->
        <!-- ======================================================== -->
        <!-- ======================================================== -->

        <!-- ======================================================== -->
        <!-- filterRegion_2_0                                         -->
        <!--   chain region: [objectBoundingBox], undefined           -->
        <!--   flood region: undefined                                -->
        <!--                                                          -->
        <!-- Expected fill region: (-10%, -10%, 120%, 120%) in user   -->
        <!-- space                                                    -->
        <!-- ======================================================== -->
        <filter id="filterRegion_2_0" primitiveUnits="objectBoundingBox">
            <feFlood style="flood-color:red;" />
        </filter>

        <!-- ======================================================== -->
        <!-- filterRegion_2_1                                         -->
        <!--   chain region: userSpaceOnUse, undefined                -->
        <!--   flood region: objectBoundingBox, (0, 0, 1, 1)          -->
        <!--                                                          -->
        <!-- Expected fill region: (0, 0, 1, 1) in object bbox space  -->
        <!-- ======================================================== -->
        <filter id="filterRegion_2_1" filterUnits="userSpaceOnUse" 
                                    primitiveUnits="objectBoundingBox">
            <feFlood style="flood-color:red;" x="0" y="0" width="1" height="1"/>
        </filter>

        <!-- ======================================================== -->
        <!-- filterRegion_2_2                                         -->
        <!--   chain region: userSpaceOnUse, undefined                -->
        <!--   flood region: objectBoundingBox, (0%, 0%, 100%, 100%)  -->
        <!--                                                          -->
        <!-- Expected fill region: (0, 0, 1, 1) in object bbox space  -->
        <!-- ======================================================== -->
        <filter id="filterRegion_2_2" filterUnits="userSpaceOnUse" 
                                    primitiveUnits="objectBoundingBox">
            <feFlood style="flood-color:red" x="0%" y="0%" width="100%" height="100%"/>
        </filter>

        <!-- ======================================================== -->
        <!-- filterRegion_2_3                                         -->
        <!--   chain region: userSpaceOnUse, undefined                -->
        <!--   flood region: objectBoundingBox, (.25, 50%, .5, 25%)   -->
        <!--                                                          -->
        <!-- Expected fill region: (.25, .5, .5, .25) in object bbox  -->
        <!-- space                                                    -->
        <!-- ======================================================== -->
       
        <filter id="filterRegion_2_3" filterUnits="userSpaceOnUse" 
                                    primitiveUnits="objectBoundingBox">
            <feFlood style="flood-color:red" x=".25" y="50%" width=".5" height="25%"/>
        </filter>

        <!-- ======================================================== -->
        <!-- filterRegion_2_4                                         -->
        <!--   chain region: userSpaceOnUse, undefined                -->
        <!--   flood region: objectBoundingBox, (25%, .5, 50%, .25)   -->
        <!--                                                          -->
        <!-- Expected fill region: (.25, .5, .5, .25) in object bbox  -->
        <!-- space                                                    -->
        <!-- ======================================================== -->
        <filter id="filterRegion_2_4" filterUnits="userSpaceOnUse" 
                                    primitiveUnits="objectBoundingBox">
            <feFlood style="flood-color:red" x="25%" y=".5" width="50%" height=".25"/>
        </filter>

        <!-- ======================================================== -->
        <!-- filterRegion_2_5                                         -->
        <!--   chain region: userSpaceOnUse, (20, 30, 50, 40)         -->
        <!--   flood region: objectBoundingBox, (XX, .5, XX, .25)     -->
        <!--                                                          -->
        <!-- Expected fill region: (XX, .5, XX, .25) in object bbox   -->
        <!--                       (20, XX, 50, XX) in user space     -->
        <!-- ======================================================== -->
        <filter id="filterRegion_2_5" filterUnits="userSpaceOnUse" 
                                    primitiveUnits="objectBoundingBox" 
                                    x="20" y="30" width="50" height="40">
            <feFlood style="flood-color:red" y=".5" height=".25"/>
        </filter>

        <!-- ======================================================== -->
        <!-- ======================================================== -->
        <!-- userSpaceOnUse for primitive, objectBoundingBox for      -->
        <!-- filter chain.                                            -->
        <!-- ======================================================== -->
        <!-- ======================================================== -->

        <!-- ======================================================== -->
        <!-- filterRegion_3_0                                         -->
        <!--   chain region: objectBoundingBox, region undefined      -->
        <!--   flood region: unspecified, region undefined            -->
        <!--                                                          -->
        <!-- Expected fill region: (0, 0, 1, 1) in object bbox space  -->
        <!-- ======================================================== -->
        <filter id="filterRegion_3_0" filterUnits="objectBoundingBox" >
            <feFlood style="flood-color:red;" />
        </filter>

        <!-- ======================================================== -->
        <!-- filterRegion_3_1                                         -->
        <!--   chain region: objectBoundingBox, region undefined      -->
        <!--   flood region: userSpaceOnUse (20, 30, 20, 25)          -->
        <!--                                                          -->
        <!-- Expected fill region: intersection of (0, 0, 1, 1) in    -->
        <!--                       bbox space and (20, 30, 20, 25) in -->
        <!--                       user space.                        -->
        <!-- ======================================================== -->
        <filter id="filterRegion_3_1" filterUnits="objectBoundingBox" 
                                      primitiveUnits="userSpaceOnUse">
            <feFlood style="flood-color:red" x="20" y="30" width="20" height="25"/>
        </filter>

        <!-- ======================================================== -->
        <!-- filterRegion_3_2                                         -->
        <!--   chain region: objectBoundingBox (-1, -1, 2, 2)         -->
        <!--   flood region: userSpaceOnUse undefined                 -->
        <!--                                                          -->
        <!-- Expected fill region:(-1, -1, 2, 2) in                   -->
        <!--                       bbox space                         -->
        <!-- ======================================================== -->
        <filter id="filterRegion_3_2" filterUnits="objectBoundingBox" 
                                      primitiveUnits="userSpaceOnUse"
                                      x="-1" y="-1" width="2" height="2">
            <feFlood style="flood-color:red" />
        </filter>

        <!-- ======================================================== -->
        <!-- filterRegion_3_3                                         -->
        <!--   chain region: objectBoundingBox (-50%, -50%, 75%, 75%  -->
        <!--   flood region: userSpaceOnUse undefined                 -->
        <!--                                                          -->
        <!-- Expected fill region:(-.5, -.5, .75, .75) in             -->
        <!--                       bbox space                         -->
        <!-- ======================================================== -->
        <filter id="filterRegion_3_3" filterUnits="objectBoundingBox" 
                                      primitiveUnits="userSpaceOnUse"
                                      x="-50%" y="-50%" width="75%" height="75%">
            <feFlood style="flood-color:red" />
        </filter>

    </defs>

    <g transform="translate(30, 50)">

    <!-- ============================ -->
    <!-- filterRegion_0               -->
    <!-- ============================ -->
    <g transform="translate(240, 95)">
        <rect x="0" y="0" width="100" height="50" style="filter:url(#filterRegion_0);" /> 
        <rect class="filterRegionControl" x="-10" y="-5" width="120" height="60" /> 
        <text x="45" y="-10" class="filterRegionControlLabel">filterRegion_0</text>
    </g>

    <!-- ============================ -->
    <!-- filterRegion_1               -->
    <!-- ============================ -->
    <rect x="0" y="3000" width="20000000" height="25" style="filter:url(#filterRegion_1);" />
    <rect class="filterRegionControl" x="20" y="30" width="50" height="40" />
    <text x="45" y="25" class="filterRegionControlLabel">filterRegion_1</text> 
    <!-- ============================ -->
    <!-- filterRegion_2               -->
    <!-- ============================ -->
    <g transform="translate(100, 0)">
        <rect x="0" y="3000" width="20000000" height="25" style="filter:url(#filterRegion_2);" />
        <rect class="filterRegionControl" x="25" y="35" width="45" height="35" />
        <text x="45" y="25" class="filterRegionControlLabel">filterRegion_2</text>
    </g>
    
    <!-- ============================ -->
    <!-- filterRegion_3               -->
    <!-- ============================ -->
    <g transform="translate(200, 0)">
        <rect x="0" y="3000" width="20000000" height="25" style="filter:url(#filterRegion_3);" />
        <rect class="filterRegionControl" x="20" y="30" width="50" height="20" />
        <text x="45" y="25" class="filterRegionControlLabel">filterRegion_3</text>
    </g>
    
    <!-- ============================ -->
    <!-- filterRegion_4               -->
    <!-- ============================ -->
    <g transform="translate(300, 0)">
        <rect x="0" y="3000" width="20000000" height="25" style="filter:url(#filterRegion_4);" />
        <rect class="filterRegionControl" x="20" y="30" width="25" height="40" />
        <text x="45" y="25" class="filterRegionControlLabel">filterRegion_4</text>
    </g>
    
    <!-- ============================ -->
    <!-- filterRegion_5               -->
    <!-- ============================ -->
    <g transform="translate(0, 75)">
        <rect x="0" y="3000" width="20000000" height="25" style="filter:url(#filterRegion_5);" />
        <rect class="filterRegionControl" x="20" y="30" width="50" height="20" />
        <text x="45" y="25" class="filterRegionControlLabel">filterRegion_5</text>
    </g>
    
    <!-- ============================ -->
    <!-- filterRegion_6               -->
    <!-- ============================ -->
    <g transform="translate(100, 75)" style="filter:url(#filterRegion_6);">
        <rect x="0" y="3000" width="20000000" height="25"/>
    </g>
    <g transform="translate(100, 75)" >
        <rect class="filterRegionControl" x="20" y="30" width="40" height="40" />
        <text x="45" y="25" class="filterRegionControlLabel">filterRegion_6</text>
    </g>

    <!-- ============================ -->
    <!-- filterRegion_2_0             -->
    <!-- ============================ -->
    <g transform="translate(120, 280)">
        <rect x="0" y="0" width="50" height="25" style="filter:url(#filterRegion_2_0);" />
        <rect class="filterRegionControl" x="-5" y="-2.5" width="60" height="30" />
        <text x="25" y="-20" class="filterRegionControlLabel">filterRegion_2_0</text>
    </g>

    <!-- ============================ -->
    <!-- filterRegion_2_1             -->
    <!-- ============================ -->
    <g transform="translate(0, 160)" style="filter:url(#filterRegion_2_1);">
        <rect x="20" y="30" width="50" height="40" />
    </g>
    <g transform="translate(0, 160)">
        <rect class="filterRegionControl" x="20" y="30" width="50" height="40" />
        <text x="45" y="25" class="filterRegionControlLabel">filterRegion_2_1</text>
    </g>

    <!-- ============================ -->
    <!-- filterRegion_2_2             -->
    <!-- ============================ -->
    <g transform="translate(100, 160)" >
        <rect x="20" y="30" width="50" height="40" style="filter:url(#filterRegion_2_2);"/>
    </g>
    <g transform="translate(100, 160)">
        <rect class="filterRegionControl" x="20" y="30" width="50" height="40" />
        <text x="45" y="25" class="filterRegionControlLabel">filterRegion_2_2</text>
    </g>

    <!-- ============================ -->
    <!-- filterRegion_2_3             -->
    <!-- ============================ -->
    <g transform="translate(200, 160)" style="filter:url(#filterRegion_2_3);">
        <rect x="20" y="30" width="50" height="40" />
    </g>
    <g transform="translate(200, 160)">
        <rect class="filterRegionControl" x="32.5" y="50" width="25" height="10" />
        <text x="45" y="25" class="filterRegionControlLabel">filterRegion_2_3</text>
    </g>

    <!-- ============================ -->
    <!-- filterRegion_2_4             -->
    <!-- ============================ -->
    <g transform="translate(300, 160)" style="filter:url(#filterRegion_2_4);">
        <rect x="20" y="30" width="50" height="40" />
    </g>
    <g transform="translate(300, 160)">
        <rect class="filterRegionControl" x="32.5" y="50" width="25" height="10" />
        <text x="45" y="25" class="filterRegionControlLabel">filterRegion_2_4</text>
    </g>

    <!-- ============================ -->
    <!-- filterRegion_2_5             -->
    <!-- ============================ -->
    <g transform="translate(0, 245)" style="filter:url(#filterRegion_2_5);">
        <rect x="20" y="30" width="50" height="40" />
    </g>
    <g transform="translate(0, 245)">
        <rect class="filterRegionControl" x="20" y="50" width="50" height="10" />
        <text x="45" y="25" class="filterRegionControlLabel">filterRegion_2_5</text>
    </g>

    <!-- ============================ -->
    <!-- filterRegion_3_1             -->
    <!-- ============================ -->
    <g transform="translate(0, 330)" style="filter:url(#filterRegion_3_1);">
        <rect x="0" y="0" width="450" height="450" />
    </g>
    <g transform="translate(0, 330)">
        <rect class="filterRegionControl" x="20" y="30" width="20" height="25" />
        <text x="45" y="25" class="filterRegionControlLabel">filterRegion_3_1</text>
    </g>

    <!-- ============================ -->
    <!-- filterRegion_3_0             -->
    <!-- ============================ -->
    <g transform="translate(320, 330)" style="filter:url(#filterRegion_3_0);">
        <rect x="0" y="0" width="40" height="50" />
    </g>
    <g transform="translate(320, 330)">
        <rect class="filterRegionControl" x="-4" y="-5" width="48" height="60" />
        <text x="45" y="25" class="filterRegionControlLabel">filterRegion_3_0</text>
    </g>

    <!-- ============================ -->
    <!-- filterRegion_3_2             -->
    <!-- ============================ -->
    <g transform="translate(100, 330)" style="filter:url(#filterRegion_3_2);">
        <rect x="50" y="40" width="50" height="40" />
    </g>
    <g transform="translate(100, 330)">
        <rect class="filterRegionControl" x="0" y="0" width="100" height="80" />
        <text x="45" y="25" class="filterRegionControlLabel">filterRegion_3_2</text>
    </g>

    <!-- ============================ -->
    <!-- filterRegion_3_3             -->
    <!-- ============================ -->
    <g transform="translate(200, 330)" style="filter:url(#filterRegion_3_3);">
        <rect x="50" y="40" width="50" height="40" />
    </g>
    <g transform="translate(200, 330)">
        <rect class="filterRegionControl" x="25" y="20" width="37.5" height="30" />
        <text x="45" y="25" class="filterRegionControlLabel">filterRegion_3_3</text>
    </g>
    </g>
    
    <!-- ============================================================= -->
    <!-- Batik sample mark                                             -->
    <!-- ============================================================= -->
    <use xlink:href="../resources/batikLogo.svg#Batik_Tag_Box" />    

</svg>
