Test frontend's timeout support.


Testing expression while (1){} with timeout: 0
error: Execution was terminated
Error: Execution was terminated

Testing expression 1 + 1 with timeout: undefined
Result:
  Description: 2
  Value:       2
  Type:        number

Does the runtime also support side effect checks? true

Clearing cached side effect support
Set timer for test function.
Script execution paused.

Testing expression while (1){} with timeout: 0
error: Execution was terminated
Error: Execution was terminated

Testing expression 1 + 1 with timeout: undefined
Result:
  Description: 2
  Value:       2
  Type:        number
Does the runtime also support side effect checks? true
Script execution resumed.

