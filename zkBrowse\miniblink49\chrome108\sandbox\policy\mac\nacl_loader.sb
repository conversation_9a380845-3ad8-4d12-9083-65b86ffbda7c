; Copyright 2011 The Chromium Authors
; Use of this source code is governed by a BSD-style license that can be
; found in the LICENSE file.
;
; This is the Sandbox configuration file used for safeguarding the user's
; untrusted code within Native Client.
;

; *** The contents of common.sb are implicitly included here. ***

; Allow a Native Client application to use semaphores, specifically
; sem_init(), et.al.
(allow ipc-posix-sem)

(allow user-preference-read)

(allow iokit-get-properties
  (iokit-registry-entry-class "IORegisterForSystemPower"))

(allow iokit-open
  (iokit-user-client-class "IOSurfaceSendRight")
  (iokit-user-client-class "RootDomainUserClient")
)

(allow file-read-data
  (subpath "/usr/share/locale")
  ; e.g. ~/Library/Preferences/com.google.Chrome.plist
  (path (string-append
          (user-homedir-path "/Library/Preferences/")
          (param bundle-id)
          ".plist"))
)

(allow mach-lookup
  (global-name "com.apple.PowerManagement.control")
  (global-name "com.apple.system.notification_center")
)

(if (>= os-version 1014)
  (begin
    (allow sysctl-read (sysctl-name "kern.tcsm_enable"))
    (allow sysctl-write (sysctl-name "kern.tcsm_enable"))
    (allow sysctl-read (sysctl-name "kern.tcsm_available"))
))
