  1) LOAD 4  // Architecture
  2) if A == 0xc000003e; then JMP 3 else JMP 5
  3) LOAD 0  // System call number
  4) if A & 0x40000000; then JMP 5 else JMP 6
  5) RET 0x0  // Kill
  6) if A >= 0x40; then JMP 7 else JMP 8
  7) if A >= 0x401; then JMP 15 else JMP 14
  8) if A >= 0x3f; then JMP 9 else JMP 14
  9) LOAD 20  // Argument 0 (MSB)
 10) if A == 0xdeadbeef; then JMP 11 else JMP 14
 11) LOAD 16  // Argument 0 (LSB)
 12) if A == 0xdeadbeef; then JMP 13 else JMP 14
 13) RET 0x50001  // errno = 1
 14) RET 0x7fff0000  // Allowed
 15) RET 0x50026  // errno = 38
