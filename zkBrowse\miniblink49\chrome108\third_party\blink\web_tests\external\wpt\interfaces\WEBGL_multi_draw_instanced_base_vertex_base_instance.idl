// GENERATED CONTENT - DO NOT EDIT
// Content was automatically extracted by <PERSON><PERSON> into webref
// (https://github.com/w3c/webref)
// Source: WebGL WEBGL_multi_draw_instanced_base_vertex_base_instance Extension Draft Specification (https://registry.khronos.org/webgl/extensions/WEBGL_multi_draw_instanced_base_vertex_base_instance/)

[Exposed=(Window,Worker), LegacyNoInterfaceObject]
interface WEBGL_multi_draw_instanced_base_vertex_base_instance {
  undefined multiDrawArraysInstancedBaseInstanceWEBGL(
      GLenum mode,
      ([AllowShared] Int32Array or sequence<GLint>) firstsList, GLuint firstsOffset,
      ([AllowShared] Int32Array or sequence<GLsizei>) countsList, GLuint countsOffset,
      ([AllowShared] Int32Array or sequence<GLsizei>) instanceCountsList, GLuint instanceCountsOffset,
      ([AllowShared] Uint32Array or sequence<GLuint>) baseInstancesList, GLuint baseInstancesOffset,
      GLsizei drawcount
  );
  undefined multiDrawElementsInstancedBaseVertexBaseInstanceWEBGL(
      GLenum mode,
      ([AllowShared] Int32Array or sequence<GLsizei>) countsList, GLuint countsOffset,
      GLenum type,
      ([AllowShared] Int32Array or sequence<GLsizei>) offsetsList, GLuint offsetsOffset,
      ([AllowShared] Int32Array or sequence<GLsizei>) instanceCountsList, GLuint instanceCountsOffset,
      ([AllowShared] Int32Array or sequence<GLint>) baseVerticesList, GLuint baseVerticesOffset,
      ([AllowShared] Uint32Array or sequence<GLuint>) baseInstancesList, GLuint baseInstancesOffset,
      GLsizei drawcount
  );
};
