<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" [
]>
<?AdobeSVGViewer save="snapshot"?>
<svg width="100%" height="100%" viewBox="0 0 1024 768" onload="initMap(evt)" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" zoomAndPan="disable">
	<script type="text/ecmascript" xlink:href="resources/helper_functions.js" />
	<script type="text/ecmascript" xlink:href="resources/timer.js" />
	<script type="text/ecmascript" xlink:href="resources/mapApp.js" />
	<script type="text/ecmascript" xlink:href="resources/initComboBox.js" />
	<script type="text/ecmascript" xlink:href="resources/comboBox.js" />
	<rect x="-1000" y="-1000" width="3000" height="3000" fill="white" stroke="none" />
	<g fill="dimgray" font-family="Arial,Helvetica" font-size="11px">
		<text x="500" y="60" font-weight="bold" font-size="13px">Combobox Demo</text>
		<a xlink:href="index.shtml"><text x="500" y="80">Link to Documentation (HTML)</text></a>
		<!-- Labels -->
		<text x="240" y="62">Fruits<tspan x="240" dy="20">This comboBox allows only single selection</tspan></text>
		<text x="240" y="312">Roses</text>
		<text x="240" y="333">This comboBox gives<tspan x="240" dy="15">feedback on changes</tspan><tspan x="240" dy="15">in the selection</tspan></text>
		<text x="50" y="400">The Following Roses are selected:<tspan x="50" dy="15" id="selectedRoses"> </tspan></text>
		<text x="240" y="562">Flowers</text>
		<text x="240" y="582" onclick="comboFlowers.deselectAll(false)">Click on this text to deselect all elements</text>
		<text x="240" y="597" onclick="comboFlowers.selectAll(false)">Click on this text to select all elements</text>
		<text x="240" y="612" onclick="comboFlowers.invertSelection(false)">Click on this text to invert selection</text>
		<text x="240" y="627" onclick="var newX = 10 + Math.random()*50; var newY = 525 + Math.random()*50; comboFlowers.moveTo(newX,newY)">Click on this text to randomly move the combobox</text>
		<text x="240" y="642" onclick="var newWidth = 140 + Math.random()*50; var newY = 525 + Math.random()*50; comboFlowers.resize(newWidth)">Click on this text to randomly resize the combobox</text>
	</g>
	<!-- Containers to hold new Graphics of SelectionLists, the position within the File is important -->
	<g transform="translate(500,20),rotate(30)">
		<g id="communitiesAarau" />
		<text x="220" y="212" fill="dimgray" font-family="Arial,Helvetica" font-size="11px">Communities of Kt. Aargau (Switzerland)<tspan x="220" dy="20">Note that one can also transform the group containing the combo box</tspan></text>
	</g>
	<g id="flowers" />
	<g id="roses" />
	<g id="fruits" />
</svg>
