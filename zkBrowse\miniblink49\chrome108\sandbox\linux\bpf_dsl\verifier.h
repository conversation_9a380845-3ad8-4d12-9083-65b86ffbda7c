// Copyright 2012 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef SANDBOX_LINUX_BPF_DSL_VERIFIER_H__
#define SANDBOX_LINUX_BPF_DSL_VERIFIER_H__

#include <stdint.h>

#include <vector>

#include "sandbox/sandbox_export.h"

struct arch_seccomp_data;
struct sock_filter;

namespace sandbox {

namespace bpf_dsl {

// TODO(mdempsky): This class doesn't perform any verification any more, so it
// deserves a new name.
class SANDBOX_EXPORT Verifier {
 public:
  Verifier() = delete;
  Verifier(const Verifier&) = delete;
  Verifier& operator=(const Verifier&) = delete;

  // Evaluate a given BPF program for a particular set of system call
  // parameters. If evaluation failed for any reason, "err" will be set to
  // a non-NULL error string. Otherwise, the BPF program's result will be
  // returned by the function and "err" is NULL.
  // We do not actually implement the full BPF state machine, but only the
  // parts that can actually be generated by our BPF compiler. If this code
  // is used for purposes other than verifying the output of the sandbox's
  // BPF compiler, we might have to extend this BPF interpreter.
  static uint32_t EvaluateBPF(const std::vector<struct sock_filter>& program,
                              const struct arch_seccomp_data& data,
                              const char** err);
};

}  // namespace bpf_dsl
}  // namespace sandbox

#endif  // SANDBOX_LINUX_BPF_DSL_VERIFIER_H__
