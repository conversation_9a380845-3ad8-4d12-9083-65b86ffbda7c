<html>
    <head>
        <script src="../../resources/gesture-util.js"></script>
        <script>
        if (window.testRunner) {
            testRunner.dumpAsText();
            testRunner.dumpChildFrames();
            testRunner.waitUntilDone();
        }

        function closeWindow(windowToClose)
        {
            windowToClose.close();
            setTimeout(doneHand<PERSON>, 1);

            function doneHandler()
            {
                if (!windowToClose.closed)
                    setTimeout(doneHandler, 1);
                else if (window.testRunner)
                    testRunner.notifyDone();
            }
        }

        function handleClick(event) {
            var newWindow = window.open('resources/window-opened.html', 'badName', 'width=100, height=100');
            if (newWindow) {
                log("Window was opened! Test succeeded!");
                closeWindow(newWindow);
            }

            event.preventDefault();
            return false;
        }

        function test()
        {
            mouseClickOn(2, 2);
        }

        function log(msg)
        {
            var res = document.getElementById('res');
            res.innerHTML = res.innerHTML + msg + "<br>";
        }
        </script>
    </head>
    <body style="border: 0; margin: 0" onload="test()">
        <iframe style="border: 0; margin: 0" src="resources/popup-blocking-click-in-iframe-otherFrame.html" name="otherFrame"></iframe>

        <p>This tests that popup blocking does not suppress windows opened in an iframe if the event handler is a function from an enclosing frame.</p>
        <p>To run manually click the link in the iframe above with popup blocking enabled.</p>
        <div id="res"></div>
    </body>
</html>
