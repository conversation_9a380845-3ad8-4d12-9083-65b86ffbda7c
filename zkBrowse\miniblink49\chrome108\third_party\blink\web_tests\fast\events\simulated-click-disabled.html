<html>
<head>
<script>

function clickOn(element)
{
    if (!window.eventSender)
        return;

    var x = element.offsetLeft + element.offsetWidth / 2;
    var y = element.offsetTop + element.offsetHeight / 2;
    eventSender.mouseMoveTo(x, y);
    eventSender.mouseDown();
    eventSender.mouseUp();
}

function runTest()
{
    if (window.testRunner)
        testRunner.dumpAsText();

    clickOn(document.querySelector('label'));
    document.querySelector('div').textContent = document.getElementById('target').checked ? 'FAIL' : 'PASS';
}

</script>
</head>
<body onload="runTest()">
    <p>Ensure that simulated click is not dispatched to a disabled node. Clicking on the label should not change the value of the checkbox.</p>
    <div>
        <input type="checkbox" id="target" disabled>
        <label for="target">CLICK ME</label>
    </div>
</body>
</html>