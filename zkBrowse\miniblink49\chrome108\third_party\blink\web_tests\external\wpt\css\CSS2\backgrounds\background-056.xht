<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background with (repeat attachment color)</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="G<PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-03-19 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <link rel="match" href="background-030-ref.xht" />

        <meta name="assert" content="Background with (repeat attachment color) sets the background to the color specified.  Repeat and attachment are ignored since image is not set." />
        <style type="text/css">
            div
            {
                background: repeat-x scroll green;
                height: 200px;
                width: 200px;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is a <strong>filled green square</strong>.</p>
        <div></div>
    </body>
</html>