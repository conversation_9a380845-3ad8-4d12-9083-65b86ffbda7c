@echo off
echo Batch fixing V8 10.8 compatibility issues in modules bindings...
echo.

set "MODULES_DIR=..\gen\blink\bindings\modules\v8"

REM List of files to fix
set FILES=V8ExtShaderTextureLod.cpp V8ExtSRGB.cpp V8ExtTextureFilterAnisotropic.cpp V8OESStandardDerivatives.cpp V8OESTextureFloatLinear.cpp V8OESTextureFloat.cpp V8OESElementIndexUint.cpp V8OESVertexArrayObject.cpp V8OESTextureHalfFloat.cpp V8OESTextureHalfFloatLinear.cpp

for %%f in (%FILES%) do (
    echo Processing %%f...
    if exist "%MODULES_DIR%\%%f" (
        echo   - Adding compatibility header...
        REM Create temp file with compatibility header added
        (
            echo // Copyright 2014 The Chromium Authors. All rights reserved.
            echo // Use of this source code is governed by a BSD-style license that can be
            echo // found in the LICENSE file.
            echo.
            echo // This file has been auto-generated by code_generator_v8.py. DO NOT MODIFY!
            echo.
            echo #include "config.h"
        ) > "%MODULES_DIR%\%%f.tmp"
        
        REM Add the rest of the file content with modifications
        powershell -Command "& { $content = Get-Content '%MODULES_DIR%\%%f' -Raw; $content = $content -replace '(#include \"wtf/RefPtr\.h\")', '$1`r`n`r`n// V8 10.8 compatibility layer`r`n#if V8_MAJOR_VERSION >= 10`r`n#include \"../../../../../v8_10_8/v8_compatibility.h\"`r`n#endif'; $content = $content -replace 'isolate->SetReferenceFromGroup\(v8::UniqueId\(reinterpret_cast<intptr_t>\(root\)\), wrapper\);', '#if V8_MAJOR_VERSION >= 10`r`n        v8::IsolateCompat::SetReferenceFromGroup(isolate, v8::UniqueId(reinterpret_cast<intptr_t>(root)), wrapper);`r`n#else`r`n        isolate->SetReferenceFromGroup(v8::UniqueId(reinterpret_cast<intptr_t>(root)), wrapper);`r`n#endif'; Set-Content '%MODULES_DIR%\%%f' $content -NoNewline }"
        
        echo   - Fixed SetReferenceFromGroup calls
        echo   - File %%f updated successfully
    ) else (
        echo   - Warning: %%f not found
    )
    echo.
)

echo.
echo Batch fix completed!
echo All specified modules V8 bindings have been updated for V8 10.8 compatibility.
pause
