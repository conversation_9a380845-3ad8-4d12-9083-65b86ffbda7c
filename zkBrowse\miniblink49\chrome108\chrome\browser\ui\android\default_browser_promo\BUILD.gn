# Copyright 2020 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/android/rules.gni")

android_library("java") {
  sources = [
    "java/src/org/chromium/chrome/browser/ui/default_browser_promo/DefaultBrowserPromoDeps.java",
    "java/src/org/chromium/chrome/browser/ui/default_browser_promo/DefaultBrowserPromoManager.java",
    "java/src/org/chromium/chrome/browser/ui/default_browser_promo/DefaultBrowserPromoUtils.java",
  ]
  deps = [
    "//base:base_java",
    "//chrome/browser/android/lifecycle:java",
    "//chrome/browser/flags:java",
    "//chrome/browser/preferences:java",
    "//chrome/browser/profiles/android:java",
    "//third_party/androidx:androidx_annotation_annotation_java",
    "//ui/android:ui_full_java",
  ]
  resources_package = "org.chromium.chrome.browser.ui.default_browser_promo"
}

robolectric_library("junit") {
  sources = [ "java/src/org/chromium/chrome/browser/ui/default_browser_promo/DefaultBrowserPromoUtilsTest.java" ]
  deps = [
    ":java",
    "//base:base_java",
    "//base:base_junit_test_support",
    "//third_party/junit",
    "//third_party/mockito:mockito_java",
  ]
}
