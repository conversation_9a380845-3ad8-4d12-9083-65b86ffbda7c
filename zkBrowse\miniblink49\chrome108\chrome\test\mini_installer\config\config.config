{"states": [["no_pv", ["chrome_user_no_pv.prop", "chrome_beta_no_pv.prop", "chrome_canary_no_pv.prop", "chrome_dev_no_pv.prop", "chrome_system_no_pv.prop"]], ["clean", ["chrome_user_not_installed.prop", "chrome_beta_not_installed.prop", "chrome_canary_not_installed.prop", "chrome_dev_not_installed.prop", "chrome_system_not_installed.prop", "chrome_user_not_inuse.prop", "chrome_beta_not_inuse.prop", "chrome_canary_not_inuse.prop", "chrome_dev_not_inuse.prop", "chrome_system_not_inuse.prop"]], ["chrome_user_installed_not_inuse", ["chrome_user_installed.prop", "chrome_beta_not_installed.prop", "chrome_canary_not_installed.prop", "chrome_dev_not_installed.prop", "chrome_system_not_installed.prop", "chrome_user_not_inuse.prop", "chrome_beta_not_inuse.prop", "chrome_canary_not_inuse.prop", "chrome_dev_not_inuse.prop", "chrome_system_not_inuse.prop"]], ["previous_chrome_user_installed_not_inuse", ["previous_chrome_user_installed.prop", "chrome_beta_not_installed.prop", "chrome_canary_not_installed.prop", "chrome_dev_not_installed.prop", "chrome_system_not_installed.prop", "chrome_user_not_inuse.prop", "chrome_beta_not_inuse.prop", "chrome_canary_not_inuse.prop", "chrome_dev_not_inuse.prop", "chrome_system_not_inuse.prop"]], ["chrome_beta_installed_not_inuse", ["chrome_user_not_installed.prop", "chrome_beta_installed.prop", "chrome_canary_not_installed.prop", "chrome_dev_not_installed.prop", "chrome_system_not_installed.prop", "chrome_user_not_inuse.prop", "chrome_beta_not_inuse.prop", "chrome_canary_not_inuse.prop", "chrome_dev_not_inuse.prop", "chrome_system_not_inuse.prop"]], ["chrome_canary_installed_not_inuse", ["chrome_user_not_installed.prop", "chrome_beta_not_installed.prop", "chrome_canary_installed.prop", "chrome_dev_not_installed.prop", "chrome_system_not_installed.prop", "chrome_user_not_inuse.prop", "chrome_beta_not_inuse.prop", "chrome_canary_not_inuse.prop", "chrome_dev_not_inuse.prop", "chrome_system_not_inuse.prop"]], ["previous_chrome_canary_installed_not_inuse", ["chrome_user_not_installed.prop", "chrome_beta_not_installed.prop", "previous_chrome_canary_installed.prop", "chrome_dev_not_installed.prop", "chrome_system_not_installed.prop", "chrome_user_not_inuse.prop", "chrome_beta_not_inuse.prop", "chrome_canary_not_inuse.prop", "chrome_dev_not_inuse.prop", "chrome_system_not_inuse.prop"]], ["chrome_dev_installed_not_inuse", ["chrome_user_not_installed.prop", "chrome_beta_not_installed.prop", "chrome_canary_not_installed.prop", "chrome_dev_installed.prop", "chrome_system_not_installed.prop", "chrome_user_not_inuse.prop", "chrome_beta_not_inuse.prop", "chrome_canary_not_inuse.prop", "chrome_dev_not_inuse.prop", "chrome_system_not_inuse.prop"]], ["chrome_user_and_beta_installed_not_inuse", ["chrome_user_installed.prop", "chrome_beta_installed.prop", "chrome_canary_not_installed.prop", "chrome_dev_not_installed.prop", "chrome_system_not_installed.prop", "chrome_user_not_inuse.prop", "chrome_beta_not_inuse.prop", "chrome_canary_not_inuse.prop", "chrome_dev_not_inuse.prop", "chrome_system_not_inuse.prop"]], ["chrome_user_and_beta_and_dev_installed_not_inuse", ["chrome_user_installed.prop", "chrome_beta_installed.prop", "chrome_canary_not_installed.prop", "chrome_dev_installed.prop", "chrome_system_not_installed.prop", "chrome_user_not_inuse.prop", "chrome_beta_not_inuse.prop", "chrome_canary_not_inuse.prop", "chrome_dev_not_inuse.prop", "chrome_system_not_inuse.prop"]], ["chrome_user_and_beta_and_dev_and_canary_installed_not_inuse", ["chrome_user_installed.prop", "chrome_beta_installed.prop", "chrome_canary_installed.prop", "chrome_dev_installed.prop", "chrome_system_not_installed.prop", "chrome_user_not_inuse.prop", "chrome_beta_not_inuse.prop", "chrome_canary_not_inuse.prop", "chrome_dev_not_inuse.prop", "chrome_system_not_inuse.prop"]], ["chrome_beta_and_dev_and_canary_installed_not_inuse", ["chrome_user_not_installed.prop", "chrome_beta_installed.prop", "chrome_canary_installed.prop", "chrome_dev_installed.prop", "chrome_system_not_installed.prop", "chrome_user_not_inuse.prop", "chrome_beta_not_inuse.prop", "chrome_canary_not_inuse.prop", "chrome_dev_not_inuse.prop", "chrome_system_not_inuse.prop"]], ["chrome_dev_and_canary_installed_not_inuse", ["chrome_user_not_installed.prop", "chrome_beta_not_installed.prop", "chrome_canary_installed.prop", "chrome_dev_installed.prop", "chrome_system_not_installed.prop", "chrome_user_not_inuse.prop", "chrome_beta_not_inuse.prop", "chrome_canary_not_inuse.prop", "chrome_dev_not_inuse.prop", "chrome_system_not_inuse.prop"]], ["chrome_system_installed_not_inuse", ["chrome_user_not_installed.prop", "chrome_beta_not_installed.prop", "chrome_canary_not_installed.prop", "chrome_dev_not_installed.prop", "chrome_system_installed.prop", "chrome_user_not_inuse.prop", "chrome_beta_not_inuse.prop", "chrome_canary_not_inuse.prop", "chrome_dev_not_inuse.prop", "chrome_system_not_inuse.prop"]], ["previous_chrome_system_installed_not_inuse", ["chrome_user_not_installed.prop", "chrome_beta_not_installed.prop", "chrome_canary_not_installed.prop", "chrome_dev_not_installed.prop", "previous_chrome_system_installed.prop", "chrome_user_not_inuse.prop", "chrome_beta_not_inuse.prop", "chrome_canary_not_inuse.prop", "chrome_dev_not_inuse.prop", "chrome_system_not_inuse.prop"]], ["chrome_user_installed_inuse", ["chrome_user_installed.prop", "chrome_beta_not_installed.prop", "chrome_canary_not_installed.prop", "chrome_dev_not_installed.prop", "chrome_system_not_installed.prop", "chrome_user_inuse.prop", "chrome_beta_not_inuse.prop", "chrome_canary_not_inuse.prop", "chrome_dev_not_inuse.prop", "chrome_system_not_inuse.prop"]], ["chrome_canary_installed_inuse", ["chrome_user_not_installed.prop", "chrome_beta_not_installed.prop", "chrome_canary_installed.prop", "chrome_dev_not_installed.prop", "chrome_system_not_installed.prop", "chrome_user_not_inuse.prop", "chrome_beta_not_inuse.prop", "chrome_canary_inuse.prop", "chrome_dev_not_inuse.prop", "chrome_system_not_inuse.prop"]], ["chrome_system_installed_inuse", ["chrome_user_not_installed.prop", "chrome_beta_not_installed.prop", "chrome_canary_not_installed.prop", "chrome_dev_not_installed.prop", "chrome_system_installed.prop", "chrome_user_not_inuse.prop", "chrome_beta_not_inuse.prop", "chrome_canary_not_inuse.prop", "chrome_dev_not_inuse.prop", "chrome_system_inuse.prop"]], ["no_chrome_user", ["chrome_user_killed.prop"]], ["no_chrome_user_binaries", ["chrome_user_binaries_killed.prop"]], ["chrome_user_updated_not_inuse", ["chrome_user_installed.prop", "chrome_beta_not_installed.prop", "chrome_canary_not_installed.prop", "chrome_dev_not_installed.prop", "chrome_system_not_installed.prop", "chrome_user_not_inuse.prop", "chrome_beta_not_inuse.prop", "chrome_canary_not_inuse.prop", "chrome_dev_not_inuse.prop", "chrome_system_not_inuse.prop"]], ["chrome_canary_updated_not_inuse", ["chrome_user_not_installed.prop", "chrome_beta_not_installed.prop", "chrome_canary_installed.prop", "chrome_dev_not_installed.prop", "chrome_system_not_installed.prop", "chrome_user_not_inuse.prop", "chrome_beta_not_inuse.prop", "chrome_canary_not_inuse.prop", "chrome_dev_not_inuse.prop", "chrome_system_not_inuse.prop"]], ["chrome_system_updated_not_inuse", ["chrome_user_not_installed.prop", "chrome_beta_not_installed.prop", "chrome_canary_not_installed.prop", "chrome_dev_not_installed.prop", "chrome_system_installed.prop", "chrome_user_not_inuse.prop", "chrome_beta_not_inuse.prop", "chrome_canary_not_inuse.prop", "chrome_dev_not_inuse.prop", "chrome_system_not_inuse.prop"]]], "actions": [["test_chrome_with_chromedriver_user", "\"$PYTHON_INTERPRETER\" test_chrome_with_chromedriver.py --chromedriver-path=\"$CHROMEDRIVER_PATH\" $QUIET $OUTPUT_DIR \"$LOCAL_APPDATA\\$CHROME_DIR\\Application\\chrome.exe\""], ["test_chrome_with_chromedriver_system", "\"$PYTHON_INTERPRETER\" test_chrome_with_chromedriver.py --chromedriver-path=\"$CHROMEDRIVER_PATH\" $QUIET $OUTPUT_DIR \"$PROGRAM_FILES\\$CHROME_DIR\\Application\\chrome.exe\""], ["test_chrome_with_chromedriver_beta", "\"$PYTHON_INTERPRETER\" test_chrome_with_chromedriver.py --chromedriver-path=\"$CHROMEDRIVER_PATH\" $QUIET $OUTPUT_DIR \"$LOCAL_APPDATA\\$CHROME_DIR_BETA\\Application\\chrome.exe\""], ["test_chrome_with_chromedriver_canary", "\"$PYTHON_INTERPRETER\" test_chrome_with_chromedriver.py --chromedriver-path=\"$CHROMEDRIVER_PATH\" $QUIET $OUTPUT_DIR \"$LOCAL_APPDATA\\$CHROME_DIR_SXS\\Application\\chrome.exe\""], ["test_chrome_with_chromedriver_dev", "\"$PYTHON_INTERPRETER\" test_chrome_with_chromedriver.py --chromedriver-path=\"$CHROMEDRIVER_PATH\" $QUIET $OUTPUT_DIR \"$LOCAL_APPDATA\\$CHROME_DIR_DEV\\Application\\chrome.exe\""], ["install_chrome_beta", "\"$MINI_INSTALLER\" --chrome-beta \"$LOG_FILE\" --verbose-logging --do-not-launch-chrome"], ["install_chrome_canary", "\"$MINI_INSTALLER\" --chrome-sxs \"$LOG_FILE\" --verbose-logging --do-not-launch-chrome"], ["install_previous_chrome_canary", "\"$PREVIOUS_VERSION_MINI_INSTALLER\" --chrome-sxs \"$LOG_FILE\" --verbose-logging --do-not-launch-chrome"], ["install_chrome_dev", "\"$MINI_INSTALLER\" --chrome-dev \"$LOG_FILE\" --verbose-logging --do-not-launch-chrome"], ["install_chrome_system", "\"$MINI_INSTALLER\" \"$LOG_FILE\" --verbose-logging --system-level --do-not-launch-chrome"], ["install_previous_chrome_system", "\"$PREVIOUS_VERSION_MINI_INSTALLER\" \"$LOG_FILE\" --verbose-logging --system-level --do-not-launch-chrome"], ["install_chrome_user", "\"$MINI_INSTALLER\" \"$LOG_FILE\" --verbose-logging --do-not-launch-chrome"], ["install_previous_chrome_user", "\"$PREVIOUS_VERSION_MINI_INSTALLER\" \"$LOG_FILE\" --verbose-logging --do-not-launch-chrome"], ["kill_user_binaries", "reg.exe delete \"HKEY_CURRENT_USER\\$BINARIES_UPDATE_REGISTRY_SUBKEY\" /v pv /f /reg:32"], ["kill_user_chrome", "reg.exe delete \"HKEY_CURRENT_USER\\$CHROME_UPDATE_REGISTRY_SUBKEY\" /v pv /f /reg:32"], ["launch_chrome_canary", "\"$PYTHON_INTERPRETER\" launch_chrome.py \"$LOCAL_APPDATA\\$CHROME_DIR_SXS\\Application\\chrome.exe\""], ["launch_chrome_system", "\"$PYTHON_INTERPRETER\" launch_chrome.py \"$PROGRAM_FILES\\$CHROME_DIR\\Application\\chrome.exe\""], ["launch_chrome_user", "\"$PYTHON_INTERPRETER\" launch_chrome.py \"$LOCAL_APPDATA\\$CHROME_DIR\\Application\\chrome.exe\""], ["quit_chrome_canary", "\"$PYTHON_INTERPRETER\" quit_chrome.py \"$LOCAL_APPDATA\\$CHROME_DIR_SXS\\Application\\chrome.exe\""], ["quit_chrome_system", "\"$PYTHON_INTERPRETER\" quit_chrome.py \"$PROGRAM_FILES\\$CHROME_DIR\\Application\\chrome.exe\""], ["quit_chrome_user", "\"$PYTHON_INTERPRETER\" quit_chrome.py \"$LOCAL_APPDATA\\$CHROME_DIR\\Application\\chrome.exe\""], ["uninstall_chrome_beta", "\"$PYTHON_INTERPRETER\" uninstall_chrome.py \"$LOG_FILE\" --chrome-long-name=\"$CHROME_LONG_NAME_BETA\""], ["uninstall_chrome_canary", "\"$PYTHON_INTERPRETER\" uninstall_chrome.py \"$LOG_FILE\" --chrome-long-name=\"$CHROME_LONG_NAME_SXS\""], ["uninstall_chrome_dev", "\"$PYTHON_INTERPRETER\" uninstall_chrome.py \"$LOG_FILE\" --chrome-long-name=\"$CHROME_LONG_NAME_DEV\""], ["uninstall_chrome_system", "\"$PYTHON_INTERPRETER\" uninstall_chrome.py \"$LOG_FILE\" --chrome-long-name=\"$CHROME_LONG_NAME\" --system-level"], ["uninstall_chrome_user", "\"$PYTHON_INTERPRETER\" uninstall_chrome.py \"$LOG_FILE\" --chrome-long-name=\"$CHROME_LONG_NAME\""], ["update_chrome_canary", "\"$MINI_INSTALLER\" --chrome-sxs \"$LOG_FILE\" --verbose-logging --do-not-launch-chrome"], ["update_chrome_system", "\"$MINI_INSTALLER\" \"$LOG_FILE\" --verbose-logging --system-level --do-not-launch-chrome"], ["update_chrome_user", "\"$MINI_INSTALLER\" \"$LOG_FILE\" --verbose-logging --do-not-launch-chrome"]], "tests": [{"name": "ChromeUserLevel", "description": "Verifies that user-level Chrome can be installed and uninstalled.", "traversal": ["no_pv", "install_chrome_user", "chrome_user_installed_not_inuse", "test_chrome_with_chromedriver_user", "chrome_user_installed_not_inuse", "uninstall_chrome_user", "clean"]}, {"name": "ChromeUserLevelUpdate", "description": "Verifies that user-level Chrome can be updated.", "traversal": ["no_pv", "install_chrome_user", "chrome_user_installed_not_inuse", "update_chrome_user", "chrome_user_updated_not_inuse", "test_chrome_with_chromedriver_user", "chrome_user_installed_not_inuse", "uninstall_chrome_user", "clean"]}, {"name": "ChromeBeta", "description": "Verifies that Chrome Beta can be installed and uninstalled.", "condition": "'$BRAND' == 'Google Chrome'", "traversal": ["no_pv", "install_chrome_beta", "chrome_beta_installed_not_inuse", "test_chrome_with_chromedriver_beta", "chrome_beta_installed_not_inuse", "uninstall_chrome_beta", "clean"]}, {"name": "ChromeCanary", "description": "Verifies that Chrome SxS can be installed and uninstalled.", "condition": "'$BRAND' == 'Google Chrome'", "traversal": ["no_pv", "install_chrome_canary", "chrome_canary_installed_not_inuse", "test_chrome_with_chromedriver_canary", "chrome_canary_installed_not_inuse", "uninstall_chrome_canary", "clean"]}, {"name": "ChromeCanaryUpdate", "description": "Verifies that Chrome SxS can be updated.", "condition": "'$BRAND' == 'Google Chrome'", "traversal": ["no_pv", "install_previous_chrome_canary", "previous_chrome_canary_installed_not_inuse", "update_chrome_canary", "chrome_canary_updated_not_inuse", "test_chrome_with_chromedriver_canary", "chrome_canary_installed_not_inuse", "uninstall_chrome_canary", "clean"]}, {"name": "ChromeDev", "description": "Verifies that Chrome Dev can be installed and uninstalled.", "condition": "'$BRAND' == 'Google Chrome'", "traversal": ["no_pv", "install_chrome_dev", "chrome_dev_installed_not_inuse", "test_chrome_with_chromedriver_dev", "chrome_dev_installed_not_inuse", "uninstall_chrome_dev", "clean"]}, {"name": "ChromeSystemLevel", "description": "Verifies that system-level Chrome can be installed and uninstalled.", "traversal": ["no_pv", "install_chrome_system", "chrome_system_installed_not_inuse", "test_chrome_with_chromedriver_system", "chrome_system_installed_not_inuse", "uninstall_chrome_system", "clean"]}, {"name": "ChromeSystemLevelUpdate", "description": "Verifies that system-level Chrome can be updated.", "traversal": ["no_pv", "install_previous_chrome_system", "previous_chrome_system_installed_not_inuse", "update_chrome_system", "chrome_system_updated_not_inuse", "test_chrome_with_chromedriver_system", "chrome_system_installed_not_inuse", "uninstall_chrome_system", "clean"]}, {"name": "AllChromeUserLevel", "description": "Verifies that user-level Chrome, Chrome Beta, Chrome Dev, and Chrome SxS can all be installed simultaneously.", "condition": "'$BRAND' == 'Google Chrome'", "traversal": ["no_pv", "install_chrome_user", "chrome_user_installed_not_inuse", "test_chrome_with_chromedriver_user", "chrome_user_installed_not_inuse", "install_chrome_beta", "chrome_user_and_beta_installed_not_inuse", "test_chrome_with_chromedriver_beta", "chrome_user_and_beta_installed_not_inuse", "install_chrome_dev", "chrome_user_and_beta_and_dev_installed_not_inuse", "test_chrome_with_chromedriver_dev", "chrome_user_and_beta_and_dev_installed_not_inuse", "install_chrome_canary", "chrome_user_and_beta_and_dev_and_canary_installed_not_inuse", "test_chrome_with_chromedriver_canary", "chrome_user_and_beta_and_dev_and_canary_installed_not_inuse", "uninstall_chrome_user", "chrome_beta_and_dev_and_canary_installed_not_inuse", "uninstall_chrome_beta", "chrome_dev_and_canary_installed_not_inuse", "uninstall_chrome_dev", "chrome_canary_installed_not_inuse", "uninstall_chrome_canary", "clean"]}]}