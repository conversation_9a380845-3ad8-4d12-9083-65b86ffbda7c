<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<script src="../resources/js-test.js"></script>
<script src="../resources/gc.js"></script>
</head>
<body>
<script>

description("Test that window.crypto wrapper protects all dependencies, so it can always be used to create crypto.subtle.");
jsTestIsAsync = true;

var anotherWindowCrypto;

function startTest()
{
    anotherWindowCrypto = frames[0].crypto;
    shouldBeDefined(anotherWindowCrypto);
    document.body.removeChild(document.getElementsByTagName("iframe")[0]);
    gc();
    setTimeout(continueTest, 10);
}

function continueTest()
{
    gc();
    setTimeout(finishTest, 10);
}

function finishTest()
{
    gc();
    shouldBeDefined(anotherWindowCrypto.subtle);
    finishJSTest();
}

window.onload = startTest;

</script>
<iframe src="about:blank"></iframe>
</body>
</html>
