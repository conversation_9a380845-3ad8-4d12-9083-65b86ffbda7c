/////////////////////////////////////////////////////////////////////////////
//
// Version
//

// Use the ordinal 1 here, to avoid needing to #include a header file
// to use the VS_VERSION_INFO macro. This header file changes with different
// SDK versions which causes headaches building in some environments. The
// VERSIONINFO resource will always be at index 1.
1 VERSIONINFO
 FILEVERSION @MAJOR@,@MINOR@,@BUILD@,@PATCH@
 PRODUCTVERSION @MAJOR@,@MINOR@,@BUILD@,@PATCH@
 FILEFLAGSMASK 0x17L
#ifdef _DEBUG
 FILEFLAGS 0x1L
#else
 FILEFLAGS 0x0L
#endif
 FILEOS 0x4L
 FILETYPE 0x1L
 FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904b0"
        BEGIN
            VALUE "CompanyName", "@COMPANY_FULLNAME@"
            VALUE "FileDescription", "@PRODUCT_INSTALLER_FULLNAME@"
            VALUE "FileVersion", "@MAJOR@.@MINOR@.@BUILD@.@PATCH@"
            VALUE "InternalName", "Chrome Updater"
            VALUE "LegalCopyright", "@COPYRIGHT@"
            VALUE "ProductName", "@PRODUCT_INSTALLER_FULLNAME@"
            VALUE "ProductVersion", "@MAJOR@.@MINOR@.@BUILD@.@PATCH@"
            VALUE "CompanyShortName", "@COMPANY_SHORTNAME@"
            VALUE "ProductShortName", "@PRODUCT_INSTALLER_SHORTNAME@"
            VALUE "LastChange", "@LASTCHANGE@"
            VALUE "Official Build", "@OFFICIAL_BUILD@"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200
    END
END
