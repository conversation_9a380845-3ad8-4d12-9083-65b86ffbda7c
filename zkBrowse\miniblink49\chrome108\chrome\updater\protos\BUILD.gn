# Copyright 2020 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//third_party/protobuf/proto_library.gni")

# This wrapper with empty |sources| is required to add "protobuf_config" as
# |all_dependent_configs|, in order to set proper |include_dirs| and |defines|.
# A "proto_library" target cannot set |all_dependent_configs| variable.
group("omaha_proto") {
  all_dependent_configs = [ "//third_party/protobuf:protobuf_config" ]
  public_deps = [
    ":proto",
    "//third_party/protobuf:protobuf_lite",
  ]
}

proto_library("proto") {
  proto_in_dir = "."
  sources = [
    "message_set.proto",
    "omaha_settings.proto",
  ]
  visibility = [ ":omaha_proto" ]
}
