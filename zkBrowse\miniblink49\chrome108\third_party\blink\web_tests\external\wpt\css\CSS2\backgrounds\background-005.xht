<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background with position</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <link rel="help" href="http://www.w3.org/TR/css3-background/#the-background-attachment" />
        <link rel="match" href="../reference/ref-nothing-below.xht" />
        <meta name="assert" content="Background shorthand with position only sets its background-position subproperty. In such case, the other background subproperties are set to their initial values: 'background-image' is set to 'none', 'background-color' is set to transparent, 'background-repeat' is set to 'repeat', 'background-attchment' is set to 'scroll'." />
        <style type="text/css">
            div
            {
                background: left top;
                height: 1in;
                width: 1in;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is nothing below.</p>
        <div></div>
    </body>
</html>