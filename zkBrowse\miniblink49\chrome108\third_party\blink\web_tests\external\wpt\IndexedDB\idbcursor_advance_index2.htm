<!DOCTYPE html>
<title>IDBCursor.advance() - attempt to pass a count parameter that is not a number</title>
<link rel="author" href="mailto:<EMAIL>" title="Odin <PERSON>">
<link rel=help href="http://dvcs.w3.org/hg/IndexedDB/raw-file/tip/Overview.html#widl-IDBCursor-advance-void-unsigned-long-count">
<link rel=assert title="The value passed into the count parameter was zero or a negative number.">
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<script src="resources/support.js"></script>

<script>

    var db,
      t = async_test(),
      records = [ { pKey: "primaryKey_0", iKey: "indexKey_0" },
                  { pKey: "primaryKey_1", iKey: "indexKey_1" } ];

    var open_rq = createdb(t);
    open_rq.onupgradeneeded = function(e) {
        db = e.target.result;
        var objStore = db.createObjectStore("test", {keyPath:"pKey"});

        objStore.createIndex("index", "iKey");

        for(var i = 0; i < records.length; i++)
            objStore.add(records[i]);
    };

    open_rq.onsuccess = function(e) {
        var cursor_rq = db.transaction("test")
                          .objectStore("test")
                          .index("index")
                          .openCursor();

        cursor_rq.onsuccess = t.step_func(function(e) {
            var cursor = e.target.result;

            assert_true(cursor != null, "cursor exist");
            assert_throws_js(TypeError,
                function() { cursor.advance(document); });

            t.done();
        });
    };

</script>

<div id="log"></div>
