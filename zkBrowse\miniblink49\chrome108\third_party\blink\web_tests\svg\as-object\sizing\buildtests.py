#!/usr/bin/env python

test_template = """<!DOCTYPE html>
<title>SVG sizing: &lt;object></title>
<meta name="timeout" content="long">
<script src="../../../resources/testharness.js"></script>
<script src="../../../resources/testharnessreport.js"></script>
<style>
  #testContainer {{
      position: absolute;
      left: 0;
      top: 0;
      width: 800px;
      height: 600px;
  }}
</style>
<link rel="help" href="http://www.w3.org/TR/CSS2/visudet.html#inline-replaced-width">
<link rel="help" href="http://www.w3.org/TR/CSS2/visudet.html#inline-replaced-height">
<link rel="help" href="http://www.whatwg.org/specs/web-apps/current-work/#replaced-elements">
<link rel="help" href="http://www.whatwg.org/specs/web-apps/current-work/#attr-dim-width">
<link rel="help" href="http://www.w3.org/TR/SVG/coords.html#ViewportSpace">
<div id="log"></div>
<div id="testContainer"></div>
<script src="svg-in-object.js"></script>
<!-- Test generated by buildtests.py -->
<script>testSVGInObjectWithPlaceholder({}, {}, {});</script>
"""

intrinsicRatios = [{'type': 'no-intrinsic-ratio', 'value': 'null'},
                   {'type': 'intrinsic-ratio', 'value': '"0 0 100 200"'}];

lengths = [{'type': 'auto', 'value': 'null'},
           {'type': 'fixed', 'value': '"100"'},
           {'type': 'percentage', 'value': '"50%"'}]

for intrinsicRatio in intrinsicRatios:
    for w in lengths:
        for h in lengths:
            with open("svg-in-object-placeholder-{}-{}-{}.html".format(w['type'], h['type'], intrinsicRatio['type']), "w") as f:
                f.write(test_template.format(w['value'], h['value'], intrinsicRatio['value']))
