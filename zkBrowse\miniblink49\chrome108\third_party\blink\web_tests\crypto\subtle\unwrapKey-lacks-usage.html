<!DOCTYPE html>
<html>
<head>
<script src="../../resources/js-test.js"></script>
<script src="resources/common.js"></script>
</head>
<body>
<p id="description"></p>
<div id="console"></div>

<script>
description("Tests that unwrapping keys must have the 'unwrapKey' usage.");

jsTestIsAsync = true;

function importUnwrappingKey()
{
    var data = new Uint8Array(16);
    var extractable = true;
    var keyUsages = ['decrypt'];

    return crypto.subtle.importKey('raw', data, {name: 'AES-CBC'}, extractable, keyUsages);
}

importUnwrappingKey().then(function(result) {
    wrappedKey = new Uint8Array(100);
    unwrappingKey = result;
    unwrapAlgorithm = {name: 'aes-cbc', iv: new Uint8Array(16)};
    unwrappedKeyAlgorithm = unwrapAlgorithm;
    extractable = true;
    keyUsages = ['encrypt'];

    return crypto.subtle.unwrapKey('raw', wrappedKey, unwrappingKey, unwrapAlgorithm, unwrappedKeyAlgorithm, extractable, keyUsages);
}).then(failAndFinishJSTest, function(result) {
    logError(result);
}).then(finishJSTest, failAndFinishJSTest);

</script>

</body>
</html>
