This test checks that Page Visibility state events are propagated to child frames.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".

Main Page:
PASS document.visibilityState is "visible"
PASS document.hidden is false
Child Frame:
PASS childFrame.contentDocument.visibilityState is "visible"
PASS childFrame.contentDocument.hidden is false
Main Page:
PASS document.visibilityState is "hidden"
PASS document.hidden is true
Child Frame:
PASS childFrame.contentDocument.visibilityState is "hidden"
PASS childFrame.contentDocument.hidden is true
Main Page:
PASS document.visibilityState is "visible"
PASS document.hidden is false
Child Frame:
PASS childFrame.contentDocument.visibilityState is "visible"
PASS childFrame.contentDocument.hidden is false
PASS successfullyParsed is true

TEST COMPLETE

