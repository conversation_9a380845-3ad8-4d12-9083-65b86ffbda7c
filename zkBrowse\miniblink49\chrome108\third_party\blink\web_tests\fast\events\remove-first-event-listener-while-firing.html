<!DOCTYPE html>
<script src="../../resources/js-test.js"></script>
<body>
<script>
description('Tests that removing the first, unexecuted event listener does ' +
            'not terminate event listener iteration');

messages = [];

function listener0() {
    messages.push('l0');
    event.target.removeEventListener('x', listener0);
    // listener1 is now the first event listener. Remove it.
    event.target.removeEventListener('x', listener1);
}

function listener1() {
    messages.push('l1');
}

function listener2() {
    messages.push('l2');
}

window.addEventListener('x', listener0);
window.addEventListener('x', listener1);
window.addEventListener('x', listener2);
window.dispatchEvent(new Event('x'));

shouldBe('messages.toString()', '"l0,l2"');
successfullyParsed = true;
</script>
