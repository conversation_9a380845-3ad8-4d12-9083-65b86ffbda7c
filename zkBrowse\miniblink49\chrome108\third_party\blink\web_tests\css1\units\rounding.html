<html>
<head>
<style type="text/css">
    body, div {
        margin: 0;
        padding: 0;
        border: 0;
    }
    #top, #bottom {
        line-height: 1.5;
        font-size: 70%;
        background:green;
        color:white;
        width:100%;
    }
    #top {
        padding:.6em 0 .7em;
    }
    #bottom {
      position:absolute;
      top:2.8em;
    }
</style>
</head>
<body>

<div id="top">no gap below</div>
<div id="bottom">no gap above</div>

<div id="description"></div>
<div id="console"></div>
<script src="../../resources/js-test.js"></script>
<script>

description("This test checks that floating point rounding doesn't cause misalignment.  There should be no gap between the divs.");

var divtop = document.getElementById("top").getBoundingClientRect();
var divbottom = document.getElementById("bottom").getBoundingClientRect();
shouldBe('Math.round(divtop.bottom)', '31');
shouldBe('Math.round(divbottom.top)', '31');

</script>


</body>
</html>
