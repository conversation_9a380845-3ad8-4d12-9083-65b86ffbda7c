# @GEN_HEADER@
# Copyright 2019 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

from .config import CodeSignConfig


class BuildPropsCodeSignConfig(CodeSignConfig):
    """The BuildPropsCodeSignConfig is processed by GN to inject build-time
    properties from the branding and version data.
    """

    @staticmethod
    def is_chrome_branded():
        return @IS_CHROME_BRANDED@

    @property
    def app_product(self):
        return '@PRODUCT_FULLNAME@'

    @property
    def product(self):
        return '@PRODUCT_FULLNAME@'

    @property
    def version(self):
        return '@MAJOR@.@MINOR@.@BUILD@.@PATCH@'

    @property
    def base_bundle_id(self):
        return '@MAC_BUNDLE_ID@'

    @property
    def keystone_app_name(self):
        return '@KEYSTONE_APP_NAME@'
