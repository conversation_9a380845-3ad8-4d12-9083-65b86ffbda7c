[input-events-get-target-ranges-backspace.tentative.html]
  expected: OK

  [Backspace at "<p>[\]abc</p>"]
    expected: PASS

  [Backspace at "<p>a[\]bc</p>"]
    expected: PASS

  [Backspace at "<p>ab[\]c</p>"]
    expected: PASS

  [Backspace at "<p>abc[\]</p>"]
    expected: PASS

  [Backspace at "<p>a<span>b</span>[\]c</p>"]
    expected: FAIL

  [Backspace at "<p>a<span>b[\]</span>c</p>"]
    expected: FAIL

  [Backspace at "<p> a[\]bc</p>"]
    expected: FAIL

  [Backspace at "<p>abc</p><p>[\]def</p>"]
    expected: FAIL

  [Backspace at "<p>abc   </p><p>   [\]def</p>"]
    expected: FAIL

  [Backspace at "<p>abc   </p><p>  [\] def</p>"]
    expected: FAIL

  [Backspace at "<p>abc   </p><p> [\]  def</p>"]
    expected: FAIL

  [Backspace at "<p>abc   </p><p>[\]   def</p>"]
    expected: FAIL

  [Backspace at "<p>abc</p><p><b>[\]def</b></p>"]
    expected: FAIL

  [Backspace at "<p><b>abc</b></p><p><b>[\]def</b></p>"]
    expected: FAIL

  [Backspace at "<p><i>abc</i></p><p><b>[\]def</b></p>"]
    expected: FAIL

  [Backspace at "<pre>abc   </pre><p>   [\]def</p>"]
    expected: FAIL

  [Backspace at "<pre>abc   </pre><p>   [\]def   </p>"]
    expected: FAIL

  [Backspace at "<p>abc   </p><pre>[\]   def</pre>"]
    expected: FAIL

  [Backspace at "<p style="white-space:pre-line">abc\n[\]def</p>"]
    expected: PASS

  [Backspace at "<p style="white-space:pre-line">abc \n[\]def</p>"]
    expected: FAIL

  [Backspace at "<p style="white-space:pre-line">abc\n [\]def</p>"]
    expected: FAIL

  [Backspace at "<p style="white-space:pre-line">abc \n [\]def</p>"]
    expected: FAIL

  [Backspace at "<p style="white-space:pre-line">abc \n \n [\]def</p>"]
    expected: FAIL

  [Backspace at "<p>abc<br></p><p>[\]def</p>"]
    expected: FAIL

  [Backspace at "<p>abc<br><br></p><p>[\]def</p>"]
    expected: FAIL

  [Backspace at "<p>abc<br>[\]def</p>"]
    expected: PASS

  [Backspace at "<p>abc <br>[\]def</p>"]
    expected: PASS

  [Backspace at "<p>abc<img>[\]def</p>"]
    expected: PASS

  [Backspace at "<p>abc <img>[\]def</p>"]
    expected: PASS

  [Backspace at "<p>abc<img>[\] def</p>"]
    expected: PASS

  [Backspace at "<p>abc<img>{}<img>def</p>"]
    expected: PASS

  [Backspace at "<p>abc<img><img>[\]def</p>"]
    expected: PASS

  [Backspace at "<div>abc<hr>[\]def</div>"]
    expected: FAIL

  [Backspace at "<div>abc <hr>[\]def</div>"]
    expected: FAIL

  [Backspace at "<div>abc<hr> [\]def</div>"]
    expected: FAIL

  [Backspace at "<div>abc<br><hr>[\]def</div>"]
    expected: FAIL

  [Backspace at "<p>abc<br>[\] def</p>"]
    expected: FAIL

  [Backspace at "<p>abc<br> [\]def</p>"]
    expected: FAIL

  [Backspace at "<div>abc<p>[\]def<br>ghi</p></div>"]
    expected: FAIL

  [Backspace at "<div>abc   <p>   [\]def<br>ghi</p></div>"]
    expected: FAIL

  [Backspace at "<div>abc<p><b>[\]def</b></p></div>"]
    expected: FAIL

  [Backspace at "<div><b>abc</b><p><b>[\]def</b></p></div>"]
    expected: FAIL

  [Backspace at "<div><i>abc</i><p><b>[\]def</b></p></div>"]
    expected: FAIL

  [Backspace at "<div><p>abc</p>[\]def</div>"]
    expected: PASS

  [Backspace at "<div><p>abc   </p>   [\]def</div>"]
    expected: FAIL

  [Backspace at "<div><p><b>abc</b></p>[\]def</div>"]
    expected: PASS

  [Backspace at "<div><p><b>abc</b></p><b>[\]def</b></div>"]
    expected: FAIL

  [Backspace at "<div><p><b>abc</b></p><i>[\]def</i></div>"]
    expected: FAIL

  [Backspace at "<div>abc<ul><li>[\]def</li></ul>ghi</div>"]
    expected: FAIL

  [Backspace at "<div>abc  <ul><li> [\]def </li></ul>  ghi</div>"]
    expected: FAIL

  [Backspace at "<div>abc  <ul><li>[\] def </li></ul>  ghi</div>"]
    expected: FAIL

  [Backspace at "<div>abc<ul><li>def</li></ul>[\]ghi</div>"]
    expected: PASS

  [Backspace at "<div>abc <ul><li>  def  </li></ul> [\]ghi</div>"]
    expected: FAIL

  [Backspace at "<div>abc <ul><li>  def  </li></ul>[\] ghi</div>"]
    expected: FAIL

  [Backspace at "<div>abc<ul><li>[\]def</li><li>ghi</li></ul>jkl</div>"]
    expected: FAIL

  [Backspace at "<div>abc<ul><li>def</li><li>[\]ghi</li></ul>jkl</div>"]
    expected: FAIL

  [Backspace at "<div>abc<ul><li>def</li><li>ghi</li></ul>[\]jkl</div>"]
    expected: FAIL

  [Backspace at "<p>abc</p><p>{}<br></p>"]
    expected: FAIL

  [Backspace at "<p>abc</p><p><span>{}</span><br></p>"]
    expected: FAIL

  [Backspace at "<p>abc  </p><p>{}<br></p>"]
    expected: FAIL

  [Backspace at "<p>abc<span contenteditable="false">def</span></p><p>{}<br></p>"]
    expected: FAIL

  [Backspace at "<p>abc</p><p contenteditable="false">def</p><p>{}<br></p>"]
    expected: FAIL

  [Backspace at "<p>abc<span contenteditable="false">def</span>[\]ghi</p>"]
    expected: FAIL

  [Backspace at "<table><tr><td>cell</td></tr></table><p>{}<br></p>"]
    expected: FAIL

  [Backspace at "<table><tr><td>cell1</td><td>{}<br></td></tr></table>"]
    expected: FAIL

  [Backspace at "<p>abc</p><table><caption>{}<br></caption><tr><td>cell</td></tr></table>"]
    expected: FAIL

  [Backspace at "<table><tr>{<td>cell1</td>}<td>cell2</td></tr></table>"]
    expected: FAIL

  [Backspace at "<table><tr><td>cell1</td>{<td>cell2</td>}</tr></table>"]
    expected: FAIL

  [Backspace at "<table><tr>{<td>cell</td>}</tr></table>"]
    expected: FAIL

  [Backspace at "<table><tr>{<td>cell1</td>}<td>cell2</td></tr><tr><td>cell3</td>{<td>cell4</td>}</tr></table>"]
    expected: FAIL

  [Backspace at "<table><tr>{<td>cell1</td>}<td>cell2</td></tr><tr>{<td>cell3</td>}<td>cell4</td></tr></table>"]
    expected: FAIL

  [Backspace at "<table><tr>{<td>cell1</td>}{<td>cell2</td>}</tr><tr><td>cell3</td><td>cell4</td></tr></table>"]
    expected: FAIL

  [Backspace at "<table><tr><td>cell1</td><td>cell2</td></tr><tr>{<td>cell3</td>}{<td>cell4</td>}</tr></table>"]
    expected: FAIL

  [Backspace at "<table><tr>{<td>cell1</td>}{<td>cell2</td>}</tr><tr>{<td>cell3</td>}{<td>cell4</td>}</tr></table>"]
    expected: FAIL

  [Backspace at "<table><tr>{<td>cell1</td>}<td>c[ell\]2</td></tr><tr>{<td>cell3</td>}<td>cell4</td></tr></table>"]
    expected: FAIL

  [Backspace at "<p>&#x5E9;&#x5DC;&#x5D5;&#x5DD;[\]hello</p>"]
    expected: PASS

  [Shift + Backspace at "<p>abc def[\] ghi</p>"]
    expected: PASS

  [Control + Backspace at "<p>abc def[\] ghi</p>"]
    expected: PASS

  [Alt + Backspace at "<p>abc def[\] ghi</p>"]
    expected: PASS

  [Meta + Backspace at "<p>abc def[\] ghi</p>"]
    expected: PASS

  [Shift + Backspace at "<p>   abc[\] def</p>"]
    expected: FAIL

  [Control + Backspace at "<p>   abc[\] def</p>"]
    expected: FAIL

  [Alt + Backspace at "<p>   abc[\] def</p>"]
    expected: FAIL

  [Meta + Backspace at "<p>   abc[\] def</p>"]
    expected: FAIL

  [Backspace at "<p>[\]abc</p>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<p>a[\]bc</p>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<p>ab[\]c</p>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<p>abc[\]</p>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<p>a<span>b</span>[\]c</p>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<p>a<span>b[\]</span>c</p>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<p> a[\]bc</p>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<p>abc</p><p>[\]def</p>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<p>abc   </p><p>   [\]def</p>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<p>abc   </p><p>  [\] def</p>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<p>abc   </p><p> [\]  def</p>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<p>abc   </p><p>[\]   def</p>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<p>abc</p><p><b>[\]def</b></p>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<p><b>abc</b></p><p><b>[\]def</b></p>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<p><i>abc</i></p><p><b>[\]def</b></p>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<pre>abc   </pre><p>   [\]def</p>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<pre>abc   </pre><p>   [\]def   </p>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<p>abc   </p><pre>[\]   def</pre>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<p style="white-space:pre-line">abc\n[\]def</p>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<p style="white-space:pre-line">abc \n[\]def</p>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<p style="white-space:pre-line">abc\n [\]def</p>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<p style="white-space:pre-line">abc \n [\]def</p>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<p style="white-space:pre-line">abc \n \n [\]def</p>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<p>abc<br></p><p>[\]def</p>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<p>abc<br><br></p><p>[\]def</p>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<p>abc<br>[\]def</p>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<p>abc <br>[\]def</p>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<p>abc<img>[\]def</p>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<p>abc <img>[\]def</p>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<p>abc<img>[\] def</p>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<p>abc<img>{}<img>def</p>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<p>abc<img><img>[\]def</p>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<div>abc<hr>[\]def</div>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<div>abc <hr>[\]def</div>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<div>abc<hr> [\]def</div>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<div>abc<br><hr>[\]def</div>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<p>abc<br>[\] def</p>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<p>abc<br> [\]def</p>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<div>abc<p>[\]def<br>ghi</p></div>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<div>abc   <p>   [\]def<br>ghi</p></div>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<div>abc<p><b>[\]def</b></p></div>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<div><b>abc</b><p><b>[\]def</b></p></div>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<div><i>abc</i><p><b>[\]def</b></p></div>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<div><p>abc</p>[\]def</div>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<div><p>abc   </p>   [\]def</div>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<div><p><b>abc</b></p>[\]def</div>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<div><p><b>abc</b></p><b>[\]def</b></div>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<div><p><b>abc</b></p><i>[\]def</i></div>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<div>abc<ul><li>[\]def</li></ul>ghi</div>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<div>abc  <ul><li> [\]def </li></ul>  ghi</div>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<div>abc  <ul><li>[\] def </li></ul>  ghi</div>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<div>abc<ul><li>def</li></ul>[\]ghi</div>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<div>abc <ul><li>  def  </li></ul> [\]ghi</div>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<div>abc <ul><li>  def  </li></ul>[\] ghi</div>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<div>abc<ul><li>[\]def</li><li>ghi</li></ul>jkl</div>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<div>abc<ul><li>def</li><li>[\]ghi</li></ul>jkl</div>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<p>abc</p><p>{}<br></p>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<p>abc</p><p><span>{}</span><br></p>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<p>abc  </p><p>{}<br></p>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<p>abc<span contenteditable="false">def</span></p><p>{}<br></p>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<p>abc</p><p contenteditable="false">def</p><p>{}<br></p>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<p>abc<span contenteditable="false">def</span>[\]ghi</p>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<table><tr><td>cell</td></tr></table><p>{}<br></p>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<table><tr>{<td>cell1</td>}<td>cell2</td></tr></table>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<table><tr><td>cell1</td>{<td>cell2</td>}</tr></table>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<table><tr>{<td>cell</td>}</tr></table>" - comparing innerHTML]
    expected: PASS

  [Backspace at "<p>&#x5E9;&#x5DC;&#x5D5;&#x5DD;[\]hello</p>" - comparing innerHTML]
    expected: PASS

  [Shift + Backspace at "<p>abc def[\] ghi</p>" - comparing innerHTML]
    expected: PASS

  [Control + Backspace at "<p>abc def[\] ghi</p>" - comparing innerHTML]
    expected: PASS

  [Alt + Backspace at "<p>abc def[\] ghi</p>" - comparing innerHTML]
    expected: PASS

  [Meta + Backspace at "<p>abc def[\] ghi</p>" - comparing innerHTML]
    expected: PASS

  [Shift + Backspace at "<p>   abc[\] def</p>" - comparing innerHTML]
    expected: PASS

  [Control + Backspace at "<p>   abc[\] def</p>" - comparing innerHTML]
    expected: PASS

  [Alt + Backspace at "<p>   abc[\] def</p>" - comparing innerHTML]
    expected: PASS

  [Meta + Backspace at "<p>   abc[\] def</p>" - comparing innerHTML]
    expected: PASS
