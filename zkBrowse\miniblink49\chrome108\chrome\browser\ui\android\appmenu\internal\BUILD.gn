# Copyright 2019 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/android/rules.gni")

android_library("java") {
  visibility = [
    ":*",
    "//chrome/android:chrome_all_java",
    "//chrome/browser/ui/android/appmenu/test:*",
  ]

  sources = [
    "java/src/org/chromium/chrome/browser/ui/appmenu/AppMenu.java",
    "java/src/org/chromium/chrome/browser/ui/appmenu/AppMenuButtonHelperImpl.java",
    "java/src/org/chromium/chrome/browser/ui/appmenu/AppMenuCoordinatorFactory.java",
    "java/src/org/chromium/chrome/browser/ui/appmenu/AppMenuCoordinatorImpl.java",
    "java/src/org/chromium/chrome/browser/ui/appmenu/AppMenuDragHelper.java",
    "java/src/org/chromium/chrome/browser/ui/appmenu/AppMenuHandlerImpl.java",
    "java/src/org/chromium/chrome/browser/ui/appmenu/AppMenuItemIcon.java",
    "java/src/org/chromium/chrome/browser/ui/appmenu/AppMenuItemViewBinder.java",
  ]

  deps = [
    ":java_resources",
    "//base:base_java",
    "//chrome/browser/android/lifecycle:java",
    "//chrome/browser/flags:java",
    "//chrome/browser/ui/android/appmenu:java",
    "//components/browser_ui/styles/android:java",
    "//components/browser_ui/widget/android:java",
    "//third_party/androidx:androidx_annotation_annotation_java",
    "//third_party/androidx:androidx_appcompat_appcompat_resources_java",
    "//third_party/androidx:androidx_core_core_java",
    "//ui/android:ui_java",
  ]
  resources_package = "org.chromium.chrome.browser.ui.appmenu.internal"
}

android_resources("java_resources") {
  sources = [
    "java/res/drawable-v23/menu_action_bar_bg.xml",
    "java/res/drawable-v31/menu_action_bar_bg.xml",
    "java/res/layout/icon_row_menu_item.xml",
    "java/res/layout/menu_item.xml",
    "java/res/layout/menu_item_start_with_icon.xml",
    "java/res/layout/title_button_menu_item.xml",
    "java/res/values-night/dimens.xml",
    "java/res/values/dimens.xml",
  ]

  # Include resource dependencies needed by :java so that resources may
  # all be accessed through the same custom_package.
  deps = [
    "//chrome/browser/ui/android/appmenu:java_resources",
    "//components/browser_ui/widget/android:java_resources",
  ]
}

android_library("unit_device_javatests") {
  testonly = true

  resources_package = "org.chromium.chrome.browser.ui.appmenu.test"
  sources = [
    "java/src/org/chromium/chrome/browser/ui/appmenu/AppMenuItemViewBinderRenderTest.java",
    "java/src/org/chromium/chrome/browser/ui/appmenu/AppMenuItemViewBinderTest.java",
    "java/src/org/chromium/chrome/browser/ui/appmenu/AppMenuTest.java",
    "java/src/org/chromium/chrome/browser/ui/appmenu/TestAppMenuDelegate.java",
    "java/src/org/chromium/chrome/browser/ui/appmenu/TestAppMenuPropertiesDelegate.java",
  ]
  deps = [
    ":java",
    ":test_java_resources",
    "//base:base_java",
    "//base:base_java_test_support",
    "//chrome/browser/android/lifecycle:java",
    "//chrome/browser/flags:java",
    "//chrome/browser/ui/android/appmenu:java",
    "//chrome/browser/ui/android/appmenu/test:test_support_java",
    "//chrome/test/android:chrome_java_integration_test_support",
    "//components/browser_ui/widget/android:java",
    "//components/browser_ui/widget/android:test_support_java",
    "//content/public/test/android:content_java_test_support",
    "//third_party/android_support_test_runner:rules_java",
    "//third_party/android_support_test_runner:runner_java",
    "//third_party/androidx:androidx_annotation_annotation_java",
    "//third_party/androidx:androidx_appcompat_appcompat_resources_java",
    "//third_party/androidx:androidx_test_runner_java",
    "//third_party/hamcrest:hamcrest_library_java",
    "//third_party/junit",
    "//third_party/mockito:mockito_java",
    "//ui/android:ui_full_java",
    "//ui/android:ui_java_test_support",
  ]
}

android_resources("test_java_resources") {
  testonly = true

  sources = [
    "test/java/res/layout/test_app_menu_activity_layout.xml",
    "test/java/res/layout/test_menu_footer.xml",
    "test/java/res/layout/test_menu_header.xml",
    "test/java/res/menu/test_menu.xml",
  ]
  deps = [
    ":java_resources",
    "//components/browser_ui/widget/android:java_test_resources",
  ]
}

robolectric_library("junit") {
  sources = [ "java/src/org/chromium/chrome/browser/ui/appmenu/AppMenuPopupPositionTest.java" ]
  deps = [
    ":java",
    "//base:base_junit_test_support",
    "//third_party/junit",
    "//third_party/mockito:mockito_java",
  ]
}
