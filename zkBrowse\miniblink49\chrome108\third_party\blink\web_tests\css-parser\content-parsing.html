<!doctype html>
<script src="../resources/testharness.js"></script>
<script src="../resources/testharnessreport.js"></script>
<script src="resources/property-parsing-test.js"></script>
<script>
assert_valid_value("content", '"a"');
assert_valid_value("content", "open-quote");
assert_valid_value("content", "normal");
assert_valid_value("content", "none");
assert_valid_value("content", 'url("test.html")');
assert_valid_value("content", 'url("test.html") / "alt"');
assert_valid_value("content", '"test" / "alt"');

assert_invalid_value("content", 'normal normal');
assert_invalid_value("content", 'normal "a"');
assert_invalid_value("content", 'normal "a" / "alt"');
assert_invalid_value("content", 'normal url("test.html")');
assert_invalid_value("content", "normal open-quote");
assert_invalid_value("content", '"a" normal');
assert_invalid_value("content", 'url("test.html") normal');
assert_invalid_value("content", "'open-quote' normal");
assert_invalid_value("content", 'normal none');
assert_invalid_value("content", 'none normal');
assert_invalid_value("content", 'none none');
assert_invalid_value("content", 'none "a"');
assert_invalid_value("content", 'none url("test.html")');
assert_invalid_value("content", "none open-quote");
assert_invalid_value("content", '"a" none');
assert_invalid_value("content", 'url("test.html") none');
assert_invalid_value("content", "open-quote none");
assert_invalid_value("content", "not valid content");
assert_invalid_value("content", "open-quote invalid content");
assert_invalid_value("content", '"a" still not valid');
assert_invalid_value("content", 'bla &@#$&^');
assert_invalid_value("content", "'foo' *(&^");
assert_invalid_value("content", "open-quote 1+2+3=6");
assert_invalid_value("content", '"thing" / "alt1" "alt2"');
assert_invalid_value("content", '"thing" / ');
</script>
