<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.3.6 background-position</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
BODY {background-image: url(../resources/bg.gif); background-position: right top;
      background-repeat: no-repeat;}
.one {background-image: url(../resources/bg.gif); background-position: center;
      background-repeat: no-repeat; background-color: aqua;}
.two {background-image: url(../resources/bg.gif); background-position: 50% 50%;
      background-repeat: no-repeat; background-color: aqua;}
.three {background-image: url(../resources/bg.gif); background-position: bottom right;
        background-repeat: no-repeat; background-color: aqua;}
.four {background-image: url(../resources/bg.gif); background-position: 100% 100%;
       background-repeat: no-repeat; background-color: aqua;}
.five {background-image: url(../resources/bg.gif); background-position: 0% 50%;
       background-repeat: no-repeat; background-color: aqua;}
.six {background-image: url(../resources/bg.gif); background-position: 75% 25%;
       background-repeat: no-repeat; background-color: aqua;}
.seven {background-image: url(../resources/bg.gif); background-position: 20px 20px;
       background-repeat: no-repeat; background-color: aqua;}
</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>BODY {background-image: url(../resources/bg.gif); background-position: right top;
      background-repeat: no-repeat;}
.one {background-image: url(../resources/bg.gif); background-position: center;
      background-repeat: no-repeat; background-color: aqua;}
.two {background-image: url(../resources/bg.gif); background-position: 50% 50%;
      background-repeat: no-repeat; background-color: aqua;}
.three {background-image: url(../resources/bg.gif); background-position: bottom right;
        background-repeat: no-repeat; background-color: aqua;}
.four {background-image: url(../resources/bg.gif); background-position: 100% 100%;
       background-repeat: no-repeat; background-color: aqua;}
.five {background-image: url(../resources/bg.gif); background-position: 0% 50%;
       background-repeat: no-repeat; background-color: aqua;}
.six {background-image: url(../resources/bg.gif); background-position: 75% 25%;
       background-repeat: no-repeat; background-color: aqua;}
.seven {background-image: url(../resources/bg.gif); background-position: 20px 20px;
       background-repeat: no-repeat; background-color: aqua;}

</PRE>
<HR>
<P>
This document should have a single, small green image in its upper right corner.
</P>
<P class="one">
This paragraph should have a single, small green image exactly in its center; that is, the center of the image should be fixed at the center of the paragraph.  The background color will make it easier to determine the edges of the paragraph, and therefore allow you to calculate its center. 
</P>
<P class="two">
This paragraph should have a single, small green image exactly in its center; that is, the center of the image should be fixed at the center of the paragraph.  The background color will make it easier to determine the edges of the paragraph, and therefore allow you to calculate its center. 
</P>
<P class="three">
This paragraph should have a single, small green image in its lower-right corner; that is, the lower right corner of the image should be fixed at the lower right corner of the paragraph.  The background color will make it easier to determine the edges of the paragraph, and therefore allow you to see its corners. 
</P>
<P class="four">
This paragraph should have a single, small green image in its lower-right corner; that is, the lower right corner of the image should be fixed at the lower right corner of the paragraph.  The background color will make it easier to determine the edges of the paragraph, and therefore allow you to see its corners. 
</P>
<P class="five">
This paragraph should have a single, small green image exactly at the left center; that is, the left center of the image should be fixed at the left center of the paragraph.  The background color will make it easier to determine the edges of the paragraph, and therefore allow you to calculate its center. 
</P>
<P class="six">
This paragraph should have a single, small green image positioned 75% of the way across the element, and 25% down.  The background color will make it easier to determine the edges of the paragraph, which should help in determining if all this is so, and the extra text should make the element long enough for this test to be simpler to evaluate.
</P>
<P class="seven">
This paragraph should have a single, small green image positioned 20 pixels down and to the left of the upper left-hand corner; that is, the upper left-hand corner of the image should be 20 pixels down and to the left of the upper-left corner of the element.  The background color will make it easier to determine the edges of the paragraph, which should assist in evaluating this test.
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P>
This document should have a single, small green image in its upper right corner.
</P>
<P class="one">
This paragraph should have a single, small green image exactly in its center; that is, the center of the image should be fixed at the center of the paragraph.  The background color will make it easier to determine the edges of the paragraph, and therefore allow you to calculate its center. 
</P>
<P class="two">
This paragraph should have a single, small green image exactly in its center; that is, the center of the image should be fixed at the center of the paragraph.  The background color will make it easier to determine the edges of the paragraph, and therefore allow you to calculate its center. 
</P>
<P class="three">
This paragraph should have a single, small green image in its lower-right corner; that is, the lower right corner of the image should be fixed at the lower right corner of the paragraph.  The background color will make it easier to determine the edges of the paragraph, and therefore allow you to see its corners. 
</P>
<P class="four">
This paragraph should have a single, small green image in its lower-right corner; that is, the lower right corner of the image should be fixed at the lower right corner of the paragraph.  The background color will make it easier to determine the edges of the paragraph, and therefore allow you to see its corners. 
</P>
<P class="five">
This paragraph should have a single, small green image exactly at the left center; that is, the left center of the image should be fixed at the left center of the paragraph.  The background color will make it easier to determine the edges of the paragraph, and therefore allow you to calculate its center. 
</P>
<P class="six">
This paragraph should have a single, small green image positioned 75% of the way across the element, and 25% down.  The background color will make it easier to determine the edges of the paragraph, which should help in determining if all this is so, and the extra text should make the element long enough for this test to be simpler to evaluate.
</P>
<P class="seven">
This paragraph should have a single, small green image positioned 20 pixels down and to the left of the upper left-hand corner; that is, the upper left-hand corner of the image should be 20 pixels down and to the left of the upper-left corner of the element.  The background color will make it easier to determine the edges of the paragraph, which should assist in evaluating this test.
</P>
</TD></TR></TABLE></BODY>
</HTML>
