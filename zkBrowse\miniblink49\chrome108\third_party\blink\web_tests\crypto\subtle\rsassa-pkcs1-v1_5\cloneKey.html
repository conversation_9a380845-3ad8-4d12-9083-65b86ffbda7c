<!DOCTYPE html>
<html>
<head>
<script src="../../../resources/js-test.js"></script>
<script src="../resources/common.js"></script>
<script src="../resources/rsa-cloneKey.js"></script>
<script src="../resources/keys.js"></script>
</head>
<body>
<script>
description('Tests structured cloning of RSASSA-PKCS1-v1_5 keys');

jsTestIsAsync = true;

function testClonePublicKeys()
{
    return testCloneRSAPublicKeys('RSASSA-PKCS1-v1_5', [[], ['verify']], [kKeyData.rsa2, kKeyData.rsa3]);
}

function testClonePrivateKeys()
{
    return testCloneRSAPrivateKeys('RSASSA-PKCS1-v1_5', [['sign']], [kKeyData.rsa1, kKeyData.rsa4]);
}

testClonePublicKeys().then(testClonePrivateKeys).then(finishJSTest, failAndFinishJSTest)
</script>
</body>
</html>
