<svg xmlns="http://www.w3.org/2000/svg">
<defs>
    <clipPath id="clip1" clipPathUnits="objectBoundingBox">
        <circle cx="0" cy="0" r="0.5" />
        <circle cx="0" cy="1" r="0.5" />
    </clipPath>

    <clipPath id="clip2" clipPathUnits="objectBoundingBox">
        <circle cx="1" cy="0" r="0.5" />
        <circle cx="1" cy="1" r="0.5" />
    </clipPath>

    <clipPath id="clip" clipPathUnits="objectBoundingBox">
        <rect x="0" y="0" width="1" height="1" clip-path="url(#clip1)"/>
        <rect x="0" y="0" width="1" height="1" clip-path="url(#clip2)"/>
    </clipPath>

    <mask id="mask1a" x="0" y="0" width="1" height="1" maskContentUnits="objectBoundingBox">
        <rect x="0" y="0" width="1" height="1" fill="white"/>
        <rect x="0" y="0" width="1" height="1" fill="black" clip-path="url(#clip1)" />
    </mask>

    <mask id="mask1b" x="0" y="0" width="1" height="1" maskContentUnits="objectBoundingBox">
        <rect x="0" y="0" width="1" height="1" fill="white"/>
        <rect x="0" y="0" width="1" height="1" fill="black" clip-path="url(#clip2)" />
    </mask>

    <mask id="mask2" x="0" y="0" width="1" height="1" maskContentUnits="objectBoundingBox">
        <rect x="0" y="0" width="1" height="1" fill="white" mask="url(#mask1a)"/>
    </mask>

    <mask id="mask3" x="0" y="0" width="1" height="1" maskContentUnits="objectBoundingBox">
        <rect x="0" y="0" width="1" height="1" fill="white" mask="url(#mask1b)"/>
    </mask>
</defs>

<!-- The mask on the <g> causes nothing to be renderer with Opera, clearly a bug -->
<g mask="url(#mask1a)">
    <circle cx="50" cy="50" r="50" fill="black" mask="url(#mask1b)"/>
</g>

<!-- The mask on the <g> causes nothing to be renderer with Opera, clearly a bug -->
<g mask="url(#mask3)">
    <circle cx="150" cy="150" r="50" fill="black" mask="url(#mask2)"/>
</g>

<text x="250" y="250" text-anchor="middle">The left shape should still be visible after panning<tspan x="250" dy="25">And the right shape shouldn't look distorted</tspan></text>
<script>
var translate = document.getElementsByTagName('svg')[0].currentTranslate;
translate.x = -75;
</script>
</svg>
