<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background with repeat</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="<PERSON><PERSON>" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-04-17 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <link rel="help" href="http://www.w3.org/TR/css3-background/#the-background-repeat" />
        <link rel="match" href="background-003-ref.xht" />
        <meta name="flags" content="image" />
        <meta name="assert" content="Background shorthand with repeat only sets its background-repeat subproperty. In such case, the other background subproperties are set to their initial values: 'background-image' is set to 'none', 'background-color' is set to transparent, 'background-attachment' is set to 'scroll', 'background-position' is set to '0% 0%'." />
        <style type="text/css">
            div
            {
                background: repeat-x;
                background-image: url("support/green15x15.png");
                height: 5in;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is a green stripe across the page.</p>
        <div></div>
    </body>
</html>