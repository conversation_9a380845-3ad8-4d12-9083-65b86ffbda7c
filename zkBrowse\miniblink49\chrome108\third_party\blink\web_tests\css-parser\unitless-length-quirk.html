<script src="../resources/testharness.js"></script>
<script src="../resources/testharnessreport.js"></script>
<script src="resources/property-parsing-test.js"></script>
<script>
// Intentionally testing in quirks mode
assert_valid_value("paddingLeft", "20", "20px", true);
assert_valid_value("top", "30", "30px", true);
assert_valid_value("minWidth", "40", "40px", true);

assert_invalid_value("outlineWidth", "50");
assert_invalid_value("offsetDistance", "60");
assert_invalid_value("paddingInlineStart", "12");
assert_invalid_value("marginBlockStart", "24");
</script>
