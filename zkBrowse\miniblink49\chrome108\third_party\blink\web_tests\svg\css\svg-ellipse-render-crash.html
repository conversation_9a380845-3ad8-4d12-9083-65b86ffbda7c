<html>
<head>
    <style type="text/css">
      .cls1 {
        stroke: black;
        fill: rgb(0,255,0);
        stroke-width: 1;
      }
    </style>

<script type="text/javascript">
function setup() {
    if (window.testRunner)
        testRunner.dumpAsText();
    var svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
    svg.width.baseVal.valueAsString = "400px";
    svg.height.baseVal.valueAsString = "400px";
    svg.viewBox.baseVal.x = 0;
    svg.viewBox.baseVal.y = 0;
    svg.viewBox.baseVal.width = 90;
    svg.viewBox.baseVal.height = 90;
    var ellipse = document.createElementNS("http://www.w3.org/2000/svg", "ellipse");
    ellipse.cx.baseVal.value = 50;
    ellipse.cy.baseVal.value = 50;
    ellipse.rx.baseVal.value = 30;
    ellipse.ry.baseVal.value = 10;
    ellipse.className.baseVal = "cls1";
    var drawing = document.getElementById("drawing");
    svg.appendChild(ellipse);
    drawing.appendChild(svg);
}
</script>
</head>
<body onload="setup()">
<p>Here is an html paragraph. And below is a svg drawing. This should render without crashing.</p>
<div id="drawing"/>
</body>
</html>


