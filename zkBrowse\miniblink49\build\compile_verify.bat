@echo off
echo Compiling V8 Version Verification Tool
echo =====================================

call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Enterprise\VC\Auxiliary\Build\vcvars32.bat"

cl /I"../v8_10_8/include" verify_v8_version.cpp /Fe:verify_v8_version.exe

if exist verify_v8_version.exe (
    echo.
    echo Compilation successful. Running verification...
    echo.
    verify_v8_version.exe
) else (
    echo.
    echo Compilation failed!
)

pause
