<!DOCTYPE html>
<title>
  Test for 'object-fit: cover' on SVG image w/ aspect ratio but no
  intrinsic size
</title>
<style type="text/css">
  img {
      object-fit: cover;
      background: red;
      width: 160px;
      height: 100px;
  }
</style>
<img src="data:image/svg+xml,
     <svg xmlns='http://www.w3.org/2000/svg'
          viewBox='0 0 16 16'>
       <rect stroke-width='2' stroke='black'
         x='1' y='1' width='14' height='14' fill='lime'/>
     </svg>">
