<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Border-width set using a single value</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="G<PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-06-26 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#border-width-properties" />
		<link rel="match" href="border-width-shorthand-001-ref.xht" />

        <meta name="assert" content="Applying a single value to the 'border-width' property applies the value to all sides of the element." />
        <style type="text/css">
            div
            {
                border-width: 10px;
                border-style: solid;
                height: 1in;
            }
        </style>
    </head>
    <body>
        <p>Test passes if the border is the same on all edges.</p>
        <div></div>
    </body>
</html>