<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.5.23 width</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
.one {width: 50px;}
.two {width: 50%;}
TABLE {width: 50%;}</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.one {width: 50px;}
.two {width: 50%;}
TABLE {width: 50%;}
</PRE>
<HR>
<IMG SRC="../resources/oransqr.gif" class="one" alt="[Image]">
<P>
The square above should be fifty pixels wide.
</P>
<IMG SRC="../resources/oransqr.gif" class="two" alt="[Image]">
<P>
The square above should be half as wide as the image's parent element (either the BODY or the table cell).
</P>
<P class="two">
This paragraph should be half the width of its parent element (either the BODY or the table, which should itself be half as wide as the BODY element).  This is extra text included to ensure that this will be a fair test of the <CODE>width</CODE> property without the need for the user to resize the viewing window.
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><IMG SRC="../resources/oransqr.gif" class="one" alt="[Image]">
<P>
The square above should be fifty pixels wide.
</P>
<IMG SRC="../resources/oransqr.gif" class="two" alt="[Image]">
<P>
The square above should be half as wide as the image's parent element (either the BODY or the table cell).
</P>
<P class="two">
This paragraph should be half the width of its parent element (either the BODY or the table, which should itself be half as wide as the BODY element).  This is extra text included to ensure that this will be a fair test of the <CODE>width</CODE> property without the need for the user to resize the viewing window.
</P>
</TD></TR></TABLE></BODY>
</HTML>
