<!DOCTYPE HTML>
<html>
  <head>
    <title>Media Router Integration Test - Receiver Page</title>
    <script>
      const addConnection = connection => {
        connection.onmessage = event => {
          if (event.data == 'close') {
            connection.close();
          } else {
            connection.send('Pong: ' + event.data);
          }
        };
      };

      navigator.presentation.receiver.connectionList
        .then(list => {
          list.onconnectionavailable = evt => {
            addConnection(evt.connection);
          };
          list.connections.map(connection => {
            addConnection(connection);
          });
        });
    </script>
  </head>
  <body>
  </body>
</html>
