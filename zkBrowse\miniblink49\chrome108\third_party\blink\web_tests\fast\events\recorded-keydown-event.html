<body onload="test()">
<p>Test for <a href="https://bugs.webkit.org/show_bug.cgi?id=37718">bug 37718</a>:
Crash when replaying a sequence of keyboard events.</p>
<iframe src="about:blank"></iframe>
<script>
if (window.testRunner) {
    testRunner.dumpAsText();
    testRunner.waitUntilDone();
}

function test()
{
    frames[0].document.write("<div contenteditable onkeydown='top.record(event)'></div>");
    frames[0].document.getElementsByTagName("div")[0].focus();
    if (window.eventSender)
        eventSender.keyDown("a");
}

function record(event)
{
    document.getElementById("p").innerHTML = "Testing...";
    window.recordedEvent = event;

    setTimeout(function() {

        document.body.removeChild(document.getElementsByTagName("iframe")[0]);

        setTimeout(function() {

            window.recordedEvent.charCode;
            window.recordedEvent.keyCode;
            document.getElementById("p").innerHTML = "PASS";
            if (window.testRunner)
                testRunner.notifyDone();

        }, 0);
    }, 0);
}
</script>
<p id=p>Please press any key.</p>
</body>
