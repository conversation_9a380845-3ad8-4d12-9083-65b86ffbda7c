<!doctype html>
<script src="../resources/testharness.js"></script>
<script src="../resources/testharnessreport.js"></script>
<script src="resources/property-parsing-test.js"></script>
<script>
assert_valid_value("scroll-padding", "3px");
assert_valid_value("scroll-padding", "3%");
assert_invalid_value("scroll-padding", "-3px");
assert_valid_value("scroll-padding-inline", "3px");
assert_valid_value("scroll-padding-inline", "3%");
assert_invalid_value("scroll-padding-inline", "-3px");
assert_valid_value("scroll-padding-block", "3px");
assert_valid_value("scroll-padding-block", "3%");
assert_invalid_value("scroll-padding-block", "-3px");

assert_valid_value("scroll-margin", "3px");
assert_invalid_value("scroll-margin", "3%");
assert_valid_value("scroll-margin", "-3px");
assert_valid_value("scroll-margin-inline", "3px");
assert_invalid_value("scroll-margin-inline", "3%");
assert_valid_value("scroll-margin-inline", "-3px");
assert_valid_value("scroll-margin-block", "3px");
assert_invalid_value("scroll-margin-block", "3%");
assert_valid_value("scroll-margin-block", "-3px");
</script>
