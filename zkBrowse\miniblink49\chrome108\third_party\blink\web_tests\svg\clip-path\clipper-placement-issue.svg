<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">

<!-- This tests some filter placement oddities caused by rounding
(https://bugs.webkit.org/show_bug.cgi?id=73643).
When opened, the test should not show any red outlines around the boxes. -->

<defs>
  <g id="paths">
    <path d="M10 10 h20 v20 h-20 v-20 m1 1 v18 h18 v-18 h-18"/>
    <rect x="19" y="19" width="2" height="2" />

    <rect x="5.51" y="0.51" width="2" height="32.3" fill="white"/>
    <rect x="35.4" y="0.51" width="2" height="32.3" fill="white"/>
  </g>

  <mask id="mask">
    <use xlink:href="#paths" fill="white" x="60" />
  </mask>

  <clipPath id="clipper">
    <!-- clipPath doesn't like use, so we copy the paths here. -->
    <path d="M40 10 h20 v20 h-20 v-20 m1 1 v18 h18 v-18 h-18"/>
    <rect x="49" y="19" width="2" height="2" />

    <rect x="35.51" y="0.51" width="2" height="32.3" />
    <rect x="65.4" y="0.51" width="2" height="32.3" />
  </clipPath>

</defs>

<g>
    <use xlink:href="#paths" fill="green" />
</g>

<g clip-path="url(#clipper)">
    <rect fill="red" x="38" y="8" width="24" height="24" />
    <use xlink:href="#paths" fill="green" x="30" />
</g>

<g mask="url(#mask)">
    <rect fill="red" x="68" y="8" width="24" height="24" />
    <use xlink:href="#paths" fill="green" x="60" />
</g>

</svg>
