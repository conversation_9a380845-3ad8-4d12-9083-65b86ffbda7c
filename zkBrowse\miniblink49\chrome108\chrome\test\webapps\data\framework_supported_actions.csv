# Action base name,                                   <PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON>
check_app_in_list_icon_correct,                        🌕, 🌕,  🌕,   🌕,
check_app_in_list_not_locally_installed,               🌓, 🌓,  🌓,   🌓,
check_app_in_list_tabbed,                              🌓, 🌓,  🌓,   🌓,
check_app_in_list_windowed,                            🌓, 🌓,  🌓,   🌓,
check_app_list_empty,                                  🌓, 🌓,  🌓,   🌓,
check_app_not_in_list,                                 🌓, 🌓,  🌓,   🌓,
check_custom_toolbar,                                  🌕, 🌕,  🌕,   🌕,
check_install_icon_not_shown,                          🌕, 🌕,  🌕,   🌕,
check_install_icon_shown,                              🌕, 🌕,  🌕,   🌕,
check_launch_icon_not_shown,                           🌕, 🌕,  🌕,   🌕,
check_launch_icon_shown,                               🌕, 🌕,  🌕,   🌕,
check_app_navigation,                                  🌕, 🌕,  🌕,   🌕,
check_app_navigation_is_start_url,                     🌕, 🌕,  🌕,   🌕,
check_no_toolbar,                                      🌕, 🌕,  🌕,   🌕,
check_platform_shortcut_and_icon,                      🌕, 🌓,  🌓,   🌓,
check_tab_created,                                     🌕, 🌕,  🌕,   🌕,
check_tab_not_created,                                 🌕, 🌕,  🌕,   🌕,
check_window_closed,                                   🌕, 🌕,  🌕,   🌕,
check_window_created,                                  🌕, 🌕,  🌕,   🌕,
check_window_display_minimal,                          🌕, 🌕,  🌕,   🌕,
check_window_display_standalone,                       🌕, 🌕,  🌕,   🌕,
check_window_controls_overlay,                         🌕, 🌕,  🌕,   🌕,
check_window_controls_overlay_toggle,                  🌕, 🌕,  🌕,   🌕,
close_custom_toolbar,                                  🌕, 🌕,  🌕,   🌕,
close_pwa,                                             🌕, 🌕,  🌕,   🌕,
create_shortcuts_from_list,                            🌕, 🌕,  🌕,   🌑,
delete_platform_shortcut,                              🌕, 🌕,  🌕,   🌑,
disable_window_controls_overlay,                       🌕, 🌕,  🌕,   🌕,
enable_window_controls_overlay,                        🌕, 🌕,  🌕,   🌕,
create_shortcut,                                       🌕, 🌕,  🌕,   🌕,
install_locally,                                       🌓, 🌓,  🌓,   🌓,
install_menu_option,                                   🌕, 🌕,  🌕,   🌕,
install_omnibox_icon,                                  🌕, 🌕,  🌕,   🌕,
install_policy_app,                                    🌓, 🌓,  🌓,   🌓,
launch_from_chrome_apps,                               🌓, 🌓,  🌓,   🌓,
launch_from_launch_icon,                               🌕, 🌕,  🌕,   🌕,
launch_from_menu_option,                               🌕, 🌕,  🌕,   🌕,
launch_from_platform_shortcut,                         🌓, 🌓,  🌓,   🌑,
manifest_update_display,                               🌕, 🌕,  🌕,   🌕,
manifest_update_scope_to,                              🌕, 🌕,  🌕,   🌕,
navigate_browser,                                      🌕, 🌕,  🌕,   🌕,
navigate_pwa,                                          🌕, 🌕,  🌕,   🌕,
navigate_notfound_url,                                 🌕, 🌕,  🌕,   🌕,
open_in_chrome,                                        🌕, 🌕,  🌕,   🌕,
set_open_in_tab,                                       🌓, 🌓,  🌓,   🌓,
set_open_in_window,                                    🌓, 🌓,  🌓,   🌓,
switch_incognito_profile,                              🌕, 🌕,  🌕,   🌕,
switch_profile_clients,                                🌕, 🌕,  🌕,   🌕,
sync_turn_off,                                         🌕, 🌕,  🌕,   🌕,
sync_turn_on,                                          🌕, 🌕,  🌕,   🌕,
uninstall_from_list,                                   🌕, 🌕,  🌕,   🌕,
uninstall_from_menu,                                   🌕, 🌕,  🌕,   🌑,
uninstall_from_os,                                     🌑, 🌕,  🌑,   🌑,
uninstall_policy_app,                                  🌕, 🌕,  🌕,   🌕,
uninstall_from_app_settings,                           🌕, 🌕,  🌕,   🌑,
check_user_cannot_set_run_on_os_login,                 🌕, 🌕,  🌕,   🌑,
apply_run_on_os_login_policy_allowed,                  🌕, 🌕,  🌕,   🌑,
apply_run_on_os_login_policy_blocked,                  🌕, 🌕,  🌕,   🌑,
apply_run_on_os_login_policy_run_windowed,             🌕, 🌕,  🌕,   🌑,
remove_run_on_os_login_policy,                         🌕, 🌕,  🌕,   🌑,
enable_run_on_os_login,                                🌕, 🌕,  🌕,   🌑,
disable_run_on_os_login,                               🌕, 🌕,  🌕,   🌑,
check_run_on_os_login_enabled,                         🌕, 🌕,  🌕,   🌑,
check_run_on_os_login_disabled,                        🌕, 🌕,  🌕,   🌑,
open_app_settings_from_chrome_apps,                    🌕, 🌕,  🌕,   🌑,
open_app_settings_from_app_menu,                       🌕, 🌕,  🌕,   🌑,
check_browser_navigation,                              🌕, 🌕,  🌕,   🌕,
check_browser_navigation_is_app_settings,              🌕, 🌕,  🌕,   🌑,
await_manifest_update,                                 🌕, 🌕,  🌕,   🌕,
