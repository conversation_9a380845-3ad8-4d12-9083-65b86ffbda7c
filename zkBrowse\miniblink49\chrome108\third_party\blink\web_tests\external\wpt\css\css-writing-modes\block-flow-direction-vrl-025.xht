<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

 <head>

  <title>CSS Writing Modes Test: writing mode of document - horizontal position of first block</title>

  <link rel="author" title="<PERSON><PERSON>" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" />
  <link rel="help" href="http://www.w3.org/TR/css-writing-modes-3/#block-flow" title="3.1 Block Flow Direction: the writing-mode property" />
  <link rel="help" href="http://www.w3.org/TR/2011/REC-CSS2-20110607/visuren.html#block-formatting" title="9.4.1 Block formatting contexts" />
  <link rel="match" href="block-flow-direction-025-ref.xht" />

  <meta content="image" name="flags" />
  <meta content="This test checks that, when 'writing-mode' value of the root element is set to 'vertical-rl', then its block boxes are laid out leftwardedly (from right to left) one after the other beginning at its righthand side." name="assert" />

  <style type="text/css"><![CDATA[
  html
    {
      writing-mode: vertical-rl;
    }

  div
    {
      background-color: blue;
      height: 100px;
      width: 100px;
    }
  ]]></style>
 </head>

 <body>

  <div></div>

  <p><img src="support/block-flow-direction-025-exp-res.png" width="359" height="36" alt="Image download support must be enabled" /></p>

  <!--
  The image says:
  Test passes if there is a blue square in the
  <strong>upper-right corner</strong> of the page.
  -->

 </body>
</html>
