<!DOCTYPE html>
<html>
<head>
<script src="../../resources/js-test.js"></script>
<script src="resources/common.js"></script>
</head>
<body>
<p id="description"></p>
<div id="console"></div>

<script>
description("Tests calling cypto.subtle.sign and crypto.subtle.verify with incorrect inputs");

jsTestIsAsync = true;

function importHmacKey()
{
    var importAlgorithm = {name: 'HMAC', hash: {name: 'sha-1'}};
    var keyData = new Uint8Array(16);
    var extractable = true;
    var usages = ['sign', 'verify'];

    return crypto.subtle.importKey('raw', keyData, importAlgorithm, extractable, usages);
}

data = asciiToUint8Array("hello");
hmac = {name: 'HMAC', hash: {name: 'sha-1'}};

importHmacKey().then(function(result) {
    key = result;

    // Pass invalid signature parameters to verify()
    return crypto.subtle.verify(hmac, key, null, data);
}).then(failAndFinishJSTest, function(result) {
    logError(result);

    // Pass invalid signature parameters to verify()
    return crypto.subtle.verify(hmac, key, 'a', data);
}).then(failAndFinishJSTest, function(result) {
    logError(result);

    // Pass invalid signature parameters to verify()
    return crypto.subtle.verify(hmac, key, [], data);
}).then(failAndFinishJSTest, function(result) {
    logError(result);

    // Operation does not support signing.
    return crypto.subtle.sign({name: 'sha-1'}, key, data);
}).then(failAndFinishJSTest, function(result) {
    logError(result);

    // Operation doesn't support signing (also given an invalid key, but the
    // first failure takes priority)
    return crypto.subtle.sign({name: 'AES-CBC'}, key, data);
}).then(failAndFinishJSTest, function(result) {
    logError(result);
}).then(finishJSTest, failAndFinishJSTest);

</script>

</body>
</html>
