<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet href="../../text/resources/glyph-orientation-vertical.css" type="text/css"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN"
"http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">

<!--

   Copyright 2001-2002  The Apache Software Foundation 

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

-->
<!-- ========================================================================= -->
<!-- Vertical <text>                                                           -->
<!--                                                                           -->
<!-- <AUTHOR>                                       -->
<!-- @version $Id: verticalText.svg,v 1.6 2004/08/18 07:12:23 vhardy Exp $     -->
<!-- ========================================================================= -->
<?xml-stylesheet type="text/css" href="../resources/test.css" ?>  

<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="body" width="450" height="500" viewBox="0 0 450 500">

    <title>Vertical Text Test</title>

    <defs>
        <path id="Path1" style="fill:none; stroke:blue;" transform="scale(0.15,0.15)"
                  d="M 100 200 C 200 100 300 0 400 100 C 500 200 600 300 700 200 C 800 100 900 0 1000 100
                     C 1100 200 1200 300 1300 200 C 1400 100 1500 0 1600 100
                     C 1700 200 1800 300 1900 200 C 2000 100 2100 0 2200 100"/>
    </defs>

    <g id="content">

        <text class="title" x="50%" y="40">Vertical Text Test</text>

        <line x1="20" y1="75" x2="430" y2="75" stroke="black"/>

	  <g opacity="0.5">
            <circle cx="50" cy="75" r="2" fill="red" />
 		<circle cx="100" cy="75" r="2" fill="red" />
 		<circle cx="150" cy="75" r="2" fill="red" />
 		<circle cx="200" cy="75" r="2" fill="red" />
 		<circle cx="250" cy="75" r="2" fill="red" />
 		<circle cx="300" cy="75" r="2" fill="red" />
 		<circle cx="350" cy="75" r="2" fill="red" />
            <circle cx="400" cy="75" r="2" fill="red" />

            <line x1="50" y1="75" x2="50" y2="450" stroke="red" />
            <line x1="100" y1="75" x2="100" y2="450" stroke="red" />
		<line x1="150" y1="75" x2="150" y2="450" stroke="red" />
		<line x1="200" y1="75" x2="200" y2="450" stroke="red" />
		<line x1="250" y1="75" x2="250" y2="450" stroke="red" />
		<line x1="300" y1="75" x2="300" y2="450" stroke="red" />
            <line x1="350" y1="75" x2="350" y2="450" stroke="red" />
            <line x1="400" y1="75" x2="400" y2="450" stroke="red" />
        </g>


        <text x="50" y="75" writing-mode="tb" font-size="15">Writing top to bottom&#x753b;&#x50cf;</text>
        <text x="100" y="75" glyph-orientation-vertical="auto" writing-mode="tb" font-size="15">Glyph Orientation:auto&#x753b;&#x50cf;</text>
        <text id="orient0"   x="150" y="75" glyph-orientation-vertical="0deg" 
              writing-mode="tb" font-size="15">Glyph Orientation:0&#x753b;&#x50cf;</text>
        <text id="orient90"  x="200" y="75" glyph-orientation-vertical="90deg" 
              writing-mode="tb" font-size="15">Glyph Orientation:90&#x753b;&#x50cf;</text>
        <text id="orient180" x="250" y="75" glyph-orientation-vertical="180deg" 
              writing-mode="tb" font-size="15">Glyph Orientation:180&#x753b;&#x50cf;</text>
        <text id="orient270" x="300" y="75" glyph-orientation-vertical="270deg" 
              writing-mode="tb" font-size="15">Glyph Orientation:270&#x753b;&#x50cf;</text>
        <text x="350" y="75" writing-mode="tb" font-size="15">Embedded <tspan fill="red">&lt;tspan&gt;</tspan> element</text>
        <text x="400" y="75" writing-mode="tb" font-size="15">Embedded <tspan fill="blue" glyph-orientation-vertical="0deg">orientated</tspan> element</text>
       


      
	  <g transform="translate(40,450)">
            <use xlink:href="#Path1" style="fill:none; stroke:blue; stroke-width:2"/>
	      <text font-size="12" writing-mode="tb" glyph-orientation-vertical="0deg">
                <textPath xlink:href="#Path1">Vertical text on a Path&#x753b;&#x50cf;</textPath>
            </text>
         </g>

    </g>

    <!-- ============================================================= -->
    <!-- Batik sample mark                                             -->
    <!-- ============================================================= -->
    <use xlink:href="../resources/batikLogo.svg#Batik_Tag_Box" />

</svg>
