<script>
if (window.testRunner)
    testRunner.dumpAsText();

var bubbleCount = 0;

function checkBubble()
{
    bubbleCount++;
    if (bubbleCount > 1) {
        document.getElementById("bubble").innerHTML = "FAIL (hit " + bubbleCount + " times)";
    }
}

var captureCount = 0;

function checkCapture()
{
    captureCount++;
    if (captureCount > 1) {
        document.getElementById("capture").innerHTML = "FAIL (hit " + captureCount + " times)";
    }
}

window.addEventListener("load", checkBubble, false);
window.addEventListener("load", checkCapture, true);


</script>
<div>
This test ensures that "load" events for images do not trigger bubble
or capture handlers on the window object. We need this quirk to be
compatible with a variety of web sites.
</div>
Bubble for load event hit at most once: <span id="bubble">PASS</span>.<br>
Capture for load event hit at most once: <span id="capture">PASS</span>.<br>

<img src="resources/greenbox.png">
<img src="resources/greenbox.png">
