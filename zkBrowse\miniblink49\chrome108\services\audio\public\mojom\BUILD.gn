# Copyright 2017 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//mojo/public/tools/bindings/mojom.gni")

mojom("mojom") {
  sources = [
    "audio_device_description.mojom",
    "audio_service.mojom",
    "debug_recording.mojom",
    "device_notifications.mojom",
    "log_factory_manager.mojom",
    "system_info.mojom",
    "testing_api.mojom",
  ]

  public_deps = [
    "//media/mojo/mojom",
    "//mojo/public/mojom/base",
    "//sandbox/policy/mojom",
  ]

  cpp_typemaps = [
    {
      types = [
        {
          mojom = "audio.mojom.AudioDeviceDescription"
          cpp = "::media::AudioDeviceDescription"
          move_only = true
        },
      ]
      traits_headers = [
        "//services/audio/public/cpp/audio_device_description_mojom_traits.h",
      ]
      traits_sources = [
        "//services/audio/public/cpp/audio_device_description_mojom_traits.cc",
      ]
      traits_public_deps = [ "//media" ]
    },
    {
      types = [
        {
          mojom = "audio.mojom.AudioProcessingSettings"
          cpp = "::media::AudioProcessingSettings"
        },
        {
          mojom = "audio.mojom.AudioProcessingStats"
          cpp = "::webrtc::AudioProcessorInterface::AudioProcessorStatistics"
        },
        {
          mojom = "audio.mojom.AutomaticGainControlType"
          cpp = "::media::AutomaticGainControlType"
        },
        {
          mojom = "audio.mojom.EchoCancellationType"
          cpp = "::media::EchoCancellationType"
        },
        {
          mojom = "audio.mojom.NoiseSuppressionType"
          cpp = "::media::NoiseSuppressionType"
        },
      ]
      traits_public_deps = [
        "//media",
        "//third_party/webrtc_overrides:webrtc_component",
      ]
    },
    {
      types = [
        {
          mojom = "audio.mojom.DebugRecordingStreamType"
          cpp = "::media::AudioDebugRecordingStreamType"
        },
      ]
      traits_headers =
          [ "//services/audio/public/cpp/debug_recording_mojom_traits.h" ]
      traits_sources =
          [ "//services/audio/public/cpp/debug_recording_mojom_traits.cc" ]
      traits_public_deps = [ "//media" ]
    },
  ]
}
