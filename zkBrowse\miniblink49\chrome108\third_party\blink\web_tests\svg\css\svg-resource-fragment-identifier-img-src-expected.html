<!DOCTYPE html>
<html>
<head>
    <style type="text/css" media="screen">

        div {
            width: 100px;
            height: 100px;
            margin: 10px;
            display: inline-block;
        }

        .red {
            background-color: red;
        }

        .green {
            background-color: green;
        }

        .blue {
            background-color: blue;
        }

    </style>

</head>
<body>

    <div class="red"></div>
    <div class="green"></div>
    <div class="blue"></div>
    <div class="green"></div>
	<script>
		if (window.testRunner) {
			testRunner.waitUntilDone();
			testRunner.setBackingScaleFactor(2, function() { testRunner.notifyDone(); });
		}
	</script>
</body>
</html>
