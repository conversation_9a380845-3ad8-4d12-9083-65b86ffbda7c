<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background with (image color position repeat)</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="<PERSON><PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-03-24 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <link rel="match" href="background-090-ref.xht" />

        <meta name="flags" content="image" />
        <meta name="assert" content="Background with (image color position repeat) sets the background to the color specified, with the image overlaid across the x-axis at the bottom." />
        <style type="text/css">
            div
            {
                background: url("support/blue15x15.png") green bottom repeat-x;
                height: 200px;
                width: 200px;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is a green rectangle on top of a blue stripe.</p>
        <div></div>
    </body>
</html>