This tests that links in a image map are able to be reached through keyboard access and tabbing.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".

PASS document.activeElement.id is 'area1'
PASS document.activeElement.id is 'area2'
PASS document.activeElement.id is 'area3'
PASS document.activeElement.id is 'area5'
PASS document.activeElement.id is 'area6'
PASS document.activeElement.id == 'area1' || document.activeElement.id == 'body' is true
PASS successfullyParsed is true

TEST COMPLETE

