<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background-repeat applied to elements with 'display' set to 'block'</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="G<PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-05-06 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background-repeat" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <link rel="match" href="../reference/ref-filled-green-100px-square.xht" />

        <meta name="flags" content="image" />
        <meta name="assert" content="The 'background-repeat' property applies to elements with 'display' set to 'block'." />
        <style type="text/css">
            span
            {
                background-color: red;
                background-image: url('support/green15x15.png');
                background-repeat: repeat;
                display: block;
                height: 100px;
                width: 100px;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is a filled green square and <strong>no red</strong>.</p>
        <div>
            <span></span>
        </div>
    </body>
</html>