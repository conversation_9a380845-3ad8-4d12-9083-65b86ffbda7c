<!DOCTYPE html>
<title>Serialization of url(...) for SVG resources</title>
<script src="../../resources/testharness.js"></script>
<script src="../../resources/testharnessreport.js"></script>
<body>
<script>
function computedValue(property, value) {
  let div = document.createElement("div");
  document.body.appendChild(div);
  div.style.setProperty(property, value);
  let computedValue = getComputedStyle(div).getPropertyValue(property);
  document.body.removeChild(div);
  return computedValue;
}

function specifiedValue(property, value) {
  let div = document.createElement("div");
  div.style.setProperty(property, value);
  return div.style.getPropertyValue(property);
}

function resolveUrl(url) {
  let anchor = document.createElement('a');
  anchor.href = url;
  return anchor.href;
}

[
  'fill',
  'stroke',
  'marker-start',
  'marker-mid',
  'marker-end',
  'mask',
  'filter',
].forEach(property => {
  test(() => {
    assert_equals(specifiedValue(property, "url(#fragment)"), 'url("#fragment")');
    assert_equals(specifiedValue(property, "url(resource.svg#fragment)"),
                  'url("resource.svg#fragment")');
  }, document.title + ', ' + property + ' (specified)');

  test(() => {
    let resolved_url = resolveUrl("resource.svg#fragment");
    assert_equals(computedValue(property, "url(#fragment)"), 'url("#fragment")');
    assert_equals(computedValue(property, "url(resource.svg#fragment)"),
                  'url("' + resolved_url + '")');
  }, document.title + ', ' + property + ' (computed)');
});
</script>
</body>
