<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright 2011 The Chromium Authors
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file.
-->
<!-- Layout for each item in the menu popup -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/AppMenuItem"
    android:layout_width="match_parent"
    android:layout_height="?android:attr/listPreferredItemHeightSmall"
    android:orientation="horizontal" >

    <TextView
        android:id="@+id/menu_item_text"
        style="@style/AppMenuItemText"
        android:layout_weight="1"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:duplicateParentState="true" />

    <org.chromium.ui.widget.ChromeImageView
        android:id="@+id/menu_item_icon"
        android:layout_weight="0"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="end"
        android:gravity="center_vertical"
        android:tint="@macro/default_icon_color_secondary"
        android:duplicateParentState="true" />
</LinearLayout>
