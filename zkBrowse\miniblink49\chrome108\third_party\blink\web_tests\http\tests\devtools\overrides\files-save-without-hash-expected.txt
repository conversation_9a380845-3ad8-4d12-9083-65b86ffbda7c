Ensures iframes are overridable if overrides are setup.

Creating UISourcecode for url: resources/bar.js
Found network UISourceCode: http://127.0.0.1:8000/devtools/overrides/resources/bar.js
Saving network UISourceCode
Created File: 127.0.0.1:8000/devtools/overrides/resources/bar.js

Creating UISourcecode for url: resources/a space/bar.js
Found network UISourceCode: http://127.0.0.1:8000/devtools/overrides/resources/a%20space/bar.js
Saving network UISourceCode
Created File: 127.0.0.1:8000/devtools/overrides/resources/a%252520space/bar.js

Creating UISourcecode for url: resources/bar.js?#hello
Found network UISourceCode: http://127.0.0.1:8000/devtools/overrides/resources/bar.js?
Saving network UISourceCode
Created File: 127.0.0.1:8000/devtools/overrides/resources/bar.js?

Creating UISourcecode for url: resources/bar.js?params
Found network UISourceCode: http://127.0.0.1:8000/devtools/overrides/resources/bar.js?params
Saving network UISourceCode
Created File: 127.0.0.1:8000/devtools/overrides/resources/bar.js?params

Creating UISourcecode for url: resources/bar.js?params&and=more&pa&ra?ms
Found network UISourceCode: http://127.0.0.1:8000/devtools/overrides/resources/bar.js?params&and=more&pa&ra?ms
Saving network UISourceCode
Created File: 127.0.0.1:8000/devtools/overrides/resources/bar.js?params&and=more&pa&ra?ms

Creating UISourcecode for url: resources/bar2.js?params&and=more&pa&ra?ms#hello?with&params
Found network UISourceCode: http://127.0.0.1:8000/devtools/overrides/resources/bar2.js?params&and=more&pa&ra?ms
Saving network UISourceCode
Created File: 127.0.0.1:8000/devtools/overrides/resources/bar2.js?params&and=more&pa&ra?ms

Creating UISourcecode for url: resources/no-extension
Found network UISourceCode: http://127.0.0.1:8000/devtools/overrides/resources/no-extension
Saving network UISourceCode
Created File: 127.0.0.1:8000/devtools/overrides/resources/no-extension

Creating UISourcecode for url: resources/foo&with%20some*bad%5EC!h%7Ba...r,acter%%s/file&with?s%20t^rang@e~character%27S
Found network UISourceCode: http://127.0.0.1:8000/devtools/overrides/resources/foo&with%20some*bad%5EC!h%7Ba...r,acter%%s/file&with?s%20t^rang@e~character%27S
Saving network UISourceCode
Created File: 127.0.0.1:8000/devtools/overrides/resources/foo&with%252520some%252Abad%25255EC!h%25257Ba...r,acter%2525%2525s/file&with?s%2520t%5Erang@e~character%2527S

Creating UISourcecode for url: resources/
Found network UISourceCode: http://127.0.0.1:8000/devtools/overrides/resources/
Saving network UISourceCode
Created File: 127.0.0.1:8000/devtools/overrides/resources/index.html

Creating UISourcecode for url: windows/bar.js
Found network UISourceCode: http://127.0.0.1:8000/devtools/overrides/windows/bar.js
Saving network UISourceCode
Created File: 127.0.0.1%253A8000/devtools/overrides/windows/bar.js

Creating UISourcecode for url: windows/a space/bar.js
Found network UISourceCode: http://127.0.0.1:8000/devtools/overrides/windows/a%20space/bar.js
Saving network UISourceCode
Created File: 127.0.0.1%253A8000/devtools/overrides/windows/a%252520space/bar.js

Creating UISourcecode for url: windows/bar.js?#hello
Found network UISourceCode: http://127.0.0.1:8000/devtools/overrides/windows/bar.js?
Saving network UISourceCode
Created File: 127.0.0.1%253A8000/devtools/overrides/windows/bar.js%3F

Creating UISourcecode for url: windows/bar.js?params
Found network UISourceCode: http://127.0.0.1:8000/devtools/overrides/windows/bar.js?params
Saving network UISourceCode
Created File: 127.0.0.1%253A8000/devtools/overrides/windows/bar.js%3Fparams

Creating UISourcecode for url: windows/bar.js?params&and=more&pa&ra?ms
Found network UISourceCode: http://127.0.0.1:8000/devtools/overrides/windows/bar.js?params&and=more&pa&ra?ms
Saving network UISourceCode
Created File: 127.0.0.1%253A8000/devtools/overrides/windows/bar.js%3Fparams&and=more&pa&ra%3Fms

Creating UISourcecode for url: windows/bar2.js?params&and=more&pa&ra?ms#hello?with&params
Found network UISourceCode: http://127.0.0.1:8000/devtools/overrides/windows/bar2.js?params&and=more&pa&ra?ms
Saving network UISourceCode
Created File: 127.0.0.1%253A8000/devtools/overrides/windows/bar2.js%3Fparams&and=more&pa&ra%3Fms

Creating UISourcecode for url: windows/no-extension
Found network UISourceCode: http://127.0.0.1:8000/devtools/overrides/windows/no-extension
Saving network UISourceCode
Created File: 127.0.0.1%253A8000/devtools/overrides/windows/no-extension

Creating UISourcecode for url: windows/foo&with%20some*bad%5EC!h%7Ba...r,acter%%s/file&with?s%20t^rang@e~character%27S
Found network UISourceCode: http://127.0.0.1:8000/devtools/overrides/windows/foo&with%20some*bad%5EC!h%7Ba...r,acter%%s/file&with?s%20t^rang@e~character%27S
Saving network UISourceCode
Created File: 127.0.0.1%253A8000/devtools/overrides/windows/foo&with%252520some%252Abad%25255EC!h%25257Ba...r,acter%2525%2525s/file&with%3Fs%2520t%5Erang@e~character%2527S

Creating UISourcecode for url: windows/
Found network UISourceCode: http://127.0.0.1:8000/devtools/overrides/windows/
Saving network UISourceCode
Created File: 127.0.0.1%253A8000/devtools/overrides/windows/index.html


