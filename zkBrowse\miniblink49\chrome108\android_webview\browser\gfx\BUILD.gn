# Copyright 2019 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/android/config.gni")
import("//build/config/android/rules.gni")

source_set("gfx") {
  sources = [
    "aw_draw_fn_impl.cc",
    "aw_draw_fn_impl.h",
    "aw_gl_functor.cc",
    "aw_gl_functor.h",
    "aw_gl_surface.cc",
    "aw_gl_surface.h",
    "aw_gl_surface_external_stencil.cc",
    "aw_gl_surface_external_stencil.h",
    "aw_picture.cc",
    "aw_picture.h",
    "aw_vulkan_context_provider.cc",
    "aw_vulkan_context_provider.h",
    "begin_frame_source_webview.cc",
    "begin_frame_source_webview.h",
    "browser_view_renderer.cc",
    "browser_view_renderer.h",
    "browser_view_renderer_client.h",
    "child_frame.cc",
    "child_frame.h",
    "compositor_frame_consumer.h",
    "compositor_frame_producer.h",
    "display_scheduler_webview.cc",
    "display_scheduler_webview.h",
    "display_webview.cc",
    "display_webview.h",
    "gpu_service_webview.cc",
    "gpu_service_webview.h",
    "hardware_renderer.cc",
    "hardware_renderer.h",
    "hardware_renderer_viz.cc",
    "hardware_renderer_viz.h",
    "java_browser_view_renderer_helper.cc",
    "java_browser_view_renderer_helper.h",
    "output_surface_provider_webview.cc",
    "output_surface_provider_webview.h",
    "overlay_processor_webview.cc",
    "overlay_processor_webview.h",
    "parent_compositor_draw_constraints.cc",
    "parent_compositor_draw_constraints.h",
    "render_thread_manager.cc",
    "render_thread_manager.h",
    "root_frame_sink.cc",
    "root_frame_sink.h",
    "root_frame_sink_proxy.cc",
    "root_frame_sink_proxy.h",
    "scoped_app_gl_state_restore.cc",
    "scoped_app_gl_state_restore.h",
    "scoped_app_gl_state_restore_impl.cc",
    "scoped_app_gl_state_restore_impl.h",
    "scoped_app_gl_state_restore_impl_angle.cc",
    "scoped_app_gl_state_restore_impl_angle.h",
    "skia_output_surface_dependency_webview.cc",
    "skia_output_surface_dependency_webview.h",
    "task_forwarding_sequence.cc",
    "task_forwarding_sequence.h",
    "task_queue_webview.cc",
    "task_queue_webview.h",
    "viz_compositor_thread_runner_webview.cc",
    "viz_compositor_thread_runner_webview.h",
    "vulkan_gl_interop.cc",
    "vulkan_gl_interop.h",
  ]

  deps = [
    "//android_webview:browser_jni_headers",
    "//android_webview/common",
    "//android_webview/public",
    "//base",
    "//components/power_scheduler",
    "//components/viz/service",
    "//components/viz/service/main",
    "//content/public/browser",
    "//gpu/command_buffer/client:gles2_implementation",
    "//gpu/command_buffer/service",
    "//gpu/ipc/common:android_image_reader_utils",
    "//gpu/ipc/common:common",
    "//gpu/skia_bindings",
    "//gpu/vulkan:vulkan",
    "//gpu/vulkan/init",
    "//gpu/vulkan/init:skia",
    "//services/viz/privileged/mojom",
    "//services/viz/public/mojom",
    "//skia",
    "//ui/gfx",
    "//ui/gl",
    "//ui/gl/init",
  ]

  libs = [ "jnigraphics" ]
}
