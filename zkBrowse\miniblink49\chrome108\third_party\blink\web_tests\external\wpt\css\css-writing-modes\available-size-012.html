<!DOCTYPE html>
<meta charset="utf-8">
<title>Testing Available Space in Orthogonal Flows / max-height + min-height / content height</title>
<link rel="author" title="Florian Rivoal" href="https://florian.rivoal.net/">
<link rel="help" href="https://www.w3.org/TR/css-writing-modes-3/#orthogonal-auto">
<link rel="match" href="reference/available-size-001-ref.html">
<meta name="assert" content="When an orthogonal flow's parent doesn't have a definite block size and the nearest ancestor scroller does not have a fixed height but does have a fixed max-height, use that, increased by min-height if it exists and is larger. (same as -001, with min-height)">
<style>
body > div {
  font-family: monospace; /* to be able to reliably measure things in ch*/
  font-size: 20px;
  max-height: 4ch; /* **max**-height does not give the element a definite block size */
  min-height: 8ch;
  overflow: hidden;
  color: transparent;
  position: relative; /* to act as a container of #red */
  padding: 1ch 0;
}

div > div { writing-mode: vertical-rl; }

span {
  background: green;
  display: inline-block; /* This should not change it's size or position, but makes the size of the background predictable*/
}

#red { /* Not necessary when when comparing to the reference, but makes human judgement easier */
  position: absolute;
  background: red;
  left: 0; top: 1ch;
  writing-mode: vertical-rl;
  z-index: -1;
}
</style>

<p>Test passes if there is a <strong>green rectangle</strong> below and <strong>no red</strong>.

<div>
  <aside id="red">0</aside>
  <div>0 0 0 0 <span>0</span> 0 0 0</div> <!-- If this div take its height from
  the min-height of its parent, it should wrap just right for the green 0 to
  overlap with the red one. -->
</div>
