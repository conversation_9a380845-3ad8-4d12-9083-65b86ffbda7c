# Copyright 2017 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//mojo/public/tools/bindings/mojom.gni")

mojom("mojom") {
  sources = [ "seatbelt_extension_token.mojom" ]

  cpp_typemaps = [
    {
      types = [
        {
          mojom = "sandbox.mac.mojom.SeatbeltExtensionToken"
          cpp = "::sandbox::SeatbeltExtensionToken"
          move_only = true
        },
      ]
      traits_headers = [ "seatbelt_extension_token_mojom_traits.h" ]
      traits_sources = [ "seatbelt_extension_token_mojom_traits.cc" ]
      traits_public_deps = [ "//sandbox/mac:seatbelt_extension" ]
    },
  ]
}

mojom("test_interfaces") {
  sources = [ "traits_test_service.mojom" ]
  public_deps = [ ":mojom" ]
}
