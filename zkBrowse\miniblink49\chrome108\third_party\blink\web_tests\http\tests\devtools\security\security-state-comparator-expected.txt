Tests that SecurityStateComparator correctly compares the severity of security states.

Sign of SecurityStateComparator("info","info"): 0 (expected: 0)
Sign of SecurityStateComparator("info","insecure-broken"): -1 (expected: -1)
Sign of SecurityStateComparator("info","insecure"): -1 (expected: -1)
Sign of SecurityStateComparator("info","neutral"): -1 (expected: -1)
Sign of SecurityStateComparator("info","secure"): -1 (expected: -1)
Sign of SecurityStateComparator("info","unknown"): -1 (expected: -1)
Sign of SecurityStateComparator("insecure-broken","info"): 1 (expected: 1)
Sign of SecurityStateComparator("insecure-broken","insecure-broken"): 0 (expected: 0)
Sign of SecurityStateComparator("insecure-broken","insecure"): -1 (expected: -1)
Sign of SecurityStateComparator("insecure-broken","neutral"): -1 (expected: -1)
Sign of SecurityStateComparator("insecure-broken","secure"): -1 (expected: -1)
Sign of SecurityStateComparator("insecure-broken","unknown"): -1 (expected: -1)
Sign of SecurityStateComparator("insecure","info"): 1 (expected: 1)
Sign of SecurityStateComparator("insecure","insecure-broken"): 1 (expected: 1)
Sign of SecurityStateComparator("insecure","insecure"): 0 (expected: 0)
Sign of SecurityStateComparator("insecure","neutral"): -1 (expected: -1)
Sign of SecurityStateComparator("insecure","secure"): -1 (expected: -1)
Sign of SecurityStateComparator("insecure","unknown"): -1 (expected: -1)
Sign of SecurityStateComparator("neutral","info"): 1 (expected: 1)
Sign of SecurityStateComparator("neutral","insecure-broken"): 1 (expected: 1)
Sign of SecurityStateComparator("neutral","insecure"): 1 (expected: 1)
Sign of SecurityStateComparator("neutral","neutral"): 0 (expected: 0)
Sign of SecurityStateComparator("neutral","secure"): -1 (expected: -1)
Sign of SecurityStateComparator("neutral","unknown"): -1 (expected: -1)
Sign of SecurityStateComparator("secure","info"): 1 (expected: 1)
Sign of SecurityStateComparator("secure","insecure-broken"): 1 (expected: 1)
Sign of SecurityStateComparator("secure","insecure"): 1 (expected: 1)
Sign of SecurityStateComparator("secure","neutral"): 1 (expected: 1)
Sign of SecurityStateComparator("secure","secure"): 0 (expected: 0)
Sign of SecurityStateComparator("secure","unknown"): -1 (expected: -1)
Sign of SecurityStateComparator("unknown","info"): 1 (expected: 1)
Sign of SecurityStateComparator("unknown","insecure-broken"): 1 (expected: 1)
Sign of SecurityStateComparator("unknown","insecure"): 1 (expected: 1)
Sign of SecurityStateComparator("unknown","neutral"): 1 (expected: 1)
Sign of SecurityStateComparator("unknown","secure"): 1 (expected: 1)
Sign of SecurityStateComparator("unknown","unknown"): 0 (expected: 0)

