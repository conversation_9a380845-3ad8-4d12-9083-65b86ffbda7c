// Copyright 2014 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef GIN_PUBLIC_DEBUG_IMPL_H_
#define GIN_PUBLIC_DEBUG_IMPL_H_

#include "gin/public/debug.h"
//#include "v8/include/v8.h"
#include "v8.h"

// V8 10.8 compatibility layer
#if V8_MAJOR_VERSION >= 10
#include "../v8_10_8/v8_compatibility.h"
#endif

namespace gin {

class DebugImpl {
 public:
#if V8_MAJOR_VERSION >= 10
  static FunctionEntryHook GetFunctionEntryHook();
#else
  static v8::FunctionEntryHook GetFunctionEntryHook();
#endif
  static v8::JitCodeEventHandler GetJitCodeEventHandler();
#if defined(OS_WIN)
  static Debug::CodeRangeCreatedCallback GetCodeRangeCreatedCallback();
  static Debug::CodeRangeDeletedCallback GetCodeRangeDeletedCallback();
#endif
};

}  // namespace gin

#endif  // GIN_PUBLIC_DEBUG_IMPL_H_
