<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<!-- saved from url=(0063)http://test.csswg.org/suites/css2.1/20110323/html4/clip-001.htm -->
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

  <title>CSS Test: clip - auto value</title>

  <link rel="author" title="G<PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/">
  <link rel="author" title="V<PERSON><PERSON>" href="http://www.vadikom.com/">
  <link rel="help" href="http://www.w3.org/TR/CSS21/visufx.html#clipping">
  <meta content="dom" name="flags">
  <meta content="An element must not clip when clip is set to auto." name="assert">
  <meta http-equiv="Content-Script-Type" content="text/javascript">

  <style type="text/css">
  #red-overlapped-layer
  {
  background-color: red;
  height: 300px;
  position: absolute;
  width: 300px;
  }

  #red-parent
  {
  background-color: red;
  clip: rect(0, 100px, 100px, 0);
  height: 200px;
  position: absolute;
  width: 200px;
  }

  #green-child
  {
  background-color: green;
  height: 300px;
  width: 300px;
  }
  </style>

  <script type="text/javascript">
  function startTest()
  {
  document.getElementById("red-parent").style.clip = "auto";
  }
  </script>

 </head>

 <body onload="startTest();">

  <p>There should be a big green square and <strong>no red</strong>.</p>

  <div id="red-overlapped-layer"></div>

  <div id="red-parent" style="clip: auto; ">
   <div id="green-child"></div>
  </div>

 
</body></html>