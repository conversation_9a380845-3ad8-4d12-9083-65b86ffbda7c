<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright 2016 The Chromium Authors
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file.
-->

<merge
    xmlns:android="http://schemas.android.com/apk/res/android">

    <LinearLayout android:id="@+id/url_action_container"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/toolbar_height_no_shadow"
        android:layout_gravity="end|center_vertical"
        android:orientation="horizontal"
        android:layout_marginEnd="@dimen/location_bar_url_action_offset">

        <org.chromium.ui.widget.ChromeImageButton
            android:id="@+id/delete_button"
            style="@style/LocationBarActionButton"
            android:src="@drawable/btn_delete_24dp"
            android:visibility="invisible"
            android:contentDescription="@string/accessibility_toolbar_btn_delete_url" />

        <org.chromium.ui.widget.ChromeImageButton
            android:id="@+id/mic_button"
            style="@style/LocationBarActionButton"
            android:src="@drawable/btn_mic"
            android:visibility="invisible"
            android:contentDescription="@string/accessibility_toolbar_btn_mic" />

    <!-- This is a short term solution to have 2 Lens buttons in this xml to
         support different position variants in experiment.
         TODO(b/182195615): only keep one Lens button after the experiment. -->
        <org.chromium.ui.widget.ChromeImageButton
            android:id="@+id/lens_camera_button"
            style="@style/LocationBarActionButton"
            android:src="@drawable/lens_camera_icon"
            android:visibility="gone"
            android:contentDescription="@string/accessibility_btn_lens_camera" />

        <org.chromium.ui.widget.ChromeImageButton
            android:id="@+id/bookmark_button"
            style="@style/LocationBarActionButton"
            android:visibility="gone"
            android:contentDescription="@string/accessibility_menu_bookmark" />

       <org.chromium.ui.widget.ChromeImageButton
            android:id="@+id/save_offline_button"
            style="@style/LocationBarActionButton"
            android:nextFocusForward="@+id/menu_button"
            android:src="@drawable/ic_file_download_white_24dp"
            android:visibility="gone"
            android:contentDescription="@string/download_page" />

    </LinearLayout>
</merge>