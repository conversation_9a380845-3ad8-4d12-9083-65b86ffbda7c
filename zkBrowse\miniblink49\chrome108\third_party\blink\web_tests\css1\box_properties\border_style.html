<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.5.17 border-style</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
.one {border-style: dotted; border-color: black; border-width: thick;}
.two {border-style: dashed; border-color: black; border-width: thick;}
.three {border-style: solid; border-color: black; border-width: thick;}
.four {border-style: double; border-color: black; border-width: thick;}
.five {border-style: groove; border-color: olive; border-width: thick;}
.six {border-style: ridge; border-color: olive; border-width: thick;}
.seven {border-style: inset; border-color: olive; border-width: thick;}
.eight {border-style: outset; border-color: olive; border-width: thick;}
.nine {border-style: double groove; border-color: purple; border-width: thick;}
.ten {border-style: double groove ridge inset;
   border-color: purple; border-width: thick;}
.eleven {border-style: none; border-color: red; border-width: thick;}</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.one {border-style: dotted; border-color: black; border-width: thick;}
.two {border-style: dashed; border-color: black; border-width: thick;}
.three {border-style: solid; border-color: black; border-width: thick;}
.four {border-style: double; border-color: black; border-width: thick;}
.five {border-style: groove; border-color: olive; border-width: thick;}
.six {border-style: ridge; border-color: olive; border-width: thick;}
.seven {border-style: inset; border-color: olive; border-width: thick;}
.eight {border-style: outset; border-color: olive; border-width: thick;}
.nine {border-style: double groove; border-color: purple; border-width: thick;}
.ten {border-style: double groove ridge inset;
   border-color: purple; border-width: thick;}
.eleven {border-style: none; border-color: red; border-width: thick;}
</PRE>
<HR>
<P class="one">
This paragraph should have a thick black dotted border all the way around.
</P>
<P class="two">
This paragraph should have a thick black dashed border all the way around.
</P>
<P class="three">
This paragraph should have a thick black solid border all the way around.
</P>
<P class="four">
This paragraph should have a thick black double border all the way around.
</P>
<P class="five">
This paragraph should have a thick olive groove border all the way around.
</P>
<P class="six">
This paragraph should have a thick olive ridge border all the way around.
</P>
<P class="seven">
This paragraph should have a thick olive inset border all the way around.
</P>
<P class="eight">
This paragraph should have a thick olive outset border all the way around.
</P>
<P class="nine">
This paragraph should have thick double top and bottom borders, and thick grooved side borders.  The color of all four sides should be based on purple.
</P>
<P class="ten">
This paragraph should have, in clockwise order from the top, a double, grooved, ridged, and inset thick border.  The color of all four sides should be based on purple.
</P>
<P class="eleven">
This paragraph should have no border at all.
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P class="one">
This paragraph should have a thick black dotted border all the way around.
</P>
<P class="two">
This paragraph should have a thick black dashed border all the way around.
</P>
<P class="three">
This paragraph should have a thick black solid border all the way around.
</P>
<P class="four">
This paragraph should have a thick black double border all the way around.
</P>
<P class="five">
This paragraph should have a thick olive groove border all the way around.
</P>
<P class="six">
This paragraph should have a thick olive ridge border all the way around.
</P>
<P class="seven">
This paragraph should have a thick olive inset border all the way around.
</P>
<P class="eight">
This paragraph should have a thick olive outset border all the way around.
</P>
<P class="nine">
This paragraph should have thick double top and bottom borders, and thick grooved side borders.  The color of all four sides should be based on purple.
</P>
<P class="ten">
This paragraph should have, in clockwise order from the top, a double, grooved, ridged, and inset thick border.  The color of all four sides should be based on purple.
</P>
<P class="eleven">
This paragraph should have no border at all.
</P>
</TD></TR></TABLE></BODY>
</HTML>
