#!/usr/bin/perl -wT

print <<EOM;
Content-Type: text/javascript
Access-Control-Allow-Origin: *
Cache-Control: private, max-age=1000

// So sorry about this waste of bytes:
// Filler comment, to trigger code caching heuristic (script > 1K.)
// Filler comment, to trigger code caching heuristic (script > 1K.)
// Filler comment, to trigger code caching heuristic (script > 1K.)
// Filler comment, to trigger code caching heuristic (script > 1K.)
// Filler comment, to trigger code caching heuristic (script > 1K.)
// Filler comment, to trigger code caching heuristic (script > 1K.)
// Filler comment, to trigger code caching heuristic (script > 1K.)
// Filler comment, to trigger code caching heuristic (script > 1K.)
// Filler comment, to trigger code caching heuristic (script > 1K.)
// Filler comment, to trigger code caching heuristic (script > 1K.)
// Filler comment, to trigger code caching heuristic (script > 1K.)
// Filler comment, to trigger code caching heuristic (script > 1K.)
// Filler comment, to trigger code caching heuristic (script > 1K.)
// Filler comment, to trigger code caching heuristic (script > 1K.)
// Filler comment, to trigger code caching heuristic (script > 1K.)
EOM
