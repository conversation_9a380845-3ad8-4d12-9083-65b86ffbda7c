{"name": "stylelint", "version": "14.2.0", "description": "A mighty, modern CSS linter.", "keywords": ["css-in-js", "css", "less", "lint", "linter", "markdown", "sass", "scss", "stylelint", "sugarss"], "homepage": "https://stylelint.io", "repository": "stylelint/stylelint", "funding": {"type": "opencollective", "url": "https://opencollective.com/stylelint"}, "license": "MIT", "author": "stylelint", "main": "lib/index.js", "types": "types/stylelint/index.d.ts", "bin": {"stylelint": "bin/stylelint.js"}, "files": ["bin/**/*.js", "lib/**/*.js", "!**/__tests__/**", "!lib/testUtils/**", "types/stylelint/index.d.ts"], "scripts": {"benchmark-rule": "node scripts/benchmark-rule.mjs", "format": "prettier . --write", "jest": "jest", "lint": "npm-run-all --parallel --continue-on-error lint:*", "lint:formatting": "prettier . --check", "lint:js": "eslint . --cache --max-warnings=0", "lint:md": "remark . --quiet --frail", "lint:types": "tsc", "prepare": "husky install", "release": "np", "pretest": "npm run lint", "test": "jest --coverage", "watch": "jest --watch"}, "lint-staged": {"*.js": "eslint --cache --fix", "*.{js,json,md,ts,yml}": "prettier --write"}, "prettier": "@stylelint/prettier-config", "eslintConfig": {"extends": ["stylelint"], "globals": {"__dirname": true, "module": true, "require": true, "testRule": true}, "root": true}, "remarkConfig": {"plugins": ["@stylelint/remark-preset"]}, "jest": {"clearMocks": true, "collectCoverage": false, "collectCoverageFrom": ["lib/**/*.js", "!lib/**/{__tests__,testUtils}/**/*.js"], "coverageDirectory": "./.coverage/", "coverageReporters": ["lcov", "text-summary"], "coverageThreshold": {"global": {"branches": 75, "functions": 75, "lines": 75, "statements": 75}}, "moduleNameMapper": {"^stylelint$": "<rootDir>/lib/index.js", "stylelint/lib/utils/getOsEol": "<rootDir>/lib/utils/getOsEol.js"}, "preset": "jest-preset-stylelint", "roots": ["lib", "system-tests"], "testEnvironment": "node", "testRegex": ".*\\.test\\.js$|rules/.*/__tests__/.*\\.js$", "watchPlugins": ["jest-watch-typeahead/filename", "jest-watch-typeahead/testname"]}, "dependencies": {"balanced-match": "^2.0.0", "colord": "^2.9.2", "cosmiconfig": "^7.0.1", "debug": "^4.3.3", "execall": "^2.0.0", "fast-glob": "^3.2.7", "fastest-levenshtein": "^1.0.12", "file-entry-cache": "^6.0.1", "get-stdin": "^8.0.0", "global-modules": "^2.0.0", "globby": "^11.0.4", "globjoin": "^0.1.4", "html-tags": "^3.1.0", "ignore": "^5.2.0", "import-lazy": "^4.0.0", "imurmurhash": "^0.1.4", "is-plain-object": "^5.0.0", "known-css-properties": "^0.24.0", "mathml-tag-names": "^2.1.3", "meow": "^9.0.0", "micromatch": "^4.0.4", "normalize-path": "^3.0.0", "normalize-selector": "^0.2.0", "picocolors": "^1.0.0", "postcss": "^8.3.11", "postcss-media-query-parser": "^0.2.3", "postcss-resolve-nested-selector": "^0.1.1", "postcss-safe-parser": "^6.0.0", "postcss-selector-parser": "^6.0.7", "postcss-value-parser": "^4.1.0", "resolve-from": "^5.0.0", "specificity": "^0.4.1", "string-width": "^4.2.3", "strip-ansi": "^6.0.1", "style-search": "^0.1.0", "svg-tags": "^1.0.0", "table": "^6.7.5", "v8-compile-cache": "^2.3.0", "write-file-atomic": "^3.0.3"}, "devDependencies": {"@stylelint/prettier-config": "^2.0.0", "@stylelint/remark-preset": "^3.0.0", "@types/balanced-match": "^1.0.1", "@types/debug": "^4.1.7", "@types/file-entry-cache": "^5.0.2", "@types/global-modules": "^2.0.0", "@types/globjoin": "^0.1.0", "@types/imurmurhash": "^0.1.1", "@types/micromatch": "^4.0.2", "@types/normalize-path": "^3.0.0", "@types/postcss-less": "^4.0.1", "@types/postcss-safe-parser": "^5.0.0", "@types/style-search": "^0.1.3", "@types/svg-tags": "^1.0.0", "@types/write-file-atomic": "^3.0.2", "benchmark": "^2.1.4", "common-tags": "^1.8.2", "deepmerge": "^4.2.2", "eslint": "^8.5.0", "eslint-config-stylelint": "^15.0.0", "eslint-plugin-jest": "^25.3.0", "husky": "^7.0.4", "jest": "^27.4.5", "jest-preset-stylelint": "^4.2.0", "jest-watch-typeahead": "^1.0.0", "lint-staged": "^12.1.3", "node-fetch": "^3.1.0", "np": "^7.6.0", "npm-run-all": "^4.1.5", "postcss-html": "^1.2.0", "postcss-import": "^14.0.2", "postcss-less": "^5.0.0", "postcss-sass": "^0.5.0", "postcss-scss": "^4.0.2", "prettier": "2.5.1", "remark-cli": "^10.0.1", "sugarss": "^4.0.1", "typescript": "^4.5.4"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}}