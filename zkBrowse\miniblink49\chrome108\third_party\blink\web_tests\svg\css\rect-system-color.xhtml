<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<script src="../../resources/js-test.js"/>
</head>
<body>
<svg display="none" xmlns="http://www.w3.org/2000/svg"/>
<p id="description"></p>
<div id="console"/>
<script><![CDATA[
SVG_NS = 'http://www.w3.org/2000/svg';
var computedStyleText, computedStyleDiv;
function checkSystemColor(id1, id2, systemColor)
{
    var textElement = document.getElementById(id1);
    computedStyleText = textElement.ownerDocument.defaultView.getComputedStyle(textElement);
    var divElement = document.getElementById(id2);
    computedStyleDiv = divElement.computedStyleMap();

    debug('Testing system color' + systemColor);
    shouldBe("computedStyleText.fill", "computedStyleDiv.get('color').toString()");
    shouldBe("computedStyleText.stroke", "computedStyleDiv.get('color').toString()");
}

description('Test that fill and stroke properties accept system colors');
debug('');

var systemColors = new Array("ActiveBorder", "ActiveCaption", "ActiveText", "AppWorkspace", "Background", "ButtonBorder",
                             "ButtonFace", "ButtonHighlight", "ButtonShadow", "ButtonText", "Canvas", "CanvasText",
                             "CaptionText", "Field", "FieldText", "GrayText", "Highlight", "HighlightText",
                             "InactiveBorder", "InactiveCaption", "InactiveCaptionText", "InfoBackground",
                             "InfoText", "LinkText", "Mark", "MarkText", "Menu", "MenuText", "Scrollbar",
                             "SelectedItem", "SelectedItemText", "ThreeDDarkShadow", "ThreeDFace",
                             "ThreeDHighlight", "ThreeDLightShadow", "ThreeDShadow", "VisitedText",
                             "Window", "WindowFrame", "WindowText");
var svgElement = document.getElementsByTagName("svg")[0];
for (i = 0; i < systemColors.length; ++i) {
    var textElement = document.createElementNS(SVG_NS, "text");
    textElement.setAttribute("id", "text" + i);
    textElement.setAttribute("stroke", systemColors[i]);
    textElement.setAttribute("style", "fill:" + systemColors[i]);
    svgElement.appendChild(textElement);

    var divElement = document.createElement("div");
    divElement.setAttribute("id", "expected" + i);
    divElement.setAttribute("style", "color:" + systemColors[i]);
    document.body.appendChild(divElement);

    checkSystemColor('text' + i, 'expected' + i, systemColors[i]);
}

]]>
</script>

</body>
</html>
