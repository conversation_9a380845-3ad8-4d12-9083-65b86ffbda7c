<!DOCTYPE html>
<title>drawImage() with SVG fragments</title>
<script>
    onload = function() {
        var context = document.getElementsByTagName('canvas')[0].getContext('2d');
        var images = document.getElementsByTagName('img');
        for (var i = 0; i < images.length; i++) {
            var newImage = new Image();
            newImage.src = images[i].src;
            context.drawImage(newImage, i*60, i*60, 120, 120);
        }
        while (images.length)
            document.body.removeChild(images.item(0));
    }
</script>
<canvas width="240" height="240"></canvas>
<img src="../css/resources/fragment-identifiers.svg#green">
<img src="../css/resources/fragment-identifiers.svg#red">
<img src="../css/resources/fragment-identifiers.svg#blue">
