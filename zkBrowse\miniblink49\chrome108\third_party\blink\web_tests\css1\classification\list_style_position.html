<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.6.5 list-style-position</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
.one {list-style-position: outside;}
.two {list-style-position: inside;}
</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.one {list-style-position: outside;}
.two {list-style-position: inside;}

</PRE>
<HR>
<UL class="one">
<LI>The text in this item should behave as expected; that is, it should line up with itself on the left margin, leaving blank space beneath the bullet.
</UL>

<UL class="two">
<LI>The text in this item should not behave as expected; that is, it should line up with the bullet on the left margin, leaving no blank space beneath the bullet.
</UL>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><UL class="one">
<LI>The text in this item should behave as expected; that is, it should line up with itself on the left margin, leaving blank space beneath the bullet.
</UL>

<UL class="two">
<LI>The text in this item should not behave as expected; that is, it should line up with the bullet on the left margin, leaving no blank space beneath the bullet.
</UL>
</TD></TR></TABLE></BODY>
</HTML>
