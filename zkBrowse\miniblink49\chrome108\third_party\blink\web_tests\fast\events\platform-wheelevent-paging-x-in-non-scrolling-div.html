<!DOCTYPE html>
<script src='../../resources/gesture-util.js'></script>
<script src="../../resources/testharness.js"></script>
<script src="../../resources/testharnessreport.js"></script>
<div id="overflow" style="border:2px solid black;overflow:auto;height:200px;width:200px;">
  <div style="border:0px;background-color:red;height:300px;width:185px;"></div>
  <div style="border:0px;background-color:green;height:300px;width:185px;"></div>
</div>

<script>
var givenScrollLeft = 2;
var expectedScrollLeft = 0;
var last_event = null;
var source = GestureSourceType.MOUSE_INPUT;
const numTicksX = givenScrollLeft / pixelsPerTick();
const expectedWheelDeltaX = numTicksX * LEGACY_MOUSE_WHEEL_TICK_MULTIPLIER;

function mousewheelHandler(e)
{
    last_event = e;
}

promise_test(async () => {
    var overflowElement = document.getElementById("overflow");
    overflowElement.addEventListener("mousewheel", mousewheelHandler, false);

    await smoothScroll(givenScrollLeft, 100, 110, source, 'right', SPEED_INSTANT, false /* precise_scrolling_deltas */, true /* scroll_by_page */);
    await conditionHolds( () => {
        return overflowElement.scrollLeft == window.expectedScrollLeft;
    });
    assert_equals(last_event.wheelDeltaX, -Math.floor(expectedWheelDeltaX));
    assert_equals(last_event.wheelDelta, -Math.floor(expectedWheelDeltaX));
}, 'one page of scroll on div moves the content by 0 pixels.');
</script>
