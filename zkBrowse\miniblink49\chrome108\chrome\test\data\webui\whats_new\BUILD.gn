# Copyright 2021 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//tools/typescript/ts_library.gni")
import("//ui/webui/resources/tools/generate_grd.gni")

assert(!is_android)

ts_library("build_ts") {
  root_dir = "."
  out_dir = "$target_gen_dir/tsc"
  tsconfig_base = "tsconfig_base.json"
  path_mappings = [
    "chrome://whats-new/*|" +
        rebase_path("$root_gen_dir/chrome/browser/resources/whats_new/tsc/*",
                    target_gen_dir),
    "chrome://webui-test/*|" +
        rebase_path("$root_gen_dir/chrome/test/data/webui/tsc/*",
                    target_gen_dir),
  ]
  in_files = [ "whats_new_app_test.ts" ]
  deps = [
    "..:build_ts",
    "//chrome/browser/resources/whats_new:build_ts",
  ]
}

generate_grd("build_grdp") {
  grd_prefix = "webui_whats_new"
  out_grd = "$target_gen_dir/resources.grdp"

  input_files_base_dir = rebase_path(".", "//")
  input_files = [
    "test.html",
    "test_with_command_3.html",
    "test_with_command_3.js",
  ]

  deps = [ ":build_ts" ]
  manifest_files =
      filter_include(get_target_outputs(":build_ts"), [ "*.manifest" ])
  resource_path_prefix = "whats_new"
}
