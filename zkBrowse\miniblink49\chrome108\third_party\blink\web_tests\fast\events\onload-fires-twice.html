<html>
<head>
    <!-- any unknown element in HEAD will trigger creation of a BODY node -->
    <anytag>

    <script type="text/javascript">
        if (window.testRunner)
            testRunner.dumpAsText()

        var count = 2;
        function testDoubleLoad() {
            --count;
            var result = document.getElementById("result");
            if (count == 1)
                result.innerHTML = "Test passed.";
            else
                result.innerHTML = "Test failed (fired more than once).";
        }
    </script>
</head>
<body onload="testDoubleLoad()">
    <p>Makes sure an inline "load" event does not fire twice. This may occur if
    an inline event listener is not removed prior to being re-added in the case
    of multiple body nodes in the document. You should see "Test passed" below.
    </p>
    <div id='result'>Test failed (never ran).</div>
</body>
</html>
