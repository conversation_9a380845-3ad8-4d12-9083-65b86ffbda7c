<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright 2022 The Chromium Authors
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file.
-->
<org.chromium.components.browser_ui.widget.FadingEdgeScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/avs_consent_ui"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:importantForAccessibility="no">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center_vertical"
        android:paddingHorizontal="20dp"
        android:paddingVertical="10dp"
        android:importantForAccessibility="no">
        <ImageView
            android:layout_width="match_parent"
            android:layout_height="122dp"
            android:layout_marginTop="20dp"
            android:importantForAccessibility="no"
            android:src="@drawable/assistant_consent_illustration"/>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            style="@style/TextAppearance.Headline.Primary"
            android:text="@string/avs_consent_ui_simplified_title" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            style="@style/TextAppearance.TextSmall.Secondary"
            android:text="@string/avs_consent_ui_simplified_body" />

        <TextView
            android:id="@+id/avs_consent_ui_learn_more"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            style="@style/TextAppearance.TextSmall.Link"
            android:text="@string/learn_more" />

    </LinearLayout>
</org.chromium.components.browser_ui.widget.FadingEdgeScrollView>
