<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background-position applied to elements with 'display' set to 'table-header-group'</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="<PERSON><PERSON>" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-11-29 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background-position" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <link rel="match" href="background-position-applies-to-001-ref.xht" />

        <meta name="flags" content="image" />
        <meta name="assert" content="The 'background-position' property applies to elements with 'display' set to 'table-header-group'." />
        <style type="text/css">
            #test
            {
                background-image: url('support/blue15x15.png');
                background-position: bottom right;
                background-repeat: no-repeat;
                display: table-header-group;
            }
            #table
            {
                border: solid black;
                display: table;
                table-layout: fixed;
                width: 1in;
            }
            .row
            {
                display: table-row;
            }
            .cell
            {
                color: white;
                display: table-cell;
                height: 0.5in;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is 1 and only 1 small filled blue square and if it is located in the lower-right corner of a hollow black square.</p>

        <div id="table">

            <div id="test">

                <div class="row">
                    <div class="cell">a</div><div class="cell">b</div>
                </div>

                <div class="row">
                    <div class="cell">c</div><div class="cell">d</div>
                </div>

            </div>

        </div>

    </body>
</html>