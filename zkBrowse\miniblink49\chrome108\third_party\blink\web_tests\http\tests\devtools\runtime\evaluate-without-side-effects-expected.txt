Test frontend's side-effect support check for compatibility.


Testing expression (async function(){ await 1; })() with throwOnSideEffect true
Exception: EvalError: Possible side-effect in debug-evaluate

Testing expression (async function(){ await 1; })() with throwOnSideEffect false
Result: Promise

Testing expression 1 + 1 with throwOnSideEffect true
Result: 2

Testing expression 1 + 1 with throwOnSideEffect false
Result: 2
Does the runtime support side effect checks? true

Clearing cached side effect support
Set timer for test function.
Script execution paused.

Testing expression (async function(){ await 1; })() with throwOnSideEffect true
Exception: EvalError: Possible side-effect in debug-evaluate

Testing expression (async function(){ await 1; })() with throwOnSideEffect false
Result: Promise

Testing expression 1 + 1 with throwOnSideEffect true
Result: 2

Testing expression 1 + 1 with throwOnSideEffect false
Result: 2
Does the runtime support side effect checks? true
Script execution resumed.

