<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright 2020 The Chromium Authors
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file.
-->
<!-- Layout for each item in the menu popup -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/AppMenuItem"
    android:layout_width="match_parent"
    android:layout_height="?android:attr/listPreferredItemHeightSmall"
    android:gravity="center_vertical"
    android:orientation="horizontal" >

    <org.chromium.ui.widget.ChromeImageView
        android:id="@+id/menu_item_icon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:gravity="center_vertical"
        android:tint="@color/default_icon_color_secondary_tint_list"
        android:tintMode="src_in"
        android:duplicateParentState="true" />

    <TextView
        android:id="@+id/menu_item_text"
        android:textAppearance="@style/TextAppearance.TextLarge.Primary"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:gravity="center_vertical"
        android:singleLine="true"
        android:paddingStart="12dp"
        android:duplicateParentState="true" />

</LinearLayout>
