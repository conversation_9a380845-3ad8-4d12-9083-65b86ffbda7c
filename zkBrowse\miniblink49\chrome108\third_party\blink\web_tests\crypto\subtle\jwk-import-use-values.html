<!DOCTYPE html>
<html>
<head>
<script src="../../resources/js-test.js"></script>
<script src="resources/common.js"></script>
</head>
<body>
<p id="description"></p>
<div id="console"></div>

<script>
description("Test importing keys with various uses from JWK.");

jsTestIsAsync = true;

var extractable = true;

var aesKeyAsJSON = {
    "alg": "A128CBC",
    "ext": true,
    "kty": "oct",
    "k": "jnOw99oOZFLIEPMrgJB55Q"
};

var hmacKeyAsJSON = {
    "alg": "HS256",
    "ext": true,
    "kty": "oct",
    "k": "ahjkn-_387fgnsibf23qsvahjkn-_387fgnsibf23qs"
};

function testWithAESCBC(expectedUsages, jwkUsages, importUsages)
{
    if (jwkUsages.key_ops) {
        aesKeyAsJSON.key_ops = jwkUsages.key_ops;
        delete aesKeyAsJSON.use;
    } else {
        delete aesKeyAsJSON.key_ops;
        aesKeyAsJSON.use = jwkUsages.use;
    }

    return crypto.subtle.importKey("jwk", aesKeyAsJSON, {name: "AES-CBC"}, extractable, importUsages).then(function(result) {
        key = result;
        debug(JSON.stringify(jwkUsages) + ":");
        shouldBe("key.usages", JSON.stringify(expectedUsages));
        debug("");
    }, function(result) {
        debug(JSON.stringify(jwkUsages) + ":");
        debug("Failed importing with " + importUsages + ": " + result);
    });
}

function testWithHMAC(expectedUsages, jwkUsages, importUsages)
{
    if (jwkUsages.key_ops) {
        hmacKeyAsJSON.key_ops = jwkUsages.key_ops;
        delete hmacKeyAsJSON.use;
    } else {
        delete hmacKeyAsJSON.key_ops;
        hmacKeyAsJSON.use = jwkUsages.use;
    }

    return crypto.subtle.importKey("jwk", hmacKeyAsJSON, {name: 'hmac', hash: {name: 'sha-256'}}, extractable, importUsages).then(function(result) {
        key = result;
        debug(JSON.stringify(jwkUsages) + ":");
        shouldBe("key.usages", JSON.stringify(expectedUsages));
        debug("");
    }, function(result) {
        debug(JSON.stringify(jwkUsages) + ":");
        debug("Failed importing with " + importUsages + ": " + result);
    });
}

debug("");


Promise.all([
    // Duplicates are not allowed.
    testWithAESCBC(null, {key_ops: ["encrypt", "encrypt"]}, ["encrypt"]),

    testWithAESCBC(["encrypt"], {key_ops: ["encrypt"]}, ["encrypt"]),
    testWithAESCBC(null, {key_ops: ["encrypt"]}, ["decrypt"]),

    testWithAESCBC(["decrypt"], {key_ops: ["decrypt"]}, ["decrypt"]),
    testWithAESCBC(null, {key_ops: ["decrypt"]}, ["encrypt"]),

    testWithAESCBC(["encrypt", "decrypt"], {key_ops: ["encrypt", "decrypt"]}, ["encrypt", "decrypt"]),
    testWithAESCBC(["encrypt"], {key_ops: ["encrypt", "decrypt"]}, ["encrypt"]),
    testWithAESCBC(null, {key_ops: ["encrypt", "decrypt"]}, ["unwrapKey"]),

    testWithAESCBC(["wrapKey"], {key_ops: ["wrapKey"]}, ["wrapKey"]),
    testWithAESCBC(null, {key_ops: ["wrapKey"]}, ["unwrapKey"]),

    testWithAESCBC(["unwrapKey"], {key_ops: ["unwrapKey"]}, ["unwrapKey"]),
    testWithAESCBC(["wrapKey", "unwrapKey"], {key_ops: ["wrapKey", "unwrapKey"]}, ["unwrapKey", "wrapKey"]),
    testWithAESCBC(["encrypt", "decrypt", "wrapKey"], {key_ops: ["encrypt", "decrypt", "wrapKey"]}, ["decrypt", "encrypt", "wrapKey"]),

    testWithAESCBC(["encrypt", "decrypt", "wrapKey", "unwrapKey"], {use: "enc"}, ["decrypt", "encrypt", "unwrapKey", "wrapKey"]),
    testWithAESCBC(["encrypt", "decrypt", "unwrapKey"], {use: "enc"}, ["decrypt", "encrypt", "unwrapKey"]),
    testWithAESCBC(["encrypt", "decrypt", "unwrapKey"], {use: "enc"}, ["decrypt", "encrypt", "unwrapKey"]),

    testWithHMAC(["sign"], {key_ops: ["sign"]}, ["sign"]),
    testWithHMAC(null, {key_ops: ["sign"]}, ["verify"]),

    testWithHMAC(["verify"], {key_ops: ["verify"]}, ["verify"]),
    testWithHMAC(null, {key_ops: ["verify"]}, ["sign"]),

    testWithHMAC(["sign", "verify"], {use: "sig"}, ["sign", "verify"]),
    testWithHMAC(["sign"], {use: "sig"}, ["sign"]),

    // Unknown key_ops strings are ignored.
    testWithAESCBC(["decrypt"], {key_ops: ["'encrypt'", "decrypt"]}, ["decrypt"]),
    testWithAESCBC(["decrypt"], {key_ops: ["encrypt ", "foo", "decrypt"]}, ["decrypt"]),
    testWithAESCBC(["decrypt"], {key_ops: ["Encrypt", "decrypt"]}, ["decrypt"]),
    testWithAESCBC(null, {key_ops: ["'encrypt'", "decrypt"]}, ["encrypt"]),
    testWithAESCBC(null, {key_ops: ["encrypt "]}, ["encrypt"]),
    testWithAESCBC(null, {key_ops: ["Encrypt"]}, ["encrypt"]),

]).then(finishJSTest, failAndFinishJSTest);
</script>

</body>
</html>
