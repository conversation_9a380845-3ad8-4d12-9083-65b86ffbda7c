<!DOCTYPE html>
<html>
<head>
    <script>
        window.jsTestIsAsync = true;
        window.isOnErrorTest = true;
    </script>
    <script src="../../resources/js-test.js"></script>
    <script src="resources/onerror-test.js"></script>
</head>
<body onload="setTimeout(function () { throwException('exception in setTimeout'); }, 0); throwException('exception in onload');">
    <script>
        description("This test should trigger 'window.onerror' multiple times, and successfully handle the errors.");

        function callback(errorsHandled) {
            if (errorsHandled === 3)
                finishJSTest();
        }

        dumpOnErrorArgumentValuesAndReturn(true, callback);

        throwException('Inline exception.');
    </script>
</body>
</html>
