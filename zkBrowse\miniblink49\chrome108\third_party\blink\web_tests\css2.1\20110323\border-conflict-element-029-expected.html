<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
    <head>
        <title>CSS Test: Border Conflict Resolution (by element): column vs. column - element with highest priority is the dominant border (rtl table)</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/">
        <link rel="help" href="http://www.w3.org/TR/CSS21/tables.html#border-conflict-resolution">
        <meta name="flags" content="">
        <meta name="assert" content="With adjacent elements (column, column) of same border-style and width, border collapsing favors the column farthest to the right in a 'right-to-left' table.">
        <style type="text/css">
            table
            {
                border-collapse: collapse;
                direction: rtl;
                height: 2in;
                width: 2in;
            }
            col
            {
                border-bottom: 5px solid black;
                border-top: 5px solid black;
            }
            #winning
            {
                border-left: 5px solid black;
                border-right: 5px solid black;
            }
            .collapsing
            {
                 border-left: 5px solid black;
                 border-right: 5px solid black;
            }
            td
            {
                direction: ltr;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is no red visible on the page.</p>
        <table>
            <col id="winning">
            <col class="collapsing">
            <col class="collapsing">
            <tr>
                <td></td>
                <td></td>
                <td></td>
            </tr>
        </table>
    </body>
</html>