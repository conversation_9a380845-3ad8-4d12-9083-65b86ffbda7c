<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.5.26 clear</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
.one {clear: left;}
.two {clear: right;}
.three {clear: both;}
.four {clear: none;}</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.one {clear: left;}
.two {clear: right;}
.three {clear: both;}
.four {clear: none;}
</PRE>
<HR>
<IMG SRC="../resources/vblank.gif" height="50" align="left" alt="[Image]">
<P>
This text should be flowing past a tall orange rectangle on the left side of the browser window.
</P>
<BR clear="all">
<IMG SRC="../resources/vblank.gif" height="50" align="left" alt="[Image]">
<P class="one">
This paragraph should appear below the tall orange rectangle above and to the left, and not flow past it. 
</P>
<BR clear="all">
<IMG SRC="../resources/vblank.gif" height="50" align="right" alt="[Image]">
<P class="two">
This paragraph should appear below the tall orange rectangle above and to the right, and not flow past it. 
</P>
<BR clear="all">
<IMG SRC="../resources/vblank.gif" height="50" align="left" alt="[Image]">
<IMG SRC="../resources/vblank.gif" height="50" align="right" alt="[Image]">
<P class="three">
This paragraph should appear below the two tall orange rectangles, and not flow between them. 
</P>
<IMG SRC="../resources/vblank.gif" height="50" align="left" alt="[Image]">
<IMG SRC="../resources/vblank.gif" height="50" align="right" alt="[Image]">
<P class="four">
This paragraph should be between both tall orange rectangles.
</P>
<BR clear="all">


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><IMG SRC="../resources/vblank.gif" height="50" align="left" alt="[Image]">
<P>
This text should be flowing past a tall orange rectangle on the left side of the browser window.
</P>
<BR clear="all">
<IMG SRC="../resources/vblank.gif" height="50" align="left" alt="[Image]">
<P class="one">
This paragraph should appear below the tall orange rectangle above and to the left, and not flow past it. 
</P>
<BR clear="all">
<IMG SRC="../resources/vblank.gif" height="50" align="right" alt="[Image]">
<P class="two">
This paragraph should appear below the tall orange rectangle above and to the right, and not flow past it. 
</P>
<BR clear="all">
<IMG SRC="../resources/vblank.gif" height="50" align="left" alt="[Image]">
<IMG SRC="../resources/vblank.gif" height="50" align="right" alt="[Image]">
<P class="three">
This paragraph should appear below the two tall orange rectangles, and not flow between them. 
</P>
<IMG SRC="../resources/vblank.gif" height="50" align="left" alt="[Image]">
<IMG SRC="../resources/vblank.gif" height="50" align="right" alt="[Image]">
<P class="four">
This paragraph should be between both tall orange rectangles.
</P>
<BR clear="all">
</TD></TR></TABLE></BODY>
</HTML>
