Tests that a warning is shown in the console if addEventListener is called after initial evaluation of the service worker script.

Message count: 1
Message count: 2
service-worker-lazy-…deventlistener.js:2 Event handler of 'install' event must be added on the initial evaluation of worker script.
(anonymous) @ service-worker-lazy-…deventlistener.js:2
setTimeout (async)
(anonymous) @ service-worker-lazy-…deventlistener.js:1
service-worker-lazy-…deventlistener.js:2 Event handler of 'fetch' event must be added on the initial evaluation of worker script.
(anonymous) @ service-worker-lazy-…deventlistener.js:2
setTimeout (async)
(anonymous) @ service-worker-lazy-…deventlistener.js:1

