  1) LOAD 4  // Architecture
  2) if A == 0x40000003; then JMP 3 else JMP 18
  3) LOAD 0  // System call number
  4) if A & 0x40000000; then JMP 18 else JMP 5
  5) if A >= 0x18; then JMP 6 else JMP 7
  6) if A >= 0x401; then JMP 26 else JMP 25
  7) if A >= 0x17; then JMP 8 else JMP 25
  8) LOAD 20  // Argument 0 (MSB)
  9) if A == 0x0; then JMP 10 else JMP 18
 10) LOAD 16  // Argument 0 (LSB)
 11) if A & 0xfff; then JMP 12 else JMP 24
 12) LOAD 20  // Argument 0 (MSB)
 13) if A == 0x0; then JMP 14 else JMP 18
 14) LOAD 16  // Argument 0 (LSB)
 15) if A & 0xff0; then JMP 16 else JMP 23
 16) LOAD 20  // Argument 0 (MSB)
 17) if A == 0x0; then JMP 19 else JMP 18
 18) RET 0x0  // Kill
 19) LOAD 16  // Argument 0 (LSB)
 20) if A & 0xf00; then JMP 21 else JMP 22
 21) RET 0x5000d  // errno = 13
 22) RET 0x50011  // errno = 17
 23) RET 0x50016  // errno = 22
 24) RET 0x50000  // errno = 0
 25) RET 0x7fff0000  // Allowed
 26) RET 0x50026  // errno = 38
