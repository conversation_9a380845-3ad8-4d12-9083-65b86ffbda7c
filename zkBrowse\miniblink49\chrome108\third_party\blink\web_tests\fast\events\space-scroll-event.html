<body>
<p>Test for <a href="http://bugs.webkit.org/show_bug.cgi?id=16421">bug 16241</a>:
REGRESSION(r28669): Page scrolls down when you hit space key in text area.</p>
<p>To test manually, press Space - the page should not scroll.</p>
<div style="width:1;height:5000"></div>
<script>
document.onkeypress=function(){return false;}
document.onscroll=function(){alert("FAIL - scrolled!");}

if (window.testRunner) {
    testRunner.dumpAsText();
    eventSender.keyDown(" ", []);
}
</script>
</body>
