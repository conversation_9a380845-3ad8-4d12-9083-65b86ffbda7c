<html>
<head>
<script>

if (window.testRunner)
    testRunner.dumpAsText();

function mouseOver(element)
{
    if (!window.eventSender)
        return;

    var x = element.offsetLeft + element.offsetWidth / 2;
    var y = element.offsetTop + element.offsetHeight / 2;
    eventSender.mouseMoveTo(x, y);
}

function runTest()
{
    var area = document.getElementById('area');
    mouseOver(area);
    area.innerHTML = '';
    mouseOver(document.getElementById('description'));
}

</script>
</head>
<body onload="runTest()">
    <p id="description">Ensures that a "mouseout" event, fired on shadow DOM element of a destroyed host element doesn't crash the browser.
    <p>Passes if doesn't crash.
    <div id="area" style="width:100px;height:100px">
        <textarea style="width:100px;height:100px">Foo</textarea>
    </div>
</body>
</html>
