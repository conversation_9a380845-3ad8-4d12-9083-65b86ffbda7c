<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8"/>
<title>unicode-bidi: div plaintext with br</title>

<link rel="author" title="<PERSON>" href='mailto:<EMAIL>'/>
<link rel="help" href='http://www.w3.org/TR/css-writing-modes-3/#text-direction'/>
<link rel="match" href='reference/block-plaintext-004.html'/>
<meta name="assert" content='If unicode-bidi:plaintext is applied to a div element containing br tags, each line of characters that starts after br is displayed according to the first strong character after the br.'/>
<style type="text/css">
.test { unicode-bidi: plaintext; }

 /* the following styles are not part of the test */
.test, .ref { font-size: 150%; border: 1px solid orange; margin: 10px; width: 10em; padding: 5px; clear: both; }
input { margin: 5px; }
@font-face {
    font-family: 'ezra_silregular';
    src: url('/fonts/sileot-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;
    }
.test, .ref { font-family: ezra_silregular, serif; }
</style>
</head>
<body>
<p class="instructions" dir="ltr">Test passes if the two boxes are identical.</p>


<!--Notes:
Key to entities used below:
        &#x5d0; ... &#x5d5; - The first six Hebrew letters (strongly RTL).
        &#x202d; - The LRO (left-to-right-override) formatting character.
        &#x202c; - The PDF (pop directional formatting) formatting character; closes LRO.
-->


<div class="test">
&gt; a &gt; &#x5d1; &gt; c &gt;<br/>
&gt; &#x5d0; &gt; b &gt; &#x5d2; &gt;<br/>
&gt; a &gt; &#x5d1; &gt; c &gt;<br/>
</div>

<div class="ref">
<div dir="ltr">&#x202d;&gt; a &gt; &#x5d1; &gt; c &gt;&#x202c;</div>
<div dir="rtl">&#x202d;&lt;  &#x5d2; &lt; b &lt;  &#x5d0; &lt;&#x202c;</div>
<div dir="ltr">&#x202d;&gt; a &gt; &#x5d1; &gt; c &gt;&#x202c;</div>
</div>





</body></html>