<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Border-top-width using 'em' units with a minimum minus one (negative) value, -1em</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="<PERSON><PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-06-24 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#propdef-border-top-width" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#border-width-properties" />
        <link rel="match" href="border-top-width-012-ref.xht" />

        <meta name="flags" content="invalid" />
        <meta name="assert" content="The 'border-top-width' property does not support a negative length value in 'em' units and resets to the initial value." />
        <style type="text/css">
            span
            {
                border-top-style: solid;
            }
            #span1
            {
                border-top-width: -1em;
            }
            #span2
            {
                border-top-width: medium;
            }
        </style>
    </head>
    <body>
        <p>Test passes if all the "Filler Text" have 2 black lines over them with the <strong>same thickness</strong>.</p>
        <div>
            <span id="span1">Filler Text Filler Text Filler Text</span> <span id="span2">Filler Text Filler Text Filler Text</span>
        </div>
    </body>
</html>