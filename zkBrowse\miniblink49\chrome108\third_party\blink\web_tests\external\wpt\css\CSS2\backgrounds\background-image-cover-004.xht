<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background-image tiling over margin</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="<PERSON><PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-04-21 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <link rel="match" href="background-image-cover-004-ref.xht" />

        <meta name="flags" content="image" />
        <meta name="assert" content="Background-image tiling does not color the margin." />
        <style type="text/css">
            div
            {
                background-image: url("support/blue15x15.png");
                border: solid black;
                height: 200px;
                margin: 30px;
            }
        </style>
    </head>
    <body>
        <p>Test passes if the big hollow black rectange is completely filled with a blue background. There is no blue outside the black border.</p>
        <div></div>
    </body>
</html>