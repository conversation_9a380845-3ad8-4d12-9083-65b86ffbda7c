<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background-repeat set to 'repeat-y'</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="<PERSON><PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-05-04 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background-repeat" />
        <link rel="match" href="background-repeat-003-ref.xht" />

        <meta name="flags" content="image" />
        <meta name="assert" content="Background-repeat set to 'repeat-y' repeats the image vertically to fill the space." />
        <style type="text/css">
            div
            {
                background-color: black;
                background-image: url("support/blue15x15.png");
                background-repeat: repeat-y;
                height: 200px;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is a vertical blue stripe to the left of a filled black rectangle. Also, the blue stripe and black rectangle have the same height.</p>
        <div></div>
    </body>
</html>