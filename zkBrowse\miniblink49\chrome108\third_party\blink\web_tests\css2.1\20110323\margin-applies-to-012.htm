<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
    <head>
        <title>CSS Test: Margin applied to element with display inline-block</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/">
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#propdef-margin">
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#margin-properties">
        <meta name="flags" content="">
        <meta name="assert" content="The 'Margin' property applies to elements with a display of inline-block.">
        <style type="text/css">
            #wrapper
            {
                border: 10px solid blue;
                position: absolute;
            }
            #test
            {
                border: 10px solid orange;
                display: inline-block;
                height: 200px;
                width: 200px;
                margin: 50px;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is space between the blue and orange lines below.</p>
        <div id="wrapper">
            <div id="test"></div>
        </div>
    </body>
</html>