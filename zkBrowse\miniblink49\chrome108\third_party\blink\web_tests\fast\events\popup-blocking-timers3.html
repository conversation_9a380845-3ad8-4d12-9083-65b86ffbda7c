<!DOCTYPE html>
<head>
    <script src="../../resources/js-test.js"></script>
    <script src="../../resources/testdriver.js"></script>
    <script src="../../resources/testdriver-vendor.js"></script>
    <script>
        var newWindow;
        var intervalId;
        var firstIntervalExecution = true;

        if (window.testRunner) {
            testRunner.dumpAsText();
            testRunner.waitUntilDone();
        }

        function clickHandler() {
            intervalId = setInterval(function() {
                debug("Test calling window.open() in a 100 ms interval. A popup should only be allowed on the first execution of the interval.");
                newWindow = window.open("about:blank");
                self.focus();
                if (firstIntervalExecution) {
                    shouldBeNonNull("newWindow");
                    firstIntervalExecution = false;
                } else {
                    shouldBeNull("newWindow");
                    clearInterval(intervalId);
                    if (window.testRunner)
                        testRunner.notifyDone();
                }
            }, 100);
        }

        function clickButton() {
            var button = document.getElementById("test");
            test_driver.click(button);
        }       
    </script>
</head>
<body onload="clickButton()">
    <button id="test" onclick="clickHandler()">Click Here</button>
    <div id="console"></div>
</body>
