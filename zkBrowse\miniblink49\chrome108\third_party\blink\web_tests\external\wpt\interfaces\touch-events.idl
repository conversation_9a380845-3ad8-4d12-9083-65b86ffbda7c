// GENERATED CONTENT - DO NOT EDIT
// Content was automatically extracted by <PERSON><PERSON> into webref
// (https://github.com/w3c/webref)
// Source: Touch Events - Level 2 (https://w3c.github.io/touch-events/)

enum TouchType {
    "direct",
    "stylus"
};

dictionary TouchInit {
    required long        identifier;
    required EventTarget target;
             double      clientX = 0;
             double      clientY = 0;
             double      screenX = 0;
             double      screenY = 0;
             double      pageX = 0;
             double      pageY = 0;
             float       radiusX = 0;
             float       radiusY = 0;
             float       rotationAngle = 0;
             float       force = 0;
             double      altitudeAngle = 0;
             double      azimuthAngle = 0;
             TouchType   touchType = "direct";
};

[Exposed=Window]
interface Touch {
    constructor(TouchInit touchInitDict);
    readonly        attribute long        identifier;
    readonly        attribute EventTarget target;
    readonly        attribute double      screenX;
    readonly        attribute double      screenY;
    readonly        attribute double      clientX;
    readonly        attribute double      clientY;
    readonly        attribute double      pageX;
    readonly        attribute double      pageY;
    readonly        attribute float       radiusX;
    readonly        attribute float       radiusY;
    readonly        attribute float       rotationAngle;
    readonly        attribute float       force;
    readonly        attribute float       altitudeAngle;
    readonly        attribute float       azimuthAngle;
    readonly        attribute TouchType   touchType;
};

[Exposed=Window]
interface TouchList {
    readonly        attribute unsigned long length;
    getter Touch? item (unsigned long index);
};

dictionary TouchEventInit : EventModifierInit {
             sequence<Touch> touches = [];
             sequence<Touch> targetTouches = [];
             sequence<Touch> changedTouches = [];
};

[Exposed=Window]
interface TouchEvent : UIEvent {
    constructor(DOMString type, optional TouchEventInit eventInitDict = {});
    readonly        attribute TouchList touches;
    readonly        attribute TouchList targetTouches;
    readonly        attribute TouchList changedTouches;
    readonly        attribute boolean   altKey;
    readonly        attribute boolean   metaKey;
    readonly        attribute boolean   ctrlKey;
    readonly        attribute boolean   shiftKey;
    getter boolean getModifierState (DOMString keyArg);
};

partial interface mixin GlobalEventHandlers {
                    attribute EventHandler ontouchstart;
                    attribute EventHandler ontouchend;
                    attribute EventHandler ontouchmove;
                    attribute EventHandler ontouchcancel;
};
