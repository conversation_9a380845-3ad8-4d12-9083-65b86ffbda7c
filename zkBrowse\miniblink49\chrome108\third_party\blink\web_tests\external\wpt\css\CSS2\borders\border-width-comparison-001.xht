<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Border-width - Relation of thin, medium, and thick</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="Gérard Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-06-26 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#border-width-properties" />
        <meta name="assert" content="A border-width set to thin is small than a border-width that is set to medium which is smaller than a border-width that is set to thick." />
        <style type="text/css">
            #thin
            {
                border-top: thin solid green;
                height: 20px;
            }
            #medium
            {
                border-top: medium solid green;
                height: 20px;
            }
            #thick
            {
                border-top: thick solid green;
                height: 20px;
            }
        </style>
    </head>
    <body>
        <p>Test passes if the three lines get thicker from top to bottom.</p>
        <div id="thin"></div>
        <div id="medium"></div>
        <div id="thick"></div>
    </body>
</html>