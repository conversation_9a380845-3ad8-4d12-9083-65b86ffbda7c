// Copyright 2014 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// This file has been auto-generated by code_generator_v8.py. DO NOT MODIFY!

#include "config.h"
#include "V8DOMImplementation.h"

#include "bindings/core/v8/ExceptionState.h"
#include "bindings/core/v8/V8DOMConfiguration.h"
#include "bindings/core/v8/V8DocumentType.h"
#include "bindings/core/v8/V8GCController.h"
#include "bindings/core/v8/V8HTMLDocument.h"
#include "bindings/core/v8/V8ObjectConstructor.h"
#include "bindings/core/v8/V8XMLDocument.h"
#include "core/dom/ContextFeatures.h"
#include "core/dom/Document.h"
#include "core/dom/Element.h"
#include "platform/RuntimeEnabledFeatures.h"
#include "platform/TraceEvent.h"
#include "wtf/GetPtr.h"
#include "wtf/RefPtr.h"

// V8 10.8 compatibility layer
#if V8_MAJOR_VERSION >= 10
#include "../../../v8_10_8/v8_compatibility.h"
#endif

namespace blink {

// Suppress warning: global constructors, because struct WrapperTypeInfo is trivial
// and does not depend on another global objects.
#if defined(COMPONENT_BUILD) && defined(WIN32) && COMPILER(CLANG)
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wglobal-constructors"
#endif
const WrapperTypeInfo V8DOMImplementation::wrapperTypeInfo = { gin::kEmbedderBlink, V8DOMImplementation::domTemplate, V8DOMImplementation::refObject, V8DOMImplementation::derefObject, V8DOMImplementation::trace, 0, V8DOMImplementation::visitDOMWrapper, V8DOMImplementation::preparePrototypeObject, V8DOMImplementation::installConditionallyEnabledProperties, "DOMImplementation", 0, WrapperTypeInfo::WrapperTypeObjectPrototype, WrapperTypeInfo::ObjectClassId, WrapperTypeInfo::NotInheritFromEventTarget, WrapperTypeInfo::Dependent, WrapperTypeInfo::WillBeGarbageCollectedObject };
#if defined(COMPONENT_BUILD) && defined(WIN32) && COMPILER(CLANG)
#pragma clang diagnostic pop
#endif

// This static member must be declared by DEFINE_WRAPPERTYPEINFO in DOMImplementation.h.
// For details, see the comment of DEFINE_WRAPPERTYPEINFO in
// bindings/core/v8/ScriptWrappable.h.
const WrapperTypeInfo& DOMImplementation::s_wrapperTypeInfo = V8DOMImplementation::wrapperTypeInfo;

namespace DOMImplementationV8Internal {

static void createDocumentTypeMethod(const v8::FunctionCallbackInfo<v8::Value>& info)
{
    ExceptionState exceptionState(ExceptionState::ExecutionContext, "createDocumentType", "DOMImplementation", info.Holder(), info.GetIsolate());
    if (UNLIKELY(info.Length() < 3)) {
        setMinimumArityTypeError(exceptionState, 3, info.Length());
        exceptionState.throwIfNeeded();
        return;
    }
    DOMImplementation* impl = V8DOMImplementation::toImpl(info.Holder());
    V8StringResource<> qualifiedName;
    V8StringResource<> publicId;
    V8StringResource<> systemId;
    {
        qualifiedName = info[0];
        if (!qualifiedName.prepare())
            return;
        publicId = info[1];
        if (!publicId.prepare())
            return;
        systemId = info[2];
        if (!systemId.prepare())
            return;
    }
    RefPtrWillBeRawPtr<DocumentType> result = impl->createDocumentType(qualifiedName, publicId, systemId, exceptionState);
    if (exceptionState.hadException()) {
        exceptionState.throwIfNeeded();
        return;
    }
    v8SetReturnValue(info, result.release());
}

static void createDocumentTypeMethodCallback(const v8::FunctionCallbackInfo<v8::Value>& info)
{
    TRACE_EVENT_SET_SAMPLING_STATE("blink", "DOMMethod");
    DOMImplementationV8Internal::createDocumentTypeMethod(info);
    TRACE_EVENT_SET_SAMPLING_STATE("v8", "V8Execution");
}

static void createDocumentMethod(const v8::FunctionCallbackInfo<v8::Value>& info)
{
    ExceptionState exceptionState(ExceptionState::ExecutionContext, "createDocument", "DOMImplementation", info.Holder(), info.GetIsolate());
    if (UNLIKELY(info.Length() < 2)) {
        setMinimumArityTypeError(exceptionState, 2, info.Length());
        exceptionState.throwIfNeeded();
        return;
    }
    DOMImplementation* impl = V8DOMImplementation::toImpl(info.Holder());
    V8StringResource<TreatNullAsNullString> namespaceURI;
    V8StringResource<TreatNullAsEmptyString> qualifiedName;
    DocumentType* doctype;
    {
        namespaceURI = info[0];
        if (!namespaceURI.prepare())
            return;
        qualifiedName = info[1];
        if (!qualifiedName.prepare())
            return;
        if (!info[2]->IsUndefined()) {
            doctype = V8DocumentType::toImplWithTypeCheck(info.GetIsolate(), info[2]);
            if (!doctype && !isUndefinedOrNull(info[2])) {
                exceptionState.throwTypeError("parameter 3 is not of type 'DocumentType'.");
                exceptionState.throwIfNeeded();
                return;
            }
        } else {
            doctype = nullptr;
        }
    }
    RefPtrWillBeRawPtr<XMLDocument> result = impl->createDocument(namespaceURI, qualifiedName, doctype, exceptionState);
    if (exceptionState.hadException()) {
        exceptionState.throwIfNeeded();
        return;
    }
    v8SetReturnValue(info, result.release());
}

static void createDocumentMethodCallback(const v8::FunctionCallbackInfo<v8::Value>& info)
{
    TRACE_EVENT_SET_SAMPLING_STATE("blink", "DOMMethod");
    DOMImplementationV8Internal::createDocumentMethod(info);
    TRACE_EVENT_SET_SAMPLING_STATE("v8", "V8Execution");
}

static void createHTMLDocumentMethod(const v8::FunctionCallbackInfo<v8::Value>& info)
{
    DOMImplementation* impl = V8DOMImplementation::toImpl(info.Holder());
    V8StringResource<> title;
    {
        if (!info[0]->IsUndefined()) {
            title = info[0];
            if (!title.prepare())
                return;
        } else {
            title = nullptr;
        }
    }
    v8SetReturnValue(info, impl->createHTMLDocument(title));
}

static void createHTMLDocumentMethodCallback(const v8::FunctionCallbackInfo<v8::Value>& info)
{
    TRACE_EVENT_SET_SAMPLING_STATE("blink", "DOMMethod");
    DOMImplementationV8Internal::createHTMLDocumentMethod(info);
    TRACE_EVENT_SET_SAMPLING_STATE("v8", "V8Execution");
}

static void hasFeatureMethod(const v8::FunctionCallbackInfo<v8::Value>& info)
{
    DOMImplementation* impl = V8DOMImplementation::toImpl(info.Holder());
    v8SetReturnValueBool(info, impl->hasFeature());
}

static void hasFeatureMethodCallback(const v8::FunctionCallbackInfo<v8::Value>& info)
{
    TRACE_EVENT_SET_SAMPLING_STATE("blink", "DOMMethod");
    DOMImplementationV8Internal::hasFeatureMethod(info);
    TRACE_EVENT_SET_SAMPLING_STATE("v8", "V8Execution");
}

} // namespace DOMImplementationV8Internal

void V8DOMImplementation::visitDOMWrapper(v8::Isolate* isolate, ScriptWrappable* scriptWrappable, const v8::Persistent<v8::Object>& wrapper)
{
    DOMImplementation* impl = scriptWrappable->toImpl<DOMImplementation>();
    // The document() method may return a reference or a pointer.
    if (Node* owner = WTF::getPtr(impl->document())) {
        Node* root = V8GCController::opaqueRootForGC(isolate, owner);
#if V8_MAJOR_VERSION >= 10
        v8::IsolateCompat::SetReferenceFromGroup(isolate, v8::UniqueId(reinterpret_cast<intptr_t>(root)), wrapper);
#else
        isolate->SetReferenceFromGroup(v8::UniqueId(reinterpret_cast<intptr_t>(root)), wrapper);
#endif
        return;
    }
}

static const V8DOMConfiguration::MethodConfiguration V8DOMImplementationMethods[] = {
    {"createDocumentType", DOMImplementationV8Internal::createDocumentTypeMethodCallback, 0, 3, V8DOMConfiguration::ExposedToAllScripts},
    {"createDocument", DOMImplementationV8Internal::createDocumentMethodCallback, 0, 2, V8DOMConfiguration::ExposedToAllScripts},
    {"createHTMLDocument", DOMImplementationV8Internal::createHTMLDocumentMethodCallback, 0, 0, V8DOMConfiguration::ExposedToAllScripts},
    {"hasFeature", DOMImplementationV8Internal::hasFeatureMethodCallback, 0, 0, V8DOMConfiguration::ExposedToAllScripts},
};

static void installV8DOMImplementationTemplate(v8::Local<v8::FunctionTemplate> functionTemplate, v8::Isolate* isolate)
{
    functionTemplate->ReadOnlyPrototype();

    v8::Local<v8::Signature> defaultSignature;
    defaultSignature = V8DOMConfiguration::installDOMClassTemplate(isolate, functionTemplate, "DOMImplementation", v8::Local<v8::FunctionTemplate>(), V8DOMImplementation::internalFieldCount,
        0, 0,
        0, 0,
        V8DOMImplementationMethods, WTF_ARRAY_LENGTH(V8DOMImplementationMethods));
    v8::Local<v8::ObjectTemplate> instanceTemplate = functionTemplate->InstanceTemplate();
    ALLOW_UNUSED_LOCAL(instanceTemplate);
    v8::Local<v8::ObjectTemplate> prototypeTemplate = functionTemplate->PrototypeTemplate();
    ALLOW_UNUSED_LOCAL(prototypeTemplate);

    // Custom toString template
#if V8_MAJOR_VERSION < 7
    functionTemplate->Set(v8AtomicString(isolate, "toString"), V8PerIsolateData::from(isolate)->toStringTemplate());
#endif
}

v8::Local<v8::FunctionTemplate> V8DOMImplementation::domTemplate(v8::Isolate* isolate)
{
    return V8DOMConfiguration::domClassTemplate(isolate, const_cast<WrapperTypeInfo*>(&wrapperTypeInfo), installV8DOMImplementationTemplate);
}

bool V8DOMImplementation::hasInstance(v8::Local<v8::Value> v8Value, v8::Isolate* isolate)
{
    return V8PerIsolateData::from(isolate)->hasInstance(&wrapperTypeInfo, v8Value);
}

v8::Local<v8::Object> V8DOMImplementation::findInstanceInPrototypeChain(v8::Local<v8::Value> v8Value, v8::Isolate* isolate)
{
    return V8PerIsolateData::from(isolate)->findInstanceInPrototypeChain(&wrapperTypeInfo, v8Value);
}

DOMImplementation* V8DOMImplementation::toImplWithTypeCheck(v8::Isolate* isolate, v8::Local<v8::Value> value)
{
    return hasInstance(value, isolate) ? toImpl(v8::Local<v8::Object>::Cast(value)) : 0;
}

void V8DOMImplementation::refObject(ScriptWrappable* scriptWrappable)
{
#if !ENABLE(OILPAN)
    scriptWrappable->toImpl<DOMImplementation>()->ref();
#endif
}

void V8DOMImplementation::derefObject(ScriptWrappable* scriptWrappable)
{
#if !ENABLE(OILPAN)
    scriptWrappable->toImpl<DOMImplementation>()->deref();
#endif
}

} // namespace blink
