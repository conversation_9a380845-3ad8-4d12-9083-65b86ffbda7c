<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.6.3 list-style-type</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
.one {list-style-type: disc;}
.two {list-style-type: circle;}
.three {list-style-type: square;}
.four {list-style-type: lower-roman;}
.five {list-style-type: upper-roman;}
.six {list-style-type: lower-alpha;}
.seven {list-style-type: upper-alpha;}
.eight {list-style-type: decimal;}
.nine {list-style-type: none;}
</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.one {list-style-type: disc;}
.two {list-style-type: circle;}
.three {list-style-type: square;}
.four {list-style-type: lower-roman;}
.five {list-style-type: upper-roman;}
.six {list-style-type: lower-alpha;}
.seven {list-style-type: upper-alpha;}
.eight {list-style-type: decimal;}
.nine {list-style-type: none;}

</PRE>
<HR>
<UL class="one">
<LI>This list...
<LI>...should feature...
<LI>...discs for each item.
</UL>
<UL class="two">
<LI>This list...
<LI>...should feature...
<LI>...circles for each item.
</UL>
<UL class="three">
<LI>This list...
<LI>...should feature...
<LI>...squares for each item.
</UL>
<OL class="four">
<LI>This list...
<LI>...should feature...
<LI>...lowercase Roman numerals for each item.
</OL>
<OL class="five">
<LI>This list...
<LI>...should feature...
<LI>...uppercase Roman numerals for each item.
</OL>
<OL class="six">
<LI>This list...
<LI>...should feature...
<LI>...lowercase letters for each item.
</OL>
<OL class="seven">
<LI>This list...
<LI>...should feature...
<LI>...uppercase letters for each item.
</OL>
<OL class="seven">
<LI>This list should feature...
<LI>...letters for each item...
<LI class="eight">...except this one.
</OL>
<UL class="nine">
<LI>This list...
<LI>...should feature...
<LI>...nothing for each item.
</UL>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><UL class="one">
<LI>This list...
<LI>...should feature...
<LI>...discs for each item.
</UL>
<UL class="two">
<LI>This list...
<LI>...should feature...
<LI>...circles for each item.
</UL>
<UL class="three">
<LI>This list...
<LI>...should feature...
<LI>...squares for each item.
</UL>
<OL class="four">
<LI>This list...
<LI>...should feature...
<LI>...lowercase Roman numerals for each item.
</OL>
<OL class="five">
<LI>This list...
<LI>...should feature...
<LI>...uppercase Roman numerals for each item.
</OL>
<OL class="six">
<LI>This list...
<LI>...should feature...
<LI>...lowercase letters for each item.
</OL>
<OL class="seven">
<LI>This list...
<LI>...should feature...
<LI>...uppercase letters for each item.
</OL>
<OL class="seven">
<LI>This list should feature...
<LI>...letters for each item...
<LI class="eight">...except this one.
</OL>
<UL class="nine">
<LI>This list...
<LI>...should feature...
<LI>...nothing for each item.
</UL>
</TD></TR></TABLE></BODY>
</HTML>
