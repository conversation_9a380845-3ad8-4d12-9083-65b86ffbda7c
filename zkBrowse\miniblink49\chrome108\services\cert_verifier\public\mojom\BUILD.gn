# Copyright 2020 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//mojo/public/tools/bindings/mojom.gni")
import("//net/features.gni")

mojom("mojom") {
  sources = [ "cert_verifier_service_factory.mojom" ]

  deps = [ "//mojo/public/mojom/base" ]

  public_deps = [ "//services/network/public/mojom" ]

  enabled_features = []

  if (trial_comparison_cert_verifier_supported) {
    enabled_features += [ "is_trial_comparison_cert_verifier_supported" ]
    sources += [ "trial_comparison_cert_verifier.mojom" ]
  }

  if (chrome_root_store_supported) {
    enabled_features += [ "is_chrome_root_store_supported" ]
  }
}
