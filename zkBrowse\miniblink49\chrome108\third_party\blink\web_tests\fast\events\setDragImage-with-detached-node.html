<!DOCTYPE html>
<script src="../../resources/js-test.js"></script>
<script>
var dragEndExpected = true;

window.onload = function()
{
    var dragger = document.getElementById('dragMe');
    dragger.addEventListener('dragstart', dragStart);
    dragger.addEventListener('drag', dragging);
    dragger.addEventListener('dragend', dragEnd);
    if (!window.testRunner)
        return;

    var x = dragger.offsetLeft + dragger.offsetWidth / 2;
    var y = dragger.offsetTop + dragger.offsetHeight / 2;

    eventSender.mouseMoveTo(x, y);
    eventSender.mouseDown();
    eventSender.leapForward(100);
    eventSender.mouseMoveTo(x + 100, y + 100);
    eventSender.mouseUp();
}

function dragStart(e)
{
    if (window.testRunner)
        dragEndExpected = false;
    debug('Started drag.');
    var emptyDiv = document.createElement('div');
    e.dataTransfer.setDragImage(emptyDiv, 0, 0);
}

function dragging()
{
    dragEndExpected = true;
}

function dragEnd()
{
    if (!dragEndExpected)
        testFailed('Not expecting drag end!');
    else
        testPassed('Ended drag.');
}
</script>
<style>
html, body {
    height: 100%;
    margin: 0;
}

#dragMe {
    border: 1px solid black;
}
</style>
<body>
<p>Test that calling setDragImage() with a detached node doesn't immediately abort the drag. This test can be manually run by dragging the 'Drag Me' div around and then ending the drag.
<div id="dragMe" draggable="true">Drag Me</div>
<div id="console"></div>
</body>
