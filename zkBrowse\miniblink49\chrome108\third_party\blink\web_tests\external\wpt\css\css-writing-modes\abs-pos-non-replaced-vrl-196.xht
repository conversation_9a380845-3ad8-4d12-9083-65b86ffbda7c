<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

 <head>

  <title>CSS Writing Modes Test: absolutely positioned non-replaced element - 'direction: rtl' and 'width' is 'auto' and 'left' and 'right' are not 'auto'</title>

  <link rel="author" title="Gérard Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" />
  <link rel="help" href="http://www.w3.org/TR/css-writing-modes-3/#vertical-layout" title="7.1 Principles of Layout in Vertical Writing Modes" />
  <link rel="help" href="http://www.w3.org/TR/CSS21/visudet.html#abs-non-replaced-height" title="10.6.4 Absolutely positioned, non-replaced elements" />
  <link rel="match" href="abs-pos-non-replaced-vlr-009-ref.xht" />

  <meta name="flags" content="ahem image" />
  <meta name="assert" content="When 'direction' is 'rtl' and 'width' is 'auto' and 'left' and 'right' are not 'auto' and 'writing-mode' is 'vertical-rl', then solve for 'width'." />

  <link rel="stylesheet" type="text/css" href="/fonts/ahem.css" />
  <style type="text/css"><![CDATA[
  div#containing-block
    {
      background: red url("support/bg-red-2col-2row-320x320.png");
      color: transparent;
      direction: rtl;
      font: 80px/1 Ahem;
      height: 320px;
      position: relative;
      width: 320px;
      writing-mode: vertical-rl;
    }

  div#containing-block > span
    {
      background-color: green;
      height: 1em;
      left: 1em;
      position: absolute;
      right: 2em;
      width: auto;
      writing-mode: horizontal-tb;
    }

/*
"
Layout calculation rules (such as those in CSS2.1, Section 10.3) that apply to the horizontal dimension in horizontal writing modes instead apply to the vertical dimension in vertical writing modes.
"
7.1 Principles of Layout in Vertical Writing Modes
http://www.w3.org/TR/css-writing-modes-3/#vertical-layout

So here, *right and *left properties are input into the §10.6.4 algorithms where *right properties refer to *top properties in the layout rules and where *left properties refer to *bottom properties in the layout rules.

"
5. 'height' is 'auto', 'top' and 'bottom' are not 'auto', then 'auto' values for 'margin-top' and 'margin-bottom' are set to 0 and solve for 'height'
"

'left' + 'margin-left' + 'border-left-width' + 'padding-left' + 'width' + 'padding-right' + 'border-right-width' + 'margin-right' + 'right' = width of containing block

So:

    160px : right
  +
      0px : margin-right
  +
      0px : border-right-width
  +
      0px : padding-right
  +
  (solve) : width: auto
  +
      0px : padding-left
  +
      0px : border-left-width
  +
      0px : margin-left
  +
     80px : left
    =====================
    320px : width of containing block

And so computed width value must be 80px .
*/

  ]]></style>

 </head>

 <body>

  <p><img src="support/pass-cdts-abs-pos-non-replaced.png" width="246" height="36" alt="Image download support must be enabled" /></p>

  <div id="containing-block">1 2 34<span></span></div>

 </body>
</html>