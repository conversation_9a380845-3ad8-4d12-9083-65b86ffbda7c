<!DOCTYPE html>
<title>IDBCursor direction - index</title>
<link rel="author" href="mailto:<EMAIL>" title="O<PERSON>">
<link rel=help href="http://dvcs.w3.org/hg/IndexedDB/raw-file/tip/Overview.html#cursor-iteration-operation">
<link rel=assert title='If direction is "next", let found record be the first record in records which satisfy all of the following requirements'>
<link rel=assert title="If position is defined, and source is an object store, the record's key is greater than position.">
<link rel=assert title='If direction is "prev", let found record be the last record in records which satisfy all of the following requirements'>
<link rel=assert title="If position is defined, and source is an object store, the record's key is less than position.">
<link rel=assert title="Set cursor's position to found record's key. If source is an index, set cursor's object store position to found record's value.">
<link rel=assert title="Set cursor's key to found record's key.">
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<script src="resources/support.js"></script>

<script>
var records = [ "Alice", "<PERSON>", "Bob", "Greg" ];
var cases = [
  {dir: 'next', expect: ['Alice:0', 'Bob:1', 'Bob:2', 'Greg:3']},
  {dir: 'prev', expect: ['Greg:3',  'Bob:2', 'Bob:1', 'Alice:0']},
  {dir: 'nextunique', expect: ['Alice:0', 'Bob:1', 'Greg:3']},
  {dir: 'prevunique', expect: ['Greg:3',  'Bob:1', 'Alice:0']},
];

cases.forEach(function(testcase) {
  var dir = testcase.dir;
  var expect = testcase.expect;
  indexeddb_test(
    function(t, db, tx) {
      var objStore = db.createObjectStore("test");
      objStore.createIndex("idx", "name");

      for (var i = 0; i < records.length; i++)
        objStore.add({ name: records[i] }, i);
    },
    function(t, db) {
      var count = 0;
      var rq = db.transaction("test").objectStore("test").index("idx").openCursor(undefined, dir);
      rq.onsuccess = t.step_func(function(e) {
        var cursor = e.target.result;
        if (!cursor) {
          assert_equals(count, expect.length, "cursor runs");
          t.done();
        }
        assert_equals(cursor.value.name + ":" + cursor.primaryKey, expect[count], "cursor.value");
        count++;
        cursor.continue();
      });
      rq.onerror = t.step_func(function(e) {
        e.preventDefault();
        e.stopPropagation();
        assert_unreached("rq.onerror - " + e.message);
      });
    },
    document.title + ' - ' + dir
  );
});
</script>
