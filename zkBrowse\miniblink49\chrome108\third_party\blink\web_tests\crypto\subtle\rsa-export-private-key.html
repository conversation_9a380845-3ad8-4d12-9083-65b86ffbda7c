<!DOCTYPE html>
<html>
<head>
<script src="../../resources/js-test.js"></script>
<script src="resources/common.js"></script>
</head>
<body>
<p id="description"></p>
<div id="console"></div>

<script>
description("Test exporting a private RSA key.");

jsTestIsAsync = true;

var extractable = true;
var nonExtractable = false;

// Example from JWK specification.
var privateKeyJSON = {
    "kty":"RSA",
    "n":"0vx7agoebGcQSuuPiLJXZptN9nndrQmbXEps2aiAFbWhM78LhWx4cbbfAAtVT86zwu1RK7aPFFxuhDR1L6tSoc_BJECPebWKRXjBZCiFV4n3oknjhMstn64tZ_2W-5JsGY4Hc5n9yBXArwl93lqt7_RN5w6Cf0h4QyQ5v-65YGjQR0_FDW2QvzqY368QQMicAtaSqzs8KJZgnYb9c7d0zgdAZHzu6qMQvRL5hajrn1n91CbOpbISD08qNLyrdkt-bFTWhAI4vMQFh6WeZu0fM4lFd2NcRwr3XPksINHaQ-G_xBniIqbw0Ls1jF44-csFCur-kEgU8awapJzKnqDKgw",
    "e":"AQAB",
    "d":"X4cTteJY_gn4FYPsXB8rdXix5vwsg1FLN5E3EaG6RJoVH-HLLKD9M7dx5oo7GURknchnrRweUkC7hT5fJLM0WbFAKNLWY2vv7B6NqXSzUvxT0_YSfqijwp3RTzlBaCxWp4doFk5N2o8Gy_nHNKroADIkJ46pRUohsXywbReAdYaMwFs9tv8d_cPVY3i07a3t8MN6TNwm0dSawm9v47UiCl3Sk5ZiG7xojPLu4sbg1U2jx4IBTNBznbJSzFHK66jT8bgkuqsk0GjskDJk19Z4qwjwbsnn4j2WBii3RL-Us2lGVkY8fkFzme1z0HbIkfz0Y6mqnOYtqc0X4jfcKoAC8Q",
    "p":"83i-7IvMGXoMXCskv73TKr8637FiO7Z27zv8oj6pbWUQyLPQBQxtPVnwD20R-60eTDmD2ujnMt5PoqMrm8RfmNhVWDtjjMmCMjOpSXicFHj7XOuVIYQyqVWlWEh6dN36GVZYk93N8Bc9vY41xy8B9RzzOGVQzXvNEvn7O0nVbfs",
    "q":"3dfOR9cuYq-0S-mkFLzgItgMEfFzB2q3hWehMuG0oCuqnb3vobLyumqjVZQO1dIrdwgTnCdpYzBcOfW5r370AFXjiWft_NGEiovonizhKpo9VVS78TzFgxkIdrecRezsZ-1kYd_s1qDbxtkDEgfAITAG9LUnADun4vIcb6yelxk",
    "dp":"G4sPXkc6Ya9y8oJW9_ILj4xuppu0lzi_H7VTkS8xj5SdX3coE0oimYwxIi2emTAue0UOa5dpgFGyBJ4c8tQ2VF402XRugKDTP8akYhFo5tAA77Qe_NmtuYZc3C3m3I24G2GvR5sSDxUyAN2zq8Lfn9EUms6rY3Ob8YeiKkTiBj0",
    "dq":"s9lAH9fggBsoFR8Oac2R_E2gw282rT2kGOAhvIllETE1efrA6huUUvMfBcMpn8lqeW6vzznYY5SSQF7pMdC_agI3nG8Ibp1BUb0JUiraRNqUfLhcQb_d9GF4Dh7e74WbRsobRonujTYN1xCaP6TO61jvWrX-L18txXw494Q_cgk",
    "qi":"GyM_p6JrXySiz1toFgKbWV-JdI3jQ4ypu9rbMWx3rQJBfmt0FoYzgUIZEVFEcOqwemRN81zoDAaa-Bk0KWNGDjJHZDdDmFhW3AN7lI-puxk_mHZGJ11rxyR8O55XLSe3SPmRfKwZI6yU24ZxvQKFYItdldUKGzO6Ia6zTKhAVRU",
    "alg":"RS256",
    "kid":"2011-04-29"
}

debug("\nImporting a JWK key...");
crypto.subtle.importKey("jwk", privateKeyJSON, {name: "RSASSA-PKCS1-v1_5", hash: {name: "sha-256"}}, extractable, ['sign']).then(function(result) {
    key = result;

    debug("\nExporting the key as JWK...");
    return crypto.subtle.exportKey("jwk", key);
}).then(function(result) {
    exportedJWK = result;

    shouldBe("exportedJWK.kty", "'RSA'");
    shouldBe("exportedJWK.n", "privateKeyJSON.n");
    shouldBe("exportedJWK.e", "privateKeyJSON.e");
    shouldBe("exportedJWK.d", "privateKeyJSON.d");
    shouldBe("exportedJWK.p", "privateKeyJSON.p");
    shouldBe("exportedJWK.q", "privateKeyJSON.q");
    shouldBe("exportedJWK.dp", "privateKeyJSON.dp");
    shouldBe("exportedJWK.dq", "privateKeyJSON.dq");
    shouldBe("exportedJWK.qi", "privateKeyJSON.qi");
    shouldBe("exportedJWK.oth", "privateKeyJSON.oth");
    shouldBe("exportedJWK.alg", "privateKeyJSON.alg");
    shouldBe("exportedJWK.ext", "true");
    shouldBe("exportedJWK.key_ops", "['sign']");
    shouldBe("exportedJWK.use", "undefined");
}).then(finishJSTest, failAndFinishJSTest);

</script>

</body>
</html>
