<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.2.6 font-size</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
.one {font-size: medium;}
.two {font-size: larger;}
.three {font-size: smaller;}
.four {font-size: xx-small;}
.five {font-size: x-small;}
.six {font-size: small;}
.seven {font-size: large;}
.eight {font-size: x-large;}
.nine {font-size: xx-large;}
.a {font-size: 0.5in;}
.b {font-size: 1cm;}
.c {font-size: 10mm;}
.d {font-size: 18pt;}
.e {font-size: 1.5pc;}
.f {font-size: 2em;}
.g {font-size: 3ex;}
.h {font-size: 25px;}
.i {font-size: 200%;}
.j {font-size: -0.5in;}
</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.one {font-size: medium;}
.two {font-size: larger;}
.three {font-size: smaller;}
.four {font-size: xx-small;}
.five {font-size: x-small;}
.six {font-size: small;}
.seven {font-size: large;}
.eight {font-size: x-large;}
.nine {font-size: xx-large;}
.a {font-size: 0.5in;}
.b {font-size: 1cm;}
.c {font-size: 10mm;}
.d {font-size: 18pt;}
.e {font-size: 1.5pc;}
.f {font-size: 2em;}
.g {font-size: 3ex;}
.h {font-size: 25px;}
.i {font-size: 200%;}
.j {font-size: -0.5in;}

</PRE>
<HR>
<P>
This paragraph element is unstyled, so the size of the font in this element is the default size for this user agent.
</P>
<P class="one">
This sentence has been set to <CODE>medium</CODE>, which may or may not be the same size as unstyled text.
</P>
<P class="two">
This sentence should be larger than unstyled text.
</P>
<P class="three">
This sentence should be smaller than unstyled text.
</P>
<P class="four">
This sentence should be very small, but the last word in the sentence should be <SPAN class="one">medium</SPAN>.
</P>
<P class="five">
This sentence should be rather small, but the last word in the sentence should be <SPAN class="one">medium</SPAN>.
</P>
<P class="six">
This sentence should be small, but the last word in the sentence should be <SPAN class="one">medium</SPAN>.
</P>
<P class="seven">
This sentence should be large, but the last word in the sentence should be <SPAN class="one">medium</SPAN>.
</P>
<P class="eight">
This sentence should be rather large, but the last word in the sentence should be <SPAN class="one">medium</SPAN>.
</P>
<P class="nine">
This sentence should be very large, but the last word in the sentence should be <SPAN class="one">medium</SPAN>.
</P>
<P class="a">
This sentence should be half an inch tall.
</P>
<P class="b">
This sentence should be one centimeter tall.
</P>
<P class="c">
This sentence should be ten millimeters tall.
</P>
<P class="d">
This sentence should be eighteen points tall.
</P>
<P class="e">
This sentence should be one and one half picas tall.
</P>
<P class="f">
This sentence should be two em tall.
</P>
<P class="g">
This sentence should be three ex tall.
</P>
<P class="h">
This sentence should be twenty-five pixels tall.
</P>
<P class="i">
This sentence should be twice normal size.
</P>
<P class="j">
This sentence should be normal size, since no negative values are allowed and therefore should be ignored.
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P>
This paragraph element is unstyled, so the size of the font in this element is the default size for this user agent.
</P>
<P class="one">
This sentence has been set to <CODE>medium</CODE>, which may or may not be the same size as unstyled text.
</P>
<P class="two">
This sentence should be larger than unstyled text.
</P>
<P class="three">
This sentence should be smaller than unstyled text.
</P>
<P class="four">
This sentence should be very small, but the last word in the sentence should be <SPAN class="one">medium</SPAN>.
</P>
<P class="five">
This sentence should be rather small, but the last word in the sentence should be <SPAN class="one">medium</SPAN>.
</P>
<P class="six">
This sentence should be small, but the last word in the sentence should be <SPAN class="one">medium</SPAN>.
</P>
<P class="seven">
This sentence should be large, but the last word in the sentence should be <SPAN class="one">medium</SPAN>.
</P>
<P class="eight">
This sentence should be rather large, but the last word in the sentence should be <SPAN class="one">medium</SPAN>.
</P>
<P class="nine">
This sentence should be very large, but the last word in the sentence should be <SPAN class="one">medium</SPAN>.
</P>
<P class="a">
This sentence should be half an inch tall.
</P>
<P class="b">
This sentence should be one centimeter tall.
</P>
<P class="c">
This sentence should be ten millimeters tall.
</P>
<P class="d">
This sentence should be eighteen points tall.
</P>
<P class="e">
This sentence should be one and one half picas tall.
</P>
<P class="f">
This sentence should be two em tall.
</P>
<P class="g">
This sentence should be three ex tall.
</P>
<P class="h">
This sentence should be twenty-five pixels tall.
</P>
<P class="i">
This sentence should be twice normal size.
</P>
<P class="j">
This sentence should be normal size, since no negative values are allowed and therefore should be ignored.
</P>
</TD></TR></TABLE></BODY>
</HTML>
