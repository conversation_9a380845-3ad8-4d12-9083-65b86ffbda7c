<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN"
"http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">

<!--

   Copyright 2001  The Apache Software Foundation 

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

-->
<!-- ========================================================================= -->
<!-- Tests various text on a path                                              -->
<!--                                                                           -->
<!-- <AUTHOR>                                      -->
<!-- @version $Id: textOnPath.svg,v 1.4 2004/08/18 07:12:22 vhardy Exp $        -->
<!-- ========================================================================= -->
<?xml-stylesheet type="text/css" href="../resources/test.css" ?>  

<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="body" width="450" height="500" viewBox="0 0 450 500">
<title>Text on a path test</title>

    <style type="text/css"><![CDATA[
        
    ]]></style>

    <g id="content">

        <text class="title" x="50%" y="40">Text on a path test</text>

        <defs>
	      <path id="Path2" style="fill:none; stroke:blue;" transform="scale(0.15,0.15)"
                  d="M 100 200 C 200 100 300 0 400 100 C 500 200 600 300 700 200 C 800 100 900 100 900 100"/>
            <path id="Path1" style="fill:none; stroke:blue;" transform="scale(0.30,0.50)"
                  d="M 100 100 C100 0 400 00 400 100"/>

        </defs>

	  <g transform="translate(0,70)">
            <use xlink:href="#Path1" style="fill:none; stroke:blue; stroke-width:2"/>
	      <text font-size="20" style="text-anchor:start">
                <textPath xlink:href="#Path1" startOffset="0%">Text <tspan fill="red" dy="-10">on</tspan><tspan dy="10"> a Path</tspan></textPath>
            </text>
            <text font-size="10" x="35" y="60">startOffset="0%"</text>
            <text font-size="10" x="35" y="70">text-anchor="start"</text>
         </g>


         <g transform="translate(150,70)">
            <use xlink:href="#Path1" style="fill:none; stroke:blue; stroke-width:2"/>
	      <text font-size="20" style="text-anchor:middle">
                <textPath xlink:href="#Path1" startOffset="0%">Text <tspan fill="red" dy="-10">on</tspan><tspan dy="10"> a Path</tspan></textPath>
            </text>
		<text font-size="10" x="35" y="60">startOffset="0%"</text>
            <text font-size="10" x="35" y="70">text-anchor="middle"</text>
         </g>

         <g transform="translate(300,70)">
            <use xlink:href="#Path1" style="fill:none; stroke:blue; stroke-width:2"/>
	      <text font-size="20" style="text-anchor:end">
                <textPath xlink:href="#Path1" startOffset="50%">Text <tspan fill="red" dy="-10">on</tspan><tspan dy="10"> a Path</tspan></textPath>
            </text>
		<text font-size="10" x="35" y="60">startOffset="50%"</text>
            <text font-size="10" x="35" y="70">text-anchor="end"</text>
         </g>


         <g transform="translate(0,170)">
            <use xlink:href="#Path1" style="fill:none; stroke:blue; stroke-width:2"/>
	      <text font-size="20" style="text-anchor:start">
                <textPath xlink:href="#Path1" startOffset="50%">Text <tspan fill="red" dy="-10">on</tspan><tspan dy="10"> a Path</tspan></textPath>
            </text>
            <text font-size="10" x="35" y="60">startOffset="50%"</text>
            <text font-size="10" x="35" y="70">text-anchor="start"</text>
         </g>


         <g transform="translate(150,170)">
            <use xlink:href="#Path1" style="fill:none; stroke:blue; stroke-width:2"/>
	      <text font-size="20" style="text-anchor:middle">
                <textPath xlink:href="#Path1" startOffset="50%">Text <tspan fill="red" dy="-10">on</tspan><tspan dy="10"> a Path</tspan></textPath>
            </text>
            <text font-size="10" x="35" y="60">startOffset="50%"</text>
            <text font-size="10" x="35" y="70">text-anchor="middle"</text>
         </g>

         <g transform="translate(300,170)">
            <use xlink:href="#Path1" style="fill:none; stroke:blue; stroke-width:2"/>
	      <text font-size="20" style="text-anchor:end">
                <textPath xlink:href="#Path1" startOffset="100%">Text <tspan fill="red" dy="-10">on</tspan><tspan dy="10"> a Path</tspan></textPath>
            </text>
            <text font-size="10" x="35" y="60">startOffset="100%"</text>
            <text font-size="10" x="35" y="70">text-anchor="end"</text>
         </g>


        <g transform="translate(0,270)">
            <use xlink:href="#Path1" style="fill:none; stroke:blue; stroke-width:2"/>
	      <text font-size="20" style="text-anchor:start">
                <textPath xlink:href="#Path1" startOffset="35">Text <tspan fill="red" dy="-10">on</tspan><tspan dy="10"> a Path</tspan></textPath>
            </text>
            <text font-size="10" x="35" y="60">startOffset="35"</text>
            <text font-size="10" x="35" y="70">text-anchor="start"</text>
         </g>


         <g transform="translate(150,270)">
            <use xlink:href="#Path1" style="fill:none; stroke:blue; stroke-width:2"/>
	      <text font-size="20" style="text-anchor:middle">
                <textPath xlink:href="#Path1" startOffset="35">Text <tspan fill="red" dy="-10">on</tspan><tspan dy="10"> a Path</tspan></textPath>
            </text>
            <text font-size="10" x="35" y="60">startOffset="35"</text>
            <text font-size="10" x="35" y="70">text-anchor="middle"</text>
         </g>

         <g transform="translate(300,270)">
            <use xlink:href="#Path1" style="fill:none; stroke:blue; stroke-width:2"/>
	      <text font-size="20" style="text-anchor:end">
                <textPath xlink:href="#Path1" startOffset="35">Text <tspan fill="red" dy="-10">on</tspan><tspan dy="10"> a Path</tspan></textPath>
            </text>
            <text font-size="10" x="35" y="60">startOffset="35"</text>
            <text font-size="10" x="35" y="70">text-anchor="end"</text>
         </g>



        <g transform="translate(10,370)">
            <use xlink:href="#Path2" style="fill:none; stroke:blue; stroke-width:2"/>
            <text font-size="15">
               <textPath xlink:href="#Path2"><tspan baseline-shift="super" fill="green">super</tspan> and <tspan baseline-shift="sub" fill="red">sub</tspan>scripts</textPath>
            </text>
            <text font-size="10" x="20" y="60">baseline-shift="super"</text>
            <text font-size="10" x="20" y="70">and baseline-shift="sub"</text>
        </g>


         <g transform="translate(155,370)">
            <use xlink:href="#Path2" style="fill:none; stroke:blue; stroke-width:2"/>
	      <text font-size="15">
                <textPath xlink:href="#Path2"><tspan baseline-shift="+20%" fill="green">positive</tspan> and <tspan baseline-shift="-20%" fill="red">negative </tspan></textPath>
            </text>
		<text font-size="10" x="20" y="60">baseline-shift="+/-20%"</text>
         </g>

        <g transform="translate(300,390)">
            <g transform="scale(0.5)">
            <use xlink:href="#Path1" style="fill:none; stroke:blue; stroke-width:2"/>
            <text font-size="30" x="30" y="-20">before path
               <textPath xlink:href="#Path1" fill="red" startOffset="10%">on path</textPath> after path
            </text>
            </g>
            <text font-size="10" x="15" y="40">text before/after textPath</text>
        </g>
       
    </g>

    <!-- ============================================================= -->
    <!-- Batik sample mark                                             -->
    <!-- ============================================================= -->
    <use xlink:href="../resources/batikLogo.svg#Batik_Tag_Box" />

</svg>

