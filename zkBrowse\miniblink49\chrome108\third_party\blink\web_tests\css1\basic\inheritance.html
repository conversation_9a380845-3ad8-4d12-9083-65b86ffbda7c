<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 1.3 Inheritance</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
BODY {
    color: green;
    overflow: hidden;
}
H3 {color: blue;}
EM {color: purple;}
.one {font-style: italic;}
.two {text-decoration: underline;}
#two {color: navy;}
.three {color: purple;}</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>BODY {color: green;}
H3 {color: blue;}
EM {color: purple;}
.one {font-style: italic;}
.two {text-decoration: underline;}
#two {color: navy;}
.three {color: purple;}
</PRE>
<HR>
<H3>This sentence should show <STRONG>blue</STRONG> and <EM>purple</EM>.</H3>
<H3>This sentence should be <SPAN class="one">blue</SPAN> throughout.</H3>
<P>
This should be green except for the <EM>emphasized words</EM>, which should be purple.
</P>
<H3 class="two">This should be blue and underlined.</H3>
<P class="two">
This sentence should be underlined, including <TT>this part</TT>, <I>this part</I>, <EM>this part</EM>, and <STRONG>this part</STRONG>.
</P>
<P class="two" ID="two">
This sentence should also be underlined, as well as dark blue (navy), <TT>including this part</TT>.
</P>
<P class="three">
This sentence should be purple, including <STRONG>this part</STRONG> and <SPAN style="text-decoration: underline;">this part (which is spanned)</SPAN>.
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><H3>This sentence should show <STRONG>blue</STRONG> and <EM>purple</EM>.</H3>
<H3>This sentence should be <SPAN class="one">blue</SPAN> throughout.</H3>
<P>
This should be green except for the <EM>emphasized words</EM>, which should be purple.
</P>
<H3 class="two">This should be blue and underlined.</H3>
<P class="two">
This sentence should be underlined, including <TT>this part</TT>, <I>this part</I>, <EM>this part</EM>, and <STRONG>this part</STRONG>.
</P>
<P class="two" ID="two">
This sentence should also be underlined, as well as dark blue (navy), <TT>including this part</TT>.
</P>
<P class="three">
This sentence should be purple, including <STRONG>this part</STRONG> and <SPAN style="text-decoration: underline;">this part (which is spanned)</SPAN>.
</P>
</TD></TR></TABLE></BODY>
</HTML>
