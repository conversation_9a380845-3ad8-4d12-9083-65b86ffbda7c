Tests that the Main Origin is assigned even if there is no matching Request.

Page origin: http://127.0.0.1:8000
Detected main origin: http://127.0.0.1:8000
Group: Main origin (non-secure)
<SPAN >
    <SPAN class=url-scheme-unknown >
http
    </SPAN>
    <SPAN class=url-scheme-separator >
://
    </SPAN>
    <SPAN >
127.0.0.1:8000
    </SPAN>
</SPAN>
<SPAN class=url-scheme-unknown >
http
</SPAN>
<SPAN class=url-scheme-separator >
://
</SPAN>
<SPAN >
127.0.0.1:8000
</SPAN>
Group: Unknown / canceled
<SPAN >
    <SPAN class=url-scheme-unknown >
https
    </SPAN>
    <SPAN class=url-scheme-separator >
://
    </SPAN>
    <SPAN >
foo.test
    </SPAN>
</SPAN>
<SPAN class=url-scheme-unknown >
https
</SPAN>
<SPAN class=url-scheme-separator >
://
</SPAN>
<SPAN >
foo.test
</SPAN>
<SPAN >
    <SPAN class=url-scheme-unknown >
https
    </SPAN>
    <SPAN class=url-scheme-separator >
://
    </SPAN>
    <SPAN >
bar.test
    </SPAN>
</SPAN>
<SPAN class=url-scheme-unknown >
https
</SPAN>
<SPAN class=url-scheme-separator >
://
</SPAN>
<SPAN >
bar.test
</SPAN>

