#include <iostream>
#include <string>

// Test C++17 features
int main() {
    // Test structured bindings (C++17 feature)
    auto pair = std::make_pair(42, "Hello");
    auto [number, text] = pair;
    
    std::cout << "C++17 Test Results:" << std::endl;
    std::cout << "Number: " << number << std::endl;
    std::cout << "Text: " << text << std::endl;
    
    // Test if constexpr (C++17 feature)
    constexpr int value = 10;
    if constexpr (value > 5) {
        std::cout << "Constexpr if works!" << std::endl;
    }
    
    std::cout << "C++17 features are working correctly!" << std::endl;
    return 0;
}
