<?xml version="1.0" encoding="iso-8859-1" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" [
    <!ATTLIST svg
    	xmlns:a3 CDATA #IMPLIED
    	a3:scriptImplementation CDATA #IMPLIED>
    <!ATTLIST script
        a3:scriptImplementation CDATA #IMPLIED>
]>
<?AdobeSVGViewer save="snapshot"?>
<svg width="100%" height="100%" viewBox="0 0 1024 768" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" onload="init(evt);">
	<script type="text/ecmascript" xlink:href="resources/helper_functions.js" />
	<script type="text/ecmascript" xlink:href="resources/timer.js" />
	<script type="text/ecmascript" xlink:href="resources/mapApp.js" />
	<script type="text/ecmascript" xlink:href="resources/slider.js" />
	<script type="text/ecmascript" xlink:href="resources/button.js" />
	<script type="text/ecmascript" xlink:href="resources/colourPicker.js" />
	<script type="text/ecmascript"><![CDATA[
		var myMapApp;
		var cpTransBgRect;

		function init(evt) {
			//initialize mapApp
			myMapApp = new mapApp(false,undefined);
			//first some colour picker styles
			var cpBgStyles = {"fill":"gainsboro"};
			var cpTextStyles = {"font-family":"Arial,Helvetica","font-size":12,"fill":"dimgray"};
			//button styles
			var buttonTextStyles = {"font-family":"Arial,Helvetica","fill":"dimgray","font-size":12};
			var buttonStyles = {"fill":"gainsboro"};
			var shadeLightStyles = {"fill":"white"};
			var shadeDarkStyles = {"fill":"dimgray"};
						//colourPicker(id,parentNode,x,y,width,height,bgstyles,textstyles,sliderSymbId,satSliderVisible,valSliderVisible,alphaSliderVisible,colValTextVisible,fillVisible,strokeVisible,startHue,endHue,nrStopVals,fillStartColor,strokeStartColor,functionToCall)
			myMapApp.colourPickers["myColourPicker1"] = new colourPicker("colourPicker1","colourPickers",200,400,300,120,cpBgStyles,cpTextStyles,"sliderSymbol",true,true,true,true,true,true,0,360,7,"0,255,0,1","0,0,0,0.7",undefined);
			myMapApp.colourPickers["myColourPicker2"] = new colourPicker("colourPicker2","colourPickerTransformed",500,200,300,90,cpBgStyles,cpTextStyles,"sliderSymbol",true,false,true,true,true,false,90,240,6,"0,255,255,0.5","0,0,0,1",changeBgColor);
			myMapApp.colourPickers["myColourPicker3"] = new colourPicker("colourPicker3","colourPickers",500,200,300,100,cpBgStyles,cpTextStyles,"sliderSymbol",true,true,false,true,true,true,0,180,7,"255,0,0,1","120,120,120,1",undefined);
			myMapApp.colourPickers["myColourPicker4"] = new colourPicker("colourPicker4","colourPickers",700,500,300,120,cpBgStyles,cpTextStyles,"sliderSymbol",true,true,true,true,false,true,0,360,7,"255,0,0,1","120,120,120,1",undefined);
			
			//create ok button
			myMapApp.buttons["okbutton"] = new button("okbutton","textbuttons",alertColorValues,"rect","OK",undefined,460,499,25,16,buttonTextStyles,buttonStyles,shadeLightStyles,shadeDarkStyles,1);
			//create hide/show buttons
			myMapApp.buttons["showHideButton"] = new button("showHideButton","buttons",showHideCP,"rect","Hide ColourPicker",undefined,510,442,110,20,buttonTextStyles,buttonStyles,shadeLightStyles,shadeDarkStyles,1);
			//create set random fill button
			myMapApp.buttons["fullbutton"] = new button("fullbutton","buttons",setRandomCol,"rect","Set Random Fill",undefined,510,470,110,20,buttonTextStyles,buttonStyles,shadeLightStyles,shadeDarkStyles,1);
			//create set random stroke button
			myMapApp.buttons["strokebutton"] = new button("strokebutton","buttons",setRandomCol,"rect","Set Random Stroke",undefined,510,498,110,20,buttonTextStyles,buttonStyles,shadeLightStyles,shadeDarkStyles,1);
			//create resize and moveTo buttons
			myMapApp.buttons["resizebutton"] = new button("resizebutton","buttons",resizeColourPicker,"rect","Randomly Resize Colour Picker",undefined,500,170,180,20,buttonTextStyles,buttonStyles,shadeLightStyles,shadeDarkStyles,1);
			myMapApp.buttons["movetobutton"] = new button("movetobutton","buttons",moveColourPicker,"rect","Randomly Move Colour Picker",undefined,685,170,180,20,buttonTextStyles,buttonStyles,shadeLightStyles,shadeDarkStyles,1);
			
			cpTransBgRect = document.getElementById("cpTransBgRect");
			
		}
		
		function alertColorValues() {
			values = myMapApp.colourPickers["myColourPicker1"].getValues();
			alert("Fill RGBA: "+values.fill.red+","+values.fill.green+","+values.fill.blue+","+values.fill.alpha+", Stroke RGBA: "+values.stroke.red+","+values.stroke.blue+","+values.stroke.green+","+values.stroke.alpha);
		}
		
		function changeBgColor(id,values) {
			cpTransBgRect.setAttributeNS(null,"fill-opacity",values.fill.alpha);
			cpTransBgRect.setAttributeNS(null,"fill","rgb("+values.fill.red+","+values.fill.green+","+values.fill.blue+")");
		}
		
		function showHideCP(id,evt,buttonText) {
			if (buttonText == "Hide ColourPicker") {
				myMapApp.colourPickers["myColourPicker1"].hide();
				myMapApp.buttons["okbutton"].hideButton();
				myMapApp.buttons["showHideButton"].setTextValue("Show ColourPicker");
			}
			else {
				myMapApp.colourPickers["myColourPicker1"].show();
				myMapApp.buttons["okbutton"].showButton();			
				myMapApp.buttons["showHideButton"].setTextValue("Hide ColourPicker");
			}
		}
		
		function resizeColourPicker(id,evt,buttonText) {
			var width = 300 + Math.round(Math.random()*60);
			var height = 100 + Math.round(Math.random()*30);
			myMapApp.colourPickers["myColourPicker3"].resize(width,height);
		}

		function moveColourPicker(id,evt,buttonText) {
			var x = 470 + Math.round(Math.random()*60);
			var y = 200 + Math.round(Math.random()*50);
			myMapApp.colourPickers["myColourPicker3"].moveTo(x,y);
		}
		
		function setRandomCol(id,evt,buttonText) {
			var colType = "fill";
			if (id == "strokebutton") {
				colType = "stroke";
			}
			var red = Math.round(Math.random()*255);
			var green = Math.round(Math.random()*255);
			var blue = Math.round(Math.random()*255);
			myMapApp.colourPickers["myColourPicker1"].setRGBAColour(colType,red,green,blue,1,false);
		}
		
]]></script>
	<defs>
		<!-- Symbol for Slider -->
		<symbol id="sliderSymbol" overflow="visible">
			<line x1="0" y1="-10" x2="0" y2="10" stroke="dimgray" stroke-width="5" pointer-events="none"/>
		</symbol>
	</defs>
	<rect x="-2000" y="-2000" width="6000" height="6000" fill="white" stroke="none" /> 
	<!-- group for colourPickers -->
	<g id="colourPickers" />
	<!-- group for transformed colour picker -->
	<g id="colourPickerTransformed" transform="translate(0,-400),rotate(45)">
		<rect id="cpTransBgRect" x="490" y="190" width="320" height="110" fill="rgb(0,255,255)" fill-opacity="0.5" />
		<text x="500" y="320" font-family="Arial,Helvetica">This colourPicker has a callBack function<tspan x="500" dy="1.2em">attached that changes the background color</tspan></text>
	</g>
	<g id="buttons" />
</svg>
