Tests doamin filter.


Domain: foo.bar.com
Subdomains: ["foo.bar.com","*.bar.com","*.com"]

Domain: thumbnails
Subdomains: ["thumbnails"]

Filter: bar.com
Domain 'foo.bar.com' matches: false
Domain 'bar.com' matches: true
Domain 'com' matches: false

Filter: *.bar.com
Domain 'foo.bar.com' matches: true
Domain 'bar.com' matches: false

Filter: *.bar.*
Domain 'foo.bar.com' matches: true
Domain 'baz.bar.org' matches: true
Domain 'bar.foo.net' matches: false

