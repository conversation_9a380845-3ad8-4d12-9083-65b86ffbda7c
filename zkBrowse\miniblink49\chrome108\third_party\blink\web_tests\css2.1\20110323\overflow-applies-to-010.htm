<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
    <head>
        <title>CSS Test: overflow applied to elements with 'display' set to 'list-item'</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/">
        <link rel="help" href="http://www.w3.org/TR/CSS21/visufx.html#propdef-overflow">
        <link rel="help" href="http://www.w3.org/TR/CSS21/visufx.html#overflow">
        <meta name="flags" content="ahem">
        <script src="../../resources/ahem.js"></script>
        <meta name="assert" content="The 'overflow' property applies to elements with 'display' set to 'list-item'.">
        <style type="text/css">
            body
            {
                margin-left: 50px;
            }
            div
            {
                border: 5px solid transparent;
                display: list-item;
                font: 20px/1em Ahem;
                height: 5em;
                overflow: hidden;
                width: 5em;
            }
            #span2
            {
                color: red;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is a black box below and there is no red visible on the page. To the left of the box can be list marker but is it not required.</p>
        <div><span>XXXXX</span><span id="span2">XXXXX</span></div>
    </body>
</html>