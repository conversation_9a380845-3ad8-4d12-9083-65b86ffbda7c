<html>
<head>
<script src="../../resources/js-test.js"></script>

<script>

description("This test checks that Page Visibility state values are correct when a document has no defaultView.");

var subdocument = document.implementation.createDocument('http://www.w3.org/1999/xhtml', 'html', null);

shouldBeTrue("subdocument.defaultView == null");
shouldBeEqualToString("subdocument.visibilityState", "hidden");
shouldBeTrue("subdocument.hidden");

</script>


</head>
</html>
