<script src="../../resources/js-test.js"></script>
<script>
if (window.testRunner)
    testRunner.dumpAsText();

function test()
{
    if (!window.testRunner)
        return;
    var elem_movetome = document.getElementById('MoveToMe'); 
    var elem_focusme = document.getElementById('focusMe');
    elem_focusme.focus();

    eventSender.keyDown("\t"); 
    shouldBeEqualToString('document.activeElement.id', 'MoveToMe');
    elem_focusme.focus();
    eventSender.keyDown("\t", ["shiftKey"]);
    shouldBeEqualToString('document.activeElement.id', 'MoveToMe');
}
</script>
<body onload="test()">
<input id="MoveToMe" tabindex="1">
<input tabindex="-1">
<input id="focusMe" tabindex="-1">
<input tabindex="-1">
</body>


