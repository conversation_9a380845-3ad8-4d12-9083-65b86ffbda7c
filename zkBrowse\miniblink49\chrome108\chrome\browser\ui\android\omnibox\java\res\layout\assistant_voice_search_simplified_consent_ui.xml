<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright 2021 The Chromium Authors
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file.
-->
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/avs_consent_ui"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center_horizontal"
    android:paddingHorizontal="20dp"
    android:paddingVertical="10dp"
    android:importantForAccessibility="no">

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:src="@drawable/ic_logo_assistant_24dp"
        android:importantForAccessibility="no"/>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:layout_marginHorizontal="30dp"
        android:textAlignment="center"
        style="@style/TextAppearance.Headline.Primary"
        android:text="@string/avs_consent_ui_simplified_title" />

    <View
        style="@style/HorizontalDivider"
        android:layout_marginTop="20dp"
        android:layout_marginHorizontal="30dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="14dp"
        style="@style/TextAppearance.TextSmall.Secondary"
        android:text="@string/avs_consent_ui_simplified_body" />

    <TextView
        android:id="@+id/avs_consent_ui_learn_more"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        style="@style/TextAppearance.TextSmall.Link"
        android:text="@string/learn_more" />

    <org.chromium.components.browser_ui.widget.DualControlLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        app:buttonAlignment="apart"
        app:primaryButtonText="@string/avs_consent_ui_simplified_accept"
        app:secondaryButtonText="@string/avs_consent_ui_simplified_deny"
        app:stackedMargin="@dimen/button_bar_stacked_margin" />
</LinearLayout>
