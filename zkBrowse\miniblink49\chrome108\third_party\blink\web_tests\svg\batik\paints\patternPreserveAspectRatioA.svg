<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN"
"http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">

<!--

   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

-->
<!-- ========================================================================= -->
<!--                                                                           -->
<!-- <AUTHOR>                                                -->
<!-- @version $Id: patternPreserveAspectRatioA.svg 475477 2006-11-15 22:44:28Z cam $  -->
<!-- ========================================================================= -->

<?xml-stylesheet type="text/css" href="../resources/test.css" ?>

<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="body" width="450" height="500" viewBox="0 0 450 500">
<title>Pattern Region Test, B</title>

    <text x="225" y="30" class="title">
        patterns, preserveAspectRatio
    </text>

    <!-- ============================================================= -->
    <!-- Test content                                                  -->
    <!-- ============================================================= -->
    <g id="testContent" >
        <defs>
        
        <g id="patternColumn">
            <circle cx=".5" cy=".5" r=".5" />
            <rect x="0" y="1" width="1" height="1" />
            <path d="M 0 3 l 1 0 l -0.5 -1 z" />
        </g>

        <!-- Pattern content is 24 by 24 -->
        <g id="patternContent" transform="scale(8, 8)" >
            <rect x="0" y="0" width="3" height="3" fill="black" />
            <use xlink:href="#patternColumn" fill="crimson" />
            <use xlink:href="#patternColumn" x="1" fill="gold" />
            <use xlink:href="#patternColumn" x="2" fill="orange" />
        </g>

        <rect id="testShape" transform="translate(45, 5)" width="90" height="30" />

        <!-- ============================================== -->
        <!-- For all the following patterns:                -->
        <!--                                                -->
        <!-- patternUnits : userSpaceOnUse                  -->
        <!-- patternContentUnits : default (userSpaceOnUse) -->
        <!-- patternTransform: default (identity)           -->
        <!-- preserveAspectRatio: variable for each test    -->
        <!-- overflow : default (hidden)                    -->
        <!--                                                -->
        <!-- ============================================== -->

        <!-- ============ -->
        <!-- meetXxxxYyyy -->
        <!-- ============ -->

        <pattern id="meetXminYmin"
                 patternUnits="userSpaceOnUse"
                 x="0" y="0" 
                 width="32" height="16" 
                 viewBox="0 0 24 24"
                 preserveAspectRatio="xMinYMin meet">
            <use xlink:href="#patternContent" />
        </pattern>

        <pattern id="meetXminYmid"
                 patternUnits="userSpaceOnUse"
                 x="0" y="0" 
                 width="32" height="16" 
                 viewBox="0 0 24 24"
                 preserveAspectRatio="xMinYMid meet">
            <use xlink:href="#patternContent" />
        </pattern>

        <pattern id="meetXminYmax"
                 patternUnits="userSpaceOnUse"
                 x="0" y="0" 
                 width="32" height="16" 
                 viewBox="0 0 24 24"
                 preserveAspectRatio="xMinYMax meet">
            <use xlink:href="#patternContent" />
        </pattern>

        <pattern id="meetXmidYmin"
                 patternUnits="userSpaceOnUse"
                 x="0" y="0" 
                 width="32" height="16" 
                 viewBox="0 0 24 24"
                 preserveAspectRatio="xMidYMin meet">
            <use xlink:href="#patternContent" />
        </pattern>

        <pattern id="meetXmidYmid"
                 patternUnits="userSpaceOnUse"
                 x="0" y="0" 
                 width="32" height="16" 
                 viewBox="0 0 24 24"
                 preserveAspectRatio="xMidYMid meet">
            <use xlink:href="#patternContent" />
        </pattern>

        <pattern id="meetXmidYmax"
                 patternUnits="userSpaceOnUse"
                 x="0" y="0" 
                 width="32" height="16" 
                 viewBox="0 0 24 24"
                 preserveAspectRatio="xMidYMax meet">
            <use xlink:href="#patternContent" />
        </pattern>

        <pattern id="meetXmaxYmin"
                 patternUnits="userSpaceOnUse"
                 x="0" y="0" 
                 width="32" height="16" 
                 viewBox="0 0 24 24"
                 preserveAspectRatio="xMaxYMin meet">
            <use xlink:href="#patternContent" />
        </pattern>

        <pattern id="meetXmaxYmid"
                 patternUnits="userSpaceOnUse"
                 x="0" y="0" 
                 width="32" height="16" 
                 viewBox="0 0 24 24"
                 preserveAspectRatio="xMaxYMid meet">
            <use xlink:href="#patternContent" />
        </pattern>

        <pattern id="meetXmaxYmax"
                 patternUnits="userSpaceOnUse"
                 x="0" y="0" 
                 width="32" height="16" 
                 viewBox="0 0 24 24"
                 preserveAspectRatio="xMaxYMax meet">
            <use xlink:href="#patternContent" />
        </pattern>


        <!-- ============ -->
        <!-- sliceXxxxYyyy -->
        <!-- ============ -->

        <pattern id="sliceXminYmin"
                 patternUnits="userSpaceOnUse"
                 x="0" y="0" 
                 width="32" height="16" 
                 viewBox="0 0 24 24"
                 preserveAspectRatio="xMinYMin slice">
            <use xlink:href="#patternContent" />
        </pattern>

        <pattern id="sliceXminYmid"
                 patternUnits="userSpaceOnUse"
                 x="0" y="0" 
                 width="32" height="16" 
                 viewBox="0 0 24 24"
                 preserveAspectRatio="xMinYMid slice">
            <use xlink:href="#patternContent" />
        </pattern>

        <pattern id="sliceXminYmax"
                 patternUnits="userSpaceOnUse"
                 x="0" y="0" 
                 width="32" height="16" 
                 viewBox="0 0 24 24"
                 preserveAspectRatio="xMinYMax slice">
            <use xlink:href="#patternContent" />
        </pattern>

        <pattern id="sliceXmidYmin"
                 patternUnits="userSpaceOnUse"
                 x="0" y="0" 
                 width="32" height="16" 
                 viewBox="0 0 24 24"
                 preserveAspectRatio="xMidYMin slice">
            <use xlink:href="#patternContent" />
        </pattern>

        <pattern id="sliceXmidYmid"
                 patternUnits="userSpaceOnUse"
                 x="0" y="0" 
                 width="32" height="16" 
                 viewBox="0 0 24 24"
                 preserveAspectRatio="xMidYMid slice">
            <use xlink:href="#patternContent" />
        </pattern>

        <pattern id="sliceXmidYmax"
                 patternUnits="userSpaceOnUse"
                 x="0" y="0" 
                 width="32" height="16" 
                 viewBox="0 0 24 24"
                 preserveAspectRatio="xMidYMax slice">
            <use xlink:href="#patternContent" />
        </pattern>

        <pattern id="sliceXmaxYmin"
                 patternUnits="userSpaceOnUse"
                 x="0" y="0" 
                 width="32" height="16" 
                 viewBox="0 0 24 24"
                 preserveAspectRatio="xMaxYMin slice">
            <use xlink:href="#patternContent" />
        </pattern>

        <pattern id="sliceXmaxYmid"
                 patternUnits="userSpaceOnUse"
                 x="0" y="0" 
                 width="32" height="16" 
                 viewBox="0 0 24 24"
                 preserveAspectRatio="xMaxYMid slice">
            <use xlink:href="#patternContent" />
        </pattern>

        <pattern id="sliceXmaxYmax"
                 patternUnits="userSpaceOnUse"
                 x="0" y="0" 
                 width="32" height="16" 
                 viewBox="0 0 24 24"
                 preserveAspectRatio="xMaxYMax slice">
            <use xlink:href="#patternContent" />
        </pattern>

        <!-- ============ -->
        <!-- noneXxxxYyyy -->
        <!-- ============ -->

        <pattern id="noneXminYmin"
                 patternUnits="userSpaceOnUse"
                 x="0" y="0" 
                 width="32" height="16" 
                 viewBox="0 0 24 24"
                 preserveAspectRatio="xMinYMin">
            <use xlink:href="#patternContent" />
        </pattern>

        <pattern id="noneXminYmid"
                 patternUnits="userSpaceOnUse"
                 x="0" y="0" 
                 width="32" height="16" 
                 viewBox="0 0 24 24"
                 preserveAspectRatio="xMinYMid">
            <use xlink:href="#patternContent" />
        </pattern>

        <pattern id="noneXminYmax"
                 patternUnits="userSpaceOnUse"
                 x="0" y="0" 
                 width="32" height="16" 
                 viewBox="0 0 24 24"
                 preserveAspectRatio="xMinYMax">
            <use xlink:href="#patternContent" />
        </pattern>

        <pattern id="noneXmidYmin"
                 patternUnits="userSpaceOnUse"
                 x="0" y="0" 
                 width="32" height="16" 
                 viewBox="0 0 24 24"
                 preserveAspectRatio="xMidYMin">
            <use xlink:href="#patternContent" />
        </pattern>

        <pattern id="noneXmidYmid"
                 patternUnits="userSpaceOnUse"
                 x="0" y="0" 
                 width="32" height="16" 
                 viewBox="0 0 24 24"
                 preserveAspectRatio="xMidYMid">
            <use xlink:href="#patternContent" />
        </pattern>

        <pattern id="noneXmidYmax"
                 patternUnits="userSpaceOnUse"
                 x="0" y="0" 
                 width="32" height="16" 
                 viewBox="0 0 24 24"
                 preserveAspectRatio="xMidYMax">
            <use xlink:href="#patternContent" />
        </pattern>

        <pattern id="noneXmaxYmin"
                 patternUnits="userSpaceOnUse"
                 x="0" y="0" 
                 width="32" height="16" 
                 viewBox="0 0 24 24"
                 preserveAspectRatio="xMaxYMin">
            <use xlink:href="#patternContent" />
        </pattern>

        <pattern id="noneXmaxYmid"
                 patternUnits="userSpaceOnUse"
                 x="0" y="0" 
                 width="32" height="16" 
                 viewBox="0 0 24 24"
                 preserveAspectRatio="xMaxYMid">
            <use xlink:href="#patternContent" />
        </pattern>

        <pattern id="noneXmaxYmax"
                 patternUnits="userSpaceOnUse"
                 x="0" y="0" 
                 width="32" height="16" 
                 viewBox="0 0 24 24"
                 preserveAspectRatio="xMaxYMax">
            <use xlink:href="#patternContent" />
        </pattern>

        <!-- ==================== -->
        <!-- align = none         -->
        <!-- ==================== -->
        <pattern id="meetNone"
                 patternUnits="userSpaceOnUse"
                 x="0" y="0" 
                 width="32" height="16" 
                 viewBox="0 0 24 24"
                 preserveAspectRatio="none meet">
            <use xlink:href="#patternContent" />
        </pattern>

        <pattern id="sliceNone"
                 patternUnits="userSpaceOnUse"
                 x="0" y="0" 
                 width="32" height="16" 
                 viewBox="0 0 24 24"
                 preserveAspectRatio="none slice">
            <use xlink:href="#patternContent" />
        </pattern>

        <pattern id="noneNone"
                 patternUnits="userSpaceOnUse"
                 x="0" y="0" 
                 width="32" height="16" 
                 viewBox="0 0 24 24"
                 preserveAspectRatio="none">
            <use xlink:href="#patternContent" />
        </pattern>

            <rect id="xMxx" x="0" y="0" width="20" height="120" />
            <rect id="yMxx" x="20" y="0" width="20" height="40" />
            <rect id="sampleCell" x="40" y="0" width="100" height="40" />

            <g id="xMxxBlock" stroke="black">
                <use xlink:href="#xMxx" />
                <use xlink:href="#yMxx" fill="rgb(255, 250, 210)"/>
                <text class="label" stroke="none" fill="black" 
                      text-anchor="middle" x="35" y="20" 
                      transform="rotate(-90, 35, 20)">
                yMin
                </text>
                <use xlink:href="#yMxx" y="40" fill="rgb(255, 240, 150)"/>
                <text class="label" stroke="none" fill="black" 
                      text-anchor="middle" x="35" y="60" 
                      transform="rotate(-90, 35, 60)">
                yMid
                </text>
                <use xlink:href="#yMxx" y="80" fill="rgb(255, 235, 120)"/>
                <text class="label" stroke="none" fill="black" 
                      text-anchor="middle" x="35" y="100" 
                      transform="rotate(-90, 35, 100)">
                yMax
                </text>
            </g>

            <g id="sampleColumn" stroke="black" fill="none">
                <use xlink:href="#sampleCell" fill="rgb(255, 250, 210)"/>
                <use xlink:href="#sampleCell" y="40" fill="rgb(255, 240, 150)"/>
                <use xlink:href="#sampleCell" y="80" fill="rgb(255, 235, 120)"/>
                <use xlink:href="#sampleCell" y="120" fill="rgb(255, 250, 210)"/>
                <use xlink:href="#sampleCell" y="160" fill="rgb(255, 240, 150)"/>
                <use xlink:href="#sampleCell" y="200" fill="rgb(255, 235, 120)"/>
                <use xlink:href="#sampleCell" y="240" fill="rgb(255, 250, 210)"/>
                <use xlink:href="#sampleCell" y="280" fill="rgb(255, 240, 150)"/>
                <use xlink:href="#sampleCell" y="320" fill="rgb(255, 235, 120)"/>
                <use xlink:href="#sampleCell" y="360"/>
            </g>

            <rect id="columnHeader" x="40" y="-20" width="100" height="20" stroke="black"/>

        </defs>

        <g transform="translate(50, 80)">
            <use xlink:href="#xMxxBlock" fill="rgb(255, 250, 210)"/>
            <text class="label" stroke="none" fill="black" 
                      text-anchor="middle" x="15" y="60" 
                      transform="rotate(-90, 15, 60)">
            xMin
            </text>

            <use xlink:href="#xMxxBlock" y="120" fill="rgb(255, 240, 150)"/>
            <text class="label" stroke="none" fill="black" 
                      text-anchor="middle" x="15" y="180" 
                      transform="rotate(-90, 15, 180)">
            xMid
            </text>

            <use xlink:href="#xMxxBlock" y="240" fill="rgb(255, 235, 120)"/>
            <text class="label" stroke="none" fill="black" 
                      text-anchor="middle" x="15" y="300" 
                      transform="rotate(-90, 15, 300)">
            xMax
            </text>

            <use xlink:href="#sampleColumn" />
            <use xlink:href="#sampleColumn" x="100" />
            <use xlink:href="#sampleColumn" x="200" />

            <use xlink:href="#columnHeader" fill="none"/>
            <use xlink:href="#columnHeader" x="100" fill="none"/>
            <use xlink:href="#columnHeader" x="200" fill="none"/>

            <text class="label" stroke="none" fill="black" 
                      text-anchor="middle" x="90" y="-5">
            meet
            </text>

            <text class="label" stroke="none" fill="black" 
                      text-anchor="middle" x="190" y="-5">
            slice
            </text>

            <text class="label" stroke="none" fill="black" 
                      text-anchor="middle" x="290" y="-5">
            none
            </text>

            <rect x="0" y="360" width="40" height="40" stroke="black" fill="none" />
            <text class="label" stroke="none" fill="black" 
                      text-anchor="middle" x="25" y="380" 
                      transform="rotate(-90, 25, 380)">
            none
            </text>

            <!-- ============================================ -->
            <!-- Various pattern settings here                -->
            <!-- ============================================ -->
            
            <!-- =============================== -->
            <!-- meetOrSlice = meet              -->
            <!-- =============================== -->
            <g>

                <!-- xMxx = xMin -->
                <use xlink:href="#testShape" fill="url(#meetXminYmin)" />
                <use xlink:href="#testShape" fill="url(#meetXminYmid)" y="40"/>
                <use xlink:href="#testShape" fill="url(#meetXminYmax)" y="80"/>

                <!-- xMxx = xMid -->
                <g transform="translate(0, 120)">
                    <use xlink:href="#testShape" fill="url(#meetXmidYmin)" />
                    <use xlink:href="#testShape" fill="url(#meetXmidYmid)" y="40"/>
                    <use xlink:href="#testShape" fill="url(#meetXmidYmax)" y="80"/>
                </g>

                <!-- xMxx = xMax -->
                <g transform="translate(0, 240)">
                    <use xlink:href="#testShape" fill="url(#meetXmaxYmin)" />
                    <use xlink:href="#testShape" fill="url(#meetXmaxYmid)" y="40"/>
                    <use xlink:href="#testShape" fill="url(#meetXmaxYmax)" y="80"/>
                </g>

            </g>
           
            <!-- =============================== -->
            <!-- meetOrSlice = slice                    -->
            <!-- =============================== -->
            <g marker-start="url(#startEndMarker)" transform="translate(100, 0)"
                marker-end="url(#startEndMarker)" stroke="black">

                <!-- xMxx = xMin -->
                <use xlink:href="#testShape" fill="url(#sliceXminYmin)" />
                <use xlink:href="#testShape" fill="url(#sliceXminYmid)" y="40"/>
                <use xlink:href="#testShape" fill="url(#sliceXminYmax)" y="80"/>

                <!-- xMxx = xMid -->
                <g transform="translate(0, 120)">
                    <use xlink:href="#testShape" fill="url(#sliceXmidYmin)" />
                    <use xlink:href="#testShape" fill="url(#sliceXmidYmid)" y="40"/>
                    <use xlink:href="#testShape" fill="url(#sliceXmidYmax)" y="80"/>
                </g>

                <!-- xMxx = xMax -->
                <g transform="translate(0, 240)">
                    <use xlink:href="#testShape" fill="url(#sliceXmaxYmin)" />
                    <use xlink:href="#testShape" fill="url(#sliceXmaxYmid)" y="40"/>
                    <use xlink:href="#testShape" fill="url(#sliceXmaxYmax)" y="80"/>
                </g>

            </g>
           
            <!-- =============================== -->
            <!-- meetOrSlice = none              -->
            <!-- =============================== -->
            <g marker-start="url(#startEndMarker)" transform="translate(200, 0)"
                marker-end="url(#startEndMarker)" stroke="black">

                <!-- xMxx = xMin -->
                <use xlink:href="#testShape" fill="url(#noneXminYmin)" />
                <use xlink:href="#testShape" fill="url(#noneXminYmid)" y="40"/>
                <use xlink:href="#testShape" fill="url(#noneXminYmax)" y="80"/>

                <!-- xMxx = xMid -->
                <g transform="translate(0, 120)">
                    <use xlink:href="#testShape" fill="url(#noneXmidYmin)" />
                    <use xlink:href="#testShape" fill="url(#noneXmidYmid)" y="40"/>
                    <use xlink:href="#testShape" fill="url(#noneXmidYmax)" y="80"/>
                </g>

                <!-- xMxx = xMax -->
                <g transform="translate(0, 240)">
                    <use xlink:href="#testShape" fill="url(#noneXmaxYmin)" />
                    <use xlink:href="#testShape" fill="url(#noneXmaxYmid)" y="40"/>
                    <use xlink:href="#testShape" fill="url(#noneXmaxYmax)" y="80"/>
                </g>

            </g>
           
            <!-- =============================== -->
            <!-- align = none                    -->
            <!-- =============================== -->
            <g marker-start="url(#startEndMarker)" transform="translate(0, 360)"
                marker-end="url(#startEndMarker)" stroke="black">

                <use xlink:href="#testShape" fill="url(#meetNone)" />
                <use xlink:href="#testShape" fill="url(#sliceNone)" x="100"/> 
                <use xlink:href="#testShape" fill="url(#noneNone)" x="200"/> 

            </g>
        </g>
    </g>


    <!-- ============================================================= -->
    <!-- Batik sample mark                                             -->
    <!-- ============================================================= -->
    <use xlink:href="../resources/batikLogo.svg#Batik_Tag_Box" />
    
</svg>
