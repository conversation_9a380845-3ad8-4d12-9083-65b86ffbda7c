<!DOCTYPE html>
<html>
<head>
<script src="../../resources/js-test.js"></script>
<script src="resources/common.js"></script>
</head>
<body>
<p id="description"></p>
<div id="console"></div>

<script>
description("Tests that the reflected Key.usages are normalized.");

jsTestIsAsync = true;

var aesCbc = {name: 'aes-cbc'};
var aesKeyBytes = new Uint8Array(16);
var extractable = true;

// The usages includes several duplicates.
crypto.subtle.importKey('raw', aesKeyBytes, aesCbc, extractable, ['decrypt','decrypt','encrypt','wrapKey','encrypt', 'encrypt']).then(function(result) {
    key = result;

    shouldEvaluateAs("key.usages.join(',')", "encrypt,decrypt,wrapKey");
}).then(finishJSTest, failAndFinishJSTest);

</script>

</body>
</html>
