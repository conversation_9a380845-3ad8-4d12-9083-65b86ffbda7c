# Copyright 2022 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/android/rules.gni")

source_set("public") {
  deps = [ "//base" ]

  sources = [
    "touch_to_fill_credit_card_view.h",
    "touch_to_fill_credit_card_view_controller.h",
  ]
}

source_set("android") {
  sources = [
    "touch_to_fill_credit_card_view_impl.cc",
    "touch_to_fill_credit_card_view_impl.h",
  ]

  public_deps = [ "//base" ]

  deps = [
    ":jni_headers",
    ":public",
    "//components/autofill/core/common:features",
    "//content/public/browser:browser",
    "//ui/android",
  ]
}

generate_jni("jni_headers") {
  sources = [
    "internal/java/src/org/chromium/chrome/browser/touch_to_fill/payments/TouchToFillCreditCardControllerBridge.java",
    "internal/java/src/org/chromium/chrome/browser/touch_to_fill/payments/TouchToFillCreditCardViewBridge.java",
  ]
}

android_library("public_java") {
  deps = [
    "//base:jni_java",
    "//chrome/android:chrome_java",
    "//components/browser_ui/bottomsheet/android:java",
    "//third_party/androidx:androidx_annotation_annotation_java",
    "//ui/android:ui_java",
  ]

  sources = [ "java/src/org/chromium/chrome/browser/touch_to_fill/payments/TouchToFillCreditCardComponent.java" ]
}
