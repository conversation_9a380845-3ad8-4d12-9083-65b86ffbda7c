This test ensures selectstart is fired when selection is created by arrow keys.

If running this test manually, click on div ("Hello World") element and try to select the text using arrow keys.
Expected result : SelectStart event will fire when user starts extending selection.

Hello World
Initial state: PASS
Check (Right arrow): PASS
Check (Right arrow + Shift): PASS
Check (Right arrow + Shift + Control): PASS
Check (End + Shift): PASS
Check (Home + Shift): PASS
Check (End + Shift): PASS
Check (Left arrow): PASS
Check (LeftArrow + Shift + Control): PASS
Check (Left arrow + Shift): PASS
Check (Home + Shift): PASS
Check (Home + Control): PASS
Done.
