<html>
<head>
    <script>
    function log(s)
    {
        document.getElementById('console').appendChild(document.createTextNode(s + "\n"));
    }

    function changeHandler()
    {
        log ('PASS: change event fired.\n');
    }

    function blurHandler()
    {
        log ('blur event fired.\n');
    }

    function test()
    {
        if (window.testRunner)
            testRunner.dumpAsText();
        
        // Test for select that's not inside a form element
        var menu1 = document.getElementById('menu1');
        menu1.focus();

        if (window.testRunner) {
            // change the option selection
            eventSender.keyDown('e');
        }
        // hit enter
        var enterEvent = document.createEvent("KeyboardEvents");
        enterEvent.initKeyboardEvent("keypress", true, false, window, "Enter", 0, false, false, false, false, false);
        menu1.dispatchEvent(enterEvent);
        
        // Test for select is inside a form element
        var menu2 = document.getElementById('menu2');
        menu2.focus();

        if (window.testRunner) {
            // change the option selection
            eventSender.keyDown('e');
        }
        // hit enter
        menu2.dispatchEvent(enterEvent);
    }
    </script>
</head>
<body onload="test()">
    <p>This test verifies that the ENTER key fires the change event for popups.</p>
    <hr>
    <select id="menu1" onchange="changeHandler()" onblur="blurHandler()"><option>abcd</option><option>efgh</option></select>
    <form onsubmit="return false;">
        <select id="menu2" onchange="changeHandler()" onblur="blurHandler()"><option>abcd</option><option>efgh</option></select>
    </form>
    <pre id="console"></pre>
</body>
</html>
