<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.4.5 text-transform</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
.ttn {text-transform: none;}
.cap {text-transform: capitalize;}
.upp {text-transform: uppercase;}
.low {text-transform: lowercase;}
</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.ttn {text-transform: none;}
.cap {text-transform: capitalize;}
.upp {text-transform: uppercase;}
.low {text-transform: lowercase;}

</PRE>
<HR>
<P CLASS=ttn>This page tests the 'text-transform' property of CSS1.
This paragraph has no text transformation and should appear normal.
</P>
<P CLASS=cap>This paragraph is capitalized and the first letter in each word should therefore appear in uppercase. Words that are in uppercase in the source (e.g. USA) should remain so. There should be a capital letter after a non-breaking&nbsp;space (&amp;nbsp;). Both those characters appear in the previous sentence.
</P>
<P>Words with inline elements inside them should only capitalize the first letter of the word. Therefore, the last word in this sentence should have one, and only one, capital <SPAN CLASS=cap>le<SPAN>tt</SPAN>er</SPAN>. 
</P>
<P CLASS=upp>This paragraph is uppercased and small characters in the source (e.g. a and &aring;) should therefore appear in uppercase.  In the last sentence, however, <SPAN class="ttn">the last eight words should not be uppercase</SPAN>.
</P>
<P CLASS=low>This paragraph is lowercased and capital characters in the source (e.g. A and &Aring;) should therefore appear in lowercase.
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P CLASS=ttn>This page tests the 'text-transform' property of CSS1.
This paragraph has no text transformation and should appear normal.
</P>
<P CLASS=cap>This paragraph is capitalized and the first letter in each word should therefore appear in uppercase. Words that are in uppercase in the source (e.g. USA) should remain so. There should be a capital letter after a non-breaking&nbsp;space (&amp;nbsp;). Both those characters appear in the previous sentence.
</P>
<P>Words with inline elements inside them should only capitalize the first letter of the word. Therefore, the last word in this sentence should have one, and only one, capital <SPAN CLASS=cap>le<SPAN>tt</SPAN>er</SPAN>. 
</P>
<P CLASS=upp>This paragraph is uppercased and small characters in the source (e.g. a and &aring;) should therefore appear in uppercase.  In the last sentence, however, <SPAN class="ttn">the last eight words should not be uppercase</SPAN>.
</P>
<P CLASS=low>This paragraph is lowercased and capital characters in the source (e.g. A and &Aring;) should therefore appear in lowercase.
</P>
</TD></TR></TABLE></BODY>
</HTML>
