<html>
<head>
    <script>
    if (window.testRunner) {
        testRunner.dumpAsText();
        testRunner.waitUntilDone();
    }

    function load() {
        if (!window.internals)
            return;

        internals.setPageScaleFactor(2);

        setTimeout(increasePageScale, 0);
    }

    function increasePageScale() {
        internals.setPageScaleFactor(4);

        if (window.testRunner)
            testRunner.notifyDone();
    }

    window.onload = load;
    </script>
</head>
<img style="width: 32px;" src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMjgiIGhlaWdodD0iMTI4Ij4KPGNpcmNsZSBmaWxsPSJibGFjayIgY3g9IjY0IiBjeT0iNjQiIHI9IjY0IiAvPgo8L3N2Zz4K">
<img style="width: 128px;" src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgoJPGNpcmNsZSBmaWxsPSJibGFjayIgY3g9IjY0IiBjeT0iNjQiIHI9IjY0IiAvPgo8L3N2Zz4=">
<p>This test passes if both of the circles have sharp edges.</p>
<p>To run manually, use full-page zoom to increase the page scale factor.</p>
</html>
