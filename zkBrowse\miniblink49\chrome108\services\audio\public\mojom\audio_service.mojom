// Copyright 2019 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

module audio.mojom;

import "media/mojo/mojom/audio_stream_factory.mojom";
import "sandbox/policy/mojom/sandbox.mojom";
import "services/audio/public/mojom/debug_recording.mojom";
import "services/audio/public/mojom/device_notifications.mojom";
import "services/audio/public/mojom/log_factory_manager.mojom";
import "services/audio/public/mojom/system_info.mojom";
import "services/audio/public/mojom/testing_api.mojom";

// The main interface to the Audio service. This is a privileged interface and
// must only be bound by trusted processes, e.g. a browser process. Note that
// the sandbox can be disabled (to kNoSandbox) by policy.
[ServiceSandbox=sandbox.mojom.Sandbox.kAudio]
interface AudioService {
  // Binds a SystemInfo interface receiver.
  BindSystemInfo(pending_receiver<SystemInfo> receiver);

  // Binds a DebugRecording interface receiver.
  BindDebugRecording(pending_receiver<DebugRecording> receiver);

  // Binds a StreamFactory interface receiver.
  BindStreamFactory(pending_receiver<media.mojom.AudioStreamFactory> receiver);

  // Binds a DeviceNotifier interface receiver.
  BindDeviceNotifier(pending_receiver<DeviceNotifier> receiver);

  // Binds a LogFactoryManager interface receiver.
  BindLogFactoryManager(pending_receiver<LogFactoryManager> receiver);

  // Binds a TestingApi interface receiver. Only callable in some test
  // environments.
  BindTestingApi(pending_receiver<TestingApi> receiver);
};
