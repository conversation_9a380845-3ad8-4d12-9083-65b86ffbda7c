<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN"
"http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">

<!--

   Copyright 2003  The Apache Software Foundation 

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

-->
<?xml-stylesheet type="text/css" href="../resources/test.css" ?>  

<svg xmlns="http://www.w3.org/2000/svg" 
     xmlns:xlink="http://www.w3.org/1999/xlink"
     id="body" width="450" height="500" viewBox="0 0 450 500">

   <title>Text on Path with for text with an 'x'</title>
   <text class="title" x="50%" y="40"
    >Text on Path with for text with an 'x'</text>

   <defs>
      <path id="path" style="fill:none; stroke:blue;" 
            d="M100 0 c0 -125 140 -125 140 0"/>
   </defs>

   <g transform="translate(40, 185)" >
      <rect fill="black" stroke="black" x="-10" y="-135" 
	    width="390" height="20"/>
      <rect fill="white" stroke="black" x="-10" y="-115" 
	    width="390" height="120"/>
      <text x="-10" y="-120" fill="white" font-size="14"
       >Text with one textPath child and x="10"</text>

      <use xlink:href="#path" />
      <text x="10" y="0" fill="black"><textPath 
          fill="crimson" xlink:href="#path" startOffset="50%"
          text-anchor="middle">Text on a path for text w/ 'x'.</textPath
	></text>
  </g>

   <g transform="translate(40, 330)" >
      <rect fill="black" stroke="black" x="-10" y="-135" 
	    width="390" height="20"/>
      <rect fill="white" stroke="black" x="-10" y="-115" 
	    width="390" height="120"/>
      <text x="-10" y="-120" fill="white" font-size="14"
       >Text with one textPath child and x="100"</text>

      <use xlink:href="#path" />
      <text x="100" y="0" fill="black"><textPath 
          fill="crimson" xlink:href="#path" startOffset="50%"
          text-anchor="middle">Text on a path for text w/ 'x'.</textPath
	></text>
  </g>

   <g transform="translate(40, 475)" >
      <rect fill="black" stroke="black" x="-10" y="-135" 
	    width="390" height="20"/>
      <rect fill="white" stroke="black" x="-10" y="-115" 
	    width="390" height="120"/>
      <text x="-10" y="-120" fill="white" font-size="14"
       >Text with one textPath child and x="-100" y="10"</text>

      <use xlink:href="#path" />
      <text x="-100" y="10" fill="black"><textPath 
          fill="crimson" xlink:href="#path" startOffset="50%"
          text-anchor="middle">Text on a <tspan x="50"
          >path for text w/ 'x'.</tspan></textPath
	></text>
  </g>
</svg>
