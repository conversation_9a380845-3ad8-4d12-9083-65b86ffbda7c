<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 4.4 The Height of Lines</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
P.one {font-size: 14px; line-height: 20px;}
IMG.onea {vertical-align: text-bottom;
     width: 200px; height: 200px;}
IMG.oneb {vertical-align: text-top; width: 200px; height: 200px;}

P.two {font-size: 14px; line-height: 20px;}
IMG.twoa {vertical-align: text-bottom; width: 100px; height: 100px;
     padding: 5px; border-style: solid;
     border-width: 10px; margin: 15px;}
IMG.twob {vertical-align: text-top;
          width: 100px; height: 100px;
     padding: 5px; border-style: solid;
     border-width: 10px; margin: 15px;}

IMG.twoc {vertical-align: middle; width: 50px; height: 50px;
     padding: 5px; border-style: solid;
     border-width: 10px; margin: -10px;}
</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>P.one {font-size: 14px; line-height: 20px;}
IMG.onea {vertical-align: text-bottom;
     width: 200px; height: 200px;}
IMG.oneb {vertical-align: text-top; width: 200px; height: 200px;}

P.two {font-size: 14px; line-height: 20px;}
IMG.twoa {vertical-align: text-bottom; width: 100px; height: 100px;
     padding: 5px; border-style: solid;
     border-width: 10px; margin: 15px;}
IMG.twob {vertical-align: text-top;
          width: 100px; height: 100px;
     padding: 5px; border-style: solid;
     border-width: 10px; margin: 15px;}

IMG.twoc {vertical-align: middle; width: 50px; height: 50px;
     padding: 5px; border-style: solid;
     border-width: 10px; margin: -10px;}

</PRE>
<HR>
<P CLASS="one">
This paragraph should have a font size of 14px and a
line height of 20px.  This means that the lines of text within it
should be separated by six pixels, three of which are part of the
line-box of each line.  Any images within the paragraph should increase
the height of the line-box so that they fit within the line box, such
as <IMG SRC="../resources/oransqr.gif" ALT="[Image]" CLASS="onea"> and <IMG
SRC="../resources/oransqr.gif" ALT="[Image]" CLASS="oneb">.  This is additional text
to make sure that there is enough room left below the image so that
this line does not hit the image that is text-top aligned.
</P>

<P CLASS="two">
This paragraph should have a font size of 14px and a
line height of 20px.  This means that the lines of text within it
should be separated by six pixels.  Any images within the paragraph
should increase the height of the line-box so that they fit, including
their padding (5px), border (10px) and margins (15px) within the
line box, such as <IMG SRC="../resources/oransqr.gif" ALT="[Image]"
CLASS="twoa"> and <IMG SRC="../resources/oransqr.gif" ALT="[Image]"
CLASS="twob">.  This is additional text to make sure that there is
enough room left below the image so that this line does not hit the
image that is text-top aligned.  It is the outer edge of the margin that should be text-bottom and text-top aligned in this paragraph, so for the first image the bottom border of the image should begin 15px above the bottom of the text, and for the second image the top border should begin 15px below the top of the text <IMG SRC="../resources/oransqr.gif" ALT="[Image]" CLASS="twoc">.  The last image in this paragraph has -10px margins set on it, so that should pull the text in toward the image in the vertical direction, and also in the horizontal direction.
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P CLASS="one">
This paragraph should have a font size of 14px and a
line height of 20px.  This means that the lines of text within it
should be separated by six pixels, three of which are part of the
line-box of each line.  Any images within the paragraph should increase
the height of the line-box so that they fit within the line box, such
as <IMG SRC="../resources/oransqr.gif" ALT="[Image]" CLASS="onea"> and <IMG
SRC="../resources/oransqr.gif" ALT="[Image]" CLASS="oneb">.  This is additional text
to make sure that there is enough room left below the image so that
this line does not hit the image that is text-top aligned.
</P>

<P CLASS="two">
This paragraph should have a font size of 14px and a
line height of 20px.  This means that the lines of text within it
should be separated by six pixels.  Any images within the paragraph
should increase the height of the line-box so that they fit, including
their padding (5px), border (10px) and margins (15px) within the
line box, such as <IMG SRC="../resources/oransqr.gif" ALT="[Image]"
CLASS="twoa"> and <IMG SRC="../resources/oransqr.gif" ALT="[Image]"
CLASS="twob">.  This is additional text to make sure that there is
enough room left below the image so that this line does not hit the
image that is text-top aligned.  It is the outer edge of the margin that should be text-bottom and text-top aligned in this paragraph, so for the first image the bottom border of the image should begin 15px above the bottom of the text, and for the second image the top border should begin 15px below the top of the text <IMG SRC="../resources/oransqr.gif" ALT="[Image]" CLASS="twoc">.  The last image in this paragraph has -10px margins set on it, so that should pull the text in toward the image in the vertical direction, and also in the horizontal direction.
</P>
</TD></TR></TABLE></BODY>
</HTML>
