<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 2.1 anchor</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
A {color: green;}
A:link {color: purple;}
A:visited {color: lime;}
A:active {color: maroon;}
#one {color: #006600;}
</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>A {color: green;}
A:link {color: purple;}
A:visited {color: lime;}
A:active {color: maroon;}
#one {color: #006600;}

</PRE>
<HR>
<P>
The following anchors should appear as described; none of them should be red.
</P>

<UL>
<LI>Purple unvisited, lime (light green) visited, maroon (dark red) while active (being clicked):
<UL>
<LI><A HREF="http://www.w3.org/">W3C Web server</A>
<LI><A HREF="http://www.nist.gov/">NIST Web server</A>
<LI><A HREF="http://www.cwru.edu/">CWRU Web server</A>
<LI><A HREF="http://www.yahoo.com/">Yahoo!</A>
<LI><A HREF="none.html">Erewhon</A> (don't click on it, it goes nowhere)
</UL>
<LI>Dark green in any circumstance:
<UL>
<LI><A HREF="http://www.cwru.edu/" ID="one">CWRU Web server</A>
</UL>
</UL>
<P>
The quoted word "<A NAME="test">anchor</A>" should be green, NOT purple, since it's part of an anchor.  It's a named anchor, and styles declared for the A tag are applied to them under CSS1.  It also should NOT turn orange when clicked upon.
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P>
The following anchors should appear as described; none of them should be red.
</P>

<UL>
<LI>Purple unvisited, lime (light green) visited, maroon (dark red) while active (being clicked):
<UL>
<LI><A HREF="http://www.w3.org/">W3C Web server</A>
<LI><A HREF="http://www.nist.gov/">NIST Web server</A>
<LI><A HREF="http://www.cwru.edu/">CWRU Web server</A>
<LI><A HREF="http://www.yahoo.com/">Yahoo!</A>
<LI><A HREF="none.html">Erewhon</A> (don't click on it, it goes nowhere)
</UL>
<LI>Dark green in any circumstance:
<UL>
<LI><A HREF="http://www.cwru.edu/" ID="one">CWRU Web server</A>
</UL>
</UL>
<P>
The quoted word "<A NAME="test">anchor</A>" should be green, NOT purple, since it's part of an anchor.  It's a named anchor, and styles declared for the A tag are applied to them under CSS1.  It also should NOT turn orange when clicked upon.
</P>
</TD></TR></TABLE></BODY>
</HTML>
