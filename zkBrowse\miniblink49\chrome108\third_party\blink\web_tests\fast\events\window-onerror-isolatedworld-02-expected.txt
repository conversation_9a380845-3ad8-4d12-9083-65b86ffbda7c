CONSOLE ERROR: Uncaught Error: <PERSON>rror in main world inline script.
CONSOLE ERROR: Uncaught Error: <PERSON>rror in main world load handler.
Test that window.onerror and "error" event listeners from isolated world are invoked for uncaught exceptions in scripts running in isolate worlds as well as for exceptions in the main world.Bug 8519.

isolated world window.onerror: Uncaught Error: Error in isolated world inline script. at window-onerror-isolatedworld-02.html, Line: 41, Column: 13
Error object present!
isolated world error event listener: Uncaught Error: Error in isolated world inline script. at window-onerror-isolatedworld-02.html:, Line: 41
Error object present!
isolated world window.onerror: Uncaught Error: Error in isolated world load handler. at window-onerror-isolatedworld-02.html, Line: 38, Column: 17
Error object present!
isolated world error event listener: Uncaught Error: Error in isolated world load handler. at window-onerror-isolatedworld-02.html:, Line: 38
Error object present!
isolated world window.onerror: Uncaught Error: Error in isolated world setTimeout callback. at window-onerror-isolatedworld-02.html, Line: 36, Column: 21
Error object present!
