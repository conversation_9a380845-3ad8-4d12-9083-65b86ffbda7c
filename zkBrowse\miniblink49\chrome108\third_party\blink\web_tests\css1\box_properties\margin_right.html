<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.5.02 margin-right</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
body {overflow: hidden;}
.zero {background-color: silver; margin-right: 0; text-align: right;}
.one {margin-right: 0.5in; text-align: right; background-color: aqua;}
.two {margin-right: 25px; text-align: right; background-color: aqua;}
.three {margin-right: 5em; text-align: right; background-color: aqua;}
.four {margin-right: 25%; text-align: right; background-color: aqua;}
.five {margin-right: -10px; background-color: aqua;}</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.zero {background-color: silver; margin-right: 0; text-align: right;}
.one {margin-right: 0.5in; text-align: right; background-color: aqua;}
.two {margin-right: 25px; text-align: right; background-color: aqua;}
.three {margin-right: 5em; text-align: right; background-color: aqua;}
.four {margin-right: 25%; text-align: right; background-color: aqua;}
.five {margin-right: -10px; background-color: aqua;}
</PRE>
<HR>
<P class="zero">
This element has a class of zero.
</P>
<P class="one">
This sentence should have a right margin of half an inch.
</P>
<P class="two">
This sentence should have a right margin of 25 pixels.
</P>
<P class="three">
This sentence should have a right margin of 5 em.
</P>
<P class="four">
This sentence should have a right margin of 25%, which is calculated with respect to the width of the parent element.
</P>
<UL class="two" style="background-color: gray;">
<LI>The right margin on this unordered list has been set to 25 pixels, and the background color has been set to gray.</LI>
<LI class="two" style="background-color: white;">Another list item might not be such a bad idea, either, considering that such things do need to be double-checked.  This list item has its right margin also set to 25 pixels, which should combine with the list's margin to make 50 pixels of margin, and its background-color has been set to white.
<LI>This is an unclassed list item
</UL>
<P class="zero">
This element has a class of zero.
</P>
<P class="five">
This paragraph has a right margin of -10px, which should cause it to be wider than it might otherwise be, and it has a light blue background.  In all other respects, however, the element should be normal.  No styles have been applied to it besides the negative right margin and the background color.
</P>

<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P class="zero">
This element has a class of zero.
</P>
<P class="one">
This sentence should have a right margin of half an inch.
</P>
<P class="two">
This sentence should have a right margin of 25 pixels.
</P>
<P class="three">
This sentence should have a right margin of 5 em.
</P>
<P class="four">
This sentence should have a right margin of 25%, which is calculated with respect to the width of the parent element.
</P>
<UL class="two" style="background-color: gray;">
<LI>The right margin on this unordered list has been set to 25 pixels, and the background color has been set to gray.</LI>
<LI class="two" style="background-color: white;">Another list item might not be such a bad idea, either, considering that such things do need to be double-checked.  This list item has its right margin also set to 25 pixels, which should combine with the list's margin to make 50 pixels of margin, and its background-color has been set to white.
<LI>This is an unclassed list item
</UL>
<P class="zero">
This element has a class of zero.
</P>
<P class="five">
This paragraph has a right margin of -10px, which should cause it to be wider than it might otherwise be, and it has a light blue background.  In all other respects, however, the element should be normal.  No styles have been applied to it besides the negative right margin and the background color.
</P></TD></TR></TABLE></BODY>
</HTML>
