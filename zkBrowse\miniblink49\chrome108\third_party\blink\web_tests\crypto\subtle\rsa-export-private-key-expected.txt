Test exporting a private RSA key.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".


Importing a JWK key...

Exporting the key as JWK...
PASS exportedJWK.kty is 'RSA'
PASS exportedJWK.n is privateKeyJSON.n
PASS exportedJWK.e is privateKeyJSON.e
PASS exportedJWK.d is privateKeyJSON.d
PASS exportedJWK.p is privateKeyJSON.p
PASS exportedJWK.q is privateKeyJSON.q
PASS exportedJWK.dp is privateKeyJSON.dp
PASS exportedJWK.dq is privateKeyJSON.dq
PASS exportedJWK.qi is privateKeyJSON.qi
PASS exportedJWK.oth is privateKeyJSON.oth
PASS exportedJWK.alg is privateKeyJSON.alg
PASS exportedJWK.ext is true
PASS exportedJWK.key_ops is ['sign']
PASS exportedJWK.use is undefined
PASS successfullyParsed is true

TEST COMPLETE

