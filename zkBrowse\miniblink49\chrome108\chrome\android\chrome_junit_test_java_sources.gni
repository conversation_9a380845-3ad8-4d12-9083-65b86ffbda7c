chrome_junit_test_java_sources = [
  "java/src/org/chromium/chrome/browser/segmentation_platform/ContextualPageActionControllerTest.java",
  "java/src/org/chromium/chrome/browser/segmentation_platform/PriceTrackingActionProviderTest.java",
  "java/src/org/chromium/chrome/browser/segmentation_platform/ReaderModeActionProviderTest.java",
  "java/src/org/chromium/chrome/browser/segmentation_platform/SignalAccumulatorTest.java",
  "java/src/org/chromium/chrome/browser/tab/TabFaviconTest.java",
  "junit/src/org/chromium/chrome/browser/AppIndexingUtilTest.java",
  "junit/src/org/chromium/chrome/browser/BackPressHelperUnitTest.java",
  "junit/src/org/chromium/chrome/browser/ChromeActionModeHandlerUnitTest.java",
  "junit/src/org/chromium/chrome/browser/ChromeActivityUnitTest.java",
  "junit/src/org/chromium/chrome/browser/ChromeBackgroundServiceTest.java",
  "junit/src/org/chromium/chrome/browser/ChromeBackupAgentTest.java",
  "junit/src/org/chromium/chrome/browser/ChromeLocalizationUtilsTest.java",
  "junit/src/org/chromium/chrome/browser/DeferredStartupHandlerTest.java",
  "junit/src/org/chromium/chrome/browser/DelayedScreenLockIntentHandlerTest.java",
  "junit/src/org/chromium/chrome/browser/PowerBroadcastReceiverTest.java",
  "junit/src/org/chromium/chrome/browser/ShadowIdleHandlerAwareMessageQueue.java",
  "junit/src/org/chromium/chrome/browser/ShortcutHelperTest.java",
  "junit/src/org/chromium/chrome/browser/UndoRefocusHelperTest.java",
  "junit/src/org/chromium/chrome/browser/about_settings/AboutSettingsBridgeTest.java",
  "junit/src/org/chromium/chrome/browser/app/appmenu/AppMenuPropertiesDelegateUnitTest.java",
  "junit/src/org/chromium/chrome/browser/app/download/home/<USER>",
  "junit/src/org/chromium/chrome/browser/app/metrics/LaunchCauseMetricsTest.java",
  "junit/src/org/chromium/chrome/browser/app/metrics/TabbedActivityLaunchCauseMetricsUnitTest.java",
  "junit/src/org/chromium/chrome/browser/app/tab_activity_glue/ActivityTabWebContentsDelegateAndroidUnitTest.java",
  "junit/src/org/chromium/chrome/browser/app/tab_activity_glue/TabReparentingControllerTest.java",
  "junit/src/org/chromium/chrome/browser/app/tabmodel/TabPersistentStoreIntegrationTest.java",
  "junit/src/org/chromium/chrome/browser/app/tabmodel/TabbedModeTabModelOrchestratorUnitTest.java",
  "junit/src/org/chromium/chrome/browser/app/video_tutorials/NewTabPageVideoIPHManagerTest.java",
  "junit/src/org/chromium/chrome/browser/autofill/AutofillSuggestionTest.java",
  "junit/src/org/chromium/chrome/browser/autofill/AutofillUiUtilsTest.java",
  "junit/src/org/chromium/chrome/browser/autofill/settings/AutofillVirtualCardEnrollmentDialogTest.java",
  "junit/src/org/chromium/chrome/browser/autofill/settings/AutofillVirtualCardUnenrollmentDialogTest.java",
  "junit/src/org/chromium/chrome/browser/background_sync/BackgroundSyncBackgroundTaskSchedulerTest.java",
  "junit/src/org/chromium/chrome/browser/background_sync/BackgroundSyncBackgroundTaskTest.java",
  "junit/src/org/chromium/chrome/browser/background_sync/BackgroundSyncGooglePlayServicesCheckerTest.java",
  "junit/src/org/chromium/chrome/browser/background_sync/PeriodicBackgroundSyncChromeWakeUpTaskTest.java",
  "junit/src/org/chromium/chrome/browser/background_task_scheduler/NativeBackgroundTaskTest.java",
  "junit/src/org/chromium/chrome/browser/base/DexFixerTest.java",
  "junit/src/org/chromium/chrome/browser/base/SplitPreloaderTest.java",
  "junit/src/org/chromium/chrome/browser/bookmarks/BookmarkSaveFlowMediatorTest.java",
  "junit/src/org/chromium/chrome/browser/bookmarks/ReadingListSectionHeaderTest.java",
  "junit/src/org/chromium/chrome/browser/browserservices/ClearDataDialogResultRecorderTest.java",
  "junit/src/org/chromium/chrome/browser/browserservices/InstalledWebappBroadcastReceiverTest.java",
  "junit/src/org/chromium/chrome/browser/browserservices/InstalledWebappDataRecorderTest.java",
  "junit/src/org/chromium/chrome/browser/browserservices/InstalledWebappDataRegisterTest.java",
  "junit/src/org/chromium/chrome/browser/browserservices/QualityEnforcerUnitTest.java",
  "junit/src/org/chromium/chrome/browser/browserservices/SessionDataHolderTest.java",
  "junit/src/org/chromium/chrome/browser/browserservices/TrustedWebActivityClientTest.java",
  "junit/src/org/chromium/chrome/browser/browserservices/digitalgoods/DigitalGoodsConverterTest.java",
  "junit/src/org/chromium/chrome/browser/browserservices/digitalgoods/DigitalGoodsUnitTest.java",
  "junit/src/org/chromium/chrome/browser/browserservices/digitalgoods/MockTrustedWebActivityClient.java",
  "junit/src/org/chromium/chrome/browser/browserservices/permissiondelegation/InstalledWebappGeolocationBridgeTest.java",
  "junit/src/org/chromium/chrome/browser/browserservices/permissiondelegation/InstalledWebappPermissionManagerTest.java",
  "junit/src/org/chromium/chrome/browser/browserservices/permissiondelegation/LocationPermissionUpdaterTest.java",
  "junit/src/org/chromium/chrome/browser/browserservices/permissiondelegation/NotificationChannelPreserverTest.java",
  "junit/src/org/chromium/chrome/browser/browserservices/permissiondelegation/NotificationPermissionUpdaterTest.java",
  "junit/src/org/chromium/chrome/browser/browserservices/permissiondelegation/PermissionUpdaterTest.java",
  "junit/src/org/chromium/chrome/browser/browserservices/trustedwebactivityui/controller/TrustedWebActivityBrowserControlsVisibilityManagerTest.java",
  "junit/src/org/chromium/chrome/browser/browserservices/ui/controller/CurrentPageVerifierTest.java",
  "junit/src/org/chromium/chrome/browser/browserservices/ui/controller/TestVerifier.java",
  "junit/src/org/chromium/chrome/browser/browserservices/ui/controller/trustedwebactivity/TrustedWebActivityDisclosureControllerTest.java",
  "junit/src/org/chromium/chrome/browser/browserservices/ui/controller/trustedwebactivity/TrustedWebActivityOpenTimeRecorderTest.java",
  "junit/src/org/chromium/chrome/browser/browserservices/ui/controller/trustedwebactivity/TwaVerifierTest.java",
  "junit/src/org/chromium/chrome/browser/browserservices/ui/controller/webapps/WebappDisclosureControllerTest.java",
  "junit/src/org/chromium/chrome/browser/browserservices/ui/trustedwebactivity/DisclosureAcceptanceBroadcastReceiverTest.java",
  "junit/src/org/chromium/chrome/browser/browserservices/ui/trustedwebactivity/DisclosureUiPickerTest.java",
  "junit/src/org/chromium/chrome/browser/browserservices/ui/trustedwebactivity/FilledLazy.java",
  "junit/src/org/chromium/chrome/browser/browserservices/ui/view/DisclosureInfobarTest.java",
  "junit/src/org/chromium/chrome/browser/browserservices/ui/view/DisclosureNotificationTest.java",
  "junit/src/org/chromium/chrome/browser/browserservices/ui/view/DisclosureSnackbarTest.java",
  "junit/src/org/chromium/chrome/browser/compositor/CompositorSurfaceManagerImplTest.java",
  "junit/src/org/chromium/chrome/browser/compositor/CompositorViewHolderUnitTest.java",
  "junit/src/org/chromium/chrome/browser/compositor/layouts/SceneOverlayTest.java",
  "junit/src/org/chromium/chrome/browser/compositor/layouts/StaticLayoutUnitTest.java",
  "junit/src/org/chromium/chrome/browser/compositor/overlays/strip/ScrollingStripStackerUnitTest.java",
  "junit/src/org/chromium/chrome/browser/compositor/overlays/strip/StripLayoutHelperTest.java",
  "junit/src/org/chromium/chrome/browser/compositor/overlays/strip/StripStackerUnitTest.java",
  "junit/src/org/chromium/chrome/browser/compositor/overlays/strip/TabUsageTrackerTest.java",
  "junit/src/org/chromium/chrome/browser/compositor/overlays/strip/TestTabModel.java",
  "junit/src/org/chromium/chrome/browser/content_capture/ContentCaptureHistoryDeletionObserverTest.java",
  "junit/src/org/chromium/chrome/browser/contextmenu/ContextMenuCoordinatorTest.java",
  "junit/src/org/chromium/chrome/browser/contextmenu/ContextMenuHeaderMediatorTest.java",
  "junit/src/org/chromium/chrome/browser/contextmenu/ContextMenuUtilsUnitTest.java",
  "junit/src/org/chromium/chrome/browser/contextualsearch/ContextualSearchContextTest.java",
  "junit/src/org/chromium/chrome/browser/contextualsearch/ContextualSearchInternalStateTest.java",
  "junit/src/org/chromium/chrome/browser/contextualsearch/ContextualSearchSelectionControllerTest.java",
  "junit/src/org/chromium/chrome/browser/contextualsearch/ContextualSearchTranslationImplTest.java",
  "junit/src/org/chromium/chrome/browser/contextualsearch/RelatedSearchesListTest.java",
  "junit/src/org/chromium/chrome/browser/contextualsearch/RelatedSearchesStampTest.java",
  "junit/src/org/chromium/chrome/browser/contextualsearch/SelectionClientManagerTest.java",
  "junit/src/org/chromium/chrome/browser/cookies/CanonicalCookieTest.java",
  "junit/src/org/chromium/chrome/browser/cryptids/ProbabilisticCryptidRendererUnitTest.java",
  "junit/src/org/chromium/chrome/browser/customtabs/ClientManagerTest.java",
  "junit/src/org/chromium/chrome/browser/customtabs/CloseButtonNavigatorTest.java",
  "junit/src/org/chromium/chrome/browser/customtabs/CustomTabActivityLifecycleUmaTrackerUnitTest.java",
  "junit/src/org/chromium/chrome/browser/customtabs/CustomTabIntentDataProviderTest.java",
  "junit/src/org/chromium/chrome/browser/customtabs/CustomTabNavigationBarControllerTest.java",
  "junit/src/org/chromium/chrome/browser/customtabs/CustomTabNightModeStateControllerTest.java",
  "junit/src/org/chromium/chrome/browser/customtabs/CustomTabStatusBarColorProviderTest.java",
  "junit/src/org/chromium/chrome/browser/customtabs/IncognitoCustomTabSnapshotControllerTest.java",
  "junit/src/org/chromium/chrome/browser/customtabs/LaunchesWithColorSchemeTest.java",
  "junit/src/org/chromium/chrome/browser/customtabs/MockPostMessageHandler.java",
  "junit/src/org/chromium/chrome/browser/customtabs/NavigationInfoCaptureTriggerTest.java",
  "junit/src/org/chromium/chrome/browser/customtabs/PartialCustomTabHeightStrategyTest.java",
  "junit/src/org/chromium/chrome/browser/customtabs/RequestThrottlerTest.java",
  "junit/src/org/chromium/chrome/browser/customtabs/TrustedCdnTest.java",
  "junit/src/org/chromium/chrome/browser/customtabs/content/CustomTabActivityContentTestEnvironment.java",
  "junit/src/org/chromium/chrome/browser/customtabs/content/CustomTabActivityNavigationControllerTest.java",
  "junit/src/org/chromium/chrome/browser/customtabs/content/CustomTabActivityTabControllerUnitTest.java",
  "junit/src/org/chromium/chrome/browser/customtabs/content/CustomTabActivityUrlLoadingTest.java",
  "junit/src/org/chromium/chrome/browser/customtabs/features/ImmersiveModeControllerTest.java",
  "junit/src/org/chromium/chrome/browser/customtabs/features/branding/BrandingCheckerUnitTest.java",
  "junit/src/org/chromium/chrome/browser/customtabs/features/branding/BrandingControllerUnitTest.java",
  "junit/src/org/chromium/chrome/browser/customtabs/features/toolbar/BrandingSecurityButtonAnimationDelegateUnitTest.java",
  "junit/src/org/chromium/chrome/browser/customtabs/features/toolbar/CustomTabToolbarUnitTest.java",
  "junit/src/org/chromium/chrome/browser/customtabs/shadows/ShadowExternalNavigationDelegateImpl.java",
  "junit/src/org/chromium/chrome/browser/directactions/FindInPageDirectActionHandlerTest.java",
  "junit/src/org/chromium/chrome/browser/directactions/GoBackDirectActionHandlerTest.java",
  "junit/src/org/chromium/chrome/browser/display_cutout/DisplayCutoutControllerTest.java",
  "junit/src/org/chromium/chrome/browser/dom_distiller/ReaderModeManagerTest.java",
  "junit/src/org/chromium/chrome/browser/dom_distiller/ReaderModeToolbarButtonControllerTest.java",
  "junit/src/org/chromium/chrome/browser/download/DownloadSharedPreferenceEntryTest.java",
  "junit/src/org/chromium/chrome/browser/download/OfflineContentAvailabilityStatusProviderTest.java",
  "junit/src/org/chromium/chrome/browser/download/items/OfflineContentAggregatorNotificationBridgeUiTest.java",
  "junit/src/org/chromium/chrome/browser/explore_sites/ExploreSitesBackgroundTaskUnitTest.java",
  "junit/src/org/chromium/chrome/browser/explore_sites/ExploreSitesCategoryUnitTest.java",
  "junit/src/org/chromium/chrome/browser/explore_sites/ExploreSitesPageStateUnitTest.java",
  "junit/src/org/chromium/chrome/browser/feedback/ChromeFeedbackCollectorUnitTest.java",
  "junit/src/org/chromium/chrome/browser/feedback/FeedFeedbackCollectorTest.java",
  "junit/src/org/chromium/chrome/browser/findinpage/FindToolbarManagerTest.java",
  "junit/src/org/chromium/chrome/browser/firstrun/ChildAccountStatusSupplierTest.java",
  "junit/src/org/chromium/chrome/browser/firstrun/FirstRunAppRestrictionInfoTest.java",
  "junit/src/org/chromium/chrome/browser/firstrun/FirstRunFlowSequencerTest.java",
  "junit/src/org/chromium/chrome/browser/firstrun/FirstRunIntegrationUnitTest.java",
  "junit/src/org/chromium/chrome/browser/firstrun/PolicyLoadListenerUnitTest.java",
  "junit/src/org/chromium/chrome/browser/firstrun/SkipTosDialogPolicyListenerUnitTest.java",
  "junit/src/org/chromium/chrome/browser/firstrun/TosDialogBehaviorSharedPrefInvalidatorUnitTest.java",
  "junit/src/org/chromium/chrome/browser/flags/BadFlagsSnackbarManagerTest.java",
  "junit/src/org/chromium/chrome/browser/fonts/FontPreloaderUnitTest.java",
  "junit/src/org/chromium/chrome/browser/fullscreen/BrowserControlsManagerUnitTest.java",
  "junit/src/org/chromium/chrome/browser/fullscreen/FullscreenHtmlApiHandlerUnitTest.java",
  "junit/src/org/chromium/chrome/browser/gcore/GoogleApiClientHelperTest.java",
  "junit/src/org/chromium/chrome/browser/gsa/GSAStateUnitTest.java",
  "junit/src/org/chromium/chrome/browser/history/HistoryAdapterAccessibilityTest.java",
  "junit/src/org/chromium/chrome/browser/history/HistoryAdapterTest.java",
  "junit/src/org/chromium/chrome/browser/history/HistoryDeletionBridgeTest.java",
  "junit/src/org/chromium/chrome/browser/history/HistoryUITest.java",
  "junit/src/org/chromium/chrome/browser/history_clusters/HistoryClustersCoordinatorTest.java",
  "junit/src/org/chromium/chrome/browser/history_clusters/HistoryClustersMediatorTest.java",
  "junit/src/org/chromium/chrome/browser/history_clusters/HistoryClustersMetricsLoggerTest.java",
  "junit/src/org/chromium/chrome/browser/homepage/HomepageManagerTest.java",
  "junit/src/org/chromium/chrome/browser/homepage/HomepagePolicyManagerTest.java",
  "junit/src/org/chromium/chrome/browser/homepage/settings/HomepageSettingsUnitTest.java",
  "junit/src/org/chromium/chrome/browser/incognito/IncognitoTabbedSnapshotControllerTest.java",
  "junit/src/org/chromium/chrome/browser/infobar/IPHInfoBarSupportTest.java",
  "junit/src/org/chromium/chrome/browser/init/AsyncInitTaskRunnerTest.java",
  "junit/src/org/chromium/chrome/browser/instantapps/InstantAppsMessageDelegateTest.java",
  "junit/src/org/chromium/chrome/browser/invalidation/ResumableDelayedTaskRunnerTest.java",
  "junit/src/org/chromium/chrome/browser/invalidation/SessionsInvalidationManagerTest.java",
  "junit/src/org/chromium/chrome/browser/javascript/WebContextFetcherTest.java",
  "junit/src/org/chromium/chrome/browser/media/ui/MediaNotificationActionsUpdatedTest.java",
  "junit/src/org/chromium/chrome/browser/media/ui/MediaNotificationFaviconTest.java",
  "junit/src/org/chromium/chrome/browser/media/ui/MediaNotificationManagerNotificationTest.java",
  "junit/src/org/chromium/chrome/browser/media/ui/MediaNotificationServiceActionsTest.java",
  "junit/src/org/chromium/chrome/browser/media/ui/MediaNotificationServiceLifecycleTest.java",
  "junit/src/org/chromium/chrome/browser/media/ui/MediaNotificationTestBase.java",
  "junit/src/org/chromium/chrome/browser/media/ui/MediaNotificationTestShadowResources.java",
  "junit/src/org/chromium/chrome/browser/media/ui/MediaNotificationTestTabHolder.java",
  "junit/src/org/chromium/chrome/browser/media/ui/MediaNotificationThrottlerTest.java",
  "junit/src/org/chromium/chrome/browser/media/ui/MediaNotificationTitleUpdatedTest.java",
  "junit/src/org/chromium/chrome/browser/messages/ChromeMessageAutodismissDurationProviderTest.java",
  "junit/src/org/chromium/chrome/browser/messages/ChromeMessageQueueMediatorTest.java",
  "junit/src/org/chromium/chrome/browser/metrics/VariationsSessionTest.java",
  "junit/src/org/chromium/chrome/browser/multiwindow/MultiInstanceManagerApi31UnitTest.java",
  "junit/src/org/chromium/chrome/browser/multiwindow/MultiWindowTestUtils.java",
  "junit/src/org/chromium/chrome/browser/multiwindow/MultiWindowUtilsUnitTest.java",
  "junit/src/org/chromium/chrome/browser/native_page/NativePageFactoryTest.java",
  "junit/src/org/chromium/chrome/browser/net/nqe/NetworkQualityProviderTest.java",
  "junit/src/org/chromium/chrome/browser/night_mode/GlobalNightModeStateControllerTest.java",
  "junit/src/org/chromium/chrome/browser/night_mode/GlobalNightModeStateProviderHolderTest.java",
  "junit/src/org/chromium/chrome/browser/notifications/NotificationTriggerBackgroundTaskTest.java",
  "junit/src/org/chromium/chrome/browser/notifications/NotificationTriggerSchedulerTest.java",
  "junit/src/org/chromium/chrome/browser/notifications/WebPlatformNotificationMetricsTest.java",
  "junit/src/org/chromium/chrome/browser/ntp/FeedPositionUtilUnitTest.java",
  "junit/src/org/chromium/chrome/browser/offlinepages/BackgroundSchedulerTest.java",
  "junit/src/org/chromium/chrome/browser/offlinepages/ClientIdTest.java",
  "junit/src/org/chromium/chrome/browser/offlinepages/OfflineBackgroundTaskTest.java",
  "junit/src/org/chromium/chrome/browser/offlinepages/OfflinePageBridgeUnitTest.java",
  "junit/src/org/chromium/chrome/browser/offlinepages/OfflinePageOriginUnitTest.java",
  "junit/src/org/chromium/chrome/browser/offlinepages/OfflinePageTabObserverTest.java",
  "junit/src/org/chromium/chrome/browser/offlinepages/OfflinePageUtilsUnitTest.java",
  "junit/src/org/chromium/chrome/browser/offlinepages/TaskExtrasPackerTest.java",
  "junit/src/org/chromium/chrome/browser/offlinepages/indicator/OfflineDetectorUnitTest.java",
  "junit/src/org/chromium/chrome/browser/offlinepages/indicator/OfflineIndicatorControllerV2UnitTest.java",
  "junit/src/org/chromium/chrome/browser/offlinepages/indicator/OfflineIndicatorMetricsDelegateUnitTest.java",
  "junit/src/org/chromium/chrome/browser/offlinepages/measurements/OfflineMeasurementsBackgroundTaskUnitTest.java",
  "junit/src/org/chromium/chrome/browser/offlinepages/prefetch/PrefetchBackgroundTaskUnitTest.java",
  "junit/src/org/chromium/chrome/browser/omaha/AttributeFinder.java",
  "junit/src/org/chromium/chrome/browser/omaha/ExponentialBackoffSchedulerTest.java",
  "junit/src/org/chromium/chrome/browser/omaha/MockExponentialBackoffScheduler.java",
  "junit/src/org/chromium/chrome/browser/omaha/MockRequestGenerator.java",
  "junit/src/org/chromium/chrome/browser/omaha/OmahaBaseTest.java",
  "junit/src/org/chromium/chrome/browser/omaha/RequestGeneratorTest.java",
  "junit/src/org/chromium/chrome/browser/omaha/ResponseParserTest.java",
  "junit/src/org/chromium/chrome/browser/omaha/StringSanitizerTest.java",
  "junit/src/org/chromium/chrome/browser/omaha/VersionNumberTest.java",
  "junit/src/org/chromium/chrome/browser/omaha/metrics/HistogramUtilsTest.java",
  "junit/src/org/chromium/chrome/browser/omaha/metrics/UpdateSuccessMetricsTest.java",
  "junit/src/org/chromium/chrome/browser/page_info/PageInfoPermissionsControllerUnitTest.java",
  "junit/src/org/chromium/chrome/browser/page_info/PermissionParamsListBuilderUnitTest.java",
  "junit/src/org/chromium/chrome/browser/partnerbookmarks/PartnerBookmarksFaviconThrottleTest.java",
  "junit/src/org/chromium/chrome/browser/partnerbookmarks/PartnerBookmarksReaderTest.java",
  "junit/src/org/chromium/chrome/browser/password_manager/settings/DialogManagerTest.java",
  "junit/src/org/chromium/chrome/browser/password_manager/settings/EnsureAsyncPostingRule.java",
  "junit/src/org/chromium/chrome/browser/password_manager/settings/ExportWarningDialogFragmentTest.java",
  "junit/src/org/chromium/chrome/browser/password_manager/settings/SingleThreadBarrierClosureTest.java",
  "junit/src/org/chromium/chrome/browser/password_manager/settings/TimedCallbackDelayerTest.java",
  "junit/src/org/chromium/chrome/browser/privacy/settings/PrivacyPreferencesManagerImplTest.java",
  "junit/src/org/chromium/chrome/browser/quickactionsearchwidget/QuickActionSearchWidgetProviderTest.java",
  "junit/src/org/chromium/chrome/browser/read_later/ReadLaterIPHControllerUnitTest.java",
  "junit/src/org/chromium/chrome/browser/read_later/ReadingListUtilsUnitTest.java",
  "junit/src/org/chromium/chrome/browser/reengagement/ReengagementNotificationControllerTest.java",
  "junit/src/org/chromium/chrome/browser/safe_browsing/SafeBrowsingReferringAppBridgeTest.java",
  "junit/src/org/chromium/chrome/browser/search_engines/SearchEngineChoiceMetricsTest.java",
  "junit/src/org/chromium/chrome/browser/search_engines/SearchEngineChoiceNotificationTest.java",
  "junit/src/org/chromium/chrome/browser/search_engines/settings/SearchEngineAdapterTest.java",
  "junit/src/org/chromium/chrome/browser/share/ShareButtonControllerUnitTest.java",
  "junit/src/org/chromium/chrome/browser/sharing/click_to_call/ClickToCallMessageHandlerTest.java",
  "junit/src/org/chromium/chrome/browser/signin/SigninBridgeTest.java",
  "junit/src/org/chromium/chrome/browser/signin/SigninManagerImplTest.java",
  "junit/src/org/chromium/chrome/browser/signin/SyncConsentActivityLauncherImplTest.java",
  "junit/src/org/chromium/chrome/browser/status_indicator/StatusIndicatorMediatorTest.java",
  "junit/src/org/chromium/chrome/browser/suggestions/SuggestionsImageFetcherTest.java",
  "junit/src/org/chromium/chrome/browser/suggestions/tile/MostVisitedMediatorUnitTest.java",
  "junit/src/org/chromium/chrome/browser/suggestions/tile/TileRendererTest.java",
  "junit/src/org/chromium/chrome/browser/supervised_user/ChildAccountServiceTest.java",
  "junit/src/org/chromium/chrome/browser/survey/ChromeSurveyControllerFlowTest.java",
  "junit/src/org/chromium/chrome/browser/survey/ChromeSurveyControllerTest.java",
  "junit/src/org/chromium/chrome/browser/survey/SurveyHttpClientBridgeUnitTest.java",
  "junit/src/org/chromium/chrome/browser/sync/ui/SyncErrorPromptUtilsTest.java",
  "junit/src/org/chromium/chrome/browser/tab/RequestDesktopUtilsUnitTest.java",
  "junit/src/org/chromium/chrome/browser/tab/TabAttributesTest.java",
  "junit/src/org/chromium/chrome/browser/tab/TabBrowserControlsConstraintsHelperTest.java",
  "junit/src/org/chromium/chrome/browser/tab/TabBrowserControlsOffsetHelperTest.java",
  "junit/src/org/chromium/chrome/browser/tab/TabStateExtractorTest.java",
  "junit/src/org/chromium/chrome/browser/tab/TabUnitTest.java",
  "junit/src/org/chromium/chrome/browser/tab/TabUtilsUnitTest.java",
  "junit/src/org/chromium/chrome/browser/tab/TabViewAndroidDelegateTest.java",
  "junit/src/org/chromium/chrome/browser/tab/TabViewManagerUnitTest.java",
  "junit/src/org/chromium/chrome/browser/tab/tab_restore/HistoricalTabModelObserverUnitTest.java",
  "junit/src/org/chromium/chrome/browser/tab/tab_restore/HistoricalTabSaverImplUnitTest.java",
  "junit/src/org/chromium/chrome/browser/tabbed_mode/TabbedAppMenuPropertiesDelegateUnitTest.java",
  "junit/src/org/chromium/chrome/browser/tabmodel/PendingTabClosureManagerTest.java",
  "junit/src/org/chromium/chrome/browser/tabmodel/TabModelImplUnitTest.java",
  "junit/src/org/chromium/chrome/browser/tabmodel/TabModelSelectorImplTest.java",
  "junit/src/org/chromium/chrome/browser/tabmodel/TabModelSelectorProfileSupplierTest.java",
  "junit/src/org/chromium/chrome/browser/tabmodel/UndoTabModelUnitTest.java",
  "junit/src/org/chromium/chrome/browser/tasks/EngagementTimeUtilTest.java",
  "junit/src/org/chromium/chrome/browser/tasks/JourneyManagerTest.java",
  "junit/src/org/chromium/chrome/browser/tasks/ReturnToChromeUtilUnitTest.java",
  "junit/src/org/chromium/chrome/browser/toolbar/LocationBarModelUnitTest.java",
  "junit/src/org/chromium/chrome/browser/toolbar/ToolbarTabControllerImplTest.java",
  "junit/src/org/chromium/chrome/browser/toolbar/adaptive/OptionalNewTabButtonControllerActivityTest.java",
  "junit/src/org/chromium/chrome/browser/toolbar/top/StartSurfaceToolbarMediatorUnitTest.java",
  "junit/src/org/chromium/chrome/browser/ui/AppLaunchDrawBlockerUnitTest.java",
  "junit/src/org/chromium/chrome/browser/ui/IncognitoRestoreAppLaunchDrawBlockerUnitTest.java",
  "junit/src/org/chromium/chrome/browser/usage_stats/EventTrackerTest.java",
  "junit/src/org/chromium/chrome/browser/usage_stats/PageViewObserverTest.java",
  "junit/src/org/chromium/chrome/browser/webapps/MockWebappDataStorageClockRule.java",
  "junit/src/org/chromium/chrome/browser/webapps/WebApkIconNameUpdateDialogTest.java",
  "junit/src/org/chromium/chrome/browser/webapps/WebApkInstallNotificationTest.java",
  "junit/src/org/chromium/chrome/browser/webapps/WebApkIntentDataProviderFactoryTest.java",
  "junit/src/org/chromium/chrome/browser/webapps/WebApkShareTargetUtilTest.java",
  "junit/src/org/chromium/chrome/browser/webapps/WebApkUpdateManagerUnitTest.java",
  "junit/src/org/chromium/chrome/browser/webapps/WebappAuthenticatorTest.java",
  "junit/src/org/chromium/chrome/browser/webapps/WebappDataStorageTest.java",
  "junit/src/org/chromium/chrome/browser/webapps/WebappDirectoryManagerTest.java",
  "junit/src/org/chromium/chrome/browser/webapps/WebappLauncherActivityTest.java",
  "junit/src/org/chromium/chrome/browser/webapps/WebappRegistryTest.java",
]
