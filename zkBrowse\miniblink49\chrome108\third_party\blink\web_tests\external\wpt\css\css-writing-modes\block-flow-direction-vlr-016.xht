<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

 <head>

  <title>CSS Writing Modes Test: inline-block and 'vertical-lr' - block flow direction of block-level boxes</title>

  <link rel="author" title="Gérard Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" />
  <link rel="help" href="http://www.w3.org/TR/css-writing-modes-3/#block-flow" title="3.1 Block Flow Direction: the writing-mode property" />
  <link rel="match" href="block-flow-direction-001-ref.xht" />

  <meta content="ahem" name="flags" />
  <meta content="This test checks that an inline-block with its 'writing-mode' set to 'vertical-lr' establishes a block formating context with a left-to-right block flow direction." name="assert" />

  <link rel="stylesheet" type="text/css" href="/fonts/ahem.css" />
  <style type="text/css"><![CDATA[
  body
    {
      color: yellow;
      font: 20px/1 Ahem;
    }

  div.inline-block
    {
      background-color: blue;
      border-left: blue solid 1em;
      border-top: blue solid 1em;
      display: inline-block;
      height: 8em;
      vertical-align: top;
  /*
  Why 'vertical-align: top' ?
  See
  http://lists.w3.org/Archives/Public/public-css-testsuite/2014Dec/0013.html
  for explanations
  */
      writing-mode: vertical-lr;
    }

  span
    {
      display: block;
    }

  span#right-border
    {
      border-right: blue solid 1em;
    }
  ]]></style>
 </head>

 <body>

  <div>

    <div class="inline-block">

<!-- The 1st left-most line of "P" --> <span>AAAAAAA</span>

<!-- The 2nd left-most line of "P" --> <span>B&nbsp; C&nbsp;&nbsp; </span>

<!-- The 3rd left-most line of "P" --> <span>D&nbsp; E&nbsp;&nbsp; </span>

<!-- The 4th left-most line of "P" --> <span>FFFF&nbsp;&nbsp; </span>

    </div><div class="inline-block">

<!-- The left-most line of "A" --> <span>GGGGGGG</span>

<!-- The 2nd left-most line of "A" --> <span>H&nbsp; J&nbsp;&nbsp; </span>

<!-- The 3rd left-most line of "A" --> <span>K&nbsp; L&nbsp;&nbsp; </span>

<!-- The 4th left-most line of "A" --> <span>MMMMMMM</span>

    </div><div class="inline-block">

<!-- The 1st left-most line of left-most "S" --> <span>NNNN&nbsp; Q</span>

<!-- The 2nd left-most line of left-most "S" --> <span>R&nbsp; S&nbsp; T</span>

<!-- The 3rd left-most line of left-most "S" --> <span>U&nbsp; V&nbsp; W</span>

<!-- The 4th left-most line of left-most "S" --> <span>X&nbsp; YYYY</span>

    </div><div class="inline-block">

<!-- The left-most line of right-most "S" --> <span>aaaa&nbsp; b</span>

<!-- The 2nd left-most line of right-most "S" --> <span>c&nbsp; d&nbsp; e</span>

<!-- The 3rd left-most line of right-most "S" --> <span>f&nbsp; g&nbsp; h</span>

<!-- The 4th left-most line of right-most "S" --> <span id="right-border">j&nbsp; kkkk</span>

    </div>

  </div>

 </body>
</html>