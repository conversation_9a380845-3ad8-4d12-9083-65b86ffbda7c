<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background with (repeat attachment image)</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <meta name="flags" content="image interact" />
        <meta name="assert" content="Background with (repeat attachment image) sets the background to the image specified, the image is repeated across the top, and scrolls with the box." />
        <style type="text/css">
            #div1
            {
                height: 200px;
                overflow: scroll;
                width: 200px;
            }
            div div
            {
                background: repeat-x scroll url("support/cat.png");
                width: 400px;
                height: 400px;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is a box below with cat images across the top. Also, the cat images move when the box is scrolled.</p>
        <div id="div1">
            <div></div>
        </div>
    </body>
</html>