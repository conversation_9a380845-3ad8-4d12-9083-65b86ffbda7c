<!DOCTYPE html>
<meta charset="utf-8">
<title>TestDriver actions: text edit commands</title>
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<script src="/resources/testdriver.js"></script>
<script src="/resources/testdriver-actions.js"></script>
<script src="/resources/testdriver-vendor.js"></script>

<style>
div { padding:0px; margin: 0px; }
</style>
<body>
  <div>
    <input type="text" id="text1" value="Hello World" />
    <input type="text" id="text2">
  </div>
</body>
<script>
async_test(t => {
  let text1 = document.getElementById("text1");
  let text2 = document.getElementById("text2");
  text1.addEventListener("click", function() {
    let text1 = document.getElementById("text1");
    text1.value="new text";
  });

  const ctrl_key = "\uE009";
  const cmd_key = "\uE03D";
  let edit_command_key = ctrl_key;
  if(navigator.platform.includes('Mac'))
    edit_command_key = cmd_key;

  let actions = new test_driver.Actions()
      .pointerMove(0, 0, {origin: text1})
      .pointerDown()
      .pointerUp()
      .addTick()
      .keyDown(edit_command_key)
      .keyDown("a")
      .keyUp("a")
      .keyDown("x")
      .keyUp("x")
      .keyUp(edit_command_key)
      .addTick()
      .pointerMove(0, 0, {origin: text2})
      .pointerDown()
      .pointerUp()
      .keyDown(edit_command_key)
      .keyDown("v")
      .keyUp("v")
      .keyUp(edit_command_key);

  actions.send()
    .then(t.step_func_done(() => {
      assert_equals(text1.value, "");
      assert_equals(text2.value, "new text");
    }))
    .catch(e => t.step_func(() => assert_unreached("Actions sequence failed " + e)));
});
</script>
