<!DOCTYPE html>
<html>
<head>
<link rel="help" href="http://www.w3.org/TR/DOM-Level-3-Events/#events-WheelEvent">
<script src="../../resources/js-test.js"></script>
<script>
function runTest()
{
    if (!window.testRunner || !window.eventSender) {
        debug("FAIL: This test requires window.testRunner and window.eventSender.");
        finishJSTest();
        return;
    }

    testRunner.overridePreference('WebKitTabToLinksPreferenceKey', true);

    for (var i = 0; i < 3; i++)
        eventSender.keyDown('\t');

    finishJSTest();
};

window.addEventListener('load', runTest);
</script>
</head>
<body>

<p>This test ensures that we can tab to all anchor elements. Press tab three
times to focus the elements below.

<p><a onfocus="testPassed('gave focus to focusable element')" href="#">Focusable</a></p>
<p><a onfocus="testFailed('gave focus to unfocusable element')">Not focusable</a></p>
<p><a tabindex=0 onfocus="testPassed('gave focus to focusable element')" href="#">Focusable</a></p>
<p><a tabindex=0 onfocus="testPassed('gave focus to focusable element')">Focusable</a></p>
<p><input onfocus="testFailed('should have stopped testing before this element')"></p>

<p>Result

<div id="console"></div>

</body>
</html>
