<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN"
"http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">

<!--

   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

-->
<!-- ========================================================================= -->
<!-- feTile test                                                               -->
<!--                                                                           -->
<!-- <AUTHOR>                                         -->
<!-- @version $Id: feTile.svg 475477 2006-11-15 22:44:28Z cam $ -->
<!-- ========================================================================= -->

<?xml-stylesheet type="text/css" href="../resources/test.css" ?>

<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="body" width="450" height="500" viewBox="0 0 450 500" externalResourcesRequired="true">
<title>feTile Test</title>

    <!-- ============================================================= -->
    <!-- Test content                                                  -->
    <!-- ============================================================= -->
    <g id="testContent" class="legend" style="text-anchor:middle">
        <text x="225" y="40" class="title">
            feTile Test
        </text>

        <defs>
            <linearGradient id="tileFill" gradientUnits="objectBoundingBox"
                            x1="0" y1="0" x2="1" y2="1">
                <stop offset="0" stop-color="white" />
                <stop offset=".5" stop-color="#44ccff" />
                <stop offset="1" stop-color="#000033" />
            </linearGradient>

            <!-- ============================== -->
            <!-- Simple tile in top left corner -->
            <!-- ============================== -->
            <filter id="tile_1" primitiveUnits="objectBoundingBox"
                filterUnits="objectBoundingBox"
                x="0" y="0" width="200%" height="200%">
                <feOffset x="0%" y="0%" width="100%" height="100%"/>
                <feTile /> 
            </filter>
                
            <!-- =============================== -->
            <!-- Tile in center of filter region -->
            <!-- =============================== -->
            <filter id="tile_2" primitiveUnits="objectBoundingBox"
                filterUnits="objectBoundingBox"
                x="-50%" y="-50%" width="200%" height="200%">
                <feOffset x="0%" y="0%" width="100%" height="100%"/>
                <feTile />
            </filter>

            <!-- =============================== -->
            <!-- Tile offset in top left corner  -->
            <!-- =============================== -->
            <filter id="tile_3" primitiveUnits="objectBoundingBox"
                filterUnits="objectBoundingBox"
                x="-25%" y="-25%" width="200%" height="200%">
                <feOffset x="0%" y="0%" width="50%" height="50%"/>
                <feTile />
            </filter>


                

        </defs>

        <g transform="translate(20, 100)">
            <rect x="0" y="0" width="50" height="25" style="fill:url(#tileFill); filter:url(#tile_1)"/>
            <rect x="0" y="0" width="100" height="50" style="fill:none; stroke:black;"/>
            <text x="50" y="-15">tile_1</text>
            <text x="50" y="-3">4 tiles, quarter size</text>
        </g>

        <g transform="translate(20, 200)">
            <rect x="25" y="12.5" width="50" height="25" style="fill:url(#tileFill); filter:url(#tile_2)"/>
            <rect x="0" y="0" width="100" height="50" style="fill:none; stroke:black;"/>
            <text x="50" y="-15">tile_2 (centered)</text>
            <text x="50" y="-3">9 tiles, 1 full, 8 partial</text>
        </g>

        <g transform="translate(32.5, 307.25)">
            <rect x="0" y="0" width="50" height="25" style="fill:url(#tileFill); filter:url(#tile_3)"/>
            <rect x="-12.5" y="-7.25" width="100" height="50" style="fill:none; stroke:black;"/>
            <text x="37.5" y="-22.25">tile_3 (top left offset)</text>
            <text x="37.5" y="-10.25">9 tiles, 1 full, 8 partial</text>
        </g>

        <g transform="translate(245, 100) skewX(30)">
            <rect x="0" y="0" width="50" height="25" style="fill:url(#tileFill); filter:url(#tile_1)"/>
            <rect x="0" y="0" width="100" height="50" style="fill:none; stroke:black;"/>
        </g>
        <g transform="translate(245, 100)">
            <text x="50" y="-15">tile_1</text>
            <text x="50" y="-3">skewed X</text>
        </g>

        <g transform="translate(245, 200) translate(50, 30) rotate(20) scale(.7, .7) translate(-50, -25)">
            <rect x="25" y="12.5" width="50" height="25" style="fill:url(#tileFill); filter:url(#tile_2)"/>
            <rect x="0" y="0" width="100" height="50" style="fill:none; stroke:black;"/>
        </g>
        <g transform="translate(245, 200)">
            <text x="50" y="-15">tile_2</text>
            <text x="50" y="-3">(centered, rotate &amp; scale)</text>
        </g>

        <g transform="translate(257.5, 307.25) translate(-12.5, 0) skewY(15) translate(12.5, 0)">
            <rect x="0" y="0" width="50" height="25" style="fill:url(#tileFill); filter:url(#tile_3)"/>
            <rect x="-12.5" y="-6.25" width="100" height="50" style="fill:none; stroke:black;"/>
        </g>
        <g transform="translate(257.5, 307.25) ">
            <text x="37.5" y="-22.25">tile_3 (top left offset)</text>
            <text x="37.5" y="-10.25">skewed Y</text>
        </g>

    </g>

        <!-- Link to visual reference -->
        <a xlink:href="feTileTarget.svg" xlink:show="new">
            <text x="20" y="475">How it should look like...</text>
        </a>


    <!-- ============================================================= -->
    <!-- Batik sample mark                                             -->
    <!-- ============================================================= -->
    <use xlink:href="../resources/batikLogo.svg#Batik_Tag_Box" />
    
</svg>
