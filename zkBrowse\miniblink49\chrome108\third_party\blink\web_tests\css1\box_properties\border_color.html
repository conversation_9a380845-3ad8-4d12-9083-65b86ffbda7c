<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.5.16 border-color</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
.one {border-color: purple; border-style: solid;}
.two {border-color: purple; border-width: medium; border-style: solid;}
.three {border-color: purple green blue yellow; border-width: medium; border-style: solid;}</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.one {border-color: purple; border-style: solid;}
.two {border-color: purple; border-width: medium; border-style: solid;}
.three {border-color: purple green blue yellow; border-width: medium; border-style: solid;}
</PRE>
<HR>
<P class="one">
This element should have a purple border surrounding it.
</P>
<P class="two">
This element should have a medium-width purple border surrounding it.
</P>
<P class="three">
This element should be surrounded by a medium width border which is purple on top, green on the right side, blue on the bottom, and yellow on the left side.
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P class="one">
This element should have a purple border surrounding it.
</P>
<P class="two">
This element should have a medium-width purple border surrounding it.
</P>
<P class="three">
This element should be surrounded by a medium width border which is purple on top, green on the right side, blue on the bottom, and yellow on the left side.
</P>
</TD></TR></TABLE></BODY>
</HTML>
