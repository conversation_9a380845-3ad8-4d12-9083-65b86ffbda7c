<html>
<head>
<script>
if (window.testRunner) {
    testRunner.dumpAsText();
    testRunner.waitUntilDone();
    // Record current window count.
    window.windowCount = testRunner.windowCount();
}

function eventfire()
{
       oClickEvent = document.createEvent("MouseEvents");
       oClickEvent.initEvent("click", true, true, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
       document.body.dispatchEvent(oClickEvent);
}

window.onload = function() {
    if (!window.eventSender)
        return;

    eventSender.mouseMoveTo(50, 50);
    eventSender.mouseMoveTo(0, 0);
    
    function finishTest()
    {
        if (testRunner.windowCount() == window.windowCount)
            document.getElementById("console").innerText = "PASSED";
        else
            document.getElementById("console").innerText = "FAILED";
        testRunner.notifyDone();
    }
    setTimeout(finishTest, 0);
}
</script>
</head>
<body onMouseOver=eventfire() onMouseOut=eventfire() onclick="window.open('about:blank','','height=200,width=200')">
If you observe a popup this test has failed due to a MouseMovement being treated as a user gesture by the popup blocker.
<div id="console" style='height: 200; width: 200'></div>
</body>
</html>

