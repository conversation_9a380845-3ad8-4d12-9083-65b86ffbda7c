IN_PROC_BROWSER_TEST_F(TestName, WAI_3Dog_2DogRed_1Chicken_1Dog_3Chicken_2ChickenGreen) {
  // Test contents are generated by script. Please do not modify!
  // See `docs/webapps/why-is-this-test-failing.md` or
  // `docs/webapps/integration-testing-framework` for more info.
  // Sheriffs: Disabling this test is supported.
  helper_.StateChangeA(Animal::kDog);
  helper_.CheckB(Animal::kDog, Color::kRed);
  helper_.CheckA(Animal::kChicken);
  helper_.CheckA(Animal::kDog);
  helper_.StateChangeA(Animal::kChicken);
  helper_.CheckB(Animal::kChicken, Color::kGreen);
}