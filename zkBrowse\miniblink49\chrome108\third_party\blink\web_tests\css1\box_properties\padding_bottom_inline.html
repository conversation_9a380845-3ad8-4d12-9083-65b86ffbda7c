<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.5.08 padding-bottom</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
.one {padding-bottom: 25px; background-color: aqua;}
.two {padding-bottom: -10px; background-color: aqua;}
</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.one {padding-bottom: 25px; background-color: aqua;}
.two {padding-bottom: -10px; background-color: aqua;}

</PRE>
<HR>
<P style="background-color: gray;">
This element is unstyled save for a background color of gray.  It contains an <SPAN class="one">inline element of class <TT>one</TT>, giving it an aqua background and a 25px bottom padding</SPAN>.  Padding on inline elements does not affect line-height calculations, so all lines in this element should have the same line-height.  There may be implementation-specific limits on how much of the padding the user agent is able to display.
</P>
<P style="background-color: gray;">
This element is unstyled save for a background color of gray.  It contains an <SPAN class="two">inline element of class <TT>two</TT>, giving it an aqua background and no bottom padding, since negative padding values are not allowed</SPAN>.  Padding on inline elements does not affect line-height calculations, so all lines in this element should have the same line-height.
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P style="background-color: gray;">
This element is unstyled save for a background color of gray.  It contains an <SPAN class="one">inline element of class <TT>one</TT>, giving it an aqua background and a 25px bottom padding</SPAN>.  Padding on inline elements does not affect line-height calculations, so all lines in this element should have the same line-height.  There may be implementation-specific limits on how much of the padding the user agent is able to display.
</P>
<P style="background-color: gray;">
This element is unstyled save for a background color of gray.  It contains an <SPAN class="two">inline element of class <TT>two</TT>, giving it an aqua background and no bottom padding, since negative padding values are not allowed</SPAN>.  Padding on inline elements does not affect line-height calculations, so all lines in this element should have the same line-height.
</P>
</TD></TR></TABLE></BODY>
</HTML>
