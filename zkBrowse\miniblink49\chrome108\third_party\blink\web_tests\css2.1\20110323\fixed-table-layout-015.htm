<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
    <head>
        <title>CSS Test: Fixed table layout - specified cell width overrides specified column-group width</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/">
        <link rel="help" href="http://www.w3.org/TR/CSS21/tables.html#fixed-table-layout">
        <meta name="flags" content="">
        <meta name="assert" content="A column that has both a cell width and column-group width specified will use its cell specified width.">
        <style type="text/css">
            table
            {
                border-spacing: 0;
                table-layout: fixed;
                width: 200px;
            }
            #colgroup
            {
                width: 75px;
            }
            #cell
            {
                background: black;
                width: 100px;
            }
            td
            {
                padding: 0;
            }
            #div1
            {
                background: blue;
                width: 100px;
            }
            #div1, td
            {
                height: 1em;
            }
        </style>
    </head>
    <body>
        <p>Test passes if the boxes below are the same width.</p>
        <table>
            <colgroup id="colgroup">
                <col>
            </colgroup>
            <colgroup>
                <col>
            </colgroup>
            <tr>
                <td id="cell"></td>
                <td></td>
            </tr>
        </table>
        <div id="div1"></div>
    </body>
</html>