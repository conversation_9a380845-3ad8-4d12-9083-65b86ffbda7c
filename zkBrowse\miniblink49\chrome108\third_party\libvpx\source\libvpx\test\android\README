Android.mk will build vpx unittests on android.
1) Configure libvpx from the parent directory:
./libvpx/configure --target=armv7-android-gcc --enable-external-build \
  --enable-postproc --disable-install-srcs --enable-multi-res-encoding \
  --enable-temporal-denoising --disable-unit-tests --disable-install-docs \
  --disable-examples --disable-runtime-cpu-detect

2) From the parent directory, invoke ndk-build:
NDK_PROJECT_PATH=. ndk-build APP_BUILD_SCRIPT=./libvpx/test/android/Android.mk \
  APP_ABI=armeabi-v7a APP_PLATFORM=android-18 APP_OPTIM=release \
  APP_STL=c++_static

Note: Both adb and ndk-build are available at:
  https://developer.android.com/studio#downloads
  https://developer.android.com/ndk/downloads

3) Run get_files.py to download the test files:
python get_files.py -i /path/to/test-data.sha1 -o /path/to/put/files \
  -u https://storage.googleapis.com/downloads.webmproject.org/test_data/libvpx

4) Transfer files to device using adb. Ensure you have proper permissions for
the target

adb push /path/to/test_files /data/local/tmp
adb push /path/to/built_libs /data/local/tmp

NOTE: Built_libs defaults to parent_dir/libs/armeabi-v7a

5) Run tests:
adb shell
(on device)
cd /data/local/tmp
LD_LIBRARY_PATH=. ./vpx_test
