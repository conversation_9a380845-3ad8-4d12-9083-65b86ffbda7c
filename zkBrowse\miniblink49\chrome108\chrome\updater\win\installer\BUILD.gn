# Copyright 2019 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/python.gni")
import("//build/timestamp.gni")
import("//chrome/process_version_rc_template.gni")
import("//testing/test.gni")

source_set("lib") {
  sources = [
    "configuration.cc",
    "configuration.h",
    "exit_code.h",
    "installer.cc",
    "installer.h",
    "installer.rc",
    "installer_constants.cc",
    "installer_constants.h",
    "installer_resource.h",
    "pe_resource.cc",
    "pe_resource.h",
    "string.cc",
    "string.h",
  ]

  deps = [
    "//base",
    "//chrome/installer/util:constants",
    "//chrome/installer/util:metainstaller_utils",
    "//chrome/updater:base",
    "//chrome/updater:branding_header",
    "//chrome/updater:constants_prod",
    "//chrome/updater/win:tag_extractor",
  ]
}

process_version_rc_template("version") {
  template_file = "installer_version.rc.version"
  output = "$root_out_dir/installer_version.rc"
}

# This target creats a list of runtime dependencies for the component
# builds. This list is parsed by the |create_installer_archive| script, the
# DLL paths extracted out from the list, and included in the archive.
updater_runtime_deps = "$target_gen_dir/updater.runtime_deps"
group("updater_runtime_deps") {
  write_runtime_deps = updater_runtime_deps
  data_deps = [ "//chrome/updater/win:updater" ]
}

updater_test_runtime_deps = "$target_gen_dir/updater_test.runtime_deps"
group("updater_test_runtime_deps") {
  write_runtime_deps = updater_test_runtime_deps
  data_deps = [ "//chrome/updater/win:updater_test" ]
}

template("generate_installer") {
  output_dir = invoker.out_dir
  packed_files_rc_file = "$target_gen_dir/$target_name/packed_files.rc"
  archive_name = target_name + "_archive"
  staging_dir = "$target_gen_dir/$target_name"

  action(archive_name) {
    script = "create_installer_archive.py"

    release_file = invoker.release_file

    inputs = [ release_file ]

    outputs = [
      "$output_dir/updater.packed.7z",
      packed_files_rc_file,
    ]

    args = [
      "--build_dir",
      rebase_path(root_out_dir, root_build_dir),
      "--staging_dir",
      rebase_path(staging_dir, root_build_dir),
      "--input_file",
      rebase_path(release_file, root_build_dir),
      "--resource_file_path",
      rebase_path(packed_files_rc_file, root_build_dir),
      "--output_dir",
      rebase_path(output_dir, root_build_dir),
      "--setup_runtime_deps",
      rebase_path(invoker.runtime_deps, root_build_dir),
      "--output_name=updater",
      "--timestamp",
      build_timestamp,

      # Optional argument for verbose archiving output.
      #"--verbose",
    ]

    deps = invoker.archive_deps

    if (is_component_build) {
      args += [ "--component_build=1" ]
    }
  }

  executable(target_name) {
    output_name = invoker.output_name

    sources = [
      "installer_main.cc",
      packed_files_rc_file,
    ]

    configs += [ "//build/config/win:windowed" ]

    libs = [ "setupapi.lib" ]

    deps = [
      ":$archive_name",
      ":lib",
      ":version",
      "//build/win:default_exe_manifest",
      "//chrome/updater:base",
    ]
  }
}

generate_installer("installer") {
  out_dir = root_out_dir
  output_name = "UpdaterSetup"
  archive_deps = [
    ":updater_runtime_deps",
    "//chrome/updater/win:updater",
  ]
  release_file = "updater.release"
  runtime_deps = updater_runtime_deps
}

generate_installer("installer_test") {
  out_dir = root_out_dir + "/update_test_contents"
  output_name = "UpdaterSetup_test"
  archive_deps = [
    ":updater_test_runtime_deps",
    "//chrome/updater/win:updater_test",
  ]
  release_file = "updater_test.release"
  runtime_deps = updater_test_runtime_deps
}

test("installer_unittest") {
  testonly = true

  output_name = "updater_installer_unittest"

  sources = [
    "configuration_unittest.cc",
    "installer_unittest.cc",
    "run_all_unittests.cc",
    "string_unittest.cc",
  ]

  public_deps = [ ":lib" ]
  deps = [
    "//base",
    "//base/test:test_support",
    "//chrome/updater:constants_header",
    "//testing/gtest",
  ]
}
