<!DOCTYPE html>
<script src="../../resources/js-test.js"></script>
<script>
var SVGSMILAnimationInImageRegardlessOfCache = 768; // From UseCounter.h
window.jsTestIsAsync = true;

shouldBeFalse("internals.isUseCounted(document, SVGSMILAnimationInImageRegardlessOfCache)");

var img = new Image();
img.onload = function() {
    shouldBeTrue("internals.isUseCounted(document, SVGSMILAnimationInImageRegardlessOfCache)");
    finishJSTest();
}
img.src = "resources/animated-rect-color.svg";
</script>
</html>