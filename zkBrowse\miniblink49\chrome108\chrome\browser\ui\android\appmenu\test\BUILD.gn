# Copyright 2020 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/android/rules.gni")

android_library("test_support_java") {
  testonly = true

  sources = [
    "java/src/org/chromium/chrome/browser/ui/appmenu/AppMenuTestSupport.java",
    "java/src/org/chromium/chrome/browser/ui/appmenu/TestAppMenuObserver.java",
  ]
  deps = [
    "//base:base_java_test_support",
    "//chrome/browser/ui/android/appmenu:java",
    "//chrome/browser/ui/android/appmenu/internal:java",
    "//ui/android:ui_no_recycler_view_java",
  ]
}
