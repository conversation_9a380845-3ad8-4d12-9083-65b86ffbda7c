[virtual_authenticator.html]
  [Can create an authenticator]
    expected:
      if product == "firefox" or product == "safari" or product == "epiphany" or product == "webkit": FAIL

  [Can add a credential]
    expected:
      if product == "firefox" or product == "safari" or product == "epiphany" or product == "webkit": FAIL

  [Can get the credentials]
    expected:
      if product == "firefox" or product == "safari" or product == "epiphany" or product == "webkit": FAIL

  [Can remove a credential]
    expected:
      if product == "firefox" or product == "safari" or product == "epiphany" or product == "webkit": FAIL

  [Can remove all credentials]
    expected:
      if product == "firefox" or product == "safari" or product == "epiphany" or product == "webkit": FAIL

  [Can set user verified]
    expected:
      if product == "firefox" or product == "safari" or product == "epiphany" or product == "webkit": FAIL

  [Can remove a virtual authenticator]
    expected:
      if product == "firefox" or product == "safari" or product == "epiphany" or product == "webkit": FAIL
