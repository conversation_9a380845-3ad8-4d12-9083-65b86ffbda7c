<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

 <head>

  <title>CSS Writing Modes Test: baseline-alignment of inline replaced element and 'vertical-lr'</title>

  <link rel="author" title="G<PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" />
  <link rel="help" href="http://www.w3.org/TR/css-writing-modes-3/#replaced-baselines" title="4.3 Atomic Inline Baselines" />
  <link rel="match" href="baseline-inline-replaced-002-ref.xht" />

  <meta content="ahem image" name="flags" />
  <meta content="This test checks that the baseline-alignment of an image in the line box of a block with 'writing-mode' set to 'vertical-lr' is 'central' and not 'alphabetic'." name="assert" />

  <link rel="stylesheet" type="text/css" href="/fonts/ahem.css" />
  <style type="text/css"><![CDATA[
  div
    {
      background-color: red;
      font: 96px/1 Ahem; /* computes to 96px/96px */
      height: 99px; /* the height of the cat image */
      writing-mode: vertical-lr;
    }

  img
    {
      vertical-align: baseline;
    }
  /* In vertical writing mode, the central baseline is used as the dominant baseline;
  here, the central baseline is assumed to be halfway between the under and over
  logical margin edges of the inline replaced element box. */

  /* cat.png has an intrinsic height of 99px and an intrinsic width of 98px */
  ]]></style>
 </head>

 <body>

  <p>Test passes if there is a cat and <strong>no red</strong>.</p>

  <div><img src="support/cat.png" alt="Image download support must be enabled" /></div>

 </body>
</html>