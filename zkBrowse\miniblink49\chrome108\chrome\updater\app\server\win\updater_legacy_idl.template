// Copyright 2020 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

import "oaidl.idl";
import "ocidl.idl";

// Backward-compatible, Omaha public interfaces.

// The following are copied from omaha3_idl.idl:
//   enum CurrentState.
//   interface ICurrentState.
//   interface IGoogleUpdate3Web.
//   interface IAppBundleWeb.
//   interface IAppWeb.
//   coclass GoogleUpdate3WebUserClass.

// The normal install flow proceeds from STATE_INIT through
// STATE_INSTALL_COMPLETE in order, skipping states that are not relevant.
// All exceptions and terminal states are start with STATE_INSTALL_COMPLETE.
typedef enum CurrentState {
  STATE_INIT = 1,
  STATE_WAITING_TO_CHECK_FOR_UPDATE = 2,
  STATE_CHECKING_FOR_UPDATE = 3,
  STATE_UPDATE_AVAILABLE = 4,
  STATE_WAITING_TO_DOWNLOAD = 5,
  STATE_RETRYING_DOWNLOAD = 6,
  STATE_DOWNLOADING = 7,
  STATE_DOWNLOAD_COMPLETE = 8,
  STATE_EXTRACTING = 9,
  STATE_APPLYING_DIFFERENTIAL_PATCH = 10,
  // TODO(omaha3): Should we move STATE_DOWNLOAD_COMPLETE here and eliminate
  // STATE_READY_TO_INSTALL?
  STATE_READY_TO_INSTALL = 11,
  STATE_WAITING_TO_INSTALL = 12,
  STATE_INSTALLING = 13,
  STATE_INSTALL_COMPLETE = 14,
  STATE_PAUSED = 15,
  STATE_NO_UPDATE = 16,
  STATE_ERROR = 17,
} CurrentState;

enum AppCommandStatus {
  // The command has never been executed.
  COMMAND_STATUS_INIT = 1,
  // The command is running.
  COMMAND_STATUS_RUNNING = 2,
  // An error occurred while launching or monitoring the command.
  COMMAND_STATUS_ERROR = 3,
  // The command has completed execution.
  COMMAND_STATUS_COMPLETE = 4,
};

[
  object,
  dual,
  uuid(ICURRENTSTATE_IID),
  helpstring("ICurrentState Interface"),
  pointer_default(unique)
]
interface ICurrentState : IDispatch {
  // This interface is exposed to web clients!
  // TODO(omaha3): Update valid comments once we settle on an implementation.

  // A value from the CurrentState enum. This value determines which of the
  // properties below are valid.
  [propget] HRESULT stateValue([out, retval] LONG*);

  // The remaining properties are only valid in the specified states. For all
  // other states, the values are not specified.

  // This property is valid only when stateValue is STATE_UPDATE_AVAILABLE.
  [propget] HRESULT availableVersion([out, retval] BSTR*);

  // The following three properties are only valid when stateValue is
  // STATE_WAITING_TO_DOWNLOAD, STATE_RETRYING_DOWNLOAD, STATE_DOWNLOADING,
  // STATE_DOWNLOAD_COMPLETE, STATE_EXTRACTING,
  // STATE_APPLYING_DIFFERENTIAL_PATCH, or STATE_READY_TO_INSTALL.

  // Bytes downloaded so far.
  [propget] HRESULT bytesDownloaded([out, retval] ULONG*);

  // Total bytes to download.
  [propget] HRESULT totalBytesToDownload([out, retval] ULONG*);

  // Estimated download time remaining in ms. -1 indicates unknown.
  // Progress may not always be available, so clients should handle the -1 case.
  [propget] HRESULT downloadTimeRemainingMs([out, retval] LONG*);

  [propget] HRESULT nextRetryTime([out, retval] ULONGLONG*);

  // TODO(omaha 3): Need some way to indicate reconnecting, retrying, etc.

  // The following two properties are only valid when stateValue is
  // STATE_INSTALLING or STATE_INSTALL_COMPLETE.

  // Current install progress in percentage from 0 to 100. -1 indicates unknown.
  // Progress may not always be available, so clients should handle the -1 case.
  [propget] HRESULT installProgress([out, retval] LONG*);

  // Estimated download time remaining in ms. -1 indicates unknown.
  // Progress may not always be available, so clients should handle the -1 case.
  [propget] HRESULT installTimeRemainingMs([out, retval] LONG*);

  // The following four properties are only valid when stateValue is
  // STATE_ERROR:

  // Returns true if the app has been canceled.
  [propget] HRESULT isCanceled([out, retval] VARIANT_BOOL* is_canceled);

  // Error code.
  [propget] HRESULT errorCode([out, retval] LONG*);

  // Error extra code.
  [propget] HRESULT extraCode1([out, retval] LONG*);

  // The following three properties are only valid when stateValue is
  // STATE_ERROR or STATE_INSTALL_COMPLETE.
  // TODO(omaha3): If STATE_DOWNLOAD_COMPLETE or STATE_READY_TO_INSTALL becomes
  // a terminal state, does it support completion messages?

  // Completion message, localized in the specified language.
  // TODO(omaha3): If we're going to have bundle error messages too, should the
  // language be at bundle level? Should bundle have its own language setter?
  [propget] HRESULT completionMessage([out, retval] BSTR*);

  // Application installer result code. This is to be used as additional
  // information only. Success/failure should be determined using errorCode.
  // This is an error if errorCode is GOOPDATEINSTALL_E_INSTALLER_FAILED.
  [propget] HRESULT installerResultCode([out, retval] LONG*);

  // Application installer extra code.
  [propget] HRESULT installerResultExtraCode1([out, retval] LONG*);

  // A command that needs to be launched by the client after installation.
  [propget] HRESULT postInstallLaunchCommandLine([out, retval] BSTR*);

  // URL to be launched after restarting the browser.
  [propget] HRESULT postInstallUrl([out, retval] BSTR*);

  // Returns a PostInstallAction value indicating the action to be taken by the
  // client after installation.
  [propget] HRESULT postInstallAction([out, retval] LONG*);
}

[
  object,
  dual,
  uuid(IGOOGLEUPDATE3WEB_IID),
  helpstring("IGoogleUpdate3Web Interface"),
  pointer_default(unique)
]
interface IGoogleUpdate3Web : IDispatch {
  HRESULT createAppBundleWeb([out, retval] IDispatch** app_bundle_web);
};

[
  object,
  dual,
  uuid(IAPPBUNDLEWEB_IID),
  helpstring("IAppBundleWeb Interface"),
  pointer_default(unique)
]
interface IAppBundleWeb : IDispatch {
  [id(2)] HRESULT createApp([in] BSTR app_guid,
                            [in] BSTR brand_code,
                            [in] BSTR language,
                            [in] BSTR ap);
  [id(3)] HRESULT createInstalledApp([in] BSTR app_id);
  [id(4)] HRESULT createAllInstalledApps();

  [propget] HRESULT displayLanguage([out, retval] BSTR*);
  [propput] HRESULT displayLanguage([in] BSTR);

  [propput] HRESULT parentHWND([in] ULONG_PTR hwnd);

  [propget] HRESULT length([out, retval] int* index);
  [id(DISPID_VALUE), propget] HRESULT appWeb(
      [in] int index, [out, retval] IDispatch** app_web);

  HRESULT initialize();

  HRESULT checkForUpdate();
  HRESULT download();
  HRESULT install();

  HRESULT pause();
  HRESULT resume();
  HRESULT cancel();

  HRESULT downloadPackage([in] BSTR app_id, [in] BSTR package_name);

  [propget] HRESULT currentState([out, retval] VARIANT* current_state);
};

[
  object,
  dual,
  uuid(IAPPWEB_IID),
  helpstring("IAppWeb Interface"),
  pointer_default(unique)
]
interface IAppWeb : IDispatch {
  [propget] HRESULT appId([out, retval] BSTR*);

  // Returns an IAppVersionWeb IDispatch object.
  [propget] HRESULT currentVersionWeb([out, retval] IDispatch** current);
  [propget] HRESULT nextVersionWeb([out, retval] IDispatch** next);

  // Returns an IAppCommandWeb IDispatch object, or NULL.
  [propget] HRESULT command([in] BSTR command_id,
                            [out, retval] IDispatch** command);

  HRESULT cancel();

  [propget] HRESULT currentState([out, retval] IDispatch** current_state);

  HRESULT launch();
  HRESULT uninstall();

  [propget] HRESULT serverInstallDataIndex([out, retval] BSTR*);
  [propput] HRESULT serverInstallDataIndex([in] BSTR);
};

[
  object,
  dual,
  uuid(IAPPCOMMANDWEB_IID),
  helpstring("IAppCommandWeb Interface"),
  pointer_default(unique)
]
interface IAppCommandWeb : IDispatch {
  // Use values from the AppCommandStatus enum.
  [propget] HRESULT status([out, retval] UINT*);
  [propget] HRESULT exitCode([out, retval] DWORD*);
  [propget] HRESULT output([out, retval] BSTR*);
  HRESULT execute([in, optional] VARIANT substitution1,
                  [in, optional] VARIANT substitution2,
                  [in, optional] VARIANT substitution3,
                  [in, optional] VARIANT substitution4,
                  [in, optional] VARIANT substitution5,
                  [in, optional] VARIANT substitution6,
                  [in, optional] VARIANT substitution7,
                  [in, optional] VARIANT substitution8,
                  [in, optional] VARIANT substitution9);
};

[
  object,
  dual,
  uuid(IPOLICYSTATUS_IID),
  helpstring("IPolicyStatus Interface"),
  pointer_default(unique)
]
interface IPolicyStatus : IDispatch {
  // Global Update Policies

  // Returns the time interval between update checks in minutes.
  // 0 indicates updates are disabled.
  [propget] HRESULT lastCheckPeriodMinutes([out, retval] DWORD* minutes);

  // For domain-joined machines, returns the suppressed times if any, and also
  // checks the current time against the times that updates are suppressed.
  // Updates are suppressed if the current time falls between the start time and
  // the duration.
  // The duration does not account for daylight savings time. For instance, if
  // the start time is 22:00 hours, and with a duration of 8 hours, the updates
  // will be suppressed for 8 hours regardless of whether daylight savings time
  // changes happen in between.
  [propget] HRESULT updatesSuppressedTimes(
      [out] DWORD* start_hour,
      [out] DWORD* start_min,
      [out] DWORD* duration_min,
      [out] VARIANT_BOOL* are_updates_suppressed);

  // Returns the value of the "DownloadPreference" group policy or an
  // empty string if the group policy does not exist, the policy is unknown, or
  // an error happened.
  [propget] HRESULT downloadPreferenceGroupPolicy([out, retval] BSTR* pref);

  // Gets the total disk size limit for cached packages. When this limit is hit,
  // packages may be deleted from oldest until total size is below the limit.
  [propget] HRESULT packageCacheSizeLimitMBytes([out, retval] DWORD* limit);

  // Gets the package cache life limit. If a cached package is older than this
  // limit, it may be deleted.
  [propget] HRESULT packageCacheExpirationTimeDays([out, retval] DWORD* days);

  // Application Update Policies

  // Returns 1 if installation of the specified app is allowed.
  // Otherwise, returns 0.
  [propget] HRESULT effectivePolicyForAppInstalls([in] BSTR app_id,
                                                  [out, retval] DWORD* policy);

  // Returns 1 if updates of the specified app is allowed.
  // Otherwise, returns one of 0 (Disabled), 2 (ManualUpdatesOnly), or
  // 3 (AutomaticUpdatesOnly).
  [propget] HRESULT effectivePolicyForAppUpdates([in] BSTR app_id,
                                                 [out, retval] DWORD* policy);

  // Returns the target version prefix for the app, if the machine is joined to
  // a domain and has the corresponding policy set.
  // Examples:
  // * "" (or not configured): update to latest version available.
  // * "55.": update to any minor version of 55 (e.g. 55.24.34 or 55.60.2).
  // * "55.2.": update to any minor version of 55.2 (e.g. 55.2.34 or 55.2.2).
  // * "55.24.34": update to this specific version only.
  [propget] HRESULT targetVersionPrefix([in] BSTR app_id,
                                        [out, retval] BSTR* prefix);

  // Returns whether the RollbackToTargetVersion policy has been set for the
  // app. Setting RollbackToTargetVersion will result in a version downgrade if
  // the app version on the client is higher than the version on the server.
  // This could happen under circumstances such as:
  // - TargetVersionPrefix is used to pick an older version on the channel.
  // - TargetChannel is used to move the client to a channel with a lower
  //   version (e.g., Dev/Beta to Beta/Stable).
  // - A user somehow installed a newer version on the client.
  // When not set, a client will not receive updates until the app version on
  // the server passes the version on the client.
  [propget] HRESULT isRollbackToTargetVersionAllowed(
      [in] BSTR app_id,
      [out, retval] VARIANT_BOOL* rollback_allowed);
};

// IPolicyStatusValue represents the managed state of a single Google Update
// policy. It contains the current source and value, as well as if any conflicts
// exist with that policy.
[
  object,
  dual,
  uuid(IPOLICYSTATUSVALUE_IID),
  helpstring("IPolicyStatusValue Interface"),
  pointer_default(unique)
]
interface IPolicyStatusValue : IDispatch {
  [propget] HRESULT source([out, retval] BSTR*);
  [propget] HRESULT value([out, retval] BSTR*);
  [propget] HRESULT hasConflict([out, retval] VARIANT_BOOL* has_conflict);
  [propget] HRESULT conflictSource([out, retval] BSTR*);
  [propget] HRESULT conflictValue([out, retval] BSTR*);
}

// IPolicyStatus2 exposes the following:
// * properties for Google Update that includes Global Update state, such as the
//   Version of the Updater, the Time that Updates were checked for last.
// * A way to refresh the latest policies from the DM Server.
// * the managed state of Omaha policies. Each policy returns an
//   IPolicyStatusValue that can be queried for the current source and value, as
//   well as if any conflicts exist with that policy.
//   IPolicyStatusValue is implemented by an object that marshals itself by
//   value. To get the "current" value, the policy needs to be queried fresh.
[
  object,
  dual,
  uuid(IPOLICYSTATUS2_IID),
  helpstring("IPolicyStatus2 Interface"),
  pointer_default(unique)
]
interface IPolicyStatus2 : IDispatch {
  // Global Update Status.

  // Returns the running version of the Updater. For instance, 1.3.35.454.
  [propget] HRESULT updaterVersion([out, retval] BSTR* version);

  // Returns the last time that the Updater successfully checked for updates.
  [propget] HRESULT lastCheckedTime([out, retval] DATE* last_checked);

  // DM policy cache refresh.

  // Gets the latest policies from the DM Server.
  HRESULT refreshPolicies();

  // Global Update Policies

  // Returns the time interval between update checks in minutes.
  // 0 indicates updates are disabled.
  [propget] HRESULT lastCheckPeriodMinutes(
      [out, retval] IPolicyStatusValue** value);

  // For domain-joined machines, returns the suppressed times if any, and also
  // checks the current time against the times that updates are suppressed.
  // Updates are suppressed if the current time falls between the start time and
  // the duration.
  // The duration does not account for daylight savings time. For instance, if
  // the start time is 22:00 hours, and with a duration of 8 hours, the updates
  // will be suppressed for 8 hours regardless of whether daylight savings time
  // changes happen in between.
  [propget] HRESULT updatesSuppressedTimes(
      [out] IPolicyStatusValue** value,
      VARIANT_BOOL* are_updates_suppressed);

  // Returns the value of the "DownloadPreference" group policy or an
  // empty string if the group policy does not exist, the policy is unknown, or
  // an error happened.
  [propget] HRESULT downloadPreferenceGroupPolicy(
      [out, retval] IPolicyStatusValue** value);

  // Gets the total disk size limit for cached packages. When this limit is hit,
  // packages may be deleted from oldest until total size is below the limit.
  [propget] HRESULT packageCacheSizeLimitMBytes(
      [out, retval] IPolicyStatusValue** value);

  // Gets the package cache life limit. If a cached package is older than this
  // limit, it may be deleted.
  [propget] HRESULT packageCacheExpirationTimeDays(
      [out, retval] IPolicyStatusValue** value);

  // Gets the proxy policy values.
  [propget] HRESULT proxyMode([out, retval] IPolicyStatusValue** value);
  [propget] HRESULT proxyPacUrl([out, retval] IPolicyStatusValue** value);
  [propget] HRESULT proxyServer([out, retval] IPolicyStatusValue** value);

  // Application Update Policies

  // Returns 1 if installation of the specified app is allowed.
  // Otherwise, returns 0.
  [propget] HRESULT effectivePolicyForAppInstalls(
      [in] BSTR app_id,
      [out, retval] IPolicyStatusValue** value);

  // Returns 1 if updates of the specified app is allowed.
  // Otherwise, returns one of 0 (Disabled), 2 (ManualUpdatesOnly), or
  // 3 (AutomaticUpdatesOnly).
  [propget] HRESULT effectivePolicyForAppUpdates(
      [in] BSTR app_id,
      [out, retval] IPolicyStatusValue** value);

  // Returns the target version prefix for the app, if the machine is joined to
  // a domain and has the corresponding policy set.
  // Examples:
  // * "" (or not configured): update to latest version available.
  // * "55.": update to any minor version of 55 (e.g. 55.24.34 or 55.60.2).
  // * "55.2.": update to any minor version of 55.2 (e.g. 55.2.34 or 55.2.2).
  // * "55.24.34": update to this specific version only.
  [propget] HRESULT targetVersionPrefix(
      [in] BSTR app_id,
      [out, retval] IPolicyStatusValue** value);

  // Returns whether the RollbackToTargetVersion policy has been set for the
  // app. If RollbackToTargetVersion is set, the TargetVersionPrefix policy
  // governs the version to rollback clients with higher versions to.
  [propget] HRESULT isRollbackToTargetVersionAllowed(
      [in] BSTR app_id, [out, retval] IPolicyStatusValue** value);

  // Returns the target channel for the app, if the machine is joined to a
  // domain and has the corresponding policy set.
  [propget] HRESULT targetChannel([in] BSTR app_id,
                                  [out, retval] IPolicyStatusValue** value);
};

// IPolicyStatus3 exposes everything IPolicyStatus2 does, and in addition,
// exposes the forceInstallApps policy.
[
  object,
  dual,
  uuid(IPOLICYSTATUS3_IID),
  helpstring("IPolicyStatus3 Interface"),
  pointer_default(unique)
]
interface IPolicyStatus3 : IPolicyStatus2 {
  // Global Update Policies

  [propget] HRESULT forceInstallApps([in] VARIANT_BOOL is_machine,
                                     [out, retval] IPolicyStatusValue** value);
};


[
  object,
  oleautomation,
  uuid(IPROCESSLAUNCHER_IID),
  helpstring("Google Update IProcessLauncher Interface"),
  pointer_default(unique)
]
interface IProcessLauncher : IUnknown {
  HRESULT LaunchCmdLine([in, string] const WCHAR* cmd_line);
  HRESULT LaunchBrowser([in] DWORD browser_type,
                        [in, string] const WCHAR* url);
  HRESULT LaunchCmdElevated([in, string] const WCHAR* app_guid,
                            [in, string] const WCHAR* cmd_id,
                            [in] DWORD caller_proc_id,
                            [out] ULONG_PTR* proc_handle);
};

[
  object,
  oleautomation,
  uuid(IPROCESSLAUNCHER2_IID),
  helpstring("Google Update IProcessLauncher2 Interface"),
  pointer_default(unique)
]
interface IProcessLauncher2 : IProcessLauncher {
  // Launches the command line, returning the COM server's process ID and
  // handles to the launched process and its stdout. The caller is responsible
  // for closing the returned handles (by passing DUPLICATE_CLOSE_SOURCE to
  // DuplicateHandle, for instance).
  HRESULT LaunchCmdLineEx([in, string] const WCHAR* cmd_line,
                          [out] DWORD* server_proc_id,
                          [out] ULONG_PTR* proc_handle,
                          [out] ULONG_PTR* stdout_handle);
};

[
  uuid(UPDATER_LEGACY_LIB_UUID),
  version(1.0),
  helpstring("Chromium Updater legacy type library.")
]
library UpdaterLegacyLib {
  importlib("stdole2.tlb");

  [
    uuid(GOOGLEUPDATE3WEBUSERCLASS_CLSID),
    helpstring("GoogleUpdate3WebUserClass Class")
  ]
  coclass GoogleUpdate3WebUserClass
  {
    [default] interface IUnknown;
  }

  [
    uuid(GOOGLEUPDATE3WEBSYSTEMCLASS_CLSID),
    helpstring("GoogleUpdate3WebSystemClass")
  ]
  coclass GoogleUpdate3WebSystemClass {
    [default] interface IUnknown;
  }

  [
    uuid(POLICYSTATUSUSERCLASS_CLSID),
    helpstring("Policy Status for per-user applications.")
  ]
  coclass PolicyStatusUserClass {
    [default] interface IUnknown;
  }

  [
    uuid(POLICYSTATUSSYSTEMCLASS_CLSID),
    helpstring("Policy Status for system applications.")
  ]
  coclass PolicyStatusSystemClass {
    [default] interface IUnknown;
  }

  [
    uuid(PROCESSLAUNCHERCLASS_CLSID),
    helpstring("ProcessLauncherClass Class")
  ]
  coclass ProcessLauncherClass {
    [default] interface IUnknown;
  }

  interface ICurrentState;
  interface IGoogleUpdate3Web;
  interface IAppBundleWeb;
  interface IAppWeb;
  interface IAppCommandWeb;
  interface IPolicyStatus;
  interface IPolicyStatus2;
  interface IPolicyStatus3;
  interface IPolicyStatusValue;
  interface IProcessLauncher;
  interface IProcessLauncher2;
};
