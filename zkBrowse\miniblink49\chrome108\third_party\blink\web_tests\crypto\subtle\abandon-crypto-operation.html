<!DOCTYPE html>
<html>
<head>
<script src="../../resources/js-test.js"></script>
<script src="resources/common.js"></script>
</head>
<body>
<p id="description"></p>
<div id="console"></div>

<script>
    description("Tests abandoning a crypto operation.");

    // This test starts up a couple worker threads, which continuously generate
    // RSA keys. It waits until each of the worker threads have started
    // running, and then exits.
    //
    // The consequence is that once the test finishes, there should be
    // outstanding crypto operations on the worker threads. This will exercise
    // the cancellation/abandonment logic for these Promises.

    jsTestIsAsync = true;

    var allPromises = [];

    var kNumWorkers = 3;

    for (var i = 0; i < kNumWorkers; ++i) {
        allPromises.push(new Promise(function(resolve, reject) {
            var worker = new Worker("resources/worker-infinite-loop-generateKey.js");
            worker.onmessage = function(event)
            {
                debug(event.data);
                resolve();
            };
        }));
    }

    Promise.all(allPromises).then(finishJSTest);
</script>
</body>
</html>
