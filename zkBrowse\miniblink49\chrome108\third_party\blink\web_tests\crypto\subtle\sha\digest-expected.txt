Tests the digest() operation for SHA-*

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".

PASS: SHA-1 of [] should be [da39a3ee5e6b4b0d3255bfef95601890afd80709] and was
PASS: SHA-256 of [] should be [e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855] and was
PASS: SHA-384 of [] should be [38b060a751ac96384cd9327eb1b1e36a21fdb71114be07434c0cc7bf63f6e1da274edebfe76f65fbd51ad2f14898b95b] and was
PASS: SHA-512 of [] should be [cf83e1357eefb8bdf1542850d66d8007d620e4050b5715dc83f4a921d36ce9ce47d0d13c5d85f2b0ff8318d2877eec2f63b931bd47417a81a538327af927da3e] and was
PASS: SHA-1 of [00] should be [5ba93c9db0cff93f52b521d7420e43f6eda2784f] and was
PASS: SHA-256 of [00] should be [6e340b9cffb37a989ca544e6bb780a2c78901d3fb33738768511a30617afa01d] and was
PASS: SHA-384 of [00] should be [bec021b4f368e3069134e012c2b4307083d3a9bdd206e24e5f0d86e13d6636655933ec2b413465966817a9c208a11717] and was
PASS: SHA-512 of [00] should be [b8244d028981d693af7b456af8efa4cad63d282e19ff14942c246e50d9351d22704a802a71c3580b6370de4ceb293c324a8423342557d4e5c38438f0e36910ee] and was
PASS: SHA-1 of [000102030405] should be [868460d98d09d8bbb93d7b6cdd15cc7fbec676b9] and was
PASS: SHA-256 of [000102030405] should be [17e88db187afd62c16e5debf3e6527cd006bc012bc90b51a810cd80c2d511f43] and was
PASS: SHA-384 of [000102030405] should be [79f4738706fce9650ac60266675c3cd07298b09923850d525604d040e6e448adc7dc22780d7e1b95bfeaa86a678e4552] and was
PASS: SHA-512 of [000102030405] should be [2f3831bccc94cf061bcfa5f8c23c1429d26e3bc6b76edad93d9025cb91c903af6cf9c935dc37193c04c2c66e7d9de17c358284418218afea2160147aaa912f4c] and was
PASS successfullyParsed is true

TEST COMPLETE

