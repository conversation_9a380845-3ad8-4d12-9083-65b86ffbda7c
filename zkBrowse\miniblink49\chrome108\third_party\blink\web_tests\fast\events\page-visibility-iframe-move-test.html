<html>
<body onload="startTest()">

<script src="../../resources/js-test.js"></script>

<script>

description("This test checks that an iframe that moves between pages with different visibility will have the correct visibility value.");

var jsTestIsAsync = true;

var window2, iframe;
var numVisibilityChanges = 0;

function window2Loaded() {
    debug("Window 2 Loaded");

    iframe = window2.document.getElementById("iframe");

    shouldBeFalse("window.document.hidden");
    shouldBeFalse("window2.document.hidden");
    shouldBeFalse("iframe.contentDocument.hidden");

    // Change the visibility of the current page to invisible.
    if (window.testRunner) {
        numVisibilityChanges++;
        testRunner.setMainWindowHidden(true);
    }
}

function onVisibilityChange() {
    shouldBeTrue("window.document.hidden");
    shouldBeFalse("window2.document.hidden");
    shouldBeFalse("iframe.contentDocument.hidden");

    window.document.adoptNode(iframe);
    window.document.body.appendChild(iframe);
    debug("Adopted iframe to Window 1");

    shouldBeTrue("window.document.hidden");
    shouldBeFalse("window2.document.hidden");
    shouldBeTrue("iframe.contentDocument.hidden");

    window2.close();

    finishJSTest();
}

function startTest() {
    if (window.testRunner) {
        testRunner.waitUntilDone();
        testRunner.setPopupBlockingEnabled(false);
    }

    debug("Window 1 Loaded");
    document.addEventListener("visibilitychange",
                              onVisibilityChange, false);

    window2 = window.open("resources/page-visibility-iframe-move-new-page.html");
    window2.addEventListener("load", window2Loaded, false);
}

</script>


</body>
</html>
