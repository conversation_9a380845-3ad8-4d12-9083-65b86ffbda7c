<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 2.4 first-letter</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
P:first-letter {color: maroon;}
.two:first-letter {font-size: 200%;}
P.three:first-letter {font-size: 350%;}
</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>P:first-letter {color: maroon;}
.two:first-letter {font-size: 200%;}
P.three:first-letter {font-size: 350%;}

</PRE>
<HR>
<P>
The first letter of this paragraph, and only that one, should be maroon.  If this precise combination does not occur, then the user agent has failed this test.  Remember that in order to ensure a complete test, the paragraph must be displayed on more than one line.
</P>
<P class="two">
The first letter of this paragraph, and only that one, should be a larger font size, as well as maroon.  If this precise combination does not occur, then the user agent has failed this test.  Remember that in order to ensure a complete test, the paragraph must be displayed on more than one line.
</P>
<P class="three">
"We should check for quotation support," it was said.  The first two characters in this paragraph-- a double-quote mark and a capital 'W'-- should be 350% bigger than the rest of the paragraph, and maroon.  Note that this is not required under CSS1, but it is recommended.
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P>
The first letter of this paragraph, and only that one, should be maroon.  If this precise combination does not occur, then the user agent has failed this test.  Remember that in order to ensure a complete test, the paragraph must be displayed on more than one line.
</P>
<P class="two">
The first letter of this paragraph, and only that one, should be a larger font size, as well as maroon.  If this precise combination does not occur, then the user agent has failed this test.  Remember that in order to ensure a complete test, the paragraph must be displayed on more than one line.
</P>
<P class="three">
"We should check for quotation support," it was said.  The first two characters in this paragraph-- a double-quote mark and a capital 'W'-- should be 350% bigger than the rest of the paragraph, and maroon.  Note that this is not required under CSS1, but it is recommended.
</P>
</TD></TR></TABLE></BODY>
</HTML>
