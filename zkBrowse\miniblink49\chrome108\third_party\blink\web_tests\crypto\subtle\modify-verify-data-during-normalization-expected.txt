Tests crypto.subtle.verify() using a BufferSource that is modified during algorithm normalization

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".

Importing RSA public key...

Verifying the signature...
Accessed name property
Corrupting data...
PASS verificationResult is true

Verifying the signature (again)...
PASS verificationResult is false
PASS successfullyParsed is true

TEST COMPLETE

