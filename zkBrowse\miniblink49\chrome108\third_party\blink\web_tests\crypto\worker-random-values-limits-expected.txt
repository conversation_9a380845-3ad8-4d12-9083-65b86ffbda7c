[Worker] Tests the limits of crypto.randomValues.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".

Starting worker: random-values-limits.js
PASS [Worker] 'crypto' in self is true
PASS [Worker] 'getRandomValues' in self.crypto is true
PASS [Worker] crypto.getRand<PERSON><PERSON><PERSON><PERSON>(almostTooLargeArray) did not throw exception.
PASS [Worker] crypto.getRandomV<PERSON>ues(tooLargeArray) threw exception QuotaExceededError: Failed to execute 'getRandomValues' on 'Crypto': The ArrayBufferView's byte length (65537) exceeds the number of bytes of entropy available via this API (65536)..
PASS successfullyParsed is true

TEST COMPLETE

