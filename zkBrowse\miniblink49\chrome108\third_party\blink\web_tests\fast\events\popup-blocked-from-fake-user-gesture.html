<html>
    <head>
        <script src="../../resources/js-test.js"></script>
        <script src="../../resources/testdriver.js"></script>
        <script src="../../resources/testdriver-vendor.js"></script>
    </head>
    <body>
        <button id="test" onclick="clickHandler()">Click Here</button>
        <div id="console"></div>
        <script>
            var stolenEvent;
            var win;
            function clickHandler1()
            {
                stolenEvent = window.event;
                win = window.open("about:blank", "blank");
                shouldBeNonNull("win");
                win.close();

                clickHandler = clickHandler2;
                window.event = stolenEvent;
                button.click();
            }

            function clickHandler2()
            {
                window.event = stolenEvent;
                win = window.open("about:blank", "blank");
                shouldBeNull("win");
                testRunner.notifyDone();
            }

            clickHandler = clickHandler1;

            if (window.testRunner) {
                testRunner.dumpAsText();
                testRunner.waitUntilDone();

                var button = document.getElementById("test");
                test_driver.click(button);
            }
        </script>
    </body>
</html>
