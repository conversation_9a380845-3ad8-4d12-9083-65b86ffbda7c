<html>
<head>
    <style>
        ::-webkit-scrollbar {
            width: 0px;
            height: 0px;
        }
    </style>
    <script>
      window.enablePixelTesting = true;

      function scroll() {
          // The page scale, as set by internals.setPageScaleFactor should not be apparent
          // to javascript. So, we expect scrolling to (100,100) to be page coordinates, rather
          // than device pixels.
          window.scrollTo(100,100);

          shouldBe("window.document.scrollingElement.scrollTop", "100");
          shouldBe("window.document.scrollingElement.scrollLeft", "100");
          shouldBe("window.scrollX", "100");
          shouldBe("window.scrollY", "100");
      }

      function scaleWithEventSender() {
          var scaleFactor = 2.0;
          if (window.internals)
            internals.setPageScaleFactor(scaleFactor);
      }

      function test() {
          scaleWithEventSender();
          scroll();
      }
    </script>
    <script src="../../resources/js-test.js"></script>
</head>
<body style="width:2000px; height:2000px; margin:0px;" onload="test();">
    <div style="left:0; top:0; width:100px; height:100px; position:absolute; background:yellow;"></div>
    <div style="left:100px; top:0; width:100px; height:100px; position:absolute; background:green;"></div>
    <div style="left:200px; top:0; width:100px; height:100px; position:absolute; background:blue;"></div>

    <div style="left:0; top:100px; width:100px; height:100px; position:absolute; background: green;"></div>
    <div style="left:100px; top:100px; width:100px; height:100px; position:absolute; background:blue;"></div>
    <div style="left:200px; top:100px; width:100px; height:100px; position:absolute; background:yellow;"></div>

    <div id="console"></div>
</body>
</html>
