  1) LOAD 4  // Architecture
  2) if A == 0xc000003e; then JMP 3 else JMP 32
  3) LOAD 0  // System call number
  4) if A & 0x40000000; then JMP 32 else JMP 5
  5) if A >= 0x6b; then JMP 6 else JMP 9
  6) if A >= 0x6e; then JMP 7 else JMP 8
  7) if A >= 0x401; then JMP 38 else JMP 37
  8) if A >= 0x6d; then JMP 11 else JMP 37
  9) if A >= 0x69; then JMP 10 else JMP 37
 10) if A >= 0x6a; then JMP 19 else JMP 27
 11) LOAD 20  // Argument 0 (MSB)
 12) if A == 0x0; then JMP 16 else JMP 13
 13) if A == 0xffffffff; then JMP 14 else JMP 32
 14) LOAD 16  // Argument 0 (LSB)
 15) if A & 0x80000000; then JMP 16 else JMP 32
 16) LOAD 16  // Argument 0 (LSB)
 17) A := A & 0xa5
 18) if A == 0xa0; then JMP 36 else JMP 35
 19) LOAD 20  // Argument 0 (MSB)
 20) if A == 0x0; then JMP 24 else JMP 21
 21) if A == 0xffffffff; then JMP 22 else JMP 32
 22) LOAD 16  // Argument 0 (LSB)
 23) if A & 0x80000000; then JMP 24 else JMP 32
 24) LOAD 16  // Argument 0 (LSB)
 25) A := A & 0xf0
 26) if A == 0xf0; then JMP 36 else JMP 35
 27) LOAD 20  // Argument 0 (MSB)
 28) if A == 0x0; then JMP 33 else JMP 29
 29) if A == 0xffffffff; then JMP 30 else JMP 32
 30) LOAD 16  // Argument 0 (LSB)
 31) if A & 0x80000000; then JMP 33 else JMP 32
 32) RET 0x0  // Kill
 33) LOAD 16  // Argument 0 (LSB)
 34) if A & 0xf; then JMP 35 else JMP 36
 35) RET 0x5000d  // errno = 13
 36) RET 0x50016  // errno = 22
 37) RET 0x7fff0000  // Allowed
 38) RET 0x50026  // errno = 38
