{"name": "stylelint-config-standard", "version": "24.0.0", "description": "Standard shareable config for Stylelint", "keywords": ["stylelint", "stylelint-config", "standard"], "repository": "stylelint/stylelint-config-standard", "license": "MIT", "author": "Stylelint", "main": "index.js", "files": ["index.js"], "scripts": {"format": "prettier . --write", "prepare": "husky install", "lint:formatting": "prettier . --check", "lint:js": "eslint . --ignore-path .gitignore", "lint:md": "remark . --quiet --frail --ignore-path .gitignore", "lint": "npm-run-all --parallel lint:*", "release": "np", "test": "jest", "watch": "jest --watch"}, "lint-staged": {"*.js": "eslint --cache --fix", "*.{js,md,yml}": "prettier --write"}, "prettier": "@stylelint/prettier-config", "eslintConfig": {"extends": ["stylelint"], "globals": {"module": true, "require": true}}, "remarkConfig": {"plugins": ["@stylelint/remark-preset"]}, "dependencies": {"stylelint-config-recommended": "^6.0.0"}, "devDependencies": {"@stylelint/prettier-config": "^2.0.0", "@stylelint/remark-preset": "^3.0.0", "eslint": "^8.2.0", "eslint-config-stylelint": "^15.0.0", "husky": "^7.0.4", "jest": "^27.3.1", "lint-staged": "^12.0.2", "np": "^7.5.0", "npm-run-all": "^4.1.5", "prettier": "^2.4.1", "remark-cli": "^10.0.0", "stylelint": "^14.1.0"}, "peerDependencies": {"stylelint": "^14.0.0"}}