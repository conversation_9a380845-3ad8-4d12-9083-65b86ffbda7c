<!DOCTYPE html>
<html>
<head>
    <style>
        div.block { height: 400px; border: 1px solid black; margin:10px; }
    </style>
</head>
<body>
    <div>
        Test how many resize events are emitted during window resize.
    </div>
    <div id=expandingblock>
    </div>
    <pre id="console"></pre>
    <script src="../../resources/js-test.js"></script>
    <script>
        var resizeEventCount = 0;
        window.onresize = function() {
            resizeEventCount++;
            setTimeout(finish, 0);
        }

        if (window.testRunner) {
            testRunner.dumpAsText();
            testRunner.waitUntilDone();
        }
        function resizeWindow() {
            window.resizeBy(10, 10);
        }
        function finish() {
            // one resize event must be emitted.
            shouldBe("resizeEventCount", "1");
            if (window.testRunner)
                testRunner.notifyDone();
        }
        window.onload = resizeWindow;
    </script>
</body>
</html>
