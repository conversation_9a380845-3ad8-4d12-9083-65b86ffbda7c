<html>
    <head>
        <script src="../../resources/testdriver.js"></script>
        <script src="../../resources/testdriver-vendor.js"></script>
        <script>
        if (window.testRunner) {
            testRunner.dumpAsText();
            testRunner.dumpChildFrames();
            testRunner.waitUntilDone();
        }

        function test()
        {
            var myLink = document.getElementById('link');
            var myFrame = window.frames['otherFrame'];
            myLink.onclick = myFrame.handleClick;
            test_driver.click(myLink);
        }

        function log(msg)
        {
            var res = document.getElementById('res');
            res.innerHTML = res.innerHTML + msg + "<br>";
        }
        </script>
    </head>
    <body onload="test()">
        This tests that window.open works across frames.<br>
        To run manually click the link below.<br>
        <a href="resources/greenbox.png" id="link">GreenBox or Success page.</a><br>
        <iframe src="resources/open-window-from-another-frame-otherFrame.html" name="otherFrame"></iframe>
        <div id="res"></div>
    </body>
</html>
