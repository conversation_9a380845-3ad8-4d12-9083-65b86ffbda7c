<!DOCTYPE html>
<script src='../../resources/testharness.js'></script>
<script src='../../resources/testharnessreport.js'></script>
<p>This test ensures drag-n-drop does start when extending an existing selecting with shift + mouse drag, starting over an image.</p>
<span id='span' style='font-size: 30px; padding: 10px;'>Some text with an image <img id='img' src='resources/abe.png' width='50' height='50'> at end.</span>
<div id='log'></div>

<script>
test(function() {
    assert_not_equals(window.testRunner, undefined, 'Requires testRunner.');
    assert_not_equals(window.eventSender, undefined, 'Requires eventSender.');

    var span = document.getElementById('span');
    span.focus();

    var img = document.getElementById('img');

    var dragStartCount = 0;
    document.addEventListener('dragstart', function (event) { dragStartCount++; });

    var y = span.offsetTop + span.offsetHeight / 2;
    eventSender.mouseMoveTo(span.offsetLeft + 5, y);
    eventSender.mouseDown();
    eventSender.leapForward(200);
    eventSender.mouseUp();

    eventSender.mouseMoveTo(span.offsetLeft + span.offsetWidth / 4, y);
    eventSender.mouseDown(0, ['shiftKey']);
    eventSender.leapForward(200);
    eventSender.mouseUp();

    eventSender.mouseMoveTo(img.offsetLeft + img.offsetWidth / 2 , y);
    eventSender.mouseDown(0, ['shiftKey']);
    eventSender.leapForward(200);
    eventSender.mouseMoveTo(img.offsetLeft + img.offsetWidth / 2 , y + 120);
    eventSender.leapForward(200);
    eventSender.mouseUp();

    assert_equals(dragStartCount, 1);
});
</script>
