<!DOCTYPE html>
<html>
<body>
<p>This test ensures selectstart is fired exactly once when selecting text by a mouse drag.
</p><span style='font-size: 50px; padding: 10px;'>hello world
</span><pre><script>

var span = document.getElementsByTagName('span')[0];
span.focus();

var selectStartCount = 0;
span.addEventListener('selectstart', function (event) { selectStartCount++; });

function expect(title, expectedCount, expectedType) {
    document.write(title + ': ');
    var actualSelectionType = window.getSelection().isCollapsed ? 'caret' : 'range';

    if (selectStartCount != expectedCount)
        document.writeln('FAIL - expected ' + expectedCount + ' events but got ' + selectStartCount + ' events');
    else if (actualSelectionType != expectedType)
        document.writeln('FAIL - expected selection to be ' + expectedType + ' but was ' + actualSelectionType);
    else
        document.writeln('PASS');
}

if (window.testRunner && !window.eventSender)
    document.write('This test requires eventSender');
else if (window.testRunner) {
    testRunner.dumpAsText();

    var y = span.offsetTop + span.offsetHeight / 2;
    expect('Initial state', 0, 'caret');
    eventSender.mouseMoveTo(span.offsetLeft + 5, y);
    eventSender.mouseDown();
    expect('Mouse down', 1, 'caret');
    eventSender.leapForward(200);
    eventSender.mouseUp();
    expect('Mouse up', 1, 'caret');

    eventSender.mouseMoveTo(span.offsetLeft + span.offsetWidth - 5, y);
    expect('Moving to the right', 1, 'caret');
    eventSender.mouseDown(0, ['shiftKey']);
    expect('Second mouse down', 2, 'range');
    eventSender.leapForward(200);
    eventSender.mouseUp();
    expect('Second mouse up', 2, 'range');

    document.writeln('Done.');
    span.parentNode.removeChild(span);
}

</script></pre>
</body>
</html>
