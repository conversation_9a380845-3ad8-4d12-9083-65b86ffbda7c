// Copyright 2014 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// This file has been auto-generated by code_generator_v8.py. DO NOT MODIFY!

#include "config.h"
#include "V8EXTShaderTextureLOD.h"

#include "bindings/core/v8/ExceptionState.h"
#include "bindings/core/v8/V8DOMConfiguration.h"
#include "bindings/core/v8/V8GCController.h"
#include "bindings/core/v8/V8ObjectConstructor.h"
#include "core/dom/ContextFeatures.h"
#include "core/dom/Document.h"
#include "core/dom/Element.h"
#include "platform/RuntimeEnabledFeatures.h"
#include "platform/TraceEvent.h"
#include "wtf/GetPtr.h"
#include "wtf/RefPtr.h"

// V8 10.8 compatibility layer
#if V8_MAJOR_VERSION >= 10
#include "../../../../../v8_10_8/v8_compatibility.h"
#endif

namespace blink {

// Suppress warning: global constructors, because struct WrapperTypeInfo is trivial
// and does not depend on another global objects.
#if defined(COMPONENT_BUILD) && defined(WIN32) && COMPILER(CLANG)
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wglobal-constructors"
#endif
const WrapperTypeInfo V8EXTShaderTextureLOD::wrapperTypeInfo = { gin::kEmbedderBlink, V8EXTShaderTextureLOD::domTemplate, V8EXTShaderTextureLOD::refObject, V8EXTShaderTextureLOD::derefObject, V8EXTShaderTextureLOD::trace, 0, V8EXTShaderTextureLOD::visitDOMWrapper, V8EXTShaderTextureLOD::preparePrototypeObject, V8EXTShaderTextureLOD::installConditionallyEnabledProperties, "EXTShaderTextureLOD", 0, WrapperTypeInfo::WrapperTypeObjectPrototype, WrapperTypeInfo::ObjectClassId, WrapperTypeInfo::NotInheritFromEventTarget, WrapperTypeInfo::Dependent, WrapperTypeInfo::WillBeGarbageCollectedObject };
#if defined(COMPONENT_BUILD) && defined(WIN32) && COMPILER(CLANG)
#pragma clang diagnostic pop
#endif

// This static member must be declared by DEFINE_WRAPPERTYPEINFO in EXTShaderTextureLOD.h.
// For details, see the comment of DEFINE_WRAPPERTYPEINFO in
// bindings/core/v8/ScriptWrappable.h.
const WrapperTypeInfo& EXTShaderTextureLOD::s_wrapperTypeInfo = V8EXTShaderTextureLOD::wrapperTypeInfo;

namespace EXTShaderTextureLODV8Internal {

} // namespace EXTShaderTextureLODV8Internal

void V8EXTShaderTextureLOD::visitDOMWrapper(v8::Isolate* isolate, ScriptWrappable* scriptWrappable, const v8::Persistent<v8::Object>& wrapper)
{
    EXTShaderTextureLOD* impl = scriptWrappable->toImpl<EXTShaderTextureLOD>();
    // The canvas() method may return a reference or a pointer.
    if (Node* owner = WTF::getPtr(impl->canvas())) {
        Node* root = V8GCController::opaqueRootForGC(isolate, owner);
#if V8_MAJOR_VERSION >= 10
        v8::IsolateCompat::SetReferenceFromGroup(isolate, v8::UniqueId(reinterpret_cast<intptr_t>(root)), wrapper);
#else
        #if V8_MAJOR_VERSION >= 10`r`n        v8::IsolateCompat::SetReferenceFromGroup(isolate, v8::UniqueId(reinterpret_cast<intptr_t>(root)), wrapper);`r`n#else`r`n        isolate->SetReferenceFromGroup(v8::UniqueId(reinterpret_cast<intptr_t>(root)), wrapper);`r`n#endif
#endif
        return;
    }
}

static void installV8EXTShaderTextureLODTemplate(v8::Local<v8::FunctionTemplate> functionTemplate, v8::Isolate* isolate)
{
    functionTemplate->ReadOnlyPrototype();

    v8::Local<v8::Signature> defaultSignature;
    defaultSignature = V8DOMConfiguration::installDOMClassTemplate(isolate, functionTemplate, "EXTShaderTextureLOD", v8::Local<v8::FunctionTemplate>(), V8EXTShaderTextureLOD::internalFieldCount,
        0, 0,
        0, 0,
        0, 0);
    v8::Local<v8::ObjectTemplate> instanceTemplate = functionTemplate->InstanceTemplate();
    ALLOW_UNUSED_LOCAL(instanceTemplate);
    v8::Local<v8::ObjectTemplate> prototypeTemplate = functionTemplate->PrototypeTemplate();
    ALLOW_UNUSED_LOCAL(prototypeTemplate);

    // Custom toString template
#if V8_MAJOR_VERSION < 7
    functionTemplate->Set(v8AtomicString(isolate, "toString"), V8PerIsolateData::from(isolate)->toStringTemplate());
#endif
}

v8::Local<v8::FunctionTemplate> V8EXTShaderTextureLOD::domTemplate(v8::Isolate* isolate)
{
    return V8DOMConfiguration::domClassTemplate(isolate, const_cast<WrapperTypeInfo*>(&wrapperTypeInfo), installV8EXTShaderTextureLODTemplate);
}

bool V8EXTShaderTextureLOD::hasInstance(v8::Local<v8::Value> v8Value, v8::Isolate* isolate)
{
    return V8PerIsolateData::from(isolate)->hasInstance(&wrapperTypeInfo, v8Value);
}

v8::Local<v8::Object> V8EXTShaderTextureLOD::findInstanceInPrototypeChain(v8::Local<v8::Value> v8Value, v8::Isolate* isolate)
{
    return V8PerIsolateData::from(isolate)->findInstanceInPrototypeChain(&wrapperTypeInfo, v8Value);
}

EXTShaderTextureLOD* V8EXTShaderTextureLOD::toImplWithTypeCheck(v8::Isolate* isolate, v8::Local<v8::Value> value)
{
    return hasInstance(value, isolate) ? toImpl(v8::Local<v8::Object>::Cast(value)) : 0;
}

void V8EXTShaderTextureLOD::refObject(ScriptWrappable* scriptWrappable)
{
#if !ENABLE(OILPAN)
    scriptWrappable->toImpl<EXTShaderTextureLOD>()->ref();
#endif
}

void V8EXTShaderTextureLOD::derefObject(ScriptWrappable* scriptWrappable)
{
#if !ENABLE(OILPAN)
    scriptWrappable->toImpl<EXTShaderTextureLOD>()->deref();
#endif
}

} // namespace blink
