<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

 <head>

  <title>CSS Writing Modes Test: Border conflict resolution - cell wins over row, cell wins over rowgroup and cell wins over table (compound)</title>

  <link rel="author" title="<PERSON><PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" />
  <link rel="help" href="http://www.w3.org/TR/css-writing-modes-3/#logical-directions" title="6.2 Flow-relative Directions" />
  <link rel="help" href="http://www.w3.org/TR/CSS21/tables.html#border-conflict-resolution" title="17.6.2.1 Border conflict resolution" />
  <link rel="match" href="../reference/ref-filled-green-100px-square.xht" />

  <meta content="This test checks that, in border-collapsing model, when 'border-style' values and 'border-width' values are identical, then the 'border-color' set on a cell wins over the 'border-color' set on a row (5th table), the 'border-color' set on a cell wins over the 'border-color' set on a row group (tfoot in 4th table, thead in 3rd table, tbody in 2nd table) and the 'border-color' set on a cell wins over the 'border-color' set on a table element (1st table)." name="assert" />

  <style type="text/css"><![CDATA[
  table
    {
      border-collapse: collapse;
      direction: ltr;
      height: 20px;
      writing-mode: vertical-lr;
    }

  td
    {
      padding: 0px;
    }



  table#first
    {
      border-left: red solid 100px;
    }

  table#first td
    {
      border-left: green solid 100px;
    }



  table#second > tbody
    {
      border-left: red solid 100px;
    }

  table#second td
    {
      border-left: green solid 100px;
    }



  table#third > thead
    {
      border-left: red solid 100px;
    }

  table#third td
    {
      border-left: green solid 100px;
    }



  table#fourth > tfoot
    {
      border-left: red solid 100px;
    }

  table#fourth td
    {
      border-left: green solid 100px;
    }



  table#fifth tr
    {
      border-left: red solid 100px;
    }

  table#fifth td
    {
      border-left: green solid 100px;
    }

  div#reference-overlapped-red
    {
      background-color: red;
      bottom: 100px;
      height: 100px;
      position: relative;
      width: 100px;
      z-index: -1;
    }
  ]]></style>

 </head>

 <body>

  <p>Test passes if there is a filled green square and <strong>no red</strong>.</p>

  <table id="first">

  <colgroup><col></col><col></col></colgroup>

    <tbody>

      <tr>
        <td></td> <td></td>
      </tr>

    </tbody>

  </table>


  <table id="second">

  <colgroup><col></col><col></col></colgroup>

    <tbody>

      <tr>
        <td></td> <td></td>
      </tr>

    </tbody>

  </table>


  <table id="third">

  <colgroup><col></col><col></col></colgroup>

    <thead>

      <tr>
        <td></td> <td></td>
      </tr>

    </thead>

  </table>


  <table id="fourth">

  <colgroup><col></col><col></col></colgroup>

    <tfoot>

      <tr>
        <td></td> <td></td>
      </tr>

    </tfoot>

  </table>



  <table id="fifth">

  <colgroup><col></col><col></col></colgroup>

    <tbody>

      <tr>
        <td></td> <td></td>
      </tr>

    </tbody>

  </table>


  <div id="reference-overlapped-red"></div>

 </body>
</html>
