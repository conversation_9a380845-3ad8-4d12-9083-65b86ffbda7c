<!DOCTYPE html>
<style>
    #scrollable { overflow: scroll; height: 100px; width: 100px; }
    #scrollable div { height: 2000px; }
</style>

<p>Tests that we only fire one scroll event per frame (and by association that it happens at raf time).</p>

<div id="scrollable">
    <div></div>
</div>

<script>
var scrollable = document.getElementById('scrollable');
var output = document.getElementById('output');
var failures = 0;

if (window.testRunner) {
    testRunner.dumpAsText();
    testRunner.waitUntilDone();
}

onload = function() {
    var scrollsSinceLastFrame = 0;
    var remainingTicks = 20;

    scrollable.onscroll = function() {
        ++scrollsSinceLastFrame;
    };

    // FIXME: we need a better way of waiting for layout/repainting to happen
    var timerId = setInterval(function() {
        scrollable.scrollTop += 1;
        if (!--remainingTicks)
            clearInterval(timerId);
    }, 1);

    function raf() {
        if (scrollsSinceLastFrame > 1) {
            document.body.appendChild(document.createTextNode('FAIL'));
            ++failures;
        }
        if (remainingTicks) {
            requestAnimationFrame(raf);
        } else {
            if (!failures)
                document.body.appendChild(document.createTextNode('PASS'));
            if (window.testRunner)
                testRunner.notifyDone();
        }
        scrollsSinceLastFrame = 0;
    }

    requestAnimationFrame(raf);
}
</script>
