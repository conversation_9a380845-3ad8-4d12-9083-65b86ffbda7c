<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Transparent background-image with background-color</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="G<PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-04-21 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <link rel="match" href="background-001-ref.xht" />

        <meta name="flags" content="image" />
        <meta name="assert" content="Background-image set to a transparent image causes the 'background-color' to shine through." />
        <style type="text/css">
            div
            {
                background-image: url("support/transparent_green.png");
                background-color: #008000;
                height: 50px;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is a filled green rectangle across the page.</p>
        <div></div>
    </body>
</html>