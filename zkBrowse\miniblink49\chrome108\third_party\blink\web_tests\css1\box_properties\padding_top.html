<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.5.06 padding-top</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
.zero {background-color: silver; padding-top: 0;}
.one {padding-top: 0.5in; background-color: aqua;}
.two {padding-top: 25px; background-color: aqua;}
.three {padding-top: 5em; background-color: aqua;}
.four {padding-top: 25%; background-color: aqua;}
.five {padding-top: -20px; background-color: aqua;}</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.zero {background-color: silver; padding-top: 0;}
.one {padding-top: 0.5in; background-color: aqua;}
.two {padding-top: 25px; background-color: aqua;}
.three {padding-top: 5em; background-color: aqua;}
.four {padding-top: 25%; background-color: aqua;}
.five {padding-top: -20px; background-color: aqua;}
</PRE>
<HR>
<P class="zero">
This element has a class of zero.
</P>
<P class="one">
This element should have a top padding of half an inch, which will require extra text in order to test.  Both the content background and the padding should be aqua (light blue).
</P>
<P class="two">
This element should have a top padding of 25 pixels, which will require extra text in order to test.  Both the content background and the padding should be aqua (light blue).
</P>
<P class="three">
This element should have a top padding of 5 em, which will require extra text in order to test.  Both the content background and the padding should be aqua (light blue).
</P>
<P class="four">
This element should have a top padding of 25%, which is calculated with respect to the width of the parent element.  Both the content background and the padding should be aqua (light blue).  This will require extra text in order to test.
</P>
<P class="five">
This element should have no top padding, since negative padding values are not allowed.  Both the content background and the normal padding should be aqua (light blue).
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P class="zero">
This element has a class of zero.
</P>
<P class="one">
This element should have a top padding of half an inch, which will require extra text in order to test.  Both the content background and the padding should be aqua (light blue).
</P>
<P class="two">
This element should have a top padding of 25 pixels, which will require extra text in order to test.  Both the content background and the padding should be aqua (light blue).
</P>
<P class="three">
This element should have a top padding of 5 em, which will require extra text in order to test.  Both the content background and the padding should be aqua (light blue).
</P>
<P class="four">
This element should have a top padding of 25%, which is calculated with respect to the width of the parent element.  Both the content background and the padding should be aqua (light blue).  This will require extra text in order to test.
</P>
<P class="five">
This element should have no top padding, since negative padding values are not allowed.  Both the content background and the normal padding should be aqua (light blue).
</P>
</TD></TR></TABLE></BODY>
</HTML>
