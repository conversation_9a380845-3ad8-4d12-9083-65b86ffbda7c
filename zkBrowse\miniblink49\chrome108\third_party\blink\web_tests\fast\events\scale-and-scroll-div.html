<!DOCTYPE html>
<script src='../../resources/testharness.js'></script>
<script src='../../resources/testharnessreport.js'></script>
<script src='../../resources/gesture-util.js'></script>
<style>
    ::-webkit-scrollbar {
        width: 0px;
        height: 0px;
    }
    #bluebox {
      width: 100px;
      height: 100px;
      background: blue;
      padding: 0px;
      margin: 0px;
    }
    #redbox {
      width: 100px;
      height: 100px;
      background: red;
      padding: 0px;
      margin: 0px;
    }
</style>
<script>
  const floatPrecision = 0.1;
  const scrollDistance = 10;

  async function scroll(scaleFactor, msg) {
      const scrollBox = document.getElementById("scrollbox");
      let expectedScrollDistance;

      if (internals.runtimeFlags.fractionalScrollOffsetsEnabled)
          expectedScrollDistance = scrollDistance/scaleFactor;
      else
          expectedScrollDistance = Math.round(scrollDistance/scaleFactor);

      scrollBox.scrollTop = 0;

      let location = { x: 10, y: 100 };
      await smoothScroll(scrollDistance,
                         location.x,
                         location.y,
                         GestureSourceType.TOUCH_INPUT,
                         'down',
                         SPEED_INSTANT);

      assert_approx_equals(scrollBox.scrollTop,
                           expectedScrollDistance,
                           floatPrecision,
                           msg + " - scroll down.");

      scrollBox.scrollTop = 20;

      location = { x: 10, y: 300 };
      await smoothScroll(scrollDistance,
                         location.x,
                         location.y,
                         GestureSourceType.TOUCH_INPUT,
                         'up',
                         SPEED_INSTANT);

      if (internals.runtimeFlags.fractionalScrollOffsetsEnabled)
          expectedScrollDistance = 20 - scrollDistance/scaleFactor;
      else
          expectedScrollDistance = Math.round(20 - scrollDistance/scaleFactor);

      assert_approx_equals(scrollBox.scrollTop,
                           expectedScrollDistance,
                           floatPrecision,
                           msg +  " - scroll up.");
  }

  window.onload = () => {
    promise_test(async () => {
      window.internals.setPageScaleFactor(1.0);
      await scroll(1.0, "Scrolling unscaled div");

      window.internals.setPageScaleFactor(1.5);
      await scroll(1.5, "Scrolling 1.5 scaled div");

      window.internals.setPageScaleFactor(1.63);
      await scroll(1.63, "Scrolling 1.63 scaled div");

      window.internals.setPageScaleFactor(2.0);
      await scroll(2.0, "Scrolling 2.0 scaled div");
    }, "div touch scrolled under pinch-zoom scrolls at correct rate");

  };
</script>
<div id="scrollbox" style="left:0; top:0; width:500px; height:500px; position:absolute; overflow-y: scroll; overflow-x: scroll;">
  <div id="bluebox"></div>
  <div id="redbox"></div>
  <div id="bluebox"></div>
  <div id="redbox"></div>
  <div id="bluebox"></div>
  <div id="redbox"></div>
  <div id="bluebox"></div>
  <div id="redbox"></div>
  <div id="bluebox"></div>
  <div id="redbox"></div>
  <div id="bluebox"></div>
  <div id="redbox"></div>
</div>
