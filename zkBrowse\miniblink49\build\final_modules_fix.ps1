# Final batch fix for all remaining modules V8 bindings
$ModulesDir = "..\gen\blink\bindings\modules\v8"

Write-Host "Final batch fix for modules V8 bindings..." -ForegroundColor Green

# List of remaining files to fix
$files = @(
    "V8ExtSRGB.cpp",
    "V8ExtTextureFilterAnisotropic.cpp", 
    "V8OESStandardDerivatives.cpp",
    "V8OESTextureFloatLinear.cpp",
    "V8OESTextureFloat.cpp",
    "V8OESElementIndexUint.cpp",
    "V8OESTextureHalfFloat.cpp",
    "V8OESTextureHalfFloatLinear.cpp"
)

foreach ($file in $files) {
    $filePath = Join-Path $ModulesDir $file
    
    if (Test-Path $filePath) {
        Write-Host "Processing $file..." -ForegroundColor Yellow
        
        try {
            $content = Get-Content $filePath -Raw
            
            # Add compatibility header if not already present
            if ($content -notmatch "v8_compatibility\.h") {
                $content = $content -replace '(#include "wtf/RefPtr\.h")', '$1

// V8 10.8 compatibility layer
#if V8_MAJOR_VERSION >= 10
#include "../../../../../v8_10_8/v8_compatibility.h"
#endif'
                Write-Host "  - Added compatibility header" -ForegroundColor Green
            }
            
            # Fix SetReferenceFromGroup calls
            if ($content -match 'isolate->SetReferenceFromGroup\(') {
                $content = $content -replace 'isolate->SetReferenceFromGroup\(v8::UniqueId\(reinterpret_cast<intptr_t>\(root\)\), wrapper\);', '#if V8_MAJOR_VERSION >= 10
        v8::IsolateCompat::SetReferenceFromGroup(isolate, v8::UniqueId(reinterpret_cast<intptr_t>(root)), wrapper);
#else
        isolate->SetReferenceFromGroup(v8::UniqueId(reinterpret_cast<intptr_t>(root)), wrapper);
#endif'
                Write-Host "  - Fixed SetReferenceFromGroup call" -ForegroundColor Green
            }
            
            # Write back to file
            Set-Content $filePath $content -NoNewline
            Write-Host "  - File updated successfully" -ForegroundColor Green
            
        } catch {
            Write-Host "  - Error processing file: $_" -ForegroundColor Red
        }
    } else {
        Write-Host "Warning: $file not found" -ForegroundColor Red
    }
    
    Write-Host ""
}

Write-Host "Final batch fix completed!" -ForegroundColor Green
Write-Host "All modules V8 bindings should now be compatible with V8 10.8" -ForegroundColor Green
