<!DOCTYPE html>
<title>Canvas.drawImage with SVG image</title>
<canvas width="300" height="300"></canvas>
<script>
  function createSVGImage() {
      var image = document.createElement('img');
      image.style.width = "5px";
      image.src = "data:image/svg+xml," +
                  "<svg xmlns='http://www.w3.org/2000/svg' width='200' viewBox='0 0 1 1'>" +
                  "<rect width='1' height='1' fill='green'/></svg>";
      image.onload = function() {
          var canvas = document.querySelector('canvas');
          var ctx = canvas.getContext("2d");
          ctx.drawImage(document.querySelector('img'), 0, 0);
          document.body.removeChild(document.querySelector('img'));
      };
      return image;
  }
  document.body.appendChild(createSVGImage());
  document.body.offsetTop; // Force layout

</script>
