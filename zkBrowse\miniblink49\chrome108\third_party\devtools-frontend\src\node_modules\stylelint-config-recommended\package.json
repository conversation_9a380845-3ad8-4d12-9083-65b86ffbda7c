{"name": "stylelint-config-recommended", "version": "6.0.0", "description": "Recommended shareable config for Stylelint", "keywords": ["stylelint", "stylelint-config", "recommended"], "repository": "stylelint/stylelint-config-recommended", "license": "MIT", "author": "Stylelint", "main": "index.js", "files": ["index.js"], "scripts": {"format": "prettier . --write", "prepare": "husky install", "lint:formatting": "prettier . --check", "lint:js": "eslint . --ignore-path .gitignore", "lint:md": "remark . --quiet --frail --ignore-path .gitignore", "lint": "npm-run-all --parallel lint:*", "release": "np", "test": "jest", "watch": "jest --watch"}, "lint-staged": {"*.js": "eslint --cache --fix", "*.{js,md,yml}": "prettier --write"}, "prettier": "@stylelint/prettier-config", "eslintConfig": {"extends": ["stylelint"], "globals": {"module": true, "require": true}}, "remarkConfig": {"plugins": ["@stylelint/remark-preset"]}, "devDependencies": {"@stylelint/prettier-config": "^2.0.0", "@stylelint/remark-preset": "^3.0.0", "eslint": "^7.32.0", "eslint-config-stylelint": "^14.0.0", "husky": "^7.0.2", "jest": "^27.1.0", "lint-staged": "^11.1.2", "np": "^7.5.0", "npm-run-all": "^4.1.5", "prettier": "^2.3.2", "remark-cli": "^10.0.0", "stylelint": "^14.0.0"}, "peerDependencies": {"stylelint": "^14.0.0"}}