<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>inert nodes are uneditable</title>
    <link rel="author" title="Alice Boxhall" href="<EMAIL>">
    <script src="/resources/testharness.js"></script>
    <script src="/resources/testharnessreport.js"></script>
    <script src="/resources/testdriver.js"></script>
    <script src="/resources/testdriver-vendor.js"></script>
  </head>
<body>
<span inert id="not-editable" contenteditable>I'm not editable.</span>
<span id="editable" contenteditable>I'm editable.</span>
<script>
var notEditable = document.querySelector('#not-editable');
var editable = document.querySelector('#editable');

promise_test(async function() {
    notEditable.focus();
    var oldValue = notEditable.textContent;
    assert_equals(oldValue, "I'm not editable.");
    await promise_rejects_js(
        this,
        Error,
        test_driver.send_keys(notEditable, 'a'),
        "send_keys should reject for non-interactive elements");
    assert_equals(notEditable.textContent, oldValue);
}, "Can't edit inert contenteditable");

promise_test(async () => {
    editable.focus();
    var oldValue = editable.textContent;
    assert_equals(oldValue, "I'm editable.");
    await test_driver.send_keys(editable, 'a');
    assert_not_equals(editable.textContent, oldValue);
}, "Can edit non-inert contenteditable");

</script>
</body>
</html>
