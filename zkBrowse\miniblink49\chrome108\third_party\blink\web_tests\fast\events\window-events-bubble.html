<html>
<head>
<script>

if (window.testRunner) {
    testRunner.dumpAsText();
    testRunner.waitUntilDone();
}
function test()
{
    if (window.eventSender) {
        eventSender.mouseMoveTo(52, 52);
        eventSender.mouseDown();
        eventSender.mouseUp();
    }
    window.setTimeout('finish()', 0);
}

function finish()
{
    if (window.testRunner)
        testRunner.notifyDone();
}

window.onclick = function(e) { log("Window.onClick fired.  Test Failed."); };
        
function log(text) {
    document.getElementById("result").innerHTML = text;
}
</script>

</head>
<body onload="test()">
    Test that stopPropagation() will not allow window events to bubble.
    <div id="d1" style="position: absolute; top: 50; left: 50; border: solid thin" onclick="log('stopPropagation called.  Test Passed.'); event.stopPropagation();">
        Clicking on this should not fire window.onclick.  This matches our old behavior and Firefox behavior.
    </div>
        
    <div id="result" style="position: absolute; top: 100; left: 50;"><br></div>
</body>
</html>