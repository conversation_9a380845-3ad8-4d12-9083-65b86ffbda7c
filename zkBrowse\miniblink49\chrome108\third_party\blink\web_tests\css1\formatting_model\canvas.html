<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 4.5 The Canvas</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
HTML {background-color: aqua;}
BODY {background-color: green; background-image: none; margin: 25px;}</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>HTML {background-color: aqua;}
BODY {background-color: green; background-image: none; margin: 25px;}
</PRE>
<HR>
<P>The body of this document should have a green background.  It also has a margin of 25 pixels, so the light blue background set for the HTML element should surround the BODY.  If the BODY content is significantly shorter than the browser's window height, then the bottom border may be larger than 25 pixels.</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P>The body of this document should have a green background.  It also has a margin of 25 pixels, so the light blue background set for the HTML element should surround the BODY.  If the BODY content is significantly shorter than the browser's window height, then the bottom border may be larger than 25 pixels.</P>
</TD></TR></TABLE></BODY>
</HTML>
