<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.5.02 margin-right</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
.one {margin-right: 25px; text-align: right; background-color: aqua;}
.two {margin-right: -10px; background-color: aqua;}</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.one {margin-right: 25px; text-align: right; background-color: aqua;}
.two {margin-right: -10px; background-color: aqua;}
</PRE>
<HR>
<P style="background-color: gray;">
This element is unstyled save for a background color of gray.  However, it contains an <SPAN class="one">inline element of class <TT>one</TT></SPAN>, which should result in 25-pixel right margin only in the <STRONG>last</STRONG> line in which the inline box appears.
</P>
<P style="background-color: gray;">
This element is unstyled save for a background color of gray.  However, it contains an <SPAN class="two">inline element of class <TT>two</TT></SPAN>, which should result in -10px right margin only in the <STRONG>last</STRONG> line in which the inline box appears.
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P style="background-color: gray;">
This element is unstyled save for a background color of gray.  However, it contains an <SPAN class="one">inline element of class <TT>one</TT></SPAN>, which should result in 25-pixel right margin only in the <STRONG>last</STRONG> line in which the inline box appears.
</P>
<P style="background-color: gray;">
This element is unstyled save for a background color of gray.  However, it contains an <SPAN class="two">inline element of class <TT>two</TT></SPAN>, which should result in -10px right margin only in the <STRONG>last</STRONG> line in which the inline box appears.
</P>
</TD></TR></TABLE></BODY>
</HTML>
