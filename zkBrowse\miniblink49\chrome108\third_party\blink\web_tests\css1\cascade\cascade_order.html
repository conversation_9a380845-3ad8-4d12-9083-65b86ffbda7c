<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 3.2 Cascading Order</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<LINK rel="stylesheet" type="text/css" HREF="../resources/linktest.css">
<STYLE type="text/css">
LI {color: purple;}
UL LI {color: blue;}
UL LI LI {color: gray;}
LI.red {color: green;}
UL LI.mar {color: #660000;}
UL LI#gre {color: green;}
.test {color: blue;}
.test {color: purple;}
.one {text-decoration: line-through;}
</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>&lt;LINK rel="stylesheet" type="text/css" HREF="../resources/linktest.css"&gt;
LI {color: purple;}
UL LI {color: blue;}
UL LI LI {color: gray;}
LI.red {color: green;}
UL LI.mar {color: #660000;}
UL LI#gre {color: green;}
.test {color: blue;}
.test {color: purple;}
.one {text-decoration: line-through;}

</PRE>
<HR>
<UL>
<LI>This list item should be blue...
<LI>...and so should this; neither should be purple.
<UL>
<LI>This list item should be gray...
<LI>...as should this....
<LI class="red">...but this one should be green.
</UL>
<LI class="mar">This ought to be dark red...
<LI ID="gre">...this green...
<LI>...and this blue.
</UL>

<P style="color: blue;">
This sentence should be blue (STYLE attr.).
</P>
<P class="test">
This sentence should be purple [<CODE>class="test"</CODE>].
</P>
<P class="one">
This text should be stricken (overriding the imported underline; only works if LINKed sheets are supported).
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><UL>
<LI>This list item should be blue...
<LI>...and so should this; neither should be purple.
<UL>
<LI>This list item should be gray...
<LI>...as should this....
<LI class="red">...but this one should be green.
</UL>
<LI class="mar">This ought to be dark red...
<LI ID="gre">...this green...
<LI>...and this blue.
</UL>

<P style="color: blue;">
This sentence should be blue (STYLE attr.).
</P>
<P class="test">
This sentence should be purple [<CODE>class="test"</CODE>].
</P>
<P class="one">
This text should be stricken (overriding the imported underline; only works if LINKed sheets are supported).
</P>
</TD></TR></TABLE></BODY>
</HTML>
