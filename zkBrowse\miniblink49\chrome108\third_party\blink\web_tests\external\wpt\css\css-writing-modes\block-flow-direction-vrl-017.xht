<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

 <head>

  <title>CSS Writing Modes Test: table-cell and 'vertical-rl' - block flow direction of block-level boxes</title>

  <link rel="author" title="Gérard Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" />
  <link rel="help" href="http://www.w3.org/TR/css-writing-modes-3/#block-flow" title="3.1 Block Flow Direction: the writing-mode property" />
  <link rel="match" href="block-flow-direction-001-ref.xht" />

  <meta content="ahem" name="flags" />
  <meta content="This test checks that a table-cell with its 'writing-mode' set to 'vertical-rl' establishes a block formating context with a right-to-left block flow direction." name="assert" />

  <link rel="stylesheet" type="text/css" href="/fonts/ahem.css" />
  <style type="text/css"><![CDATA[
  body
    {
      color: yellow;
      font: 20px/1 Ahem;
    }

  div#table-cell
    {
      display: table-cell;
      height: 9em;
      writing-mode: vertical-rl;
    }

  div#table-cell > div
    {
      background-color: blue;
      border-bottom: blue solid 1em;
      border-top: blue solid 1em;
    }

  div.right-border
    {
      border-right: blue solid 1em;
    }

  div#left-border
    {
      border-left: blue solid 1em;
    }
  ]]></style>
 </head>

 <body>

  <div id="table-cell">

<!-- The right-most line of right-most "S" --> <div class="right-border">A&nbsp; BBBB</div>

<!-- The 2nd right-most line of right-most "S" --> <div>C&nbsp; D&nbsp; E</div>

<!-- The 3rd right-most line of right-most "S" --> <div>F&nbsp; G&nbsp; H</div>

<!-- The 4th right-most line of right-most "S" --> <div>JJJJ&nbsp; K</div>



<!-- The right-most line of left-most "S" --> <div class="right-border">L&nbsp; MMMM</div>

<!-- The 2nd right-most line of left-most "S" --> <div>Q&nbsp; R&nbsp; S</div>

<!-- The 3rd right-most line of left-most "S" --> <div>T&nbsp; U&nbsp; V</div>

<!-- The 4th right-most line of left-most "S" --> <div>WWWW&nbsp; X</div>



<!-- The right-most line of "A" --> <div class="right-border">YYYYYYY</div>

<!-- The 2nd right-most line of "A" --> <div>Z&nbsp; a&nbsp;&nbsp; </div>

<!-- The 3rd right-most line of "A" --> <div>b&nbsp; c&nbsp;&nbsp; </div>

<!-- The 4th right-most line of "A" --> <div>ddddddd</div>



<!-- The right-most line of "P" --> <div class="right-border">eeee&nbsp;&nbsp; </div>

<!-- The 2nd right-most line of "P" --> <div>f&nbsp; g&nbsp;&nbsp; </div>

<!-- The 3rd right-most line of "P" --> <div>h&nbsp; j&nbsp;&nbsp; </div>

<!-- The 4th right-most line of "P" --> <div id="left-border">kkkkkkk</div>

  </div>

 </body>
</html>