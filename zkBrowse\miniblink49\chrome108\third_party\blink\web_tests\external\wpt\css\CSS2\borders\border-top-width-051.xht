<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Border-top-width using millimeters with a nominal value with a plus sign, +25.4mm</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="<PERSON><PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-06-24 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#propdef-border-top-width" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#border-width-properties" />
        <link rel="match" href="border-bottom-width-006-ref.xht" />

        <meta name="assert" content="The 'border-top-width' property supports a nominal length value in millimeters that has a plus sign before it." />
        <style type="text/css">
            div
            {
                display: inline-block;
                width: 1in;
            }
            #reference
            {
                background-color: black;
                height: 1in;
                margin-left: 5px;
            }
            #test
            {
                border-top-style: solid;
                border-top-width: +25.4mm;
                height: 0;
            }
        </style>
    </head>
    <body>
        <p>Test passes if the 2 filled black squares have the <strong>same height</strong>.</p>
        <div id="test"></div>
        <div id="reference"></div>
    </body>
</html>