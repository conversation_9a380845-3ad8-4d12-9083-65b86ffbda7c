<!DOCTYPE html>
<html>
<body>
<p>This test ensures selectstart is fired when selection is created by arrow keys.</p>
If running this test manually, click on div ("Hello World") element and try to select the text using arrow keys.<br>
Expected result : SelectStart event will fire when user starts extending selection.<br>
<div id="test" contenteditable>Hello World</div>
<script>

var selectStartCount = 0;
var div = document.getElementById('test');
div.addEventListener('selectstart', function (event) { selectStartCount++; });
div.focus();

if (window.testRunner) {
    testRunner.dumpAsText();
    logResult('Initial state', 0);

    eventSender.keyDown("ArrowRight");
    logResult('Check (Right arrow)', 0);

    eventSender.keyDown("ArrowRight", ["shiftKey"]);
    logResult('Check (Right arrow + Shift)', 1);

    if (navigator.platform.indexOf('Mac') == 0)
        eventSender.keyDown("ArrowRight", ["shiftKey"], ["altKey"]);
    else
        eventSender.keyDown("ArrowRight", ["shiftKey"], ["ctrlKey"]);
    logResult('Check (Right arrow + Shift + Control)', 1);

    eventSender.keyDown("End", ["shiftKey"]);
    logResult('Check (End + Shift)', 1);

    eventSender.keyDown("Home", ["shiftKey"]);
    logResult('Check (Home + Shift)', 1);

    eventSender.keyDown("End", ["shiftKey"]);
    logResult('Check (End + Shift)', 1);

    // On Mac, home/end doesn't move caret so manually select " World".
    if (navigator.platform.indexOf('Mac') == 0)
        window.getSelection().setBaseAndExtent(div.firstChild, div.textContent.indexOf('World'), div.firstChild, div.textContent.length);

    eventSender.keyDown("ArrowLeft");
    logResult('Check (Left arrow)', 1);

    if (navigator.platform.indexOf('Mac') == 0)
        eventSender.keyDown("ArrowLeft", ["shiftKey"], ["altKey"]);
    else
        eventSender.keyDown("ArrowLeft", ["shiftKey"], ["ctrlKey"]);
    logResult('Check (LeftArrow + Shift + Control)', 2);

    eventSender.keyDown("ArrowLeft", ["shiftKey"]);
    logResult('Check (Left arrow + Shift)', 2);

    eventSender.keyDown("End", ["shiftKey"]);
    logResult('Check (Home + Shift)', 2);

    eventSender.keyDown("Home", ["ctrlKey"]);
    logResult('Check (Home + Control)', 2);
}

document.write("Done.")

function logResult(title, expectedCount) {
    document.write(title + ': ');
    if (selectStartCount != expectedCount)
        document.write('FAIL - expected ' + expectedCount + ' events but got ' + selectStartCount + ' events');
    else
        document.write('PASS');
    document.write('<br>');
}
</script>
</body>
</html>
