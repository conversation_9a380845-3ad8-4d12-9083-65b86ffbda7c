<!DOCTYPE html>
<script>
function test()
{
    if (window.testRunner) {
        testRunner.dumpAsText();
        testRunner.dumpNavigationPolicy();
        document.querySelector("#link").focus();
        eventSender.keyDown("Enter", ["altKey"]);
    }
}
</script>
<body onload="test()">
<p>Tests that hitting alt-enter on a link opens it in the foreground</p>
<a href="about:blank" id="link">link</a>
</body>
