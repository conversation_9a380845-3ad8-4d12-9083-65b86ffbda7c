<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<script src="../../resources/js-test.js"></script>
</head>
<body>
<script>
description("This test documents the behavior of MouseEvent.offsetX/Y in response to HTMLElement.click().");

var testDiv = document.createElement("div");
testDiv.setAttribute("style", "background: green; width: 100px; height: 100px; position: absolute; top: 100px; left: 100px;");
document.body.appendChild(testDiv);

testDiv.addEventListener("click", function(e) {
    event = e;
    shouldBe("event.offsetX", expectedX);
    shouldBe("event.offsetY", expectedY);
});

debug("Simulated click with .click():");
expectedX = "0";
expectedY = "0";
testDiv.click();

if (!window.eventSender)
    debug("This part of the test requires eventSender!");
else {
    debug("Click with mouse at 0,0:");
    expectedX = "0";
    expectedY = "0";
    eventSender.mouseMoveTo(100, 100);
    eventSender.mouseDown();
    eventSender.mouseUp();

    debug("Click with mouse at 40,50:");
    expectedX = "40";
    expectedY = "50";
    eventSender.mouseMoveTo(140, 150);
    eventSender.mouseDown();
    eventSender.mouseUp();
}

testDiv.remove();

</script>
</body>
</html>
