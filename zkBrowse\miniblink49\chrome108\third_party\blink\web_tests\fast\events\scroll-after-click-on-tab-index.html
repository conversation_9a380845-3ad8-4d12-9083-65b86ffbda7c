<html>
<script src="../../resources/js-test.js"></script>
<script>
window.jsTestIsAsync = true;

function getCenterFor(element)
{
  var rect = element.getBoundingClientRect();
  return { x : parseInt((rect.left + rect.right) / 2) , y : parseInt((rect.top + rect.bottom) / 2)};
}

function runTest()
{
  if (!window.testRunner)
    return;
  if (!window.eventSender)
    return;

  testRunner.waitUntilDone();
  testRunner.dumpAsText();

  alert("Scroll position " + document.scrollingElement.scrollTop);
  center = getCenterFor(document.getElementById("sp"));
  eventSender.mouseMoveTo(center.x, center.y);
  eventSender.mouseDown();
  eventSender.mouseUp();
  eventSender.keyDown("ArrowDown");

  debug("Scroll position is more than 0");
  shouldBecomeEqual("document.scrollingElement.scrollTop > 0", "true", finishJSTest);
}

</script>
<style>
</style>
<body onload="runTest();">
<input type="radio" id="myradio">Click the text below and then try to scroll.<br>
<div id="console"></div>
<br><br><br><br><br><br><br><br><br><br>
<span tabindex=1 id="sp">My span</span>
<br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br>
</body>
</html>

