<!DOCTYPE html>
<script src="../../resources/testdriver.js"></script>
<script src="../../resources/testdriver-vendor.js"></script>
<html>
    <body>
        <p>
            Test that only a single popup is allowed in response to a single
            user action, even if the the user action triggers multiple events.
            The test passes if only one popup is created.
        </p>
        <button id="button" onmousedown="popup1()" onmouseup="popup2()">Click Here</button>
        <div id="console"></div>
        <script>
            function popup1() {
                window.open("about:blank", "window1");
            }

            function popup2() {
                window.open("about:blank", "window2");
                if (window.testRunner) {
                    if (testRunner.windowCount() == windowCount + 1)
                        document.getElementById("console").innerText = "PASSED";
                    else
                        document.getElementById("console").innerText = "FAILED";
                    testRunner.notifyDone();
                }
            }

            if (window.testRunner) {
                testRunner.dumpAsText();
                testRunner.waitUntilDone();
                windowCount = testRunner.windowCount();

                var button = document.getElementById("button");
                test_driver.click(button);
            }
        </script>
    </body>
  </html>
