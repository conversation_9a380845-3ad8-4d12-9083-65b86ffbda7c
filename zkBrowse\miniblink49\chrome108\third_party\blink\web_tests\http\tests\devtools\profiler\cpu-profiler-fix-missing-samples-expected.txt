Tests missing samples are replaced with neighbor stacks.
Profile tree:
(root) id:1 total:16 self:0 depth:-1
  (garbage collector) id:2 total:3.2 self:3.2 depth:0
  (program) id:3 total:3.2 self:3.2 depth:0
  bar id:4 total:6.4 self:3.2 depth:0
    foo id:6 total:3.2 self:3.2 depth:1
  baz id:5 total:3.2 self:3.2 depth:0
raw samples: 3  4  3  4  3  6  2  2  3  6  3  3  6  5  3  6
samples:     3  4  4  4  4  6  2  2  3  6  3  3  6  5  3  6
timestamps: 2  3  4  5  6  7  8  9  10  11  12  13  14  15  16  17  18
forEachFrame iterator structure:
+ 0 (program) 2
- 0 (program) 2 1 1
+ 0 bar 3
  + 1 foo 7
    + 2 (garbage collector) 8
    - 2 (garbage collector) 8 2 2
  - 1 foo 7 3 1
- 0 bar 3 7 4
+ 0 (program) 10
- 0 (program) 10 1 1
+ 0 bar 11
  + 1 foo 11
  - 1 foo 11 1 1
- 0 bar 11 1 0
+ 0 (program) 12
- 0 (program) 12 2 2
+ 0 bar 14
  + 1 foo 14
  - 1 foo 14 1 1
- 0 bar 14 1 0
+ 0 baz 15
- 0 baz 15 1 1
+ 0 (program) 16
- 0 (program) 16 1 1
+ 0 bar 17
  + 1 foo 17
  - 1 foo 17 1 1
- 0 bar 17 1 0

