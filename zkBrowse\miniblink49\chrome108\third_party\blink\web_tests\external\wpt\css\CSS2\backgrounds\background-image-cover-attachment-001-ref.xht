<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

 <head>

  <title>CSS Reftest Reference</title>

  <link rel="author" title="Gérard Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" />

  <style type="text/css"><![CDATA[
  div
  {
  border: black solid medium;
  height: 216px;
  margin: 30px;
  }
  ]]></style>

 </head>

 <body>

  <p>Test passes if the big hollow black rectangle is completely filled with a blue background. There is no blue outside the black border.</p>

  <div><img src="support/blue15x15.png" width="100%" height="100%" alt="Image download support must be enabled" /></div>

 </body>
</html>
