<?xml version="1.0" encoding="utf-8" ?>
<lint>
  <!-- Ignore all lint errors in chrome code. -->
  <issue id="all">
    <ignore regexp="\.\./\.\./base/"/>
    <ignore regexp="\.\./\.\./components/"/>
    <ignore regexp="\.\./\.\./content/"/>
    <ignore regexp="\.\./\.\./device/"/>
    <ignore regexp="\.\./\.\./media/"/>
    <ignore regexp="\.\./\.\./net/"/>
    <ignore regexp="\.\./\.\./services/"/>
    <ignore regexp="\.\./\.\./third_party/"/>
    <ignore regexp="\.\./\.\./ui/"/>
  </issue>
  <!-- The following cast-specific suppressions have been migrated from
       //build/android/lint/suppressions.xml -->
  <issue id="ContentDescription" severity="Error">
    <ignore regexp="chromecast/internal"/>
  </issue>
  <issue id="HandlerLeak">
    <ignore regexp="chromecast/internal"/>
  </issue>
  <issue id="HardcodedText" severity="Error">
    <ignore regexp="chromecast/internal"/>
  </issue>
  <issue id="IconDensities">
    <ignore regexp="chromecast/internal"/>
  </issue>
  <issue id="IconDipSize">
    <ignore regexp="chromecast/internal"/>
  </issue>
  <issue id="IconDuplicates" severity="Error">
    <ignore regexp="chromecast/internal"/>
  </issue>
  <issue id="IconDuplicatesConfig" severity="Error">
    <ignore regexp="chromecast/internal"/>
  </issue>
  <issue id="IconLauncherShape" severity="Error">
    <ignore regexp="chromecast/internal"/>
  </issue>
  <issue id="IconLocation">
    <ignore regexp="chromecast/internal"/>
  </issue>
  <issue id="IconMissingDensityFolder">
    <ignore regexp="chromecast/internal"/>
  </issue>
  <issue id="MissingTranslation">
    <!-- http://crbug.com/450548 -->
    <ignore regexp="chromecast/internal"/>
  </issue>
  <issue id="SpUsage" severity="Error">
    <ignore regexp="chromecast/internal"/>
  </issue>
  <issue id="TextFields" severity="Error">
    <ignore regexp="chromecast/internal"/>
  </issue>
  <issue id="UnusedResources">
    <!-- 1 resource used by android tv to generate MediaShell.apk file -->
    <ignore regexp="chromecast/internal/android/prebuilt/google-play-services-first-party"/>
  </issue>
  <issue id="UnusedResources">
    <ignore regexp="chromecast/browser/android/apk/res"/>
  </issue>
  <issue id="UselessParent">
    <ignore regexp="chromecast/internal"/>
  </issue>
</lint>
