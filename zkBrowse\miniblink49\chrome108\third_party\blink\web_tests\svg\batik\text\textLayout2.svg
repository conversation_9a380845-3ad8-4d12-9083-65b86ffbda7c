<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN"
"http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">

<!--

   Copyright 2001-2002  The Apache Software Foundation 

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

-->
<!-- ========================================================================= -->
<!-- Test description here                                                     -->
<!--                                                                           -->
<!-- <AUTHOR>                                      -->
<!-- @version $Id: textLayout2.svg,v 1.5 2004/08/18 07:12:22 vhardy Exp $                                                             -->
<!-- ========================================================================= -->
<?xml-stylesheet type="text/css" href="../resources/test.css" ?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="450" height="500" viewBox="0 0 450 500">
<title>Text Layout Properties</title>

    <!-- ============================================================= -->
    <!-- Test content                                                  -->
    <!-- ============================================================= -->

    <defs>
     <style type="text/css"><![CDATA[
       .Caption { font-size: 8px; font-family: dialog; font-weight: normal;
                  font-style: normal; stroke: none; stroke-width: 0; 
                  fill: black } ]]>
     </style>

     <style type="text/css"><![CDATA[
       .SerifPlain { font-size: 24px; font-family: Serif; font-weight: normal;
                  font-style: normal; stroke: none; stroke-width: 0; 
                  fill: black } ]]>
     </style>

     <text x="0" y="0" text-anchor="start" id="sample">sample</text>

    </defs>

    <g id="testContent" text-anchor="start" font-size="30" class="SerifPlain">
        <text text-anchor="middle" class="title" x="50%" y="10%" font-size="15" >
            Text Layout Properties (2)</text>

        <text x="50%" y="15%" text-anchor="middle" font-size="10">Baseline Properties</text>

        <g text-anchor="middle">
            <text x="225" y="110" font-size="15"><tspan baseline-shift="super" fill="blue">super</tspan> and <tspan baseline-shift="sub" fill="green">sub</tspan>scripts</text>
            <text x="225" y="135" class="Caption">baseline-shift="super"/baseline-shift="sub"</text>
        </g>

        <g text-anchor="middle">
            <text x="225" y="165" font-size="15"><tspan baseline-shift="+20%" fill="blue">positive</tspan> and <tspan baseline-shift="-20%" fill="green">negative </tspan>baseline-shift</text>
            <text x="225" y="180" class="Caption">baseline-shift="+/-20%"</text>
        </g>
        <text x="50%" y="42%" text-anchor="middle" font-size="10">Multi-line Text Selection</text>

        <text x="50%" y="45%" text-anchor="middle" font-size="7.5" font-style="italic">(Try selecting the following text regions)</text>

        <text text-anchor="start" font-size="15">
	    <tspan x="120" y="50%">Text selections can span multiple</tspan>
          <tspan x="120" y="53%">lines via &lt;tspan&gt; elements.</tspan>
        </text>

        <text text-anchor="start" fill="blue" font-size="20" transform="translate(90, 360)">
          <tspan x="0 10 25 50 75 90 100 90 75 50 25 10 0" 
                 y="0 -25 -40 -50 -40 -25 0 25 40 50 40 25 10 0">Text-circle.</tspan>
        </text>

        <text fill="green" font-size="20" transform="translate(250, 320)"
            x="0 20 40 60 80 80 80 80 80 60 40 20 0 0 0 0" 
            y="0 0 0 0 0 20 40 60 80 80 80 80 80 60 40 20">
            Text-in-a-square
        </text>

        <text font-size="15" x="225" y="450" text-anchor="middle">Text with <tspan rotate="10 -10 10 -10 10 -10 10" fill="red">rotated</tspan> glyphs</text>
        <text font-size="15" x="225" y="465" text-anchor="middle" rotate="15">More rotated glyphs</text>

    </g>

    <!-- ============================================================= -->
    <!-- Batik sample mark                                             -->
    <!-- ============================================================= -->
    <use xlink:href="../resources/batikLogo.svg#Batik_Tag_Box" />
    
</svg>
