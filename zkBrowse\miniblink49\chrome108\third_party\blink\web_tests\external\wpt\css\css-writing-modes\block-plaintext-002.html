<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8"/>
<title>unicode-bidi: div plaintext, ltr</title>

<link rel="author" title="<PERSON>" href='mailto:<EMAIL>'/>
<link rel="help" href='http://www.w3.org/TR/css-writing-modes-3/#text-direction'/>
<link rel="match" href='reference/block-plaintext-002.html'/>
<meta name="assert" content='If unicode-bidi: plaintext is applied to a div element containing mixed direction text, the order of directional runs in the text in that element will be determined by its first strong character.'/>
<style type="text/css">
.test { unicode-bidi: plaintext; }

 /* the following styles are not part of the test */
.test, .ref { font-size: 150%; border: 1px solid orange; margin: 10px; width: 10em; padding: 5px; clear: both; }
input { margin: 5px; }
@font-face {
    font-family: 'ezra_silregular';
    src: url('/fonts/sileot-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;
    }
.test, .ref { font-family: ezra_silregular, serif; }
</style>
</head>
<body>
<p class="instructions" dir="ltr">Test passes if the two boxes are identical.</p>


<!--Notes:
Key to entities used below:
        &#x5d0; ... &#x5d5; - The first six Hebrew letters (strongly RTL).
        &#x202d; - The LRO (left-to-right-override) formatting character.
        &#x202c; - The PDF (pop directional formatting) formatting character; closes LRO.

The punctuation is moved around in the source to make it easier to do visual comparisons when the test is run.
-->


<div dir="rtl">
<div class="test">&gt; a &gt; &#x5d1; &gt; c &gt;</div>

<div class="ref" dir="ltr">&#x202d;&gt; a &gt; &#x5d1; &gt; c &gt;&#x202c;</div>
</div>






</body></html>