# Copyright 2016 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# The seccomp-bpf sandbox is only supported on six architectures
# currently.
# Do not disable seccomp_bpf anywhere without talking to
# <EMAIL>!
use_seccomp_bpf = (is_linux || is_chromeos || is_android) &&
                  (current_cpu == "x86" || current_cpu == "x64" ||
                   current_cpu == "arm" || current_cpu == "arm64" ||
                   current_cpu == "mipsel" || current_cpu == "mips64el")

# SSBD (Speculative Store Bypass Disable) is a mitigation of Spectre Variant 4.
# As Spectre Variant 4 can be mitigated by site isolation, opt-out SSBD on site
# isolation fully applied platform.
disable_seccomp_ssbd = use_seccomp_bpf && !is_android
