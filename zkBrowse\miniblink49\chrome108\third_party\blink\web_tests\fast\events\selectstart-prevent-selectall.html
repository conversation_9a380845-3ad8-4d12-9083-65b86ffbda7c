<!DOCTYPE html>
<html>
<body onselectstart="event.preventDefault();">
<p>This test ensures selectstart event fires when selecting all and script can prevent the selection change.</p>
<section id="test">
<div onselectstart="handlerOnDivWasCalled = true;" contenteditable>hello</div>
<input onselectstart="event.preventDefault();" type="text" value="world">
</section>
<pre><script>

if (window.testRunner) {
    testRunner.dumpAsText();
    testRunner.dumpEditingCallbacks();
}

var handlerOnDivWasCalled = false;
var div = document.getElementsByTagName('div')[0];
div.focus();
window.getSelection().collapse(div.firstChild, 1);
document.execCommand('SelectAll', false, null);
var range = window.getSelection().getRangeAt(0);
document.write('div: ');
if (!handlerOnDivWasCalled)
    document.writeln('FAIL - handler on div was never called');
else if (range.startOffset != 1 || range.endOffset != 1)
    document.writeln('FAIL - selection changed');
else
    document.writeln('PASS');

var input = document.getElementsByTagName('input')[0];
input.focus();
input.selectionStart = 1;
input.selectionEnd = 1;
document.execCommand('SelectAll', false, null);
document.writeln('input: ' + (input.selectionStart == 1 || input.selectionEnd == 1 ? 'PASS' : 'FAIL'));

document.getElementById('test').style.display = 'none';

</script></pre>
</body>
</html>
