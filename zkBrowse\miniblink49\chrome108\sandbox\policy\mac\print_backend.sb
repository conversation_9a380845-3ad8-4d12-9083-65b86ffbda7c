; Copyright 2021 The Chromium Authors
; Use of this source code is governed by a BSD-style license that can be
; found in the LICENSE file.
;
; This is the sandbox configuration file used for safeguarding the print
; backend service which is used for interfacing with operating system print
; drivers.
;

; *** The contents of common.sb are implicitly included here. ***

; Need ~/.cups folder access for cupsEnumDests() to determine the user's
; default printer choice.
;   https://www.cups.org/doc/cupspm.html#cupsEnumDests
;   https://www.cups.org/doc/options.html
(allow file-read-data
  (path (user-homedir-path "/.cups/lpoptions"))
)

; Network socket access.
; Required to establish a connection to CUPS server:
;   https://www.cups.org/doc/cupspm.html#httpConnect2
(allow network-outbound
  (remote tcp)
)
