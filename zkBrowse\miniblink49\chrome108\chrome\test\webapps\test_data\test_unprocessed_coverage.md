# Tests for all platforms.
# This test should generate 4 processed tests:
| MWLC | changes | checks |

# This test should generate 1 processed tests:
| MWLC | changes(<PERSON>) | check_b(<PERSON>, <PERSON>) |

# Tests only for ChromeOS
| C | state_change_a | state_change_b(<PERSON>, <PERSON>) | check_a |
| C | state_change_a(<PERSON>) | state_change_a(<PERSON>) | check_b(<PERSON>, <PERSON>) |

# This test should generate 2 processed tests:
| MWLC | changes(<PERSON>) | check_a(Animal::All) |