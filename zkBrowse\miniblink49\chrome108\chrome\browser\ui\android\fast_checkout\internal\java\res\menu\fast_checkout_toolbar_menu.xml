<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright 2022 The Chromium Authors
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file.
-->
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto" >
    <item
        android:id="@+id/settings_menu_id"
        android:icon="@drawable/settings_cog"
        android:title=""
        app:showAsAction="ifRoom"
        app:iconTint="@macro/default_icon_color" />
</menu>
