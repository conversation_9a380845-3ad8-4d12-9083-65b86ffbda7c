<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Border-top set to inherit, inheriting two values for a shorthand property</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="G<PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-06-24 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#propdef-border-top" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#border-shorthand-properties" />
        <meta name="assert" content="The 'border-top' shorthand property properly accepts 'inherit' as a value and uses its parent's border setting." />
        <style type="text/css">
            #div1
            {
                border-top: dashed blue;
                padding-top: 10px;
            }
            div div
            {
                border-top: inherit;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there are two dashed blue lines.</p>
        <div id="div1">
            <div></div>
        </div>
    </body>
</html>