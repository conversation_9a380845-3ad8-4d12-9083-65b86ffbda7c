<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN"
"http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">

<!--

   Copyright 2001  The Apache Software Foundation 

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

-->
<!-- ========================================================================= -->
<!-- Tests the text decoration property                                        -->
<!--                                                                           -->
<!-- <AUTHOR>                                      -->
<!-- @version $Id: textDecoration.svg,v 1.4 2004/08/18 07:12:21 vhardy Exp $        -->
<!-- ========================================================================= -->
<?xml-stylesheet type="text/css" href="../resources/test.css" ?>  

<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="body" width="450" height="500" viewBox="0 0 450 500">
<title>Text decoration test</title>

    <style type="text/css"><![CDATA[
        
    ]]></style>

    <defs>
            <pattern id="svgPattern" viewBox="0 0 10 10" x="0" y="0" width="5" height="5"
                     patternContentUnits="userSpaceOnUse" patternUnits="userSpaceOnUse">
                <g fill="red" stroke="none">
                    <rect x="0" y="0" width="5" height="5" />
                    <rect x="5" y="5" width="5" height="5" />
                </g>
                <g fill="black" stroke="none">
                    <rect x="0" y="5" width="5" height="5" />
                    <rect x="5" y="0" width="5" height="5" />
                </g>

            </pattern>
    </defs>


    <g id="content">

        <text class="title" x="50%" y="40">Text decoration test</text>

        <g font-size="20">

           <g fill="#6666FF">
                <text x="50" y="100" text-decoration="underline">Underline</text>     
                <text x="170" y="100" text-decoration="overline">Overline</text> 
                <text x="270" y="100" text-decoration="line-through">Line-through</text>
            </g>

		<g fill="none" stroke="#33CC33" stroke-width="0.25">
                <text x="50" y="140" text-decoration="underline">Underline</text>     
                <text x="170" y="140" text-decoration="overline">Overline</text> 
                <text x="270" y="140" text-decoration="line-through">Line-through</text>
            </g>

	      <g fill="url(#svgPattern)">
                <text x="50" y="180" text-decoration="underline">Underline</text>     
                <text x="170" y="180" text-decoration="overline">Overline</text> 
                <text x="270" y="180" text-decoration="line-through">Line-through</text>
            </g>


            <text x="50" y="220" fill="#6666FF" text-decoration="underline overline">Underline and overline</text>  
            <text x="50" y="260" fill="none" stroke="#33CC33" stroke-width="0.25" text-decoration="line-through underline overline">Underline, overline and line-through</text>
            <text x="50" y="300" fill="url(#svgPattern)" text-decoration="underline">Decorations <tspan fill="black" dy="-10">with</tspan><tspan dy="10"> tspan</tspan></text> 
            <text x="50" y="340" fill="#6666FF" fill-opacity="0.5" text-decoration="line-through">Transparent text <tspan fill="#33CC33" fill-opacity="1">with</tspan> tspan</text> 
            <text x="50" y="380" text-decoration="line-through">Tspan <tspan fill="url(#svgPattern)" text-decoration="line-through">with own</tspan> decoration</text>
            <text x="50" y="420" fill="#6666FF" text-decoration="overline">Tspan <tspan fill="#33CC33" text-decoration="none">with no</tspan> decoration</text>
            <text x="50" y="460" fill="#6666FF" text-decoration="line-through">Nested <tspan fill="url(#svgPattern)" text-decoration="underline">tspan <tspan fill="#33CC33" text-decoration="overline">decor</tspan>ations</tspan></text>
 
    </g>
       
    </g>

    <!-- ============================================================= -->
    <!-- Batik sample mark                                             -->
    <!-- ============================================================= -->
    <use xlink:href="../resources/batikLogo.svg#Batik_Tag_Box" />

</svg>

