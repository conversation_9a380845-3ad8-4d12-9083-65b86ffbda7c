<!DOCTYPE html>
<meta charset="utf-8">
<title>TestDriver actions: element position</title>
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<script src="/resources/testdriver.js"></script>
<script src="/resources/testdriver-actions.js"></script>
<script src="/resources/testdriver-vendor.js"></script>

<style>
div#test {
  position: fixed;
  left: -100px;
  top: -25px;
  width: 200px;
  height: 75px;
  background-color:blue;
}
</style>

<div id="test">
</div>

<script>
let events = [];

async_test(t => {
  let test = document.getElementById("test");
  test.addEventListener("click", e => {
    events.push(e.clientX);
    events.push(e.clientY)
  });

  let div = document.getElementById("test");
  let actions = new test_driver.Actions()
    .pointerMove(0, 0, {origin: test})
    .pointerDown()
    .pointerUp()
    .send()
    .then(t.step_func_done(() => assert_array_equals(events, [50, 25])))
    .catch(e => t.step_func(() => assert_unreached("Actions sequence failed " + e)));
});
</script>
