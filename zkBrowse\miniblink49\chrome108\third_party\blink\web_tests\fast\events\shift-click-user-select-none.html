<!DOCTYPE html>
<html>
<head>
<style type="text/css">
.no-select {
    -webkit-user-select: none;
}
</style>
</head>
<body>
<p id="sample" class="text">some text</p>
</body>
<script>
// When Shift+Click in selection range, and make event target to change to
// user-select:none with Mac editing behavior causes crash.
// Note: When we have contents other than "p" element, the crash isn't occurred.
if (window.testRunner)
    testRunner.dumpAsText();

(function() {
    var sample = document.getElementById('sample');
    var selection = window.getSelection();
    selection.selectAllChildren(sample);
    sample.addEventListener('mousedown', function(event) {
        if (event.shiftKey)
            event.currentTarget.classList.add('no-select');
    });

    if (!window.eventSender || !window.internals)
        return;
    internals.settings.setEditingBehavior("mac");
    eventSender.mouseMoveTo(sample.offsetLeft + 5, sample.offsetTop + 5);
    eventSender.mouseDown(0, ['shiftKey']);
    document.body.textContent = 'PASS; not crashed';
})();
</script>
</html>
