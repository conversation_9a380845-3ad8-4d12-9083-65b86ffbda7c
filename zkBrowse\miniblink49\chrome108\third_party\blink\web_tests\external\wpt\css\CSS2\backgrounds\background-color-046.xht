<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background-color set to hex with 3 digits with a blue set to the maximum plus one value of #00g is invalid</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="G<PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2013-04-08 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background-color" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <link rel="match" href="background-color-001-ref.xht" />

        <meta name="flags" content="invalid" />
        <meta name="assert" content="Background-color set to #00g falls back to the initial value." />
        <style type="text/css">
            div
            {
                height: 100px;
                width: 100px;
            }
            #wrapper
            {
                background-color: green;
            }
            #test
            {
                background-color: #00g;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is a <strong>filled green square</strong>.</p>
        <div id="wrapper">
            <div id="test"></div>
        </div>
    </body>
</html>