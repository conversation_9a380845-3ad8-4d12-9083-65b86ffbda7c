<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.3.2 background-color</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
P {background-color: green;}
.one {background-color: lime;}
.two {background-color: transparent;}
</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>P {background-color: green;}
.one {background-color: lime;}
.two {background-color: transparent;}

</PRE>
<HR>
<P>
This element's background should be green.
</P>
<P class="one">
This element's background should be lime (light green).
</P>
<P>
This element's background should be green, and the last word in this sentence should also have a green <SPAN class="two">background</SPAN>.  This is because the background color of the parent element (the paragraph) should "shine through" the SPANned word "sentence," which was set to <CODE>transparent</CODE>.  If the document background is visible, the browser is in error.
</P>
<P class="two">
This element should allow the document background to "shine through."  There should be no green backgrounds here!
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P>
This element's background should be green.
</P>
<P class="one">
This element's background should be lime (light green).
</P>
<P>
This element's background should be green, and the last word in this sentence should also have a green <SPAN class="two">background</SPAN>.  This is because the background color of the parent element (the paragraph) should "shine through" the SPANned word "sentence," which was set to <CODE>transparent</CODE>.  If the document background is visible, the browser is in error.
</P>
<P class="two">
This element should allow the document background to "shine through."  There should be no green backgrounds here!
</P>
</TD></TR></TABLE></BODY>
</HTML>
