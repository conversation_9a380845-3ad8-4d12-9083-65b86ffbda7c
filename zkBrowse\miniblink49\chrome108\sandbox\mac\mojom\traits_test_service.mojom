// Copyright 2017 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

module sandbox.mac.mojom;

import "sandbox/mac/mojom/seatbelt_extension_token.mojom";

// Interface for unittesting StructTraits.
interface TraitsTestService {
  [Sync]
  EchoSeatbeltExtensionToken(SeatbeltExtensionToken in)
      => (SeatbeltExtensionToken out);
};
