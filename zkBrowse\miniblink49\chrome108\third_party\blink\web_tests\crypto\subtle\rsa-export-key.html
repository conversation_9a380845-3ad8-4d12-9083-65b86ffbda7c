<!DOCTYPE html>
<html>
<head>
<script src="../../resources/js-test.js"></script>
<script src="resources/common.js"></script>
</head>
<body>
<p id="description"></p>
<div id="console"></div>

<script>
description("Test exporting an RSA key.");

jsTestIsAsync = true;

var extractable = true;
var nonExtractable = false;

var publicKeyJSON = {
    kty: "RSA",
    alg: "RS256",
    n: "rcCUCv7Oc1HVam1DIhCzqknThWawOp8QLk8Ziy2p10ByjQFCajoFiyuAWl-R1WXZaf4xitLRracT9agpzIzc-MbLSHIGgWQGO21lGiImy5ftZ-D8bHAqRz2y15pzD4c4CEou7XSSLDoRnR0QG5MsDhD6s2gV9mwHkrtkCxtMWdBi-77as8wGmlNRldcOSgZDLK8UnCSgA1OguZ989bFyc8tOOEIb0xUSfPSz3LPSCnyYz68aDjmKVeNH-ig857OScyWbGyEy3Biw64qun3juUlNWsJ3zngkOdteYWytx5Qr4XKNs6R-Myyq72KUp02mJDZiiyiglxML_i3-_CeecCw",
    e: "AQAB"
};

debug("\nImporting a JWK key...");
crypto.subtle.importKey("jwk", publicKeyJSON, {name: "RSASSA-PKCS1-v1_5", hash: {name: "sha-256"}}, extractable, ['verify']).then(function(result) {
    key = result;

    return crypto.subtle.exportKey(null, key);
}).then(failAndFinishJSTest, function(result) {
    logError(result);
    return crypto.subtle.exportKey(undefined, key);
}).then(failAndFinishJSTest, function(result) {
    logError(result);
    return crypto.subtle.exportKey({}, key);
}).then(failAndFinishJSTest, function(result) {
    logError(result);
    return crypto.subtle.exportKey("", key);
}).then(failAndFinishJSTest, function(result) {
    logError(result);
    return crypto.subtle.exportKey("foobar", key);
}).then(failAndFinishJSTest, function(result) {
    logError(result);

    debug("\nExporting the key as JWK...");
    return crypto.subtle.exportKey("jwk", key);
}).then(function(result) {
    exportedJWK = result;

    shouldBe("exportedJWK.kty", "'RSA'");
    shouldBe("exportedJWK.n", "publicKeyJSON.n");
    shouldBe("exportedJWK.e", "publicKeyJSON.e");
    shouldBe("exportedJWK.alg", "'RS256'");
    shouldBe("exportedJWK.ext", "true");
    shouldBe("exportedJWK.use", "undefined");
    shouldBe("exportedJWK.key_ops", "['verify']");
}).then(finishJSTest, failAndFinishJSTest);
</script>

</body>
</html>
