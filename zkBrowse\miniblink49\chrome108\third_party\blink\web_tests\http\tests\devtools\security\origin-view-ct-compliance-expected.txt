Tests that the panel includes Certificate Transparency compliance status

Panel on origin view:
<DIV class=widget vbox security-origin-view slot=insertion-point-main >
    <DIV class=title-section >
        <DIV class=title-section-header role=heading aria-level=1 >
Origin
        </DIV>
        <DIV class=origin-display >
            <SPAN class=security-property security-property-secure >
            </SPAN>
            <SPAN >
                <SPAN class=url-scheme-secure >
https
                </SPAN>
                <SPAN class=url-scheme-separator >
://
                </SPAN>
                <SPAN >
foo.test
                </SPAN>
            </SPAN>
        </DIV>
        <DIV class=view-network-button >
            <BUTTON class=text-button type=button role=link >
View requests in Network Panel
            </BUTTON>
        </DIV>
    </DIV>
    <DIV class=origin-view-section >
        <DIV class=origin-view-section-title role=heading aria-level=2 >
Connection
        </DIV>
        <TABLE class=details-table >
            <TR class=details-table-row >
                <TD >
Protocol
                </TD>
                <TD >
TLS 1.2
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
Key exchange
                </TD>
                <TD >
Key_Exchange
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
Cipher
                </TD>
                <TD >
Cypher with Mac
                </TD>
            </TR>
        </TABLE>
    </DIV>
    <DIV class=origin-view-section >
        <DIV class=origin-view-section-title role=heading aria-level=2 >
Certificate
        </DIV>
        <TABLE class=details-table >
            <TR class=details-table-row >
                <TD >
Subject
                </TD>
                <TD >
foo.test
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
SAN
                </TD>
                <TD >
                    <DIV >
                        <SPAN class=san-entry >
foo.test
                        </SPAN>
                        <SPAN class=san-entry >
*.test
                        </SPAN>
                    </DIV>
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
Valid from
                </TD>
                <TD >
Mon, 20 Mar 2017 08:53:20 GMT
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
Valid until
                </TD>
                <TD >
Wed, 18 May 2033 03:33:20 GMT
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
Issuer
                </TD>
                <TD >
Super CA
                </TD>
            </TR>
            <TR class=details-table-row >
                <TD >
                </TD>
                <TD >
                    <BUTTON class=origin-button text-button type=button role=button >
Open full certificate details
                    </BUTTON>
                </TD>
            </TR>
        </TABLE>
    </DIV>
    <DIV class=origin-view-section >
        <DIV class=origin-view-section-title role=heading aria-level=2 >
Certificate Transparency
        </DIV>
        <TABLE class=details-table sct-summary >
        </TABLE>
        <DIV class=sct-details hidden >
        </DIV>
        <DIV class=origin-view-section-notes >
This request complies with Chrome's Certificate Transparency policy.
        </DIV>
    </DIV>
    <DIV class=origin-view-section origin-view-notes >
        <DIV >
The security details above are from the first inspected response.
        </DIV>
    </DIV>
</DIV>

