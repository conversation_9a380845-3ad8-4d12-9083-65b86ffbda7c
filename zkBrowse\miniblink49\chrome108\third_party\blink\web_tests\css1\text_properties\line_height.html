<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.4.8 line-height</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
.one {line-height: 0.5in; font-size: 12px;}
.two {line-height: 2cm; font-size: 12px;}
.three {line-height: 20mm; font-size: 12px;}
.four {line-height: 24pt; font-size: 12px;}
.five {line-height: 2pc; font-size: 12px;}
.six {line-height: 2em; font-size: 12px;}
.seven {line-height: 3ex; font-size: 12px;}
.eight {line-height: 200%; font-size: 12px;}
.nine {line-height: 2; font-size: 12px;}
.ten {line-height: 50px; font-size: 12px;}
.eleven {line-height: -1em; font-size: 12px;}
TABLE .ten {line-height: normal; font-size: 12px;}
DIV {background-color: silver;}
SPAN.color {background-color: silver;}</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.one {line-height: 0.5in; font-size: 12px;}
.two {line-height: 2cm; font-size: 12px;}
.three {line-height: 20mm; font-size: 12px;}
.four {line-height: 24pt; font-size: 12px;}
.five {line-height: 2pc; font-size: 12px;}
.six {line-height: 2em; font-size: 12px;}
.seven {line-height: 3ex; font-size: 12px;}
.eight {line-height: 200%; font-size: 12px;}
.nine {line-height: 2; font-size: 12px;}
.ten {line-height: 50px; font-size: 12px;}
.eleven {line-height: -1em; font-size: 12px;}
TABLE .ten {line-height: normal; font-size: 12px;}
DIV {background-color: silver;}
SPAN.color {background-color: silver;}
</PRE>
<HR>
<P class="one">
This sentence should have a line-height of half an inch, which should cause extra spacing between the lines.
</P>
<P class="two">
This sentence should have a line-height of two centimeters, which should cause extra spacing between the lines.
</P>
<P class="three">
This sentence should have a line-height of twenty millimeters, which should cause extra spacing between the lines.
</P>
<P class="four">
This sentence should have a line-height of twenty-four points, which should cause extra spacing between the lines.
</P>
<P class="five">
This sentence should have a line-height of two picas, which should cause extra spacing between the lines.
</P>
<P class="six">
This sentence should have a line-height of two em, which should cause extra spacing between the lines.
</P>
<P class="seven">
This sentence should have a line-height of three ex, which should cause extra spacing between the lines.
</P>
<P class="eight">
This sentence should have a line-height of twice the font size, which should cause extra spacing between the lines.
</P>
<DIV class="eight">
This first part of the DIV should have a line-height of twice the font size, which should cause extra spacing between the lines.
<P style="font-size: 200%;">
This sentence should have a line-height of twice the DIV's font size, or 28px; this should not cause extra spacing between the lines, since the line-height and font-size should have the same value.
</P>
This second part of the DIV should have a line-height of twice the font size, which should cause extra spacing between the lines.
</DIV>
<P class="nine">
This sentence should have a line-height of twice the font size, which should cause extra spacing between the lines.
</P>
<DIV class="nine">
This first part of the DIV should have a line-height of twice the font size, which should cause extra spacing between the lines.
<P style="font-size: 200%;">
This sentence should have a line-height of twice the font size, which is 200% normal thanks to an inline style; this should cause extra spacing between the lines, as the font-size will be 28px and the line-height will be 56px.
</P>
This second part of the DIV should have a line-height of twice the font size, which should cause extra spacing between the lines.
</DIV>
<P class="ten">
This paragraph should have a line-height of 50 pixels in the first section, which should cause extra spacing between the lines.  In the second section (within the table) its line-height should be normal.
</P>
<P class="eleven">
This sentence should have a normal line-height, because negative values are not permitted for this property.
</P>
<P class="two">
<SPAN class="color">This sentence should have a line-height of two centimeters, which should cause extra spacing between the lines. The text has a background color of silver, but no padding or border. The background color has been set on an inline element and should therefore only cover the text, not the interline spacing.</SPAN>
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P class="one">
This sentence should have a line-height of half an inch, which should cause extra spacing between the lines.
</P>
<P class="two">
This sentence should have a line-height of two centimeters, which should cause extra spacing between the lines.
</P>
<P class="three">
This sentence should have a line-height of twenty millimeters, which should cause extra spacing between the lines.
</P>
<P class="four">
This sentence should have a line-height of twenty-four points, which should cause extra spacing between the lines.
</P>
<P class="five">
This sentence should have a line-height of two picas, which should cause extra spacing between the lines.
</P>
<P class="six">
This sentence should have a line-height of two em, which should cause extra spacing between the lines.
</P>
<P class="seven">
This sentence should have a line-height of three ex, which should cause extra spacing between the lines.
</P>
<P class="eight">
This sentence should have a line-height of twice the font size, which should cause extra spacing between the lines.
</P>
<DIV class="eight">
This first part of the DIV should have a line-height of twice the font size, which should cause extra spacing between the lines.
<P style="font-size: 200%;">
This sentence should have a line-height of twice the DIV's font size, or 28px; this should not cause extra spacing between the lines, since the line-height and font-size should have the same value.
</P>
This second part of the DIV should have a line-height of twice the font size, which should cause extra spacing between the lines.
</DIV>
<P class="nine">
This sentence should have a line-height of twice the font size, which should cause extra spacing between the lines.
</P>
<DIV class="nine">
This first part of the DIV should have a line-height of twice the font size, which should cause extra spacing between the lines.
<P style="font-size: 200%;">
This sentence should have a line-height of twice the font size, which is 200% normal thanks to an inline style; this should cause extra spacing between the lines, as the font-size will be 28px and the line-height will be 56px.
</P>
This second part of the DIV should have a line-height of twice the font size, which should cause extra spacing between the lines.
</DIV>
<P class="ten">
This paragraph should have a line-height of 50 pixels in the first section, which should cause extra spacing between the lines.  In the second section (within the table) its line-height should be normal.
</P>
<P class="eleven">
This sentence should have a normal line-height, because negative values are not permitted for this property.
</P>
<P class="two">
<SPAN class="color">This sentence should have a line-height of two centimeters, which should cause extra spacing between the lines. The text has a background color of silver, but no padding or border. The background color has been set on an inline element and should therefore only cover the text, not the interline spacing.</SPAN>
</P>
</TD></TR></TABLE></BODY>
</HTML>
