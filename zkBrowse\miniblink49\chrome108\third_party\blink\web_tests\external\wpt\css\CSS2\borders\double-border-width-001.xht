<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Double lines border style does not change border width</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="Gérard Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-06-26 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#border-style-properties" />
        <meta name="assert" content="The 'double' 'border-style' does not change the size of the border width. The sum of the two lines and the space need to equal the border width." />
        <style type="text/css">
            div
            {
                position: relative;
                width: 1in;
            }
            #test
            {
                border-top-style: double;
                border-top-width: 1in;
            }
            #reference
            {
                background: blue;
                height: 1in;
                left: 1.1in;
                position: absolute;
                top: 0;
            }
        </style>
    </head>
    <body>
        <p>Test passes if the top edge of the top-most black box is aligned with the top of the blue box and the bottom edge of the bottom-most black box is aligned with the bottom edge of the blue box.</p>
        <div>
            <div id="test"></div>
            <div id="reference"></div>
        </div>
    </body>
</html>