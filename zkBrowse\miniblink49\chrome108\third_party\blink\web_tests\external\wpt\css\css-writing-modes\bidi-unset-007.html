<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8"/>
<title>direction/unicode-bidi: element isolation and unicode-bidi unset, rtl + number</title>

<link rel="author" title="<PERSON>" href='mailto:<EMAIL>'/>
<link rel="help" href='http://www.w3.org/TR/css-writing-modes-3/#text-direction'/>
<link rel="match" href='reference/bidi-normal-007.html'/>
<meta name="assert" content='If direction is set but unicode-bidi is not set on an inline element, the text in that element will NOT be directionally isolated from surrounding text.'/>
<style type="text/css">
.test span { direction: rtl; }

 /* the following styles are not part of the test */
.test, .ref { font-size: 150%; border: 1px solid orange; margin: 10px; width: 10em; padding: 5px; clear: both; }
input { margin: 5px; }
@font-face {
    font-family: 'ezra_silregular';
    src: url('/fonts/sileot-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;
    }
.test, .ref { font-family: ezra_silregular, serif; }
</style>
</head>
<body>
<p class="instructions" dir="ltr">Test passes if the two boxes are identical.</p>


<!--Notes:
Key to entities used below:
        &#x5d0; ... &#x5d5; - The first six Hebrew letters (strongly RTL).
        &#x202d; - The LRO (left-to-right-override) formatting character.
        &#x202c; - The PDF (pop directional formatting) formatting character; closes LRO.
-->



<div class="test" dir="ltr"><span>&#x5d0;</span> 3</div>


<div class="ref" dir="ltr">&#x202d;3 &#x5d0;&#x202c;</div>






</body></html>