<!DOCTYPE html>
<title>Zoomed SVG and raster image in list-style-image</title>
<script src="../../resources/testharness.js"></script>
<script src="../../resources/testharnessreport.js"></script>
<style>
  ul { font-family: Ahem; }
  ul li {
      font-size: 250px;
      color: green;
      list-style-position: inside;
      line-height: 1em;
  }
  ul li.svg {
      list-style-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'><rect width='1' height='1' fill='green'/></svg>");
  }
  ul li.png {
      list-style-image: url("../filters/resources/green.png");
  }
</style>
<ul><li class="svg"><li class="png"></ul>
<script>
  var ul = document.querySelector('ul');
  [ 2, 3, 4, 5, 0.5, 0.2, 1].forEach(function(zoom) {
      test(function() {
          document.body.style.zoom = zoom;
          assert_approx_equals(ul.getBoundingClientRect().height, 500, 0.5);
      }, 'Zoom to ' + zoom + " and list height should be equal to line-height");
  });
</script>
