// This "done" function is defined in v8-cache-iframe.html
done();

// So sorry about this waste of bytes:
// Filler comment, to trigger code caching heuristic (script > 1K.)
// Filler comment, to trigger code caching heuristic (script > 1K.)
// Filler comment, to trigger code caching heuristic (script > 1K.)
// Filler comment, to trigger code caching heuristic (script > 1K.)
// Filler comment, to trigger code caching heuristic (script > 1K.)
// Filler comment, to trigger code caching heuristic (script > 1K.)
// Filler comment, to trigger code caching heuristic (script > 1K.)
// Filler comment, to trigger code caching heuristic (script > 1K.)
// Filler comment, to trigger code caching heuristic (script > 1K.)
// Filler comment, to trigger code caching heuristic (script > 1K.)
// Filler comment, to trigger code caching heuristic (script > 1K.)
// Filler comment, to trigger code caching heuristic (script > 1K.)
// Filler comment, to trigger code caching heuristic (script > 1K.)
// Filler comment, to trigger code caching heuristic (script > 1K.)
