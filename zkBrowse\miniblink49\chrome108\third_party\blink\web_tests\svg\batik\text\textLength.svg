<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 20010904//EN" 
"http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">

<!--

   Copyright 2002  The Apache Software Foundation 

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

-->
<!-- ====================================================================== -->
<!-- Tests text-anchor on tspan elements                                    -->
<!--                                                                        -->
<!-- <AUTHOR>                                             -->
<!-- @version $Id: textLength.svg,v 1.4 2004/08/18 07:12:22 vhardy Exp $    -->
<!-- ====================================================================== -->
<?xml-stylesheet type="text/css" href="../resources/test.css" ?>  

<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="450" height="500" viewBox="0 0 450 500">

    <text class="title" x="50%" y="20">textLength with Anchor</text>


   <defs>
      <g id="fill-rgn">
         <rect x="200" y="5" width="50" height="20"
                 fill="#DDE8FF" stroke="none"/>
         <line x1="225" y1="3" x2="225" y2="27" stroke="red"/>
      </g>

      <g id="bg1">
         <rect x="50"  y="0" width ="350" height="45"
               fill="lightGrey" stroke="black"/>
         <use xlink:href="#fill-rgn"/>
      </g>

      <g id="bg2">
         <rect x="50"  y="0" width ="350" height="45"
               fill="white" stroke="black"/>
         <use xlink:href="#fill-rgn"/>
      </g>
   </defs>
   
   <g font-size="12" >
      <!-- no lengthadjust -->
      <g transform="translate(0,  30)">
              <use xlink:href="#bg1"/>
              <text x="225" y="19" text-anchor="middle">xml-batik</text>
              <text x="225" y="40" text-anchor="middle">No textLength</text>
      </g>
      
      <!-- With lengthAdjust spacingAndGlyphs -->
      <g transform="translate(0, 75)">
              <use xlink:href="#bg2"/>
              <text x="225" y="19" text-anchor="middle" textLength="50"
                      lengthAdjust="spacingAndGlyphs">B</text>
              <text x="225" y="40" text-anchor="middle">textLength="50" lengthAdjust="spacingAndGlyphs"</text>
      </g>
      
      <!-- with lengthAdjust spacing -->
      <g transform="translate(0, 120)">
              <use xlink:href="#bg1"/>
              <text x="225" y="19" text-anchor="middle" textLength="50"
                      lengthAdjust="spacing">B</text>
              <text x="225" y="40" text-anchor="middle">textLength="50" lengthAdjust="spacing"</text>
      </g>
      
      <!-- with lengthAdjust spacing -->
      <g transform="translate(0, 165)">
              <use xlink:href="#bg2"/>
              <text x="225" y="19" text-anchor="middle" textLength="50"
                      lengthAdjust="spacingAndGlyphs">Batik</text>
              <text x="225" y="40" text-anchor="middle">textLength="50" lengthAdjust="spacingAndGlyphs"</text>
      </g>
      
      <!-- with lengthAdjust spacing -->
      <g transform="translate(0, 210)">
              <use xlink:href="#bg1"/>
              <text x="225" y="19" text-anchor="middle" textLength="50"
                      lengthAdjust="spacing">Batik</text>
              <text x="225" y="40" text-anchor="middle">textLength="50" lengthAdjust="spacing"</text>
      </g>
      
      <!-- large number -->
      <g transform="translate(0, 255)">
              <use xlink:href="#bg2"/>
              <text x="225" y="19" text-anchor="middle" textLength="50"
                      lengthAdjust="spacingAndGlyphs">Apache Batik</text>
              <text x="225" y="40" text-anchor="middle">textLength="50" lengthAdjust="spacingAndGlyphs" (shrinking)</text>
      </g>
      
      <!-- large number, no anchor -->
      <g transform="translate(0, 300)">
              <use xlink:href="#bg1"/>
              <text x="200" y="19" textLength="50"
                      lengthAdjust="spacingAndGlyphs">Apache Batik</text>
              <text x="225" y="40" text-anchor="middle">textLength="50" lengthAdjust="spacingAndGlyphs" (no anchor)</text>
      </g>
      
      <!-- large number, tspan -->
      <g transform="translate(0, 345)">
              <use xlink:href="#bg2"/>
              <text x="225" y="19" text-anchor="middle" textLength="50" 
                    lengthAdjust="spacingAndGlyphs">Apache <tspan fill="red">Batik</tspan>!!</text>
              <text x="225" y="40" text-anchor="middle">textLength="50" lengthAdjust="spacingAndGlyphs" (tspan)</text>
      </g>

      <!-- Big Font Squeezed alot -->
      <g transform="translate(0, 390)">
         <rect x="50"  y="0" width ="350" height="95"
               fill="lightGrey" stroke="black"/>
         <rect x="112.5" y="5" width="25" height="80"
                 fill="#DDE8FF" stroke="none"/>
         <line x1="125" y1="3" x2="125" y2="87" stroke="red"/>
         <text x="125" y="79" text-anchor="middle" textLength="25" 
               lengthAdjust="spacingAndGlyphs"
               font-size="100">B a t i k</text>
         <text x="160" y="40">lengthAdjust="spacingAndGlyphs"
          <tspan x="160" dy="1.2em">textLength="25"</tspan>
          <tspan x="160" dy="1.2em">font-size="100"</tspan></text>
      </g>
   </g>

   <!-- ============================================================= -->
   <!-- Batik sample mark                                             -->
   <!-- ============================================================= -->
   <use xlink:href="../resources/batikLogo.svg#Batik_Tag_Box" />
</svg>
