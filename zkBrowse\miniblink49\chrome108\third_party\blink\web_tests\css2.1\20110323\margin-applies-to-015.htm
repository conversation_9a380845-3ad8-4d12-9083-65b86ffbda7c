<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN">
<html>
    <head>
 <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
        <title>CSS Test: Margin applied to element with display table-caption</title>
        <link rel="author" href="http://www.microsoft.com/" title="Microsoft">
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#propdef-margin">
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#margin-properties">
        <meta content="" name="flags">
        <meta content="The 'Margin' property applies to elements with a display of table-caption." name="assert">
        <style type="text/css">
            #wrapper
            {
                border: 10px solid blue;
                position: absolute;
            }
            #test
            {
                border: 10px solid orange;
                display: table-caption;
                height: 200px;
                margin: 50px;
            }
            #table
            {
                border-collapse: collapse;
                display: table;
                width: 320px;
            }
            #row
            {
                display: table-row;
            }
            #cell
            {
                display: table-cell;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is space between the blue and orange lines below.</p>
        <div id="wrapper">
            <div id="table">
                <div id="test"></div>
                <div id="row">
                    <div id="cell"></div>
                </div>
            </div>
        </div>
    </body>
</html>