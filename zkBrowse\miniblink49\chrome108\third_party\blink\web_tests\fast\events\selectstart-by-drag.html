<!DOCTYPE html>
<html>
<body>
<p>This test ensures selectstart is fired exactly once when selecting text by a mouse drag.
</p><span style='font-size: 50px; padding: 10px;' contenteditable>hello
</span><pre><script>

var span = document.getElementsByTagName('span')[0];
span.focus();

var selectStartCount = 0;
span.addEventListener('selectstart', function (event) { selectStartCount++; });

function expect(title, expectedCount, expectedType) {
    document.write(title + ': ');
    var actualSelectionType = window.getSelection().isCollapsed ? 'caret' : 'range';

    if (selectStartCount != expectedCount)
        document.writeln('FAIL - expected ' + expectedCount + ' events but got ' + selectStartCount + ' events');
    else if (actualSelectionType != expectedType)
        document.writeln('FAIL - expected selection to be ' + expectedType + ' but was ' + actualSelectionType);
    else
        document.writeln('PASS');
}

if (window.testRunner && !window.eventSender)
    document.write('This test requires eventSender');
else if (window.testRunner) {
    testRunner.dumpAsText();

    var y = span.offsetTop + span.offsetHeight / 2;

    function leapForwardAndMove(x) {
        eventSender.leapForward(200);
        eventSender.mouseMoveTo(span.offsetLeft + x, y);
    }

    expect('Initial state', 0, 'caret');
    eventSender.dragMode = false;
    eventSender.mouseMoveTo(span.offsetLeft + 5, y);
    eventSender.mouseDown();
    expect('Mouse down', 1, 'caret');

    leapForwardAndMove(5);
    expect('Moving slightly to the right', 1, 'caret');

    leapForwardAndMove(-5);
    expect('Moving slightly to the left', 1, 'caret');

    leapForwardAndMove(span.offsetWidth / 2);
    expect('Moving to the right', 1, 'range');

    leapForwardAndMove(span.offsetWidth);
    expect('Moving further to the right', 1, 'range');

    leapForwardAndMove(0);
    expect('Moving back to the left', 1, 'caret');

    leapForwardAndMove(span.offsetWidth);
    expect('Moving to the right again', 1, 'range');

    eventSender.mouseUp();
    window.getSelection().collapse(span, 0);
    eventSender.leapForward(1000);

    eventSender.mouseMoveTo(span.offsetLeft + span.offsetWidth - 5, y);
    eventSender.mouseDown();
    expect('Mouse down on the right', 2, 'caret');

    leapForwardAndMove(span.offsetWidth / 2);
    expect('Moving to the left', 2, 'range');

    eventSender.mouseUp();

    document.writeln('Done.');
    span.parentNode.removeChild(span);
}

</script></pre>
</body>
</html>
