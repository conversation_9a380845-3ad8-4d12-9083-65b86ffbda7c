# Copyright 2014 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/features.gni")

executable("service_discovery_sniffer") {
  testonly = true
  sources = [
    "service_discovery_sniffer.cc",
    "service_discovery_sniffer.h",
  ]

  deps = [
    "//base",
    "//base/test:test_support",
    "//chrome/browser",
    "//net",
    "//third_party/webrtc_overrides:webrtc_component",
  ]
}
