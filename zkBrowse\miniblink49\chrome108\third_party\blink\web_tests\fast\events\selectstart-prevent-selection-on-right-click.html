<!DOCTYPE html>
<html>
<body>
<p>This test ensures selectstart event fires when selecting a word by right click and script can prevent the selection change.</p>
<div id="test">Select a <span id="target">word</span> in this paragraph by a right-click</div>
<script>

if (window.testRunner) {
    testRunner.dumpAsText();
    testRunner.dumpEditingCallbacks();
}

var handlerWasCalled = false;
document.body.onselectstart = function () { handlerWasCalled = true; return false; }

var test = document.getElementById('test');

if (window.testRunner && !window.eventSender)
    test.textContent = 'This test requires eventSender';
else {
    window.getSelection().collapse(document.body, 0);
    var baseNode = getSelection().baseNode;
    var baseOffset = getSelection().baseOffset;

    var target = document.getElementById('target');
    eventSender.mouseMoveTo(target.offsetLeft + target.offsetWidth / 2, target.offsetTop + target.offsetHeight / 2);
    eventSender.contextClick();

    if (!handlerWasCalled)
        test.textContent = 'FAIL - handler was never called';
    else if (!getSelection().isCollapsed || getSelection().baseNode != baseNode || getSelection().baseOffset != baseOffset)
        test.textContent = 'FAIL - selection changed';
    else
        test.textContent = 'PASS';
}

</script>
</body>
</html>
