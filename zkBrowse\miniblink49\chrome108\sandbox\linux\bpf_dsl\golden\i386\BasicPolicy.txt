  1) LOAD 4  // Architecture
  2) if A == 0x40000003; then JMP 3 else JMP 20
  3) LOAD 0  // System call number
  4) if A & 0x40000000; then JMP 20 else JMP 5
  5) if A >= 0x84; then JMP 6 else JMP 8
  6) if A >= 0x85; then JMP 7 else JMP 10
  7) if A >= 0x401; then JMP 22 else JMP 21
  8) if A >= 0x17; then JMP 9 else JMP 21
  9) if A >= 0x18; then JMP 21 else JMP 16
 10) LOAD 20  // Argument 0 (MSB)
 11) if A == 0x0; then JMP 12 else JMP 20
 12) LOAD 16  // Argument 0 (LSB)
 13) if A == 0x0; then JMP 15 else JMP 14
 14) RET 0x50016  // errno = 22
 15) RET 0x50001  // errno = 1
 16) LOAD 20  // Argument 0 (MSB)
 17) if A == 0x0; then JMP 18 else JMP 20
 18) LOAD 16  // Argument 0 (LSB)
 19) if A == 0x2a; then JMP 21 else JMP 20
 20) RET 0x0  // Kill
 21) RET 0x7fff0000  // Allowed
 22) RET 0x50026  // errno = 38
