; Copyright 2018 The Chromium Authors
; Use of this source code is governed by a BSD-style license that can be
; found in the LICENSE file.
;
; This is the sandbox configuration file used for safeguarding the print
; compositor service which is used for compositing web contents printed from
; different renderer processes.
;
; This configuration locks everything down, except font accesses.
;

; *** The contents of common.sb are implicitly included here. ***

; Needed for Fonts.
(allow-font-access)

; Reads from /System.
(allow file-read-data
  (subpath "/System/Library/ColorSync/Profiles")  ; https://crbug.com/822218
  ; https://crbug.com/1237384
  (subpath "/System/Library/CoreServices/SystemAppearance.bundle")
)

; This is available in 10.15+, and rolled out as a Finch experiment.
(if (param-true? filter-syscalls-debug)
  (when (defined? 'syscall-unix)
    (deny syscall-unix (with send-signal SIGSYS))
    (allow syscall-unix
      (syscall-number SYS_fsgetpath)
      (syscall-number SYS_getfsstat64)
      (syscall-number SYS_mkdir)
      (syscall-number SYS_pathconf)
      (syscall-number SYS_sigaltstack)
      (syscall-number SYS_write)
)))
