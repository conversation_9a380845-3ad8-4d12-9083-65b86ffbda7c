<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background-image tiling over border</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="<PERSON><PERSON>" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-04-21 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <meta name="flags" content="image" />
        <meta name="assert" content="Background-image tiling covers the border of the box." />
        <style type="text/css">
            div
            {
                background: url("support/orange15x15.png");
                border: 10px dotted black;
                margin: 30px;
            }
        </style>
    </head>
    <body>
        <p>Test passes if the background of the "Filler Text" is orange, everything within the black border as well as the spaces between the dots of the border.</p>
        <div>Filler Text</div>
    </body>
</html>