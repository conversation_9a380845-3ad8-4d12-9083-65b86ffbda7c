<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
 <head>
  <title>CSS Test: word-spacing</title>
  <meta name="flags" content="ahem">
  <script src="../../resources/ahem.js"></script>
  <link rel="help" href="http://www.w3.org/TR/REC-CSS1#word-spacing">
  <link rel="author" title="CSS1 Test Suite Contributors" href="http://www.w3.org/Style/CSS/Test/CSS1/current/tsack.html">
  <link rel="author" title="<PERSON>" href="mailto:<EMAIL>">
  <link rel="match" href="c541-word-sp-001-ref.htm">

  <style type="text/css">
   div { font: 25px/1 Ahem; width: 12em; background: yellow; color: aqua; margin: 0 0 0 2em; }
   .eight {word-spacing: 25px;}
   .nine {word-spacing: normal;}
   .ten {word-spacing: 300%;}
   .eleven {word-spacing: -1em;}
   .fill { color: yellow; }
  </style>
  <link rel="help" href="http://www.w3.org/TR/CSS21/text.html#spacing-props" title="16.4 Letter and word spacing: the 'letter-spacing' and 'word-spacing' properties">
 </head>
 <body>
  <p>There should be a stripy pattern of yellow and aqua below (each vertical stripe should be straight and unbroken).</p>
  <div class="test">x&nbsp;&nbsp;x&nbsp;&nbsp;xx&nbsp;xx</div>
  <div class="test">x&nbsp;&nbsp;x&nbsp;&nbsp;xx&nbsp;xx</div>
  <div class="test">x&nbsp;&nbsp;x&nbsp;&nbsp;xx&nbsp;xx</div>
  <div class="eight"> x x <span class="nine">xx xx</span> </div>
  <div class="ten"> x&nbsp; x &nbsp;xx xx </div>
  <div class="eleven"> x&nbsp; &nbsp;<span class="nine"> &nbsp;</span>x&nbsp;&nbsp;<span class="fill">xx</span> xx&nbsp; <span class="fill">x</span>xx </div>
 </body>
</html>