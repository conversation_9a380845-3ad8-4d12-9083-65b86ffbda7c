<!DOCTYPE html>
<html> 
<head>
<script src="../../resources/js-test.js"></script>
</head>
<body>
<a id="link" onclick="test()">click here</a>
<object type="application/x-shockwave-flash" id="obj"></object>
<pre id="console"></pre>
<script> 
    window.jsTestIsAsync = true;
    description("Tests that the plugin container doesn't generate user gestures");

    if (window.eventSender) {
        var link = document.querySelector("#link");
        eventSender.mouseMoveTo(link.offsetLeft + 10, link.offsetTop + link.offsetHeight / 2);
        eventSender.mouseDown();
        eventSender.mouseUp();
    }

    function test() {
        // Consume user gesture.
        shouldBeDefined("window.open('about:blank')");
        // Try to create a user gesture, should not work...
        var obj = document.querySelector("#obj");
        obj.focus();
        obj.click();
        var mousedown = document.createEvent("MouseEvents");
        mousedown.initMouseEvent("mousedown", true, true, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
        obj.dispatchEvent(mousedown);
        var mouseup = document.createEvent("MouseEvents");
        mouseup.initMouseEvent("mouseup", true, true, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
        obj.dispatchEvent(mouseup);
        // ... and we should not be able to open a new popup.
        shouldBeNull("window.open('about:blank')");
        finishJSTest();
    }
</script> 
</body> 
</html>
