[input-events-get-target-ranges-joining-dl-element-and-another-list.tentative.html?Backspace]
  [Backspace at "<ul><li>list-item1</li></ul><dl><dt>\[\]list-item2</dt></dl>"]
    expected: FAIL

  [Backspace at "<dl><dt>list-item1</dt><dt}>list-item2</dt></dl><ul><li>\[\]list-item3</li></ul>"]
    expected: FAIL

  [Backspace at "<dl><dt>list-item1</dt><dt>\[list-item2</dt></dl><ul><li>list-item3\]</li></ul>"]
    expected: FAIL

  [Backspace at "<ul><li>list-item1</li></ul><dl><dt>\[\]list-item2</dt><dt>list-item3</dt></dl>"]
    expected: FAIL

  [Backspace at "<dl><dt>list-item1</dt><dd}>list-item2</dd></dl><ul><li>\[\]list-item3</li></ul>"]
    expected: FAIL

  [Backspace at "<ul><li>list-item1</li></ul><dl><dt>\[\]list-item2</dt><dd>list-item3</dd></dl>"]
    expected: FAIL

  [Backspace at "<ul><li>list-item1</li></ul><dl><dd>\[\]list-item2</dd></dl>"]
    expected: FAIL

  [Backspace at "<dl><dd>list-item1</dd><dt}>list-item2</dt></dl><ul><li>\[\]list-item3</li></ul>"]
    expected: FAIL

  [Backspace at "<ul><li>list-item1</li></ul><dl><dd>\[\]list-item2</dd><dt>list-item3</dt></dl>"]
    expected: FAIL

  [Backspace at "<dl><dd>list-item1</dd><dd}>list-item2</dd></dl><ul><li>\[\]list-item3</li></ul>"]
    expected: FAIL

  [Backspace at "<dl><dd>list-item1</dd><dd>\[list-item2</dd></dl><ul><li>list-item3\]</li></ul>"]
    expected: FAIL

  [Backspace at "<ul><li>list-item1</li></ul><dl><dd>\[\]list-item2</dd><dd>list-item3</dd></dl>"]
    expected: FAIL

  [Backspace at "<ol><li>list-item1</li></ol><dl><dt>\[\]list-item2</dt></dl>"]
    expected: FAIL

  [Backspace at "<dl><dt>list-item1</dt><dt}>list-item2</dt></dl><ol><li>\[\]list-item3</li></ol>"]
    expected: FAIL

  [Backspace at "<dl><dt>list-item1</dt><dt>\[list-item2</dt></dl><ol><li>list-item3\]</li></ol>"]
    expected: FAIL

  [Backspace at "<ol><li>list-item1</li></ol><dl><dt>\[\]list-item2</dt><dt>list-item3</dt></dl>"]
    expected: FAIL

  [Backspace at "<dl><dt>list-item1</dt><dd}>list-item2</dd></dl><ol><li>\[\]list-item3</li></ol>"]
    expected: FAIL

  [Backspace at "<ol><li>list-item1</li></ol><dl><dt>\[\]list-item2</dt><dd>list-item3</dd></dl>"]
    expected: FAIL

  [Backspace at "<ol><li>list-item1</li></ol><dl><dd>\[\]list-item2</dd></dl>"]
    expected: FAIL

  [Backspace at "<dl><dd>list-item1</dd><dt}>list-item2</dt></dl><ol><li>\[\]list-item3</li></ol>"]
    expected: FAIL

  [Backspace at "<ol><li>list-item1</li></ol><dl><dd>\[\]list-item2</dd><dt>list-item3</dt></dl>"]
    expected: FAIL

  [Backspace at "<dl><dd>list-item1</dd><dd}>list-item2</dd></dl><ol><li>\[\]list-item3</li></ol>"]
    expected: FAIL

  [Backspace at "<dl><dd>list-item1</dd><dd>\[list-item2</dd></dl><ol><li>list-item3\]</li></ol>"]
    expected: FAIL

  [Backspace at "<ol><li>list-item1</li></ol><dl><dd>\[\]list-item2</dd><dd>list-item3</dd></dl>"]
    expected: FAIL

[input-events-get-target-ranges-joining-dl-element-and-another-list.tentative.html?Delete]
  [Delete at "<ul><li>list-item1\[\]</li></ul><dl><dt>list-item2</dt></dl>"]
    expected: FAIL

  [Delete at "<dl><dt>list-item1</dt><dt>list-item2\[\]</dt></dl><ul><li>list-item3</li></ul>"]
    expected: FAIL

  [Delete at "<dl><dt>list-item1</dt><dt>\[list-item2</dt></dl><ul><li>list-item3\]</li></ul>"]
    expected: FAIL

  [Delete at "<ul><li>list-item1\[\]</li></ul><dl><dt>list-item2</dt><dt>list-item3</dt></dl>"]
    expected: FAIL

  [Delete at "<dl><dt>list-item1</dt><dd>list-item2\[\]</dd></dl><ul><li>list-item3</li></ul>"]
    expected: FAIL

  [Delete at "<ul><li>list-item1\[\]</li></ul><dl><dt>list-item2</dt><dd>list-item3</dd></dl>"]
    expected: FAIL

  [Delete at "<ul><li>list-item1\[\]</li></ul><dl><dd>list-item2</dd></dl>"]
    expected: FAIL

  [Delete at "<dl><dd>list-item1</dd><dt>list-item2\[\]</dt></dl><ul><li>list-item3</li></ul>"]
    expected: FAIL

  [Delete at "<ul><li>list-item1\[\]</li></ul><dl><dd>list-item2</dd><dt>list-item3</dt></dl>"]
    expected: FAIL

  [Delete at "<dl><dd>list-item1</dd><dd>list-item2\[\]</dd></dl><ul><li>list-item3</li></ul>"]
    expected: FAIL

  [Delete at "<dl><dd>list-item1</dd><dd>\[list-item2</dd></dl><ul><li>list-item3\]</li></ul>"]
    expected: FAIL

  [Delete at "<ul><li>list-item1\[\]</li></ul><dl><dd>list-item2</dd><dd>list-item3</dd></dl>"]
    expected: FAIL

  [Delete at "<ol><li>list-item1\[\]</li></ol><dl><dt>list-item2</dt></dl>"]
    expected: FAIL

  [Delete at "<dl><dt>list-item1</dt><dt>list-item2\[\]</dt></dl><ol><li>list-item3</li></ol>"]
    expected: FAIL

  [Delete at "<dl><dt>list-item1</dt><dt>\[list-item2</dt></dl><ol><li>list-item3\]</li></ol>"]
    expected: FAIL

  [Delete at "<ol><li>list-item1\[\]</li></ol><dl><dt>list-item2</dt><dt>list-item3</dt></dl>"]
    expected: FAIL

  [Delete at "<dl><dt>list-item1</dt><dd>list-item2\[\]</dd></dl><ol><li>list-item3</li></ol>"]
    expected: FAIL

  [Delete at "<ol><li>list-item1\[\]</li></ol><dl><dt>list-item2</dt><dd>list-item3</dd></dl>"]
    expected: FAIL

  [Delete at "<ol><li>list-item1\[\]</li></ol><dl><dd>list-item2</dd></dl>"]
    expected: FAIL

  [Delete at "<dl><dd>list-item1</dd><dt>list-item2\[\]</dt></dl><ol><li>list-item3</li></ol>"]
    expected: FAIL

  [Delete at "<ol><li>list-item1\[\]</li></ol><dl><dd>list-item2</dd><dt>list-item3</dt></dl>"]
    expected: FAIL

  [Delete at "<dl><dd>list-item1</dd><dd>list-item2\[\]</dd></dl><ol><li>list-item3</li></ol>"]
    expected: FAIL

  [Delete at "<dl><dd>list-item1</dd><dd>\[list-item2</dd></dl><ol><li>list-item3\]</li></ol>"]
    expected: FAIL

  [Delete at "<ol><li>list-item1\[\]</li></ol><dl><dd>list-item2</dd><dd>list-item3</dd></dl>"]
    expected: FAIL
