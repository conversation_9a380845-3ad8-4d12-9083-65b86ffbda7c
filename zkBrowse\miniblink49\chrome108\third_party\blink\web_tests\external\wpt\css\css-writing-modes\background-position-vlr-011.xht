<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

 <head>

  <title>CSS Writing Modes Test: 'background-position: left top' and 'vertical-lr'</title>

  <link rel="author" title="<PERSON><PERSON>" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" />
  <link rel="help" href="http://www.w3.org/TR/css-writing-modes-3/#physical-only" title="7.6 Purely Physical Mappings" />

  <meta content="image interact" name="flags" />
  <meta content="This test checks that 'background-position: left top' will make background-image start at upper left corner of page (even in case there is resizing of the window) because background properties should not be affected by vertical writing-mode." name="assert" />

  <style type="text/css"><![CDATA[
  html
    {
      background-image: url("support/cat.png");
      background-position: left top;
      background-repeat: repeat-y;
      writing-mode: vertical-lr;
    }
  ]]></style>
 </head>

 <body>

  <div><img src="support/pass-cdts-bg-pos-vrl-010.png" width="465" height="94" alt="Image download support must be enabled" /></div>

<!--
  The image says:

  Test passes if there is a column of cats on
  the left of the page and if one of the cats
  starts <strong>exactly inside the top left corner</strong>
  of the page. This should remain true even
  after resizing the window.
-->

 </body>
</html>