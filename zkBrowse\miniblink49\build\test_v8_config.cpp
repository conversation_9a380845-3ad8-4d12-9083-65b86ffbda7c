#include <iostream>

// Test V8 configuration macros
int main() {
    std::cout << "Testing V8 Configuration Macros" << std::endl;
    std::cout << "================================" << std::endl;
    
#ifdef USING_V8_SHARED
    std::cout << "? USING_V8_SHARED is defined" << std::endl;
#else
    std::cout << "? USING_V8_SHARED is NOT defined" << std::endl;
#endif

#ifdef BUILDING_V8_SHARED
    std::cout << "? BUILDING_V8_SHARED is defined" << std::endl;
#else
    std::cout << "? BUILDING_V8_SHARED is NOT defined" << std::endl;
#endif

#if V8_MAJOR_VERSION >= 10
    std::cout << "? V8 version is 10 or higher" << std::endl;
#else
    std::cout << "? V8 version is less than 10" << std::endl;
#endif

    std::cout << std::endl;
    std::cout << "V8 configuration test completed!" << std::endl;
    
    return 0;
}
