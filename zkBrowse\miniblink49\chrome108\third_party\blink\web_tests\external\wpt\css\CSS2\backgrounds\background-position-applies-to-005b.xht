<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

 <head>

  <title>CSS Test: Background-position applied to elements with 'display' set to 'table-column-group'</title>

  <link rel="author" title="<PERSON><PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" />
  <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" title="14.2.1 Background properties: 'background-color', 'background-image', 'background-repeat', 'background-attachment', 'background-position', and 'background'" />
  <link rel="match" href="background-position-applies-to-001b-ref.xht" />

  <meta name="flags" content="image" />
  <meta name="assert" content="The 'background-position' property applies to elements with 'display' set to 'table-column-group'." />

  <style type="text/css"><![CDATA[
  table {border-spacing: 0px;}

  colgroup
  {
  background-image: url("/css/support/60x60-red.png");
  background-position: bottom right;
  background-repeat: no-repeat;
  }

  td {padding: 0px;}

  td#top-left {border-top: transparent solid 60px;}

  td#top-right {border-right: transparent solid 60px;}

  td#bottom-left
  {
  border-bottom: transparent solid 60px;
  border-left: transparent solid 60px;
  }

  td#green-overlapping {border-bottom: green solid 60px;}
  ]]></style>

 </head>

 <body>

  <p>Test passes if there is a filled green square and <strong>no red</strong>.</p>

  <table>
    <colgroup><col></col><col></col></colgroup>
    <tbody>
      <tr><td id="top-left"></td><td id="top-right"></td></tr>
      <tr><td id="bottom-left"></td><td id="green-overlapping"></td></tr>
    </tbody>
  </table>

 </body>
</html>
