<div>PASS</div>
<script>
function dragEnter() {
    event.dataTransfer.dropEffect = "none";
    event.preventDefault();
}
function dragOver() {
    // See https://bugs.webkit.org/show_bug.cgi?id=25922
    // This is the line which causes failure:
    event.dataTransfer.dropEffect = "none";

    event.preventDefault();
}
function dragLeave() {
    event.preventDefault();
    setTimeout(function() {
        // Wait until after the drop to notifyDone, just to make sure
        // the navigation was prevented correctly.
        if (window.testRunner)
            testRunner.notifyDone();
    }, 0);
}

// Capture all drag events
window.addEventListener("dragenter", dragEnter, true);
window.addEventListener("dragover", dragOver, true);
// We don't get a drop event when we prevent drop, so listen for dragleave
window.addEventListener("dragleave", dragLeave, true);

function doTest() {
    testRunner.dumpAsText();

    eventSender.beginDragWithFiles(["resources/file-for-prevent-drag-to-navigate.html"]);
    eventSender.mouseMoveTo(10, 10);
    eventSender.mouseUp();
}

if (window.eventSender) {
    testRunner.waitUntilDone();
    // The load seems to fail (for the wrong reasons) if we try to kick of a
    // new load before this one is finished.  So we wait.
    window.onload = doTest();
}
</script>
