// Copyright 2019 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

module data_decoder.mojom;

import "components/web_package/mojom/web_bundle_parser.mojom";
import "sandbox/policy/mojom/sandbox.mojom";
import "services/data_decoder/public/mojom/gzipper.mojom";
import "services/data_decoder/public/mojom/image_decoder.mojom";
import "services/data_decoder/public/mojom/json_parser.mojom";
import "services/data_decoder/public/mojom/web_bundler.mojom";
import "services/data_decoder/public/mojom/xml_parser.mojom";

[EnableIf=is_chromeos_ash]
import "services/data_decoder/public/mojom/ble_scan_parser.mojom";

// TODO(https://crbug.com/1296386): Remove JIT permission on Fuchsia when
// decoding images no longer requires the VMEX capability.
[EnableIf=is_fuchsia]
const sandbox.mojom.Sandbox kDataDecoderSandbox
  = sandbox.mojom.Sandbox.kServiceWithJit;
[EnableIfNot=is_fuchsia]
const sandbox.mojom.Sandbox kDataDecoderSandbox
  = sandbox.mojom.Sandbox.kService;

// The main interface to the Data Decoder service.
[ServiceSandbox=kDataDecoderSandbox]
interface DataDecoderService {
  // Binds an interface which can be used to decode compressed image data.
  BindImageDecoder(pending_receiver<ImageDecoder> receiver);

  // Binds an interface which can be used to parse JSON data.
  BindJsonParser(pending_receiver<JsonParser> receiver);

  // Binds an interface which can be used to parse XML data.
  BindXmlParser(pending_receiver<XmlParser> reciever);

  // Binds an interface which can be used to parse Web Bundles.
  BindWebBundleParserFactory(
      pending_receiver<web_package.mojom.WebBundleParserFactory> receiver);

  // Binds an interface which can be used to generate a Web Bundle.
  BindWebBundler(pending_receiver<WebBundler> receiver);

  // Binds an interface which can be used to compress and decompress data using
  // gzip.
  BindGzipper(pending_receiver<Gzipper> receiver);

  // Binds an interface which can be used to parse raw BLE advertising packet
  // data.
  [EnableIf=is_chromeos_ash]
  BindBleScanParser(pending_receiver<BleScanParser> receiver);
};
