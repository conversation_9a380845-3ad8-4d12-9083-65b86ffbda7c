<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background with (color image repeat position)</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="G<PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-03-24 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <link rel="match" href="background-087-ref.xht" />

        <meta name="flags" content="image" />
        <meta name="assert" content="Background with (color image repeat position) sets the background to the color specified, with the image overlaid across the x-axis at the bottom." />
        <style type="text/css">
            div
            {
                background: orange url("support/blue15x15.png") repeat-x bottom;
                height: 100px;
                width: 100px;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is an orange rectangle above a blue stripe.</p>
        <div></div>
    </body>
</html>