<!DOCTYPE html>
<script>
function key(evt)
{
    window.open("about:blank");
    evt.preventDefault();
}

function test()
{
    if (window.testRunner) {
        testRunner.dumpAsText();
        testRunner.dumpNavigationPolicy();
        var a = document.querySelector("#link");
        a.addEventListener('click', key);
        a.focus();
        eventSender.keyDown("Enter", ["altKey"]);
    }
}
</script>
<body onload="test()">
<p>Tests that hitting alt-enter doesn't force a download if the navigation is triggered by script.</p>
<a href="about:blank" id="link">link</a>
</body>
