<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.6.1 display</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
.one {display: block;}
.two {display: inline;}
.three {display: list-item; list-style-type: square; margin-left: 3em;}
.four {display: none; color: red;}
I {display: block;}</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.one {display: block;}
.two {display: inline;}
.three {display: list-item; list-style-type: square; margin-left: 3em;}
.four {display: none; color: red;}
I {display: block;}
</PRE>
<HR>
<P class="one">
This sentence should be a block-level element.
</P>

<P class="two">
This sentence should be part of an inline element, as are the next three.
</P><P class="two">
This sentence and the next two are part of a second inline element.  They should therefore appear, along with the sentence above, to be all one paragraph which is four sentences long.  If this is not the case, then the keyword <CODE>inline</CODE> is being ignored.
</P>

<P class="three">
This sentence should be treated as a list-item, and therefore be rendered however this user agent displays list items (if <CODE>list-style-type</CODE> is supported, there will be a square for the item marker).  A 3em left margin has been applied in order to ensure that there is space for the list-item marker.
</P>

<P>
The next paragraph should be invisible (if it's visible, you'll see red text).
</P>
<P class="four">
This paragraph should be invisible.
</P>

<P>
There should be no text after the colon: <SPAN class="four">fnord.</SPAN>
</P>

<P>
The italicized text <I>in this paragraph</I> should be a block-level element.
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P class="one">
This sentence should be a block-level element.
</P>

<P class="two">
This sentence should be part of an inline element, as are the next three.
</P><P class="two">
This sentence and the next two are part of a second inline element.  They should therefore appear, along with the sentence above, to be all one paragraph which is four sentences long.  If this is not the case, then the keyword <CODE>inline</CODE> is being ignored.
</P>

<P class="three">
This sentence should be treated as a list-item, and therefore be rendered however this user agent displays list items (if <CODE>list-style-type</CODE> is supported, there will be a square for the item marker).  A 3em left margin has been applied in order to ensure that there is space for the list-item marker.
</P>

<P>
The next paragraph should be invisible (if it's visible, you'll see red text).
</P>
<P class="four">
This paragraph should be invisible.
</P>

<P>
There should be no text after the colon: <SPAN class="four">fnord.</SPAN>
</P>

<P>
The italicized text <I>in this paragraph</I> should be a block-level element.
</P>
</TD></TR></TABLE></BODY>
</HTML>
