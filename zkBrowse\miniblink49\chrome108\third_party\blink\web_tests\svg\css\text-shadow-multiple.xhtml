<html xmlns="http://www.w3.org/1999/xhtml">
<body style="margin: 0px">

<style>
    div, text {
        font-family: Arial;
        font-size: 50px;
    }

    text, tspan {
        alignment-baseline: hanging;
    }

    div.shadow, text.shadow {
        text-shadow: hsla(20, 100%, 50%, 1) 7px 4px 0
                   , hsla(60, 100%, 50%, 1) -8px 14px 2px
                   , hsla(100, 100%, 50%, 1) -21px -12px 5px;
    }
</style>
<p>The first two texts should look identical, as there is no shadow applied.</p>

<div style="position: absolute; left: 20px;">
    <span style="-webkit-text-stroke: 1px; -webkit-text-fill-color: transparent;">This</span>
    <span style="-webkit-text-stroke: 1px; -webkit-text-fill-color: white;">text</span>
    <span>casts multiple</span>
    shadows
</div>
<svg xmlns="http://www.w3.org/2000/svg" width="800" height="100" style="margin-top: 50px;">
    <text x="20" y="30">
        <tspan fill="white" fill-opacity="0" stroke-width="1px" stroke="black">This</tspan>
        <tspan fill="white" stroke-width="1px" stroke="black">text</tspan>
        <tspan>casts multiple</tspan>
        shadows
    </text>
</svg>

<p>The next two texts have subtle differences, as the stroke/fill is painted seperated in SVG.</p>

<div class="shadow" style="position: absolute; left: 20px;">
    <span style="-webkit-text-stroke: 1px; -webkit-text-fill-color: transparent;">This</span>
    <span style="-webkit-text-stroke: 1px; -webkit-text-fill-color: white;">text</span>
    <span>casts multiple</span>
    shadows
</div>
<svg xmlns="http://www.w3.org/2000/svg" width="800" height="100" style="margin-top: 50px;">
    <text class="shadow" x="20" y="30">
        <tspan fill="white" fill-opacity="0" stroke-width="1px" stroke="black">This</tspan>
        <tspan fill="white" stroke-width="1px" stroke="black">text</tspan>
        <tspan>casts multiple</tspan>
        shadows
    </text>
</svg>

</body>
</html>
