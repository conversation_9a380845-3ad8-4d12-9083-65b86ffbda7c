import type { KeywordCxt } from ".";
import type { AddedKeywordDefinition, MacroKeywordDefinition, FuncKeywordDefinition } from "../../types";
import type { SchemaObjCxt } from "..";
import type { JSONType } from "../rules";
export declare function macroKeywordCode(cxt: KeywordCxt, def: MacroKeywordDefinition): void;
export declare function funcKeywordCode(cxt: KeywordCxt, def: FuncKeywordDefinition): void;
export declare function validSchemaType(schema: unknown, schemaType: JSONType[], allowUndefined?: boolean): boolean;
export declare function validateKeywordUsage({ schema, opts, self, errSchemaPath }: SchemaObjCxt, def: AddedKeywordDefinition, keyword: string): void;
