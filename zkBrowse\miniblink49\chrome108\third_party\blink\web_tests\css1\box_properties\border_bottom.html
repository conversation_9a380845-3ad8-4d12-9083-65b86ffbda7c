<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.5.20 border-bottom</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
.one {border-bottom: purple double 10px;}
.two {border-bottom: purple thin solid;}
.three {border-bottom: black medium solid;}
TD {border-bottom: green 2px solid;}</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.one {border-bottom: purple double 10px;}
.two {border-bottom: purple thin solid;}
.three {border-bottom: black medium solid;}
TD {border-bottom: green 2px solid;}
</PRE>
<HR>
<P>
Note that all table cells on this page should have a two-pixel solid green border along their bottom sides.  This border applies only to the cells, not the rows which contain them.
</P>

<P class="one">
This paragraph should have a purple, double, 10-pixel bottom border.
</P>
<P class="two">
This paragraph should have a thin purple bottom border.
</P>

<TABLE cellspacing="5" border>
<TR>
<TD colspan="2">Every cell in this table should have a 2-pixel solid green bottom border.  This is also true of the table-testing section in the second half of the test page.
</TD>
</TR>
<TR>
<TD>Cell one</TD><TD>Cell two<TABLE border><TR><TD>Nested single-cell table!</TD></TR></TABLE></TD>
</TR>
</TABLE>

<UL>
<LI class="three">This is a list item...
<UL>
<LI>...and this...
<LI>...is a second list...
<LI>...nested within the list item.
</UL>
</LI>
<LI class="three">This is a second list item.</LI>
<LI class="three">Each list item in this list should have a medium-width black border at its bottom, which for the first item means that it should appear <EM>beneath</EM> the nested list (below the line "...nested within the list item.").
</UL>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P>
Note that all table cells on this page should have a two-pixel solid green border along their bottom sides.  This border applies only to the cells, not the rows which contain them.
</P>

<P class="one">
This paragraph should have a purple, double, 10-pixel bottom border.
</P>
<P class="two">
This paragraph should have a thin purple bottom border.
</P>

<TABLE cellspacing="5" border>
<TR>
<TD colspan="2">Every cell in this table should have a 2-pixel solid green bottom border.  This is also true of the table-testing section in the second half of the test page.
</TD>
</TR>
<TR>
<TD>Cell one</TD><TD>Cell two<TABLE border><TR><TD>Nested single-cell table!</TD></TR></TABLE></TD>
</TR>
</TABLE>

<UL>
<LI class="three">This is a list item...
<UL>
<LI>...and this...
<LI>...is a second list...
<LI>...nested within the list item.
</UL>
</LI>
<LI class="three">This is a second list item.</LI>
<LI class="three">Each list item in this list should have a medium-width black border at its bottom, which for the first item means that it should appear <EM>beneath</EM> the nested list (below the line "...nested within the list item.").
</UL>
</TD></TR></TABLE></BODY>
</HTML>
