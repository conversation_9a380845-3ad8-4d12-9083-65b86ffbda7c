CONSOLE ERROR: Uncaught Error: Original exception.
CONSOLE ERROR: Uncaught Error: Nested exception.
This test should trigger 'window.onerror' only once, without diving into horrible recursion.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".

window.onerror: "Uncaught Error: Original exception." at onerror-test.js (Line: 6, Column: 5)
Stack Trace:
Error: Original exception.
    at throwException onerror-test.js:6:11
    at window-onerror-06.html:22:9

PASS successfullyParsed is true

TEST COMPLETE

