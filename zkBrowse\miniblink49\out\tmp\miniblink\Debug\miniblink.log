﻿C:\Program Files (x86)\Microsoft Visual Studio\2017\Enterprise\Common7\IDE\VC\VCTargets\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
  PlatformEventHandler.cpp
  WebFrameClientImpl.cpp
  WebPage.cpp
  WebPageImpl.cpp
  DevToolsAgent.cpp
  DevToolsClient.cpp
  DragHandle.cpp
  PopupMenuWin.cpp
  BlinkPlatformImpl.cpp
  WebMediaPlayerImpl.cpp
  PluginMessageThrottlerWin.cpp
  PluginPackage.cpp
  PluginPackageWin.cpp
  PluginStream.cpp
  WebPluginImpl.cpp
  WebPluginImplWin.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\browser\WebPage.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\browser\PlatformEventHandler.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\web_impl_win\npapi\PluginPackageWin.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\browser\WebFrameClientImpl.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\web_impl_win\BlinkPlatformImpl.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\web_impl_win\npapi\WebPluginImplWin.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\web_impl_win\npapi\PluginPackage.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\web_impl_win\npapi\PluginMessageThrottlerWin.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\devtools\DevToolsClient.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\web_impl_win\npapi\WebPluginImpl.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\ui\PopupMenuWin.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\web_impl_win\npapi\PluginStream.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\browser\WebPageImpl.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\ui\DragHandle.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\devtools\DevToolsAgent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\web_impl_win\WebMediaPlayerImpl.cpp)
  npapi.cpp
  USVStringOrURLSearchParams.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\USVStringOrURLSearchParams.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\web_impl_win\npapi\npapi.cpp)
  UnionTypesCore.cpp
  V8Animation.cpp
  V8AnimationEffectReadOnly.cpp
  V8AnimationEffectTiming.cpp
  V8AnimationEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Animation.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8AnimationEffectReadOnly.cpp)
  V8AnimationEventInit.cpp
  V8AnimationPlayerEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8AnimationEffectTiming.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\UnionTypesCore.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8AnimationEvent.cpp)
  V8AnimationPlayerEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8AnimationPlayerEvent.cpp)
  V8AnimationTimeline.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8AnimationEventInit.cpp)
  V8ApplicationCache.cpp
  V8ApplicationCacheErrorEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8AnimationPlayerEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8AnimationTimeline.cpp)
  V8ApplicationCacheErrorEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ApplicationCache.cpp)
  V8ArrayBuffer.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ApplicationCacheErrorEvent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ApplicationCacheErrorEventInit.cpp)
  V8ArrayBufferView.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ArrayBuffer.cpp)
  V8Attr.cpp
  V8AudioTrack.cpp
  V8AudioTrackList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ArrayBufferView.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Attr.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8AudioTrack.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8AudioTrackList.cpp)
  V8AutocompleteErrorEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8AutocompleteErrorEvent.cpp)
  V8AutocompleteErrorEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8AutocompleteErrorEventInit.cpp)
  V8BarProp.cpp
  V8BeforeUnloadEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8BarProp.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8BeforeUnloadEvent.cpp)
  V8Blob.cpp
  V8BlobPropertyBag.cpp
  V8CDATASection.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Blob.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8BlobPropertyBag.cpp)
  V8CSS.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CDATASection.cpp)
  V8CSSFontFaceRule.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSS.cpp)
  V8CSSGroupingRule.cpp
  V8CSSImportRule.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSFontFaceRule.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSGroupingRule.cpp)
  V8CSSKeyframeRule.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSImportRule.cpp)
  V8CSSKeyframesRule.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSKeyframeRule.cpp)
  V8CSSMediaRule.cpp
  V8CSSPageRule.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSKeyframesRule.cpp)
  V8CSSRule.cpp
  V8CSSRuleList.cpp
  V8CSSStyleDeclaration.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSMediaRule.cpp)
  V8CSSStyleRule.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSPageRule.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSRule.cpp)
  V8CSSStyleSheet.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSStyleDeclaration.cpp)
  V8CSSSupportsRule.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSRuleList.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSStyleRule.cpp)
  V8CSSViewportRule.cpp
  V8CanvasContextCreationAttributes.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSStyleSheet.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSSupportsRule.cpp)
  V8CharacterData.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSViewportRule.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CanvasContextCreationAttributes.cpp)
  V8ClientRect.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CharacterData.cpp)
  V8ClientRectList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ClientRect.cpp)
  V8ClipboardEvent.cpp
  V8Comment.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ClientRectList.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ClipboardEvent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Comment.cpp)
  V8CompositionEvent.cpp
  V8CompositionEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CompositionEvent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CompositionEventInit.cpp)
  V8CompositorProxy.cpp
  V8ComputedTimingProperties.cpp
  V8Console.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CompositorProxy.cpp)
  V8ConsoleBase.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ComputedTimingProperties.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Console.cpp)
  V8CustomEvent.cpp
  V8CustomEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ConsoleBase.cpp)
  V8DOMError.cpp
  V8DOMException.cpp
  V8DOMImplementation.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CustomEvent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CustomEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMError.cpp)
  V8DOMMatrix.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMException.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMImplementation.cpp)
  V8DOMMatrixReadOnly.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMMatrix.cpp)
  V8DOMParser.cpp
  V8DOMPoint.cpp
  V8DOMPointInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMMatrixReadOnly.cpp)
  V8DOMPointReadOnly.cpp
  V8DOMRect.cpp
  V8DOMRectReadOnly.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMParser.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMPoint.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMPointInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMPointReadOnly.cpp)
  V8DOMSettableTokenList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMRect.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMRectReadOnly.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMSettableTokenList.cpp)
  V8DOMStringList.cpp
  V8DOMStringMap.cpp
  V8DOMTokenList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMStringList.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMStringMap.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMTokenList.cpp)
  V8DataTransfer.cpp
  V8DataTransferItem.cpp
  V8DataTransferItemList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DataTransfer.cpp)
  V8DataView.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DataTransferItem.cpp)
  V8DedicatedWorkerGlobalScope.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DataTransferItemList.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DataView.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DedicatedWorkerGlobalScope.cpp)
  V8DevToolsHost.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DevToolsHost.cpp)
  V8Document.cpp
  V8DocumentFragment.cpp
  V8DocumentType.cpp
  V8EffectModel.cpp
  V8Element.cpp
  V8ElementRegistrationOptions.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Document.cpp)
  V8ErrorEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DocumentFragment.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DocumentType.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8EffectModel.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Element.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ElementRegistrationOptions.cpp)
  V8ErrorEventInit.cpp
  V8Event.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ErrorEvent.cpp)
  V8EventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ErrorEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Event.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8EventInit.cpp)
  V8EventModifierInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8documentfragment.cpp(409): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
  V8EventSource.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8EventModifierInit.cpp)
  V8EventSourceInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8EventSource.cpp)
  V8EventTarget.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8document.cpp(6020): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
  V8External.cpp
  V8File.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8EventSourceInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8EventTarget.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8External.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8File.cpp)
  V8FileError.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8element.cpp(2913): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
  V8FileList.cpp
  V8FilePropertyBag.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FileError.cpp)
  V8FileReader.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FileList.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FilePropertyBag.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FileReader.cpp)
  V8FileReaderSync.cpp
  V8Float32Array.cpp
  V8Float64Array.cpp
  V8FocusEvent.cpp
  V8FocusEventInit.cpp
  V8FontFace.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FileReaderSync.cpp)
  V8FontFaceDescriptors.cpp
  V8FontFaceSet.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Float32Array.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Float64Array.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FocusEvent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FocusEventInit.cpp)
  V8FontFaceSetForEachCallback.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FontFace.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FontFaceDescriptors.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FontFaceSet.cpp)
  V8FontFaceSetLoadEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FontFaceSetLoadEvent.cpp)
  V8FontFaceSetLoadEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FontFaceSetForEachCallback.cpp)
  V8FormData.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FontFaceSetLoadEventInit.cpp)
  V8FrameRequestCallback.cpp
  V8GCObservation.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8fontfaceset.cpp(400): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FormData.cpp)
  V8GarbageCollectedScriptWrappable.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8GCObservation.cpp)
  V8HTMLAllCollection.cpp
  V8HTMLAnchorElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8GarbageCollectedScriptWrappable.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FrameRequestCallback.cpp)
  V8HTMLAppletElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLAllCollection.cpp)
  V8HTMLAreaElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLAnchorElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLAppletElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLAreaElement.cpp)
  V8HTMLAudioElement.cpp
  V8HTMLBRElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLAudioElement.cpp)
  V8HTMLBaseElement.cpp
  V8HTMLBodyElement.cpp
  V8HTMLButtonElement.cpp
  V8HTMLCanvasElement.cpp
  V8HTMLCollection.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLBRElement.cpp)
  V8HTMLContentElement.cpp
  V8HTMLDListElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLBaseElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLBodyElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8htmlallcollection.cpp(182): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLButtonElement.cpp)
  V8HTMLDataListElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLCanvasElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLCollection.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLContentElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLDListElement.cpp)
  V8HTMLDetailsElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLDataListElement.cpp)
  V8HTMLDialogElement.cpp
  V8HTMLDirectoryElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLDetailsElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLDialogElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLDirectoryElement.cpp)
  V8HTMLDivElement.cpp
  V8HTMLDocument.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLDivElement.cpp)
  V8HTMLElement.cpp
  V8HTMLEmbedElement.cpp
  V8HTMLFieldSetElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLDocument.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLEmbedElement.cpp)
  V8HTMLFontElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLFieldSetElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLFontElement.cpp)
  V8HTMLFormControlsCollection.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLFormControlsCollection.cpp)
  V8HTMLFormElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8htmldocument.cpp(320): error C2039: “SetHiddenPrototype”: 不是“v8::FunctionTemplate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-template.h(463): note: 参见“v8::FunctionTemplate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLFormElement.cpp)
  V8HTMLFrameElement.cpp
  V8HTMLFrameSetElement.cpp
  V8HTMLHRElement.cpp
  V8HTMLLIElement.cpp
  V8HTMLLabelElement.cpp
  V8HTMLLegendElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLFrameElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLFrameSetElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLHRElement.cpp)
  V8HTMLLinkElement.cpp
  V8HTMLMapElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLLIElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLLabelElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLLegendElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8htmlformcontrolscollection.cpp(158): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLLinkElement.cpp)
  V8HTMLMarqueeElement.cpp
  V8HTMLMediaElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLMapElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLMarqueeElement.cpp)
  V8HTMLMenuElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLMediaElement.cpp)
  V8HTMLMenuItemElement.cpp
  V8HTMLMetaElement.cpp
  V8HTMLMeterElement.cpp
  V8HTMLModElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLMenuElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLMenuItemElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLMetaElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLMeterElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLModElement.cpp)
  V8HTMLOListElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLOListElement.cpp)
  V8HTMLObjectElement.cpp
  V8HTMLOptGroupElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLObjectElement.cpp)
  V8HTMLOptionElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLOptGroupElement.cpp)
  V8HTMLOptionsCollection.cpp
  V8HTMLOutputElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLOptionElement.cpp)
  V8HTMLParagraphElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLOptionsCollection.cpp)
  V8HTMLParamElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLOutputElement.cpp)
  V8HTMLPictureElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLParagraphElement.cpp)
  V8HTMLPreElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLParamElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLPictureElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLPreElement.cpp)
  V8HTMLProgressElement.cpp
  V8HTMLQuoteElement.cpp
  V8HTMLScriptElement.cpp
  V8HTMLSelectElement.cpp
  V8HTMLShadowElement.cpp
  V8HTMLSourceElement.cpp
  V8HTMLSpanElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLQuoteElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLProgressElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLScriptElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLSelectElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLShadowElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLSourceElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLSpanElement.cpp)
  V8HTMLStyleElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8htmloptionscollection.cpp(307): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLStyleElement.cpp)
  V8HTMLTableCaptionElement.cpp
  V8HTMLTableCellElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLTableCaptionElement.cpp)
  V8HTMLTableColElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLTableCellElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLTableColElement.cpp)
  V8HTMLTableElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLTableElement.cpp)
  V8HTMLTableRowElement.cpp
  V8HTMLTableSectionElement.cpp
  V8HTMLTemplateElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLTableRowElement.cpp)
  V8HTMLTextAreaElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLTableSectionElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLTemplateElement.cpp)
  V8HTMLTitleElement.cpp
  V8HTMLTrackElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLTextAreaElement.cpp)
  V8HTMLUListElement.cpp
  V8HTMLUnknownElement.cpp
  V8HTMLVideoElement.cpp
  V8HashChangeEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLTitleElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLTrackElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLUnknownElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLUListElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLVideoElement.cpp)
  V8HashChangeEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HashChangeEvent.cpp)
  V8History.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HashChangeEventInit.cpp)
  V8ImageBitmap.cpp
  V8ImageData.cpp
  V8InputDevice.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8History.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ImageBitmap.cpp)
  V8InputDeviceInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ImageData.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8InputDevice.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8InputDeviceInit.cpp)
  V8InspectorOverlayHost.cpp
  V8Int16Array.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8InspectorOverlayHost.cpp)
  V8Int32Array.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Int16Array.cpp)
  V8Int8Array.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Int32Array.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Int8Array.cpp)
  V8Iterator.cpp
  V8KeyboardEvent.cpp
  V8KeyboardEventInit.cpp
  V8KeyframeEffect.cpp
  V8KeyframeEffectOptions.cpp
  V8LayerRect.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Iterator.cpp)
  V8LayerRectList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8KeyboardEvent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8KeyboardEventInit.cpp)
  V8Location.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8KeyframeEffect.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8KeyframeEffectOptions.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8LayerRect.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8LayerRectList.cpp)
  V8MediaController.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Location.cpp)
  V8MediaError.cpp
  V8MediaKeyError.cpp
  V8MediaKeyEvent.cpp
  V8MediaKeyEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MediaController.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MediaError.cpp)
  V8MediaList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MediaKeyError.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MediaKeyEvent.cpp)
  V8MediaQueryList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MediaKeyEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MediaList.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MediaQueryList.cpp)
  V8MediaQueryListEvent.cpp
  V8MediaQueryListEventInit.cpp
  V8MemoryInfo.cpp
  V8MessageChannel.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MediaQueryListEvent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MediaQueryListEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MemoryInfo.cpp)
  V8MessageEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MessageChannel.cpp)
  V8MessageEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MessageEvent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MessageEventInit.cpp)
  V8MessagePort.cpp
  V8MouseEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MessagePort.cpp)
  V8MouseEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MouseEvent.cpp)
  V8MutationEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MouseEventInit.cpp)
  V8MutationObserver.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MutationEvent.cpp)
  V8MutationObserverInit.cpp
  V8MutationRecord.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MutationObserver.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MutationObserverInit.cpp)
  V8NamedNodeMap.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MutationRecord.cpp)
  V8Navigator.cpp
  V8Node.cpp
  V8NodeFilter.cpp
  V8NodeIterator.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8NamedNodeMap.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Navigator.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Node.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8NodeFilter.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8NodeIterator.cpp)
  V8NodeList.cpp
  V8PagePopupController.cpp
  V8PageTransitionEvent.cpp
  V8PageTransitionEventInit.cpp
  V8Performance.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8NodeList.cpp)
  V8PerformanceCompositeTiming.cpp
  V8PerformanceEntry.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8PageTransitionEvent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8PagePopupController.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8PageTransitionEventInit.cpp)
  V8PerformanceMark.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Performance.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8PerformanceCompositeTiming.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8PerformanceEntry.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8namednodemap.cpp(317): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8PerformanceMeasure.cpp
  V8PerformanceNavigation.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8PerformanceMark.cpp)
  V8PerformanceRenderTiming.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8PerformanceNavigation.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8PerformanceMeasure.cpp)
  V8PerformanceResourceTiming.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8nodelist.cpp(191): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8PerformanceRenderTiming.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8PerformanceResourceTiming.cpp)
  V8PerformanceTiming.cpp
  V8PluginPlaceholderElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8PerformanceTiming.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8PluginPlaceholderElement.cpp)
  V8PointerEvent.cpp
  V8PointerEventInit.cpp
  V8PopStateEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8PointerEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8PointerEvent.cpp)
  V8PopStateEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8PopStateEvent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8PopStateEventInit.cpp)
  V8ProcessingInstruction.cpp
  V8ProgressEvent.cpp
  V8ProgressEventInit.cpp
  V8PromiseRejectionEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ProgressEvent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ProcessingInstruction.cpp)
  V8PromiseRejectionEventInit.cpp
  V8RadioNodeList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ProgressEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8PromiseRejectionEvent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8PromiseRejectionEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8RadioNodeList.cpp)
  V8Range.cpp
  V8ReadableByteStream.cpp
  V8ReadableByteStreamReader.cpp
  V8ReadableStream.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Range.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ReadableByteStream.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ReadableByteStreamReader.cpp)
  V8ReadableStreamReader.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ReadableStream.cpp)
  V8RefCountedScriptWrappable.cpp
  V8RelatedEvent.cpp
  V8RelatedEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ReadableStreamReader.cpp)
  V8ResourceProgressEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8RefCountedScriptWrappable.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8RelatedEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8RelatedEvent.cpp)
  V8SVGAElement.cpp
  V8SVGAngle.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ResourceProgressEvent.cpp)
  V8SVGAnimateElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGAElement.cpp)
  V8SVGAnimateMotionElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGAngle.cpp)
  V8SVGAnimateTransformElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGAnimateElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGAnimateMotionElement.cpp)
  V8SVGAnimatedAngle.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGAnimateTransformElement.cpp)
  V8SVGAnimatedBoolean.cpp
  V8SVGAnimatedEnumeration.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGAnimatedAngle.cpp)
  V8SVGAnimatedInteger.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGAnimatedBoolean.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGAnimatedEnumeration.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGAnimatedInteger.cpp)
  V8SVGAnimatedLength.cpp
  V8SVGAnimatedLengthList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGAnimatedLength.cpp)
  V8SVGAnimatedNumber.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGAnimatedLengthList.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGAnimatedNumber.cpp)
  V8SVGAnimatedNumberList.cpp
  V8SVGAnimatedPreserveAspectRatio.cpp
  V8SVGAnimatedRect.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGAnimatedNumberList.cpp)
  V8SVGAnimatedString.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGAnimatedRect.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGAnimatedPreserveAspectRatio.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGAnimatedString.cpp)
  V8SVGAnimatedTransformList.cpp
  V8SVGAnimationElement.cpp
  V8SVGCircleElement.cpp
  V8SVGClipPathElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGAnimatedTransformList.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGAnimationElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGCircleElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGClipPathElement.cpp)
  V8SVGComponentTransferFunctionElement.cpp
  V8SVGCursorElement.cpp
  V8SVGDefsElement.cpp
  V8SVGDescElement.cpp
  V8SVGDiscardElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGComponentTransferFunctionElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGCursorElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGDefsElement.cpp)
  V8SVGElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGDescElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGDiscardElement.cpp)
  V8SVGEllipseElement.cpp
  V8SVGFEBlendElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGEllipseElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEBlendElement.cpp)
  V8SVGFEColorMatrixElement.cpp
  V8SVGFEComponentTransferElement.cpp
  V8SVGFECompositeElement.cpp
  V8SVGFEConvolveMatrixElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEColorMatrixElement.cpp)
  V8SVGFEDiffuseLightingElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEComponentTransferElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFECompositeElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEConvolveMatrixElement.cpp)
  V8SVGFEDisplacementMapElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEDiffuseLightingElement.cpp)
  V8SVGFEDistantLightElement.cpp
  V8SVGFEDropShadowElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEDisplacementMapElement.cpp)
  V8SVGFEFloodElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEDistantLightElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEDropShadowElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEFloodElement.cpp)
  V8SVGFEFuncAElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEFuncAElement.cpp)
  V8SVGFEFuncBElement.cpp
  V8SVGFEFuncGElement.cpp
  V8SVGFEFuncRElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEFuncGElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEFuncBElement.cpp)
  V8SVGFEGaussianBlurElement.cpp
  V8SVGFEImageElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEFuncRElement.cpp)
  V8SVGFEMergeElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEGaussianBlurElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEImageElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEMergeElement.cpp)
  V8SVGFEMergeNodeElement.cpp
  V8SVGFEMorphologyElement.cpp
  V8SVGFEOffsetElement.cpp
  V8SVGFEPointLightElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEMergeNodeElement.cpp)
  V8SVGFESpecularLightingElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEMorphologyElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEOffsetElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEPointLightElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFESpecularLightingElement.cpp)
  V8SVGFESpotLightElement.cpp
  V8SVGFETileElement.cpp
  V8SVGFETurbulenceElement.cpp
  V8SVGFilterElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFESpotLightElement.cpp)
  V8SVGForeignObjectElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFETurbulenceElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFETileElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFilterElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGForeignObjectElement.cpp)
  V8SVGGElement.cpp
  V8SVGGeometryElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGGeometryElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGGElement.cpp)
  V8SVGGradientElement.cpp
  V8SVGGraphicsElement.cpp
  V8SVGImageElement.cpp
  V8SVGLength.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGGradientElement.cpp)
  V8SVGLengthList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGImageElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGGraphicsElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGLength.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGLengthList.cpp)
  V8SVGLineElement.cpp
  V8SVGLinearGradientElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGLineElement.cpp)
  V8SVGMPathElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGLinearGradientElement.cpp)
  V8SVGMarkerElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGMPathElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGMarkerElement.cpp)
  V8SVGMaskElement.cpp
  V8SVGMatrix.cpp
  V8SVGMetadataElement.cpp
  V8SVGNumber.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGMaskElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGMatrix.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGMetadataElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGNumber.cpp)
  V8SVGNumberList.cpp
  V8SVGPathElement.cpp
  V8SVGPathSeg.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGNumberList.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSeg.cpp)
  V8SVGPathSegArcAbs.cpp
  V8SVGPathSegArcRel.cpp
  V8SVGPathSegClosePath.cpp
  V8SVGPathSegCurvetoCubicAbs.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegArcAbs.cpp)
  V8SVGPathSegCurvetoCubicRel.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegArcRel.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegClosePath.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegCurvetoCubicAbs.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegCurvetoCubicRel.cpp)
  V8SVGPathSegCurvetoCubicSmoothAbs.cpp
  V8SVGPathSegCurvetoCubicSmoothRel.cpp
  V8SVGPathSegCurvetoQuadraticAbs.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegCurvetoCubicSmoothAbs.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegCurvetoCubicSmoothRel.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegCurvetoQuadraticAbs.cpp)
  V8SVGPathSegCurvetoQuadraticRel.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegCurvetoQuadraticRel.cpp)
  V8SVGPathSegCurvetoQuadraticSmoothAbs.cpp
  V8SVGPathSegCurvetoQuadraticSmoothRel.cpp
  V8SVGPathSegLinetoAbs.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegCurvetoQuadraticSmoothAbs.cpp)
  V8SVGPathSegLinetoHorizontalAbs.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegCurvetoQuadraticSmoothRel.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegLinetoAbs.cpp)
  V8SVGPathSegLinetoHorizontalRel.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegLinetoHorizontalAbs.cpp)
  V8SVGPathSegLinetoRel.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegLinetoHorizontalRel.cpp)
  V8SVGPathSegLinetoVerticalAbs.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegLinetoRel.cpp)
  V8SVGPathSegLinetoVerticalRel.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegLinetoVerticalAbs.cpp)
  V8SVGPathSegList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegLinetoVerticalRel.cpp)
  V8SVGPathSegMovetoAbs.cpp
  V8SVGPathSegMovetoRel.cpp
  V8SVGPatternElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegList.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegMovetoAbs.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegMovetoRel.cpp)
  V8SVGPoint.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPatternElement.cpp)
  V8SVGPointList.cpp
  V8SVGPolygonElement.cpp
  V8SVGPolylineElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPoint.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPointList.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPolygonElement.cpp)
  V8SVGPreserveAspectRatio.cpp
  V8SVGRadialGradientElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPolylineElement.cpp)
  V8SVGRect.cpp
  V8SVGRectElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGRadialGradientElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPreserveAspectRatio.cpp)
  V8SVGSVGElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGRect.cpp)
  V8SVGScriptElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGRectElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGSVGElement.cpp)
  V8SVGSetElement.cpp
  V8SVGStopElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGScriptElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGSetElement.cpp)
  V8SVGStringList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGStopElement.cpp)
  V8SVGStyleElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGStringList.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGStyleElement.cpp)
  V8SVGSwitchElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGSwitchElement.cpp)
  V8SVGSymbolElement.cpp
  V8SVGTSpanElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGSymbolElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGTSpanElement.cpp)
  V8SVGTextContentElement.cpp
  V8SVGTextElement.cpp
  V8SVGTextPathElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGTextContentElement.cpp)
  V8SVGTextPositioningElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGTextElement.cpp)
  V8SVGTitleElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGTextPathElement.cpp)
  V8SVGTransform.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGTextPositioningElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGTitleElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGTransform.cpp)
  V8SVGTransformList.cpp
  V8SVGUnitTypes.cpp
  V8SVGUseElement.cpp
  V8SVGViewElement.cpp
  V8SVGViewSpec.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGTransformList.cpp)
  V8SVGZoomEvent.cpp
  V8Screen.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGUnitTypes.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGUseElement.cpp)
  V8ScrollOptions.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGViewElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGViewSpec.cpp)
  V8ScrollState.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGZoomEvent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Screen.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ScrollOptions.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ScrollState.cpp)
  V8ScrollToOptions.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ScrollToOptions.cpp)
  V8SecurityPolicyViolationEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SecurityPolicyViolationEvent.cpp)
  V8SecurityPolicyViolationEventInit.cpp
  V8Selection.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SecurityPolicyViolationEventInit.cpp)
  V8ShadowRoot.cpp
  V8ShadowRootInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Selection.cpp)
  V8SharedArrayBuffer.cpp
  V8SharedWorker.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ShadowRoot.cpp)
  V8StateOptions.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ShadowRootInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SharedArrayBuffer.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SharedWorker.cpp)
  V8Stream.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8StateOptions.cpp)
  V8StringCallback.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Stream.cpp)
  V8StyleMedia.cpp
  V8StyleSheet.cpp
  V8StyleSheetList.cpp
  V8Text.cpp
  V8TextEvent.cpp
  V8TextMetrics.cpp
  V8TextTrack.cpp
  V8TextTrackCue.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8StyleMedia.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8StyleSheet.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8StyleSheetList.cpp)
  V8TextTrackCueList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8StringCallback.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Text.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8TextEvent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8TextMetrics.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8TextTrackCue.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8TextTrack.cpp)
  V8TextTrackList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8TextTrackCueList.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8TextTrackList.cpp)
  V8TimeRanges.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8stylesheet.cpp(185): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8stylesheetlist.cpp(127): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8TimeRanges.cpp)
  V8Touch.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8texttrackcue.cpp(268): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8TouchEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8texttrack.cpp(364): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8TouchList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Touch.cpp)
  V8TrackEvent.cpp
  V8TrackEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8TouchEvent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8TouchList.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8texttracklist.cpp(226): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8TrackEvent.cpp)
  V8TransitionEvent.cpp
  V8TransitionEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8TrackEventInit.cpp)
  V8TreeWalker.cpp
  V8TypeConversions.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8TransitionEvent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8TransitionEventInit.cpp)
  V8UIEvent.cpp
  V8UIEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8TreeWalker.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8TypeConversions.cpp)
  V8URL.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8UIEvent.cpp)
  V8URLSearchParams.cpp
  V8Uint16Array.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8UIEventInit.cpp)
  V8Uint32Array.cpp
  V8Uint8Array.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8URL.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8URLSearchParams.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Uint16Array.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Uint32Array.cpp)
  V8Uint8ClampedArray.cpp
  V8VTTCue.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Uint8Array.cpp)
  V8VTTRegion.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Uint8ClampedArray.cpp)
  V8VTTRegionList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8VTTCue.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8VTTRegion.cpp)
  V8ValidityState.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8VTTRegionList.cpp)
  V8VideoTrack.cpp
  V8VideoTrackList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ValidityState.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8VideoTrack.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8VideoTrackList.cpp)
  V8VoidCallback.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8vttcue.cpp(386): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8WebKitCSSMatrix.cpp
  V8WheelEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8WebKitCSSMatrix.cpp)
  V8WheelEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8WheelEvent.cpp)
  V8Window.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8VoidCallback.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8videotrack.cpp(139): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8WheelEventInit.cpp)
  V8Worker.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8videotracklist.cpp(213): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Window.cpp)
  V8WorkerConsole.cpp
  V8WorkerGlobalScope.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Worker.cpp)
  V8WorkerLocation.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8WorkerConsole.cpp)
  V8WorkerNavigator.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8WorkerGlobalScope.cpp)
  V8WorkerPerformance.cpp
  V8XMLDocument.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8WorkerLocation.cpp)
  V8XMLHttpRequest.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8WorkerNavigator.cpp)
  V8XMLHttpRequestEventTarget.cpp
  V8XMLHttpRequestProgressEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8WorkerPerformance.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XMLDocument.cpp)
  V8XMLHttpRequestUpload.cpp
  V8XMLSerializer.cpp
  V8XPathEvaluator.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XMLHttpRequest.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XMLHttpRequestEventTarget.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XMLHttpRequestProgressEvent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XMLHttpRequestUpload.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XMLSerializer.cpp)
  V8XPathExpression.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathEvaluator.cpp)
  V8XPathNSResolver.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathExpression.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathNSResolver.cpp)
  V8XPathResult.cpp
  V8XSLTProcessor.cpp
  v8sharedworkerglobalscope.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathResult.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XSLTProcessor.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\v8sharedworkerglobalscope.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8window.cpp(470): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
  UnionTypesModulesNone.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8window.cpp(8201): error C2039: “SetHiddenPrototype”: 不是“v8::FunctionTemplate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-template.h(463): note: 参见“v8::FunctionTemplate”的声明
  V8ANGLEInstancedArrays.cpp
  V8Body.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\UnionTypesModulesNone.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8ANGLEInstancedArrays.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Body.cpp)
  V8CHROMIUMSubscribeUniform.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8sharedworkerglobalscope.cpp(81): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8sharedworkerglobalscope.cpp(198): error C2039: “SetHiddenPrototype”: 不是“v8::FunctionTemplate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-template.h(463): note: 参见“v8::FunctionTemplate”的声明
  V8CHROMIUMValuebuffer.cpp
  V8Canvas2DContextAttributes.cpp
  V8CanvasGradient.cpp
  V8CanvasPattern.cpp
  V8CanvasRenderingContext2D.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CHROMIUMSubscribeUniform.cpp)
  V8CloseEvent.cpp
  V8CloseEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CHROMIUMValuebuffer.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Canvas2DContextAttributes.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CanvasGradient.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CanvasPattern.cpp)
  V8CompositorWorker.cpp
  V8CompositorWorkerGlobalScope.cpp
  V8Coordinates.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CanvasRenderingContext2D.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CloseEvent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CloseEventInit.cpp)
  V8Crypto.cpp
  V8DataTransferItemPartial.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CompositorWorker.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CompositorWorkerGlobalScope.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Coordinates.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DataTransferItemPartial.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Crypto.cpp)
  V8DedicatedWorkerGlobalScopePartial.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8angleinstancedarrays.cpp(157): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DedicatedWorkerGlobalScopePartial.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8canvasrenderingcontext2d.cpp(2729): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8chromiumsubscribeuniform.cpp(237): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8DeprecatedStorageInfo.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8compositorworkerglobalscope.cpp(65): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8compositorworkerglobalscope.cpp(228): error C2039: “SetHiddenPrototype”: 不是“v8::FunctionTemplate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-template.h(463): note: 参见“v8::FunctionTemplate”的声明
  V8DeprecatedStorageQuota.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DeprecatedStorageInfo.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DeprecatedStorageQuota.cpp)
  V8DevToolsHostPartial.cpp
  V8EXTBlendMinMax.cpp
  V8EXTFragDepth.cpp
  V8EXTShaderTextureLOD.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DevToolsHostPartial.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTBlendMinMax.cpp)
  V8EXTTextureFilterAnisotropic.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTFragDepth.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTShaderTextureLOD.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8dedicatedworkerglobalscopepartial.cpp(73): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
  V8EXTsRGB.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTTextureFilterAnisotropic.cpp)
  V8Geolocation.cpp
  V8Geoposition.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTsRGB.cpp)
  V8HTMLVideoElementPartial.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Geolocation.cpp)
  V8Headers.cpp
  V8HeadersNone.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Geoposition.cpp)
  V8HitRegionOptions.cpp
  V8MediaSession.cpp
  V8MediaStreamTrackSourcesCallback.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8HTMLVideoElementPartial.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Headers.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8HeadersNone.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8HitRegionOptions.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8MediaSession.cpp)
  V8MimeType.cpp
  V8MimeTypeArray.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8MediaStreamTrackSourcesCallback.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8MimeType.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8extblendminmax.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8MimeTypeArray.cpp)
  V8MouseEventPartial.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8extfragdepth.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8extshadertexturelod.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8NavigatorPartial.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8exttexturefilteranisotropic.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8NavigatorUserMediaError.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8MouseEventPartial.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8extsrgb.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8NavigatorPartial.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8NavigatorUserMediaError.cpp)
  V8NavigatorUserMediaErrorCallback.cpp
  V8NetworkInformation.cpp
  V8OESElementIndexUint.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8NetworkInformation.cpp)
  V8OESStandardDerivatives.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8OESElementIndexUint.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8NavigatorUserMediaErrorCallback.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8OESStandardDerivatives.cpp)
  V8OESTextureFloat.cpp
  V8OESTextureFloatLinear.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8OESTextureFloat.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8OESTextureFloatLinear.cpp)
  V8OESTextureHalfFloat.cpp
  V8OESTextureHalfFloatLinear.cpp
  V8OESVertexArrayObject.cpp
  V8PartialNone.cpp
  V8Path2D.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8OESTextureHalfFloat.cpp)
  V8Plugin.cpp
  V8PluginArray.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8OESTextureHalfFloatLinear.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8OESVertexArrayObject.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Path2D.cpp)
  V8PositionCallback.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8PartialNone.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Plugin.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8PluginArray.cpp)
  V8PositionError.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oeselementindexuint.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8PositionErrorCallback.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8PositionError.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8PositionCallback.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oesstandardderivatives.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8PositionOptions.cpp
  V8Request.cpp
  V8Response.cpp
  V8SharedWorkerGlobalScopePartial.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oestexturefloatlinear.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8PositionOptions.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oestexturefloat.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8SourceBuffer.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Request.cpp)
  V8Storage.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Response.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8SharedWorkerGlobalScopePartial.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8PositionErrorCallback.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oestexturehalffloat.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8SourceBuffer.cpp)
  V8StorageErrorCallback.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oesvertexarrayobject.cpp(127): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oestexturehalffloatlinear.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Storage.cpp)
  V8StorageEvent.cpp
  V8StorageEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8StorageErrorCallback.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8StorageEvent.cpp)
  V8StorageInfo.cpp
  V8StorageQuota.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8sharedworkerglobalscopepartial.cpp(70): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8StorageEventInit.cpp)
  V8StorageQuotaCallback.cpp
  V8StorageUsageCallback.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8StorageInfo.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8StorageQuota.cpp)
  V8TextDecodeOptions.cpp
  V8TextDecoder.cpp
  V8TextDecoderOptions.cpp
  V8TextEncoder.cpp
  V8TrackDefault.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8TextDecoder.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8TextDecodeOptions.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8TextDecoderOptions.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8StorageQuotaCallback.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8TextEncoder.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8TrackDefault.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8StorageUsageCallback.cpp)
  V8TrackDefaultList.cpp
  V8URLPartial.cpp
  V8VideoPlaybackQuality.cpp
  V8WebGL2RenderingContext.cpp
  V8WebGLActiveInfo.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8TrackDefaultList.cpp)
  V8WebGLBuffer.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8URLPartial.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8VideoPlaybackQuality.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8WebGL2RenderingContext.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8WebGLActiveInfo.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8WebGLBuffer.cpp)
  V8WebGLCompressedTextureATC.cpp
  V8WebGLCompressedTextureETC1.cpp
  V8WebGLCompressedTexturePVRTC.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8WebGLCompressedTextureATC.cpp)
  V8WebGLCompressedTextureS3TC.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8WebGLCompressedTextureETC1.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8WebGLCompressedTexturePVRTC.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8WebGLCompressedTextureS3TC.cpp)
  V8WebGLContextAttributes.cpp
  V8WebGLContextEvent.cpp
  V8WebGLContextEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8WebGLContextAttributes.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8WebGLContextEvent.cpp)
  V8WebGLDebugRendererInfo.cpp
  V8WebGLDebugShaders.cpp
  V8WebGLDepthTexture.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8WebGLContextEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8WebGLDebugRendererInfo.cpp)
  V8WebGLDrawBuffers.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8WebGLDebugShaders.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8WebGLDepthTexture.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8webgl2renderingcontext.cpp(9134): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8webgl2renderingcontext.cpp(10525): warning C4838: 从“unsigned int”转换到“int”需要收缩转换
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8webgl2renderingcontext.cpp(10697): warning C4838: 从“unsigned int”转换到“int”需要收缩转换
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8WebGLDrawBuffers.cpp)
  V8WebGLFramebuffer.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8webglcompressedtextureetc1.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8webglcompressedtextureatc.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8webglcompressedtexturepvrtc.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8WebGLFramebuffer.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8webglcompressedtextures3tc.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8WebGLLoseContext.cpp
  V8WebGLProgram.cpp
  V8WebGLQuery.cpp
  V8WebGLRenderbuffer.cpp
  V8WebGLRenderingContext.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8WebGLLoseContext.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8WebGLProgram.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8WebGLQuery.cpp)
  V8WebGLSampler.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8WebGLRenderbuffer.cpp)
  V8WebGLShader.cpp
  V8WebGLShaderPrecisionFormat.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8WebGLRenderingContext.cpp)
  V8WebGLSync.cpp
  V8WebGLTexture.cpp
  V8WebGLTransformFeedback.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8WebGLSampler.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8WebGLShader.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8WebGLShaderPrecisionFormat.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8webgldebugrendererinfo.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8webgldepthtexture.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8webgldebugshaders.cpp(76): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8WebGLSync.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8WebGLTexture.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8WebGLTransformFeedback.cpp)
  V8WebGLUniformLocation.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8webgldrawbuffers.cpp(75): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8WebGLVertexArrayObject.cpp
  V8WebGLVertexArrayObjectOES.cpp
  V8WebSocket.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8WebGLUniformLocation.cpp)
  V8WindowPartial.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8WebGLVertexArrayObject.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8WebGLVertexArrayObjectOES.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8WebSocket.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8WindowPartial.cpp)
  V8WorkerGlobalScopePartial.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8webgllosecontext.cpp(76): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8WorkerGlobalScopePartial.cpp)
  V8WorkerNavigatorPartial.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8webglrenderingcontext.cpp(5478): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  initPartialInterfacesInModules.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8WorkerNavigatorPartial.cpp)
  EventFactoryCreate.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\initPartialInterfacesInModules.cpp)
  HTMLElementFactory.cpp
  HTMLMetaElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\core\EventFactoryCreate.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8workerglobalscopepartial.cpp(65): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
  InspectorInstrumentationImpl.cpp
  SVGElementFactory.cpp
  StyleBuilder.cpp
  StyleBuilderFunctions.cpp
  XPathGrammar.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\core\InspectorInstrumentationImpl.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8windowpartial.cpp(287): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\core\HTMLElementFactory.cpp)
  ComputedTimingProperties.cpp
  KeyframeEffectOptions.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\core\HTMLMetaElement.cpp)
  FontFaceSetLoadEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\core\SVGElementFactory.cpp)
  ElementRegistrationOptions.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\core\XPathGrammar.cpp)
  CompositionEventInit.cpp
  CustomEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\core\StyleBuilder.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\core\StyleBuilderFunctions.cpp)
  ErrorEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\core\dom\ElementRegistrationOptions.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\core\events\CustomEventInit.cpp)
e:\paraplay_svn\out\debug\gen\blink\core\xpathgrammar.cpp(1190): warning C4065: switch 语句包含“default”但是未包含“case”标签 (编译源文件 ..\..\gen\blink\core\XPathGrammar.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\core\animation\ComputedTimingProperties.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\core\animation\KeyframeEffectOptions.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\core\css\FontFaceSetLoadEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\core\events\CompositionEventInit.cpp)
  EventModifierInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\core\events\ErrorEventInit.cpp)
  FocusEventInit.cpp
  KeyboardEventInit.cpp
  MessageEventInit.cpp
  MouseEventInit.cpp
  PointerEventInit.cpp
  PopStateEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\core\events\FocusEventInit.cpp)
  PromiseRejectionEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\core\events\EventModifierInit.cpp)
  RelatedEventInit.cpp
  UIEventInit.cpp
  WheelEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\core\events\PopStateEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\core\events\KeyboardEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\core\events\MessageEventInit.cpp)
  MediaKeyEventInit.cpp
  TrackEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\core\events\MouseEventInit.cpp)
  EventModules.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\core\events\PointerEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\core\events\RelatedEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\core\events\PromiseRejectionEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\core\events\WheelEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\core\events\UIEventInit.cpp)
  HitRegionOptions.cpp
  MediaStreamEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\core\html\track\TrackEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\core\html\MediaKeyEventInit.cpp)
  StorageEventInit.cpp
  debug_impl.cc
  isolate_holder.cc
  v8_platform.cc
  mb2.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\modules\EventModules.cpp)
  WebURLLoaderManager.cpp
  WebSocketChannelImpl.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gin\debug_impl.cc)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gin\public\debug.h(29): error C2061: 语法错误: 标识符“FunctionEntryHook” (编译源文件 ..\..\gin\debug_impl.cc)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gin\isolate_holder.cc)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gin\debug_impl.cc(24): error C2511: “void gin::Debug::SetFunctionEntryHook(FunctionEntryHook)”:“gin::Debug”中没有找到重载的成员函数
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gin\public\debug.h(20): note: 参见“gin::Debug”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gin\v8_platform.cc)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\modules\canvas2d\HitRegionOptions.cpp)
  WebSocketHandshake.cpp
  nodeblink.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\modules\mediastream\MediaStreamEventInit.cpp)
  ActiveDOMCallback.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\modules\storage\StorageEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\node\nodeblink.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\mbvip\core\mb2.cpp)
  ArrayValue.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\net\websocket\WebSocketChannelImpl.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gin\v8_platform.cc(276): error C2039: “GetPageAllocator”: 不是“gin::DefaultPlatformWrap”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_7_5\src\libplatform\default_platform_wrap.h(15): note: 参见“gin::DefaultPlatformWrap”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gin\v8_platform.cc(282): error C2039: “CreateJob”: 不是“gin::DefaultPlatformWrap”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_7_5\src\libplatform\default_platform_wrap.h(15): note: 参见“gin::DefaultPlatformWrap”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\net\WebURLLoaderManager.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\net\websocket\WebSocketHandshake.cpp)
  BindingSecurity.cpp
  CustomElementBinding.cpp
  CustomElementConstructorBuilder.cpp
  DOMWrapperWorld.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\ActiveDOMCallback.cpp)
  Dictionary.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\ArrayValue.cpp)
  DictionaryHelperForCore.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\CustomElementBinding.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\CustomElementConstructorBuilder.cpp)
  ExceptionState.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\DOMWrapperWorld.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\mbvip\core\mb2.cpp(51): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\public\web\webframe.h(52): note: 参见“v8::Object”的声明
  ExceptionStatePlaceholder.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\BindingSecurity.cpp)
  Modulator.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\node\nodeblink.cpp(316): error C2660: “v8::FunctionTemplate::GetFunction”: 函数不接受 0 个参数
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-template.h(495): note: 参见“v8::FunctionTemplate::GetFunction”的声明 (编译源文件 ..\..\node\nodeblink.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\node\nodeblink.cpp(319): error C2661: “v8::Object::Set”: 没有重载函数接受 2 个参数
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\Dictionary.cpp)
  ModuleRecord.cpp
  NPV8Object.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\DictionaryHelperForCore.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\ExceptionState.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\Modulator.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\NPV8Object.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\ModuleRecord.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\ExceptionStatePlaceholder.cpp)
  OnStackObjectChecker.cpp
  PrivateScriptRunner.cpp
  RejectedPromises.cpp
  RetainedDOMInfo.cpp
  ScheduledAction.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\dictionaryhelperforcore.cpp(73): error C2672: “blink::v8Call”: 未找到匹配的重载函数
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\dictionaryhelperforcore.cpp(73): error C2780: “bool blink::v8Call(v8::MaybeLocal<T>,v8::Local<T> &,v8::TryCatch &)”: 应输入 3 个参数，却提供了 2 个
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8bindingmacros.h(79): note: 参见“blink::v8Call”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\dictionaryhelperforcore.cpp(73): error C2780: “bool blink::v8Call(v8::Maybe<T>,T &,v8::TryCatch &)”: 应输入 3 个参数，却提供了 2 个
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8bindingmacros.h(69): note: 参见“blink::v8Call”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\dictionaryhelperforcore.cpp(73): error C2784: “bool blink::v8Call(v8::Maybe<T>,T &)”: 未能从“bool”为“v8::Maybe<T>”推导 模板 参数
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8bindingmacros.h(54): note: 参见“blink::v8Call”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\RetainedDOMInfo.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\retaineddominfo.h(49): error C3668: “blink::RetainedDOMInfo::GetGroupLabel”: 包含重写说明符“override”的方法没有重写任何基类方法 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\RetainedDOMInfo.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\retaineddominfo.h(67): error C3668: “blink::ActiveDOMObjectsInfo::GetGroupLabel”: 包含重写说明符“override”的方法没有重写任何基类方法 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\RetainedDOMInfo.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\PrivateScriptRunner.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\OnStackObjectChecker.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\modulerecord.cpp(98): error C2664: “v8::Maybe<bool> v8::Module::InstantiateModule(v8::Local<v8::Context>,v8::Module::ResolveModuleCallback)”: 无法将参数 2 从“v8::MaybeLocal<v8::Module> (__cdecl *)(v8::Local<v8::Context>,v8::Local<v8::String>,v8::Local<v8::Module>)”转换为“v8::Module::ResolveModuleCallback”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\modulerecord.cpp(98): note: 在匹配目标类型的范围内没有具有该名称的函数
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\modulerecord.cpp(98): error C2248: “v8::Maybe<bool>::Maybe”: 无法访问 private 成员(在“v8::Maybe<bool>”类中声明)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-maybe.h(95): note: 参见“v8::Maybe<bool>::Maybe”的声明
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value.h(422): note: 参见“v8::Maybe<bool>”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\ScheduledAction.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\RejectedPromises.cpp)
  ScriptCallStackFactory.cpp
  ScriptController.cpp
  ScriptEventListener.cpp
  ScriptFunction.cpp
  ScriptFunctionCall.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\rejectedpromises.cpp(51): error C2039: “IsExecutionTerminating”: 不是“v8::V8”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-initialization.h(58): note: 参见“v8::V8”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\rejectedpromises.cpp(51): error C3861: “IsExecutionTerminating”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\ScriptController.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\ScriptEventListener.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\npv8object.cpp(156): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\public\web\webframe.h(52): note: 参见“v8::Object”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\npv8object.cpp(213): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\public\web\webframe.h(52): note: 参见“v8::Object”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\ScriptFunction.cpp)
  ScriptHeapSnapshot.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\ScriptFunctionCall.cpp)
  ScriptProfiler.cpp
  ScriptPromise.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\ScriptCallStackFactory.cpp)
  ScriptPromisePropertyBase.cpp
  ScriptPromiseResolver.cpp
  ScriptRegexp.cpp
  ScriptSourceCode.cpp
  ScriptState.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\ScriptPromise.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\ScriptPromisePropertyBase.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\ScriptPromiseResolver.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\ScriptHeapSnapshot.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\ScriptRegexp.cpp)
  ScriptStreamer.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\ScriptState.cpp)
  ScriptStreamerThread.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\ScriptProfiler.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\retaineddominfo.h(49): error C3668: “blink::RetainedDOMInfo::GetGroupLabel”: 包含重写说明符“override”的方法没有重写任何基类方法 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\ScriptProfiler.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\retaineddominfo.h(67): error C3668: “blink::ActiveDOMObjectsInfo::GetGroupLabel”: 包含重写说明符“override”的方法没有重写任何基类方法 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\ScriptProfiler.cpp)
  ScriptString.cpp
  ScriptValue.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptpromisepropertybase.cpp(94): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptpromisepropertybase.cpp(94): error C2512: “blink::ScriptState::Scope::Scope”: 没有合适的默认构造函数可用
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptpromisepropertybase.cpp(112): error C2039: “CreationContext”: 不是“v8::Promise::Resolver”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-promise.h(32): note: 参见“v8::Promise::Resolver”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptpromisepropertybase.cpp(141): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\ScriptSourceCode.cpp)
  ScriptValueSerializer.cpp
  ScriptWrappable.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\ScriptString.cpp)
  SerializedScriptValue.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptprofiler.cpp(54): error C2039: “GetCpuProfiler”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(21): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptprofiler.cpp(67): error C2039: “GetCpuProfiler”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(21): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptprofiler.cpp(77): error C2039: “GetCpuProfiler”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(21): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptprofiler.cpp(121): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptprofiler.cpp(146): error C3668: “blink::`anonymous-namespace'::ActivityControlAdapter::ReportProgressValue”: 包含重写说明符“override”的方法没有重写任何基类方法
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptprofiler.cpp(239): error C2259: “blink::`anonymous-namespace'::ActivityControlAdapter”: 不能实例化抽象类
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptprofiler.cpp(239): note: 由于下列成员:
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptprofiler.cpp(239): note: “v8::ActivityControl::ControlOption v8::ActivityControl::ReportProgressValue(uint32_t,uint32_t)”: 是抽象的
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-profiler.h(687): note: 参见“v8::ActivityControl::ReportProgressValue”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptprofiler.cpp(259): error C2039: “SetWrapperClassInfoProvider”: 不是“v8::HeapProfiler”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-isolate.h(33): note: 参见“v8::HeapProfiler”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptprofiler.cpp(271): error C2039: “GetCpuProfiler”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(21): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptprofiler.cpp(272): error C2039: “SetIdle”: 不是“v8::CpuProfiler”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-profiler.h(357): note: 参见“v8::CpuProfiler”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\ScriptValue.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\ScriptStreamerThread.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\ScriptStreamer.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\ScriptValueSerializer.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptcontroller.cpp(744): error C2664: “void v8::RegisterExtension(std::unique_ptr<v8::Extension,std::default_delete<_Ty>>)”: 无法将参数 1 从“v8::Extension *”转换为“std::unique_ptr<v8::Extension,std::default_delete<_Ty>>”
          with
          [
              _Ty=v8::Extension
          ]
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptcontroller.cpp(744): note: class“std::unique_ptr<v8::Extension,std::default_delete<_Ty>>”的构造函数声明为“explicit”
          with
          [
              _Ty=v8::Extension
          ]
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptcontroller.cpp(823): error C2039: “SetCaptureStackTraceForUncaughtExceptions”: 不是“v8::V8”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-initialization.h(58): note: 参见“v8::V8”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptcontroller.cpp(823): error C3861: “SetCaptureStackTraceForUncaughtExceptions”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\ScriptWrappable.cpp)
  SerializedScriptValueFactory.cpp
  ToV8.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\SerializedScriptValue.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstring.cpp(72): error C2660: “v8::String::Concat”: 函数不接受 2 个参数
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-primitive.h(446): note: 参见“v8::String::Concat”的声明 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\ScriptString.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstring.cpp(72): error C2440: “<function-style-cast>”: 无法从“v8::Isolate *”转换为“blink::ScriptString”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstring.cpp(72): note: 无构造函数可以接受源类型，或构造函数重载决策不明确
  V8AbstractEventListener.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\SerializedScriptValueFactory.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\ToV8.cpp)
  V8Binding.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptwrappable.cpp(129): error C2664: “void v8::ReturnValue<v8::Value>::Set(uint32_t)”: 无法将参数 1 从“v8::Persistent<v8::Object,v8::NonCopyablePersistentTraits<T>>”转换为“bool”
          with
          [
              T=v8::Object
          ]
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptwrappable.cpp(129): note: 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptwrappable.cpp(142): error C2039: “MarkPartiallyDependent”: 不是“v8::Persistent<v8::Object,v8::NonCopyablePersistentTraits<T>>”的成员
          with
          [
              T=v8::Object
          ]
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(85): note: 参见“v8::Persistent<v8::Object,v8::NonCopyablePersistentTraits<T>>”的声明
          with
          [
              T=v8::Object
          ]
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptwrappable.cpp(144): error C2039: “SetObjectGroupId”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptwrappable.cpp(149): error C2039: “SetReference”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8CustomElementLifecycleCallbacks.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstreamer.cpp(200): error C3668: “blink::SourceStream::SetBookmark”: 包含重写说明符“override”的方法没有重写任何基类方法
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstreamer.cpp(208): error C3668: “blink::SourceStream::ResetToBookmark”: 包含重写说明符“override”的方法没有重写任何基类方法
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstreamer.cpp(494): error C2664: “v8::ScriptCompiler::StreamedSource::StreamedSource(const v8::ScriptCompiler::StreamedSource &)”: 无法将参数 1 从“blink::SourceStream *”转换为“std::unique_ptr<v8::ScriptCompiler::ExternalSourceStream,std::default_delete<_Ty>>”
          with
          [
              _Ty=v8::ScriptCompiler::ExternalSourceStream
          ]
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstreamer.cpp(494): note: class“std::unique_ptr<v8::ScriptCompiler::ExternalSourceStream,std::default_delete<_Ty>>”的构造函数声明为“explicit”
          with
          [
              _Ty=v8::ScriptCompiler::ExternalSourceStream
          ]
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstreamer.cpp(494): error C2672: “adoptPtr”: 未找到匹配的重载函数
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstreamer.cpp(497): error C2039: “StartStreamingScript”: 不是“v8::ScriptCompiler”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-script.h(357): note: 参见“v8::ScriptCompiler”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstreamer.cpp(497): error C3861: “StartStreamingScript”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstreamer.cpp(639): error C2039: “kProduceParserCache”: 不是“v8::ScriptCompiler”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-script.h(357): note: 参见“v8::ScriptCompiler”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstreamer.cpp(639): error C2065: “kProduceParserCache”: 未声明的标识符
  V8DOMActivityLogger.cpp
  V8DOMConfiguration.cpp
  V8DOMWrapper.cpp
  V8ErrorHandler.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8AbstractEventListener.cpp)
  V8EventListener.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8Binding.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\serializedscriptvalue.cpp(127): error C2039: “IsNeuterable”: 不是“v8::ArrayBuffer”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-wasm.h(19): note: 参见“v8::ArrayBuffer”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\serializedscriptvalue.cpp(140): error C2039: “Neuter”: 不是“v8::ArrayBuffer”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-wasm.h(19): note: 参见“v8::ArrayBuffer”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8CustomElementLifecycleCallbacks.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8DOMWrapper.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8DOMConfiguration.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8ErrorHandler.cpp)
  V8EventListenerList.cpp
  V8GCController.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8EventListener.cpp)
  V8GCController_v8_5_7.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptvalueserializer.cpp(100): error C2660: “v8::String::Utf8Length”: 函数不接受 0 个参数
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-primitive.h(143): note: 参见“v8::String::Utf8Length”的声明 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\ScriptValueSerializer.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptvalueserializer.cpp(109): error C2664: “int v8::String::WriteOneByte(v8::Isolate *,uint8_t *,int,int,int) const”: 无法将参数 1 从“uint8_t *”转换为“v8::Isolate *”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptvalueserializer.cpp(109): note: 与指向的类型无关；强制转换要求 reinterpret_cast、C 样式强制转换或函数样式强制转换
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptvalueserializer.cpp(112): error C2664: “int v8::String::WriteUtf8(v8::Isolate *,char *,int,int *,int) const”: 无法将参数 1 从“char *”转换为“v8::Isolate *”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptvalueserializer.cpp(112): note: 与指向的类型无关；强制转换要求 reinterpret_cast、C 样式强制转换或函数样式强制转换
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptvalueserializer.cpp(133): error C2664: “int v8::String::Write(v8::Isolate *,uint16_t *,int,int,int) const”: 无法将参数 1 从“uint16_t *”转换为“v8::Isolate *”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptvalueserializer.cpp(133): note: 与指向的类型无关；强制转换要求 reinterpret_cast、C 样式强制转换或函数样式强制转换
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptvalueserializer.cpp(305): error C2664: “v8::String::Utf8Value::Utf8Value(const v8::String::Utf8Value &)”: 无法将参数 1 从“v8::Local<v8::String>”转换为“const v8::String::Utf8Value &”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptvalueserializer.cpp(305): note: 原因如下: 无法从“v8::Local<v8::String>”转换为“const v8::String::Utf8Value”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptvalueserializer.cpp(305): note: 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptvalueserializer.cpp(861): error C2664: “v8::String::Utf8Value::Utf8Value(const v8::String::Utf8Value &)”: 无法将参数 1 从“v8::Local<v8::String>”转换为“const v8::String::Utf8Value &”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptvalueserializer.cpp(861): note: 原因如下: 无法从“v8::Local<v8::String>”转换为“const v8::String::Utf8Value”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptvalueserializer.cpp(861): note: 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
  V8GCForContextDispose.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8DOMActivityLogger.cpp)
  V8HiddenValue.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8EventListenerList.cpp)
  V8Initializer.cpp
  V8IteratorResultValue.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8HiddenValue.cpp)
  V8LazyEventListener.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8Initializer.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8GCController.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\retaineddominfo.h(49): error C3668: “blink::RetainedDOMInfo::GetGroupLabel”: 包含重写说明符“override”的方法没有重写任何基类方法 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8GCController.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\retaineddominfo.h(67): error C3668: “blink::ActiveDOMObjectsInfo::GetGroupLabel”: 包含重写说明符“override”的方法没有重写任何基类方法 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8GCController.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8IteratorResultValue.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8GCController_v8_5_7.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8GCForContextDispose.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\retaineddominfo.h(49): error C3668: “blink::RetainedDOMInfo::GetGroupLabel”: 包含重写说明符“override”的方法没有重写任何基类方法 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8GCController_v8_5_7.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\retaineddominfo.h(67): error C3668: “blink::ActiveDOMObjectsInfo::GetGroupLabel”: 包含重写说明符“override”的方法没有重写任何基类方法 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8GCController_v8_5_7.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8binding.cpp(118): error C2664: “bool v8::Value::BooleanValue(v8::Isolate *) const”: 无法将参数 1 从“v8::Local<v8::Context>”转换为“v8::Isolate *”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8binding.cpp(118): note: 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8binding.cpp(118): error C2672: “v8Call”: 未找到匹配的重载函数
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8binding.cpp(118): error C2780: “bool blink::v8Call(v8::Maybe<T>,T &)”: 应输入 2 个参数，却提供了 3 个
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8bindingmacros.h(54): note: 参见“blink::v8Call”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8binding.cpp(681): error C2039: “GetEnteredContext”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8binding.cpp(699): error C2039: “GetCallingContext”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8binding.cpp(704): error C2039: “GetEnteredContext”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8binding.cpp(735): error C2039: “GetCallingContext”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8binding.cpp(740): error C2039: “GetEnteredContext”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8binding.cpp(805): error C2039: “IsDead”: 不是“v8::V8”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-initialization.h(58): note: 参见“v8::V8”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8binding.cpp(805): error C3861: “IsDead”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8binding.cpp(1011): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
  V8MutationCallback.cpp
  V8NPObject.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8LazyEventListener.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8NPObject.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8gccontroller_v8_5_7.cpp(85): error C2039: “SetReference”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(21): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8gccontroller_v8_5_7.cpp(143): error C2039: “MarkActive”: 不是“v8::Persistent<v8::Object,v8::NonCopyablePersistentTraits<T>>”的成员
          with
          [
              T=v8::Object
          ]
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(85): note: 参见“v8::Persistent<v8::Object,v8::NonCopyablePersistentTraits<T>>”的声明
          with
          [
              T=v8::Object
          ]
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8gccontroller_v8_5_7.cpp(153): error C2039: “MarkActive”: 不是“v8::Persistent<v8::Object,v8::NonCopyablePersistentTraits<T>>”的成员
          with
          [
              T=v8::Object
          ]
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(85): note: 参见“v8::Persistent<v8::Object,v8::NonCopyablePersistentTraits<T>>”的声明
          with
          [
              T=v8::Object
          ]
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8gccontroller_v8_5_7.cpp(161): error C2039: “MarkActive”: 不是“v8::Persistent<v8::Object,v8::NonCopyablePersistentTraits<T>>”的成员
          with
          [
              T=v8::Object
          ]
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(85): note: 参见“v8::Persistent<v8::Object,v8::NonCopyablePersistentTraits<T>>”的声明
          with
          [
              T=v8::Object
          ]
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8gccontroller_v8_5_7.cpp(169): error C2039: “MarkActive”: 不是“v8::Persistent<v8::Object,v8::NonCopyablePersistentTraits<T>>”的成员
          with
          [
              T=v8::Object
          ]
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(85): note: 参见“v8::Persistent<v8::Object,v8::NonCopyablePersistentTraits<T>>”的声明
          with
          [
              T=v8::Object
          ]
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8gccontroller_v8_5_7.cpp(218): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-profiler.h(26): note: 参见“v8::Object”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8gccontroller_v8_5_7.cpp(220): error C2039: “SetObjectGroupId”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(21): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8gccontroller_v8_5_7.cpp(253): error C2039: “SetObjectGroupId”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(21): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8gccontroller_v8_5_7.cpp(298): error C2039: “SetObjectGroupId”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(21): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8gccontroller_v8_5_7.cpp(327): error C2039: “VisitWeakHandles”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(21): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8gccontroller_v8_5_7.cpp(333): error C2039: “VisitHandlesWithClassIds”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(21): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8gccontroller_v8_5_7.cpp(598): error C3609: “blink::`anonymous-namespace'::DOMWrapperForwardingVisitor::VisitTracedGlobalHandle”: “final”函数必须是虚函数
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8gccontroller_v8_5_7.cpp(651): error C2039: “VisitHandlesWithClassIds”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(21): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8gccontroller_v8_5_7.cpp(690): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-profiler.h(26): note: 参见“v8::Object”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8gccontroller_v8_5_7.cpp(715): error C2039: “VisitHandlesWithClassIds”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(21): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8MutationCallback.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(109): error C2039: “ScriptID”: 不是“v8::ScriptOrigin”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-message.h(62): note: 参见“v8::ScriptOrigin”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(305): error C2039: “ScriptID”: 不是“v8::ScriptOrigin”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-message.h(62): note: 参见“v8::ScriptOrigin”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(431): error C2664: “void v8::Isolate::SetHostImportModuleDynamicallyCallback(v8::HostImportModuleDynamicallyCallback)”: 无法将参数 1 从“v8::MaybeLocal<v8::Promise> (__cdecl *)(v8::Local<v8::Context>,v8::Local<v8::ScriptOrModule>,v8::Local<v8::String>)”转换为“v8::HostImportModuleDynamicallyCallback”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(431): note: 在匹配目标类型的范围内没有具有该名称的函数
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(435): error C2039: “SetLiveEditEnabled”: 不是“v8::Debug”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(351): note: 参见“v8::Debug”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(435): error C3861: “SetLiveEditEnabled”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(437): error C2039: “SetAutorunMicrotasks”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(531): error C2039: “SetFatalErrorHandler”: 不是“v8::V8”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-initialization.h(58): note: 参见“v8::V8”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(531): error C3861: “SetFatalErrorHandler”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(532): error C2039: “AddMessageListener”: 不是“v8::V8”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-initialization.h(58): note: 参见“v8::V8”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(532): error C3861: “AddMessageListener”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(533): error C2039: “SetFailedAccessCheckCallbackFunction”: 不是“v8::V8”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-initialization.h(58): note: 参见“v8::V8”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(533): error C3861: “SetFailedAccessCheckCallbackFunction”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(534): error C2039: “SetAllowCodeGenerationFromStringsCallback”: 不是“v8::V8”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-initialization.h(58): note: 参见“v8::V8”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(534): error C3861: “SetAllowCodeGenerationFromStringsCallback”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(581): error C2039: “IsExecutionTerminating”: 不是“v8::V8”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-initialization.h(58): note: 参见“v8::V8”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(581): error C3861: “IsExecutionTerminating”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(600): error C2039: “AddMessageListener”: 不是“v8::V8”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-initialization.h(58): note: 参见“v8::V8”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(600): error C3861: “AddMessageListener”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(601): error C2039: “SetFatalErrorHandler”: 不是“v8::V8”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-initialization.h(58): note: 参见“v8::V8”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(601): error C3861: “SetFatalErrorHandler”: 找不到标识符
  V8NPUtils.cpp
  V8NodeFilterCondition.cpp
  V8ObjectBuilder.cpp
  V8ObjectConstructor.cpp
  V8PagePopupControllerBinding.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8lazyeventlistener.cpp(209): error C2664: “v8::MaybeLocal<v8::Function> v8::Function::New(v8::Local<v8::Context>,v8::FunctionCallback,v8::Local<v8::Value>,int,v8::ConstructorBehavior,v8::SideEffectType)”: 无法将参数 1 从“v8::Isolate *”转换为“v8::Local<v8::Context>”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8lazyeventlistener.cpp(209): note: 无构造函数可以接受源类型，或构造函数重载决策不明确
  V8PerContextData.cpp
  V8PerIsolateData.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8NodeFilterCondition.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8ObjectBuilder.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8NPUtils.cpp)
  V8RecursionScope.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8ObjectConstructor.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8npobject.cpp(289): error C2660: “v8::Value::ToString”: 函数不接受 0 个参数
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value.h(360): note: 参见“v8::Value::ToString”的声明 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8NPObject.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8npobject.cpp(316): error C2660: “v8::Value::ToString”: 函数不接受 0 个参数
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value.h(360): note: 参见“v8::Value::ToString”的声明 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8NPObject.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8npobject.cpp(353): error C2660: “v8::Value::ToString”: 函数不接受 0 个参数
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value.h(360): note: 参见“v8::Value::ToString”的声明 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8NPObject.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8PagePopupControllerBinding.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8npobject.cpp(477): error C2039: “SetNamedPropertyHandler”: 不是“v8::ObjectTemplate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-template.h(773): note: 参见“v8::ObjectTemplate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8PerContextData.cpp)
  V8ScriptRunner.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8PerIsolateData.cpp)
  V8StringResource.cpp
  V8ThrowException.cpp
  V8ValueCache.cpp
  V8WorkerGlobalScopeEventListener.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8RecursionScope.cpp)
  WindowProxy.cpp
  WindowProxyManager.cpp
  WorkerScriptController.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8nputils.cpp(169): error C2440: “<function-style-cast>”: 无法从“v8::Local<v8::Value>”转换为“v8::String::Utf8Value”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8nputils.cpp(169): note: 无构造函数可以接受源类型，或构造函数重载决策不明确
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8nputils.cpp(169): error C2198: “blink::ExceptionHandler”: 用于调用的参数太少
  WrapperTypeInfo.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8WorkerGlobalScopeEventListener.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\WindowProxy.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8ScriptRunner.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8StringResource.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\unifiedheapcontroller.h(53): error C3609: “blink::UnifiedHeapController::TracePrologue”: “final”函数必须是虚函数 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8PerIsolateData.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\unifiedheapcontroller.h(54): error C3609: “blink::UnifiedHeapController::TraceEpilogue”: “final”函数必须是虚函数 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8PerIsolateData.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\unifiedheapcontroller.h(42): error C2065: “isolate_”: 未声明的标识符 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8PerIsolateData.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\unifiedheapcontroller.h(47): error C2065: “isolate_”: 未声明的标识符 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8PerIsolateData.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8perisolatedata.cpp(292): error C2039: “Callee”: 不是“v8::FunctionCallbackInfo<v8::Value>”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-function-callback.h(251): note: 参见“v8::FunctionCallbackInfo<v8::Value>”的声明
  V8CSSStyleDeclarationCustom.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8ThrowException.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8ValueCache.cpp)
  V8CustomEventCustom.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\WrapperTypeInfo.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\WorkerScriptController.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\WindowProxyManager.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8stringresource.cpp(64): error C2664: “int v8::String::Write(v8::Isolate *,uint16_t *,int,int,int) const”: 无法将参数 1 从“uint16_t *”转换为“v8::Isolate *”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8stringresource.cpp(64): note: 与指向的类型无关；强制转换要求 reinterpret_cast、C 样式强制转换或函数样式强制转换
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8stringresource.cpp(72): error C2664: “int v8::String::WriteOneByte(v8::Isolate *,uint8_t *,int,int,int) const”: 无法将参数 1 从“blink::V8StringOneByteTrait::CharType *”转换为“v8::Isolate *”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8stringresource.cpp(72): note: 与指向的类型无关；强制转换要求 reinterpret_cast、C 样式强制转换或函数样式强制转换
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8pagepopupcontrollerbinding.cpp(40): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8scriptrunner.cpp(104): error C2664: “v8::MaybeLocal<v8::Function> v8::Function::New(v8::Local<v8::Context>,v8::FunctionCallback,v8::Local<v8::Value>,int,v8::ConstructorBehavior,v8::SideEffectType)”: 无法将参数 1 从“v8::Isolate *”转换为“v8::Local<v8::Context>”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8scriptrunner.cpp(104): note: 无构造函数可以接受源类型，或构造函数重载决策不明确
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8scriptrunner.cpp(225): error C2039: “GetCachedData”: 不是“v8::ScriptCompiler::StreamedSource”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-script.h(477): note: 参见“v8::ScriptCompiler::StreamedSource”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8scriptrunner.cpp(282): error C2039: “kConsumeParserCache”: 不是“v8::ScriptCompiler”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-script.h(357): note: 参见“v8::ScriptCompiler”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8scriptrunner.cpp(282): error C2065: “kConsumeParserCache”: 未声明的标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8scriptrunner.cpp(282): error C2039: “kProduceParserCache”: 不是“v8::ScriptCompiler”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-script.h(357): note: 参见“v8::ScriptCompiler”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8scriptrunner.cpp(282): error C2065: “kProduceParserCache”: 未声明的标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8scriptrunner.cpp(297): error C2039: “kProduceCodeCache”: 不是“v8::ScriptCompiler”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-script.h(357): note: 参见“v8::ScriptCompiler”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8scriptrunner.cpp(297): error C2065: “kProduceCodeCache”: 未声明的标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8scriptrunner.cpp(355): error C2664: “v8::ScriptOrigin::ScriptOrigin(v8::ScriptOrigin &&)”: 无法将参数 1 从“v8::Local<v8::String>”转换为“v8::Isolate *”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8scriptrunner.cpp(369): note: 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\custom\V8CSSStyleDeclarationCustom.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8valuecache.cpp(132): error C2039: “MarkIndependent”: 不是“v8::Global<ValueType>”的成员
          with
          [
              ValueType=v8::String
          ]
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8globalvaluemap.h(86): note: 参见“v8::Global<ValueType>”的声明
          with
          [
              ValueType=v8::String
          ]
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8throwexception.cpp(52): error C2039: “IsExecutionTerminating”: 不是“v8::V8”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-initialization.h(58): note: 参见“v8::V8”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8throwexception.cpp(52): error C3861: “IsExecutionTerminating”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8throwexception.cpp(71): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8throwexception.cpp(163): error C2039: “IsExecutionTerminating”: 不是“v8::V8”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-initialization.h(58): note: 参见“v8::V8”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8throwexception.cpp(163): error C3861: “IsExecutionTerminating”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\custom\V8CustomEventCustom.cpp)
  V8CustomXPathNSResolver.cpp
  V8DevToolsHostCustom.cpp
  V8DocumentCustom.cpp
  V8ErrorEventCustom.cpp
  V8EventTargetCustom.cpp
  V8HTMLAllCollectionCustom.cpp
  V8HTMLOptionsCollectionCustom.cpp
  V8HTMLPlugInElementCustom.cpp
  V8InjectedScriptManager.cpp
  V8MessageChannelCustom.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\custom\V8CustomXPathNSResolver.cpp)
  V8MessageEventCustom.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\custom\V8DevToolsHostCustom.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\custom\V8DocumentCustom.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\custom\V8ErrorEventCustom.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\custom\V8EventTargetCustom.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\custom\V8HTMLAllCollectionCustom.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\custom\V8HTMLPlugInElementCustom.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\custom\V8InjectedScriptManager.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\custom\V8HTMLOptionsCollectionCustom.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\custom\V8MessageChannelCustom.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\custom\V8MessageEventCustom.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8customeventcustom.cpp(83): error C2664: “bool v8::Value::BooleanValue(v8::Isolate *) const”: 无法将参数 1 从“v8::Local<v8::Context>”转换为“v8::Isolate *”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8customeventcustom.cpp(83): note: 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8customeventcustom.cpp(83): error C2672: “blink::v8Call”: 未找到匹配的重载函数
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8customeventcustom.cpp(83): error C2780: “bool blink::v8Call(v8::MaybeLocal<T>,v8::Local<T> &,v8::TryCatch &)”: 应输入 3 个参数，却提供了 2 个
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8bindingmacros.h(79): note: 参见“blink::v8Call”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8customeventcustom.cpp(83): error C2780: “bool blink::v8Call(v8::Maybe<T>,T &,v8::TryCatch &)”: 应输入 3 个参数，却提供了 2 个
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8bindingmacros.h(69): note: 参见“blink::v8Call”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8customeventcustom.cpp(84): error C2664: “bool v8::Value::BooleanValue(v8::Isolate *) const”: 无法将参数 1 从“v8::Local<v8::Context>”转换为“v8::Isolate *”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8customeventcustom.cpp(84): note: 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8customeventcustom.cpp(84): error C2672: “blink::v8Call”: 未找到匹配的重载函数
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8customeventcustom.cpp(84): error C2780: “bool blink::v8Call(v8::MaybeLocal<T>,v8::Local<T> &,v8::TryCatch &)”: 应输入 3 个参数，却提供了 2 个
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8bindingmacros.h(79): note: 参见“blink::v8Call”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8customeventcustom.cpp(84): error C2780: “bool blink::v8Call(v8::Maybe<T>,T &,v8::TryCatch &)”: 应输入 3 个参数，却提供了 2 个
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8bindingmacros.h(69): note: 参见“blink::v8Call”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\windowproxy.cpp(391): error C2039: “ForceSet”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
  V8MutationObserverCustom.cpp
  V8PopStateEventCustom.cpp
  V8WindowCustom.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8devtoolshostcustom.cpp(167): error C2039: “GetEnteredContext”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8XMLHttpRequestCustom.cpp
  InspectorWrapper.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\custom\V8MutationObserverCustom.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\custom\V8PopStateEventCustom.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8documentcustom.cpp(70): error C2661: “v8::Object::Get”: 没有重载函数接受 1 个参数
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8messageeventcustom.cpp(106): error C2664: “bool v8::Value::BooleanValue(v8::Isolate *) const”: 无法将参数 1 从“v8::Local<v8::Context>”转换为“v8::Isolate *”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8messageeventcustom.cpp(106): note: 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8messageeventcustom.cpp(106): error C2672: “blink::v8Call”: 未找到匹配的重载函数
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8messageeventcustom.cpp(106): error C2780: “bool blink::v8Call(v8::MaybeLocal<T>,v8::Local<T> &,v8::TryCatch &)”: 应输入 3 个参数，却提供了 2 个
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8bindingmacros.h(79): note: 参见“blink::v8Call”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8messageeventcustom.cpp(106): error C2780: “bool blink::v8Call(v8::Maybe<T>,T &,v8::TryCatch &)”: 应输入 3 个参数，却提供了 2 个
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8bindingmacros.h(69): note: 参见“blink::v8Call”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8messageeventcustom.cpp(107): error C2664: “bool v8::Value::BooleanValue(v8::Isolate *) const”: 无法将参数 1 从“v8::Local<v8::Context>”转换为“v8::Isolate *”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8messageeventcustom.cpp(107): note: 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8messageeventcustom.cpp(107): error C2672: “blink::v8Call”: 未找到匹配的重载函数
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8messageeventcustom.cpp(107): error C2780: “bool blink::v8Call(v8::MaybeLocal<T>,v8::Local<T> &,v8::TryCatch &)”: 应输入 3 个参数，却提供了 2 个
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8bindingmacros.h(79): note: 参见“blink::v8Call”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8messageeventcustom.cpp(107): error C2780: “bool blink::v8Call(v8::Maybe<T>,T &,v8::TryCatch &)”: 应输入 3 个参数，却提供了 2 个
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8bindingmacros.h(69): note: 参见“blink::v8Call”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\custom\V8WindowCustom.cpp)
