﻿C:\Program Files (x86)\Microsoft Visual Studio\2017\Enterprise\Common7\IDE\VC\VCTargets\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
  DevToolsAgent.cpp
  DevToolsClient.cpp
  DragHandle.cpp
  PopupMenuWin.cpp
  WebPluginImpl.cpp
  WebPluginImplWin.cpp
  USVStringOrURLSearchParams.cpp
  V8DevToolsHost.cpp
  V8External.cpp
  V8InspectorOverlayHost.cpp
  V8URLSearchParams.cpp
  V8XMLSerializer.cpp
  V8XPathEvaluator.cpp
  V8XPathExpression.cpp
  V8XPathNSResolver.cpp
  V8XPathResult.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8External.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathExpression.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathNSResolver.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8URLSearchParams.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathEvaluator.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DevToolsHost.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathResult.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8InspectorOverlayHost.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XMLSerializer.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\USVStringOrURLSearchParams.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(167): error C2011: “v8::ObjectCompat”:“class”类型重定义 (编译源文件 ..\..\gen\blink\bindings\core\v8\USVStringOrURLSearchParams.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\USVStringOrURLSearchParams.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\web_impl_win\npapi\WebPluginImplWin.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\USVStringOrURLSearchParams.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\USVStringOrURLSearchParams.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\USVStringOrURLSearchParams.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\USVStringOrURLSearchParams.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\USVStringOrURLSearchParams.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\USVStringOrURLSearchParams.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\USVStringOrURLSearchParams.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\USVStringOrURLSearchParams.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\USVStringOrURLSearchParams.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\web_impl_win\npapi\WebPluginImpl.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(167): error C2011: “v8::ObjectCompat”:“class”类型重定义 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathNSResolver.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathNSResolver.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(167): error C2011: “v8::ObjectCompat”:“class”类型重定义 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathResult.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathResult.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(167): error C2011: “v8::ObjectCompat”:“class”类型重定义 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8InspectorOverlayHost.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8InspectorOverlayHost.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(167): error C2011: “v8::ObjectCompat”:“class”类型重定义 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8External.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8External.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(167): error C2011: “v8::ObjectCompat”:“class”类型重定义 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XMLSerializer.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XMLSerializer.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathNSResolver.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathNSResolver.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathNSResolver.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathNSResolver.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathNSResolver.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathNSResolver.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathNSResolver.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathNSResolver.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathNSResolver.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(167): error C2011: “v8::ObjectCompat”:“class”类型重定义 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathEvaluator.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathEvaluator.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8External.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8External.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8External.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8External.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8External.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8External.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8External.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8External.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8External.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(167): error C2011: “v8::ObjectCompat”:“class”类型重定义 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathExpression.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathExpression.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8InspectorOverlayHost.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8InspectorOverlayHost.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8InspectorOverlayHost.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8InspectorOverlayHost.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8InspectorOverlayHost.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathResult.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathResult.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8InspectorOverlayHost.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8InspectorOverlayHost.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8InspectorOverlayHost.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8InspectorOverlayHost.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathResult.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathResult.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathResult.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathResult.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathResult.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathResult.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathResult.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(167): error C2011: “v8::ObjectCompat”:“class”类型重定义 (编译源文件 ..\..\content\web_impl_win\npapi\WebPluginImplWin.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\content\web_impl_win\npapi\WebPluginImplWin.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathEvaluator.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathEvaluator.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathEvaluator.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathEvaluator.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathEvaluator.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XMLSerializer.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XMLSerializer.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathEvaluator.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XMLSerializer.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathEvaluator.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XMLSerializer.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathEvaluator.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XMLSerializer.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathEvaluator.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XMLSerializer.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XMLSerializer.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XMLSerializer.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XMLSerializer.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathExpression.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathExpression.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathExpression.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathExpression.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathExpression.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathExpression.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathExpression.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathExpression.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathExpression.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\content\web_impl_win\npapi\WebPluginImplWin.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\content\web_impl_win\npapi\WebPluginImplWin.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\content\web_impl_win\npapi\WebPluginImplWin.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\content\web_impl_win\npapi\WebPluginImplWin.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\content\web_impl_win\npapi\WebPluginImplWin.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\content\web_impl_win\npapi\WebPluginImplWin.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\content\web_impl_win\npapi\WebPluginImplWin.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\content\web_impl_win\npapi\WebPluginImplWin.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\content\web_impl_win\npapi\WebPluginImplWin.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(167): error C2011: “v8::ObjectCompat”:“class”类型重定义 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8URLSearchParams.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8URLSearchParams.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\devtools\DevToolsClient.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(167): error C2011: “v8::ObjectCompat”:“class”类型重定义 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DevToolsHost.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DevToolsHost.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(167): error C2011: “v8::ObjectCompat”:“class”类型重定义 (编译源文件 ..\..\content\web_impl_win\npapi\WebPluginImpl.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\content\web_impl_win\npapi\WebPluginImpl.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(167): error C2011: “v8::ObjectCompat”:“class”类型重定义 (编译源文件 ..\..\content\devtools\DevToolsClient.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\content\devtools\DevToolsClient.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8URLSearchParams.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8URLSearchParams.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8URLSearchParams.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8URLSearchParams.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8URLSearchParams.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8URLSearchParams.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8URLSearchParams.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8URLSearchParams.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8URLSearchParams.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\content\web_impl_win\npapi\WebPluginImpl.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\content\web_impl_win\npapi\WebPluginImpl.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\content\web_impl_win\npapi\WebPluginImpl.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\content\web_impl_win\npapi\WebPluginImpl.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\content\web_impl_win\npapi\WebPluginImpl.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\content\web_impl_win\npapi\WebPluginImpl.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\content\web_impl_win\npapi\WebPluginImpl.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\content\web_impl_win\npapi\WebPluginImpl.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DevToolsHost.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DevToolsHost.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\content\web_impl_win\npapi\WebPluginImpl.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DevToolsHost.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DevToolsHost.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DevToolsHost.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DevToolsHost.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DevToolsHost.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DevToolsHost.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DevToolsHost.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\content\devtools\DevToolsClient.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\content\devtools\DevToolsClient.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\content\devtools\DevToolsClient.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\content\devtools\DevToolsClient.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\content\devtools\DevToolsClient.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\content\devtools\DevToolsClient.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\content\devtools\DevToolsClient.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\content\devtools\DevToolsClient.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\content\devtools\DevToolsClient.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\devtools\DevToolsAgent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(167): error C2011: “v8::ObjectCompat”:“class”类型重定义 (编译源文件 ..\..\content\devtools\DevToolsAgent.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\content\devtools\DevToolsAgent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\USVStringOrURLSearchParams.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\USVStringOrURLSearchParams.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\USVStringOrURLSearchParams.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\content\devtools\DevToolsAgent.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\content\devtools\DevToolsAgent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\content\devtools\DevToolsAgent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\content\devtools\DevToolsAgent.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\content\devtools\DevToolsAgent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\content\devtools\DevToolsAgent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\content\devtools\DevToolsAgent.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\content\devtools\DevToolsAgent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\content\devtools\DevToolsAgent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\ui\PopupMenuWin.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\ui\DragHandle.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(167): error C2011: “v8::ObjectCompat”:“class”类型重定义 (编译源文件 ..\..\content\ui\PopupMenuWin.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\content\ui\PopupMenuWin.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(167): error C2011: “v8::ObjectCompat”:“class”类型重定义 (编译源文件 ..\..\content\ui\DragHandle.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\content\ui\DragHandle.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\content\ui\PopupMenuWin.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\content\ui\PopupMenuWin.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\content\ui\PopupMenuWin.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\content\ui\PopupMenuWin.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\content\ui\PopupMenuWin.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\content\ui\PopupMenuWin.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\content\ui\PopupMenuWin.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\content\ui\PopupMenuWin.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\content\ui\PopupMenuWin.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\content\ui\DragHandle.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\content\ui\DragHandle.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\content\ui\DragHandle.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\content\ui\DragHandle.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\content\ui\DragHandle.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\content\ui\DragHandle.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\content\ui\DragHandle.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\content\ui\DragHandle.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\content\ui\DragHandle.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathResult.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathResult.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathResult.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8External.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8External.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8External.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8InspectorOverlayHost.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8InspectorOverlayHost.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8InspectorOverlayHost.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathNSResolver.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathNSResolver.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathNSResolver.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8URLSearchParams.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8URLSearchParams.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8URLSearchParams.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathEvaluator.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathEvaluator.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathEvaluator.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XMLSerializer.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XMLSerializer.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XMLSerializer.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DevToolsHost.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DevToolsHost.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DevToolsHost.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathExpression.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathExpression.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathExpression.cpp)
  V8XSLTProcessor.cpp
  V8ANGLEInstancedArrays.cpp
  V8CHROMIUMSubscribeUniform.cpp
  V8CHROMIUMValuebuffer.cpp
  V8Coordinates.cpp
  V8Crypto.cpp
  V8DataTransferItemPartial.cpp
  V8DevToolsHostPartial.cpp
  V8EXTBlendMinMax.cpp
  V8EXTFragDepth.cpp
  V8EXTShaderTextureLOD.cpp
  V8EXTsRGB.cpp
  V8EXTTextureFilterAnisotropic.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8ANGLEInstancedArrays.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XSLTProcessor.cpp)
  V8Geolocation.cpp
  V8Geoposition.cpp
  V8MediaSession.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DevToolsHostPartial.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CHROMIUMValuebuffer.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DataTransferItemPartial.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Coordinates.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Crypto.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CHROMIUMSubscribeUniform.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTFragDepth.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTBlendMinMax.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTShaderTextureLOD.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTsRGB.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(167): error C2011: “v8::ObjectCompat”:“class”类型重定义 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XSLTProcessor.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XSLTProcessor.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(167): error C2011: “v8::ObjectCompat”:“class”类型重定义 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8ANGLEInstancedArrays.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8ANGLEInstancedArrays.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XSLTProcessor.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XSLTProcessor.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XSLTProcessor.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XSLTProcessor.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XSLTProcessor.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XSLTProcessor.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XSLTProcessor.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XSLTProcessor.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XSLTProcessor.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8ANGLEInstancedArrays.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8ANGLEInstancedArrays.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8ANGLEInstancedArrays.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8ANGLEInstancedArrays.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8ANGLEInstancedArrays.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8ANGLEInstancedArrays.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8ANGLEInstancedArrays.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTTextureFilterAnisotropic.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8ANGLEInstancedArrays.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8ANGLEInstancedArrays.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Geoposition.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Geolocation.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8MediaSession.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XSLTProcessor.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XSLTProcessor.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XSLTProcessor.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8ANGLEInstancedArrays.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8ANGLEInstancedArrays.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8ANGLEInstancedArrays.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(167): error C2011: “v8::ObjectCompat”:“class”类型重定义 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Crypto.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Crypto.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(167): error C2011: “v8::ObjectCompat”:“class”类型重定义 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Coordinates.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Coordinates.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(167): error C2011: “v8::ObjectCompat”:“class”类型重定义 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CHROMIUMValuebuffer.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CHROMIUMValuebuffer.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(167): error C2011: “v8::ObjectCompat”:“class”类型重定义 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DataTransferItemPartial.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DataTransferItemPartial.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(167): error C2011: “v8::ObjectCompat”:“class”类型重定义 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DevToolsHostPartial.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DevToolsHostPartial.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Coordinates.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(167): error C2011: “v8::ObjectCompat”:“class”类型重定义 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CHROMIUMSubscribeUniform.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Coordinates.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CHROMIUMSubscribeUniform.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Coordinates.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Coordinates.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Coordinates.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Coordinates.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Coordinates.cpp)e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Crypto.cpp)
  
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Crypto.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Coordinates.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Coordinates.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Crypto.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Crypto.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Crypto.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Crypto.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Crypto.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Crypto.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Crypto.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DataTransferItemPartial.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DataTransferItemPartial.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DataTransferItemPartial.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DataTransferItemPartial.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DataTransferItemPartial.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DataTransferItemPartial.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DataTransferItemPartial.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DataTransferItemPartial.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DataTransferItemPartial.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CHROMIUMValuebuffer.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CHROMIUMValuebuffer.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CHROMIUMValuebuffer.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CHROMIUMValuebuffer.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CHROMIUMValuebuffer.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(167): error C2011: “v8::ObjectCompat”:“class”类型重定义 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTFragDepth.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTFragDepth.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CHROMIUMValuebuffer.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CHROMIUMValuebuffer.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CHROMIUMValuebuffer.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CHROMIUMValuebuffer.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(167): error C2011: “v8::ObjectCompat”:“class”类型重定义 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTBlendMinMax.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTBlendMinMax.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CHROMIUMSubscribeUniform.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CHROMIUMSubscribeUniform.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CHROMIUMSubscribeUniform.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CHROMIUMSubscribeUniform.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CHROMIUMSubscribeUniform.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CHROMIUMSubscribeUniform.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CHROMIUMSubscribeUniform.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(167): error C2011: “v8::ObjectCompat”:“class”类型重定义 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTShaderTextureLOD.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CHROMIUMSubscribeUniform.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTShaderTextureLOD.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CHROMIUMSubscribeUniform.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DevToolsHostPartial.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DevToolsHostPartial.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DevToolsHostPartial.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DevToolsHostPartial.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DevToolsHostPartial.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DevToolsHostPartial.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DevToolsHostPartial.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DevToolsHostPartial.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DevToolsHostPartial.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(167): error C2011: “v8::ObjectCompat”:“class”类型重定义 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTsRGB.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTsRGB.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTBlendMinMax.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTBlendMinMax.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTBlendMinMax.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTBlendMinMax.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTBlendMinMax.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTBlendMinMax.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTBlendMinMax.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTBlendMinMax.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTBlendMinMax.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTFragDepth.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTFragDepth.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTFragDepth.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTFragDepth.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTFragDepth.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTFragDepth.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTFragDepth.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTFragDepth.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTFragDepth.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTsRGB.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTsRGB.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTsRGB.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTsRGB.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTsRGB.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTsRGB.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTsRGB.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTsRGB.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTsRGB.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTShaderTextureLOD.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTShaderTextureLOD.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTShaderTextureLOD.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTShaderTextureLOD.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTShaderTextureLOD.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTShaderTextureLOD.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTShaderTextureLOD.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTShaderTextureLOD.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTShaderTextureLOD.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(167): error C2011: “v8::ObjectCompat”:“class”类型重定义 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTTextureFilterAnisotropic.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTTextureFilterAnisotropic.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTTextureFilterAnisotropic.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTTextureFilterAnisotropic.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTTextureFilterAnisotropic.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTTextureFilterAnisotropic.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTTextureFilterAnisotropic.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTTextureFilterAnisotropic.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTTextureFilterAnisotropic.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTTextureFilterAnisotropic.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTTextureFilterAnisotropic.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Coordinates.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Coordinates.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Coordinates.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(167): error C2011: “v8::ObjectCompat”:“class”类型重定义 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Geolocation.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Geolocation.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(167): error C2011: “v8::ObjectCompat”:“class”类型重定义 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Geoposition.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Geoposition.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Geolocation.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Geolocation.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Geolocation.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Geolocation.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Geolocation.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Geolocation.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Geolocation.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Geolocation.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Geolocation.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DataTransferItemPartial.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DataTransferItemPartial.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DataTransferItemPartial.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CHROMIUMSubscribeUniform.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CHROMIUMSubscribeUniform.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CHROMIUMSubscribeUniform.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(167): error C2011: “v8::ObjectCompat”:“class”类型重定义 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8MediaSession.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8MediaSession.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Geoposition.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Geoposition.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Geoposition.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Geoposition.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Geoposition.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Geoposition.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Geoposition.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Geoposition.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Geoposition.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CHROMIUMValuebuffer.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CHROMIUMValuebuffer.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CHROMIUMValuebuffer.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Crypto.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Crypto.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Crypto.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8MediaSession.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8MediaSession.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(101): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8MediaSession.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8MediaSession.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8MediaSession.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(110): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8MediaSession.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8MediaSession.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8MediaSession.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstate.h(119): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8MediaSession.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTFragDepth.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTFragDepth.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTFragDepth.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTsRGB.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTsRGB.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTsRGB.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DevToolsHostPartial.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DevToolsHostPartial.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DevToolsHostPartial.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTShaderTextureLOD.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTShaderTextureLOD.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTShaderTextureLOD.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTBlendMinMax.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTBlendMinMax.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTBlendMinMax.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTTextureFilterAnisotropic.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTTextureFilterAnisotropic.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTTextureFilterAnisotropic.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Geolocation.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Geolocation.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Geolocation.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Geoposition.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Geoposition.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Geoposition.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C2027: 使用了未定义类型“v8::ObjectCompat” (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8MediaSession.cpp)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(102): note: 参见“v8::ObjectCompat”的声明 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8MediaSession.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8domwrapper.h(123): error C3861: “CreationContext”: 找不到标识符 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8MediaSession.cpp)
  V8MediaSource.cpp
  V8MediaStream.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8MediaSource.cpp)
  V8MediaStreamEvent.cpp
  V8MediaStreamEventInit.cpp
  V8MediaStreamTrack.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8angleinstancedarrays.cpp(157): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8MediaStreamTrackEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8MediaStream.cpp)
