﻿C:\Program Files (x86)\Microsoft Visual Studio\2017\Enterprise\Common7\IDE\VC\VCTargets\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
  config.cpp
  json_parser.cc
  json_reader.cc
  json_util.cc
  json_writer.cc
  string_piece.cc
  WindowsVersion.cpp
  PageNavController.cpp
  PlatformMessagePortChannel.cpp
  PostTaskHelper.cpp
  DevToolsAgent.cpp
  DevToolsClient.cpp
  DevToolsMgr.cpp
  DevToolsProtocolDispatcher.cpp
  DebuggerScriptSourceJs.cpp
  FullscreenCss.cpp
  InjectedScriptSourceJs.cpp
  InspectorOverlayPageHtml.cpp
  MediaPlayerData.cpp
  ThemeInputMultipleFieldsCss.cpp
  ClipboardUtil.cpp
  DragHandle.cpp
  PopupMenuWin.cpp
  WCDataObject.cpp
  WebDropSource.cpp
  DefaultMediaPlayerFactory.cpp
  WebPluginImeWin.cpp
  WebPluginImpl.cpp
  WebPluginImplWin.cpp
  WebCryptoImpl.cpp
  WebMessagePortChannelImpl.cpp
  USVStringOrURLSearchParams.cpp
  V8DevToolsHost.cpp
  V8External.cpp
  V8InspectorOverlayHost.cpp
  V8URLSearchParams.cpp
  V8XMLSerializer.cpp
  V8XPathEvaluator.cpp
  V8XPathExpression.cpp
  V8XPathNSResolver.cpp
  V8XPathResult.cpp
  V8XSLTProcessor.cpp
  V8AnalyserNode.cpp
  V8ANGLEInstancedArrays.cpp
  V8AudioBuffer.cpp
  V8AudioBufferCallback.cpp
  V8AudioBufferSourceNode.cpp
  V8AudioContext.cpp
  V8AudioDestinationNode.cpp
  V8AudioListener.cpp
  V8AudioNode.cpp
  V8AudioParam.cpp
  V8AudioProcessingEvent.cpp
  V8AudioSourceNode.cpp
  V8BiquadFilterNode.cpp
  V8ChannelMergerNode.cpp
  V8ChannelSplitterNode.cpp
  V8CHROMIUMSubscribeUniform.cpp
  V8CHROMIUMValuebuffer.cpp
  V8ConvolverNode.cpp
  V8Coordinates.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8angleinstancedarrays.cpp(157): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8Crypto.cpp
  V8DataTransferItemPartial.cpp
  V8DelayNode.cpp
  V8DevToolsHostPartial.cpp
  V8DynamicsCompressorNode.cpp
  V8EXTBlendMinMax.cpp
  V8EXTFragDepth.cpp
  V8EXTShaderTextureLOD.cpp
  V8EXTsRGB.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8chromiumsubscribeuniform.cpp(237): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8EXTTextureFilterAnisotropic.cpp
  V8GainNode.cpp
  V8Geolocation.cpp
  V8Geoposition.cpp
  V8MediaElementAudioSourceNode.cpp
  V8MediaSession.cpp
  V8MediaSource.cpp
  V8MediaStream.cpp
  V8MediaStreamAudioDestinationNode.cpp
  V8MediaStreamAudioSourceNode.cpp
  V8MediaStreamEvent.cpp
  V8MediaStreamEventInit.cpp
  V8MediaStreamTrack.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8extblendminmax.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8extfragdepth.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8MediaStreamTrackEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8extshadertexturelod.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8extsrgb.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8exttexturefilteranisotropic.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8MediaStreamTrackSourcesCallback.cpp
  V8NavigatorUserMediaError.cpp
  V8NavigatorUserMediaErrorCallback.cpp
  V8NetworkInformation.cpp
  V8OESElementIndexUint.cpp
  V8OESStandardDerivatives.cpp
  V8OESTextureFloat.cpp
  V8OESTextureFloatLinear.cpp
  V8OESTextureHalfFloat.cpp
  V8OESTextureHalfFloatLinear.cpp
  V8OESVertexArrayObject.cpp
  V8OfflineAudioCompletionEvent.cpp
  V8OfflineAudioContext.cpp
  V8OscillatorNode.cpp
  V8PannerNode.cpp
  V8PeriodicWave.cpp
  V8PositionCallback.cpp
  V8PositionError.cpp
  V8PositionErrorCallback.cpp
  V8PositionOptions.cpp
  V8ScriptProcessorNode.cpp
  V8SourceBuffer.cpp
  V8SourceBufferList.cpp
  V8SourceInfo.cpp
  V8StereoPannerNode.cpp
  V8TrackDefault.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oesstandardderivatives.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8TrackDefaultList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oestexturefloatlinear.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oestexturefloat.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oeselementindexuint.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oesvertexarrayobject.cpp(127): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oestexturehalffloat.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oestexturehalffloatlinear.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8VideoPlaybackQuality.cpp
  V8WaveShaperNode.cpp
  V8WebGL2RenderingContext.cpp
  V8WebGLActiveInfo.cpp
  V8WebGLBuffer.cpp
  V8WebGLCompressedTextureATC.cpp
  V8WebGLCompressedTextureETC1.cpp
  V8WebGLCompressedTexturePVRTC.cpp
  V8WebGLCompressedTextureS3TC.cpp
  V8WebGLContextAttributes.cpp
  V8WebGLContextEvent.cpp
  V8WebGLContextEventInit.cpp
  V8WebGLDebugRendererInfo.cpp
  V8WebGLDebugShaders.cpp
  V8WebGLDepthTexture.cpp
  V8WebGLDrawBuffers.cpp
  V8WebGLFramebuffer.cpp
  V8WebGLLoseContext.cpp
  V8WebGLProgram.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8webgl2renderingcontext.cpp(9134): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8webgl2renderingcontext.cpp(10525): warning C4838: 从“unsigned int”转换到“int”需要收缩转换
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8webgl2renderingcontext.cpp(10697): warning C4838: 从“unsigned int”转换到“int”需要收缩转换
  V8WebGLQuery.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8webglcompressedtextureetc1.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8webglcompressedtextureatc.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8webglcompressedtexturepvrtc.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
