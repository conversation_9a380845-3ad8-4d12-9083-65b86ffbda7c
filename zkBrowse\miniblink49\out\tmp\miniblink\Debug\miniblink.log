﻿C:\Program Files (x86)\Microsoft Visual Studio\2017\Enterprise\Common7\IDE\VC\VCTargets\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
  PlatformEventHandler.cpp
  WebFrameClientImpl.cpp
  WebPage.cpp
  WebPageImpl.cpp
  DevToolsAgent.cpp
  DevToolsClient.cpp
  DragHandle.cpp
  PopupMenuWin.cpp
  BlinkPlatformImpl.cpp
  WebMediaPlayerImpl.cpp
  PluginMessageThrottlerWin.cpp
  PluginPackage.cpp
  PluginPackageWin.cpp
  PluginStream.cpp
  WebPluginImpl.cpp
  WebPluginImplWin.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\web_impl_win\npapi\PluginMessageThrottlerWin.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\web_impl_win\npapi\WebPluginImpl.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\web_impl_win\npapi\PluginStream.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\web_impl_win\npapi\PluginPackageWin.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\browser\PlatformEventHandler.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\browser\WebFrameClientImpl.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\web_impl_win\npapi\PluginPackage.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\web_impl_win\npapi\WebPluginImplWin.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\web_impl_win\BlinkPlatformImpl.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\browser\WebPage.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\devtools\DevToolsAgent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\devtools\DevToolsClient.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\ui\DragHandle.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\ui\PopupMenuWin.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\web_impl_win\WebMediaPlayerImpl.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\browser\WebPageImpl.cpp)
  npapi.cpp
  USVStringOrURLSearchParams.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\USVStringOrURLSearchParams.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\web_impl_win\npapi\npapi.cpp)
  UnionTypesCore.cpp
  V8Animation.cpp
  V8AnimationEffectReadOnly.cpp
  V8AnimationEffectTiming.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Animation.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8AnimationEffectReadOnly.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8AnimationEffectTiming.cpp)
  V8AnimationEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\UnionTypesCore.cpp)
  V8AnimationEventInit.cpp
  V8AnimationPlayerEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8AnimationEvent.cpp)
  V8AnimationPlayerEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8AnimationEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8AnimationPlayerEvent.cpp)
  V8AnimationTimeline.cpp
  V8ApplicationCache.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8AnimationPlayerEventInit.cpp)
  V8ApplicationCacheErrorEvent.cpp
  V8ApplicationCacheErrorEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8AnimationTimeline.cpp)
  V8ArrayBuffer.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ApplicationCache.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ApplicationCacheErrorEvent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ApplicationCacheErrorEventInit.cpp)
  V8ArrayBufferView.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ArrayBuffer.cpp)
  V8Attr.cpp
  V8AudioTrack.cpp
  V8AudioTrackList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ArrayBufferView.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Attr.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8AudioTrack.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8AudioTrackList.cpp)
  V8AutocompleteErrorEvent.cpp
  V8AutocompleteErrorEventInit.cpp
  V8BarProp.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8AutocompleteErrorEvent.cpp)
  V8BeforeUnloadEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8AutocompleteErrorEventInit.cpp)
  V8Blob.cpp
  V8BlobPropertyBag.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8BarProp.cpp)
  V8CDATASection.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8BeforeUnloadEvent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Blob.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8BlobPropertyBag.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CDATASection.cpp)
  V8CSS.cpp
  V8CSSFontFaceRule.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSS.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSFontFaceRule.cpp)
  V8CSSGroupingRule.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSGroupingRule.cpp)
  V8CSSImportRule.cpp
  V8CSSKeyframeRule.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSImportRule.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSKeyframeRule.cpp)
  V8CSSKeyframesRule.cpp
  V8CSSMediaRule.cpp
  V8CSSPageRule.cpp
  V8CSSRule.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSKeyframesRule.cpp)
  V8CSSRuleList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSMediaRule.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSPageRule.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSRule.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSRuleList.cpp)
  V8CSSStyleDeclaration.cpp
  V8CSSStyleRule.cpp
  V8CSSStyleSheet.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSStyleDeclaration.cpp)
  V8CSSSupportsRule.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSStyleRule.cpp)
  V8CSSViewportRule.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSStyleSheet.cpp)
  V8CanvasContextCreationAttributes.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSSupportsRule.cpp)
  V8CharacterData.cpp
  V8ClientRect.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSViewportRule.cpp)
  V8ClientRectList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CanvasContextCreationAttributes.cpp)
  V8ClipboardEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CharacterData.cpp)
  V8Comment.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ClientRect.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ClientRectList.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ClipboardEvent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Comment.cpp)
  V8CompositionEvent.cpp
  V8CompositionEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CompositionEvent.cpp)
  V8CompositorProxy.cpp
  V8ComputedTimingProperties.cpp
  V8Console.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CompositionEventInit.cpp)
  V8ConsoleBase.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CompositorProxy.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ComputedTimingProperties.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Console.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ConsoleBase.cpp)
  V8CustomEvent.cpp
  V8CustomEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CustomEvent.cpp)
  V8DOMError.cpp
  V8DOMException.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CustomEventInit.cpp)
  V8DOMImplementation.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMError.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMException.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMImplementation.cpp)
  V8DOMMatrix.cpp
  V8DOMMatrixReadOnly.cpp
  V8DOMParser.cpp
  V8DOMPoint.cpp
  V8DOMPointInit.cpp
  V8DOMPointReadOnly.cpp
  V8DOMRect.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMMatrix.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMMatrixReadOnly.cpp)
  V8DOMRectReadOnly.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMParser.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMPoint.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMPointInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMRect.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMPointReadOnly.cpp)
  V8DOMSettableTokenList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMRectReadOnly.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMSettableTokenList.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8domimplementation.cpp(178): error C2672: “v8::IsolateCompat::SetReferenceFromGroup”: 未找到匹配的重载函数
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8domimplementation.cpp(178): error C2784: “void v8::IsolateCompat::SetReferenceFromGroup(v8::Isolate *,const v8::Persistent<T,v8::NonCopyablePersistentTraits<T>> &,const v8::Persistent<T,v8::NonCopyablePersistentTraits<T>> &)”: 未能从“v8::UniqueId”为“const v8::Persistent<T,v8::NonCopyablePersistentTraits<T>> &”推导 模板 参数
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(80): note: 参见“v8::IsolateCompat::SetReferenceFromGroup”的声明
  V8DOMStringList.cpp
  V8DOMStringMap.cpp
  V8DOMTokenList.cpp
  V8DataTransfer.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMStringList.cpp)
  V8DataTransferItem.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMStringMap.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DataTransfer.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMTokenList.cpp)
  V8DataTransferItemList.cpp
  V8DataView.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DataTransferItem.cpp)
  V8DedicatedWorkerGlobalScope.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DataTransferItemList.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DataView.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DedicatedWorkerGlobalScope.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8domstringmap.cpp(214): error C2672: “v8::IsolateCompat::SetReferenceFromGroup”: 未找到匹配的重载函数
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8domstringmap.cpp(214): error C2784: “void v8::IsolateCompat::SetReferenceFromGroup(v8::Isolate *,const v8::Persistent<T,v8::NonCopyablePersistentTraits<T>> &,const v8::Persistent<T,v8::NonCopyablePersistentTraits<T>> &)”: 未能从“v8::UniqueId”为“const v8::Persistent<T,v8::NonCopyablePersistentTraits<T>> &”推导 模板 参数
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(80): note: 参见“v8::IsolateCompat::SetReferenceFromGroup”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8domtokenlist.cpp(376): error C2672: “v8::IsolateCompat::SetReferenceFromGroup”: 未找到匹配的重载函数
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8domtokenlist.cpp(376): error C2784: “void v8::IsolateCompat::SetReferenceFromGroup(v8::Isolate *,const v8::Persistent<T,v8::NonCopyablePersistentTraits<T>> &,const v8::Persistent<T,v8::NonCopyablePersistentTraits<T>> &)”: 未能从“v8::UniqueId”为“const v8::Persistent<T,v8::NonCopyablePersistentTraits<T>> &”推导 模板 参数
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(80): note: 参见“v8::IsolateCompat::SetReferenceFromGroup”的声明
  V8DevToolsHost.cpp
  V8Document.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DevToolsHost.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Document.cpp)
  V8DocumentFragment.cpp
  V8DocumentType.cpp
  V8EffectModel.cpp
  V8Element.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DocumentFragment.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DocumentType.cpp)
  V8ElementRegistrationOptions.cpp
  V8ErrorEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8EffectModel.cpp)
  V8ErrorEventInit.cpp
  V8Event.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Element.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ElementRegistrationOptions.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ErrorEvent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ErrorEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Event.cpp)
  V8EventInit.cpp
  V8EventModifierInit.cpp
  V8EventSource.cpp
  V8EventSourceInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8EventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8EventModifierInit.cpp)
  V8EventTarget.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8documenttype.cpp(252): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8EventSource.cpp)
  V8External.cpp
  V8File.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8documentfragment.cpp(409): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8EventSourceInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8document.cpp(6020): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8EventTarget.cpp)
  V8FileError.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8External.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8File.cpp)
  V8FileList.cpp
  V8FilePropertyBag.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FileError.cpp)
  V8FileReader.cpp
  V8FileReaderSync.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8element.cpp(2913): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FileList.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FilePropertyBag.cpp)
  V8Float32Array.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FileReader.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FileReaderSync.cpp)
  V8Float64Array.cpp
  V8FocusEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Float32Array.cpp)
  V8FocusEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Float64Array.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FocusEvent.cpp)
  V8FontFace.cpp
  V8FontFaceDescriptors.cpp
  V8FontFaceSet.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FocusEventInit.cpp)
  V8FontFaceSetForEachCallback.cpp
  V8FontFaceSetLoadEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FontFace.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FontFaceDescriptors.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FontFaceSet.cpp)
  V8FontFaceSetLoadEventInit.cpp
  V8FormData.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FontFaceSetLoadEvent.cpp)
  V8FrameRequestCallback.cpp
  V8GCObservation.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FontFaceSetLoadEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FormData.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FontFaceSetForEachCallback.cpp)
  V8GarbageCollectedScriptWrappable.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8GCObservation.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8fontfaceset.cpp(400): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8GarbageCollectedScriptWrappable.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FrameRequestCallback.cpp)
  V8HTMLAllCollection.cpp
  V8HTMLAnchorElement.cpp
  V8HTMLAppletElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLAllCollection.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLAnchorElement.cpp)
  V8HTMLAreaElement.cpp
  V8HTMLAudioElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLAppletElement.cpp)
  V8HTMLBRElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLAreaElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLAudioElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLBRElement.cpp)
  V8HTMLBaseElement.cpp
  V8HTMLBodyElement.cpp
  V8HTMLButtonElement.cpp
  V8HTMLCanvasElement.cpp
  V8HTMLCollection.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLBaseElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLBodyElement.cpp)
  V8HTMLContentElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLButtonElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLCanvasElement.cpp)
  V8HTMLDListElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8htmlallcollection.cpp(182): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLCollection.cpp)
  V8HTMLDataListElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLContentElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLDListElement.cpp)
  V8HTMLDetailsElement.cpp
  V8HTMLDialogElement.cpp
  V8HTMLDirectoryElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLDataListElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLDialogElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLDetailsElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLDirectoryElement.cpp)
  V8HTMLDivElement.cpp
  V8HTMLDocument.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLDivElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLDocument.cpp)
  V8HTMLElement.cpp
  V8HTMLEmbedElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLElement.cpp)
  V8HTMLFieldSetElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLEmbedElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLFieldSetElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8htmldocument.cpp(320): error C2039: “SetHiddenPrototype”: 不是“v8::FunctionTemplate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-template.h(463): note: 参见“v8::FunctionTemplate”的声明
  V8HTMLFontElement.cpp
  V8HTMLFormControlsCollection.cpp
  V8HTMLFormElement.cpp
  V8HTMLFrameElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLFontElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLFormElement.cpp)
  V8HTMLFrameSetElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLFormControlsCollection.cpp)
  V8HTMLHRElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLFrameElement.cpp)
  V8HTMLHeadElement.cpp
  V8HTMLHeadingElement.cpp
  V8HTMLHtmlElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLHRElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLFrameSetElement.cpp)
  V8HTMLIFrameElement.cpp
  V8HTMLImageElement.cpp
  V8HTMLInputElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLHeadElement.cpp)
  V8HTMLKeygenElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLHeadingElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLHtmlElement.cpp)
