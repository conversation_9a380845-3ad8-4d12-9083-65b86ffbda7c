﻿C:\Program Files (x86)\Microsoft Visual Studio\2017\Enterprise\Common7\IDE\VC\VCTargets\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
  algorithmvc6.cpp
  command_line.cc
  alias.cc
  gdi_debug_util_win.cc
  ostreamvc6.cpp
  memory.cc
  memory_win.cc
  rand_util.cc
  rand_util_win.cc
  string_util.cc
  thread.cc
  time.cc
  time_win.cc
  values.cc
  windowsvc6.cpp
  CheckReEnter.cpp
  PlatformEventHandler.cpp
  WebFrameClientImpl.cpp
  WebPage.cpp
  WebPageImpl.cpp
  HTMLMarqueeElementJs.cpp
  PluginPlaceholderElementJs.cpp
  PrivateScriptRunnerJs.cpp
  calendarPickerCss.cpp
  calendarPickerJs.cpp
  colorSuggestionPickerCss.cpp
  colorSuggestionPickerJs.cpp
  listPickerCss.cpp
  listPickerJs.cpp
  pickerButtonCss.cpp
  pickerCommonCss.cpp
  pickerCommonJs.cpp
  suggestionPickerCss.cpp
  suggestionPickerJs.cpp
  testWebPagePopupImpl.cpp
  BlinkPlatformImpl.cpp
  CurrentTimeImpl.cpp
  WaitableEventWin.cpp
  WebBlobRegistryImpl.cpp
  WebClipboardImpl.cpp
  WebFileUtilitiesImpl.cpp
  WebMediaPlayerImpl.cpp
  WebMimeRegistryImpl.cpp
  WebSchedulerImpl.cpp
  WebThemeEngineImpl.cpp
  WebThreadImpl.cpp
  WebTimerBase.cpp
  WebURLLoaderImpl.cpp
  WebURLLoaderImplCurl.cpp
  PluginDatabase.cpp
  PluginDatabaseWin.cpp
  PluginMainThreadScheduler.cpp
  PluginMessageThrottlerWin.cpp
  PluginPackage.cpp
  PluginPackageWin.cpp
  PluginStream.cpp
  npapi.cpp
  UnionTypesCore.cpp
  V8Animation.cpp
  V8AnimationEffectReadOnly.cpp
  V8AnimationEffectTiming.cpp
  V8AnimationEvent.cpp
  V8AnimationEventInit.cpp
  V8AnimationPlayerEvent.cpp
  V8AnimationPlayerEventInit.cpp
  V8AnimationTimeline.cpp
  V8ApplicationCache.cpp
  V8ApplicationCacheErrorEvent.cpp
  V8ApplicationCacheErrorEventInit.cpp
  V8ArrayBuffer.cpp
  V8ArrayBufferView.cpp
  V8Attr.cpp
  V8AudioTrack.cpp
  V8AudioTrackList.cpp
  V8AutocompleteErrorEvent.cpp
  V8AutocompleteErrorEventInit.cpp
  V8BarProp.cpp
  V8BeforeUnloadEvent.cpp
  V8Blob.cpp
  V8BlobPropertyBag.cpp
  V8CDATASection.cpp
  V8CSS.cpp
  V8CSSFontFaceRule.cpp
  V8CSSGroupingRule.cpp
  V8CSSImportRule.cpp
  V8CSSKeyframeRule.cpp
  V8CSSKeyframesRule.cpp
  V8CSSMediaRule.cpp
  V8CSSPageRule.cpp
  V8CSSRule.cpp
  V8CSSRuleList.cpp
  V8CSSStyleDeclaration.cpp
  V8CSSStyleRule.cpp
  V8CSSStyleSheet.cpp
  V8CSSSupportsRule.cpp
  V8CSSViewportRule.cpp
  V8CanvasContextCreationAttributes.cpp
  V8CharacterData.cpp
  V8ClientRect.cpp
  V8ClientRectList.cpp
  V8ClipboardEvent.cpp
  V8Comment.cpp
  V8CompositionEvent.cpp
  V8CompositionEventInit.cpp
  V8CompositorProxy.cpp
  V8ComputedTimingProperties.cpp
  V8Console.cpp
  V8ConsoleBase.cpp
  V8CustomEvent.cpp
  V8CustomEventInit.cpp
  V8DOMError.cpp
  V8DOMException.cpp
  V8DOMImplementation.cpp
  V8DOMMatrix.cpp
  V8DOMMatrixReadOnly.cpp
  V8DOMParser.cpp
  V8DOMPoint.cpp
  V8DOMPointInit.cpp
  V8DOMPointReadOnly.cpp
  V8DOMRect.cpp
  V8DOMRectReadOnly.cpp
  V8DOMSettableTokenList.cpp
  V8DOMStringList.cpp
  V8DOMStringMap.cpp
  V8DOMTokenList.cpp
  V8DataTransfer.cpp
  V8DataTransferItem.cpp
  V8DataTransferItemList.cpp
  V8DataView.cpp
  V8DedicatedWorkerGlobalScope.cpp
  V8Document.cpp
  V8DocumentFragment.cpp
  V8DocumentType.cpp
  V8EffectModel.cpp
  V8Element.cpp
  V8ElementRegistrationOptions.cpp
  V8ErrorEvent.cpp
  V8ErrorEventInit.cpp
  V8Event.cpp
  V8EventInit.cpp
  V8EventModifierInit.cpp
  V8EventSource.cpp
  V8EventSourceInit.cpp
  V8EventTarget.cpp
  V8File.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8documentfragment.cpp(409): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
  V8FileError.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8document.cpp(6020): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
  V8FileList.cpp
  V8FilePropertyBag.cpp
  V8FileReader.cpp
  V8FileReaderSync.cpp
  V8Float32Array.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8element.cpp(2913): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
  V8Float64Array.cpp
  V8FocusEvent.cpp
  V8FocusEventInit.cpp
  V8FontFace.cpp
  V8FontFaceDescriptors.cpp
  V8FontFaceSet.cpp
  V8FontFaceSetForEachCallback.cpp
  V8FontFaceSetLoadEvent.cpp
  V8FontFaceSetLoadEventInit.cpp
  V8FormData.cpp
  V8FrameRequestCallback.cpp
  V8GCObservation.cpp
  V8GarbageCollectedScriptWrappable.cpp
  V8HTMLAllCollection.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8fontfaceset.cpp(400): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8HTMLAnchorElement.cpp
  V8HTMLAppletElement.cpp
  V8HTMLAreaElement.cpp
  V8HTMLAudioElement.cpp
  V8HTMLBRElement.cpp
  V8HTMLBaseElement.cpp
  V8HTMLBodyElement.cpp
  V8HTMLButtonElement.cpp
  V8HTMLCanvasElement.cpp
  V8HTMLCollection.cpp
  V8HTMLContentElement.cpp
  V8HTMLDListElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8htmlallcollection.cpp(182): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8HTMLDataListElement.cpp
  V8HTMLDetailsElement.cpp
  V8HTMLDialogElement.cpp
  V8HTMLDirectoryElement.cpp
  V8HTMLDivElement.cpp
  V8HTMLDocument.cpp
  V8HTMLElement.cpp
  V8HTMLEmbedElement.cpp
  V8HTMLFieldSetElement.cpp
  V8HTMLFontElement.cpp
  V8HTMLFormControlsCollection.cpp
  V8HTMLFormElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8htmldocument.cpp(320): error C2039: “SetHiddenPrototype”: 不是“v8::FunctionTemplate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-template.h(463): note: 参见“v8::FunctionTemplate”的声明
  V8HTMLFrameElement.cpp
  V8HTMLFrameSetElement.cpp
  V8HTMLHRElement.cpp
  V8HTMLHeadElement.cpp
  V8HTMLHeadingElement.cpp
  V8HTMLHtmlElement.cpp
  V8HTMLIFrameElement.cpp
  V8HTMLImageElement.cpp
  V8HTMLInputElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8htmlformcontrolscollection.cpp(158): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8HTMLKeygenElement.cpp
  V8HTMLLIElement.cpp
  V8HTMLLabelElement.cpp
  V8HTMLLegendElement.cpp
  V8HTMLLinkElement.cpp
  V8HTMLMapElement.cpp
  V8HTMLMarqueeElement.cpp
  V8HTMLMediaElement.cpp
  V8HTMLMenuElement.cpp
  V8HTMLMenuItemElement.cpp
  V8HTMLMetaElement.cpp
  V8HTMLMeterElement.cpp
  V8HTMLModElement.cpp
  V8HTMLOListElement.cpp
  V8HTMLObjectElement.cpp
  V8HTMLOptGroupElement.cpp
  V8HTMLOptionElement.cpp
  V8HTMLOptionsCollection.cpp
  V8HTMLOutputElement.cpp
  V8HTMLParagraphElement.cpp
  V8HTMLParamElement.cpp
  V8HTMLPictureElement.cpp
  V8HTMLPreElement.cpp
  V8HTMLProgressElement.cpp
  V8HTMLQuoteElement.cpp
  V8HTMLScriptElement.cpp
  V8HTMLSelectElement.cpp
  V8HTMLShadowElement.cpp
  V8HTMLSourceElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8htmloptionscollection.cpp(307): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8HTMLSpanElement.cpp
  V8HTMLStyleElement.cpp
  V8HTMLTableCaptionElement.cpp
  V8HTMLTableCellElement.cpp
  V8HTMLTableColElement.cpp
  V8HTMLTableElement.cpp
  V8HTMLTableRowElement.cpp
  V8HTMLTableSectionElement.cpp
  V8HTMLTemplateElement.cpp
  V8HTMLTextAreaElement.cpp
  V8HTMLTitleElement.cpp
  V8HTMLTrackElement.cpp
  V8HTMLUListElement.cpp
  V8HTMLUnknownElement.cpp
  V8HTMLVideoElement.cpp
  V8HashChangeEvent.cpp
  V8HashChangeEventInit.cpp
  V8History.cpp
  V8ImageBitmap.cpp
  V8ImageData.cpp
  V8InputDevice.cpp
  V8InputDeviceInit.cpp
  V8Int16Array.cpp
  V8Int32Array.cpp
  V8Int8Array.cpp
  V8Iterator.cpp
  V8KeyboardEvent.cpp
  V8KeyboardEventInit.cpp
  V8KeyframeEffect.cpp
  V8KeyframeEffectOptions.cpp
  V8LayerRect.cpp
  V8LayerRectList.cpp
  V8Location.cpp
  V8MediaController.cpp
  V8MediaError.cpp
  V8MediaKeyError.cpp
  V8MediaKeyEvent.cpp
  V8MediaKeyEventInit.cpp
  V8MediaList.cpp
  V8MediaQueryList.cpp
  V8MediaQueryListEvent.cpp
  V8MediaQueryListEventInit.cpp
  V8MemoryInfo.cpp
  V8MessageChannel.cpp
  V8MessageEvent.cpp
  V8MessageEventInit.cpp
  V8MessagePort.cpp
  V8MouseEvent.cpp
  V8MouseEventInit.cpp
  V8MutationEvent.cpp
  V8MutationObserver.cpp
  V8MutationObserverInit.cpp
  V8MutationRecord.cpp
  V8NamedNodeMap.cpp
  V8Navigator.cpp
  V8Node.cpp
  V8NodeFilter.cpp
  V8NodeIterator.cpp
  V8NodeList.cpp
  V8PagePopupController.cpp
  V8PageTransitionEvent.cpp
  V8PageTransitionEventInit.cpp
  V8Performance.cpp
  V8PerformanceCompositeTiming.cpp
  V8PerformanceEntry.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8namednodemap.cpp(317): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8PerformanceMark.cpp
  V8PerformanceMeasure.cpp
  V8PerformanceNavigation.cpp
  V8PerformanceRenderTiming.cpp
  V8PerformanceResourceTiming.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8nodelist.cpp(191): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8PerformanceTiming.cpp
  V8PluginPlaceholderElement.cpp
  V8PointerEvent.cpp
  V8PointerEventInit.cpp
  V8PopStateEvent.cpp
  V8PopStateEventInit.cpp
  V8ProcessingInstruction.cpp
  V8ProgressEvent.cpp
  V8ProgressEventInit.cpp
  V8PromiseRejectionEvent.cpp
  V8PromiseRejectionEventInit.cpp
  V8RadioNodeList.cpp
  V8Range.cpp
  V8ReadableByteStream.cpp
  V8ReadableByteStreamReader.cpp
  V8ReadableStream.cpp
  V8ReadableStreamReader.cpp
  V8RefCountedScriptWrappable.cpp
  V8RelatedEvent.cpp
  V8RelatedEventInit.cpp
  V8ResourceProgressEvent.cpp
  V8SVGAElement.cpp
  V8SVGAngle.cpp
  V8SVGAnimateElement.cpp
  V8SVGAnimateMotionElement.cpp
  V8SVGAnimateTransformElement.cpp
  V8SVGAnimatedAngle.cpp
  V8SVGAnimatedBoolean.cpp
  V8SVGAnimatedEnumeration.cpp
  V8SVGAnimatedInteger.cpp
  V8SVGAnimatedLength.cpp
  V8SVGAnimatedLengthList.cpp
  V8SVGAnimatedNumber.cpp
  V8SVGAnimatedNumberList.cpp
  V8SVGAnimatedPreserveAspectRatio.cpp
  V8SVGAnimatedRect.cpp
  V8SVGAnimatedString.cpp
  V8SVGAnimatedTransformList.cpp
  V8SVGAnimationElement.cpp
  V8SVGCircleElement.cpp
  V8SVGClipPathElement.cpp
  V8SVGComponentTransferFunctionElement.cpp
  V8SVGCursorElement.cpp
  V8SVGDefsElement.cpp
  V8SVGDescElement.cpp
  V8SVGDiscardElement.cpp
  V8SVGElement.cpp
  V8SVGEllipseElement.cpp
  V8SVGFEBlendElement.cpp
  V8SVGFEColorMatrixElement.cpp
  V8SVGFEComponentTransferElement.cpp
  V8SVGFECompositeElement.cpp
  V8SVGFEConvolveMatrixElement.cpp
  V8SVGFEDiffuseLightingElement.cpp
  V8SVGFEDisplacementMapElement.cpp
  V8SVGFEDistantLightElement.cpp
  V8SVGFEDropShadowElement.cpp
  V8SVGFEFloodElement.cpp
  V8SVGFEFuncAElement.cpp
  V8SVGFEFuncBElement.cpp
  V8SVGFEFuncGElement.cpp
  V8SVGFEFuncRElement.cpp
  V8SVGFEGaussianBlurElement.cpp
  V8SVGFEImageElement.cpp
  V8SVGFEMergeElement.cpp
  V8SVGFEMergeNodeElement.cpp
  V8SVGFEMorphologyElement.cpp
  V8SVGFEOffsetElement.cpp
  V8SVGFEPointLightElement.cpp
  V8SVGFESpecularLightingElement.cpp
  V8SVGFESpotLightElement.cpp
  V8SVGFETileElement.cpp
  V8SVGFETurbulenceElement.cpp
  V8SVGFilterElement.cpp
  V8SVGForeignObjectElement.cpp
  V8SVGGElement.cpp
  V8SVGGeometryElement.cpp
  V8SVGGradientElement.cpp
  V8SVGGraphicsElement.cpp
  V8SVGImageElement.cpp
  V8SVGLength.cpp
  V8SVGLengthList.cpp
  V8SVGLineElement.cpp
  V8SVGLinearGradientElement.cpp
  V8SVGMPathElement.cpp
  V8SVGMarkerElement.cpp
  V8SVGMaskElement.cpp
  V8SVGMatrix.cpp
  V8SVGMetadataElement.cpp
  V8SVGNumber.cpp
  V8SVGNumberList.cpp
  V8SVGPathElement.cpp
  V8SVGPathSeg.cpp
  V8SVGPathSegArcAbs.cpp
  V8SVGPathSegArcRel.cpp
  V8SVGPathSegClosePath.cpp
  V8SVGPathSegCurvetoCubicAbs.cpp
  V8SVGPathSegCurvetoCubicRel.cpp
  V8SVGPathSegCurvetoCubicSmoothAbs.cpp
  V8SVGPathSegCurvetoCubicSmoothRel.cpp
  V8SVGPathSegCurvetoQuadraticAbs.cpp
  V8SVGPathSegCurvetoQuadraticRel.cpp
  V8SVGPathSegCurvetoQuadraticSmoothAbs.cpp
  V8SVGPathSegCurvetoQuadraticSmoothRel.cpp
  V8SVGPathSegLinetoAbs.cpp
  V8SVGPathSegLinetoHorizontalAbs.cpp
  V8SVGPathSegLinetoHorizontalRel.cpp
  V8SVGPathSegLinetoRel.cpp
  V8SVGPathSegLinetoVerticalAbs.cpp
  V8SVGPathSegLinetoVerticalRel.cpp
  V8SVGPathSegList.cpp
  V8SVGPathSegMovetoAbs.cpp
  V8SVGPathSegMovetoRel.cpp
  V8SVGPatternElement.cpp
  V8SVGPoint.cpp
  V8SVGPointList.cpp
  V8SVGPolygonElement.cpp
  V8SVGPolylineElement.cpp
  V8SVGPreserveAspectRatio.cpp
  V8SVGRadialGradientElement.cpp
  V8SVGRect.cpp
  V8SVGRectElement.cpp
  V8SVGSVGElement.cpp
  V8SVGScriptElement.cpp
  V8SVGSetElement.cpp
  V8SVGStopElement.cpp
  V8SVGStringList.cpp
  V8SVGStyleElement.cpp
  V8SVGSwitchElement.cpp
  V8SVGSymbolElement.cpp
  V8SVGTSpanElement.cpp
  V8SVGTextContentElement.cpp
  V8SVGTextElement.cpp
  V8SVGTextPathElement.cpp
  V8SVGTextPositioningElement.cpp
  V8SVGTitleElement.cpp
  V8SVGTransform.cpp
  V8SVGTransformList.cpp
  V8SVGUnitTypes.cpp
  V8SVGUseElement.cpp
  V8SVGViewElement.cpp
  V8SVGViewSpec.cpp
  V8SVGZoomEvent.cpp
  V8Screen.cpp
  V8ScrollOptions.cpp
  V8ScrollState.cpp
  V8ScrollToOptions.cpp
  V8SecurityPolicyViolationEvent.cpp
  V8SecurityPolicyViolationEventInit.cpp
  V8Selection.cpp
  V8ShadowRoot.cpp
  V8ShadowRootInit.cpp
  V8SharedArrayBuffer.cpp
  V8SharedWorker.cpp
  V8StateOptions.cpp
  V8Stream.cpp
  V8StringCallback.cpp
  V8StyleMedia.cpp
  V8StyleSheet.cpp
  V8StyleSheetList.cpp
  V8Text.cpp
  V8TextEvent.cpp
  V8TextMetrics.cpp
  V8TextTrack.cpp
  V8TextTrackCue.cpp
  V8TextTrackCueList.cpp
  V8TextTrackList.cpp
  V8TimeRanges.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8stylesheet.cpp(185): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8Touch.cpp
  V8TouchEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8stylesheetlist.cpp(127): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8TouchList.cpp
  V8TrackEvent.cpp
  V8TrackEventInit.cpp
  V8TransitionEvent.cpp
  V8TransitionEventInit.cpp
  V8TreeWalker.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8texttrackcue.cpp(268): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8TypeConversions.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8texttrack.cpp(364): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8UIEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8texttracklist.cpp(226): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8UIEventInit.cpp
  V8URL.cpp
  V8Uint16Array.cpp
  V8Uint32Array.cpp
  V8Uint8Array.cpp
  V8Uint8ClampedArray.cpp
  V8VTTCue.cpp
  V8VTTRegion.cpp
  V8VTTRegionList.cpp
  V8ValidityState.cpp
  V8VideoTrack.cpp
  V8VideoTrackList.cpp
  V8VoidCallback.cpp
  V8WebKitCSSMatrix.cpp
  V8WheelEvent.cpp
  V8WheelEventInit.cpp
  V8Window.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8vttcue.cpp(386): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8Worker.cpp
  V8WorkerConsole.cpp
  V8WorkerGlobalScope.cpp
  V8WorkerLocation.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8videotrack.cpp(139): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8WorkerNavigator.cpp
  V8WorkerPerformance.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8videotracklist.cpp(213): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8XMLDocument.cpp
  V8XMLHttpRequest.cpp
  V8XMLHttpRequestEventTarget.cpp
  V8XMLHttpRequestProgressEvent.cpp
  V8XMLHttpRequestUpload.cpp
  v8sharedworkerglobalscope.cpp
  UnionTypesModulesNone.cpp
  V8ANGLEInstancedArrays.cpp
  V8Body.cpp
  V8CHROMIUMSubscribeUniform.cpp
  V8Canvas2DContextAttributes.cpp
  V8CanvasGradient.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8window.cpp(470): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8window.cpp(8201): error C2039: “SetHiddenPrototype”: 不是“v8::FunctionTemplate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-template.h(463): note: 参见“v8::FunctionTemplate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8sharedworkerglobalscope.cpp(81): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8sharedworkerglobalscope.cpp(198): error C2039: “SetHiddenPrototype”: 不是“v8::FunctionTemplate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-template.h(463): note: 参见“v8::FunctionTemplate”的声明
  V8CanvasPattern.cpp
  V8CanvasRenderingContext2D.cpp
  V8CloseEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8angleinstancedarrays.cpp(157): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8CloseEventInit.cpp
  V8CompositorWorker.cpp
  V8CompositorWorkerGlobalScope.cpp
  V8DedicatedWorkerGlobalScopePartial.cpp
  V8DeprecatedStorageInfo.cpp
  V8DeprecatedStorageQuota.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8chromiumsubscribeuniform.cpp(237): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8EXTBlendMinMax.cpp
  V8EXTFragDepth.cpp
  V8EXTShaderTextureLOD.cpp
  V8EXTTextureFilterAnisotropic.cpp
  V8EXTsRGB.cpp
  V8HTMLVideoElementPartial.cpp
  V8Headers.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8canvasrenderingcontext2d.cpp(2729): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8HeadersNone.cpp
  V8HitRegionOptions.cpp
  V8MimeType.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8compositorworkerglobalscope.cpp(65): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8compositorworkerglobalscope.cpp(228): error C2039: “SetHiddenPrototype”: 不是“v8::FunctionTemplate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-template.h(463): note: 参见“v8::FunctionTemplate”的声明
  V8MimeTypeArray.cpp
  V8MouseEventPartial.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8dedicatedworkerglobalscopepartial.cpp(73): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
  V8NavigatorPartial.cpp
  V8OESElementIndexUint.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8extshadertexturelod.cpp(119): fatal error C1004: 发现意外的文件尾
  V8OESStandardDerivatives.cpp
  V8OESTextureFloat.cpp
  V8OESTextureFloatLinear.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8extsrgb.cpp(126): fatal error C1004: 发现意外的文件尾
  V8OESTextureHalfFloat.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8exttexturefilteranisotropic.cpp(20): warning C4067: 预处理器指令后有意外标记 - 应输入换行符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8exttexturefilteranisotropic.cpp(50): error C2018: 未知字符“0x60”
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8exttexturefilteranisotropic.cpp(50): warning C4067: 预处理器指令后有意外标记 - 应输入换行符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8exttexturefilteranisotropic.cpp(115): fatal error C1070: 文件“e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8exttexturefilteranisotropic.cpp”中的 #if/#endif 对不匹配
  V8OESTextureHalfFloatLinear.cpp
  V8OESVertexArrayObject.cpp
  V8PartialNone.cpp
  V8Path2D.cpp
  V8Plugin.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oeselementindexuint.cpp(20): warning C4067: 预处理器指令后有意外标记 - 应输入换行符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oeselementindexuint.cpp(50): error C2018: 未知字符“0x60”
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oeselementindexuint.cpp(50): warning C4067: 预处理器指令后有意外标记 - 应输入换行符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oeselementindexuint.cpp(110): fatal error C1070: 文件“e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oeselementindexuint.cpp”中的 #if/#endif 对不匹配
  V8PluginArray.cpp
  V8Request.cpp
  V8Response.cpp
  V8SharedWorkerGlobalScopePartial.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oesstandardderivatives.cpp(20): warning C4067: 预处理器指令后有意外标记 - 应输入换行符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oesstandardderivatives.cpp(50): error C2018: 未知字符“0x60”
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oesstandardderivatives.cpp(50): warning C4067: 预处理器指令后有意外标记 - 应输入换行符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oesstandardderivatives.cpp(114): fatal error C1070: 文件“e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oesstandardderivatives.cpp”中的 #if/#endif 对不匹配
  V8Storage.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oestexturefloatlinear.cpp(20): warning C4067: 预处理器指令后有意外标记 - 应输入换行符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oestexturefloatlinear.cpp(50): error C2018: 未知字符“0x60”
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oestexturefloatlinear.cpp(50): warning C4067: 预处理器指令后有意外标记 - 应输入换行符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oestexturefloatlinear.cpp(110): fatal error C1070: 文件“e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oestexturefloatlinear.cpp”中的 #if/#endif 对不匹配
  V8StorageErrorCallback.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oestexturefloat.cpp(20): warning C4067: 预处理器指令后有意外标记 - 应输入换行符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oestexturefloat.cpp(50): error C2018: 未知字符“0x60”
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oestexturefloat.cpp(50): warning C4067: 预处理器指令后有意外标记 - 应输入换行符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oestexturefloat.cpp(110): fatal error C1070: 文件“e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oestexturefloat.cpp”中的 #if/#endif 对不匹配
  V8StorageEvent.cpp
  V8StorageEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oestexturehalffloat.cpp(20): warning C4067: 预处理器指令后有意外标记 - 应输入换行符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oestexturehalffloat.cpp(50): error C2018: 未知字符“0x60”
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oestexturehalffloat.cpp(50): warning C4067: 预处理器指令后有意外标记 - 应输入换行符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oestexturehalffloat.cpp(114): fatal error C1070: 文件“e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oestexturehalffloat.cpp”中的 #if/#endif 对不匹配
  V8StorageInfo.cpp
  V8StorageQuota.cpp
  V8StorageQuotaCallback.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oestexturehalffloatlinear.cpp(20): warning C4067: 预处理器指令后有意外标记 - 应输入换行符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oestexturehalffloatlinear.cpp(50): error C2018: 未知字符“0x60”
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oestexturehalffloatlinear.cpp(50): warning C4067: 预处理器指令后有意外标记 - 应输入换行符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oestexturehalffloatlinear.cpp(110): fatal error C1070: 文件“e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oestexturehalffloatlinear.cpp”中的 #if/#endif 对不匹配
  V8StorageUsageCallback.cpp
  V8TextDecodeOptions.cpp
  V8TextDecoder.cpp
  V8TextDecoderOptions.cpp
  V8TextEncoder.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8sharedworkerglobalscopepartial.cpp(70): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
  V8URLPartial.cpp
  V8VideoPlaybackQuality.cpp
  V8WebGL2RenderingContext.cpp
  V8WebGLBuffer.cpp
  V8WebGLCompressedTextureATC.cpp
  V8WebGLCompressedTextureETC1.cpp
  V8WebGLCompressedTexturePVRTC.cpp
  V8WebGLCompressedTextureS3TC.cpp
  V8WebGLContextEvent.cpp
  V8WebGLDebugRendererInfo.cpp
  V8WebGLDebugShaders.cpp
  V8WebGLDepthTexture.cpp
  V8WebGLDrawBuffers.cpp
  V8WebGLFramebuffer.cpp
  V8WebGLLoseContext.cpp
  V8WebGLProgram.cpp
  V8WebGLQuery.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8webglcompressedtextureatc.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8WebGLRenderbuffer.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8webglcompressedtextureetc1.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8WebGLRenderingContext.cpp
  V8WebGLSampler.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8webgl2renderingcontext.cpp(9134): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8webgl2renderingcontext.cpp(10525): warning C4838: 从“unsigned int”转换到“int”需要收缩转换
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8webgl2renderingcontext.cpp(10697): warning C4838: 从“unsigned int”转换到“int”需要收缩转换
  V8WebGLShader.cpp
  V8WebGLShaderPrecisionFormat.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8webglcompressedtexturepvrtc.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8webglcompressedtextures3tc.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8webgldebugrendererinfo.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8WebGLSync.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8webgldepthtexture.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8webgldebugshaders.cpp(76): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8webgldrawbuffers.cpp(75): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8webgllosecontext.cpp(76): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8WebGLTexture.cpp
  V8WebGLTransformFeedback.cpp
  V8WebGLUniformLocation.cpp
  V8WebGLVertexArrayObject.cpp
  V8WebGLVertexArrayObjectOES.cpp
  V8WebSocket.cpp
  V8WindowPartial.cpp
  V8WorkerGlobalScopePartial.cpp
  V8WorkerNavigatorPartial.cpp
  initPartialInterfacesInModules.cpp
  CSSPropertyMetadata.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8webglrenderingcontext.cpp(5478): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  CSSPropertyNames.cpp
  CSSValueKeywords.cpp
  EventFactoryCreate.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\core\css\hashtools.h(35): warning C5033: “register”不再为受支持的存储类 (编译源文件 ..\..\gen\blink\core\CSSPropertyNames.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\core\css\hashtools.h(36): warning C5033: “register”不再为受支持的存储类 (编译源文件 ..\..\gen\blink\core\CSSPropertyNames.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\core\csspropertynames.cpp(1430): warning C5033: “register”不再为受支持的存储类
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\core\csspropertynames.cpp(1461): warning C5033: “register”不再为受支持的存储类
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\core\csspropertynames.cpp(3235): warning C5033: “register”不再为受支持的存储类
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\core\csspropertynames.cpp(3239): warning C5033: “register”不再为受支持的存储类
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\core\csspropertynames.cpp(3243): warning C5033: “register”不再为受支持的存储类
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\core\csspropertynames.cpp(3247): warning C5033: “register”不再为受支持的存储类
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\core\csspropertynames.cpp(3257): warning C5033: “register”不再为受支持的存储类
  EventNames.cpp
  EventTargetNames.cpp
  EventTypeNames.cpp
  HTMLElementFactory.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\core\css\hashtools.h(35): warning C5033: “register”不再为受支持的存储类 (编译源文件 ..\..\gen\blink\core\CSSValueKeywords.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\core\css\hashtools.h(36): warning C5033: “register”不再为受支持的存储类 (编译源文件 ..\..\gen\blink\core\CSSValueKeywords.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\core\cssvaluekeywords.cpp(1655): warning C5033: “register”不再为受支持的存储类
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\core\cssvaluekeywords.cpp(1686): warning C5033: “register”不再为受支持的存储类
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\core\cssvaluekeywords.cpp(3422): warning C5033: “register”不再为受支持的存储类
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\core\cssvaluekeywords.cpp(4942): warning C5033: “register”不再为受支持的存储类
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\core\cssvaluekeywords.cpp(4946): warning C5033: “register”不再为受支持的存储类
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\core\cssvaluekeywords.cpp(4950): warning C5033: “register”不再为受支持的存储类
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\core\cssvaluekeywords.cpp(4960): warning C5033: “register”不再为受支持的存储类
  HTMLElementLookupTrie.cpp
  HTMLEntityTable.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8workerglobalscopepartial.cpp(65): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
  HTMLMetaElement.cpp
  HTMLNames.cpp
  HTMLTokenizerNames.cpp
  InputTypeNames.cpp
  InspectorBackendDispatcher.cpp
  InspectorFrontend.cpp
  InspectorInstrumentationImpl.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8windowpartial.cpp(287): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
  InspectorTypeBuilder.cpp
  MathMLNames.cpp
  MediaFeatureNames.cpp
  MediaTypeNames.cpp
  SVGElementFactory.cpp
  SVGNames.cpp
  StyleBuilder.cpp
  StyleBuilderFunctions.cpp
  StylePropertyShorthand.cpp
  UserAgentStyleSheetsData.cpp
  XLinkNames.cpp
  XMLNSNames.cpp
  XMLNames.cpp
  XPathGrammar.cpp
  ComputedTimingProperties.cpp
  KeyframeEffectOptions.cpp
  FontFaceDescriptors.cpp
  FontFaceSetLoadEventInit.cpp
  MediaQueryListEventInit.cpp
  DOMPointInit.cpp
  ElementRegistrationOptions.cpp
  MutationObserverInit.cpp
  ShadowRootInit.cpp
  AnimationEventInit.cpp
  AnimationPlayerEventInit.cpp
  ApplicationCacheErrorEventInit.cpp
e:\paraplay_svn\out\debug\gen\blink\core\xpathgrammar.cpp(1190): warning C4065: switch 语句包含“default”但是未包含“case”标签 (编译源文件 ..\..\gen\blink\core\XPathGrammar.cpp)
  AutocompleteErrorEventInit.cpp
  CompositionEventInit.cpp
  CustomEventInit.cpp
  ErrorEventInit.cpp
  EventInit.cpp
  EventModifierInit.cpp
  FocusEventInit.cpp
  HashChangeEventInit.cpp
  KeyboardEventInit.cpp
  MessageEventInit.cpp
  MouseEventInit.cpp
  PageTransitionEventInit.cpp
  PointerEventInit.cpp
  PopStateEventInit.cpp
  ProgressEventInit.cpp
  PromiseRejectionEventInit.cpp
  RelatedEventInit.cpp
  SecurityPolicyViolationEventInit.cpp
  TransitionEventInit.cpp
  UIEventInit.cpp
  WheelEventInit.cpp
  FetchInitiatorTypeNames.cpp
  BlobPropertyBag.cpp
  FilePropertyBag.cpp
  ScrollOptions.cpp
  ScrollToOptions.cpp
  StateOptions.cpp
  MediaKeyEventInit.cpp
  CanvasContextCreationAttributes.cpp
  TrackEventInit.cpp
  InputDeviceInit.cpp
  EventSourceInit.cpp
  EventModules.cpp
  EventModulesNames.cpp
  EventTargetModulesNames.cpp
  Canvas2DContextAttributes.cpp
  HitRegionOptions.cpp
  TextDecodeOptions.cpp
  TextDecoderOptions.cpp
  PositionOptions.cpp
  MediaStreamEventInit.cpp
  StorageEventInit.cpp
  WebGLContextAttributes.cpp
  WebGLContextEventInit.cpp
  CloseEventInit.cpp
  ColorData.cpp
  FontFamilyNames.cpp
  RuntimeEnabledFeatures.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\build\miniblink\colordata.gperf(27): warning C5033: “register”不再为受支持的存储类 (编译源文件 ..\..\gen\blink\platform\ColorData.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\build\miniblink\colordata.gperf(58): warning C5033: “register”不再为受支持的存储类 (编译源文件 ..\..\gen\blink\platform\ColorData.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\build\miniblink\colordata.gperf(247): warning C5033: “register”不再为受支持的存储类 (编译源文件 ..\..\gen\blink\platform\ColorData.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\build\miniblink\colordata.gperf(251): warning C5033: “register”不再为受支持的存储类 (编译源文件 ..\..\gen\blink\platform\ColorData.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\build\miniblink\colordata.gperf(255): warning C5033: “register”不再为受支持的存储类 (编译源文件 ..\..\gen\blink\platform\ColorData.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\build\miniblink\colordata.gperf(259): warning C5033: “register”不再为受支持的存储类 (编译源文件 ..\..\gen\blink\platform\ColorData.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\build\miniblink\colordata.gperf(172): warning C5033: “register”不再为受支持的存储类 (编译源文件 ..\..\gen\blink\platform\ColorData.cpp)
  array_buffer.cc
  debug_impl.cc
  isolate_holder.cc
  v8_initializer.cc
  v8_platform.cc
  BindJsQuery.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gin\public\debug.h(29): error C2039: “FunctionEntryHook”: 不是“`global namespace'”的成员 (编译源文件 ..\..\gin\debug_impl.cc)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gin\public\debug.h(29): error C2061: 语法错误: 标识符“FunctionEntryHook” (编译源文件 ..\..\gin\debug_impl.cc)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gin\debug_impl.cc(24): error C2511: “void gin::Debug::SetFunctionEntryHook(FunctionEntryHook)”:“gin::Debug”中没有找到重载的成员函数
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gin\public\debug.h(20): note: 参见“gin::Debug”的声明
  LiveIdDetect.cpp
  StringUtil.cpp
  ThreadCall.cpp
  MbWebView.cpp
  mb.cpp
  mb2.cpp
  SimpleDownload.cpp
  PdfViewerPlugin.cpp
  PdfViewerPluginFunc.cpp
  PdfiumLoad.cpp
  Printing.cpp
  WkePrinting.cpp
  AnimationCurve.cpp
  AnimationIdProvider.cpp
  AnimationObj.cpp
  FilterOperationsWrap.cpp
  KeyframedAnimationCurve.cpp
  LayerAnimationController.cpp
  ScrollOffsetAnimationCurve.cpp
  TimingFunctionMc.cpp
  TransformOperationMc.cpp
  TransformOperationsMc.cpp
  CubicBezier.cpp
  FloatBox.cpp
  MathUtil.cpp
  TransformUtil.cpp
  Tween.cpp
  WebAnimationCurveCommon.cpp
  WebAnimationImpl.cpp
  WebCompositorAnimationPlayerImpl.cpp
  WebCompositorAnimationTimelineImpl.cpp
  WebCompositorSupportImpl.cpp
  WebContentLayerImpl.cpp
  WebFilterAnimationCurveImpl.cpp
  WebFilterOperationsImpl.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\mbvip\core\mb2.cpp(51): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\public\web\webframe.h(52): note: 参见“v8::Object”的声明
  WebFloatAnimationCurveImpl.cpp
  WebImageLayerImpl.cpp
  WebLayerImpl.cpp
  WebScrollOffsetAnimationCurveImpl.cpp
  WebScrollbarLayerImpl.cpp
  WebToCcAnimationDelegateAdapter.cpp
  WebTransformAnimationCurveImpl.cpp
  WebTransformOperationsImpl.cpp
  CompositingLayer.cpp
  LayerChangeAction.cpp
  TileActionInfo.cpp
  RasterFilters.cpp
  RasterResouce.cpp
  RasterTask.cpp
  CompositingTile.cpp
  Tile.cpp
  TileGrid.cpp
  ActionsFrameGroup.cpp
  LayerSorter.cc
  LayerTreeHost.cpp
  ActivatingObjCheck.cpp
  BlobResourceLoader.cpp
  CurlCacheEntry.cpp
  CurlCacheManager.cpp
  DataURL.cpp
  DefaultFullPath.cpp
  FileStream.cpp
  FileSystem.cpp
  FileSystemWin.cpp
  FixedReceivedData.cpp
  MultipartHandle.cpp
  PageNetExtraData.cpp
  PathWalker.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\net\mergevec.h(99): warning C4018: “>=”: 有符号/无符号不匹配 (编译源文件 ..\..\net\CurlCacheEntry.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\net\mergevec.h(162): warning C4018: “<”: 有符号/无符号不匹配 (编译源文件 ..\..\net\CurlCacheEntry.cpp)
  SharedMemoryDataConsumerHandle.cpp
  StorageMgr.cpp
  WebStorageAreaImpl.cpp
  WebStorageNamespaceImpl.cpp
  WebURLLoaderManager.cpp
  WebURLLoaderManagerUtil.cpp
  WebURLLoaderWinINet.cpp
  CanonicalCookie.cpp
  CookieJarMgr.cpp
  CookieMonster.cpp
  CookieUtil.cpp
  ParsedCookie.cpp
  WebCookieJarCurlImpl.cpp
  SocketStreamErrorBase.cpp
  SocketStreamHandleBase.cpp
  SocketStreamHandleCurl.cpp
  WebSocketChannelImpl.cpp
  WebSocketDeflateFramer.cpp
  WebSocketDeflater.cpp
  WebSocketExtensionDispatcher.cpp
  WebSocketExtensionParser.cpp
  WebSocketHandshake.cpp
  WebSocketOneFrame.cpp
  nodeblink.cpp
  SkMemory_new_handler.cpp
  analysis_canvas.cc
  bitmap_platform_device_win.cc
  fontmgr_default_win.cc
  lazy_pixel_ref.cc
  paint_simplifier.cc
  platform_canvas.cc
  platform_device.cc
  platform_device_win.cc
  skia_utils_win.cc
  ActiveDOMCallback.cpp
  ArrayValue.cpp
  BindingSecurity.cpp
  CustomElementBinding.cpp
  CustomElementConstructorBuilder.cpp
  DOMWrapperWorld.cpp
  Dictionary.cpp
  DictionaryHelperForCore.cpp
  ExceptionMessages.cpp
  ExceptionState.cpp
  ExceptionStatePlaceholder.cpp
  Modulator.cpp
  ModuleProxy.cpp
  ModuleRecord.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\node\nodeblink.cpp(316): error C2660: “v8::FunctionTemplate::GetFunction”: 函数不接受 0 个参数
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-template.h(495): note: 参见“v8::FunctionTemplate::GetFunction”的声明 (编译源文件 ..\..\node\nodeblink.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\node\nodeblink.cpp(319): error C2661: “v8::Object::Set”: 没有重载函数接受 2 个参数
  NPV8Object.cpp
  OnStackObjectChecker.cpp
  PrivateScriptRunner.cpp
  RejectedPromises.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\dictionaryhelperforcore.cpp(73): error C2672: “blink::v8Call”: 未找到匹配的重载函数
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\dictionaryhelperforcore.cpp(73): error C2780: “bool blink::v8Call(v8::MaybeLocal<T>,v8::Local<T> &,v8::TryCatch &)”: 应输入 3 个参数，却提供了 2 个
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8bindingmacros.h(79): note: 参见“blink::v8Call”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\dictionaryhelperforcore.cpp(73): error C2780: “bool blink::v8Call(v8::Maybe<T>,T &,v8::TryCatch &)”: 应输入 3 个参数，却提供了 2 个
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8bindingmacros.h(69): note: 参见“blink::v8Call”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\dictionaryhelperforcore.cpp(73): error C2784: “bool blink::v8Call(v8::Maybe<T>,T &)”: 未能从“bool”为“v8::Maybe<T>”推导 模板 参数
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8bindingmacros.h(54): note: 参见“blink::v8Call”的声明
  RetainedDOMInfo.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\modulerecord.cpp(98): error C2664: “v8::Maybe<bool> v8::Module::InstantiateModule(v8::Local<v8::Context>,v8::Module::ResolveModuleCallback)”: 无法将参数 2 从“v8::MaybeLocal<v8::Module> (__cdecl *)(v8::Local<v8::Context>,v8::Local<v8::String>,v8::Local<v8::Module>)”转换为“v8::Module::ResolveModuleCallback”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\modulerecord.cpp(98): note: 在匹配目标类型的范围内没有具有该名称的函数
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\modulerecord.cpp(98): error C2248: “v8::Maybe<bool>::Maybe”: 无法访问 private 成员(在“v8::Maybe<bool>”类中声明)
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-maybe.h(95): note: 参见“v8::Maybe<bool>::Maybe”的声明
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value.h(422): note: 参见“v8::Maybe<bool>”的声明
  ScheduledAction.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\retaineddominfo.h(49): error C3668: “blink::RetainedDOMInfo::GetGroupLabel”: 包含重写说明符“override”的方法没有重写任何基类方法 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\RetainedDOMInfo.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\retaineddominfo.h(67): error C3668: “blink::ActiveDOMObjectsInfo::GetGroupLabel”: 包含重写说明符“override”的方法没有重写任何基类方法 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\RetainedDOMInfo.cpp)
  ScriptCallStackFactory.cpp
  ScriptController.cpp
  ScriptEventListener.cpp
  ScriptFunction.cpp
  ScriptFunctionCall.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\rejectedpromises.cpp(51): error C2039: “IsExecutionTerminating”: 不是“v8::V8”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-initialization.h(58): note: 参见“v8::V8”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\rejectedpromises.cpp(51): error C3861: “IsExecutionTerminating”: 找不到标识符
  ScriptHeapSnapshot.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\npv8object.cpp(156): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\public\web\webframe.h(52): note: 参见“v8::Object”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\npv8object.cpp(213): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\public\web\webframe.h(52): note: 参见“v8::Object”的声明
  ScriptProfiler.cpp
  ScriptPromise.cpp
  ScriptPromisePropertyBase.cpp
  ScriptPromiseResolver.cpp
  ScriptRegexp.cpp
  ScriptSourceCode.cpp
  ScriptState.cpp
  ScriptStreamer.cpp
  ScriptStreamerThread.cpp
  ScriptString.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\retaineddominfo.h(49): error C3668: “blink::RetainedDOMInfo::GetGroupLabel”: 包含重写说明符“override”的方法没有重写任何基类方法 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\ScriptProfiler.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\retaineddominfo.h(67): error C3668: “blink::ActiveDOMObjectsInfo::GetGroupLabel”: 包含重写说明符“override”的方法没有重写任何基类方法 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\ScriptProfiler.cpp)
  ScriptValue.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptpromisepropertybase.cpp(94): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptpromisepropertybase.cpp(94): error C2512: “blink::ScriptState::Scope::Scope”: 没有合适的默认构造函数可用
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptpromisepropertybase.cpp(112): error C2039: “CreationContext”: 不是“v8::Promise::Resolver”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-promise.h(32): note: 参见“v8::Promise::Resolver”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptpromisepropertybase.cpp(141): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
  ScriptValueSerializer.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptcontroller.cpp(744): error C2664: “void v8::RegisterExtension(std::unique_ptr<v8::Extension,std::default_delete<_Ty>>)”: 无法将参数 1 从“v8::Extension *”转换为“std::unique_ptr<v8::Extension,std::default_delete<_Ty>>”
          with
          [
              _Ty=v8::Extension
          ]
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptcontroller.cpp(744): note: class“std::unique_ptr<v8::Extension,std::default_delete<_Ty>>”的构造函数声明为“explicit”
          with
          [
              _Ty=v8::Extension
          ]
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptcontroller.cpp(823): error C2039: “SetCaptureStackTraceForUncaughtExceptions”: 不是“v8::V8”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-initialization.h(58): note: 参见“v8::V8”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptcontroller.cpp(823): error C3861: “SetCaptureStackTraceForUncaughtExceptions”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptprofiler.cpp(54): error C2039: “GetCpuProfiler”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(21): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptprofiler.cpp(67): error C2039: “GetCpuProfiler”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(21): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptprofiler.cpp(77): error C2039: “GetCpuProfiler”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(21): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptprofiler.cpp(121): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptprofiler.cpp(146): error C3668: “blink::`anonymous-namespace'::ActivityControlAdapter::ReportProgressValue”: 包含重写说明符“override”的方法没有重写任何基类方法
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptprofiler.cpp(239): error C2259: “blink::`anonymous-namespace'::ActivityControlAdapter”: 不能实例化抽象类
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptprofiler.cpp(239): note: 由于下列成员:
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptprofiler.cpp(239): note: “v8::ActivityControl::ControlOption v8::ActivityControl::ReportProgressValue(uint32_t,uint32_t)”: 是抽象的
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-profiler.h(687): note: 参见“v8::ActivityControl::ReportProgressValue”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptprofiler.cpp(259): error C2039: “SetWrapperClassInfoProvider”: 不是“v8::HeapProfiler”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-isolate.h(33): note: 参见“v8::HeapProfiler”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptprofiler.cpp(271): error C2039: “GetCpuProfiler”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(21): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptprofiler.cpp(272): error C2039: “SetIdle”: 不是“v8::CpuProfiler”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-profiler.h(357): note: 参见“v8::CpuProfiler”的声明
  ScriptWrappable.cpp
  SerializedScriptValue.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstring.cpp(72): error C2660: “v8::String::Concat”: 函数不接受 2 个参数
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-primitive.h(446): note: 参见“v8::String::Concat”的声明 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\ScriptString.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstring.cpp(72): error C2440: “<function-style-cast>”: 无法从“v8::Isolate *”转换为“blink::ScriptString”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstring.cpp(72): note: 无构造函数可以接受源类型，或构造函数重载决策不明确
  SerializedScriptValueFactory.cpp
  ToV8.cpp
  V8AbstractEventListener.cpp
  V8Binding.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstreamer.cpp(200): error C3668: “blink::SourceStream::SetBookmark”: 包含重写说明符“override”的方法没有重写任何基类方法
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstreamer.cpp(208): error C3668: “blink::SourceStream::ResetToBookmark”: 包含重写说明符“override”的方法没有重写任何基类方法
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstreamer.cpp(494): error C2664: “v8::ScriptCompiler::StreamedSource::StreamedSource(const v8::ScriptCompiler::StreamedSource &)”: 无法将参数 1 从“blink::SourceStream *”转换为“std::unique_ptr<v8::ScriptCompiler::ExternalSourceStream,std::default_delete<_Ty>>”
          with
          [
              _Ty=v8::ScriptCompiler::ExternalSourceStream
          ]
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstreamer.cpp(494): note: class“std::unique_ptr<v8::ScriptCompiler::ExternalSourceStream,std::default_delete<_Ty>>”的构造函数声明为“explicit”
          with
          [
              _Ty=v8::ScriptCompiler::ExternalSourceStream
          ]
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstreamer.cpp(494): error C2672: “adoptPtr”: 未找到匹配的重载函数
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstreamer.cpp(497): error C2039: “StartStreamingScript”: 不是“v8::ScriptCompiler”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-script.h(357): note: 参见“v8::ScriptCompiler”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstreamer.cpp(497): error C3861: “StartStreamingScript”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstreamer.cpp(639): error C2039: “kProduceParserCache”: 不是“v8::ScriptCompiler”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-script.h(357): note: 参见“v8::ScriptCompiler”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptstreamer.cpp(639): error C2065: “kProduceParserCache”: 未声明的标识符
  V8CustomElementLifecycleCallbacks.cpp
  V8DOMActivityLogger.cpp
  V8DOMConfiguration.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptwrappable.cpp(129): error C2664: “void v8::ReturnValue<v8::Value>::Set(uint32_t)”: 无法将参数 1 从“v8::Persistent<v8::Object,v8::NonCopyablePersistentTraits<T>>”转换为“bool”
          with
          [
              T=v8::Object
          ]
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptwrappable.cpp(129): note: 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptwrappable.cpp(142): error C2039: “MarkPartiallyDependent”: 不是“v8::Persistent<v8::Object,v8::NonCopyablePersistentTraits<T>>”的成员
          with
          [
              T=v8::Object
          ]
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(85): note: 参见“v8::Persistent<v8::Object,v8::NonCopyablePersistentTraits<T>>”的声明
          with
          [
              T=v8::Object
          ]
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptwrappable.cpp(144): error C2039: “SetObjectGroupId”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\scriptwrappable.cpp(149): error C2039: “SetReference”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8DOMWrapper.cpp
  V8ErrorHandler.cpp
  V8EventListener.cpp
  V8EventListenerList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\serializedscriptvalue.cpp(127): error C2039: “IsNeuterable”: 不是“v8::ArrayBuffer”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-wasm.h(19): note: 参见“v8::ArrayBuffer”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\serializedscriptvalue.cpp(140): error C2039: “Neuter”: 不是“v8::ArrayBuffer”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-wasm.h(19): note: 参见“v8::ArrayBuffer”的声明
  V8GCController.cpp
  V8GCController_v8_5_7.cpp
  V8GCForContextDispose.cpp
  V8HiddenValue.cpp
  V8Initializer.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\retaineddominfo.h(49): error C3668: “blink::RetainedDOMInfo::GetGroupLabel”: 包含重写说明符“override”的方法没有重写任何基类方法 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8GCController.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\retaineddominfo.h(67): error C3668: “blink::ActiveDOMObjectsInfo::GetGroupLabel”: 包含重写说明符“override”的方法没有重写任何基类方法 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8GCController.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\retaineddominfo.h(49): error C3668: “blink::RetainedDOMInfo::GetGroupLabel”: 包含重写说明符“override”的方法没有重写任何基类方法 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8GCController_v8_5_7.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\retaineddominfo.h(67): error C3668: “blink::ActiveDOMObjectsInfo::GetGroupLabel”: 包含重写说明符“override”的方法没有重写任何基类方法 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8GCController_v8_5_7.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8binding.cpp(118): error C2664: “bool v8::Value::BooleanValue(v8::Isolate *) const”: 无法将参数 1 从“v8::Local<v8::Context>”转换为“v8::Isolate *”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8binding.cpp(118): note: 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8binding.cpp(118): error C2672: “v8Call”: 未找到匹配的重载函数
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8binding.cpp(118): error C2780: “bool blink::v8Call(v8::Maybe<T>,T &)”: 应输入 2 个参数，却提供了 3 个
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8bindingmacros.h(54): note: 参见“blink::v8Call”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8binding.cpp(681): error C2039: “GetEnteredContext”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8binding.cpp(699): error C2039: “GetCallingContext”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8binding.cpp(704): error C2039: “GetEnteredContext”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8binding.cpp(735): error C2039: “GetCallingContext”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8binding.cpp(740): error C2039: “GetEnteredContext”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8binding.cpp(805): error C2039: “IsDead”: 不是“v8::V8”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-initialization.h(58): note: 参见“v8::V8”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8binding.cpp(805): error C3861: “IsDead”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8binding.cpp(1011): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
  V8IteratorResultValue.cpp
  V8LazyEventListener.cpp
  V8MutationCallback.cpp
  V8NPObject.cpp
  V8NPUtils.cpp
  V8NodeFilterCondition.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8gccontroller_v8_5_7.cpp(85): error C2039: “SetReference”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(21): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8gccontroller_v8_5_7.cpp(143): error C2039: “MarkActive”: 不是“v8::Persistent<v8::Object,v8::NonCopyablePersistentTraits<T>>”的成员
          with
          [
              T=v8::Object
          ]
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(85): note: 参见“v8::Persistent<v8::Object,v8::NonCopyablePersistentTraits<T>>”的声明
          with
          [
              T=v8::Object
          ]
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8gccontroller_v8_5_7.cpp(153): error C2039: “MarkActive”: 不是“v8::Persistent<v8::Object,v8::NonCopyablePersistentTraits<T>>”的成员
          with
          [
              T=v8::Object
          ]
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(85): note: 参见“v8::Persistent<v8::Object,v8::NonCopyablePersistentTraits<T>>”的声明
          with
          [
              T=v8::Object
          ]
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8gccontroller_v8_5_7.cpp(161): error C2039: “MarkActive”: 不是“v8::Persistent<v8::Object,v8::NonCopyablePersistentTraits<T>>”的成员
          with
          [
              T=v8::Object
          ]
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(85): note: 参见“v8::Persistent<v8::Object,v8::NonCopyablePersistentTraits<T>>”的声明
          with
          [
              T=v8::Object
          ]
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8gccontroller_v8_5_7.cpp(169): error C2039: “MarkActive”: 不是“v8::Persistent<v8::Object,v8::NonCopyablePersistentTraits<T>>”的成员
          with
          [
              T=v8::Object
          ]
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(85): note: 参见“v8::Persistent<v8::Object,v8::NonCopyablePersistentTraits<T>>”的声明
          with
          [
              T=v8::Object
          ]
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8gccontroller_v8_5_7.cpp(218): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-profiler.h(26): note: 参见“v8::Object”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8gccontroller_v8_5_7.cpp(220): error C2039: “SetObjectGroupId”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(21): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8gccontroller_v8_5_7.cpp(253): error C2039: “SetObjectGroupId”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(21): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8gccontroller_v8_5_7.cpp(298): error C2039: “SetObjectGroupId”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(21): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8gccontroller_v8_5_7.cpp(327): error C2039: “VisitWeakHandles”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(21): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8gccontroller_v8_5_7.cpp(333): error C2039: “VisitHandlesWithClassIds”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(21): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8gccontroller_v8_5_7.cpp(598): error C3609: “blink::`anonymous-namespace'::DOMWrapperForwardingVisitor::VisitTracedGlobalHandle”: “final”函数必须是虚函数
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8gccontroller_v8_5_7.cpp(651): error C2039: “VisitHandlesWithClassIds”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(21): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8gccontroller_v8_5_7.cpp(690): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-profiler.h(26): note: 参见“v8::Object”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8gccontroller_v8_5_7.cpp(715): error C2039: “VisitHandlesWithClassIds”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(21): note: 参见“v8::Isolate”的声明
  V8ObjectBuilder.cpp
  V8ObjectConstructor.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(109): error C2039: “ScriptID”: 不是“v8::ScriptOrigin”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-message.h(62): note: 参见“v8::ScriptOrigin”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(305): error C2039: “ScriptID”: 不是“v8::ScriptOrigin”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-message.h(62): note: 参见“v8::ScriptOrigin”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(431): error C2664: “void v8::Isolate::SetHostImportModuleDynamicallyCallback(v8::HostImportModuleDynamicallyCallback)”: 无法将参数 1 从“v8::MaybeLocal<v8::Promise> (__cdecl *)(v8::Local<v8::Context>,v8::Local<v8::ScriptOrModule>,v8::Local<v8::String>)”转换为“v8::HostImportModuleDynamicallyCallback”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(431): note: 在匹配目标类型的范围内没有具有该名称的函数
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(435): error C2039: “SetLiveEditEnabled”: 不是“v8::Debug”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\v8_compatibility.h(408): note: 参见“v8::Debug”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(435): error C3861: “SetLiveEditEnabled”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(437): error C2039: “SetAutorunMicrotasks”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(531): error C2039: “SetFatalErrorHandler”: 不是“v8::V8”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-initialization.h(58): note: 参见“v8::V8”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(531): error C3861: “SetFatalErrorHandler”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(532): error C2039: “AddMessageListener”: 不是“v8::V8”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-initialization.h(58): note: 参见“v8::V8”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(532): error C3861: “AddMessageListener”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(533): error C2039: “SetFailedAccessCheckCallbackFunction”: 不是“v8::V8”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-initialization.h(58): note: 参见“v8::V8”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(533): error C3861: “SetFailedAccessCheckCallbackFunction”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(534): error C2039: “SetAllowCodeGenerationFromStringsCallback”: 不是“v8::V8”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-initialization.h(58): note: 参见“v8::V8”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(534): error C3861: “SetAllowCodeGenerationFromStringsCallback”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(581): error C2039: “IsExecutionTerminating”: 不是“v8::V8”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-initialization.h(58): note: 参见“v8::V8”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(581): error C3861: “IsExecutionTerminating”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(600): error C2039: “AddMessageListener”: 不是“v8::V8”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-initialization.h(58): note: 参见“v8::V8”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(600): error C3861: “AddMessageListener”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(601): error C2039: “SetFatalErrorHandler”: 不是“v8::V8”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-initialization.h(58): note: 参见“v8::V8”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8initializer.cpp(601): error C3861: “SetFatalErrorHandler”: 找不到标识符
  V8PagePopupControllerBinding.cpp
  V8PerContextData.cpp
  V8PerIsolateData.cpp
  V8RecursionScope.cpp
  V8ScriptRunner.cpp
  V8StringResource.cpp
  V8ThrowException.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8nputils.cpp(169): error C2440: “<function-style-cast>”: 无法从“v8::Local<v8::Value>”转换为“v8::String::Utf8Value”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8nputils.cpp(169): note: 无构造函数可以接受源类型，或构造函数重载决策不明确
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8nputils.cpp(169): error C2198: “blink::ExceptionHandler”: 用于调用的参数太少
  V8ValueCache.cpp
  V8WorkerGlobalScopeEventListener.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8lazyeventlistener.cpp(209): error C2664: “v8::MaybeLocal<v8::Function> v8::Function::New(v8::Local<v8::Context>,v8::FunctionCallback,v8::Local<v8::Value>,int,v8::ConstructorBehavior,v8::SideEffectType)”: 无法将参数 1 从“v8::Isolate *”转换为“v8::Local<v8::Context>”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8lazyeventlistener.cpp(209): note: 无构造函数可以接受源类型，或构造函数重载决策不明确
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8npobject.cpp(289): error C2660: “v8::Value::ToString”: 函数不接受 0 个参数
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value.h(360): note: 参见“v8::Value::ToString”的声明 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8NPObject.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8npobject.cpp(316): error C2660: “v8::Value::ToString”: 函数不接受 0 个参数
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value.h(360): note: 参见“v8::Value::ToString”的声明 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8NPObject.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8npobject.cpp(353): error C2660: “v8::Value::ToString”: 函数不接受 0 个参数
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value.h(360): note: 参见“v8::Value::ToString”的声明 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8NPObject.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8npobject.cpp(477): error C2039: “SetNamedPropertyHandler”: 不是“v8::ObjectTemplate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-template.h(773): note: 参见“v8::ObjectTemplate”的声明
  WindowProxy.cpp
  WindowProxyManager.cpp
  WorkerScriptController.cpp
  WrapperTypeInfo.cpp
  V8CSSStyleDeclarationCustom.cpp
  V8CustomEventCustom.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\unifiedheapcontroller.h(53): error C3609: “blink::UnifiedHeapController::TracePrologue”: “final”函数必须是虚函数 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8PerIsolateData.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\unifiedheapcontroller.h(54): error C3609: “blink::UnifiedHeapController::TraceEpilogue”: “final”函数必须是虚函数 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8PerIsolateData.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\unifiedheapcontroller.h(42): error C2065: “isolate_”: 未声明的标识符 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8PerIsolateData.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\unifiedheapcontroller.h(47): error C2065: “isolate_”: 未声明的标识符 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\V8PerIsolateData.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8perisolatedata.cpp(292): error C2039: “Callee”: 不是“v8::FunctionCallbackInfo<v8::Value>”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-function-callback.h(251): note: 参见“v8::FunctionCallbackInfo<v8::Value>”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8pagepopupcontrollerbinding.cpp(40): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
  V8CustomXPathNSResolver.cpp
  V8DevToolsHostCustom.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8stringresource.cpp(64): error C2664: “int v8::String::Write(v8::Isolate *,uint16_t *,int,int,int) const”: 无法将参数 1 从“uint16_t *”转换为“v8::Isolate *”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8stringresource.cpp(64): note: 与指向的类型无关；强制转换要求 reinterpret_cast、C 样式强制转换或函数样式强制转换
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8stringresource.cpp(72): error C2664: “int v8::String::WriteOneByte(v8::Isolate *,uint8_t *,int,int,int) const”: 无法将参数 1 从“blink::V8StringOneByteTrait::CharType *”转换为“v8::Isolate *”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8stringresource.cpp(72): note: 与指向的类型无关；强制转换要求 reinterpret_cast、C 样式强制转换或函数样式强制转换
  V8DocumentCustom.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8valuecache.cpp(132): error C2039: “MarkIndependent”: 不是“v8::Global<ValueType>”的成员
          with
          [
              ValueType=v8::String
          ]
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8globalvaluemap.h(86): note: 参见“v8::Global<ValueType>”的声明
          with
          [
              ValueType=v8::String
          ]
  V8ErrorEventCustom.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8scriptrunner.cpp(104): error C2664: “v8::MaybeLocal<v8::Function> v8::Function::New(v8::Local<v8::Context>,v8::FunctionCallback,v8::Local<v8::Value>,int,v8::ConstructorBehavior,v8::SideEffectType)”: 无法将参数 1 从“v8::Isolate *”转换为“v8::Local<v8::Context>”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8scriptrunner.cpp(104): note: 无构造函数可以接受源类型，或构造函数重载决策不明确
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8scriptrunner.cpp(225): error C2039: “GetCachedData”: 不是“v8::ScriptCompiler::StreamedSource”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-script.h(477): note: 参见“v8::ScriptCompiler::StreamedSource”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8scriptrunner.cpp(282): error C2039: “kConsumeParserCache”: 不是“v8::ScriptCompiler”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-script.h(357): note: 参见“v8::ScriptCompiler”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8scriptrunner.cpp(282): error C2065: “kConsumeParserCache”: 未声明的标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8scriptrunner.cpp(282): error C2039: “kProduceParserCache”: 不是“v8::ScriptCompiler”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-script.h(357): note: 参见“v8::ScriptCompiler”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8scriptrunner.cpp(282): error C2065: “kProduceParserCache”: 未声明的标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8scriptrunner.cpp(297): error C2039: “kProduceCodeCache”: 不是“v8::ScriptCompiler”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-script.h(357): note: 参见“v8::ScriptCompiler”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8scriptrunner.cpp(297): error C2065: “kProduceCodeCache”: 未声明的标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8scriptrunner.cpp(355): error C2664: “v8::ScriptOrigin::ScriptOrigin(v8::ScriptOrigin &&)”: 无法将参数 1 从“v8::Local<v8::String>”转换为“v8::Isolate *”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8scriptrunner.cpp(369): note: 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8throwexception.cpp(52): error C2039: “IsExecutionTerminating”: 不是“v8::V8”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-initialization.h(58): note: 参见“v8::V8”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8throwexception.cpp(52): error C3861: “IsExecutionTerminating”: 找不到标识符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8throwexception.cpp(71): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8throwexception.cpp(163): error C2039: “IsExecutionTerminating”: 不是“v8::V8”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-initialization.h(58): note: 参见“v8::V8”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8throwexception.cpp(163): error C3861: “IsExecutionTerminating”: 找不到标识符
  V8EventTargetCustom.cpp
  V8HTMLAllCollectionCustom.cpp
  V8HTMLOptionsCollectionCustom.cpp
  V8HTMLPlugInElementCustom.cpp
  V8InjectedScriptManager.cpp
  V8MessageChannelCustom.cpp
  V8MessageEventCustom.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8customeventcustom.cpp(83): error C2664: “bool v8::Value::BooleanValue(v8::Isolate *) const”: 无法将参数 1 从“v8::Local<v8::Context>”转换为“v8::Isolate *”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8customeventcustom.cpp(83): note: 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8customeventcustom.cpp(83): error C2672: “blink::v8Call”: 未找到匹配的重载函数
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8customeventcustom.cpp(83): error C2780: “bool blink::v8Call(v8::MaybeLocal<T>,v8::Local<T> &,v8::TryCatch &)”: 应输入 3 个参数，却提供了 2 个
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8bindingmacros.h(79): note: 参见“blink::v8Call”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8customeventcustom.cpp(83): error C2780: “bool blink::v8Call(v8::Maybe<T>,T &,v8::TryCatch &)”: 应输入 3 个参数，却提供了 2 个
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8bindingmacros.h(69): note: 参见“blink::v8Call”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8customeventcustom.cpp(84): error C2664: “bool v8::Value::BooleanValue(v8::Isolate *) const”: 无法将参数 1 从“v8::Local<v8::Context>”转换为“v8::Isolate *”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8customeventcustom.cpp(84): note: 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8customeventcustom.cpp(84): error C2672: “blink::v8Call”: 未找到匹配的重载函数
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8customeventcustom.cpp(84): error C2780: “bool blink::v8Call(v8::MaybeLocal<T>,v8::Local<T> &,v8::TryCatch &)”: 应输入 3 个参数，却提供了 2 个
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8bindingmacros.h(79): note: 参见“blink::v8Call”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8customeventcustom.cpp(84): error C2780: “bool blink::v8Call(v8::Maybe<T>,T &,v8::TryCatch &)”: 应输入 3 个参数，却提供了 2 个
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8bindingmacros.h(69): note: 参见“blink::v8Call”的声明
  V8MutationObserverCustom.cpp
  V8PopStateEventCustom.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\windowproxy.cpp(391): error C2039: “ForceSet”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
  V8WindowCustom.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8devtoolshostcustom.cpp(167): error C2039: “GetEnteredContext”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8documentcustom.cpp(70): error C2661: “v8::Object::Get”: 没有重载函数接受 1 个参数
  V8XMLHttpRequestCustom.cpp
  InspectorWrapper.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8messageeventcustom.cpp(106): error C2664: “bool v8::Value::BooleanValue(v8::Isolate *) const”: 无法将参数 1 从“v8::Local<v8::Context>”转换为“v8::Isolate *”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8messageeventcustom.cpp(106): note: 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8messageeventcustom.cpp(106): error C2672: “blink::v8Call”: 未找到匹配的重载函数
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8messageeventcustom.cpp(106): error C2780: “bool blink::v8Call(v8::MaybeLocal<T>,v8::Local<T> &,v8::TryCatch &)”: 应输入 3 个参数，却提供了 2 个
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8bindingmacros.h(79): note: 参见“blink::v8Call”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8messageeventcustom.cpp(106): error C2780: “bool blink::v8Call(v8::Maybe<T>,T &,v8::TryCatch &)”: 应输入 3 个参数，却提供了 2 个
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8bindingmacros.h(69): note: 参见“blink::v8Call”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8messageeventcustom.cpp(107): error C2664: “bool v8::Value::BooleanValue(v8::Isolate *) const”: 无法将参数 1 从“v8::Local<v8::Context>”转换为“v8::Isolate *”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8messageeventcustom.cpp(107): note: 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8messageeventcustom.cpp(107): error C2672: “blink::v8Call”: 未找到匹配的重载函数
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8messageeventcustom.cpp(107): error C2780: “bool blink::v8Call(v8::MaybeLocal<T>,v8::Local<T> &,v8::TryCatch &)”: 应输入 3 个参数，却提供了 2 个
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8bindingmacros.h(79): note: 参见“blink::v8Call”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8messageeventcustom.cpp(107): error C2780: “bool blink::v8Call(v8::Maybe<T>,T &,v8::TryCatch &)”: 应输入 3 个参数，却提供了 2 个
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8bindingmacros.h(69): note: 参见“blink::v8Call”的声明
  V8InjectedScriptHost.cpp
  V8JavaScriptCallFrame.cpp
  npruntime.cpp
  DictionaryHelperForModules.cpp
  ModuleBindingsInitializer.cpp
  ScriptValueSerializerForModules.cpp
  SerializedScriptValueForModulesFactory.cpp
  WebGLAny.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8windowcustom.cpp(314): error C2273: “函数样式强制转换”: 位于“->”运算符右边时非法
  Init.cpp
  Animation.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\inspector\v8injectedscripthost.cpp(272): error C2661: “v8::Object::Set”: 没有重载函数接受 2 个参数
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\inspector\v8injectedscripthost.cpp(347): error C2661: “v8::Object::Set”: 没有重载函数接受 2 个参数
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\inspector\v8injectedscripthost.cpp(431): error C2661: “v8::Object::Set”: 没有重载函数接受 2 个参数
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\inspector\v8injectedscripthost.cpp(432): error C2661: “v8::Object::Set”: 没有重载函数接受 2 个参数
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\inspector\v8injectedscripthost.cpp(440): error C2664: “v8::Local<v8::Value> v8::Exception::Error(v8::Local<v8::String>)”: 无法将参数 1 从“v8::MaybeLocal<v8::String>”转换为“v8::Local<v8::String>”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\inspector\v8injectedscripthost.cpp(440): note: 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\inspector\v8injectedscripthost.cpp(444): error C2664: “v8::MaybeLocal<v8::String> v8::Value::ToString(v8::Local<v8::Context>) const”: 无法将参数 1 从“v8::Isolate *”转换为“v8::Local<v8::Context>”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\inspector\v8injectedscripthost.cpp(444): note: 无构造函数可以接受源类型，或构造函数重载决策不明确
  AnimationClock.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\inspector\v8injectedscripthost.cpp(446): error C2664: “v8::Local<v8::Value> v8::Exception::Error(v8::Local<v8::String>)”: 无法将参数 1 从“v8::MaybeLocal<v8::String>”转换为“v8::Local<v8::String>”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\inspector\v8injectedscripthost.cpp(446): note: 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\inspector\v8injectedscripthost.cpp(467): error C2661: “v8::Object::Set”: 没有重载函数接受 2 个参数
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\inspector\v8injectedscripthost.cpp(468): error C2661: “v8::Object::Set”: 没有重载函数接受 2 个参数
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\inspector\v8injectedscripthost.cpp(567): error C2660: “v8::Function::Call”: 函数不接受 3 个参数
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-function.h(55): note: 参见“v8::Function::Call”的声明 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\inspector\V8InjectedScriptHost.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\inspector\v8injectedscripthost.cpp(585): error C2660: “v8::Function::Call”: 函数不接受 3 个参数
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-function.h(55): note: 参见“v8::Function::Call”的声明 (编译源文件 ..\..\third_party\WebKit\Source\bindings\core\v8\inspector\V8InjectedScriptHost.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\inspector\v8injectedscripthost.cpp(606): error C2039: “ForceSet”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
  AnimationEffect.cpp
  AnimationEffectTiming.cpp
  AnimationInputHelpers.cpp
  AnimationStack.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8xmlhttprequestcustom.cpp(91): error C2664: “v8::MaybeLocal<v8::Value> v8::JSON::Parse(v8::Local<v8::Context>,v8::Local<v8::String>)”: 无法将参数 1 从“v8::Isolate *”转换为“v8::Local<v8::Context>”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8xmlhttprequestcustom.cpp(91): note: 无构造函数可以接受源类型，或构造函数重载决策不明确
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8xmlhttprequestcustom.cpp(91): error C2672: “v8Call”: 未找到匹配的重载函数
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\custom\v8xmlhttprequestcustom.cpp(91): error C2780: “bool blink::v8Call(v8::Maybe<T>,T &)”: 应输入 2 个参数，却提供了 3 个
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\bindings\core\v8\v8bindingmacros.h(54): note: 参见“blink::v8Call”的声明
  AnimationTimeline.cpp
  AnimationTranslationUtil.cpp
  CSSValueInterpolationType.cpp
  ColorStyleInterpolation.cpp
  CompositorAnimations.cpp
  CompositorPendingAnimations.cpp
  DefaultSVGInterpolation.cpp
  DeferredLegacyStyleInterpolation.cpp
  DocumentAnimations.cpp
  DoubleStyleInterpolation.cpp
  EffectInput.cpp
  ElementAnimations.cpp
  FilterStyleInterpolation.cpp
  ImageSliceStyleInterpolation.cpp
  ImageStyleInterpolation.cpp
  InertEffect.cpp
  IntegerOptionalIntegerSVGInterpolation.cpp
  InterpolableValue.cpp
  Interpolation.cpp
  InterpolationEffect.cpp
  InvalidatableStyleInterpolation.cpp
  KeyframeEffect.cpp
  KeyframeEffectModel.cpp
  LengthBoxStyleInterpolation.cpp
  LengthPairStyleInterpolation.cpp
  LengthSVGInterpolation.cpp
  LengthStyleInterpolation.cpp
  NumberSVGInterpolation.cpp
  PathSVGInterpolation.cpp
  PropertyHandle.cpp
  RectSVGInterpolation.cpp
  SVGInterpolation.cpp
  SVGStrokeDasharrayStyleInterpolation.cpp
  SampledEffect.cpp
  ShadowStyleInterpolation.cpp
  StringKeyframe.cpp
  Timing.cpp
  TimingInput.cpp
  TransformSVGInterpolation.cpp
  VisibilityStyleInterpolation.cpp
  AnimatableClipPathOperation.cpp
  AnimatableColor.cpp
  AnimatableDouble.cpp
  AnimatableDoubleAndBool.cpp
  AnimatableFilterOperations.cpp
  AnimatableImage.cpp
  AnimatableLength.cpp
  AnimatableLengthBox.cpp
  AnimatableLengthBoxAndBool.cpp
  AnimatableLengthPoint.cpp
  AnimatableLengthPoint3D.cpp
  AnimatableLengthSize.cpp
  AnimatableRepeatable.cpp
  AnimatableSVGPaint.cpp
  AnimatableShadow.cpp
  AnimatableShapeValue.cpp
  AnimatableStrokeDasharrayList.cpp
  AnimatableTransform.cpp
  AnimatableValue.cpp
  AnimatableValueKeyframe.cpp
  AnimatableVisibility.cpp
  CSSAnimatableValueFactory.cpp
  CSSAnimationData.cpp
  CSSAnimations.cpp
  CSSPropertyEquality.cpp
  CSSTimingData.cpp
  CSSTransitionData.cpp
  DataObject.cpp
  DataObjectItem.cpp
  DataTransfer.cpp
  DataTransferItem.cpp
  DataTransferItemList.cpp
  DraggedIsolatedFileSystem.cpp
  Pasteboard.cpp
  BasicShapeFunctions.cpp
  BinaryDataFontFaceSource.cpp
  CSSBasicShapes.cpp
  CSSBorderImage.cpp
  CSSBorderImageSliceValue.cpp
  CSSCalculationValue.cpp
  CSSCanvasValue.cpp
  CSSComputedStyleDeclaration.cpp
  CSSContentDistributionValue.cpp
  CSSCrossfadeValue.cpp
  CSSCursorImageValue.cpp
  CSSCustomPropertyDeclaration.cpp
  CSSDefaultStyleSheets.cpp
  CSSFontFace.cpp
  CSSFontFaceRule.cpp
  CSSFontFaceSource.cpp
  CSSFontFaceSrcValue.cpp
  CSSFontFeatureValue.cpp
  CSSFontSelector.cpp
  CSSFunctionValue.cpp
  CSSGradientValue.cpp
  CSSGridLineNamesValue.cpp
  CSSGridTemplateAreasValue.cpp
  CSSGroupingRule.cpp
  CSSImageGeneratorValue.cpp
  CSSImageSetValue.cpp
  CSSImageValue.cpp
  CSSImportRule.cpp
  CSSInheritedValue.cpp
  CSSInitialValue.cpp
  CSSKeyframeRule.cpp
  CSSKeyframesRule.cpp
  CSSLineBoxContainValue.cpp
  CSSMarkup.cpp
  CSSMatrix.cpp
  CSSMediaRule.cpp
  CSSPageRule.cpp
  CSSPathValue.cpp
  CSSPrimitiveValue.cpp
  CSSProperty.cpp
  CSSPropertySourceData.cpp
  CSSReflectValue.cpp
  CSSRule.cpp
  CSSRuleList.cpp
  CSSSVGDocumentValue.cpp
  CSSSegmentedFontFace.cpp
  CSSSelector.cpp
  CSSSelectorList.cpp
  CSSShadowValue.cpp
  CSSStyleRule.cpp
  CSSStyleSheet.cpp
  CSSSupportsRule.cpp
  CSSTimingFunctionValue.cpp
  CSSToLengthConversionData.cpp
  CSSUnicodeRangeValue.cpp
  CSSUnsetValue.cpp
  CSSValue.cpp
  CSSValueList.cpp
  CSSValuePool.cpp
  CSSVariableData.cpp
  CSSVariableReferenceValue.cpp
  CSSViewportRule.cpp
  ComputedStyleCSSValueMapping.cpp
  Counter.cpp
  DOMWindowCSS.cpp
  DocumentFontFaceSet.cpp
  ElementRuleCollector.cpp
  FontFace.cpp
  FontFaceCache.cpp
  FontFaceSet.cpp
  FontFaceSetLoadEvent.cpp
  FontLoader.cpp
  FontSize.cpp
  KeyframeStyleRuleCSSStyleDeclaration.cpp
  LocalFontFaceSource.cpp
  MediaList.cpp
  MediaQuery.cpp
  MediaQueryEvaluator.cpp
  MediaQueryExp.cpp
  MediaQueryList.cpp
  MediaQueryListListener.cpp
  MediaQueryMatcher.cpp
  MediaValues.cpp
  MediaValuesCached.cpp
  MediaValuesDynamic.cpp
  PageRuleCollector.cpp
  Pair.cpp
  PropertySetCSSStyleDeclaration.cpp
  Rect.cpp
  RemoteFontFaceSource.cpp
  RuleFeature.cpp
  RuleSet.cpp
  SelectorChecker.cpp
  SelectorFilter.cpp
  StyleMedia.cpp
  StylePropertySerializer.cpp
  StylePropertySet.cpp
  StylePropertyShorthandCustom.cpp
  StyleRule.cpp
  StyleRuleImport.cpp
  StyleRuleKeyframe.cpp
  StyleSheet.cpp
  StyleSheetContents.cpp
  StyleSheetList.cpp
  TreeBoundaryCrossingRules.cpp
  DescendantInvalidationSet.cpp
  StyleInvalidator.cpp
  StyleSheetInvalidationAnalysis.cpp
  CSSParser.cpp
  CSSParserFastPaths.cpp
  CSSParserImpl.cpp
  CSSParserMode.cpp
  CSSParserObserverWrapper.cpp
  CSSParserToken.cpp
  CSSParserTokenRange.cpp
  CSSParserValues.cpp
  CSSPropertyParser.cpp
  CSSSelectorParser.cpp
  CSSSupportsParser.cpp
  CSSTokenizer.cpp
  CSSTokenizerInputStream.cpp
  CSSVariableParser.cpp
  MediaQueryBlockWatcher.cpp
  MediaQueryParser.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\core\css\hashtools.h(35): warning C5033: “register”不再为受支持的存储类 (编译源文件 ..\..\third_party\WebKit\Source\core\css\parser\CSSPropertyParser.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\core\css\hashtools.h(36): warning C5033: “register”不再为受支持的存储类 (编译源文件 ..\..\third_party\WebKit\Source\core\css\parser\CSSPropertyParser.cpp)
  SizesAttributeParser.cpp
  SizesCalcParser.cpp
  AnimatedStyleBuilder.cpp
  CSSToStyleMap.cpp
  CSSVariableResolver.cpp
  ElementResolveContext.cpp
  ElementStyleResources.cpp
  FilterOperationResolver.cpp
  FontBuilder.cpp
  MatchResult.cpp
  MatchedPropertiesCache.cpp
  ScopedStyleResolver.cpp
  SharedStyleFinder.cpp
  StyleAdjuster.cpp
  StyleBuilderConverter.cpp
  StyleBuilderCustom.cpp
  StyleResolver.cpp
  StyleResolverParentScope.cpp
  StyleResolverState.cpp
  StyleResolverStats.cpp
  StyleResourceLoader.cpp
  TransformBuilder.cpp
  ViewportStyleResolver.cpp
  AXObjectCache.cpp
  ActiveDOMObject.cpp
  AddConsoleMessageTask.cpp
  Attr.cpp
  CDATASection.cpp
  CSSSelectorWatch.cpp
  CharacterData.cpp
  ChildFrameDisconnector.cpp
  ChildListMutationScope.cpp
  ChildNodeList.cpp
  ClassCollection.cpp
  ClientRect.cpp
  ClientRectList.cpp
  Comment.cpp
  CompositorProxy.cpp
  ContainerNode.cpp
  ContextFeatures.cpp
  ContextLifecycleNotifier.cpp
  DOMArrayBuffer.cpp
  DOMArrayPiece.cpp
  DOMDataView.cpp
  DOMError.cpp
  DOMException.cpp
  DOMImplementation.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\core\dom\domarraybuffer.cpp(22): error C2661: “v8::ArrayBuffer::New”: 没有重载函数接受 3 个参数
  DOMMatrix.cpp
  DOMMatrixReadOnly.cpp
  DOMNodeIds.cpp
  DOMPoint.cpp
  DOMPointReadOnly.cpp
  DOMRect.cpp
  DOMRectReadOnly.cpp
  DOMSettableTokenList.cpp
  DOMSharedArrayBuffer.cpp
  DOMStringList.cpp
  DOMStringMap.cpp
  DOMTokenList.cpp
  DOMTypedArray.cpp
  DOMURL.cpp
  DOMURLUtils.cpp
  DOMURLUtilsReadOnly.cpp
  DatasetDOMStringMap.cpp
  DecodedDataDocumentParser.cpp
  Document.cpp
  DocumentEncodingData.cpp
  DocumentFragment.cpp
  DocumentFullscreen.cpp
  DocumentInit.cpp
  DocumentLifecycle.cpp
  DocumentLifecycleNotifier.cpp
  DocumentMarker.cpp
  DocumentMarkerController.cpp
  DocumentOrderedList.cpp
  DocumentOrderedMap.cpp
  DocumentParser.cpp
  DocumentStyleSheetCollection.cpp
  DocumentStyleSheetCollector.cpp
  DocumentTiming.cpp
  DocumentType.cpp
  DocumentVisibilityObserver.cpp
  Element.cpp
  ElementData.cpp
  ElementDataCache.cpp
  ElementFullscreen.cpp
  ElementRareData.cpp
  EmptyNodeList.cpp
  ExecutionContext.cpp
  FirstLetterPseudoElement.cpp
  FrameRequestCallbackCollection.cpp
  Fullscreen.cpp
  IconURL.cpp
  IdTargetObserver.cpp
  IdTargetObserverRegistry.cpp
  IncrementLoadEventDelayCount.cpp
  LayoutTreeBuilder.cpp
  LayoutTreeBuilderTraversal.cpp
  LiveNodeList.cpp
  LiveNodeListBase.cpp
  MainThreadTaskRunner.cpp
  MessageChannel.cpp
  MessagePort.cpp
  Microtask.cpp
  ModuleScriptLoader.cpp
  MutationObserver.cpp
  MutationObserverInterestGroup.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\core\dom\microtask.cpp(53): error C2039: “RunMicrotasks”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  MutationObserverRegistration.cpp
  MutationRecord.cpp
  NameNodeList.cpp
  NamedNodeMap.cpp
  Node.cpp
  NodeChildRemovalTracker.cpp
  NodeFilter.cpp
  NodeIterator.cpp
  NodeIteratorBase.cpp
  NodeListsNodeData.cpp
  NodeRareData.cpp
  NodeTraversal.cpp
  NthIndexCache.cpp
  PendingScript.cpp
  Position.cpp
  PositionIterator.cpp
  PresentationAttributeStyle.cpp
  ProcessingInstruction.cpp
  PseudoElement.cpp
  QualifiedName.cpp
  Range.cpp
  RemoteSecurityContext.cpp
  SandboxFlags.cpp
  ScriptLoader.cpp
  ScriptRunner.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\core\dom\pendingscript.cpp(324): error C2664: “v8::ScriptOrigin::ScriptOrigin(v8::ScriptOrigin &&)”: 无法将参数 1 从“v8::Local<v8::String>”转换为“v8::Isolate *”
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\core\dom\pendingscript.cpp(334): note: 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\core\dom\pendingscript.cpp(358): error C2039: “GetModuleRequestsLength”: 不是“v8::Module”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-callbacks.h(28): note: 参见“v8::Module”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\core\dom\pendingscript.cpp(360): error C2039: “GetModuleRequest”: 不是“v8::Module”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-callbacks.h(28): note: 参见“v8::Module”的声明
  ScriptableDocumentParser.cpp
  ScriptedAnimationController.cpp
  SecurityContext.cpp
  SelectorQuery.cpp
  ShadowTreeStyleSheetCollection.cpp
  SpaceSplitString.cpp
  StringCallback.cpp
  StyleChangeReason.cpp
  StyleElement.cpp
  StyleEngine.cpp
