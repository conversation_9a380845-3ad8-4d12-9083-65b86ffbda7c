﻿C:\Program Files (x86)\Microsoft Visual Studio\2017\Enterprise\Common7\IDE\VC\VCTargets\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
  PlatformEventHandler.cpp
  WebFrameClientImpl.cpp
  WebPage.cpp
  WebPageImpl.cpp
  DevToolsAgent.cpp
  DevToolsClient.cpp
  DragHandle.cpp
  PopupMenuWin.cpp
  BlinkPlatformImpl.cpp
  WebMediaPlayerImpl.cpp
  PluginMessageThrottlerWin.cpp
  PluginPackage.cpp
  PluginPackageWin.cpp
  PluginStream.cpp
  WebPluginImpl.cpp
  WebPluginImplWin.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\web_impl_win\npapi\PluginMessageThrottlerWin.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\browser\PlatformEventHandler.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\web_impl_win\npapi\WebPluginImplWin.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\web_impl_win\npapi\PluginPackageWin.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\web_impl_win\npapi\WebPluginImpl.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\web_impl_win\BlinkPlatformImpl.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\devtools\DevToolsClient.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\browser\WebFrameClientImpl.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\browser\WebPage.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\web_impl_win\npapi\PluginPackage.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\web_impl_win\npapi\PluginStream.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\web_impl_win\WebMediaPlayerImpl.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\ui\DragHandle.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\ui\PopupMenuWin.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\browser\WebPageImpl.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\devtools\DevToolsAgent.cpp)
  npapi.cpp
  USVStringOrURLSearchParams.cpp
  UnionTypesCore.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\USVStringOrURLSearchParams.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\content\web_impl_win\npapi\npapi.cpp)
  V8Animation.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Animation.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\UnionTypesCore.cpp)
  V8AnimationEffectReadOnly.cpp
  V8AnimationEffectTiming.cpp
  V8AnimationEvent.cpp
  V8AnimationEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8AnimationEffectReadOnly.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8AnimationEffectTiming.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8AnimationEvent.cpp)
  V8AnimationPlayerEvent.cpp
  V8AnimationPlayerEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8AnimationEventInit.cpp)
  V8AnimationTimeline.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8AnimationPlayerEvent.cpp)
  V8ApplicationCache.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8AnimationPlayerEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8AnimationTimeline.cpp)
  V8ApplicationCacheErrorEvent.cpp
  V8ApplicationCacheErrorEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ApplicationCache.cpp)
  V8ArrayBuffer.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ApplicationCacheErrorEvent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ApplicationCacheErrorEventInit.cpp)
  V8ArrayBufferView.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ArrayBuffer.cpp)
  V8Attr.cpp
  V8AudioTrack.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ArrayBufferView.cpp)
  V8AudioTrackList.cpp
  V8AutocompleteErrorEvent.cpp
  V8AutocompleteErrorEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Attr.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8AudioTrack.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8AudioTrackList.cpp)
  V8BarProp.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8AutocompleteErrorEvent.cpp)
  V8BeforeUnloadEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8AutocompleteErrorEventInit.cpp)
  V8Blob.cpp
  V8BlobPropertyBag.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8BarProp.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8BeforeUnloadEvent.cpp)
  V8CDATASection.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Blob.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8BlobPropertyBag.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CDATASection.cpp)
  V8CSS.cpp
  V8CSSFontFaceRule.cpp
  V8CSSGroupingRule.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSS.cpp)
  V8CSSImportRule.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSFontFaceRule.cpp)
  V8CSSKeyframeRule.cpp
  V8CSSKeyframesRule.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSGroupingRule.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSImportRule.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSKeyframeRule.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSKeyframesRule.cpp)
  V8CSSMediaRule.cpp
  V8CSSPageRule.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSMediaRule.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSPageRule.cpp)
  V8CSSRule.cpp
  V8CSSRuleList.cpp
  V8CSSStyleDeclaration.cpp
  V8CSSStyleRule.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSRule.cpp)
  V8CSSStyleSheet.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSRuleList.cpp)
  V8CSSSupportsRule.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSStyleDeclaration.cpp)
  V8CSSViewportRule.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSStyleRule.cpp)
  V8CanvasContextCreationAttributes.cpp
  V8CharacterData.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSStyleSheet.cpp)
  V8ClientRect.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSSupportsRule.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CSSViewportRule.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CanvasContextCreationAttributes.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CharacterData.cpp)
  V8ClientRectList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ClientRect.cpp)
  V8ClipboardEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ClientRectList.cpp)
  V8Comment.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ClipboardEvent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Comment.cpp)
  V8CompositionEvent.cpp
  V8CompositionEventInit.cpp
  V8CompositorProxy.cpp
  V8ComputedTimingProperties.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CompositionEvent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CompositionEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CompositorProxy.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ComputedTimingProperties.cpp)
  V8Console.cpp
  V8ConsoleBase.cpp
  V8CustomEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Console.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ConsoleBase.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CustomEvent.cpp)
  V8CustomEventInit.cpp
  V8DOMError.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8CustomEventInit.cpp)
  V8DOMException.cpp
  V8DOMImplementation.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMError.cpp)
  V8DOMMatrix.cpp
  V8DOMMatrixReadOnly.cpp
  V8DOMParser.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMException.cpp)
  V8DOMPoint.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMImplementation.cpp)
  V8DOMPointInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMMatrix.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMMatrixReadOnly.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMParser.cpp)
  V8DOMPointReadOnly.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMPoint.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMPointInit.cpp)
  V8DOMRect.cpp
  V8DOMRectReadOnly.cpp
  V8DOMSettableTokenList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMPointReadOnly.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMRect.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMRectReadOnly.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMSettableTokenList.cpp)
  V8DOMStringList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8domimplementation.cpp(172): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8DOMStringMap.cpp
  V8DOMTokenList.cpp
  V8DataTransfer.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMStringList.cpp)
  V8DataTransferItem.cpp
  V8DataTransferItemList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMStringMap.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DOMTokenList.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DataTransfer.cpp)
  V8DataView.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DataTransferItem.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DataTransferItemList.cpp)
  V8DedicatedWorkerGlobalScope.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DataView.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DedicatedWorkerGlobalScope.cpp)
  V8DevToolsHost.cpp
  V8Document.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8domstringmap.cpp(208): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8domtokenlist.cpp(370): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DevToolsHost.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Document.cpp)
  V8DocumentFragment.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DocumentFragment.cpp)
  V8DocumentType.cpp
  V8EffectModel.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8dedicatedworkerglobalscope.cpp(85): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8dedicatedworkerglobalscope.cpp(226): error C2039: “SetHiddenPrototype”: 不是“v8::FunctionTemplate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-template.h(463): note: 参见“v8::FunctionTemplate”的声明
  V8Element.cpp
  V8ElementRegistrationOptions.cpp
  V8ErrorEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8DocumentType.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8EffectModel.cpp)
  V8ErrorEventInit.cpp
  V8Event.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Element.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ElementRegistrationOptions.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ErrorEvent.cpp)
  V8EventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ErrorEventInit.cpp)
  V8EventModifierInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Event.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8EventInit.cpp)
  V8EventSource.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8EventModifierInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8documentfragment.cpp(409): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
  V8EventSourceInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8document.cpp(6020): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
  V8EventTarget.cpp
  V8External.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8EventSource.cpp)
  V8File.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8documenttype.cpp(252): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8EventSourceInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8EventTarget.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8External.cpp)
  V8FileError.cpp
  V8FileList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8File.cpp)
  V8FilePropertyBag.cpp
  V8FileReader.cpp
  V8FileReaderSync.cpp
  V8Float32Array.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FileError.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FileList.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FilePropertyBag.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FileReader.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FileReaderSync.cpp)
  V8Float64Array.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Float32Array.cpp)
  V8FocusEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8element.cpp(2913): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Float64Array.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FocusEvent.cpp)
  V8FocusEventInit.cpp
  V8FontFace.cpp
  V8FontFaceDescriptors.cpp
  V8FontFaceSet.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FocusEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FontFace.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FontFaceDescriptors.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FontFaceSet.cpp)
  V8FontFaceSetForEachCallback.cpp
  V8FontFaceSetLoadEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FontFaceSetLoadEvent.cpp)
  V8FontFaceSetLoadEventInit.cpp
  V8FormData.cpp
  V8FrameRequestCallback.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FontFaceSetLoadEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FontFaceSetForEachCallback.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FormData.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8fontfaceset.cpp(400): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8FrameRequestCallback.cpp)
  V8GCObservation.cpp
  V8GarbageCollectedScriptWrappable.cpp
  V8HTMLAllCollection.cpp
  V8HTMLAnchorElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8GCObservation.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8GarbageCollectedScriptWrappable.cpp)
  V8HTMLAppletElement.cpp
  V8HTMLAreaElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLAllCollection.cpp)
  V8HTMLAudioElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLAnchorElement.cpp)
  V8HTMLBRElement.cpp
  V8HTMLBaseElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLAppletElement.cpp)
  V8HTMLBodyElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLAreaElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLAudioElement.cpp)
  V8HTMLButtonElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLBRElement.cpp)
  V8HTMLCanvasElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLBaseElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLBodyElement.cpp)
  V8HTMLCollection.cpp
  V8HTMLContentElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLButtonElement.cpp)
  V8HTMLDListElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLCanvasElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLCollection.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLContentElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLDListElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8htmlallcollection.cpp(182): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8HTMLDataListElement.cpp
  V8HTMLDetailsElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLDataListElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLDetailsElement.cpp)
  V8HTMLDialogElement.cpp
  V8HTMLDirectoryElement.cpp
  V8HTMLDivElement.cpp
  V8HTMLDocument.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLDialogElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLDirectoryElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLDivElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLDocument.cpp)
  V8HTMLElement.cpp
  V8HTMLEmbedElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLEmbedElement.cpp)
  V8HTMLFieldSetElement.cpp
  V8HTMLFontElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8htmldocument.cpp(320): error C2039: “SetHiddenPrototype”: 不是“v8::FunctionTemplate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-template.h(463): note: 参见“v8::FunctionTemplate”的声明
  V8HTMLFormControlsCollection.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLFieldSetElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLFontElement.cpp)
  V8HTMLFormElement.cpp
  V8HTMLFrameElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLFormControlsCollection.cpp)
  V8HTMLFrameSetElement.cpp
  V8HTMLHRElement.cpp
  V8HTMLHeadElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLFrameElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLFormElement.cpp)
  V8HTMLHeadingElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLFrameSetElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLHRElement.cpp)
  V8HTMLHtmlElement.cpp
  V8HTMLIFrameElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLHeadElement.cpp)
  V8HTMLImageElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLHeadingElement.cpp)
  V8HTMLInputElement.cpp
  V8HTMLKeygenElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLIFrameElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLHtmlElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLImageElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLInputElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLKeygenElement.cpp)
  V8HTMLLIElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8htmlformcontrolscollection.cpp(158): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8HTMLLabelElement.cpp
  V8HTMLLegendElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLLIElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLLabelElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLLegendElement.cpp)
  V8HTMLLinkElement.cpp
  V8HTMLMapElement.cpp
  V8HTMLMarqueeElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLLinkElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLMapElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLMarqueeElement.cpp)
  V8HTMLMediaElement.cpp
  V8HTMLMenuElement.cpp
  V8HTMLMenuItemElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLMediaElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLMenuElement.cpp)
  V8HTMLMetaElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLMenuItemElement.cpp)
  V8HTMLMeterElement.cpp
  V8HTMLModElement.cpp
  V8HTMLOListElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLMetaElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLMeterElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLModElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLOListElement.cpp)
  V8HTMLObjectElement.cpp
  V8HTMLOptGroupElement.cpp
  V8HTMLOptionElement.cpp
  V8HTMLOptionsCollection.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLObjectElement.cpp)
  V8HTMLOutputElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLOptGroupElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLOptionsCollection.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLOptionElement.cpp)
  V8HTMLParagraphElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLOutputElement.cpp)
  V8HTMLParamElement.cpp
  V8HTMLPictureElement.cpp
  V8HTMLPreElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLParagraphElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLParamElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLPictureElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLPreElement.cpp)
  V8HTMLProgressElement.cpp
  V8HTMLQuoteElement.cpp
  V8HTMLScriptElement.cpp
  V8HTMLSelectElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLProgressElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLQuoteElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLScriptElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLSelectElement.cpp)
  V8HTMLShadowElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8htmloptionscollection.cpp(307): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8HTMLSourceElement.cpp
  V8HTMLSpanElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLShadowElement.cpp)
  V8HTMLStyleElement.cpp
  V8HTMLTableCaptionElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLSourceElement.cpp)
  V8HTMLTableCellElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLSpanElement.cpp)
  V8HTMLTableColElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLTableCaptionElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLStyleElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLTableCellElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLTableColElement.cpp)
  V8HTMLTableElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLTableElement.cpp)
  V8HTMLTableRowElement.cpp
  V8HTMLTableSectionElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLTableRowElement.cpp)
  V8HTMLTemplateElement.cpp
  V8HTMLTextAreaElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLTableSectionElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLTemplateElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLTextAreaElement.cpp)
  V8HTMLTitleElement.cpp
  V8HTMLTrackElement.cpp
  V8HTMLUListElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLTitleElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLTrackElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLUListElement.cpp)
  V8HTMLUnknownElement.cpp
  V8HTMLVideoElement.cpp
  V8HashChangeEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLUnknownElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HTMLVideoElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HashChangeEvent.cpp)
  V8HashChangeEventInit.cpp
  V8History.cpp
  V8ImageBitmap.cpp
  V8ImageData.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8HashChangeEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8History.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ImageBitmap.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ImageData.cpp)
  V8InputDevice.cpp
  V8InputDeviceInit.cpp
  V8InspectorOverlayHost.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8InputDevice.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8InputDeviceInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8InspectorOverlayHost.cpp)
  V8Int16Array.cpp
  V8Int32Array.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Int16Array.cpp)
  V8Int8Array.cpp
  V8Iterator.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Int32Array.cpp)
  V8KeyboardEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Int8Array.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Iterator.cpp)
  V8KeyboardEventInit.cpp
  V8KeyframeEffect.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8KeyboardEvent.cpp)
  V8KeyframeEffectOptions.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8KeyboardEventInit.cpp)
  V8LayerRect.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8KeyframeEffect.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8KeyframeEffectOptions.cpp)
  V8LayerRectList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8LayerRect.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8LayerRectList.cpp)
  V8Location.cpp
  V8MediaController.cpp
  V8MediaError.cpp
  V8MediaKeyError.cpp
  V8MediaKeyEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Location.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MediaController.cpp)
  V8MediaKeyEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MediaError.cpp)
  V8MediaList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MediaKeyError.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MediaKeyEvent.cpp)
  V8MediaQueryList.cpp
  V8MediaQueryListEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MediaKeyEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MediaList.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MediaQueryList.cpp)
  V8MediaQueryListEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MediaQueryListEvent.cpp)
  V8MemoryInfo.cpp
  V8MessageChannel.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MediaQueryListEventInit.cpp)
  V8MessageEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MemoryInfo.cpp)
  V8MessageEventInit.cpp
  V8MessagePort.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MessageChannel.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MessageEvent.cpp)
  V8MouseEvent.cpp
  V8MouseEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MessagePort.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MessageEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MouseEvent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MouseEventInit.cpp)
  V8MutationEvent.cpp
  V8MutationObserver.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MutationEvent.cpp)
  V8MutationObserverInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MutationObserver.cpp)
  V8MutationRecord.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MutationObserverInit.cpp)
  V8NamedNodeMap.cpp
  V8Navigator.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8MutationRecord.cpp)
  V8Node.cpp
  V8NodeFilter.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8NamedNodeMap.cpp)
  V8NodeIterator.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Navigator.cpp)
  V8NodeList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Node.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8NodeFilter.cpp)
  V8PagePopupController.cpp
  V8PageTransitionEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8NodeIterator.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8NodeList.cpp)
  V8PageTransitionEventInit.cpp
  V8Performance.cpp
  V8PerformanceCompositeTiming.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8PagePopupController.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8PageTransitionEvent.cpp)
  V8PerformanceEntry.cpp
  V8PerformanceMark.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8PageTransitionEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Performance.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8PerformanceCompositeTiming.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8PerformanceEntry.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8namednodemap.cpp(317): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8PerformanceMark.cpp)
  V8PerformanceMeasure.cpp
  V8PerformanceNavigation.cpp
  V8PerformanceRenderTiming.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8PerformanceMeasure.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8nodelist.cpp(191): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8PerformanceResourceTiming.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8PerformanceNavigation.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8PerformanceRenderTiming.cpp)
  V8PerformanceTiming.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8PerformanceResourceTiming.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8PerformanceTiming.cpp)
  V8PluginPlaceholderElement.cpp
  V8PointerEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8PluginPlaceholderElement.cpp)
  V8PointerEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8PointerEvent.cpp)
  V8PopStateEvent.cpp
  V8PopStateEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8PointerEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8PopStateEvent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8PopStateEventInit.cpp)
  V8ProcessingInstruction.cpp
  V8ProgressEvent.cpp
  V8ProgressEventInit.cpp
  V8PromiseRejectionEvent.cpp
  V8PromiseRejectionEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ProcessingInstruction.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ProgressEvent.cpp)
  V8RadioNodeList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ProgressEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8PromiseRejectionEvent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8PromiseRejectionEventInit.cpp)
  V8Range.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8RadioNodeList.cpp)
  V8ReadableByteStream.cpp
  V8ReadableByteStreamReader.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Range.cpp)
  V8ReadableStream.cpp
  V8ReadableStreamReader.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ReadableByteStream.cpp)
  V8RefCountedScriptWrappable.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ReadableByteStreamReader.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ReadableStream.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ReadableStreamReader.cpp)
  V8RelatedEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8RefCountedScriptWrappable.cpp)
  V8RelatedEventInit.cpp
  V8ResourceProgressEvent.cpp
  V8SVGAElement.cpp
  V8SVGAngle.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8RelatedEvent.cpp)
  V8SVGAnimateElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8RelatedEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ResourceProgressEvent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGAElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGAngle.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGAnimateElement.cpp)
  V8SVGAnimateMotionElement.cpp
  V8SVGAnimateTransformElement.cpp
  V8SVGAnimatedAngle.cpp
  V8SVGAnimatedBoolean.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGAnimateMotionElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGAnimateTransformElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGAnimatedAngle.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGAnimatedBoolean.cpp)
  V8SVGAnimatedEnumeration.cpp
  V8SVGAnimatedInteger.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGAnimatedEnumeration.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGAnimatedInteger.cpp)
  V8SVGAnimatedLength.cpp
  V8SVGAnimatedLengthList.cpp
  V8SVGAnimatedNumber.cpp
  V8SVGAnimatedNumberList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGAnimatedLength.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGAnimatedLengthList.cpp)
  V8SVGAnimatedPreserveAspectRatio.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGAnimatedNumber.cpp)
  V8SVGAnimatedRect.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGAnimatedNumberList.cpp)
  V8SVGAnimatedString.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGAnimatedPreserveAspectRatio.cpp)
  V8SVGAnimatedTransformList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGAnimatedRect.cpp)
  V8SVGAnimationElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGAnimatedString.cpp)
  V8SVGCircleElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGAnimatedTransformList.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGAnimationElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGCircleElement.cpp)
  V8SVGClipPathElement.cpp
  V8SVGComponentTransferFunctionElement.cpp
  V8SVGCursorElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGClipPathElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGComponentTransferFunctionElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGCursorElement.cpp)
  V8SVGDefsElement.cpp
  V8SVGDescElement.cpp
  V8SVGDiscardElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGDefsElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGDescElement.cpp)
  V8SVGElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGDiscardElement.cpp)
  V8SVGEllipseElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGEllipseElement.cpp)
  V8SVGFEBlendElement.cpp
  V8SVGFEColorMatrixElement.cpp
  V8SVGFEComponentTransferElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEBlendElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEColorMatrixElement.cpp)
  V8SVGFECompositeElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEComponentTransferElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFECompositeElement.cpp)
  V8SVGFEConvolveMatrixElement.cpp
  V8SVGFEDiffuseLightingElement.cpp
  V8SVGFEDisplacementMapElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEConvolveMatrixElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEDiffuseLightingElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEDisplacementMapElement.cpp)
  V8SVGFEDistantLightElement.cpp
  V8SVGFEDropShadowElement.cpp
  V8SVGFEFloodElement.cpp
  V8SVGFEFuncAElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEDistantLightElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEFloodElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEDropShadowElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEFuncAElement.cpp)
  V8SVGFEFuncBElement.cpp
  V8SVGFEFuncGElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEFuncBElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEFuncGElement.cpp)
  V8SVGFEFuncRElement.cpp
  V8SVGFEGaussianBlurElement.cpp
  V8SVGFEImageElement.cpp
  V8SVGFEMergeElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEFuncRElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEGaussianBlurElement.cpp)
  V8SVGFEMergeNodeElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEImageElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEMergeElement.cpp)
  V8SVGFEMorphologyElement.cpp
  V8SVGFEOffsetElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEMergeNodeElement.cpp)
  V8SVGFEPointLightElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEMorphologyElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEOffsetElement.cpp)
  V8SVGFESpecularLightingElement.cpp
  V8SVGFESpotLightElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFEPointLightElement.cpp)
  V8SVGFETileElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFESpecularLightingElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFESpotLightElement.cpp)
  V8SVGFETurbulenceElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFETileElement.cpp)
  V8SVGFilterElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFETurbulenceElement.cpp)
  V8SVGForeignObjectElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGFilterElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGForeignObjectElement.cpp)
  V8SVGGElement.cpp
  V8SVGGeometryElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGGElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGGeometryElement.cpp)
  V8SVGGradientElement.cpp
  V8SVGGraphicsElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGGradientElement.cpp)
  V8SVGImageElement.cpp
  V8SVGLength.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGGraphicsElement.cpp)
  V8SVGLengthList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGImageElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGLength.cpp)
  V8SVGLineElement.cpp
  V8SVGLinearGradientElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGLengthList.cpp)
  V8SVGMPathElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGLineElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGLinearGradientElement.cpp)
  V8SVGMarkerElement.cpp
  V8SVGMaskElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGMPathElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGMaskElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGMarkerElement.cpp)
  V8SVGMatrix.cpp
  V8SVGMetadataElement.cpp
  V8SVGNumber.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGMatrix.cpp)
  V8SVGNumberList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGMetadataElement.cpp)
  V8SVGPathElement.cpp
  V8SVGPathSeg.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGNumber.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGNumberList.cpp)
  V8SVGPathSegArcAbs.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSeg.cpp)
  V8SVGPathSegArcRel.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegArcAbs.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegArcRel.cpp)
  V8SVGPathSegClosePath.cpp
  V8SVGPathSegCurvetoCubicAbs.cpp
  V8SVGPathSegCurvetoCubicRel.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegClosePath.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegCurvetoCubicAbs.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegCurvetoCubicRel.cpp)
  V8SVGPathSegCurvetoCubicSmoothAbs.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegCurvetoCubicSmoothAbs.cpp)
  V8SVGPathSegCurvetoCubicSmoothRel.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegCurvetoCubicSmoothRel.cpp)
  V8SVGPathSegCurvetoQuadraticAbs.cpp
  V8SVGPathSegCurvetoQuadraticRel.cpp
  V8SVGPathSegCurvetoQuadraticSmoothAbs.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegCurvetoQuadraticAbs.cpp)
  V8SVGPathSegCurvetoQuadraticSmoothRel.cpp
  V8SVGPathSegLinetoAbs.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegCurvetoQuadraticRel.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegCurvetoQuadraticSmoothAbs.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegCurvetoQuadraticSmoothRel.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegLinetoAbs.cpp)
  V8SVGPathSegLinetoHorizontalAbs.cpp
  V8SVGPathSegLinetoHorizontalRel.cpp
  V8SVGPathSegLinetoRel.cpp
  V8SVGPathSegLinetoVerticalAbs.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegLinetoHorizontalAbs.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegLinetoHorizontalRel.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegLinetoRel.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegLinetoVerticalAbs.cpp)
  V8SVGPathSegLinetoVerticalRel.cpp
  V8SVGPathSegList.cpp
  V8SVGPathSegMovetoAbs.cpp
  V8SVGPathSegMovetoRel.cpp
  V8SVGPatternElement.cpp
  V8SVGPoint.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegLinetoVerticalRel.cpp)
  V8SVGPointList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegList.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegMovetoRel.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPathSegMovetoAbs.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPatternElement.cpp)
  V8SVGPolygonElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPoint.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPointList.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPolygonElement.cpp)
  V8SVGPolylineElement.cpp
  V8SVGPreserveAspectRatio.cpp
  V8SVGRadialGradientElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPolylineElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGPreserveAspectRatio.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGRadialGradientElement.cpp)
  V8SVGRect.cpp
  V8SVGRectElement.cpp
  V8SVGSVGElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGRect.cpp)
  V8SVGScriptElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGRectElement.cpp)
  V8SVGSetElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGSVGElement.cpp)
  V8SVGStopElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGScriptElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGSetElement.cpp)
  V8SVGStringList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGStopElement.cpp)
  V8SVGStyleElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGStringList.cpp)
  V8SVGSwitchElement.cpp
  V8SVGSymbolElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGStyleElement.cpp)
  V8SVGTSpanElement.cpp
  V8SVGTextContentElement.cpp
  V8SVGTextElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGSwitchElement.cpp)
  V8SVGTextPathElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGSymbolElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGTSpanElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGTextElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGTextContentElement.cpp)
  V8SVGTextPositioningElement.cpp
  V8SVGTitleElement.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGTextPathElement.cpp)
  V8SVGTransform.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGTextPositioningElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGTitleElement.cpp)
  V8SVGTransformList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGTransform.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGTransformList.cpp)
  V8SVGUnitTypes.cpp
  V8SVGUseElement.cpp
  V8SVGViewElement.cpp
  V8SVGViewSpec.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGUnitTypes.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGUseElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGViewElement.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGViewSpec.cpp)
  V8SVGZoomEvent.cpp
  V8Screen.cpp
  V8ScrollOptions.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SVGZoomEvent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Screen.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ScrollOptions.cpp)
  V8ScrollState.cpp
  V8ScrollToOptions.cpp
  V8SecurityPolicyViolationEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ScrollState.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ScrollToOptions.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SecurityPolicyViolationEvent.cpp)
  V8SecurityPolicyViolationEventInit.cpp
  V8Selection.cpp
  V8ShadowRoot.cpp
  V8ShadowRootInit.cpp
  V8SharedArrayBuffer.cpp
  V8SharedWorker.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SecurityPolicyViolationEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Selection.cpp)
  V8StateOptions.cpp
  V8Stream.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ShadowRoot.cpp)
  V8StringCallback.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ShadowRootInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SharedArrayBuffer.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8SharedWorker.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Stream.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8StateOptions.cpp)
  V8StyleMedia.cpp
  V8StyleSheet.cpp
  V8StyleSheetList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8StyleMedia.cpp)
  V8Text.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8StyleSheet.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8StringCallback.cpp)
  V8TextEvent.cpp
  V8TextMetrics.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8StyleSheetList.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Text.cpp)
  V8TextTrack.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8TextEvent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8TextMetrics.cpp)
  V8TextTrackCue.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8TextTrack.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8TextTrackCue.cpp)
  V8TextTrackCueList.cpp
  V8TextTrackList.cpp
  V8TimeRanges.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8stylesheet.cpp(185): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8Touch.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8TextTrackList.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8TextTrackCueList.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8TimeRanges.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8stylesheetlist.cpp(127): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Touch.cpp)
  V8TouchEvent.cpp
  V8TouchList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8TouchEvent.cpp)
  V8TrackEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8TouchList.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8texttrack.cpp(364): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8TrackEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8texttrackcue.cpp(268): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8TransitionEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8TrackEvent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8TrackEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8TransitionEvent.cpp)
  V8TransitionEventInit.cpp
  V8TreeWalker.cpp
  V8TypeConversions.cpp
  V8UIEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8texttracklist.cpp(226): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8TransitionEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8TreeWalker.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8TypeConversions.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8UIEvent.cpp)
  V8UIEventInit.cpp
  V8URL.cpp
  V8URLSearchParams.cpp
  V8Uint16Array.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8UIEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8URL.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8URLSearchParams.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Uint16Array.cpp)
  V8Uint32Array.cpp
  V8Uint8Array.cpp
  V8Uint8ClampedArray.cpp
  V8VTTCue.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Uint32Array.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Uint8Array.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Uint8ClampedArray.cpp)
  V8VTTRegion.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8VTTCue.cpp)
  V8VTTRegionList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8VTTRegion.cpp)
  V8ValidityState.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8VTTRegionList.cpp)
  V8VideoTrack.cpp
  V8VideoTrackList.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8ValidityState.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8VideoTrack.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8VideoTrackList.cpp)
  V8VoidCallback.cpp
  V8WebKitCSSMatrix.cpp
  V8WheelEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8vttcue.cpp(386): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8WheelEventInit.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8WebKitCSSMatrix.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8WheelEvent.cpp)
  V8Window.cpp
  V8Worker.cpp
  V8WorkerConsole.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8WheelEventInit.cpp)
  V8WorkerGlobalScope.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Window.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8VoidCallback.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8WorkerConsole.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8Worker.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8WorkerGlobalScope.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8videotrack.cpp(139): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8videotracklist.cpp(213): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8WorkerLocation.cpp
  V8WorkerNavigator.cpp
  V8WorkerPerformance.cpp
  V8XMLDocument.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8WorkerLocation.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8WorkerNavigator.cpp)
  V8XMLHttpRequest.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8WorkerPerformance.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XMLDocument.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XMLHttpRequest.cpp)
  V8XMLHttpRequestEventTarget.cpp
  V8XMLHttpRequestProgressEvent.cpp
  V8XMLHttpRequestUpload.cpp
  V8XMLSerializer.cpp
  V8XPathEvaluator.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XMLHttpRequestEventTarget.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XMLHttpRequestProgressEvent.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathEvaluator.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XMLSerializer.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XMLHttpRequestUpload.cpp)
  V8XPathExpression.cpp
  V8XPathNSResolver.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathExpression.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathNSResolver.cpp)
  V8XPathResult.cpp
  V8XSLTProcessor.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XPathResult.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\V8XSLTProcessor.cpp)
  v8sharedworkerglobalscope.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8window.cpp(470): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8window.cpp(8201): error C2039: “SetHiddenPrototype”: 不是“v8::FunctionTemplate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-template.h(463): note: 参见“v8::FunctionTemplate”的声明
  UnionTypesModulesNone.cpp
  V8ANGLEInstancedArrays.cpp
  V8Body.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\core\v8\v8sharedworkerglobalscope.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\UnionTypesModulesNone.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8ANGLEInstancedArrays.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Body.cpp)
  V8CHROMIUMSubscribeUniform.cpp
  V8CHROMIUMValuebuffer.cpp
  V8Canvas2DContextAttributes.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CHROMIUMSubscribeUniform.cpp)
  V8CanvasGradient.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CHROMIUMValuebuffer.cpp)
  V8CanvasPattern.cpp
  V8CanvasRenderingContext2D.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Canvas2DContextAttributes.cpp)
  V8CloseEvent.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8sharedworkerglobalscope.cpp(81): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\core\v8\v8sharedworkerglobalscope.cpp(198): error C2039: “SetHiddenPrototype”: 不是“v8::FunctionTemplate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-template.h(463): note: 参见“v8::FunctionTemplate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CanvasGradient.cpp)
  V8CloseEventInit.cpp
  V8CompositorWorker.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CanvasPattern.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CanvasRenderingContext2D.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CloseEvent.cpp)
  V8CompositorWorkerGlobalScope.cpp
  V8Coordinates.cpp
  V8Crypto.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CloseEventInit.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CompositorWorker.cpp)
  V8DataTransferItemPartial.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8angleinstancedarrays.cpp(157): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8CompositorWorkerGlobalScope.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Crypto.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Coordinates.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DataTransferItemPartial.cpp)
  V8DedicatedWorkerGlobalScopePartial.cpp
  V8DeprecatedStorageInfo.cpp
  V8DeprecatedStorageQuota.cpp
  V8DevToolsHostPartial.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DedicatedWorkerGlobalScopePartial.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DeprecatedStorageInfo.cpp)
  V8EXTBlendMinMax.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8chromiumsubscribeuniform.cpp(237): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DeprecatedStorageQuota.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8canvasrenderingcontext2d.cpp(2729): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8DevToolsHostPartial.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTBlendMinMax.cpp)
  V8EXTFragDepth.cpp
  V8EXTShaderTextureLOD.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8compositorworkerglobalscope.cpp(65): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8compositorworkerglobalscope.cpp(228): error C2039: “SetHiddenPrototype”: 不是“v8::FunctionTemplate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-template.h(463): note: 参见“v8::FunctionTemplate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTFragDepth.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTShaderTextureLOD.cpp)
  V8EXTTextureFilterAnisotropic.cpp
  V8EXTsRGB.cpp
  V8Geolocation.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTTextureFilterAnisotropic.cpp)
  V8Geoposition.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8dedicatedworkerglobalscopepartial.cpp(73): error C2039: “CreationContext”: 不是“v8::Object”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-value-serializer.h(22): note: 参见“v8::Object”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8EXTsRGB.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Geolocation.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Geoposition.cpp)
  V8HTMLVideoElementPartial.cpp
  V8Headers.cpp
  V8HeadersNone.cpp
  V8HitRegionOptions.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8HTMLVideoElementPartial.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Headers.cpp)
  V8MediaSession.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8HeadersNone.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8extblendminmax.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8MediaStreamTrackSourcesCallback.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8HitRegionOptions.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8extfragdepth.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8MediaSession.cpp)
  V8MimeType.cpp
  V8MimeTypeArray.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8extshadertexturelod.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8MouseEventPartial.cpp
  V8NavigatorPartial.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8MimeType.cpp)
  V8NavigatorUserMediaError.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8MimeTypeArray.cpp)
  V8NavigatorUserMediaErrorCallback.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8MediaStreamTrackSourcesCallback.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8MouseEventPartial.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8exttexturefilteranisotropic.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8NavigatorPartial.cpp)
  V8NetworkInformation.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8extsrgb.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8NavigatorUserMediaError.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8NetworkInformation.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8NavigatorUserMediaErrorCallback.cpp)
  V8OESElementIndexUint.cpp
  V8OESStandardDerivatives.cpp
  V8OESTextureFloat.cpp
  V8OESTextureFloatLinear.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8OESStandardDerivatives.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8OESElementIndexUint.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8OESTextureFloat.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8OESTextureFloatLinear.cpp)
  V8OESTextureHalfFloat.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8OESTextureHalfFloat.cpp)
  V8OESTextureHalfFloatLinear.cpp
  V8OESVertexArrayObject.cpp
  V8PartialNone.cpp
  V8Path2D.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8OESTextureHalfFloatLinear.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8OESVertexArrayObject.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8PartialNone.cpp)
  V8Plugin.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Path2D.cpp)
  V8PluginArray.cpp
  V8PositionCallback.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Plugin.cpp)
  V8PositionError.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8PluginArray.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oesstandardderivatives.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oestexturefloat.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oeselementindexuint.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oestexturefloatlinear.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
  V8PositionErrorCallback.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8PositionError.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8PositionCallback.cpp)
  V8PositionOptions.cpp
  V8Request.cpp
  V8Response.cpp
  V8SharedWorkerGlobalScopePartial.cpp
  V8SourceBuffer.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oestexturehalffloat.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8PositionErrorCallback.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8PositionOptions.cpp)
  V8SourceBufferList.cpp
  V8SourceInfo.cpp
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Request.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8Response.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8SharedWorkerGlobalScopePartial.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oestexturehalffloatlinear.cpp(50): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\gen\blink\bindings\modules\v8\v8oesvertexarrayobject.cpp(127): error C2039: “SetReferenceFromGroup”: 不是“v8::Isolate”的成员
  e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\third_party\webkit\source\platform\heap\threadstate.h(48): note: 参见“v8::Isolate”的声明
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8SourceBuffer.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8SourceBufferList.cpp)
e:\paraplay_svn\newwebbrowser\pc_chrome_src\zkbrowse\miniblink49\v8_10_8\include\v8-typed-array.h(28): warning C4309: “static_cast”: 截断常量值 (编译源文件 ..\..\gen\blink\bindings\modules\v8\V8SourceInfo.cpp)
