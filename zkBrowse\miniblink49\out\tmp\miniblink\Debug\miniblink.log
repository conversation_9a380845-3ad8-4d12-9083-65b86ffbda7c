﻿C:\Program Files (x86)\Microsoft Visual Studio\2017\Enterprise\Common7\IDE\VC\VCTargets\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
  algorithmvc6.cpp
  command_line.cc
  alias.cc
  gdi_debug_util_win.cc
  ostreamvc6.cpp
  memory.cc
  memory_win.cc
  rand_util.cc
  rand_util_win.cc
  string_util.cc
  thread.cc
  time.cc
  time_win.cc
  values.cc
  windowsvc6.cpp
  CheckReEnter.cpp
  PlatformEventHandler.cpp
  WebFrameClientImpl.cpp
  WebPage.cpp
  WebPageImpl.cpp
  HTMLMarqueeElementJs.cpp
  PluginPlaceholderElementJs.cpp
  PrivateScriptRunnerJs.cpp
  calendarPickerCss.cpp
  calendarPickerJs.cpp
  colorSuggestionPickerCss.cpp
  colorSuggestionPickerJs.cpp
  listPickerCss.cpp
  listPickerJs.cpp
  pickerButtonCss.cpp
  pickerCommonCss.cpp
  pickerCommonJs.cpp
  suggestionPickerCss.cpp
  suggestionPickerJs.cpp
  testWebPagePopupImpl.cpp
  BlinkPlatformImpl.cpp
  CurrentTimeImpl.cpp
  WaitableEventWin.cpp
  WebBlobRegistryImpl.cpp
  WebClipboardImpl.cpp
  WebFileUtilitiesImpl.cpp
  WebMediaPlayerImpl.cpp
  WebMimeRegistryImpl.cpp
  WebSchedulerImpl.cpp
  WebThemeEngineImpl.cpp
  WebThreadImpl.cpp
  WebTimerBase.cpp
  WebURLLoaderImpl.cpp
  WebURLLoaderImplCurl.cpp
  PluginDatabase.cpp
  PluginDatabaseWin.cpp
  PluginMainThreadScheduler.cpp
  PluginMessageThrottlerWin.cpp
  PluginPackage.cpp
  PluginPackageWin.cpp
  PluginStream.cpp
  npapi.cpp
  UnionTypesCore.cpp
  V8Animation.cpp
  V8AnimationEffectReadOnly.cpp
  V8AnimationEffectTiming.cpp
  V8AnimationEvent.cpp
  V8AnimationEventInit.cpp
  V8AnimationPlayerEvent.cpp
  V8AnimationPlayerEventInit.cpp
  V8AnimationTimeline.cpp
