<!DOCTYPE html>
<html>
<body>
<p>This test ensures WebKit does not fire click event on a node that has been removed twice in mouseup event.</p>
<div id="test"><span id="target" onmouseup="mouseup()" onclick="test.innerHTML = 'FAIL';">click here</span></div>
<script>

var test = document.getElementById('test');
var target = document.getElementById('target');

function mouseup() {
    test.appendChild(document.createTextNode('PASS'));
    test.removeChild(target);
    test.appendChild(target);
    test.removeChild(target);
}

if (window.testRunner) {
    testRunner.dumpAsText();
    if (!window.eventSender)
        test.innerHTML = 'FAIL - this test requires eventSender';
    else {
        eventSender.mouseMoveTo(target.offsetLeft + target.offsetWidth / 2, target.offsetTop + target.offsetHeight / 2);
        eventSender.mouseDown();
        eventSender.leapForward(200);
        eventSender.mouseUp();
    }
}

</script>
</body>
</html>
