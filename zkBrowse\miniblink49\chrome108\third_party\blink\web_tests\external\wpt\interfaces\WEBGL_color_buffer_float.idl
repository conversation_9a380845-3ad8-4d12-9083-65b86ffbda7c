// GENERATED CONTENT - DO NOT EDIT
// Content was automatically extracted by <PERSON><PERSON> into webref
// (https://github.com/w3c/webref)
// Source: WebGL WEBGL_color_buffer_float Extension Specification (https://registry.khronos.org/webgl/extensions/WEBGL_color_buffer_float/)

[Exposed=(Window,Worker), LegacyNoInterfaceObject]
interface WEBGL_color_buffer_float {
  const GLenum RGBA32F_EXT = 0x8814;
  const GLenum FRAMEBUFFER_ATTACHMENT_COMPONENT_TYPE_EXT = 0x8211;
  const GLenum UNSIGNED_NORMALIZED_EXT = 0x8C17;
}; // interface WEBGL_color_buffer_float
