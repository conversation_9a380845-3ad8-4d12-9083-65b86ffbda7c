<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" width="600" height="500">
<style>
foreignObject { background-color: green; }
foreignobject { background-color: red; }
</style>
<text y="20">There should be a PASS and a green square below.</text>
<text y="40" id="result">FAIL</text>
<foreignObject y="60" id="foreign" width="100" height="100" />
<script>
if (window.testRunner)
    testRunner.dumpAsText();

if (getComputedStyle(document.getElementById("foreign")).backgroundColor == "rgb(0, 128, 0)")
    document.getElementById("result").firstChild.data = "PASS";
</script>
</svg>
