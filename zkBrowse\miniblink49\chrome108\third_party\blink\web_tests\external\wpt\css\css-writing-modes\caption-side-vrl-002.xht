<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

 <head>

  <title>CSS Writing Modes Test: 'caption-side: top' and vertical-rl</title>

  <link rel="author" title="<PERSON><PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" />
  <link rel="help" href="https://drafts.csswg.org/css-writing-modes-3/#logical-direction-layout" title="7.4. Flow-Relative Mappings" />
  <link rel="match" href="../reference/ref-filled-green-100px-square.xht" />

  <meta content="ahem image" name="flags" />
  <meta content="This test checks that when 'caption-side' is set to 'top' in a vertical-rl table, then the caption is placed at the block-start side of the table, which is on the righthand side of such table." name="assert" />

  <link rel="stylesheet" type="text/css" href="/fonts/ahem.css" />
  <style type="text/css"><![CDATA[
  div#reference-overlapped-red
    {
      background: url("support/pattern-gr-gr-100x100.png") no-repeat;
      height: 100px;
      position: absolute;
      width: 100px;
      z-index: -1;
    }

  table#test-overlapping-green
    {
      border-spacing: 0px;
      caption-side: top;
      font: 50px/1 Ahem; /* computes to 50px/50px */
      writing-mode: vertical-rl;
    }

  caption
    {
      color: green;
    }

  td
    {
      color: transparent;
      padding: 0px;
    }
  ]]></style>

 </head>

 <body>

  <p>Test passes if there is a filled green square and <strong>no red</strong>.</p>

  <div id="reference-overlapped-red"></div>

  <table id="test-overlapping-green">
    <caption>CA</caption>
    <tr>
      <td>T</td><td>D</td>
    </tr>
  </table>

 </body>
</html>
