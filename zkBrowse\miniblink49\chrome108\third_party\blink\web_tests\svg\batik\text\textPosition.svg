<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN"
"http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">

<!--

   Copyright 2001-2002  The Apache Software Foundation 

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

-->
<!-- ========================================================================= -->
<!-- Test the x,dx and y,dy attributes                                         -->
<!--                                                                           -->
<!-- <AUTHOR>                                                  -->
<!-- @version $Id: textPosition.svg,v 1.4 2004/08/18 07:12:23 vhardy Exp $ -->
<!-- ========================================================================= -->
<?xml-stylesheet type="text/css" href="../resources/test.css" ?>

<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="450" height="500" viewBox="0 0 450 500">
<title>Text Position</title>

  <style type="text/css"><![CDATA[
    .info {
      font-family: Arial;
      font-size: 10px;
    }
  ]]>
  </style>

    <!-- ============================================================= -->
    <!-- Test content                                                  -->
    <!-- ============================================================= -->

<text class="title" x="50%" y="30">Text Position</text>

<g id="testContent" style="font-family:Arial; font-size:24px">

<!-- ################################################################ -->

<text x="50" y="80">Batik</text>
<text x="50" y="100" class="info">&lt;text x="100" y="80"></text>

<text x="240" y="80" style="letter-spacing:20">Batik</text>
<text x="240" y="100" class="info">&lt;text x="240" y="80" letter-spacing="20"></text>


<text x="50 70 90 110 130" y="140">Batik</text>
<text x="50" y="160" class="info">&lt;text x="50 70 90 110 130" y="140"></text>

<text dx="240 20 20 20 20" y="140">Batik</text>
<text x="240" y="160" class="info">&lt;text dx="240 20 20 20 20" y="140"></text>

<!-- ################################################################ -->

<text><tspan x="50" y="200">Batik</tspan></text>
<text x="50" y="220" class="info">&lt;tspan x="50" y="200"></text>

<text><tspan x="240" y="200" style="letter-spacing:20">Batik</tspan></text>
<text><tspan x="240" y="220" class="info">&lt;tspan x="240" y="200" letter-spacing="20"></tspan></text>


<text><tspan x="50 70 90 110 130" y="260">Batik</tspan></text>
<text x="50" y="280" class="info">&lt;tspan x="50 70 90 110 130" y="260"></text>

<text><tspan dx="240 20 20 20 20" y="260">Batik</tspan></text>
<text x="240" y="280" class="info">&lt;tspan dx="240 20 20 20 20" y="260"></text>

<!-- ################################################################ -->

<text><tspan x="50 60 70 80 90" dx="0 10 20 30 40" y="320">Batik</tspan></text>
<text x="50" y="340" class="info">&lt;tspan x="50 60 70 80 90" <tspan x="83" dy="1.1em">dx="0 10 20 30 40" y="320"></tspan></text>


<text><tspan x="240" dx="0 10 10 10 10" y="320" style="letter-spacing:10">Batik</tspan></text>
<text x="240" y="340" class="info">&lt;tspan x="240" dx="0 10 10 10 10" <tspan x="273" dy="1.1em">y="320" style="letter-spacing:10"></tspan></text>


<!-- ################################################################ -->

<text><tspan x="50" letter-spacing="10" y="380">Batik</tspan><tspan x="10" y="380"/></text>
<text x="50" y="400" class="info">Empty Trailing tspan</text>


<text><tspan x="200" y="380"/><tspan x="240" letter-spacing="20" y="380">Batik</tspan></text>
<text x="240" y="400" class="info">Empty Preceeding tspan</text>


</g>

    <!-- ============================================================= -->
    <!-- Batik sample mark                                             -->
    <!-- ============================================================= -->
    <use xlink:href="../resources/batikLogo.svg#Batik_Tag_Box" />
    
</svg>

