Verify that automapping is sane.

Binding created: {
       network: http://example.com/path/foo.js
    fileSystem: file:///var/www/scripts/foo.js
    exactMatch: true
}
Mapping has stabilized.

Running: Rename foo.js => bar.js
Binding removed: {
       network: http://example.com/path/foo.js
    fileSystem: file:///var/www/scripts/bar.js
    exactMatch: true
}
Mapping has stabilized.

Running: Rename bar.js => foo.js
Binding created: {
       network: http://example.com/path/foo.js
    fileSystem: file:///var/www/scripts/foo.js
    exactMatch: true
}
Mapping has stabilized.

