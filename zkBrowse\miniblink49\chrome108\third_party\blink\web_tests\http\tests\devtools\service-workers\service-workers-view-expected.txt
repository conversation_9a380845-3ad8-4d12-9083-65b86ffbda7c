Tests ServiceWorkersView on resources panel.

Select ServiceWorkers tree element.
Register ServiceWorker for scope1
http://127.0.0.1:8000/devtools/service-workers/resources/scope1/
Network requests
Update
Unregister
Source
service-worker-empty.js
Received
Status
#N activated and is running
stop
Clients
Push
Push
Sync
Sync
Periodic Sync
Periodic Sync
Update Cycle
Version
Update Activity
Timeline
#N
Install
​
Start time
End time
#N
Wait
​
Start time
End time
#N
Activate
​
Start time
End time
Register ServiceWorker for scope2
http://127.0.0.1:8000/devtools/service-workers/resources/scope2
Network requests
Update
Unregister
Source
service-worker-empty.js
Received
Status
#N activated and is running
stop
Clients
Push
Push
Sync
Sync
Periodic Sync
Periodic Sync
Update Cycle
Version
Update Activity
Timeline
#N
Install
​
Start time
End time
#N
Wait
​
Start time
End time
#N
Activate
​
Start time
End time
http://127.0.0.1:8000/devtools/service-workers/resources/scope1/
Network requests
Update
Unregister
Source
service-worker-empty.js
Received
Status
#N activated and is running
stop
Clients
Push
Push
Sync
Sync
Periodic Sync
Periodic Sync
Update Cycle
Version
Update Activity
Timeline
#N
Install
​
Start time
End time
#N
Wait
​
Start time
End time
#N
Activate
​
Start time
End time
Unregister ServiceWorker for scope1
http://127.0.0.1:8000/devtools/service-workers/resources/scope2
Network requests
Update
Unregister
Source
service-worker-empty.js
Received
Status
#N activated and is running
stop
Clients
Push
Push
Sync
Sync
Periodic Sync
Periodic Sync
Update Cycle
Version
Update Activity
Timeline
#N
Install
​
Start time
End time
#N
Wait
​
Start time
End time
#N
Activate
​
Start time
End time
http://127.0.0.1:8000/devtools/service-workers/resources/scope1/ - deleted
Network requests
Update
Unregister
Source
service-worker-empty.js
Received
Status
#N is redundant
Clients
Push
Push
Sync
Sync
Periodic Sync
Periodic Sync
Update Cycle
Version
Update Activity
Timeline
#N
Install
​
Start time
End time
#N
Wait
​
Start time
End time
#N
Activate
​
Start time
End time
Unregister ServiceWorker for scope2

