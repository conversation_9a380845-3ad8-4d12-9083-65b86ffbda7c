<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background-image set to 'none'</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="G<PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-04-21 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background-image" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <link rel="match" href="../reference/ref-if-there-is-no-red.xht" />

        <meta name="flags" content="image" />
        <meta name="assert" content="Background-image set to 'none' does not set an image as the background." />
        <style type="text/css">
            div
            {
                background-image: url("support/red_box.png");
            }
            #test
            {
                background-image: none;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is <strong>no red</strong>.</p>
        <div id="test"></div>
    </body>
</html>