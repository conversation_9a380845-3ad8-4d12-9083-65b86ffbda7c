Tests that persistence syncs network and filesystem UISourceCodes.


Running: addFileSystem

Running: addFileMapping
Binding created: {
       network: http://127.0.0.1:8000/devtools/persistence/resources/foo.js
    fileSystem: file:///var/www/devtools/persistence/resources/foo.js
}

Running: addFileSystemRevision
network code: 'window.foo3 = 3;'
fileSystem code: 'window.foo3 = 3;'

Running: addFileSystemWorkingCopy
network code: 'window.foo4 = 4;'
fileSystem code: 'window.foo4 = 4;'

Running: resetFileSystemWorkingCopy
network code: 'window.foo3 = 3;'
fileSystem code: 'window.foo3 = 3;'

Running: setNetworkRevision
network code: 'window.foo2 = 2;'
fileSystem code: 'window.foo2 = 2;'

Running: setNetworkWorkingCopy
network code: 'window.foo5 = 5;'
fileSystem code: 'window.foo5 = 5;'

