<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background-repeat applied to elements with 'display' set to 'table-header-group'</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="<PERSON><PERSON>" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-11-29 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background-repeat" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <link rel="match" href="../reference/ref-filled-green-100px-square.xht" />

        <meta name="flags" content="image" />
        <meta name="assert" content="The 'background-repeat' property applies to elements with 'display' set to 'table-header-group'." />
        <style type="text/css">
            #test
            {
                background-color: red;
                background-image: url('support/green15x15.png');
                background-repeat: repeat;
                display: table-header-group;
            }
            #table
            {
                display: table;
                table-layout: fixed;
                width: 100px;
            }
            .row
            {
                display: table-row;
            }
            .cell
            {
                color: green;
                display: table-cell;
                height: 50px;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is a filled green square and <strong>no red</strong>.</p>

        <div id="table">

            <div id="test">

                <div class="row">
                    <div class="cell">a</div><div class="cell">b</div>
                </div>

                <div class="row">
                    <div class="cell">c</div><div class="cell">d</div>
                </div>

            </div>

        </div>

    </body>
</html>