#include <iostream>

// Test namespace fix
#define V8_MAJOR_VERSION 10

#include "../v8_10_8/v8_compatibility.h"

int main() {
    std::cout << "Testing V8 Compatibility Layer Namespace Fix" << std::endl;
    std::cout << "=============================================" << std::endl;
    
#if V8_MAJOR_VERSION >= 10
    std::cout << "? V8 10.8 compatibility layer is active" << std::endl;
    
    // Test UniqueId
    v8::UniqueId id1;
    v8::UniqueId id2(12345);
    
    std::cout << "? UniqueId compatibility working" << std::endl;
    
#else
    std::cout << "? V8 compatibility layer not active" << std::endl;
#endif
    
    std::cout << std::endl;
    std::cout << "Namespace fix test completed successfully!" << std::endl;
    
    return 0;
}
