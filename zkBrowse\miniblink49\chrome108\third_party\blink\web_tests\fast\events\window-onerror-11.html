<!DOCTYPE html>
<html>
<head>
    <script>
        window.isOnErrorTest = true;
    </script>
    <script src="../../resources/js-test.js"></script>
    <script src="resources/onerror-test.js"></script>
</head>
<body>
    <button onclick="%">Button.</button>
    <script>
        description("This test should trigger 'window.onerror' for the syntax error in the attribute handler, regardless of how it's set.");

        function callback(errorsHandled) {
            if (errorsHandled === 3)
                finishJSTest();
        }

        dumpOnErrorArgumentValuesAndReturn(true, callback);

        document.write('<button onclick="%">Button 2</button>\n');

        var button3 = document.createElement("button");
        button3.textContent = "Button 3";
        button3.setAttribute("onclick", "%");
        document.body.appendChild(button3);

        var buttons = document.querySelectorAll('button');
        for (var i = 0; i < buttons.length; ++i) {
            buttons[i].click();
        }
    </script>
</body>
</html>
