<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

 <head>

  <title>CSS Writing Modes Test: absolutely positioned non-replaced element - 'direction: ltr' and 'height' is 'auto' and 'top' and 'bottom' are not 'auto'</title>

  <link rel="author" title="Gérard Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" />
  <link rel="help" href="http://www.w3.org/TR/css-writing-modes-3/#vertical-layout" title="7.1 Principles of Layout in Vertical Writing Modes" />
  <link rel="help" href="http://www.w3.org/TR/CSS21/visudet.html#abs-non-replaced-width" title="10.3.7 Absolutely positioned, non-replaced elements" />
  <link rel="match" href="abs-pos-non-replaced-vlr-003-ref.xht" />

  <meta name="flags" content="ahem image" />
  <meta name="assert" content="When 'direction' is 'ltr' and 'height' is 'auto' and 'top' and 'bottom' are not 'auto', then solve for 'height'." />

  <link rel="stylesheet" type="text/css" href="/fonts/ahem.css" />
  <style type="text/css"><![CDATA[
  div#containing-block
    {
      background: red url("support/bg-red-2col-3row-320x320.png");
      color: transparent;
      direction: ltr;
      font: 80px/1 Ahem;
      height: 320px;
      position: relative;
      width: 320px;
      writing-mode: vertical-lr;
    }

  div#containing-block > span
    {
      background-color: green;
      bottom: 1em;
      height: auto;
      position: absolute;
      top: 2em;
      width: 1em;
    }

/*
"
Layout calculation rules (such as those in CSS2.1, Section 10.3) that apply to the horizontal dimension in horizontal writing modes instead apply to the vertical dimension in vertical writing modes.
"
7.1 Principles of Layout in Vertical Writing Modes
http://www.w3.org/TR/css-writing-modes-3/#vertical-layout

So here, *-top and *-bottom properties are input into the §10.3.7 algorithms where *-top properties refer to *-left properties in the layout rules and where *-bottom properties refer to *-right properties in the layout rules.

"
5. 'width' is 'auto', 'left' and 'right' are not 'auto', then solve for 'width'
"

'top' + 'margin-top' + 'border-top-width' + 'padding-top' + 'height' + 'padding-bottom' + 'border-bottom-width' + 'margin-bottom' + 'bottom' = height of containing block

So:

    160px : top
  +
      0px : margin-top
  +
      0px : border-top-width
  +
      0px : padding-top
  +
  (solve) : height: auto
  +
      0px : padding-bottom
  +
      0px : border-bottom-width
  +
      0px : margin-bottom
  +
     80px : bottom
    =====================
    320px : height of containing block

And so computed height value must be 80px .
*/

  ]]></style>

 </head>

 <body>

  <p><img src="support/pass-cdts-abs-pos-non-replaced.png" width="246" height="36" alt="Image download support must be enabled" /></p>

  <div id="containing-block">1 2 34<span></span></div>

 </body>
</html>