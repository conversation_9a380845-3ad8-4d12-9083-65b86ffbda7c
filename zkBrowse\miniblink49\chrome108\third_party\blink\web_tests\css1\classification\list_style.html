<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.6.6 list-style</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
.one {list-style: upper-alpha inside;}
.two {list-style: url(../resources/oransqr.gif) disc outside;}</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.one {list-style: upper-alpha inside;}
.two {list-style: url(../resources/oransqr.gif) disc outside;}
</PRE>
<HR>
<UL class="one">
<LI>The text in this item should not behave as expected; that is, it should line up with the capital-A on the left margin, leaving no blank space beneath the capital-A.
</UL>
<UL class="two">
<LI>The text in this item have an orange square for its bullet; failing that, a disc.  Also, the bullet should be outside the text block, as the list has been set to 'outside'.
</UL>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><UL class="one">
<LI>The text in this item should not behave as expected; that is, it should line up with the capital-A on the left margin, leaving no blank space beneath the capital-A.
</UL>
<UL class="two">
<LI>The text in this item have an orange square for its bullet; failing that, a disc.  Also, the bullet should be outside the text block, as the list has been set to 'outside'.
</UL>
</TD></TR></TABLE>
<SCRIPT>
// Force layout to ensure image loads block onload.
document.body.offsetTop;
</SCRIPT>
</BODY>
</HTML>
