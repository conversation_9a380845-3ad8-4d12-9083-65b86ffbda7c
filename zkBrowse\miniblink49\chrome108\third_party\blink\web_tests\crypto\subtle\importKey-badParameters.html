<!DOCTYPE html>
<html>
<head>
<script src="../../resources/js-test.js"></script>
<script src="resources/common.js"></script>
<script src="../../resources/sab-polyfill.js"></script>
</head>
<body>
<p id="description"></p>
<div id="console"></div>

<script>
description("Tests calling cypto.subtle.importKey with bad parameters");

jsTestIsAsync = true;

var aesCbc = {name: 'aes-cbc'};
var aesKeyBytes = new Uint8Array(16);
var extractable = true;

// Undefined key usage.
// FIXME: http://crbug.com/262383
//shouldThrow("crypto.subtle.importKey('raw', aesKeyBytes, aesCbc, extractable, undefined)");

Promise.resolve(null).then(function() {
    // Invalid data
    return crypto.subtle.importKey('raw', [], aesCbc, extractable, ['encrypt']);
}).then(failAndFinishJSTest, function(result) {
    logError(result);

    // 'null' treated as a Dictionary with default values - an empty dictionary
    return crypto.subtle.importKey('raw', null, aesCbc, extractable, ['encrypt']);
}).then(failAndFinishJSTest, function(result) {
    logError(result);

    // Invalid algorithm
    return crypto.subtle.importKey('raw', aesKeyBytes, null, extractable, ['encrypt']);
}).then(failAndFinishJSTest, function(result) {
    logError(result);

    // Invalid format.
    return crypto.subtle.importKey('invalid format', aesKeyBytes, aesCbc, extractable, ['encrypt']);
}).then(failAndFinishJSTest, function(result) {
    logError(result);

    // Invalid key usage (case sensitive).
    return crypto.subtle.importKey('raw', aesKeyBytes, aesCbc, extractable, ['ENCRYPT']);
}).then(failAndFinishJSTest, function(result) {
    logError(result);

    // If both the format and key usage are bogus, should complain about the
    // format first.
    return crypto.subtle.importKey('invalid format', aesKeyBytes, aesCbc, extractable, ['ENCRYPT']);
}).then(failAndFinishJSTest, function(result) {
    logError(result);

    // Missing hash parameter for HMAC.
    return crypto.subtle.importKey('raw', new Uint8Array(20), {name: 'hmac'}, extractable, ['sign']);
}).then(failAndFinishJSTest, function(result) {
    logError(result);

    // SHA-1 doesn't support the importKey operation.
    return crypto.subtle.importKey('raw', new Uint8Array(20), {name: 'sha-1'}, extractable, ['sign']);
}).then(failAndFinishJSTest, function(result) {
    logError(result);

    // SharedArrayBuffer-backed view is not allowed.
    if (window.SharedArrayBuffer) {
        var bytes = new Uint8Array(new SharedArrayBuffer(16));
        return crypto.subtle.importKey('raw', bytes, aesCbc, extractable, ['encrypt']);
    } else {
        return Promise.reject('SharedArrayBuffers not enabled.');
    }
}).then(failAndFinishJSTest, function(result) {
    logError(result);
}).then(finishJSTest, failAndFinishJSTest);

</script>

</body>
</html>
