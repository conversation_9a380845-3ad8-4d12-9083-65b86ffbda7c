<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

 <head>

  <title>CSS Writing Modes Test: absolutely positioned non-replaced element - 'direction: ltr' and 'left', 'width' and 'right' are not 'auto' (overconstrained)</title>

  <link rel="author" title="Gérard Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" />
  <link rel="help" href="http://www.w3.org/TR/css-writing-modes-3/#vertical-layout" title="7.1 Principles of Layout in Vertical Writing Modes" />
  <link rel="help" href="http://www.w3.org/TR/CSS21/visudet.html#abs-non-replaced-width" title="10.3.7 Absolutely positioned, non-replaced elements" />
  <link rel="match" href="abs-pos-non-replaced-vrl-004-ref.xht" />

  <meta name="flags" content="ahem image" />
  <meta name="assert" content="When 'direction' is 'ltr' and 'left', 'width' and 'right' are not 'auto' (overconstrained), then ignore 'right' and then solve for 'right'." />

  <link rel="stylesheet" type="text/css" href="/fonts/ahem.css" />
  <style type="text/css"><![CDATA[
  div#containing-block
    {
      background: red url("support/bg-red-3col-2row-320x320.png");
      color: transparent;
      direction: ltr;
      font: 80px/1 Ahem;
      height: 320px;
      position: relative;
      width: 320px;
    }

  div#containing-block > span
    {
      background-color: red;
      color: green;
      left: 2em;
      margin-left: 0em;
      margin-right: 0em;
      position: absolute;
      right: 2em;
      width: 1em;
      writing-mode: vertical-lr;
    }

/*
"
If none of the three is 'auto': If both 'margin-left' and 'margin-right' are 'auto', solve the equation under the extra constraint that the two margins get equal values, unless this would make them negative, in which case when direction of the containing block is 'ltr' ('rtl'), set 'margin-left' ('margin-right') to zero and solve for 'margin-right' ('margin-left'). If one of 'margin-left' or 'margin-right' is 'auto', solve the equation for that value. If the values are over-constrained, ignore the value for 'left' (in case the 'direction' property of the containing block is 'rtl') or 'right' (in case 'direction' is 'ltr') and solve for that value.
"

'left' + 'margin-left' + 'border-left-width' + 'padding-left' + 'width' + 'padding-right' + 'border-right-width' + 'margin-right' + 'right' = width of containing block

So:

    160px : left
  +
      0px : margin-left
  +
      0px : border-top-width
  +
      0px : padding-top
  +
     80px : width
  +
      0px : padding-right
  +
      0px : border-right-width
  +
      0px : margin-right
  +
    160px : right
    =====================
    320px : width of containing block

gives us:

    160px : left
  +
      0px : margin-left
  +
      0px : border-top-width
  +
      0px : padding-top
  +
     80px : width
  +
      0px : padding-right
  +
      0px : border-right-width
  +
      0px : margin-right
  +
  (solve) : right
    =====================
    320px : width of containing block

And so computed right value must be 80px .
*/

  ]]></style>

 </head>

 <body>

  <p><img src="support/pass-cdts-abs-pos-non-replaced.png" width="246" height="36" alt="Image download support must be enabled" /></p>

  <div id="containing-block">1 2 34<span>X</span></div>

 </body>
</html>