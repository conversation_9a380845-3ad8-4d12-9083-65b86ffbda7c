<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN"
"http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">

<!--

   Copyright 2001  The Apache Software Foundation 

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

-->
<!-- ========================================================================= -->
<!--  text effects                                                             -->
<!--                                                                           -->
<!-- <AUTHOR>                                               -->
<!-- @version $Id: textEffect3.svg,v 1.5 2004/08/18 07:12:21 vhardy Exp $ -->
<!-- ========================================================================= -->

<?xml-stylesheet type="text/css" href="../resources/test.css" ?>

<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="450" height="500" viewBox="0 0 450 500">
<title>Mix text transformation</title>

    <!-- ============================================================= -->
    <!-- Test content                                                  -->
    <!-- ============================================================= -->
    <g id="testContent">
        <text x="225" y="30" class="title">Gradient and filter on text and tspan</text>
        <text x="225" y="50" class="title">(Using System font and SVG font)</text>


<defs>

<font id="SVGArial" horiz-adv-x="904" >
<font-face
    font-family="SVGArial"
    units-per-em="2048"
    panose-1="2 11 6 4 2 2 2 2 2 4"
    ascent="1854"
    descent="-434" />

<missing-glyph horiz-adv-x="1536" d="M256 0V1280H1280V0H256ZM288 32H1248V1248H288V32Z" />

<glyph unicode="A" glyph-name="A" horiz-adv-x="1366" d="M-3 0L560 1466H769L1369 0H1148L977 444H364L203 0H-3ZM420 602H917L764 1008Q694 1193 660 1312Q632 1171 581 1032L420 602Z" />
<glyph unicode="B" glyph-name="B" horiz-adv-x="1366" d="M150 0V1466H700Q868 1466 969 1422T1128 1285T1186 1091Q1186 997 1135 914T981 780Q1114 741 1185 647T1257 425Q1257 322 1214 234T1106 97T946 25T709 0H150ZM344 850H661Q790 850 846 867Q920 889
957 940T995 1068Q995 1141 960 1196T860 1272T637 1293H344V850ZM344 173H709Q803 173 841 180Q908 192 953 220T1027 301T1056 425Q1056 507 1014 567T898 652T683 677H344V173Z" />
<glyph unicode="I" glyph-name="I" horiz-adv-x="569" d="M191 0V1466H385V0H191Z" />
<glyph unicode="K" glyph-name="K" horiz-adv-x="1366" d="M150 0V1466H344V739L1072 1466H1335L720 872L1362 0H1106L584 742L344 508V0H150Z" />
<glyph unicode="T" glyph-name="T" horiz-adv-x="1251" d="M531 0V1293H48V1466H1210V1293H725V0H531Z" />
</font>

	<linearGradient id="linearGradient">
		<stop style="stop-color:gold" offset="0"/>
		<stop style="stop-color:orange" offset=".5"/>
		<stop style="stop-color:crimson" offset="1"/>
	</linearGradient>


      <radialGradient id="radialGradient">
		<stop style="stop-color:gold" offset="0"/>
		<stop style="stop-color:orange" offset=".5"/>
		<stop style="stop-color:crimson" offset="1"/>
	</radialGradient>


        <filter id="dropShadow"  filterUnits="objectBoundingBox">
            <feGaussianBlur in="SourceGraphic" stdDeviation="2 2" /> 
        </filter>

</defs>

  <text x="120" y="160" style="fill:url(#linearGradient); font-size:40pt; font-family:Arial; text-anchor:middle">
    B<tspan dy="-20" style="font-size:20pt">A</tspan>T<tspan style="font-size:30pt">I</tspan><tspan rotate="20">K</tspan>
  </text>

  <text x="330" y="160" style="fill:url(#linearGradient); font-family:SVGArial; font-size:40pt; text-anchor:middle">
    B<tspan dy="-20" style="font-size:20pt">A</tspan>T<tspan style="font-size:30pt">I</tspan><tspan rotate="20">K</tspan>
  </text>

  <text x="120" y="260" style="stroke:url(#radialGradient); stroke-width:2; fill:none; font-size:40pt; text-anchor:middle; font-family:Arial">
    B<tspan dy="-20" style="font-size:20pt">A</tspan>T<tspan style="font-size:30pt">I</tspan><tspan rotate="20">K</tspan>
  </text>

  <text x="330" y="260" style="stroke:url(#radialGradient); stroke-width:2; fill:none; font-family:SVGArial; font-size:40pt; text-anchor:middle">
    B<tspan dy="-20" style="font-size:20pt">A</tspan>T<tspan style="font-size:30pt">I</tspan><tspan rotate="20">K</tspan>
  </text>

  <text x="120" y="360" style="filter:url(#dropShadow); fill:red; font-size:40pt; font-family:Arial; text-anchor:middle">
    B<tspan dy="-20" style="font-size:20pt">A</tspan>T<tspan style="font-size:30pt">I</tspan><tspan rotate="20">K</tspan>
  </text>
  
  <text x="330" y="360" style="filter:url(#dropShadow); fill:red; font-family:SVGArial; font-size:40pt; text-anchor:middle">
    B<tspan dy="-20" style="font-size:20pt">A</tspan>T<tspan style="font-size:30pt">I</tspan><tspan rotate="20">K</tspan>
  </text>
  
  <text x="120" y="400" style="font-size:12px; filter:none; fill:black; text-anchor:middle">(System font)</text>
  <text x="330" y="400" style="font-size:12px; filter:none; fill:black; text-anchor:middle">(SVG font)</text>

    </g>


    <!-- ============================================================= -->
    <!-- Batik sample mark                                             -->
    <!-- ============================================================= -->
    <use xlink:href="../resources/batikLogo.svg#Batik_Tag_Box" />

</svg>
