<html>
    <head>
        <script>
        function test()
        {
            var sf = document.getElementById('sf');
            sf.focus();
            if (window.testRunner) {
                testRunner.dumpAsText();
                testRunner.waitUntilDone();
                eventSender.keyDown("\r", []);
            }
        }
        function log(msg)
        {
            var res = document.getElementById('res');
            res.innerHTML = res.innerHTML + msg + "<br>";
        }
        </script>
    </head>
    <body onload="test()">
        This tests that onSearch fires correctly.<br>
        <input type="search" id="sf" onSearch="log('Test Passed'); testRunner.notifyDone();"></input>
        <div id="res"></div>
    </body>
</html>
