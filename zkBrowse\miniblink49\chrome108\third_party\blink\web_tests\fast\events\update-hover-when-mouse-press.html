<!DOCTYPE html>
<script src='../../resources/testharness.js'></script>
<script src='../../resources/testharnessreport.js'></script>
<style type="text/css">
.box:hover{background-color: rgb(0, 255, 255);}
.box:active{background-color: rgb(0, 0, 255);}
.box:active:hover{background-color: rgb(255, 255, 0);}

.box{border:2px solid #aaa; width:100px; height:100px;}
</style>

<body>
<div class="box" id="upbox"></div>
<div class="box" id="downbox"></div>

<script type="text/javascript">
var mouseover = false;
var mousedown = false;
var upbox = document.getElementById("upbox");
var downbox = document.getElementById("downbox");
var targetRect = downbox.getBoundingClientRect();
var offset = 20;
var x1 = targetRect.left + offset;
var y1 = targetRect.top + offset;
targetRect = upbox.getBoundingClientRect();
var x2 = targetRect.left + offset;
var y2 = targetRect.top + offset;
const leftButton = 0;

function mouseDownHandler(event) {
    mouseHoverActive = true;
    testHover.step(function () {
        assert_equals(event.target.id, "downbox");
        assert_equals(getComputedStyle(event.target).backgroundColor, "rgb(255, 255, 0)");
    });
}

function mouseOverHandler(event) {
    mouseHover = true;
    testHover.step(function () {
        assert_true(mouseHoverActive);
        assert_true(mouseHover);
        assert_equals(event.target.id, "upbox");
        assert_equals(getComputedStyle(upbox).backgroundColor, "rgb(0, 255, 255)");
        assert_equals(getComputedStyle(downbox).backgroundColor, "rgb(0, 0, 255)");
        testHover.done();
    });
}

function testHoverMousePress() {
    if (window.chrome && chrome.gpuBenchmarking) {
        var pointerActions =
            [{source: "mouse",
              actions: [
                { name: "pointerMove", x: x1, y: y1 },
                { name: "pointerDown", x: x1, y: y1, button: leftButton},
                { name: "pointerMove", x: x2, y: y2, button: leftButton }]}];
        chrome.gpuBenchmarking.pointerActionSequence(pointerActions);
    }
}

var testHover = async_test('Test that we update the hover states when we move the mouse to the element while the left button is pressed.');
upbox.addEventListener('mouseover', mouseOverHandler);
downbox.addEventListener('mousedown', mouseDownHandler);
testHoverMousePress();

</script>
</body>


