<!DOCTYPE html>
<script src="../../resources/testharness.js"></script>
<script src="../../resources/testharnessreport.js"></script>
<script>
var t = async_test("Garbage collection of ImageResourceContent while " +
                   "asynchronous loading of SVG is in progress " +
                   "shouldn't crash. crbug.com/726220");
var img;

function step1() {
  setTimeout(t.step_func(step2), 0);

  // 1. Creating an <img> element with SVG of which loading is not completed
  //    synchronously.
  img = document.createElement('img');
  img.src='data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg"><foreignObject><body xmlns="http://www.w3.org/1999/xhtml"><marquee></marquee></body></foreignObject></svg>';
  document.body.appendChild(img);
  assert_false(img.complete);
}

function step2() {
  // 2. Adopt the <img> to a new document.
  newdoc = document.implementation.createDocument("svg", null);
  newdoc.adoptNode(img);

  // 3. Do garbage collection. The oold ImageResourceContent is garbage
  //    collected while async SVG loading is still in progress.
  gc();

  setTimeout(t.step_func_done(), 100);
}

</script>
<body onload="setTimeout(t.step_func(step1), 0)"></body>
