<!doctype html>
<script src="../resources/testharness.js"></script>
<script src="../resources/testharnessreport.js"></script>

<div id="div1" data="hello"></div>
<div id="div2" test="yay"></div>

<script>
test(function () {
    assert_equals(div1, document.querySelector("[ data ]"));
    assert_equals(div1, document.querySelector("[ data = hello ]"));
    assert_equals(div2, document.querySelector("[ test $=ay ]"));
    assert_equals(div2, document.querySelector("[test *= a]"));
    assert_equals(div1, document.querySelector("[ data^=h ]"));
}, "Checks that whitespaces are allowed in attribute selectors");
</script>
