<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 3.1 important</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
P {color: green ! important;}
P {color: red;}
P#id1 {color: purple;}
</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>P {color: green ! important;}
P {color: red;}
P#id1 {color: purple;}

</PRE>
<HR>
<P>
This sentence should be green, because the declaration marked important should override any other normal declaration for the same element, even if it occurs later in the stylesheet.
</P>
<P ID="id1">
This sentence should also be green, even though it has an ID of <TT>id1</TT>, which would ordinarily make it purple.  This is because declarations marked important have more weight than normal declarations given in a STYLE attribute.
</P>
<P style="color: red;">
This sentence should also be green, even though it has a STYLE attribute declaring it to be red.  This is because declarations marked important have more weight than normal declarations given in a STYLE attribute.
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P>
This sentence should be green, because the declaration marked important should override any other normal declaration for the same element, even if it occurs later in the stylesheet.
</P>
<P ID="id1">
This sentence should also be green, even though it has an ID of <TT>id1</TT>, which would ordinarily make it purple.  This is because declarations marked important have more weight than normal declarations given in a STYLE attribute.
</P>
<P style="color: red;">
This sentence should also be green, even though it has a STYLE attribute declaring it to be red.  This is because declarations marked important have more weight than normal declarations given in a STYLE attribute.
</P>
</TD></TR></TABLE></BODY>
</HTML>
