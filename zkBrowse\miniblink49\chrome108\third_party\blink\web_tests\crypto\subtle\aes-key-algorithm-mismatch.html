<!DOCTYPE html>
<html>
<head>
<script src="../../resources/js-test.js"></script>
<script src="resources/common.js"></script>
</head>
<body>
<p id="description"></p>
<div id="console"></div>

<script>
description("Call encrypt using the wrong AES key");

jsTestIsAsync = true;

var keyData = hexStringToUint8Array("2b7e151628aed2a6abf7158809cf4f3c");
var data = asciiToUint8Array("hello");
var aesCbcKey = null;

Promise.resolve(null).then(function(result) {
    var usages = ['encrypt', 'decrypt'];
    var extractable = false;
    var algorithm = {name: 'aes-gcm'};

    return crypto.subtle.importKey('raw', keyData, algorithm, extractable, usages);
}).then(function(result) {
    key = result;
    shouldEvaluateAs("key.algorithm.name", "AES-GCM");

    // Can't use an AES-KW key for AES-CBC (even though both are AES keys).
    return crypto.subtle.encrypt({name: 'AES-CBC', iv: new Uint8Array(16)}, key, data);
}).then(failAndFinishJSTest, function(result) {
    logError(result);
}).then(finishJSTest, failAndFinishJSTest);

</script>

</body>
</html>
