// GENERATED CONTENT - DO NOT EDIT
// Content was automatically extracted by <PERSON><PERSON> into webref
// (https://github.com/w3c/webref)
// Source: Orientation Sensor (https://w3c.github.io/orientation-sensor/)

typedef (Float32Array or Float64Array or DOMMatrix) RotationMatrixType;

[SecureContext, Exposed=Window]
interface OrientationSensor : Sensor {
  readonly attribute FrozenArray<double>? quaternion;
  undefined populateMatrix(RotationMatrixType targetMatrix);
};

enum OrientationSensorLocalCoordinateSystem { "device", "screen" };

dictionary OrientationSensorOptions : SensorOptions {
  OrientationSensorLocalCoordinateSystem referenceFrame = "device";
};

[SecureContext, Exposed=Window]
interface AbsoluteOrientationSensor : OrientationSensor {
  constructor(optional OrientationSensorOptions sensorOptions = {});
};

[SecureContext, Exposed=Window]
interface RelativeOrientationSensor : OrientationSensor {
  constructor(optional OrientationSensorOptions sensorOptions = {});
};

dictionary AbsoluteOrientationReadingValues {
  required FrozenArray<double>? quaternion;
};

dictionary RelativeOrientationReadingValues : AbsoluteOrientationReadingValues {
};
