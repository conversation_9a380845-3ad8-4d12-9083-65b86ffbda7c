Tests that blocked reason is recognized correctly.


Running: testCSP
BlockedReason: csp

Running: testBlockedByDevTools1
Blocked patterns: resources**/silent*.js
Request: silent_script.js
BlockedReason: inspector

Running: testBlockedByDevTools2
Blocked patterns: a*b
Request: ba
BlockedReason: undefined

Running: testBlockedByDevTools3
Blocked patterns: ***pattern***
Request: there/is/a/pattern/inside.js
BlockedReason: inspector

Running: testBlockedByDevTools4
Blocked patterns: pattern
Request: patt1ern
BlockedReason: undefined

Running: testBlockedByDevTools5
Blocked patterns: *this***is*a*pattern
Request: file/this/is/the/pattern
BlockedReason: undefined

Running: testBlockedByDevTools6
Blocked patterns: *this***is*a*pattern
Request: this/is/a/pattern
BlockedReason: inspector

Running: testBlockedByDevTools6
Blocked patterns: *this***is*a*pattern
Request: this/is
BlockedReason: undefined

Running: testBlockedByDevTools7
Blocked patterns: pattern
Request: long/pattern/inside
BlockedReason: inspector

Running: testBlockedByDevTools8
Blocked patterns: pattern
Request: pattern
BlockedReason: inspector

Running: testBlockedByDevTools9
Blocked patterns: pattern;pattern
Request: pattern
BlockedReason: inspector

Running: testBlockedByDevTools10
Blocked patterns: a*b*c*d*e
Request: edcbaedcbaedcbaedcba
BlockedReason: undefined

Running: testBlockedByDevTools11
Blocked patterns: a*b*c*d*e
Request: edcbaedcbaedcbaedcbae
BlockedReason: inspector

Running: testBlockedByDevTools12
Blocked patterns: one1;two2
Request: one1two2
BlockedReason: inspector

Running: testBlockedByDevTools13
Blocked patterns: one1;two2;three3
Request: four4
BlockedReason: undefined

Running: testBlockedByDevTools14
Blocked patterns: one1;two2;three3
Request: only-two2-here
BlockedReason: inspector

Running: cleanupBlockedURLs
Blocked patterns: 
Request: silent_script.js
BlockedReason: undefined

