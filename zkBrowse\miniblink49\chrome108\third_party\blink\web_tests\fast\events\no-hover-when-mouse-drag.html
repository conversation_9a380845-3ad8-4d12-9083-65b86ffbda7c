<!DOCTYPE html>
<script src='../../resources/testharness.js'></script>
<script src='../../resources/testharnessreport.js'></script>
<style type="text/css">
.box:hover {background-color: rgb(0, 255, 255);}
.box:active {background-color: rgb(0, 0, 255);}
.box:active:hover {background-color: rgb(255, 255, 0);}

.box{border:2px solid #aaa; width:100px; height:100px; background-color: rgb(0, 0, 0);}
</style>

<body>
<div class="box" draggable="true" id="upbox"></div>
<div class="box" id="downbox"></div>

<script type="text/javascript">
var upbox = document.getElementById("upbox");
var downbox = document.getElementById("downbox");
var targetRect = upbox.getBoundingClientRect();
var offset = 20;
var x1 = targetRect.left + offset;
var y1 = targetRect.top + offset;
targetRect = downbox.getBoundingClientRect();
var x2 = targetRect.left + offset;
var y2 = targetRect.top + offset;


function mouseDownHandler(event) {
    testHover.step(function () {
        assert_equals(event.target.id, "upbox");
        assert_equals(getComputedStyle(event.target).backgroundColor, "rgb(255, 255, 0)");
    });
}

function mouseDragHandler(event) {
    testHover.step(function () {
        assert_equals(getComputedStyle(downbox).backgroundColor, "rgb(0, 0, 0)");
        assert_equals(getComputedStyle(event.target).backgroundColor, "rgb(255, 255, 0)");
    });
}

function mouseDragEndHandler(event) {
    testHover.step(function () {
        assert_equals(getComputedStyle(downbox).backgroundColor, "rgb(0, 255, 255)");
        assert_equals(getComputedStyle(upbox).backgroundColor, "rgb(0, 0, 0)");
    });
    testHover.done();
}

function testHoverMouseDrag() {
    if (window.eventSender) {
        eventSender.mouseMoveTo(x1, y1);
        eventSender.mouseDown();
        eventSender.leapForward(100);
        eventSender.mouseMoveTo(x1, y1+10);
        eventSender.mouseMoveTo(x1, y1+20);
        eventSender.mouseMoveTo(x2, y2);
        eventSender.mouseUp();
    }
}

var testHover = async_test('Tests that we do not update the hover states of any element when we are dragging an element around.');
upbox.addEventListener('drag', mouseDragHandler);
upbox.addEventListener('dragend', mouseDragEndHandler);
upbox.addEventListener('mousedown', mouseDownHandler);
testHoverMouseDrag();

</script>
</body>


