<p>This test verifies that the ENTER key fires the change event.</p>
<hr>
<input id="input" type="text" onchange="changeHandler()" onblur="blurHandler()"></input>
<pre id="console"></pre>

<script>
function log(s)
{
    document.getElementById('console').appendChild(document.createTextNode(s + "\n"));
}

function changeHandler()
{
    log ('PASS: change event fired.\n');
}

function blurHandler()
{
    log ('blur event fired.\n');
}

if (window.testRunner)
    testRunner.dumpAsText();
    
// change the field
document.getElementById('input').focus();
document.execCommand("InsertText", false, "foo bar baz");

// hit enter
input.focus();
if (window.eventSender)
    eventSender.keyDown("\r", []);

</script>
