<!DOCTYPE html>
<meta charset="utf-8">
<link rel="author" title="<PERSON>" href="<EMAIL>">
<link rel="help" href="https://www.w3.org/TR/CSS22/tables.html#model">
<link rel="match" href="../../reference/ref-filled-green-100px-square.xht">
<meta name="assert" content="relpos caption is containing block for abspos child" />
<title>
Captions and abspos descendants
</title>

<style>
caption, div {
  height:100px;
  width:50px;
  background:green;
}
#redSquare {
  height: 100px;
  width: 100px;
  background-color: red;
  position: absolute;
  z-index: -1;
}
</style>

<p>Test passes if there is a filled green square and <strong>no red</strong>.</p>
<div id="redSquare"></div>

<table>
  <caption style="position:relative">
    <div style="position:absolute; left:50px;">
    </div>
  </caption>
</table>
