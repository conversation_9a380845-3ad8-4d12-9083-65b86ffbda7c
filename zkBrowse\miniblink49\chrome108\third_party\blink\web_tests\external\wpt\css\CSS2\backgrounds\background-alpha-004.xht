<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <title>CSS Test: CSS Backgrounds: Overlapped alpha transparent backgrounds</title>
  <link rel="author" title="<PERSON>" href="mailto:<EMAIL>"/>
  <link rel="alternate" href="http://www.hixie.ch/tests/adhoc/css/background/alpha/004.html" type="text/html"/>
  <link rel="alternate" href="http://www.hixie.ch/tests/adhoc/css/background/alpha/004.xml" type="application/xhtml+xml"/>
  <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background"/>
  <style type="text/css">
   html { background: white url(support/background-alpha-test.png) fixed; color: black; }
   p { background: white; color: black; padding: 0.25em; border: thin solid black; display: inline; line-height: 2; }
   div { height: 512px; width: 512px; margin: 5em auto 100em; background: url(support/background-alpha-test.png); border: solid thick black; }
  </style>
 </head>
 <body>
  <p>This document has a white background overlayed with a fixed-positioned image with a multi-bit alpha channel. In the middle of the page is a square div with a height the size of two tiles, with the same background, but scrolling.</p>
  <div>

</div></body>
</html>
