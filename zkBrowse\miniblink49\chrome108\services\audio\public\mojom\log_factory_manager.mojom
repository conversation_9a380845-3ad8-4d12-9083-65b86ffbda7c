// Copyright 2018 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

module audio.mojom;

import "media/mojo/mojom/audio_logging.mojom";

// This interface is exposed by the audio service to allow trusted clients
// (like the browser process) to set a factory for creating audio logs.
interface LogFactoryManager {

  // Sets the factory for creating audio logs. If a previous factory exists,
  // the request is ignored.
  SetLogFactory(pending_remote<media.mojom.AudioLogFactory> factory);
};
