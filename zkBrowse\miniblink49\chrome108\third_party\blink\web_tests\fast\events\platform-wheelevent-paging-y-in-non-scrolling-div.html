<!DOCTYPE html>
<script src='../../resources/testharness.js'></script>
<script src='../../resources/testharnessreport.js'></script>
<script src='../../resources/gesture-util.js'></script>
<style>
  body {
    margin: 0;
  }

  #overflow {
    border:2px solid black;
    overflow:auto;
    white-space:nowrap;
    height:200px;
    width:200px;
  }

  #container {
    height: 185px;
    width: 600px;
    line-height: 0;
  }

  .box {
    height:181px;
    width:300px;
    display:inline-block;
  }
</style>

<div id="overflow">
  <div id="container">
    <div class="box" style="background-color:red"></div>
    <div class="box" style="background-color:green"></div>
  </div>
</div>

<script>
    const scrollDelta = 2;
    var last_event = null;
    var source = GestureSourceType.MOUSE_INPUT;
    const numTicksY = scrollDelta / pixelsPerTick();
    const expectedWheelDeltaY = numTicksY * LEGACY_MOUSE_WHEEL_TICK_MULTIPLIER;

    function mousewheelHandler(e)
    {
        last_event = e;
    }

    promise_test(async () => {
      const overflowElement = document.getElementById("overflow");
      overflowElement.addEventListener("mousewheel", mousewheelHandler, false);

      await smoothScroll(scrollDelta, 100, 110, source, 'down', SPEED_INSTANT, false /* precise_scrolling_deltas */, true /* scroll_by_page */);
      await conditionHolds( () => {
          return overflowElement.scrollTop == 0;
      });
      assert_equals(last_event.wheelDeltaY, -Math.floor(expectedWheelDeltaY));
      assert_equals(last_event.wheelDeltaX, 0);
      assert_equals(last_event.wheelDelta, -Math.floor(expectedWheelDeltaY));
    }, "Page-based wheel scrolls provide correct delta in event handler, don't scroll if there's no " +
       "overflow in direction when scrolling on div.");
</script>
