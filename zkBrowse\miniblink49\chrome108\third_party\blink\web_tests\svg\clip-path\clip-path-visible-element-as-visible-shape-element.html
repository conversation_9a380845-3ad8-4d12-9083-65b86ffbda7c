<!DOCTYPE HTML>
<script src=../../resources/run-after-layout-and-paint.js></script>
<script>
if (window.testRunner)
  testRunner.waitUntilDone();

window.onload = function () {
  runAfterLayoutAndPaint(function() {
    container.style.visibility = "visible";
    runAfterLayoutAndPaint(function() {
      container.style.visibility = "hidden";
      runAfterLayoutAndPaint(function() {
        container.style.visibility = "visible";
        if (window.testRunner)
          testRunner.notifyDone();
      });
    });
  });
}
</script>
<div id="container" style="visibility: hidden">
  <svg>
    <clipPath id="clip">
      <rect width="100" height="100"></rect>
    </clipPath>
    <rect width="400" height="400" fill="green" visibility="visible" clip-path="url(#clip)"></rect>
  </svg>
</div>
