<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.2.4 font-variant</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
.one {font-variant: small-caps;}
.two {font-variant: normal;}
</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.one {font-variant: small-caps;}
.two {font-variant: normal;}

</PRE>
<HR>
<P class="one">
This Paragraph should be in Small Caps.
</P>
<P class="one">
This Paragraph should be in Small Caps, but the Last Word in the Sentence should be <SPAN class="two">Normal</SPAN>.
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P class="one">
This Paragraph should be in Small Caps.
</P>
<P class="one">
This Paragraph should be in Small Caps, but the Last Word in the Sentence should be <SPAN class="two">Normal</SPAN>.
</P>
</TD></TR></TABLE></BODY>
</HTML>
