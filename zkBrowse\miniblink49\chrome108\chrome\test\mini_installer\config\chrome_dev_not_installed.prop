{"Condition": "'$BRAND' == 'Google Chrome'", "Files": {"$LOCAL_APPDATA\\$CHROME_DIR_DEV\\Application": {"exists": false}}, "RegistryEntries": {"HKEY_CURRENT_USER\\$CHROME_UPDATE_REGISTRY_SUBKEY_DEV": {"exists": "forbidden", "wow_key": "KEY_WOW64_32KEY"}, "HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\$CHROME_LONG_NAME_DEV": {"exists": "forbidden", "wow_key": "KEY_WOW64_32KEY"}, "HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\Windows Error Reporting\\RuntimeExceptionHelperModules": {"exists": "optional", "values": {"$LOCAL_APPDATA\\$CHROME_DIR_DEV\\Application\\$MINI_INSTALLER_FILE_VERSION\\chrome_wer.dll": {}}}, "HKEY_CURRENT_USER\\Software\\Classes\\$CHROME_HTML_PROG_ID_DEV$USER_SPECIFIC_REGISTRY_SUFFIX": {"exists": "forbidden"}, "HKEY_CURRENT_USER\\Software\\Classes\\$CHROME_SHORT_NAME_DEV$USER_SPECIFIC_REGISTRY_SUFFIX": {"exists": "forbidden"}, "HKEY_CURRENT_USER\\Software\\Clients\\StartMenuInternet\\$CHROME_SHORT_NAME_DEV$USER_SPECIFIC_REGISTRY_SUFFIX": {"exists": "forbidden"}, "HKEY_CURRENT_USER\\Software\\RegisteredApplications": {"exists": "optional", "values": {"$CHROME_LONG_NAME_DEV$USER_SPECIFIC_REGISTRY_SUFFIX": {}}}, "HKEY_CURRENT_USER\\Software\\Classes\\CLSID\\$CHROME_TOAST_ACTIVATOR_CLSID_DEV": {"exists": "forbidden"}, "HKEY_CURRENT_USER\\Software\\Classes\\CLSID\\$CHROME_ELEVATOR_CLSID_DEV": {"exists": "forbidden"}, "HKEY_CURRENT_USER\\Software\\Classes\\AppID\\$CHROME_ELEVATOR_CLSID_DEV": {"exists": "forbidden"}, "HKEY_CURRENT_USER\\Software\\Classes\\Interface\\$CHROME_ELEVATOR_IID_DEV": {"exists": "forbidden"}, "HKEY_CURRENT_USER\\Software\\Classes\\TypeLib\\$CHROME_ELEVATOR_IID_DEV": {"exists": "forbidden"}}}