<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Border-width set using three values</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="<PERSON><PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-06-26 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#border-width-properties" />
		<link rel="match" href="border-width-shorthand-003-ref.xht" />

        <meta name="assert" content="Applying three values to the border-width property applies the first value to the top. The second value to the left and right and the third value to the bottom." />
        <style type="text/css">
            div
            {
                border-width: 3px 10px 30px;
                border-style: solid;
                height: 1in;
            }
        </style>
    </head>
    <body>
        <p>Test passes if the top border is thinner than all the other borders and the left and right is thinner than the bottom border.</p>
        <div></div>
    </body>
</html>