Tests to make sure the proper view is used for the data that is received in network panel.

Simple JSON
Testing with MimeType: application/json, and StatusCode: 200
Content: [533,3223]

ResourceType(xhr): SearchableView > widget vbox json-view
ResourceType(fetch): SearchableView > widget vbox json-view
ResourceType(document): SearchableView > widget vbox json-view
ResourceType(other): SearchableView > widget vbox json-view

MIME JSON
Testing with MimeType: application/vnd.document+json, and StatusCode: 200
Content: {"foo0foo": 123}

ResourceType(xhr): SearchableView > widget vbox json-view
ResourceType(fetch): SearchableView > widget vbox json-view
ResourceType(document): SearchableView > widget vbox json-view
ResourceType(other): SearchableView > widget vbox json-view

Simple XML
Testing with MimeType: text/xml, and StatusCode: 200
Content: <bar><foo/></bar>

ResourceType(xhr): SearchableView > widget shadow-xml-view source-code
ResourceType(fetch): SearchableView > widget shadow-xml-view source-code
ResourceType(document): SearchableView > widget shadow-xml-view source-code
ResourceType(other): SearchableView > widget shadow-xml-view source-code

XML MIME But JSON
Testing with MimeType: text/xml, and StatusCode: 200
Content: {"foo0": "barr", "barr": "fooo"}

ResourceType(xhr): SearchableView > widget vbox json-view
ResourceType(fetch): SearchableView > widget vbox json-view
ResourceType(document): SearchableView > widget vbox json-view
ResourceType(other): SearchableView > widget vbox json-view

HTML MIME But JSON
Testing with MimeType: text/html, and StatusCode: 200
Content: {"hi": "hi"}

ResourceType(xhr): SearchableView > widget vbox json-view
ResourceType(fetch): SearchableView > widget vbox json-view
ResourceType(document): SearchableView > widget vbox json-view
ResourceType(other): SearchableView > widget vbox json-view

TEXT MIME But JSON
Testing with MimeType: text/html, and StatusCode: 200
Content: {"hi": "hi"}

ResourceType(xhr): SearchableView > widget vbox json-view
ResourceType(fetch): SearchableView > widget vbox json-view
ResourceType(document): SearchableView > widget vbox json-view
ResourceType(other): SearchableView > widget vbox json-view

HTML MIME With 500 error
Testing with MimeType: text/html, and StatusCode: 500
Content: This
<b>is a </b><br /><br />test.

ResourceType(xhr): widget vbox html request-view
ResourceType(fetch): widget vbox html request-view
ResourceType(document): widget vbox html request-view
ResourceType(other): widget vbox html request-view

Plain Text MIME But HTML/XML
Testing with MimeType: text/plain, and StatusCode: 200
Content: This
<b>is a </b><br /><br />test.

ResourceType(xhr): widget vbox html request-view
ResourceType(fetch): widget vbox html request-view
ResourceType(document): widget vbox html request-view
ResourceType(other): widget vbox html request-view

XML With Unknown MIME
Testing with MimeType: text/foobar, and StatusCode: 200
Content: <bar><foo/></bar>

ResourceType(xhr): SearchableContainer > SearchableView > widget vbox
ResourceType(fetch): SearchableContainer > SearchableView > widget vbox
ResourceType(document): SearchableContainer > SearchableView > widget vbox
ResourceType(other): SearchableContainer > SearchableView > widget vbox

XML With Error 500
Testing with MimeType: text/xml, and StatusCode: 500
Content: <bar><foo/></bar>

ResourceType(xhr): SearchableView > widget shadow-xml-view source-code
ResourceType(fetch): SearchableView > widget shadow-xml-view source-code
ResourceType(document): SearchableView > widget shadow-xml-view source-code
ResourceType(other): SearchableView > widget shadow-xml-view source-code

Unknown MIME Text With Error 500
Testing with MimeType: text/foobar, and StatusCode: 500
Content: Foo Bar

ResourceType(xhr): SearchableContainer > SearchableView > widget vbox
ResourceType(fetch): SearchableContainer > SearchableView > widget vbox
ResourceType(document): SearchableContainer > SearchableView > widget vbox
ResourceType(other): SearchableContainer > SearchableView > widget vbox

Binary Image File
Testing with MimeType: image/png, and StatusCode: 200
Content: Bin**NULL**ary File**NULL****NULL**

ResourceType(xhr): widget vbox image-view
ResourceType(fetch): widget vbox image-view
ResourceType(document): widget vbox image-view
ResourceType(other): widget vbox image-view

Binary Blank Image File
Testing with MimeType: image/png, and StatusCode: 200
Content: 

ResourceType(xhr): widget vbox image-view
ResourceType(fetch): widget vbox image-view
ResourceType(document): widget vbox image-view
ResourceType(other): widget vbox image-view


