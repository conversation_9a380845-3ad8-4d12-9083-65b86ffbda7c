<!DOCTYPE html>
<head>
    <script src="../../resources/js-test.js"></script>
    <script>
        var newWindow;

        if (window.testRunner) {
            testRunner.dumpAsText();
            testRunner.waitUntilDone();
        }

        function clickHandler() {
            setTimeout(function() {
                newWindow = window.open("about:blank");
                self.focus();
                debug("Test calling window.open() with a 5000+ ms delay. A popup should not be allowed.")
                shouldBeNull("newWindow");

                if (window.testRunner)
                    testRunner.notifyDone();
            }, 5020);
            if (window.eventSender)
                eventSender.leapForward(5020);
        }

        function clickButton() {
            var button = document.getElementById("test");
            var buttonX = button.offsetLeft + button.offsetWidth / 2;
            var buttonY = button.offsetTop + button.offsetHeight / 2;
            if (window.eventSender) {
                eventSender.mouseMoveTo(buttonX, buttonY);
                eventSender.mouseDown();
                eventSender.mouseUp();
            }
        }
    </script>
</head>
<body onload="clickButton()">
    <button id="test" onclick="clickHandler()">Click Here</button>
    <div id="console"></div>
</body>
