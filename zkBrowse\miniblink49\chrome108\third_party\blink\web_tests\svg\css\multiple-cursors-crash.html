<html>
<script>
svgNS = "http://www.w3.org/2000/svg";

gc = window.gc || function()
{
    if (window.GCController)
        GCController.collect();
        
    for (var i = 0; i < 10000; ++i)
        var s = new String("AAAA");
}

window.onload = function()
{
    if (window.testRunner) {
        testRunner.dumpAsText();
        testRunner.waitUntilDone();
    }    

    cursor = document.body.appendChild(document.createElementNS(svgNS, "cursor"));
    cursor.id = "cursor";

    element = document.body.appendChild(document.createElementNS(svgNS, "element"));
    element.style.setProperty("cursor", "url(#cursor)");
  
    setTimeout(step2, 0);
}

function step2()
{
    fakeCursor = document.body.insertBefore(document.createElementNS(svgNS, "cursor"), cursor);
    fakeCursor.id = "cursor";
  
    element.style.removeProperty("cursor");
    
    document.body.removeChild(element);
    element = null;
    gc();
  
    setTimeout(finishTest, 0);
}

function finishTest()
{
    document.body.removeChild(cursor);
    cursor = null;
    gc();
  
    document.body.innerHTML = "PASS";
    if (window.testRunner)
        testRunner.notifyDone();
}
</script>
</html>
