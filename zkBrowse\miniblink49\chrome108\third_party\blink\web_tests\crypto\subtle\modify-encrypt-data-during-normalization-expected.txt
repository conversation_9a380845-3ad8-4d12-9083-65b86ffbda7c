Tests crypto.subtle.encrypt() using a BufferSource that is modified during algorithm normalization

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".

Importing key...

Encrypting (as a control group)...
PASS: Encryption should be [7649abac8119b246cee98e9b12e9197d5086cb9b507219ee95db113a917678b273bed6b8e3c1743b7116e69e222295163ff1caa1681fac09120eca307586e1a78cb82807230e1321d3fae00d18cc2012] and was

Encrypting again, using an algorithm that mutates the array buffer...
Accessed name property
Corrupted plainText
Accessed iv property
Corrupted plainText
PASS: plainText should be [0000bee22e409f96e93d7e117393172aae2d8a571e03ac9c9eb76fac45af8e5130c81c46a35ce411e5fbc1191a0a52eff69f2445df4f9b17ad2b417be66c3710] and was
PASS: Encryption should be [7649abac8119b246cee98e9b12e9197d5086cb9b507219ee95db113a917678b273bed6b8e3c1743b7116e69e222295163ff1caa1681fac09120eca307586e1a78cb82807230e1321d3fae00d18cc2012] and was
PASS successfullyParsed is true

TEST COMPLETE

