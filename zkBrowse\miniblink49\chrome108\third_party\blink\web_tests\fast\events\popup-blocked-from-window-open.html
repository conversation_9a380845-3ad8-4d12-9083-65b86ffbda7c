<html>
<head>
<script>
if (window.testRunner) {
  testRunner.dumpAsText();
  testRunner.waitUntilDone();
    // Record current window count.
    window.windowCount = testRunner.windowCount();
}

function test() {
    if (window.testRunner && testRunner.windowCount() == window.windowCount)
        document.getElementById("console").innerText = "PASSED";
    // Close the test.
    testRunner.notifyDone();
}
</script>
</head>
<body onload="test()">
<script>
window.open("javascript:window.open('about:blank', '_blank', 'height=600,width=720')");
</script>
If the inner pop-up was not blocked then there will be one additional windows generated. Otherwise, the test passes.<br>
For more details, please refer to https://bugs.webkit.org/show_bug.cgi?id=37138.
<div id="console">FAILED</div>
</body>
</html>
