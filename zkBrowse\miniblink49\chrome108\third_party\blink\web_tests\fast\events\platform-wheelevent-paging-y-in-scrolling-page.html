<!DOCTYPE html>
<script src='../../resources/testharness.js'></script>
<script src='../../resources/testharnessreport.js'></script>
<script src='../../resources/gesture-util.js'></script>
<div style="height:900px;width:400px">
  <div style="background-color:red;height:900px;width:200px;position:relative;left:0px;top:0px"></div>
  <div style="background-color:green;height:900px;width:200px;position:relative;left:200px;top:-900px"></div>
</div>
<div style="height:900px;width:400px">
  <div style="background-color:blue;height:900px;width:200px;position:relative;left:0px;top:0px"></div>
  <div style="background-color:yellow;height:900px;width:200px;position:relative;left:200px;top:-900px"></div>
</div>

<script>
    var givenScrollTop = 2; // When paging, this is ignored. Every event is one page.
    var expectedScrollTop = 525; // Window is 800x600. Scroll 87.5% of visible.
    var last_event = null;
    var source = GestureSourceType.MOUSE_INPUT;
    const numTicksY = givenScrollTop / pixelsPerTick();
    const expectedWheelDeltaY = numTicksY * LEGACY_MOUSE_WHEEL_TICK_MULTIPLIER;

    function mousewheelHandler(e)
    {
      last_event = e;
    }

    promise_test(async () => {
      document.body.addEventListener("mousewheel", mousewheelHandler, false);

      await smoothScroll(givenScrollTop, 100, 110, source, 'down', SPEED_INSTANT, false /* precise_scrolling_deltas */, true /* scroll_by_page */);
      await waitForAnimationEndTimeBased( () => {
          return document.scrollingElement.scrollTop == window.expectedScrollTop;
      });
      assert_equals(last_event.wheelDeltaY, -Math.floor(expectedWheelDeltaY));
      assert_equals(last_event.wheelDeltaX, 0);
      assert_equals(last_event.wheelDelta, -Math.floor(expectedWheelDeltaY));
    }, "Page-based wheel scrolls provide correct delta in event handler, scrolling on overflow document.");
</script>