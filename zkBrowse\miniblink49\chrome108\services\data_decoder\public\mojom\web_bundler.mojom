// Copyright 2020 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

module data_decoder.mojom;

import "services/data_decoder/public/mojom/resource_snapshot_for_web_bundle.mojom";
import "mojo/public/mojom/base/file.mojom";

enum WebBundlerError {
  kOK,
  kConnectionError,
  kFileOpenFailed,
  kWebBundlerConnectionError,
  kInvalidInput,
};

// Bundler interface to generate a web bundle from snapshots.
interface WebBundler {
  // Generates a web bundle from |snapshots| and writes to the passed |file|.
  Generate(
      array<pending_remote<ResourceSnapshotForWebBundle>> snapshots,
      mojo_base.mojom.File file)
    => (uint64 file_size, WebBundlerError error);
};
