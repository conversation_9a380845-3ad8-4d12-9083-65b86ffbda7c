<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

 <head>

  <title>CSS Writing Modes Test: box offsets - position relative</title>

  <link rel="author" title="<PERSON><PERSON>" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" />
  <link rel="help" title="7.1 Principles of Layout in Vertical Writing Modes" href="http://www.w3.org/TR/css-writing-modes-3/#vertical-layout" />
  <link rel="match" href="abs-pos-non-replaced-vlr-013-ref.xht" />

  <meta content="image" name="flags" />
  <meta content="The 'top', 'right', 'bottom', 'left' property, for relative positioning, specify the offset of the box itself from its normal in-flow position, from the position it would have had if it had been static. The 'top', 'right', 'bottom', 'left' offset property are physical properties, not logical ones in vertical writing-modes." name="assert" />

  <style type="text/css"><![CDATA[
  html
    {
      writing-mode: vertical-lr;
    }

  img
    {
      position: relative;
    }

  img.moves-toward-left
    {
      right: 80px;
    }

  img.moves-toward-top
    {
      bottom: 80px;
    }

  img.moves-toward-right
    {
      left: 80px;
    }

  img.moves-toward-bottom
    {
      top: 80px;
    }

  /*
  In this testcase, 7 red 80px by 80px squares overlap
  another red 80px by 80px square (placed in the center of
  a 3 by 3 grid of squares) and then, at the end, one single
  green 80px by 80px square overlaps all 8 other red squares.
  */
  ]]></style>

 </head>

 <body>

  <p><img src="support/pass-cdts-abs-pos-non-replaced.png" width="246" height="36" alt="Image download support must be enabled" /></p>

  <div><img class="moves-toward-bottom moves-toward-right" src="support/100x100-red.png" width="80" height="80" alt="Image download support must be enabled" /><img class="moves-toward-right" src="support/100x100-red.png" width="80" height="80" alt="Image download support must be enabled" /><img class="moves-toward-right moves-toward-top" src="support/100x100-red.png" width="80" height="80" alt="Image download support must be enabled" /></div>

  <div><img class="moves-toward-bottom" src="support/100x100-red.png" width="80" height="80" alt="Image download support must be enabled" /><img src="support/100x100-red.png" width="80" height="80" alt="Image download support must be enabled" /><img class="moves-toward-top" src="support/100x100-red.png" width="80" height="80" alt="Image download support must be enabled" /></div>

  <div><img class="moves-toward-left moves-toward-bottom" src="support/100x100-red.png" width="80" height="80" alt="Image download support must be enabled" /><img class="moves-toward-left" src="support/100x100-red.png" width="80" height="80" alt="Image download support must be enabled" /><img class="moves-toward-left moves-toward-top" src="support/swatch-green.png" width="80" height="80" alt="Image download support must be enabled" /></div>

 </body>
</html>