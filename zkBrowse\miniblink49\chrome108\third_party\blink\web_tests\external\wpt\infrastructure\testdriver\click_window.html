<!DOCTYPE html>
<meta charset="utf-8">
<title>TestDriver click method in window</title>
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<script src="/resources/testdriver.js"></script>
<script src="/resources/testdriver-vendor.js"></script>

<script>
setup({single_test: true});
addEventListener("load", () => {
    let child = window.open("click_child.html");
    child.addEventListener("load", () => {
        let button = child.document.getElementById("button");
        test_driver
            .click(button)
            .then(() => {
                assert_equals(child.document.getElementById("log").textContent, "PASS");
                done();
            })
            .catch(() => assert_unreached("click failed"));
      });
})
</script>
