<!DOCTYPE html>
<title>MouseEvent.offsetX/offsetY for an SVG element is always relative to the outermost &lt;svg> CSS layout box</title>
<script src="../../resources/testharness.js"></script>
<script src="../../resources/testharnessreport.js"></script>
<style>
svg {
    border: 10px solid blue;
    border-width: 20px 40px 30px 10px;
}
</style>
<svg width="400" height="400" viewBox="0 0 400000 400000">
  <rect x="40000" y="60000" width="80000" height="80000" fill="blue"/>
  <rect x="160000" y="180000" width="80000" height="80000" fill="blue" stroke="lightblue" stroke-width="10000"/>
</svg>
<script>
function check_offset(test, element, clientX, clientY, expectedOffsetX, expectedOffsetY) {
    element.onclick = test.step_func(function(event) {
        assert_equals(event.offsetX, expectedOffsetX, 'offsetX');
        assert_equals(event.offsetY, expectedOffsetY, 'offsetY');
    });
    var mouseEvent = new MouseEvent('click', { clientX: clientX, clientY: clientY });
    element.dispatchEvent(mouseEvent);
}

test(function(t) {
    var rects = document.querySelectorAll('rect');
    var svgBoundingRect = document.querySelector('svg').getBoundingClientRect();
    var offsetBase = { x: svgBoundingRect.left + 10, y: svgBoundingRect.top + 20 };
    check_offset(t, rects[0], offsetBase.x + 80, offsetBase.y + 100, 80, 100);
    check_offset(t, rects[1], offsetBase.x + 200, offsetBase.y + 220, 200, 220);
});
</script>
