<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

 <head>

  <title>CSS Reftest Reference</title>
  <link rel="author" title="Gérard Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" />

  <style type="text/css"><![CDATA[
  body
  {
  background-color: navy;
  border: blue solid;
  color: white;
  margin: 1em;
  padding: 2em;
  }

  div
  {
  background: green url("support/square-purple.png") no-repeat fixed center;
  border: lime solid;
  padding: 3em;
  }
  ]]></style>

 </head>

 <body>

  <div>In the exact middle of this page, there should be a purple tile. The tile must only be visible when the green box overlaps the tile; it must be hidden when overlapping the blue background.</div>

 </body>
</html>
