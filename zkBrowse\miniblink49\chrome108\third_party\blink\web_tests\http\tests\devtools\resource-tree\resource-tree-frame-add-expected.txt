Tests resource tree model on iframe addition, compares resource tree against golden. Every line is important.

Before addition
====================================
Resources:
stylesheet http://127.0.0.1:8000/devtools/resource-tree/resources/styles-initial.css
document http://127.0.0.1:8000/devtools/resources/inspected-page.html

Resources URL Map:
http://127.0.0.1:8000/devtools/resource-tree/resources/styles-initial.css == http://127.0.0.1:8000/devtools/resource-tree/resources/styles-initial.css
http://127.0.0.1:8000/devtools/resources/inspected-page.html == http://127.0.0.1:8000/devtools/resources/inspected-page.html

Resources Tree:
Frames
    top
        Stylesheets
            styles-initial.css
        inspected-page.html
Sources:
-------- Setting mode: [frame]
top
  inspected-page.html
  styles-initial.css
Sources:
-------- Setting mode: [frame/domain]
top
  127.0.0.1:8000
    inspected-page.html
    styles-initial.css
Sources:
-------- Setting mode: [frame/domain/folder]
top
  127.0.0.1:8000
    devtools
      resource-tree/resources
        styles-initial.css
      resources
        inspected-page.html
Sources:
-------- Setting mode: [domain]
127.0.0.1:8000
  inspected-page.html
  styles-initial.css
Sources:
-------- Setting mode: [domain/folder]
127.0.0.1:8000
  devtools
    resource-tree/resources
      styles-initial.css
    resources
      inspected-page.html

After addition
====================================
Resources:
document http://127.0.0.1:8000/devtools/resource-tree/resources/resource-tree-frame-add-iframe.html
script http://127.0.0.1:8000/devtools/resource-tree/resources/script-navigated.js
stylesheet http://127.0.0.1:8000/devtools/resource-tree/resources/styles-initial.css
stylesheet http://127.0.0.1:8000/devtools/resource-tree/resources/styles-navigated.css
document http://127.0.0.1:8000/devtools/resources/inspected-page.html

Resources URL Map:
http://127.0.0.1:8000/devtools/resource-tree/resources/resource-tree-frame-add-iframe.html == http://127.0.0.1:8000/devtools/resource-tree/resources/resource-tree-frame-add-iframe.html
http://127.0.0.1:8000/devtools/resource-tree/resources/script-navigated.js == http://127.0.0.1:8000/devtools/resource-tree/resources/script-navigated.js
http://127.0.0.1:8000/devtools/resource-tree/resources/styles-initial.css == http://127.0.0.1:8000/devtools/resource-tree/resources/styles-initial.css
http://127.0.0.1:8000/devtools/resource-tree/resources/styles-navigated.css == http://127.0.0.1:8000/devtools/resource-tree/resources/styles-navigated.css
http://127.0.0.1:8000/devtools/resources/inspected-page.html == http://127.0.0.1:8000/devtools/resources/inspected-page.html

Resources Tree:
Frames
    top
        resource-tree-frame-add-iframe.html
            Scripts
                script-navigated.js
            Stylesheets
                styles-navigated.css
            resource-tree-frame-add-iframe.html
        Stylesheets
            styles-initial.css
        inspected-page.html
Sources:
-------- Setting mode: [frame]
top
  inspected-page.html
  styles-initial.css
  resource-tree-frame-add-iframe.html
    resource-tree-frame-add-iframe.html
    script-navigated.js
    styles-navigated.css
Sources:
-------- Setting mode: [frame/domain]
top
  127.0.0.1:8000
    inspected-page.html
    styles-initial.css
  resource-tree-frame-add-iframe.html
    127.0.0.1:8000
      resource-tree-frame-add-iframe.html
      script-navigated.js
      styles-navigated.css
Sources:
-------- Setting mode: [frame/domain/folder]
top
  127.0.0.1:8000
    devtools
      resource-tree/resources
        styles-initial.css
      resources
        inspected-page.html
  resource-tree-frame-add-iframe.html
    127.0.0.1:8000
      devtools/resource-tree/resources
        resource-tree-frame-add-iframe.html
        script-navigated.js
        styles-navigated.css
Sources:
-------- Setting mode: [domain]
127.0.0.1:8000
  inspected-page.html
  resource-tree-frame-add-iframe.html
  script-navigated.js
  styles-initial.css
  styles-navigated.css
Sources:
-------- Setting mode: [domain/folder]
127.0.0.1:8000
  devtools
    resource-tree/resources
      resource-tree-frame-add-iframe.html
      script-navigated.js
      styles-initial.css
      styles-navigated.css
    resources
      inspected-page.html

