<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8"/>
<title>unicode-bidi: pre plaintext</title>

<link rel="author" title="<PERSON>" href='mailto:<EMAIL>'/>
<link rel="help" href='http://www.w3.org/TR/css-writing-modes-3/#text-direction'/>
<link rel="match" href="reference/block-plaintext-006.html"/>
<meta name="assert" content='If unicode-bidi:plaintext is applied to a pre element, each line of characters after a linebreak is displayed according to the first strong character after the linebreak.'/>
<style type="text/css">
.test pre { unicode-bidi: plaintext; }

 /* the following styles are not part of the test */
.test, .ref { font-size: 150%; border: 1px solid orange; margin: 10px; width: 10em; padding: 5px; clear: both; }
input { margin: 5px; }
@font-face {
    font-family: 'ezra_silregular';
    src: url('/fonts/sileot-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;
    }
.test, .ref { font-family: ezra_silregular, serif; }
pre { font-family: ezra_silregular, serif; width: 100%; border: 0; margin: 0; font-size: 1em; }
</style>
</head>
<body>
<p class="instructions" dir="ltr">Test passes if the two boxes display the same glyphs in the same order, with the same line breaks.</p>


<!--Notes:
Key to entities used below:
        &#x5d0; ... &#x5d5; - The first six Hebrew letters (strongly RTL).
        &#x202d; - The LRO (left-to-right-override) formatting character.
        &#x202c; - The PDF (pop directional formatting) formatting character; closes LRO.
-->


<div class="test">
<pre><!-- comment token so following LF character isn't ignored by the HTML parser -->
&gt; a &gt; &#x5d1; &gt; c &gt;
&gt; &#x5d0; &gt; b &gt; &#x5d2; &gt;
&gt; a &gt; &#x5d1; &gt; c &gt;
<!-- need a blank line for whitespace to appear-->
</pre>
</div>

<div class="ref">
<div>&#xA0;</div>
<div dir="ltr">&#x202d;&gt; a &gt; &#x5d1; &gt; c &gt;&#x202c;</div>
<div dir="rtl">&#x202d;&lt;  &#x5d2; &lt; b &lt;  &#x5d0; &lt;&#x202c;</div>
<div dir="ltr">&#x202d;&gt; a &gt; &#x5d1; &gt; c &gt;&#x202c;</div>
<div>&#xA0;</div>
</div>





</body></html>
