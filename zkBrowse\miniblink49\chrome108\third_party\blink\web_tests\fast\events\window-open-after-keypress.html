<!DOCTYPE html>
<script>
function test()
{
    if (window.testRunner) {
        testRunner.dumpAsText();
        testRunner.dumpNavigationPolicy();
        document.querySelector("#link").focus();
        // Ctrl+Enter might get translated to a click event, which gets in turn
        // would lead to a default navigation policy of new-foreground-tab. We
        // test here that Ctrl+something else doesn't override the navigation
        // policy for window.open().
        eventSender.keyDown("n", ["ctrlKey"]);
    }
}

function key()
{
    window.open("about:blank", "", "popup");
}
</script>
<body onload="test()">
<p>Tests that hitting any key but enter doesn't override the regular window policy.</p>
<button onkeypress="key()" id="link">link</button>
</body>
