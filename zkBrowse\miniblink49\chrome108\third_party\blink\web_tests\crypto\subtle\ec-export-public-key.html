<!DOCTYPE html>
<html>
<head>
<script src="../../resources/js-test.js"></script>
<script src="resources/common.js"></script>
</head>
<body>
<p id="description"></p>
<div id="console"></div>

<script>
description("Test exporting a public EC key.");

jsTestIsAsync = true;

var extractable = true;

var publicKeySpkiHex = "3059301306072A8648CE3D020106082A8648CE3D030107034200049CB0CF69303DAFC761D4E4687B4ECF039E6D34AB964AF80810D8D558A4A8D6F72D51233A1788920A86EE08A1962C79EFA317FB7879E297DAD2146DB995FA1C78";

debug("\nImporting a SPKI key...");
crypto.subtle.importKey("spki", hexStringToUint8Array(publicKeySpkiHex), {name: "ECDSA", namedCurve: "P-256"}, extractable, ['verify']).then(function(result) {
    key = result;

    debug("\nExporting the key as JWK...");
    return crypto.subtle.exportKey("jwk", key);
}).then(function(result) {
    exportedJWK = result;

    shouldEvaluateAs("exportedJWK.kty", "EC");
    shouldEvaluateAs("exportedJWK.crv", "P-256");
    shouldEvaluateAs("exportedJWK.x", "nLDPaTA9r8dh1ORoe07PA55tNKuWSvgIENjVWKSo1vc");
    shouldEvaluateAs("exportedJWK.y", "LVEjOheIkgqG7gihlix576MX-3h54pfa0hRtuZX6HHg");
    shouldBe("exportedJWK.alg", "undefined");
    shouldBe("exportedJWK.ext", "true");
    shouldBe("exportedJWK.key_ops", "['verify']");
    shouldBe("exportedJWK.use", "undefined");

    debug("\nExporting the key as SPKI...");
    return crypto.subtle.exportKey("spki", key);
}).then(function(result) {
    exportedSpki = result;

    bytesShouldMatchHexString("exportedSpki", publicKeySpkiHex, exportedSpki);
}).then(finishJSTest, failAndFinishJSTest);

</script>

</body>
</html>
