# Copyright 2020 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/features.gni")
import("//build/util/branding.gni")
import("//chrome/installer/mac/mac_signing_sources.gni")
import("//chrome/process_version_rc_template.gni")
import("//chrome/updater/branding.gni")
import("//chrome/version.gni")

group("signing") {
  public_deps = [ ":copies" ]
}

_packaging_dir = "$root_out_dir/Updater Packaging"

process_version_rc_template("sign_config") {
  visibility = [ ":copy_signing" ]

  process_only = true

  template_file = "//chrome/updater/mac/signing/build_props_config.py.in"

  output = "$_packaging_dir/signing/build_props_config.py"

  _full_target_name = get_label_info(target_name, "label_no_toolchain")
  _file_path = rebase_path(template_file)

  extra_args = [
    "-e",
    "GEN_HEADER=\"THIS FILE IS AUTOMATICALLY GENERATED BY $_full_target_name.\n\"",
    "-e",
    "PRODUCT_FULLNAME=\"" + updater_product_full_name + "\"",
    "-e",
    "MAC_BUNDLE_ID=\"" + mac_updater_bundle_identifier + "\"",
    "-e",
    "KEYSTONE_APP_NAME=\"" + keystone_app_name + "\"",
  ]

  if (is_chrome_branded) {
    extra_args += [
      "-e",
      "IS_CHROME_BRANDED=True",
    ]
  } else {
    extra_args += [
      "-e",
      "IS_CHROME_BRANDED=False",
    ]
  }
}

copy("copy_signing") {
  visibility = [ ":copies" ]

  public_deps = [ ":sign_config" ]

  adjusted_mac_signing_sources = mac_signing_sources - [
                                   "signing/chromium_config.py",
                                   "signing/config_factory.py",
                                   "signing/parts.py",
                                   "signing/pipeline.py",
                                 ]

  sources =
      rebase_path(adjusted_mac_signing_sources, ".", "//chrome/installer/mac/")

  sources += [
    "//chrome/installer/mac/pkg-dmg",
    "config_factory.py",
    "parts.py",
    "pipeline.py",
    "unbranded_config.py",
  ]

  outputs = [ "$_packaging_dir/signing/{{source_file_part}}" ]
}

copy("copies") {
  visibility = [ ":signing" ]

  public_deps = [ ":copy_signing" ]

  sources = [ "//chrome/installer/mac/sign_chrome.py" ]

  outputs = [ "$_packaging_dir/sign_updater.py" ]
}
