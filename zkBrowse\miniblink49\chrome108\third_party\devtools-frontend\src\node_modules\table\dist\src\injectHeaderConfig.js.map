{"version": 3, "file": "injectHeaderConfig.js", "sourceRoot": "", "sources": ["../../src/injectHeaderConfig.ts"], "names": [], "mappings": ";;;AAQO,MAAM,kBAAkB,GAAG,CAAC,IAAW,EAAE,MAAuB,EAAiC,EAAE;;IACxG,IAAI,kBAAkB,GAAG,MAAA,MAAM,CAAC,aAAa,mCAAI,EAAE,CAAC;IACpD,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC;IACnC,MAAM,YAAY,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;IAE/B,IAAI,YAAY,EAAE;QAChB,kBAAkB,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC,EAAC,GAAG,EAAE,GAAG,IAAI,EAAC,EAAE,EAAE;YAC7D,OAAO,EAAC,GAAG,IAAI;gBACb,GAAG,EAAE,GAAG,GAAG,CAAC,EAAC,CAAC;QAClB,CAAC,CAAC,CAAC;QAEH,MAAM,EAAC,OAAO,EAAE,GAAG,YAAY,EAAC,GAAG,YAAY,CAAC;QAEhD,kBAAkB,CAAC,OAAO,CAAC,EAAC,SAAS,EAAE,QAAQ;YAC7C,GAAG,EAAE,CAAC;YACN,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM;YACvB,WAAW,EAAE,CAAC;YACd,YAAY,EAAE,CAAC;YACf,GAAG,EAAE,CAAC;YACN,QAAQ,EAAE,KAAK;YACf,GAAG,YAAY,EAAC,CAAC,CAAC;QAEpB,YAAY,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,IAAI,CAAS,EAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,EAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KAC/F;IAED,OAAO,CAAC,YAAY;QAClB,kBAAkB,CAAC,CAAC;AACxB,CAAC,CAAC;AA3BW,QAAA,kBAAkB,sBA2B7B"}