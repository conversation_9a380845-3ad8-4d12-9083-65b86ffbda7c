<!DOCTYPE html>
<html>
<body>
<script>

if (!window.testRunner)
    document.writeln("This crash test needs to be ran inside DumpRenderTree");

var target;

function startTest() {
    if (!window.testRunner)
        return;

    testRunner.dumpAsText();

    function mouseMoveToCenterOfElement(element) {
        eventSender.mouseMoveTo(element.offsetLeft + element.offsetWidth / 2, element.offsetTop + element.offsetHeight / 2);
    }

    var src = document.getElementById('src');
    mouseMoveToCenterOfElement(src);
    eventSender.mouseDown();
    eventSender.leapForward(200);

    target = document.getElementById('target');
    eventSender.mouseMoveTo(target.offsetLeft + 5, target.offsetTop + 5);
    eventSender.mouseUp();

    document.body.innerHTML = "PASS. DRT didn't crash."
}

function trigger() {
    document.body.removeChild(target);
    target = null;
    if (window.GCController)
        GCController.collect();
}

</script>
<img id="src" src="resources/abe.png" onload="startTest()" draggable="true" ondrag="trigger();">
<textarea id="target" style="width: 500px; height: 500px;">Dropzone</textarea>
</body>
</html>
