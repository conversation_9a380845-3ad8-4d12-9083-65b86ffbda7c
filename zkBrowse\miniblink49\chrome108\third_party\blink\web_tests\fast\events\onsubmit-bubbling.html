<html>
<head>
<script>
function print(message, color) 
{
    var paragraph = document.createElement("div");
    paragraph.appendChild(document.createTextNode(message));
    paragraph.style.fontFamily = "monospace";
    if (color)
        paragraph.style.color = color;
    document.getElementById("console").appendChild(paragraph);
}

function test() 
{
    if (window.testRunner)
        testRunner.dumpAsText();


    document.getElementById('div1').onsubmit = function () { 
        pass(this.id); 
        return false; // returning false cancels form submission
    };
    document.getElementById('input1').click();
}

function pass(id)
{
    print("PASS: containing element " + id + " caught submit event", "green");
}

function fail()
{
    print("FAIL: containing element did not cancel submit event", "red");
}

</script>
</head>
<body onload="test();">
<p>This page tests whether the form 'submit' event bubbles to containing elements.
   If it passes, you'll see 'PASS' messages below.</p>
<hr>
<div id='console'></div>
<div id="div1">
    <div id="div2" onsubmit="pass(this.id);">
        <form id="form1" action="javascript:fail();"> <!-- only executes if form submits -->
            <input id="input1" type="submit">
        </form>
    </div>
</div>
</body>
</html>
