<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

 <head>

  <title>CSS Writing Modes Test: central baseline-alignment of text with 'text-orientation: upright' (vertical-lr)</title>

  <link rel="author" title="Gérard Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" />
  <link rel="help" href="http://www.w3.org/TR/css-writing-modes-3/#text-baselines" title="4.2 Text Baselines" />
  <link rel="match" href="central-baseline-alignment-002-ref.xht" />

  <meta content="This test checks that the central baseline is used as the dominant baseline when 'text-orientation' is 'upright' in vertical writing-mode." name="assert" />
  <meta content="ahem" name="flags" />

  <link rel="stylesheet" type="text/css" href="/fonts/ahem.css" />
  <style type="text/css"><![CDATA[
  div#textorient-mixed
    {
      color: orange;
      font: 60px/1.5 Ahem; /* computes to 60px/90px */
      height: 4em;
      text-orientation: upright;
      writing-mode: vertical-lr;
    }

  span#blue120
    {
      color: blue;
      font-size: 2em; /* computes to 120px */
    }

  span#orange30
    {
      font-size: 0.5em; /* computes to 30px */
    }
  ]]></style>

 </head>

 <body>

  <p>Test passes if 2 orange squares are centered with respect to a blue square.</p>

  <div id="textorient-mixed">A<span id="blue120">B</span><span id="orange30">O</span></div>

 </body>
</html>