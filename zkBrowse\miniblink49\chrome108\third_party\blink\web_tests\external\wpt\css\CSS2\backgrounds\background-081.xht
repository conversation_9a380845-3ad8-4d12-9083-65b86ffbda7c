<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background with (position repeat image)</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="<PERSON><PERSON>" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-03-24 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <link rel="match" href="background-043-ref.xht" />
        <meta name="flags" content="image" />
        <meta name="assert" content="Background with (position repeat image) sets the background to the image specified, repeated across the x-axis, and the image is positioned at the bottom." />
        <style type="text/css">
            div
            {
                background: bottom repeat-x url("support/blue15x15.png");
                height: 200px;
                width: 200px;
                border: 3px solid black;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is a blue stripe inside the hollow black square, positioned at the bottom of the black square.</p>
        <div></div>
    </body>
</html>