<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<defs>
    <clipPath id="clip">
        <rect x="0" y="0" width="100" height="100" clip-path="url(#clip)"/>
    </clipPath>
</defs>
<!--
Spec: clip-path: If the IRI reference is not valid (e.g it points to an object that doesn't exist or the object
                 is not a 'clipPath' element) the 'clip-path' property must be treated as if it hadn't been specified.

     Our view is that an clipPath containing a cycle is not valid, and thus should be ignored.
     But the object that references the clipPath should still be drawn.
-->
<rect x="0" y="0" width="100" height="100" style="fill:red;"/>
<rect x="0" y="0" width="100" height="100" clip-path="url(#clip)" style="fill:green;"/>
</svg>
