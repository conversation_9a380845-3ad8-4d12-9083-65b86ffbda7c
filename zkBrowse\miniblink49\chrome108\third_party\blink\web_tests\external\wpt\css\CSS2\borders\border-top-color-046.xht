<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Border-top-color set to hex with three digits with a blue set to the maximum plus one value, #00g</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="<PERSON><PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-05-26 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#propdef-border-top-color" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#border-color-properties" />
        <link rel="match" href="../reference/ref-filled-black-96px-square.xht" />

        <meta name="flags" content="invalid" />
        <meta name="assert" content="The 'border-top-color' set to #00g falls back to the initial value." />
        <style type="text/css">
            div
            {
                border-top-style: solid;
                border-top-width: 1in;
                border-top-color: #00g;
                height: 0;
                width: 1in;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is a filled black square.</p>
        <div></div>
    </body>
</html>