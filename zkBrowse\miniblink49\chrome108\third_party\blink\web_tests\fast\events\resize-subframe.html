<script>
    function test()
    {
        if (window.testRunner)
        {
            testRunner.dumpAsText();
            testRunner.waitUntilDone();
        }
        document.body.offsetTop;
        document.getElementById("iframe").style.height = "100px";
    }
</script>
<body onload="test()">
    <iframe id="iframe" style="height: 200px;" src="data:text/html,
        <head>
            <script>
                function resized()
                {
                    if (window.testRunner)
                    {
                        console.log('PASS');
                        testRunner.notifyDone();
                    }
                    else
                        document.body.appendChild(document.createTextNode('PASS'));
                }
            </script>
        </head>
        <body onresize='resized()'></body>
    "></iframe>
</body>
