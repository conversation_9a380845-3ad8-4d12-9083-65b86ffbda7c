<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 2.5 Pseudo-elements in Selectors</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
P:first-line {font-weight: bold;}
P.two:first-line {color: green;}
P:first-line.three {color: red;}
</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>P:first-line {font-weight: bold;}
P.two:first-line {color: green;}
P:first-line.three {color: red;}

</PRE>
<HR>
<P>
The first line of this sentence should be boldfaced.  This test is included simply to establish a baseline for the following tests, since if this test fails, then the rest of the tests on this page are expected to fail as well.
</P>
<P class="two">
The first line of this sentence should be boldfaced and green, thanks to its selector.  If this is not the case, then the user agent may have failed to properly parse the selector, or it may simply not support the <TT>:first-line</TT> pseudo-element.
</P>
<P class="three">
The first line of this sentence should be boldfaced.  If it is red, then the user agent has violated the specification in allowing pseudo-elements at a point other than the end of a selector.  If neither is the case, then the user agent has correctly ignored the incorrect selector, but has suppressed other styles which are valid, and therefore must be considered to have failed the test.
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P>
The first line of this sentence should be boldfaced.  This test is included simply to establish a baseline for the following tests, since if this test fails, then the rest of the tests on this page are expected to fail as well.
</P>
<P class="two">
The first line of this sentence should be boldfaced and green, thanks to its selector.  If this is not the case, then the user agent may have failed to properly parse the selector, or it may simply not support the <TT>:first-line</TT> pseudo-element.
</P>
<P class="three">
The first line of this sentence should be boldfaced.  If it is red, then the user agent has violated the specification in allowing pseudo-elements at a point other than the end of a selector.  If neither is the case, then the user agent has correctly ignored the incorrect selector, but has suppressed other styles which are valid, and therefore must be considered to have failed the test.
</P>
</TD></TR></TABLE></BODY>
</HTML>
