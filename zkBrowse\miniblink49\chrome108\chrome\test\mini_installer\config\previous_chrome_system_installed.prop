{"Files": {"$PROGRAM_FILES\\$CHROME_DIR\\Application\\chrome.exe": {"exists": true}, "$PROGRAM_FILES\\$CHROME_DIR\\Application\\chrome.VisualElementsManifest.xml": {"exists": true}, "$PROGRAM_FILES\\$CHROME_DIR\\Application\\chrome_proxy.exe": {"exists": true}, "$PROGRAM_FILES\\$CHROME_DIR\\Application\\$PREVIOUS_VERSION_MINI_INSTALLER_FILE_VERSION\\chrome.dll": {"exists": true}, "$PROGRAM_FILES\\$CHROME_DIR\\Application\\$PREVIOUS_VERSION_MINI_INSTALLER_FILE_VERSION\\chrome_elf.dll": {"exists": true}, "$PROGRAM_FILES\\$CHROME_DIR\\Application\\$PREVIOUS_VERSION_MINI_INSTALLER_FILE_VERSION\\chrome_wer.dll": {"exists": true}, "$PROGRAM_FILES\\$CHROME_DIR\\Application\\$PREVIOUS_VERSION_MINI_INSTALLER_FILE_VERSION\\elevation_service.exe": {"condition": "'$CHROME_SHORT_NAME' != 'Chrome'", "exists": false}, "$PROGRAM_FILES\\$CHROME_DIR\\Application\\$PREVIOUS_VERSION_MINI_INSTALLER_FILE_VERSION\\Installer\\chrome.7z": {"exists": true}, "$PROGRAM_FILES\\$CHROME_DIR\\Application\\$PREVIOUS_VERSION_MINI_INSTALLER_FILE_VERSION\\Installer\\setup.exe": {"exists": true}, "$PROGRAM_FILES\\$CHROME_DIR\\Application\\$PREVIOUS_VERSION_MINI_INSTALLER_FILE_VERSION\\$PREVIOUS_VERSION_MINI_INSTALLER_FILE_VERSION.manifest": {"exists": true}, "$PROGRAM_FILES\\$CHROME_DIR\\Application\\$MINI_INSTALLER_FILE_VERSION": {"exists": false}}, "RegistryEntries": {"HKEY_LOCAL_MACHINE\\$CHROME_CLIENT_STATE_KEY": {"exists": "required", "values": {"CleanInstallRequiredForVersionBelow": {"type": "SZ", "data": "$LAST_INSTALLER_BREAKING_VERSION"}, "DowngradeCleanupCommand": {"type": "SZ", "data": "\"$PROGRAM_FILES\\$CHROME_DIR\\Application\\$PREVIOUS_VERSION_MINI_INSTALLER_FILE_VERSION\\Installer\\setup.exe\" --cleanup-for-downgrade-version=$$1 --cleanup-for-downgrade-operation=$$2 --system-level --verbose-logging"}}, "wow_key": "KEY_WOW64_32KEY"}, "HKEY_LOCAL_MACHINE\\$CHROME_UPDATE_REGISTRY_SUBKEY": {"exists": "required", "values": {"pv": {"type": "SZ", "data": "$PREVIOUS_VERSION_MINI_INSTALLER_FILE_VERSION"}}, "wow_key": "KEY_WOW64_32KEY"}, "HKEY_LOCAL_MACHINE\\$CHROME_UPDATE_REGISTRY_SUBKEY\\Commands\\rotate-dtkey": {"condition": "'$CHROME_SHORT_NAME' == 'Chrome'", "exists": "required", "values": {"CommandLine": {"type": "SZ", "data": "\"$PROGRAM_FILES\\$CHROME_DIR\\Application\\$PREVIOUS_VERSION_MINI_INSTALLER_FILE_VERSION\\Installer\\setup.exe\" --rotate-dtkey=%1 --dm-server-url=%2 --nonce=%3 --system-level --verbose-logging"}, "WebAccessible": {"type": "DWORD", "data": 1}}, "wow_key": "KEY_WOW64_32KEY"}, "HKEY_LOCAL_MACHINE\\$CHROME_UPDATE_REGISTRY_SUBKEY\\Commands\\store-dmtoken": {"condition": "'$CHROME_SHORT_NAME' == 'Chrome'", "exists": "required", "values": {"CommandLine": {"type": "SZ", "data": "\"$PROGRAM_FILES\\$CHROME_DIR\\Application\\$PREVIOUS_VERSION_MINI_INSTALLER_FILE_VERSION\\Installer\\setup.exe\" --store-dmtoken=%1 --system-level --verbose-logging"}, "WebAccessible": {"type": "DWORD", "data": 1}}, "wow_key": "KEY_WOW64_32KEY"}, "HKEY_LOCAL_MACHINE\\$CHROME_UPDATE_REGISTRY_SUBKEY\\Commands\\delete-dmtoken": {"condition": "'$CHROME_SHORT_NAME' == 'Chrome'", "exists": "required", "values": {"CommandLine": {"type": "SZ", "data": "\"$PROGRAM_FILES\\$CHROME_DIR\\Application\\$PREVIOUS_VERSION_MINI_INSTALLER_FILE_VERSION\\Installer\\setup.exe\" --delete-dmtoken --system-level --verbose-logging"}, "WebAccessible": {"type": "DWORD", "data": 1}}, "wow_key": "KEY_WOW64_32KEY"}, "HKEY_LOCAL_MACHINE\\$BINARIES_UPDATE_REGISTRY_SUBKEY": {"exists": "forbidden", "wow_key": "KEY_WOW64_32KEY"}, "HKEY_LOCAL_MACHINE\\$LAUNCHER_UPDATE_REGISTRY_SUBKEY": {"condition": "'$CHROME_SHORT_NAME' == 'Chrome'", "exists": "forbidden", "wow_key": "KEY_WOW64_32KEY"}, "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\$CHROME_LONG_NAME": {"exists": "required", "values": {"UninstallString": {"type": "SZ", "data": "\"$PROGRAM_FILES\\$CHROME_DIR\\Application\\$PREVIOUS_VERSION_MINI_INSTALLER_FILE_VERSION\\Installer\\setup.exe\" --uninstall --system-level --verbose-logging"}, "Version": {"type": "SZ", "data": "$PREVIOUS_VERSION_MINI_INSTALLER_FILE_VERSION"}}, "wow_key": "KEY_WOW64_32KEY"}, "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\Windows Error Reporting\\RuntimeExceptionHelperModules": {"exists": "required", "values": {"$PROGRAM_FILES\\$CHROME_DIR\\Application\\$PREVIOUS_VERSION_MINI_INSTALLER_FILE_VERSION\\chrome_wer.dll": {"type": "DWORD"}}}, "HKEY_LOCAL_MACHINE\\Software\\Classes\\CLSID\\$CHROME_TOAST_ACTIVATOR_CLSID\\LocalServer32": {"exists": "required", "values": {"type": "SZ", "data": "$PROGRAM_FILES\\$CHROME_DIR\\Application\\$PREVIOUS_VERSION_MINI_INSTALLER_FILE_VERSION\\notification_helper.exe"}}, "HKEY_LOCAL_MACHINE\\Software\\Classes\\CLSID\\$CHROME_ELEVATOR_CLSID": {"condition": "'$CHROME_SHORT_NAME' == 'Chrome'", "exists": "required", "values": {"AppID": {"type": "SZ", "data": "$CHROME_ELEVATOR_CLSID"}}}, "HKEY_LOCAL_MACHINE\\Software\\Classes\\AppID\\$CHROME_ELEVATOR_CLSID": {"condition": "'$CHROME_SHORT_NAME' == 'Chrome'", "exists": "required", "values": {"LocalService": {"type": "SZ", "data": "$CHROME_ELEVATION_SERVICE_NAME"}, "ServiceParameters": {"data": "ValueShouldNotExist"}}}, "HKEY_LOCAL_MACHINE\\Software\\Classes\\Interface\\$CHROME_ELEVATOR_IID": {"condition": "'$CHROME_SHORT_NAME' == 'Chrome'", "exists": "required"}, "HKEY_LOCAL_MACHINE\\Software\\Classes\\Interface\\$CHROME_ELEVATOR_IID\\ProxyStubClsid32": {"condition": "'$CHROME_SHORT_NAME' == 'Chrome'", "exists": "required", "values": {"type": "SZ", "data": "{00020424-0000-0000-C000-000000000046}"}}, "HKEY_LOCAL_MACHINE\\Software\\Classes\\Interface\\$CHROME_ELEVATOR_IID\\TypeLib": {"condition": "'$CHROME_SHORT_NAME' == 'Chrome'", "exists": "required", "values": {"type": "SZ", "data": "$CHROME_ELEVATOR_IID"}}, "HKEY_LOCAL_MACHINE\\Software\\Classes\\TypeLib\\$CHROME_ELEVATOR_IID": {"condition": "'$CHROME_SHORT_NAME' == 'Chrome'", "exists": "required"}, "HKEY_LOCAL_MACHINE\\Software\\Classes\\TypeLib\\$CHROME_ELEVATOR_IID\\1.0": {"condition": "'$CHROME_SHORT_NAME' == 'Chrome'", "exists": "required"}, "HKEY_LOCAL_MACHINE\\Software\\Classes\\TypeLib\\$CHROME_ELEVATOR_IID\\1.0\\0": {"condition": "'$CHROME_SHORT_NAME' == 'Chrome'", "exists": "required"}, "HKEY_LOCAL_MACHINE\\Software\\Classes\\TypeLib\\$CHROME_ELEVATOR_IID\\1.0\\0\\win32": {"condition": "'$CHROME_SHORT_NAME' == 'Chrome'", "exists": "required", "values": {"type": "SZ", "data": "\"$PROGRAM_FILES\\$CHROME_DIR\\Application\\$PREVIOUS_VERSION_MINI_INSTALLER_FILE_VERSION\\elevation_service.exe\""}}, "HKEY_LOCAL_MACHINE\\Software\\Classes\\TypeLib\\$CHROME_ELEVATOR_IID\\1.0\\0\\win64": {"condition": "'$CHROME_SHORT_NAME' == 'Chrome'", "exists": "required", "values": {"type": "SZ", "data": "\"$PROGRAM_FILES\\$CHROME_DIR\\Application\\$PREVIOUS_VERSION_MINI_INSTALLER_FILE_VERSION\\elevation_service.exe\""}}, "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\$CHROME_ELEVATION_SERVICE_NAME": {"condition": "'$CHROME_SHORT_NAME' == 'Chrome'", "exists": "required", "values": {"Type": {"type": "DWORD", "data": 16}, "Start": {"type": "DWORD", "data": 3}, "ErrorControl": {"type": "DWORD", "data": 1}, "ImagePath": {"type": "EXPAND_SZ", "data": "\"$PROGRAM_FILES\\$CHROME_DIR\\Application\\$PREVIOUS_VERSION_MINI_INSTALLER_FILE_VERSION\\elevation_service.exe\""}, "DisplayName": {"type": "SZ", "data": "$CHROME_ELEVATION_SERVICE_DISPLAY_NAME"}, "ObjectName": {"type": "SZ", "data": "LocalSystem"}}}, "HKEY_LOCAL_MACHINE\\Software\\Classes\\$CHROME_SHORT_NAME": {"exists": "forbidden"}, "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\EventLog\\Application\\$CHROME_SHORT_NAME": {"exists": "required", "values": {"CategoryCount": {"type": "DWORD", "data": 1}, "TypesSupported": {"type": "DWORD", "data": 7}, "CategoryMessageFile": {"type": "SZ", "data": "$PROGRAM_FILES\\$CHROME_DIR\\Application\\$PREVIOUS_VERSION_MINI_INSTALLER_FILE_VERSION\\eventlog_provider.dll"}, "EventMessageFile": {"type": "SZ", "data": "$PROGRAM_FILES\\$CHROME_DIR\\Application\\$PREVIOUS_VERSION_MINI_INSTALLER_FILE_VERSION\\eventlog_provider.dll"}, "ParameterMessageFile": {"type": "SZ", "data": "$PROGRAM_FILES\\$CHROME_DIR\\Application\\$PREVIOUS_VERSION_MINI_INSTALLER_FILE_VERSION\\eventlog_provider.dll"}}}}}