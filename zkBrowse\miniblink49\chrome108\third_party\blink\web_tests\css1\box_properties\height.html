<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.5.24 height</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
.one {height: 50px;}
.two {height: 100px;}
</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.one {height: 50px;}
.two {height: 100px;}

</PRE>
<HR>
<IMG SRC="../resources/oransqr.gif" class="one" alt="[Image]">
<P>
The square above should be fifty pixels tall.
</P>
<IMG SRC="../resources/oransqr.gif" class="two" alt="[Image]">
<P>
The square above should be 100 pixels tall and wide.
</P>
<IMG SRC="../resources/vblank.gif" class="two" alt="[Image]">
<P>
The rectangular image above should be 100 pixels tall and 30 pixels wide (the original image is 50x15, and the size has been doubled using the <CODE>height</CODE> property).
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><IMG SRC="../resources/oransqr.gif" class="one" alt="[Image]">
<P>
The square above should be fifty pixels tall.
</P>
<IMG SRC="../resources/oransqr.gif" class="two" alt="[Image]">
<P>
The square above should be 100 pixels tall and wide.
</P>
<IMG SRC="../resources/vblank.gif" class="two" alt="[Image]">
<P>
The rectangular image above should be 100 pixels tall and 30 pixels wide (the original image is 50x15, and the size has been doubled using the <CODE>height</CODE> property).
</P>
</TD></TR></TABLE></BODY>
</HTML>
