<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<html>
<body background="../resources/basebg.gif">
<p>The style declarations which apply to the text below are:</p>
<pre>.one {color: green;}
.1 {color: red;}
.a1 {color: green;}
P.two {color: purple;}
</pre>
<hr><p style="color:green">This sentence should be green.</p>
<p>This sentence should be black, not red-- class selectors cannot begin with digits in CSS1.</p>
<p style="color:green">This sentence should be green.</p>
<p style="color:purple">This sentence should be purple.</p>
<pre>This sentence should NOT be purple.</pre>
<ul>
  <li>This sentence should NOT be purple.</li>
</ul>
<table border cellspacing="0" cellpadding="3">
     <tr>
         <td bgcolor="silver">&nbsp;</td>
         <td>
             <p style="color:green">This sentence should be green.</p>
             <p>This sentence should be black, not red-- class selectors cannot begin with digits in CSS1.</p>
             <p style="color:green">This sentence should be green.</p>
             <p style="color:purple">This sentence should be purple.</p>
             <pre>This sentence should NOT be purple.</pre>
             <ul>
                 <li>This sentence should NOT be purple.</li>
             </ul>
         </td>
     </tr>
</table>
</body>
</html>