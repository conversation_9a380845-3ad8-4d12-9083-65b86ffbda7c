<!DOCTYPE html>
<meta charset="utf-8">
<title>Testing Available Space in Orthogonal Flows / max-height / no scroller</title>
<link rel="author" title="Florian Rivoal" href="https://florian.rivoal.net/">
<link rel="help" href="https://www.w3.org/TR/css-writing-modes-3/#orthogonal-auto">
<link rel="match" href="reference/available-size-002-ref.html">
<meta name="assert" content="When an orthogonal flow's ancestor doesn't have a definite block size but does have a fixed max-height, but isn't a scroller, do not use that size.">
<style>
body > div {
  font-family: monospace; /* to be able to reliably measure things in ch*/
  font-size: 20px;
  max-height: 8ch; /* **max**-height does not give the element a definite block size */
  color: transparent;
  position: relative; /* to act as a container of #green */
}

div > div > div { writing-mode: vertical-rl; }

span {
  background: white;
  display: inline-block; /* This should not change it's size or position, but makes the size of the background predictable*/
}

#green {
  position: absolute;
  background: green;
  left: 0;
  writing-mode: vertical-rl;
  z-index: -1;
}
</style>

<p>Test passes if there is a <strong>green rectangle</strong> below and <strong>no red</strong>.

<div>
  <div>
    <aside id="green">0</aside>
    <div>0 0 0 0 <span>0</span> 0 0 0</div> <!-- If this div takes its height from
    the max-height of its parent (which it shouldn't, since it's not a scroller),
    it should wrap just right for the white 0 to
    overlap with the green one. -->
  </div>
</div>
