; Copyright 2017 The Chromium Authors
; Use of this source code is governed by a BSD-style license that can be
; found in the LICENSE file.

; *** The contents of common.sb are implicitly included here. ***

; Allow preloading of the CDM using seatbelt extension.
(allow file-read* (extension "com.apple.app-sandbox.read"))

; mach IPC
(allow mach-lookup (global-name "com.apple.windowserver.active"))

; This is available in 10.15+, and rolled out as a Finch experiment.
(if (param-true? filter-syscalls-debug)
  (when (defined? 'syscall-unix)
    (deny syscall-unix (with send-signal SIGSYS))))
