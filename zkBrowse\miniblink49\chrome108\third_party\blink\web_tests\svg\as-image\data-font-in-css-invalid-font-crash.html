<!DOCTYPE html>
<script src="../../resources/testharness.js"></script>
<script src="../../resources/testharnessreport.js"></script>
<script>
var t1 = async_test("Loading of SVG with a invalid font completes " +
                    "before <img> load event and drawImage() doesn't crash.");

var imgOnLoadCalled = false;

var imgOnLoad = t1.step_func(function() {
    imgOnLoadCalled = true;

    var svg = document.getElementById('img');
    var canvas = document.createElement("canvas");
    var ctx = canvas.getContext("2d");
    ctx.drawImage(svg, 0, 0);
});

var bodyOnLoad = t1.step_func_done(function() {
    assert_true(imgOnLoadCalled,
        "<img> load event must be fired before <body> load event");
});
</script>
<body onload="bodyOnLoad()">
<img src="resources/data-font-in-css-invalid-font.svg" id="img" onload="imgOnLoad()">
</body>
