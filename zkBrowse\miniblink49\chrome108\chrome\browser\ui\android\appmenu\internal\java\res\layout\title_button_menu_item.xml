<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright 2014 The Chromium Authors
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file.
-->

<!-- Represents a menu item that can display any of the following options:
     * [text label]
     * [text label] [checkbox indicating that it is enabled or disabled]
     * [text label] [icon]
     * [icon] [text label]
     * [icon] [text label] [checkbox indicating that it is enabled or disabled]
     * [icon] [text label] [icon]
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              xmlns:app="http://schemas.android.com/apk/res-auto"
              android:layout_width="match_parent"
              android:layout_height="?android:attr/listPreferredItemHeightSmall"
              android:gravity="center_vertical"
              android:orientation="horizontal">

     <org.chromium.components.browser_ui.widget.text.TextViewWithCompoundDrawables
        android:id="@+id/title"
        style="@style/AppMenuItemTextViewWithCompoundDrawables"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:paddingStart="16dp"
        android:background="?attr/listChoiceBackgroundIndicator"/>

    <!-- Checkbox.  Paddings account for built-in padding from the Android resource. -->
    <org.chromium.chrome.browser.ui.appmenu.AppMenuItemIcon
        android:id="@+id/checkbox"
        android:layout_width="56dp"
        android:layout_height="match_parent"
        android:background="?attr/listChoiceBackgroundIndicator"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        android:scaleType="fitCenter"
        android:src="?android:attr/listChoiceIndicatorMultiple" />

    <!-- Displays an icon. -->
    <!-- TODO(twellington): Consider setting a default tint that adjusts when the item is disabled.
         currently there are no menu items with icons that are visible but disabled so this behavior
         is currently unused. -->
    <org.chromium.ui.widget.ChromeImageButton
        android:id="@+id/button"
        android:layout_width="56dp"
        android:layout_height="match_parent"
        android:background="?attr/listChoiceBackgroundIndicator"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:scaleType="fitCenter" />
</LinearLayout>
