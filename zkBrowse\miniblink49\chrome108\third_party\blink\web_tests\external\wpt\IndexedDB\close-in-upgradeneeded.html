<!doctype html>
<!-- Submitted from TestTWF Paris -->
<title>When db.close is called in upgradeneeded, the db is cleaned up on refresh</title>
<script src=/resources/testharness.js></script>
<script src=/resources/testharnessreport.js></script>
<script src=resources/support.js></script>

<script>

var db
var open_rq = createdb(async_test())
var sawTransactionComplete = false

open_rq.onupgradeneeded = function(e) {
    db = e.target.result
    assert_equals(db.version, 1)

    db.createObjectStore('os')
    db.close()

    e.target.transaction.oncomplete = function() { sawTransactionComplete = true }
}

open_rq.onerror = function(e) {
    assert_true(sawTransactionComplete, "saw transaction.complete")

    assert_equals(e.target.error.name, 'AbortError')
    assert_equals(e.result, undefined)

    assert_true(!!db)
    assert_equals(db.version, 1)
    assert_equals(db.objectStoreNames.length, 1)
    assert_throws_dom("InvalidStateError", function() { db.transaction('os') })

    this.done()
}

</script>

<div id=log></div>
