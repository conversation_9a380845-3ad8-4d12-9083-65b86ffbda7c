CONSOLE ERROR: Uncaught Error: Error in isolated world inline script.
CONSOLE ERROR: Uncaught Error: Error in isolated world load handler.
CONSOLE ERROR: Uncaught Error: Error in isolated world setTimeout callback.
Test that window.onerror and "error" event listeners from main world are not invoked for uncaught exceptions in scripts running in isolate worlds, but only for exceptions in the main world.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".

window.onerror: "Uncaught Error: Error in main world inline script." at window-onerror-isolatedworld-01.html (Line: 49, Column: 13)
Stack Trace:
Error: Error in main world inline script.
    at exceptions window-onerror-isolatedworld-01.html:49:19
    at window-onerror-isolatedworld-01.html:55:9

Returning 'true': the error should not be reported in the console as an unhandled exception.



Handling 'error' event (phase 2): "Uncaught Error: Error in main world inline script." at window-onerror-isolatedworld-01.html:49
Stack Trace:
Error: Error in main world inline script.
    at exceptions window-onerror-isolatedworld-01.html:49:19
    at window-onerror-isolatedworld-01.html:55:9

PASS eventPassedToTheErrorListener is window.event
PASS eventCurrentTarget is window
Calling e.preventDefault(): the error should not be reported in the console as an unhandled exception.



window.onerror: "Uncaught Error: Error in main world load handler." at window-onerror-isolatedworld-01.html (Line: 46, Column: 17)
Stack Trace:
Error: Error in main world load handler.
    at window-onerror-isolatedworld-01.html:46:23

Returning 'true': the error should not be reported in the console as an unhandled exception.



Handling 'error' event (phase 2): "Uncaught Error: Error in main world load handler." at window-onerror-isolatedworld-01.html:46
Stack Trace:
Error: Error in main world load handler.
    at window-onerror-isolatedworld-01.html:46:23

PASS eventPassedToTheErrorListener is window.event
PASS eventCurrentTarget is window
Calling e.preventDefault(): the error should not be reported in the console as an unhandled exception.



window.onerror: "Uncaught Error: Error in main world setTimeout callback." at window-onerror-isolatedworld-01.html (Line: 44, Column: 21)
Stack Trace:
Error: Error in main world setTimeout callback.
    at window-onerror-isolatedworld-01.html:44:27

Returning 'true': the error should not be reported in the console as an unhandled exception.



Handling 'error' event (phase 2): "Uncaught Error: Error in main world setTimeout callback." at window-onerror-isolatedworld-01.html:44
Stack Trace:
Error: Error in main world setTimeout callback.
    at window-onerror-isolatedworld-01.html:44:27

PASS eventPassedToTheErrorListener is window.event
PASS eventCurrentTarget is window
Calling e.preventDefault(): the error should not be reported in the console as an unhandled exception.



PASS successfullyParsed is true

TEST COMPLETE

