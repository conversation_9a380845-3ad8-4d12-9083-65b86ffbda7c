<!DOCTYPE html>
<html>
<head>
<script>

if (window.testRunner)
    testRunner.dumpAsText();

window.onload = function()
{
    if (!window.testRunner)
        return;
    
    for (var i = 0; i < 4; i++) {
        eventSender.keyDown('\t');
    }
};

function print(s)
{
    var el = document.querySelector('pre');
    el.textContent += s + '\n';
}

var nextToFocus = 0;

document.addEventListener('focus', function(e) {
    var el = e.target;
    print((el.getAttribute('data-tab') == nextToFocus ? 'PASS' : 'FAIL') +
          ' - ' + el.textContent);
    nextToFocus++;
}, true);

</script>
</head>
<body>

<p>Layout test for <a href="https://bugs.webkit.org/show_bug.cgi?id=27099" tabindex=-1>https://bugs.webkit.org/show_bug.cgi?id=27099</a>

<div tabindex=0 data-tab=0>Div 0</div>
<div tabindex=0 style="display:none">Div 1</div>
<div tabindex=0 data-tab=1>Div 2</div>
<div tabindex=0 data-tab=2 style="overflow:hidden;height:0">Div 3</div>
<div tabindex=0 style="visibility:hidden">Div 4</div>
<div tabindex=0 data-tab=3>Div 5</div>

<p>Result

<pre id=out></pre>

</body>
</html>
