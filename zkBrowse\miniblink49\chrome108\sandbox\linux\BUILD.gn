# Copyright 2014 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/features.gni")
import("//build/config/nacl/config.gni")
import("//sandbox/features.gni")
import("//testing/test.gni")

if (is_android) {
  import("//build/config/android/rules.gni")
}

declare_args() {
  compile_suid_client = is_linux || is_chromeos

  compile_credentials = is_linux || is_chromeos

  # On Android, use plain GTest.
  use_base_test_suite = is_linux || is_chromeos
}

# We have two principal targets: sandbox and sandbox_linux_unittests
# All other targets are listed as dependencies.
# There is one notable exception: for historical reasons, chrome_sandbox is
# the setuid sandbox and is its own target.

group("sandbox") {
  public_deps = [ ":sandbox_services" ]
  if (compile_suid_client) {
    public_deps += [ ":suid_sandbox_client" ]
  }
  if (use_seccomp_bpf) {
    public_deps += [ ":seccomp_bpf" ]
  }
  if (is_android) {
    public_deps += [ ":seccomp_starter_android" ]
  }
}

source_set("sandbox_linux_test_utils") {
  testonly = true
  sources = [
    "tests/sandbox_test_runner.cc",
    "tests/sandbox_test_runner.h",
    "tests/sandbox_test_runner_function_pointer.cc",
    "tests/sandbox_test_runner_function_pointer.h",
    "tests/test_utils.cc",
    "tests/test_utils.h",
    "tests/unit_tests.cc",
    "tests/unit_tests.h",
  ]

  deps = [
    "//base",
    "//testing/gtest",
  ]

  if (use_seccomp_bpf) {
    sources += [
      "seccomp-bpf/bpf_tester_compatibility_delegate.h",
      "seccomp-bpf/bpf_tests.h",
      "seccomp-bpf/sandbox_bpf_test_runner.cc",
      "seccomp-bpf/sandbox_bpf_test_runner.h",
    ]
    deps += [ ":seccomp_bpf" ]
  }

  if (use_base_test_suite) {
    deps += [ "//base/test:test_support" ]
    defines = [ "SANDBOX_USES_BASE_TEST_SUITE" ]
  }
}

# Sources for sandbox_linux_unittests.
source_set("sandbox_linux_unittests_sources") {
  testonly = true

  sources = [
    "services/proc_util_unittest.cc",
    "services/resource_limits_unittest.cc",
    "services/scoped_process_unittest.cc",
    "services/syscall_wrappers_unittest.cc",
    "services/thread_helpers_unittest.cc",
    "services/yama_unittest.cc",
    "syscall_broker/broker_file_permission_unittest.cc",
    "syscall_broker/broker_process_unittest.cc",
    "syscall_broker/broker_simple_message_unittest.cc",
    "syscall_broker/remote_syscall_arg_handler_unittest.cc",
    "tests/main.cc",
    "tests/scoped_temporary_file.cc",
    "tests/scoped_temporary_file.h",
    "tests/scoped_temporary_file_unittest.cc",
    "tests/test_utils_unittest.cc",
    "tests/unit_tests_unittest.cc",
  ]

  deps = [
    ":sandbox",
    ":sandbox_linux_test_utils",
    "//base",
    "//base/test:test_support",
    "//base/third_party/dynamic_annotations",
    "//build:chromeos_buildflags",
    "//testing/gtest",
  ]

  if (use_base_test_suite) {
    deps += [ "//base/test:test_support" ]
    defines = [ "SANDBOX_USES_BASE_TEST_SUITE" ]
  }

  if (compile_suid_client) {
    sources += [
      "suid/client/setuid_sandbox_client_unittest.cc",
      "suid/client/setuid_sandbox_host_unittest.cc",
    ]
  }
  if (use_seccomp_bpf) {
    sources += [
      "bpf_dsl/bpf_dsl_unittest.cc",
      "bpf_dsl/codegen_unittest.cc",
      "bpf_dsl/cons_unittest.cc",
      "bpf_dsl/dump_bpf.cc",
      "bpf_dsl/dump_bpf.h",
      "bpf_dsl/syscall_set_unittest.cc",
      "bpf_dsl/test_trap_registry.cc",
      "bpf_dsl/test_trap_registry.h",
      "bpf_dsl/test_trap_registry_unittest.cc",
      "bpf_dsl/verifier.cc",
      "bpf_dsl/verifier.h",
      "integration_tests/bpf_dsl_seccomp_unittest.cc",
      "integration_tests/seccomp_broker_process_unittest.cc",
      "seccomp-bpf-helpers/baseline_policy_unittest.cc",
      "seccomp-bpf-helpers/syscall_parameters_restrictions_unittests.cc",
      "seccomp-bpf/bpf_tests_unittest.cc",
      "seccomp-bpf/sandbox_bpf_unittest.cc",
      "seccomp-bpf/syscall_unittest.cc",
      "seccomp-bpf/trap_unittest.cc",
    ]
    deps += [ ":bpf_dsl_golden" ]

    if (is_android) {
      sources += [ "seccomp-bpf-helpers/baseline_policy_android_unittest.cc" ]
    }
  }
  if (compile_credentials) {
    sources += [
      "integration_tests/namespace_unix_domain_socket_unittest.cc",
      "services/credentials_unittest.cc",
      "services/namespace_utils_unittest.cc",
    ]

    if (use_base_test_suite) {
      # Tests that use advanced features not available in stock GTest.
      sources += [ "services/namespace_sandbox_unittest.cc" ]
    }

    # For credentials_unittest.cc
    configs += [ "//build/config/linux:libcap" ]
  }
}

action("bpf_dsl_golden") {
  script = "bpf_dsl/golden/generate.py"
  inputs = [
    "bpf_dsl/golden/i386/ArgSizePolicy.txt",
    "bpf_dsl/golden/i386/BasicPolicy.txt",
    "bpf_dsl/golden/i386/ElseIfPolicy.txt",
    "bpf_dsl/golden/i386/MaskingPolicy.txt",
    "bpf_dsl/golden/i386/MoreBooleanLogicPolicy.txt",
    "bpf_dsl/golden/i386/NegativeConstantsPolicy.txt",
    "bpf_dsl/golden/i386/SwitchPolicy.txt",
    "bpf_dsl/golden/x86-64/ArgSizePolicy.txt",
    "bpf_dsl/golden/x86-64/BasicPolicy.txt",
    "bpf_dsl/golden/x86-64/BooleanLogicPolicy.txt",
    "bpf_dsl/golden/x86-64/ElseIfPolicy.txt",
    "bpf_dsl/golden/x86-64/MaskingPolicy.txt",
    "bpf_dsl/golden/x86-64/MoreBooleanLogicPolicy.txt",
    "bpf_dsl/golden/x86-64/NegativeConstantsPolicy.txt",
    "bpf_dsl/golden/x86-64/SwitchPolicy.txt",
  ]
  outputs = [ "$target_gen_dir/bpf_dsl/golden/golden_files.h" ]
  args =
      rebase_path(outputs, root_build_dir) + rebase_path(inputs, root_build_dir)
}

test("sandbox_linux_unittests") {
  deps = [
    ":sandbox_linux_unittests_sources",
    "//sandbox/policy:tests",
  ]
  data_deps = [ "//testing/buildbot/filters:sandbox_linux_unittests_filters" ]
  if (is_android) {
    use_raw_android_executable = true
  }
}

component("seccomp_bpf") {
  sources = [
    "bpf_dsl/bpf_dsl.cc",
    "bpf_dsl/bpf_dsl.h",
    "bpf_dsl/bpf_dsl_forward.h",
    "bpf_dsl/bpf_dsl_impl.h",
    "bpf_dsl/codegen.cc",
    "bpf_dsl/codegen.h",
    "bpf_dsl/cons.h",
    "bpf_dsl/errorcode.h",
    "bpf_dsl/linux_syscall_ranges.h",
    "bpf_dsl/policy.cc",
    "bpf_dsl/policy.h",
    "bpf_dsl/policy_compiler.cc",
    "bpf_dsl/policy_compiler.h",
    "bpf_dsl/seccomp_macros.h",
    "bpf_dsl/syscall_set.cc",
    "bpf_dsl/syscall_set.h",
    "bpf_dsl/trap_registry.h",
    "seccomp-bpf-helpers/baseline_policy.cc",
    "seccomp-bpf-helpers/baseline_policy.h",
    "seccomp-bpf-helpers/sigsys_handlers.cc",
    "seccomp-bpf-helpers/sigsys_handlers.h",
    "seccomp-bpf-helpers/syscall_parameters_restrictions.cc",
    "seccomp-bpf-helpers/syscall_parameters_restrictions.h",
    "seccomp-bpf-helpers/syscall_sets.cc",
    "seccomp-bpf-helpers/syscall_sets.h",
    "seccomp-bpf/die.cc",
    "seccomp-bpf/die.h",
    "seccomp-bpf/sandbox_bpf.cc",
    "seccomp-bpf/sandbox_bpf.h",
    "seccomp-bpf/syscall.cc",
    "seccomp-bpf/syscall.h",
    "seccomp-bpf/trap.cc",
    "seccomp-bpf/trap.h",
  ]
  defines = [ "SANDBOX_IMPLEMENTATION" ]

  public_deps = [
    ":sandbox_services_headers",
    "//sandbox:sandbox_export",
  ]
  deps = [
    ":sandbox_services",
    "//base",
    "//base/third_party/dynamic_annotations",
    "//build:chromeos_buildflags",
    "//sandbox:sandbox_buildflags",
  ]

  if (is_android) {
    sources += [
      "seccomp-bpf-helpers/baseline_policy_android.cc",
      "seccomp-bpf-helpers/baseline_policy_android.h",
    ]
  }
}

if (is_android) {
  # This target is available even if use_seccomp_bpf is disabled, but it also
  # works when it is enabled.
  component("seccomp_starter_android") {
    sources = [
      "seccomp-bpf-helpers/seccomp_starter_android.cc",
      "seccomp-bpf-helpers/seccomp_starter_android.h",
    ]

    defines = [ "SANDBOX_IMPLEMENTATION" ]

    deps = [
      "//base",
      "//sandbox:sandbox_buildflags",
    ]

    if (use_seccomp_bpf) {
      deps += [ ":seccomp_bpf" ]
    }

    visibility = [ ":*" ]
  }
}

if (is_linux || is_chromeos) {
  # The setuid sandbox for Linux.
  executable("chrome_sandbox") {
    sources = [
      "suid/common/sandbox.h",
      "suid/common/suid_unsafe_environment_variables.h",
      "suid/process_util.h",
      "suid/process_util_linux.c",
      "suid/sandbox.c",
    ]

    cflags = [
      # For ULLONG_MAX
      "-std=gnu99",

      # These files have a suspicious comparison.
      # TODO fix this and re-enable this warning.
      "-Wno-sign-compare",
    ]
  }
}

component("sandbox_services") {
  sources = [
    "services/init_process_reaper.cc",
    "services/init_process_reaper.h",
    "services/proc_util.cc",
    "services/proc_util.h",
    "services/resource_limits.cc",
    "services/resource_limits.h",
    "services/scoped_process.cc",
    "services/scoped_process.h",
    "services/syscall_wrappers.cc",
    "services/syscall_wrappers.h",
    "services/thread_helpers.cc",
    "services/thread_helpers.h",
    "services/yama.cc",
    "services/yama.h",
    "syscall_broker/broker_channel.cc",
    "syscall_broker/broker_channel.h",
    "syscall_broker/broker_client.cc",
    "syscall_broker/broker_client.h",
    "syscall_broker/broker_command.cc",
    "syscall_broker/broker_command.h",
    "syscall_broker/broker_file_permission.cc",
    "syscall_broker/broker_file_permission.h",
    "syscall_broker/broker_host.cc",
    "syscall_broker/broker_host.h",
    "syscall_broker/broker_permission_list.cc",
    "syscall_broker/broker_permission_list.h",
    "syscall_broker/broker_process.cc",
    "syscall_broker/broker_process.h",
    "syscall_broker/broker_sandbox_config.cc",
    "syscall_broker/broker_sandbox_config.h",
    "syscall_broker/broker_simple_message.cc",
    "syscall_broker/broker_simple_message.h",
    "syscall_broker/remote_syscall_arg_handler.cc",
    "syscall_broker/remote_syscall_arg_handler.h",
    "syscall_broker/syscall_dispatcher.cc",
    "syscall_broker/syscall_dispatcher.h",
  ]

  defines = [ "SANDBOX_IMPLEMENTATION" ]

  public_deps = [ "//sandbox:sandbox_export" ]
  deps = [
    "//base",
    "//base/third_party/dynamic_annotations",
  ]

  if (compile_credentials) {
    sources += [
      "services/credentials.cc",
      "services/credentials.h",
      "services/namespace_sandbox.cc",
      "services/namespace_sandbox.h",
      "services/namespace_utils.cc",
      "services/namespace_utils.h",
    ]

    public_deps += [ ":sandbox_services_headers" ]
  } else {
    deps += [ ":sandbox_services_headers" ]
  }

  if (!is_android) {
    sources += [
      "services/libc_interceptor.cc",
      "services/libc_interceptor.h",
    ]
  }
}

source_set("sandbox_services_headers") {
  sources = [
    "system_headers/arm64_linux_syscalls.h",
    "system_headers/arm_linux_syscalls.h",
    "system_headers/arm_linux_ucontext.h",
    "system_headers/i386_linux_ucontext.h",
    "system_headers/linux_filter.h",
    "system_headers/linux_futex.h",
    "system_headers/linux_prctl.h",
    "system_headers/linux_ptrace.h",
    "system_headers/linux_seccomp.h",
    "system_headers/linux_signal.h",
    "system_headers/linux_stat.h",
    "system_headers/linux_syscalls.h",
    "system_headers/linux_time.h",
    "system_headers/mips64_linux_syscalls.h",
    "system_headers/mips_linux_syscalls.h",
    "system_headers/x86_32_linux_syscalls.h",
    "system_headers/x86_64_linux_syscalls.h",
  ]
}

if (compile_suid_client) {
  component("suid_sandbox_client") {
    sources = [
      "suid/client/setuid_sandbox_client.cc",
      "suid/client/setuid_sandbox_client.h",
      "suid/client/setuid_sandbox_host.cc",
      "suid/client/setuid_sandbox_host.h",
      "suid/common/sandbox.h",
      "suid/common/suid_unsafe_environment_variables.h",
    ]
    defines = [ "SANDBOX_IMPLEMENTATION" ]
    public_deps = [ "//sandbox:sandbox_export" ]
    deps = [
      ":sandbox_services",
      "//base",
      "//base/third_party/dynamic_annotations",
    ]
  }
}
