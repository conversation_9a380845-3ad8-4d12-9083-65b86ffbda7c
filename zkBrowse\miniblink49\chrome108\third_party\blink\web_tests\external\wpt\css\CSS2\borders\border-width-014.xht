<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

 <head>

  <title>CSS Test: Border-width: inherit - border-style inherit</title>

  <link rel="author" title="<PERSON><PERSON>" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" />
  <link rel="help" title="Section 8.5.1 Border width" href="http://www.w3.org/TR/CSS21/box.html#border-width-properties" />
  <link rel="match" href="border-width-014-ref.xht" />


  <style type="text/css"><![CDATA[
  #grand-parent
  {
  border-color: transparent;
  border-style: solid;
  border-width: 1em;
  }

  #parent
  {
  border-color: transparent;
  border-style: inherit;
  border-width: inherit;
  }

  #child
  {
  border-color: green;
  border-style: inherit;
  border-width: inherit;
  }
  ]]></style>

 </head>

 <body>

  <p>Test passes if there is a thick green bar across the page.</p>

  <div id="grand-parent">
    <div id="parent">
      <div id="child"></div>
    </div>
  </div>

 </body>
</html>