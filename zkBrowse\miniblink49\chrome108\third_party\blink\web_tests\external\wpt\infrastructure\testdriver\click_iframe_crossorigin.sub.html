<!DOCTYPE html>
<meta charset="utf-8">
<title>TestDriver click on a document in an iframe</title>
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<script src="/resources/testdriver.js"></script>
<script src="/resources/testdriver-vendor.js"></script>

<script>
setup({single_test: true});
addEventListener("message", (msg) => {
    if (msg.data === "PASS") {
        done();
    } else if (msg.data === "FAIL") {
        assert_unreached("click failed")
    }
});
</script>

<!-- Make sure we add the event listener before loading the iframe, to avoid
potentially missing messages from the child. -->
<iframe src="https://{{host}}:{{ports[https][1]}}/infrastructure/testdriver/click_child_testdriver.html"></iframe>

