<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>CSS Test: Image backgrounds on &lt;body&gt; and &lt;html&gt; - image vs. color</title>
  <link rel="author" title="<PERSON>" href="mailto:<EMAIL>"/>
  <link rel="alternate" href="http://www.hixie.ch/tests/adhoc/css/background/03.html" type="text/html"/>
  <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background"/>
  <link rel="match" href="background-root-006-ref.xht" />

  <meta name="flags" content="image"/>

<style type="text/css"><![CDATA[
   body { border: solid lime; background: red url(support/square-white.png); color: green; }
   p { font: 900 1.75em <PERSON>ana, sans-serif; }
   html { border: solid blue; background: navy; color: yellow; }
   :link, :visited { color: inherit; background: transparent; }
   * { margin: 1em; padding: 1em; }
]]></style>

</head>
<body>
<p>This box, which should have a bright green border, should have a
white tile background. The box surrounding it should have a navy
background. The rest of the page should also be navy.</p>


</body>
</html>
