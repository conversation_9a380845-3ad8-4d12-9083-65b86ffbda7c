<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>inert nodes are unselectable</title>
    <link rel="author" title="Alice Boxhall" href="<EMAIL>">
    <script src="/resources/testharness.js"></script>
    <script src="/resources/testharnessreport.js"></script>
  </head>
<body>
  <div inert>Here is a text node you can't select.</div>
  <div>I'm selectable.</div>
<script>
test(function() {
    document.execCommand('SelectAll');
    assert_equals(window.getSelection().toString().trim(), "I'm selectable.");
}, "Inert nodes cannot be selected.");
</script>
</body>
</html>
