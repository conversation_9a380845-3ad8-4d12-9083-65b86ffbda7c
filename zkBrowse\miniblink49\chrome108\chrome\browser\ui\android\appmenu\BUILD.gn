# Copyright 2019 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/android/rules.gni")
import("//chrome/android/features/android_library_factory_tmpl.gni")

android_library("java") {
  sources = [
    "java/src/org/chromium/chrome/browser/ui/appmenu/AppMenuBlocker.java",
    "java/src/org/chromium/chrome/browser/ui/appmenu/AppMenuButtonHelper.java",
    "java/src/org/chromium/chrome/browser/ui/appmenu/AppMenuClickHandler.java",
    "java/src/org/chromium/chrome/browser/ui/appmenu/AppMenuCoordinator.java",
    "java/src/org/chromium/chrome/browser/ui/appmenu/AppMenuDelegate.java",
    "java/src/org/chromium/chrome/browser/ui/appmenu/AppMenuHandler.java",
    "java/src/org/chromium/chrome/browser/ui/appmenu/AppMenuItemProperties.java",
    "java/src/org/chromium/chrome/browser/ui/appmenu/AppMenuObserver.java",
    "java/src/org/chromium/chrome/browser/ui/appmenu/AppMenuPropertiesDelegate.java",
    "java/src/org/chromium/chrome/browser/ui/appmenu/AppMenuUtil.java",
    "java/src/org/chromium/chrome/browser/ui/appmenu/CustomViewBinder.java",
    "java/src/org/chromium/chrome/browser/ui/appmenu/MenuButtonDelegate.java",
  ]
  deps = [
    ":java_resources",
    "//chrome/browser/android/lifecycle:java",
    "//third_party/androidx:androidx_annotation_annotation_java",
    "//ui/android:ui_java",
  ]
  resources_package = "org.chromium.chrome.browser.ui.appmenu"
}

android_library_factory("factory_java") {
  # These deps will be inherited by the resulting android_library target.
  deps = [
    ":java",
    "//base:base_java",
    "//chrome/browser/android/lifecycle:java",
  ]

  # This internal file will be replaced by a generated file so the resulting
  # android_library target does not actually depend on this internal file.
  sources = [ "//chrome/browser/ui/android/appmenu/internal/java/src/org/chromium/chrome/browser/ui/appmenu/AppMenuCoordinatorFactory.java" ]
}

android_resources("java_resources") {
  sources = [
    "java/res/values/ids.xml",
    "java/res/values/styles.xml",
  ]
  deps = [
    "//components/browser_ui/styles/android:java_resources",
    "//ui/android:ui_java_resources",
  ]
}
