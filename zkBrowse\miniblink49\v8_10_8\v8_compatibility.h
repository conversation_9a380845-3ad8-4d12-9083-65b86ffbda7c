#ifndef V8_COMPATIBILITY_H_
#define V8_COMPATIBILITY_H_

#include "v8.h"
#include "v8-traced-handle.h"
#include "v8-util.h"

// V8 10.8 compatibility layer for miniblink
// This file provides compatibility shims for APIs that changed between V8 7.5 and V8 10.8

// V8CALL macro compatibility
#ifndef V8CALL
#define V8CALL
#endif

// Only define compatibility layer for V8 10.8+
#if V8_MAJOR_VERSION >= 10

namespace v8 {

// Compatibility alias for TracedGlobal -> TracedReference
template<typename T>
using TracedGlobal = TracedReference<T>;

// Compatibility class for UniqueId (removed in V8 10.8)
class UniqueId {
public:
    UniqueId() : id_(next_id_++) {}

    // Constructor from intptr_t for compatibility
    explicit UniqueId(intptr_t value) : id_(static_cast<uint32_t>(value)) {}

    bool operator==(const UniqueId& other) const {
        return id_ == other.id_;
    }

    bool operator!=(const UniqueId& other) const {
        return !(*this == other);
    }

private:
    static uint32_t next_id_;
    uint32_t id_;
};

// Compatibility extensions for Isolate
class IsolateCompat {
public:
    // SetObjectGroupId was removed - provide empty implementation
    static void SetObjectGroupId(Isolate* isolate, const Persistent<Value>& object, UniqueId id) {
        // No-op in V8 10.8 - object grouping is handled differently
    }

    // Overload for different persistent types
    template<typename T>
    static void SetObjectGroupId(Isolate* isolate, const Persistent<T>& object, UniqueId id) {
        // No-op in V8 10.8 - object grouping is handled differently
    }

    // RunMicrotasks was removed - provide empty implementation
    static void RunMicrotasks(Isolate* isolate) {
        // No-op in V8 10.8 - microtasks are handled differently
        // Use isolate->PerformMicrotaskCheckpoint() if needed
    }

    // SetReference was removed - provide empty implementation
    template<typename T, typename U>
    static void SetReference(Isolate* isolate, const Persistent<T>& parent, const Persistent<U>& child) {
        // No-op in V8 10.8 - reference tracking is handled differently
    }

    // Overload for same types
    template<typename T>
    static void SetReference(Isolate* isolate, const Persistent<T>& parent, const Persistent<T>& child) {
        // No-op in V8 10.8 - reference tracking is handled differently
    }

    // SetReferenceFromGroup was removed - provide empty implementation
    template<typename T>
    static void SetReferenceFromGroup(Isolate* isolate, const Persistent<T>& parent, const Persistent<T>& child) {
        // No-op in V8 10.8 - reference tracking is handled differently
    }
};

// Compatibility extensions for PersistentBase
class PersistentCompat {
public:
    // MarkIndependent was removed - provide empty implementation
    template<typename T>
    static void MarkIndependent(const Persistent<T>& persistent) {
        // No-op in V8 10.8 - independence marking is handled differently
    }

    // Overload for PersistentBase
    template<typename T>
    static void MarkIndependent(const PersistentBase<T>& persistent) {
        // No-op in V8 10.8 - independence marking is handled differently
    }
};

// Compatibility extensions for Object
class ObjectCompat {
public:
    // CreationContext was removed, replaced with GetCreationContext
    static Local<Context> CreationContext(Local<Object> object) {
        return object->GetCreationContext().ToLocalChecked();
    }

    // Object::Set signature changed in V8 10.8
    static Maybe<bool> Set(Local<Object> object, Local<Value> key, Local<Value> value, Isolate* isolate) {
        Local<Context> context = isolate->GetCurrentContext();
        return object->Set(context, key, value);
    }

    static void Set(Local<Object> object, Local<Value> key, Local<Value> value) {
        // For compatibility - use current isolate and context
        Isolate* isolate = Isolate::GetCurrent();
        Local<Context> context = isolate->GetCurrentContext();
        object->Set(context, key, value).Check();
    }

    // Object::Get signature changed in V8 10.8
    static MaybeLocal<Value> Get(Local<Object> object, Local<Value> key, Isolate* isolate) {
        Local<Context> context = isolate->GetCurrentContext();
        return object->Get(context, key);
    }

    static Local<Value> Get(Local<Object> object, Local<Value> key) {
        // For compatibility - use current isolate and context
        Isolate* isolate = Isolate::GetCurrent();
        Local<Context> context = isolate->GetCurrentContext();
        return object->Get(context, key).ToLocalChecked();
    }
};

// Compatibility extensions for Value
class ValueCompat {
public:
    // ToString signature changed in V8 10.8
    static MaybeLocal<String> ToString(Local<Value> value, Isolate* isolate) {
        Local<Context> context = isolate->GetCurrentContext();
        return value->ToString(context);
    }

    // ToInt32 signature changed in V8 10.8
    static MaybeLocal<Int32> ToInt32(Local<Value> value, Isolate* isolate) {
        Local<Context> context = isolate->GetCurrentContext();
        return value->ToInt32(context);
    }

    // ToNumber signature changed in V8 10.8
    static MaybeLocal<Number> ToNumber(Local<Value> value, Isolate* isolate) {
        Local<Context> context = isolate->GetCurrentContext();
        return value->ToNumber(context);
    }

    // ToObject signature changed in V8 10.8
    static MaybeLocal<Object> ToObject(Local<Value> value, Isolate* isolate) {
        Local<Context> context = isolate->GetCurrentContext();
        return value->ToObject(context);
    }

    // BooleanValue signature changed in V8 10.8
    static bool BooleanValue(Local<Value> value, Local<Context> context) {
        return value->BooleanValue(context->GetIsolate());
    }
};

// Compatibility extensions for Symbol
class SymbolCompat {
public:
    // Symbol::Name() was changed to Description() in V8 10.8
    static Local<Value> Name(Local<Symbol> symbol, Isolate* isolate) {
        return symbol->Description(isolate);
    }
};

// Compatibility extensions for FunctionTemplate
class FunctionTemplateCompat {
public:
    // GetFunction signature changed in V8 10.8
    static MaybeLocal<Function> GetFunction(Local<FunctionTemplate> tmpl, Local<Context> context) {
        return tmpl->GetFunction(context);
    }

    static Local<Function> GetFunction(Local<FunctionTemplate> tmpl, Isolate* isolate) {
        Local<Context> context = isolate->GetCurrentContext();
        return tmpl->GetFunction(context).ToLocalChecked();
    }

    // SetHiddenPrototype was removed in V8 10.8
    static void SetHiddenPrototype(Local<FunctionTemplate> tmpl, bool value) {
        // No-op in V8 10.8 - hidden prototypes are no longer supported
        // This was used for DOM inheritance but is now handled differently
    }
};



// For backward compatibility
using ObjectTemplateCompat = ObjectCompat;

// Compatibility extensions for FunctionCallbackInfo
template<typename T>
class FunctionCallbackInfoCompat {
public:
    // Callee was removed in V8 10.8
    static Local<Function> Callee(const FunctionCallbackInfo<T>& info) {
        // Return the function being called - use NewTarget as fallback
        Local<Value> newTarget = info.NewTarget();
        if (newTarget->IsFunction()) {
            return newTarget.As<Function>();
        }
        // Return empty function if not available
        return Local<Function>();
    }
};

// RetainedObjectInfo compatibility (removed in V8 10.8)
class RetainedObjectInfo {
public:
    RetainedObjectInfo() {}
    virtual ~RetainedObjectInfo() {}

    virtual void Dispose() = 0;
    virtual bool IsEquivalent(RetainedObjectInfo* other) = 0;
    virtual intptr_t GetHash() = 0;
    virtual const char* GetLabel() = 0;
    virtual intptr_t GetElementCount() { return -1; }
    virtual intptr_t GetSizeInBytes() { return -1; }
};

// NativeWeakMap compatibility (removed in V8 10.8)
template<typename K, typename V>
class NativeWeakMap {
public:
    NativeWeakMap() {}
    ~NativeWeakMap() {}

    // Provide basic interface for compatibility
    void Set(Local<K> key, Local<V> value) {
        // No-op in V8 10.8
    }

    Local<V> Get(Local<K> key) {
        return Local<V>();
    }

    bool Has(Local<K> key) {
        return false;
    }

    void Delete(Local<K> key) {
        // No-op in V8 10.8
    }
};

} // namespace v8

// FunctionEntryHook compatibility (removed in V8 10.8)
typedef void (*FunctionEntryHook)(uintptr_t function,
                                  uintptr_t return_addr_location);

namespace v8 {

// SysInfo compatibility for V8 10.8
namespace base {
class SysInfoCompat {
public:
    static int NumberOfProcessors() {
        return 4; // Default value
    }

    static int64_t AmountOfPhysicalMemory() {
        return 8LL * 1024 * 1024 * 1024; // 8GB default
    }

    static int64_t AmountOfVirtualMemory() {
        return 16LL * 1024 * 1024 * 1024; // 16GB default
    }
};
} // namespace base



// Compatibility extensions for ArrayBuffer
class ArrayBufferCompat {
public:
    // ArrayBuffer::Contents was removed in V8 10.8
    struct Contents {
        Contents() : data_(nullptr), byte_length_(0) {}
        Contents(void* data, size_t length) : data_(data), byte_length_(length) {}

        void* Data() const { return data_; }
        size_t ByteLength() const { return byte_length_; }
        void* AllocationBase() const { return data_; }
        size_t AllocationLength() const { return byte_length_; }
        void* AllocationMode() const { return nullptr; }
        void* Deleter() const { return nullptr; }
        void* DeleterData() const { return nullptr; }

    private:
        void* data_;
        size_t byte_length_;
    };
};

// Compatibility extensions for GlobalValueMap
template<typename K, typename V, typename Traits>
class GlobalValueMapCompat {
public:
    // SetReference was removed from GlobalValueMap - provide empty implementation
    static void SetReference(GlobalValueMap<K, V, Traits>* map, Isolate* isolate,
                           const Persistent<V>& parent, K key) {
        // No-op in V8 10.8 - reference tracking is handled differently
    }
};

// V8 compatibility - simplified implementation
class V8Compat {
public:
    // SetCaptureStackTraceForUncaughtExceptions was removed in V8 10.8
    static void SetCaptureStackTraceForUncaughtExceptions(bool capture, int frame_limit = 10) {
        // No-op in V8 10.8 - stack trace capture is handled differently
    }
};

// Compatibility extensions for Function
class FunctionCompat {
public:
    // Function::New signature changed in V8 10.8
    static MaybeLocal<Function> New(Isolate* isolate, FunctionCallback callback,
                                   Local<Value> data = Local<Value>(), int length = 0) {
        Local<Context> context = isolate->GetCurrentContext();
        return Function::New(context, callback, data, length);
    }

    // GetDisplayName was removed in V8 10.8
    static Local<Value> GetDisplayName(Local<Function> function) {
        // Return the function name as display name
        return function->GetName();
    }
};

// Debug compatibility - simplified implementation
class Debug {
public:
    static Local<Context> GetDebugContext(Isolate* isolate) {
        // Return the current context as debug context is no longer separate
        return isolate->GetCurrentContext();
    }

    // GetInternalProperties was removed in V8 10.8
    static MaybeLocal<Array> GetInternalProperties(Isolate* isolate, Local<Value> value) {
        // Return empty array as internal properties are no longer accessible
        return Array::New(isolate, 0);
    }

    // EventDetails compatibility class (removed in V8 10.8)
    class EventDetails {
    public:
        EventDetails() {}

        // Provide basic interface for compatibility
        Local<Value> GetEventData() const {
            return Local<Value>();
        }

        Local<Context> GetEventContext() const {
            return Local<Context>();
        }
    };
};

} // namespace v8

// Macros for easier migration
#define V8_ISOLATE_SET_OBJECT_GROUP_ID(isolate, object, id) \
    v8::IsolateCompat::SetObjectGroupId(isolate, object, id)

#define V8_PERSISTENT_MARK_INDEPENDENT(persistent) \
    v8::PersistentCompat::MarkIndependent(persistent)

#define V8_OBJECT_CREATION_CONTEXT(object) \
    v8::ObjectCompat::CreationContext(object)

#define V8_DEBUG_GET_DEBUG_CONTEXT(isolate) \
    v8::Debug::GetDebugContext(isolate)

#define V8_ISOLATE_RUN_MICROTASKS(isolate) \
    v8::IsolateCompat::RunMicrotasks(isolate)

#define V8_ISOLATE_SET_REFERENCE(isolate, parent, child) \
    v8::IsolateCompat::SetReference(isolate, parent, child)

#define V8_FUNCTION_NEW(isolate, callback, data, length) \
    v8::FunctionCompat::New(isolate, callback, data, length)

#endif // V8_MAJOR_VERSION >= 10

#endif // V8_COMPATIBILITY_H_
