<!DOCTYPE html>
<title>IDBCursor.continue() - object store - attempt to pass a key parameter is not a valid key </title>
<link rel="author" title="Microsoft" href="http://www.microsoft.com">
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<script src="resources/support.js"></script>

<script>
    var db,
      t = async_test(),
      records = [ { pKey: "primaryKey_0" },
                  { pKey: "primaryKey_1" } ];

    var open_rq = createdb(t);
    open_rq.onupgradeneeded = function(e) {
        db = e.target.result;
        var objStore = db.createObjectStore("test", {keyPath:"pKey"});

        for (var i = 0; i < records.length; i++)
            objStore.add(records[i]);
    };

    open_rq.onsuccess = function(e) {
        var cursor_rq = db.transaction("test")
                          .objectStore("test")
                          .openCursor();

        cursor_rq.onsuccess = t.step_func(function(e) {
            var cursor = e.target.result;

            assert_true(cursor instanceof IDBCursor, "cursor exists");
            assert_throws_dom("DataError",
                function() { cursor.continue(document); });

            t.done();
        });
    };
</script>

<div id="log"> </div>
