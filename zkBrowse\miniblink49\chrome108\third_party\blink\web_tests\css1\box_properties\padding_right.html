<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.5.07 padding-right</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
.zero {background-color: silver; padding-right: 0;}
.one {padding-right: 0.5in; text-align: right; background-color: aqua;}
.two {padding-right: 25px; text-align: right; background-color: aqua;}
.three {padding-right: 5em; text-align: right; background-color: aqua;}
.four {padding-right: 25%; text-align: right; background-color: aqua;}
.five {padding-right: -20px; text-align: right; background-color: aqua;}</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.zero {background-color: silver; padding-right: 0;}
.one {padding-right: 0.5in; text-align: right; background-color: aqua;}
.two {padding-right: 25px; text-align: right; background-color: aqua;}
.three {padding-right: 5em; text-align: right; background-color: aqua;}
.four {padding-right: 25%; text-align: right; background-color: aqua;}
.five {padding-right: -20px; text-align: right; background-color: aqua;}
</PRE>
<HR>
<P class="zero">
This element has a class of zero.
</P>
<P class="one">
This element should have a right padding of half an inch, which will require extra text in order to test.  Both the content background and the padding should be aqua (light blue).  The text has been right-aligned in order to make the right padding easier to see.
</P>
<P class="two">
This element should have a right padding of 25 pixels, which will require extra text in order to test.  Both the content background and the padding should be aqua (light blue).  The text has been right-aligned in order to make the right padding easier to see.
</P>
<P class="three">
This element should have a right padding of 5 em, which will require extra text in order to test.  Both the content background and the padding should be aqua (light blue).  The text has been right-aligned in order to make the right padding easier to see.
</P>
<P class="four">
This element should have a right padding of 25%, which is calculated with respect to the width of the parent element.  Both the content background and the padding should be aqua (light blue).  The text has been right-aligned in order to make the right padding easier to see.
</P>
<UL class="two" style="background-color: gray;">
<LI>The right padding on this unordered list has been set to 25 pixels, which will require some extra text in order to test.</LI>
<LI class="two" style="background-color: white;">This list item has a right padding of 25 pixels, which will appear to the left of the gray padding of the UL element.
</UL>
<P class="five">
This element should have no right padding, since negative padding values are not allowed.  Both the content background and the normal padding should be aqua (light blue).  The text has been right-aligned in order to make the lack of right padding easier to see.
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P class="zero">
This element has a class of zero.
</P>
<P class="one">
This element should have a right padding of half an inch, which will require extra text in order to test.  Both the content background and the padding should be aqua (light blue).  The text has been right-aligned in order to make the right padding easier to see.
</P>
<P class="two">
This element should have a right padding of 25 pixels, which will require extra text in order to test.  Both the content background and the padding should be aqua (light blue).  The text has been right-aligned in order to make the right padding easier to see.
</P>
<P class="three">
This element should have a right padding of 5 em, which will require extra text in order to test.  Both the content background and the padding should be aqua (light blue).  The text has been right-aligned in order to make the right padding easier to see.
</P>
<P class="four">
This element should have a right padding of 25%, which is calculated with respect to the width of the parent element.  Both the content background and the padding should be aqua (light blue).  The text has been right-aligned in order to make the right padding easier to see.
</P>
<UL class="two" style="background-color: gray;">
<LI>The right padding on this unordered list has been set to 25 pixels, which will require some extra text in order to test.</LI>
<LI class="two" style="background-color: white;">This list item has a right padding of 25 pixels, which will appear to the left of the gray padding of the UL element.
</UL>
<P class="five">
This element should have no right padding, since negative padding values are not allowed.  Both the content background and the normal padding should be aqua (light blue).  The text has been right-aligned in order to make the lack of right padding easier to see.
</P>
</TD></TR></TABLE></BODY>
</HTML>
