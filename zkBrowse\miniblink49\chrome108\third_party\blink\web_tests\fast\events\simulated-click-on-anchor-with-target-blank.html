<!DOCTYPE html>
<script>
function test()
{
    if (window.testRunner) {
        testRunner.dumpAsText();
        testRunner.dumpNavigationPolicy();
        document.querySelector("#link").focus();
        eventSender.keyDown("Enter", ["ctrlKey", "metaKey"]);
    }
}
</script>
<body onload="test()">
<p>Tests that hitting ctrl-enter on a link with target=_blank still opens it in the background</p>
<a href="blank" id="link">link</a>
</body>
