Tests that ScriptSearchScope performs search across all sources correctly.

Total uiSourceCodes: 7

Running: testIgnoreCase
Search result #1: uiSourceCode.url = file:///var/www/search.css
  search match #1: lineNumber = 0, lineContent = 'div.searchTestUniqueString {'
  search match #2: lineNumber = 4, lineContent = 'div.searchTestUniqueString:hover {'
  search match #3: lineNumber = 5, lineContent = '    /* another searchTestUniqueString occurence */'
Search result #2: uiSourceCode.url = file:///var/www/search.html
  search match #1: lineNumber = 4, lineContent = '<script>window.eval("function searchTestUniqueString() {}");</script>'
  search match #2: lineNumber = 6, lineContent = '<div>searchTestUniqueString</div>'
  search match #3: lineNumber = 8, lineContent = '<!-- searchTestUniqueString -->'
  search match #4: lineNumber = 10, lineContent = '<div id="searchTestUniqueString">div text</div>'
Search result #3: uiSourceCode.url = file:///var/www/search.js
  search match #1: lineNumber = 0, lineContent = 'function searchTestUniqueString()'
  search match #2: lineNumber = 3, lineContent = '    // searchTestUniqueString two occurences on the same line searchTestUniqueString'
  search match #3: lineNumber = 4, lineContent = '    // searchTestUniqueString on the next line.'
  search match #4: lineNumber = 10, lineContent = '    searchTestUniqueString();'
  search match #5: lineNumber = 11, lineContent = '    // SEARCHTestUniqueString();'

Running: testCaseSensitive
Search result #1: uiSourceCode.url = file:///var/www/search.css
  search match #1: lineNumber = 0, lineContent = 'div.searchTestUniqueString {'
  search match #2: lineNumber = 4, lineContent = 'div.searchTestUniqueString:hover {'
  search match #3: lineNumber = 5, lineContent = '    /* another searchTestUniqueString occurence */'
Search result #2: uiSourceCode.url = file:///var/www/search.html
  search match #1: lineNumber = 4, lineContent = '<script>window.eval("function searchTestUniqueString() {}");</script>'
  search match #2: lineNumber = 6, lineContent = '<div>searchTestUniqueString</div>'
  search match #3: lineNumber = 8, lineContent = '<!-- searchTestUniqueString -->'
  search match #4: lineNumber = 10, lineContent = '<div id="searchTestUniqueString">div text</div>'
Search result #3: uiSourceCode.url = file:///var/www/search.js
  search match #1: lineNumber = 0, lineContent = 'function searchTestUniqueString()'
  search match #2: lineNumber = 3, lineContent = '    // searchTestUniqueString two occurences on the same line searchTestUniqueString'
  search match #3: lineNumber = 4, lineContent = '    // searchTestUniqueString on the next line.'
  search match #4: lineNumber = 10, lineContent = '    searchTestUniqueString();'

Running: testFileHTML
Search result #1: uiSourceCode.url = file:///var/www/search.html
  search match #1: lineNumber = 4, lineContent = '<script>window.eval("function searchTestUniqueString() {}");</script>'
  search match #2: lineNumber = 6, lineContent = '<div>searchTestUniqueString</div>'
  search match #3: lineNumber = 8, lineContent = '<!-- searchTestUniqueString -->'
  search match #4: lineNumber = 10, lineContent = '<div id="searchTestUniqueString">div text</div>'

Running: testFileJS
Search result #1: uiSourceCode.url = file:///var/www/search.js
  search match #1: lineNumber = 0, lineContent = 'function searchTestUniqueString()'
  search match #2: lineNumber = 3, lineContent = '    // searchTestUniqueString two occurences on the same line searchTestUniqueString'
  search match #3: lineNumber = 4, lineContent = '    // searchTestUniqueString on the next line.'
  search match #4: lineNumber = 10, lineContent = '    searchTestUniqueString();'
  search match #5: lineNumber = 11, lineContent = '    // SEARCHTestUniqueString();'

Running: testFileHTMLJS

Running: testSpaceQueries
Search result #1: uiSourceCode.url = file:///var/www/search.css
  search match #1: lineNumber = 9, lineContent = '/* searchTestUnique space String */'
Search result #2: uiSourceCode.url = file:///var/www/search.html
  search match #1: lineNumber = 14, lineContent = '<!-- searchTestUnique space String -->'
Search result #3: uiSourceCode.url = file:///var/www/search.js
  search match #1: lineNumber = 14, lineContent = '// searchTestUnique space String'

Running: testSpaceQueriesFileHTML
Search result #1: uiSourceCode.url = file:///var/www/search.html
  search match #1: lineNumber = 14, lineContent = '<!-- searchTestUnique space String -->'

Running: testSpaceQueriesFileHTML_SEARCH
Search result #1: uiSourceCode.url = file:///var/www/search.html
  search match #1: lineNumber = 14, lineContent = '<!-- searchTestUnique space String -->'

Running: testSpaceQueriesFileJS_SEARCH_HTML

Running: testSeveralQueriesFileHTML
Search result #1: uiSourceCode.url = file:///var/www/search.html
  search match #1: lineNumber = 4, lineContent = '<script>window.eval("function searchTestUniqueString() {}");</script>'
  search match #2: lineNumber = 6, lineContent = '<div>searchTestUniqueString</div>'
  search match #3: lineNumber = 8, lineContent = '<!-- searchTestUniqueString -->'
  search match #4: lineNumber = 10, lineContent = '<div id="searchTestUniqueString">div text</div>'
  search match #5: lineNumber = 14, lineContent = '<!-- searchTestUnique space String -->'

Running: testSeveralQueriesFileHTML_SEARCH
Search result #1: uiSourceCode.url = file:///var/www/search.html
  search match #1: lineNumber = 4, lineContent = '<script>window.eval("function searchTestUniqueString() {}");</script>'
  search match #2: lineNumber = 6, lineContent = '<div>searchTestUniqueString</div>'
  search match #3: lineNumber = 8, lineContent = '<!-- searchTestUniqueString -->'
  search match #4: lineNumber = 10, lineContent = '<div id="searchTestUniqueString">div text</div>'
  search match #5: lineNumber = 14, lineContent = '<!-- searchTestUnique space String -->'

Running: testSeveralQueriesFileJS_SEARCH_HTML

Running: testFileSEARCH_NOT_JS_NOT_CSS
Search result #1: uiSourceCode.url = file:///var/www/search.html
  search match #1: lineNumber = 4, lineContent = '<script>window.eval("function searchTestUniqueString() {}");</script>'
  search match #2: lineNumber = 6, lineContent = '<div>searchTestUniqueString</div>'
  search match #3: lineNumber = 8, lineContent = '<!-- searchTestUniqueString -->'
  search match #4: lineNumber = 10, lineContent = '<div id="searchTestUniqueString">div text</div>'

Running: testSeveralQueriesFileNotCSS
Search result #1: uiSourceCode.url = file:///var/www/search.html
  search match #1: lineNumber = 4, lineContent = '<script>window.eval("function searchTestUniqueString() {}");</script>'
  search match #2: lineNumber = 6, lineContent = '<div>searchTestUniqueString</div>'
  search match #3: lineNumber = 8, lineContent = '<!-- searchTestUniqueString -->'
  search match #4: lineNumber = 10, lineContent = '<div id="searchTestUniqueString">div text</div>'
  search match #5: lineNumber = 14, lineContent = '<!-- searchTestUnique space String -->'
Search result #2: uiSourceCode.url = file:///var/www/search.js
  search match #1: lineNumber = 0, lineContent = 'function searchTestUniqueString()'
  search match #2: lineNumber = 3, lineContent = '    // searchTestUniqueString two occurences on the same line searchTestUniqueString'
  search match #3: lineNumber = 4, lineContent = '    // searchTestUniqueString on the next line.'
  search match #4: lineNumber = 10, lineContent = '    searchTestUniqueString();'
  search match #5: lineNumber = 11, lineContent = '    // SEARCHTestUniqueString();'
  search match #6: lineNumber = 14, lineContent = '// searchTestUnique space String'

Running: testFileQueryWithProjectName
Running a file query with existing project name first:
Search result #1: uiSourceCode.url = file:///var/www/search.css
  search match #1: lineNumber = 0, lineContent = 'div.searchTestUniqueString {'
  search match #2: lineNumber = 4, lineContent = 'div.searchTestUniqueString:hover {'
  search match #3: lineNumber = 5, lineContent = '    /* another searchTestUniqueString occurence */'
  search match #4: lineNumber = 9, lineContent = '/* searchTestUnique space String */'
Search result #2: uiSourceCode.url = file:///var/www/search.html
  search match #1: lineNumber = 4, lineContent = '<script>window.eval("function searchTestUniqueString() {}");</script>'
  search match #2: lineNumber = 6, lineContent = '<div>searchTestUniqueString</div>'
  search match #3: lineNumber = 8, lineContent = '<!-- searchTestUniqueString -->'
  search match #4: lineNumber = 10, lineContent = '<div id="searchTestUniqueString">div text</div>'
  search match #5: lineNumber = 14, lineContent = '<!-- searchTestUnique space String -->'
Search result #3: uiSourceCode.url = file:///var/www/search.js
  search match #1: lineNumber = 0, lineContent = 'function searchTestUniqueString()'
  search match #2: lineNumber = 3, lineContent = '    // searchTestUniqueString two occurences on the same line searchTestUniqueString'
  search match #3: lineNumber = 4, lineContent = '    // searchTestUniqueString on the next line.'
  search match #4: lineNumber = 10, lineContent = '    searchTestUniqueString();'
  search match #5: lineNumber = 11, lineContent = '    // SEARCHTestUniqueString();'
  search match #6: lineNumber = 14, lineContent = '// searchTestUnique space String'
Running a file query with non-existing project name now:

Running: testDirtyFiles
Search result #1: uiSourceCode.url = file:///var/www/search.css
  search match #1: lineNumber = 0, lineContent = 'div.searchTestUniqueString {'
  search match #2: lineNumber = 4, lineContent = 'div.searchTestUniqueString:hover {'
  search match #3: lineNumber = 5, lineContent = '    /* another searchTestUniqueString occurence */'
Search result #2: uiSourceCode.url = file:///var/www/search.html
  search match #1: lineNumber = 4, lineContent = '<script>window.eval("function searchTestUniqueString() {}");</script>'
  search match #2: lineNumber = 6, lineContent = '<div>searchTestUniqueString</div>'
  search match #3: lineNumber = 8, lineContent = '<!-- searchTestUniqueString -->'
  search match #4: lineNumber = 10, lineContent = '<div id="searchTestUniqueString">div text</div>'
Search result #3: uiSourceCode.url = file:///var/www/search.js
  search match #1: lineNumber = 0, lineContent = 'FOO searchTestUniqueString BAR'

