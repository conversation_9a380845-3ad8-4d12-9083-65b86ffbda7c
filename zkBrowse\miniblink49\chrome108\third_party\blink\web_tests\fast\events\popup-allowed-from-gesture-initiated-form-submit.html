<html>
    <script src="../../resources/testdriver.js"></script>
    <script src="../../resources/testdriver-vendor.js"></script>
    <body>
        <form action="resources/popup-allowed-from-gesture-initiated-form-submit-target.html" method="post" target="_blank" rel="opener">
            <input id="button" type="submit" value="Click Here" />
        </form>
        <div id="console">FAIL</div>
        <script>
            if (window.testRunner) {
                testRunner.dumpAsText();
                testRunner.waitUntilDone();

                var button = document.getElementById("button");

                window.onload = function() {
                    test_driver.click(button);
                }
            }
        </script>
    </body>
</html>
