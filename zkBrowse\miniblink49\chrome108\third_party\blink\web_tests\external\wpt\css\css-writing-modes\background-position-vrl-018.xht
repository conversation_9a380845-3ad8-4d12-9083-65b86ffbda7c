<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

 <head>

  <title>CSS Writing Modes Test: 'background-position: left top' and 'vertical-rl' when document root element does not fill viewport width</title>

  <link rel="author" title="G<PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" />
  <link rel="help" href="http://www.w3.org/TR/css-writing-modes-3/#physical-only" title="7.6 Purely Physical Mappings" />
  <link rel="match" href="background-position-vrl-018-ref.xht" />

  <meta content="image" name="flags" />
  <meta content="This test checks that 'background-position: left top' will make background-image start at left side of document root element (even in case there is resizing of the window) because background properties should not be affected by vertical writing-mode." name="assert" />

  <style type="text/css"><![CDATA[
  html
    {
      background-image: url("support/100x100-red.png");
      background-position: left top;
      background-repeat: repeat-y;
      width: auto; /* Very important: we intentionally want the
      document root element to not fill the viewport width */
      writing-mode: vertical-rl;
    }

  div#reference-overlapping-green
    {
      background-color: green;
      height: 100%;
      position: absolute;
      right: 273px;
      /*
        8px : body's margin-right
    +
      357px : pass-cdts-background-position.png's image width
    +
        8px : body's margin-left
    -
      100px : background-image width (100x100-red.png)
    ==========
      273px is
      */
      top: 0px;
      width: 100px;
    }

    /*
    This test requires a viewport width of at least (strict minimum) of 473px !
    */
  ]]></style>
 </head>

 <body>

  <div><img src="support/pass-cdts-bg-pos-vrl-018.png" width="357" height="17" alt="Image download support must be enabled" /></div>

<!--
  The image says:

  Test passes if there is a <strong>no red</strong>.
-->

  <div id="reference-overlapping-green"></div>

 </body>
</html>
