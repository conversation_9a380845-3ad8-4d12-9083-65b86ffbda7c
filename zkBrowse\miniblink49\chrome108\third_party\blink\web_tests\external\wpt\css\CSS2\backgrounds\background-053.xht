<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background with (repeat image color)</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="<PERSON><PERSON>" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-03-19 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <link rel="match" href="background-026-ref.xht" />

        <meta name="flags" content="image" />
        <meta name="assert" content="Background with (repeat image color) sets the background of the element to the color specified and places the image in its initial position repeating across the x-axis." />
        <style type="text/css">
            div
            {
                background: repeat-x url("support/blue15x15.png") green;
                height: 200px;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is a blue stripe above a green rectangle across the page.</p>
        <div></div>
    </body>
</html>