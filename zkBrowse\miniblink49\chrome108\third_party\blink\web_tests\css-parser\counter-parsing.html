<!doctype html>
<script src="../resources/testharness.js"></script>
<script src="../resources/testharnessreport.js"></script>
<script src="resources/property-parsing-test.js"></script>
<script>
assert_valid_value("counterIncrement", "hello world 999", "hello 1 world 999");
assert_valid_value("counterIncrement", "a 1234567");
assert_invalid_value("counterIncrement", 'hello "world"');
assert_invalid_value("counterIncrement", "40");
assert_invalid_value("counterReset", 'c 10 20');
assert_invalid_value("counterReset", "c + 5");
</script>
