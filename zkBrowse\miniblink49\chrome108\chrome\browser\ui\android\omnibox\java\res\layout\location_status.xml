<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright 2018 The Chromium Authors
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file.
-->
<merge
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <!-- StatusView acts as a single focusable group. This improves accessibility screen
        reading behavior. -->
    <org.chromium.chrome.browser.omnibox.status.StatusView
        android:id="@+id/location_bar_status"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:paddingEnd="@dimen/location_bar_icon_end_padding"
        android:focusable="true"
        android:screenReaderFocusable="true">

        <!-- End padding is adjusted programmatically. -->
        <ViewStub
            android:id="@+id/location_bar_incognito_badge_stub"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:inflatedId="@+id/location_bar_incognito_badge"
            android:layout="@layout/location_status_incognito_badge"
            android:focusable="false" />

        <include layout="@layout/location_status_icon" />

        <TextView android:id="@+id/location_bar_verbose_status"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingStart="@dimen/location_bar_verbose_start_padding_verbose_text"
            android:paddingEnd="8dp"
            android:gravity="center_vertical"
            android:textAlignment="viewStart"
            android:textAppearance="@style/TextAppearance.OmniboxVerboseStatus"
            android:ellipsize="end"
            android:maxLines="1"
            android:visibility="gone"
            android:focusable="false" />

        <!-- Separator is going to be shown or hidden together with the status defined above. -->
        <View android:id="@+id/location_bar_verbose_status_separator"
            android:layout_width="@dimen/location_bar_status_separator_width"
            android:layout_height="24dp"
            android:layout_gravity="center_vertical"
            android:background="@color/black_alpha_12"
            android:visibility="gone"
            android:focusable="false" />

        <!-- This space follows the verbose status and compliments the space that follows
            the status. -->
        <Space android:id="@+id/location_bar_verbose_status_extra_space"
            android:layout_width="@dimen/location_bar_status_extra_padding"
            android:layout_height="match_parent"
            android:visibility="gone"
            android:focusable="false" />

    </org.chromium.chrome.browser.omnibox.status.StatusView>
</merge>
