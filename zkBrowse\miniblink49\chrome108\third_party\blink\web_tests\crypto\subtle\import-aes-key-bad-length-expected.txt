Tests importing an AES key from raw with wrong length

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".

PASS: Failed to import AES-CBC key of length 0 bytes
PASS: Failed to import AES-CBC key of length 1 bytes
PASS: Failed to import AES-CBC key of length 15 bytes
PASS: Failed to import AES-CBC key of length 17 bytes
PASS: Failed to import AES-CBC key of length 31 bytes
PASS: Failed to import AES-CBC key of length 33 bytes
PASS: Failed to import AES-CBC key of length 23 bytes
PASS: Failed to import AES-CBC key of length 25 bytes
PASS: Failed to import AES-CBC key of length 64 bytes
PASS: Failed to import AES-GCM key of length 0 bytes
PASS: Failed to import AES-GCM key of length 1 bytes
PASS: Failed to import AES-GCM key of length 15 bytes
PASS: Failed to import AES-GCM key of length 17 bytes
PASS: Failed to import AES-GCM key of length 31 bytes
PASS: Failed to import AES-GCM key of length 33 bytes
PASS: Failed to import AES-GCM key of length 23 bytes
PASS: Failed to import AES-GCM key of length 25 bytes
PASS: Failed to import AES-GCM key of length 64 bytes
PASS: Failed to import AES-KW key of length 0 bytes
PASS: Failed to import AES-KW key of length 1 bytes
PASS: Failed to import AES-KW key of length 15 bytes
PASS: Failed to import AES-KW key of length 17 bytes
PASS: Failed to import AES-KW key of length 31 bytes
PASS: Failed to import AES-KW key of length 33 bytes
PASS: Failed to import AES-KW key of length 23 bytes
PASS: Failed to import AES-KW key of length 25 bytes
PASS: Failed to import AES-KW key of length 64 bytes
PASS successfullyParsed is true

TEST COMPLETE

