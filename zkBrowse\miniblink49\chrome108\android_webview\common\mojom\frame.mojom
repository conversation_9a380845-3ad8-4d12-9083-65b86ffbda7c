// Copyright 2020 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

module android_webview.mojom;

import "mojo/public/mojom/base/string16.mojom";
import "mojo/public/mojom/base/time.mojom";
import "ui/gfx/geometry/mojom/geometry.mojom";
import "url/mojom/url.mojom";

enum HitTestDataType{
  // Default type where nothing we are interested in is hit.
  // |extra_data_for_type| will be empty. All other values should be emtpy
  // except the special case described below.
  // For special case of invalid or javascript scheme url that would
  // otherwise be type an LINK type, |href| will contain the javascript
  // string in the href attribute, and |anchor_text| and |img_src| contain
  // their normal values for the respective type.
  kUnknown = 0,

  // Special case urls for kSrcLink below. Each type corresponds to a
  // different prefix in content url_constants. |extra_data_for_type| will
  // contain the url but with the prefix removed. |href| will contain the
  // exact href attribute string. Other fields are the same as kSrcLink.
  kPhone = 2,
  kGeo = 3,
  kEmail = 4,

  // Hit on a pure image (without links). |extra_data_for_type|, |href|,
  // and |anchor_text| will be empty. |img_src| will contain the absolute
  // source url of the image.
  kImage = 5,

  // Hit on a link with valid and non-javascript url and without embedded
  // image. |extra_data_for_type| and |href| will be the valid absolute url
  // of the link. |anchor_text| will contain the anchor text if the link is
  // an anchor tag. |img_src| will be empty.
  // Note 1: If the link url is invalid or javascript scheme, then the type
  // will be UNKNOWN_TYPE.
  // Note 2: Note that this matches SRC_ANCHOR_TYPE in the public WebView
  // Java API, but the actual tag can be something other than <a>, such as
  // <link> or <area>.
  // Note 3: |href| is not the raw attribute string, but the absolute link
  // url.
  kSrcLink = 7,

  // Same as kSrcLink except the link contains an image. |img_src| and
  // |extra_data_for_type| will contain the absolute valid url of the image
  // source. |href| will be the valid absolute url of the link. |anchor_text|
  // will be empty. All notes from kSrcLink apply.
  kSrcImageLink = 8,

  // Hit on an editable text input element. All other values will be empty.
  kEditText = 9,
};

// Holds all hit test data needed by public WebView APIs.
// The Java counter part to this is AwContents.HitTestData.
struct HitTestData {
  // The type of the hit test.
  HitTestDataType type;

  // The extra type of the hit test.
  string extra_data_for_type;

  // The valid absolute url of the link.
  mojo_base.mojom.String16 href;

  // The anchor text of the link
  mojo_base.mojom.String16 anchor_text;

  // The valid absolute url of the image source.
  url.mojom.Url img_src;
};

// Similar to blink::mojom::LocalMainFrame. This interface adds additional
// things that webview needs from the main frame.
interface LocalMainFrame {

  // Sets the initial page scale. This overrides initial scale set by
  // the meta viewport tag.
  SetInitialPageScale(double page_scale_factor);

  // Sets the zoom factor for text only. Used in layout modes other than
  // Text Autosizing.
  SetTextZoomFactor(float zoom_factor);

  // Do hit test at the given webview coordinate. "Webview" coordinates are
  // physical pixel values with the 0,0 at the top left of the current displayed
  // view (ie 0,0 is not the top left of the page if the page is scrolled).
  // AwRenderViewHostExt::OnUpdateHitTestData receives the result of the hit
  // test via AwViewHostMsg_UpdateHitTestData IPC message.
  HitTest(gfx.mojom.PointF touch_center, gfx.mojom.SizeF touch_area);

  // Requests for the renderer to determine if the document contains any image
  // elements.
  DocumentHasImage() => (bool has_images);

  // Resets Blink WebView scrolling and scale state. We need to call this
  // method whenever we want to guarantee that page's scale will be recalculated
  // by Blink.
  ResetScrollAndScaleState();

  // Tells Blink to smooth scroll to the specified location within |duration|
  // miliseconds.
  SmoothScroll(
      int32 target_x, int32 target_y, mojo_base.mojom.TimeDelta duration);
};

// Similar to blink::mojom::FrameHost. Implemented in Browser, this
// interface defines frame-specific methods that will be invoked from the
// renderer process (e.g. AwRenderFrameExt).
interface FrameHost {

  // Response to AwViewMsg_DoHitTest.
  UpdateHitTestData(HitTestData data);

  // Calls whenever the contents size (as seen by RenderView) is changed.
  ContentsSizeChanged(gfx.mojom.Size contents_size);

  // Calls immediately before a top level navigation is initiated within Blink.
  // There are some exlusions, the most important ones are it is not sent
  // when creating a popup window, and not sent for application initiated
  // navigations. See AwContentRendererClient::HandleNavigation for all
  // cornercases. This is sent before updating the NavigationController state
  // or creating a URLRequest for the main frame resource.
  [Sync]
  ShouldOverrideUrlLoading(mojo_base.mojom.String16 url,
                           bool has_user_gesture, bool is_redirect,
                           bool is_outermost_main_frame)
      => (bool result);
};
