[wpt-server-websocket.sub.html]
  [WSS protocol, www subdomain #3]
    expected:
      if product == "epiphany" or product == "webkit": FAIL

  [WSS protocol, punycode subdomain #1]
    expected:
      if product == "epiphany" or product == "webkit": FAIL

  [WSS protocol, punycode subdomain #2]
    expected:
      if product == "epiphany" or product == "webkit": FAIL

  [WSS protocol, www subdomain #1]
    expected:
      if product == "epiphany" or product == "webkit": FAIL

  [WSS protocol, www subdomain #2]
    expected:
      if product == "epiphany" or product == "webkit": FAIL

  [WSS protocol, www subdomain #3]
    expected:
      if product == "epiphany" or product == "webkit": FAIL

  [WSS protocol, punycode subdomain #1]
    expected:
      if product == "epiphany" or product == "webkit": FAIL

  [WSS protocol, punycode subdomain #2]
    expected:
      if product == "epiphany" or product == "webkit": FAIL

  [WSS protocol, www subdomain #1]
    expected:
      if product == "epiphany" or product == "webkit": FAIL

  [WSS protocol, www subdomain #2]
    expected:
      if product == "epiphany" or product == "webkit": FAIL

