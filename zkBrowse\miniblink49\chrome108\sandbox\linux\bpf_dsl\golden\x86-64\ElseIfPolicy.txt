  1) LOAD 4  // Architecture
  2) if A == 0xc000003e; then JMP 3 else JMP 27
  3) LOAD 0  // System call number
  4) if A & 0x40000000; then JMP 27 else JMP 5
  5) if A >= 0x6a; then JMP 6 else JMP 7
  6) if A >= 0x401; then JMP 35 else JMP 34
  7) if A >= 0x69; then JMP 8 else JMP 34
  8) LOAD 20  // Argument 0 (MSB)
  9) if A == 0x0; then JMP 13 else JMP 10
 10) if A == 0xffffffff; then JMP 11 else JMP 27
 11) LOAD 16  // Argument 0 (LSB)
 12) if A & 0x80000000; then JMP 13 else JMP 27
 13) LOAD 16  // Argument 0 (LSB)
 14) if A & 0xfff; then JMP 15 else JMP 33
 15) LOAD 20  // Argument 0 (MSB)
 16) if A == 0x0; then JMP 20 else JMP 17
 17) if A == 0xffffffff; then JMP 18 else JMP 27
 18) LOAD 16  // Argument 0 (LSB)
 19) if A & 0x80000000; then JMP 20 else JMP 27
 20) LOAD 16  // Argument 0 (LSB)
 21) if A & 0xff0; then JMP 22 else JMP 32
 22) LOAD 20  // Argument 0 (MSB)
 23) if A == 0x0; then JMP 28 else JMP 24
 24) if A == 0xffffffff; then JMP 25 else JMP 27
 25) LOAD 16  // Argument 0 (LSB)
 26) if A & 0x80000000; then JMP 28 else JMP 27
 27) RET 0x0  // Kill
 28) LOAD 16  // Argument 0 (LSB)
 29) if A & 0xf00; then JMP 30 else JMP 31
 30) RET 0x5000d  // errno = 13
 31) RET 0x50011  // errno = 17
 32) RET 0x50016  // errno = 22
 33) RET 0x50000  // errno = 0
 34) RET 0x7fff0000  // Allowed
 35) RET 0x50026  // errno = 38
