<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
    <head>
        <title>CSS Test: Margin applied to element with display table-row-group</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/">
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#propdef-margin">
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#margin-properties">
        <meta name="flags" content="">
        <meta name="assert" content="The 'Margin' property does not apply to elements with a display of table-row-group.">
        <style type="text/css">
            #wrapper
            {
                border: 10px solid blue;
                position: absolute;
            }
            #test
            {
                display: table-row-group;
                margin: 50px;
            }
            #table
            {
                display: table;
                table-layout: fixed;
            }
            #row
            {
                display: table-row;
            }
            #cell
            {
                border: 10px solid orange;
                display: table-cell;
                height: 200px;
                width: 200px;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is no space between the blue and orange lines below.</p>
        <div id="wrapper">
            <div id="table">
                <div id="test">
                    <div id="row">
                        <div id="cell"></div>
                    </div>
                </div>
            </div>
        </div>
    </body>
</html>