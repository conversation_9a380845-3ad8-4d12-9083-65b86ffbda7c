<!DOCTYPE html>
<html>
<body onselectstart="handler(event)">
<p>This test ensures selectstart event fires when selecting all.</p>
<script>

if (window.testRunner) {
    testRunner.dumpAsText();
    testRunner.dumpEditingCallbacks();
}

var targetWasBody = true;
var handlerCount = 0;
var listenerCount = 0;

function handler(event) {
    if (event.target != document.body)
        targetWasBody = false;
    handlerCount++;
}

document.body.addEventListener('selectstart', function (event) {
    if (event.target != document.body)
        targetWasBody = false;
    listenerCount++;
});

document.execCommand('SelectAll', false, null);

if (handlerCount != 1)
    document.writeln("FAIL: selectionstart's event handler was called " + handlerCount + ' times');
else if (listenerCount != 1)
    document.writeln("FAIL: selectionstart's event listener was called " + listenerCount + ' times');
else if (!targetWasBody)
    document.writeln("FAIL: target node wasn't body");
else if (window.getSelection().toString() != document.body.innerText)
    document.writeln("FAIL: some contents on document was not selected");
else
    document.writeln("PASS");

</script>
</body>
</html>
