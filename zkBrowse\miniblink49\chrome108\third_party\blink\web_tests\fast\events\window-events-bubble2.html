<html>
<head>
<script>

if (window.testRunner) {
    testRunner.dumpAsText();
    testRunner.waitUntilDone();
}
function test()
{
    if (window.eventSender) {
        eventSender.mouseMoveTo(52, 52);
        eventSender.mouseDown();
        eventSender.mouseUp();
    }
    window.setTimeout('finish()', 0);
}

function finish()
{
    if (window.testRunner)
        testRunner.notifyDone();
}

window.onclick = function(e) { log("Window.onClick fired.  Test Passed."); };
        
function log(text) {
    document.getElementById("result").innerHTML = text;
}
</script>

</head>
<body onload="test()">
    Tests that preventDefault() will still allow window events to bubble.
    <div id="d1" style="position: absolute; top: 50; left: 50; border: solid thin" onclick="log('preventDefault called.  Test Failed.'); event.preventDefault();">
       Clicking here should fire window.onclick.  This will match Firefox behavior.
    </div>
        
    <div id="result" style="position: absolute; top: 100; left: 50;"><br></div>
</body>
</html>
