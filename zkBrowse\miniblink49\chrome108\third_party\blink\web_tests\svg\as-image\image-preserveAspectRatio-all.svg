<svg width="800" height="400" viewBox="0 0 1200 600"
     xmlns="http://www.w3.org/2000/svg"
     xmlns:xlink="http://www.w3.org/1999/xlink">
  <title>Example feImage - Examples of feImage use</title>
  <desc>Testing all preserveAspectRatio options for feImage.
        See bug 99984.</desc>
  <rect fill="none" stroke="blue" x="1" y="1" width="1198" height="498"/>
  <g>
    <image xlink:href="../W3C-SVG-1.1/resources/smiley.png" x="50"  y="25" width="100" height="200" preserveAspectRatio="none"/>
    <rect x="50"  y="25" width="100" height="200" fill="none" stroke="green"/>
    <image xlink:href="../W3C-SVG-1.1/resources/smiley.png" x="50"  y="325" width="200" height="100" preserveAspectRatio="none"/>
    <rect x="50"  y="325" width="200" height="100" fill="none" stroke="green"/>

    <image xlink:href="../W3C-SVG-1.1/resources/smiley.png" x="300" y="25" width="100" height="200" preserveAspectRatio="xMinYMin meet"/>
    <rect x="300" y="25" width="100" height="200" fill="none" stroke="green"/>
    <image xlink:href="../W3C-SVG-1.1/resources/smiley.png" x="420" y="25" width="100" height="200" preserveAspectRatio="xMidYMid meet"/>
    <rect x="420" y="25" width="100" height="200" fill="none" stroke="green"/>
    <image xlink:href="../W3C-SVG-1.1/resources/smiley.png" x="540" y="25" width="100" height="200" preserveAspectRatio="xMaxYMax meet"/>
    <rect x="540" y="25" width="100" height="200" fill="none" stroke="green"/>

    <image xlink:href="../W3C-SVG-1.1/resources/smiley.png" x="700" y="25" width="200" height="100" preserveAspectRatio="xMinYMin meet"/>
    <rect x="700" y="25" width="200" height="100" fill="none" stroke="green"/>
    <image xlink:href="../W3C-SVG-1.1/resources/smiley.png" x="700" y="145" width="200" height="100" preserveAspectRatio="xMidYMid meet"/>
    <rect x="700" y="145" width="200" height="100" fill="none" stroke="green"/>
    <image xlink:href="../W3C-SVG-1.1/resources/smiley.png" x="700" y="265" width="200" height="100" preserveAspectRatio="xMaxYMax meet"/>
    <rect x="700" y="265" width="200" height="100" fill="none" stroke="green"/>
    
    <image xlink:href="../W3C-SVG-1.1/resources/smiley.png" x="300" y="250" width="100" height="200" preserveAspectRatio="xMinYMin slice"/>
    <rect x="300" y="250" width="100" height="200" fill="none" stroke="green"/>
    <image xlink:href="../W3C-SVG-1.1/resources/smiley.png" x="420" y="250" width="100" height="200" preserveAspectRatio="xMidYMid slice"/>
    <rect x="420" y="250" width="100" height="200" fill="none" stroke="green"/>
    <image xlink:href="../W3C-SVG-1.1/resources/smiley.png" x="540" y="250" width="100" height="200" preserveAspectRatio="xMaxYMax slice"/>
    <rect x="540" y="250" width="100" height="200" fill="none" stroke="green"/>

    <image xlink:href="../W3C-SVG-1.1/resources/smiley.png" x="950" y="25" width="200" height="100" preserveAspectRatio="xMinYMin slice"/>
    <rect x="950" y="25" width="200" height="100" fill="none" stroke="green"/>
    <image xlink:href="../W3C-SVG-1.1/resources/smiley.png" x="950" y="145" width="200" height="100" preserveAspectRatio="xMidYMid slice"/>
    <rect x="950" y="145" width="200" height="100" fill="none" stroke="green"/>
    <image xlink:href="../W3C-SVG-1.1/resources/smiley.png" x="950" y="265" width="200" height="100" preserveAspectRatio="xMaxYMax slice"/>
    <rect x="950" y="265" width="200" height="100" fill="none" stroke="green"/>
  </g>
</svg>