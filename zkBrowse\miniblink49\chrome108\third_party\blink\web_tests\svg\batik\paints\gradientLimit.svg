<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN"
"http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">

<!--

   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

-->
<!-- ========================================================================= -->
<!-- Limit test on gradients.                                                  -->
<!--                                                                           -->
<!-- <AUTHOR>                                                 -->
<!-- @version $Id: gradientLimit.svg 475685 2006-11-16 11:16:05Z cam $   -->
<!-- ========================================================================= -->
<?xml-stylesheet type="text/css" href="../resources/test.css" ?>  
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="body" width="450" height="500" viewBox="0 0 450 500">
    <title>Limit test on Gradients</title>

    <text x="50%" y="45" class="title">Gradient Limit Test</text>

    <g id="testContent">
        <g id="closeOffsetsLinear">
            <title>Close Offsets on linearGradient</title>
            <desc>The last two offsets of this linearGradient are 
                  very close (0.000001) to one-another</desc>
            <linearGradient id="closeOffsetsLinearGradient" gradientUnits="objectBoundingBox" y1="0" x1="0" x2="0" y2=".1">
              <stop  offset="0" style="stop-color:crimson"/>
              <stop  offset="0.999999" style="stop-color:gold"/>
              <stop  offset="1" style="stop-color:crimson"/>
            </linearGradient>
            <rect fill="url(#closeOffsetsLinearGradient)" x="35" y="80" width="80" height="40" />
            <text class="legend" x="75" y="135">Two close offsets</text>
            <text class="legend" x="75" y="150">&lt;linearGradient&gt;</text>
        </g>

        <g id="closeOffsetsLinear2" transform="translate(150, 0)">
            <title>Close Offsets on linearGradient</title>
            <desc>The last two offsets of this linearGradient are 
                  very close (0.00000001) to one-another, with a
                  difference below the single precision floating
                  point</desc>
            <linearGradient id="closeOffsetsLinearGradient2" gradientUnits="objectBoundingBox" y1="0" x1="0" x2="0" y2=".1">
              <stop  offset="0" style="stop-color:crimson"/>
              <stop  offset="0.99999999" style="stop-color:gold"/>
              <stop  offset="1" style="stop-color:crimson"/>
            </linearGradient>
            <rect fill="url(#closeOffsetsLinearGradient2)" x="35" y="80" width="80" height="40" />
            <text class="legend" x="75" y="135">Two very close offsets</text>
            <text class="legend" x="75" y="150">&lt;linearGradient&gt;</text>
        </g>

        <g id="closeOffsetsRadial" transform="translate(0, 100)">
            <title>Close Offsets on radialGradient</title>
            <desc>The last two offsets of this radialGradient are 
                  very close (0.000001) to one-another</desc>
            <radialGradient id="closeOffsetsRadialGradient" gradientUnits="objectBoundingBox" r=".1">
              <stop  offset="0" style="stop-color:crimson"/>
              <stop  offset="0.999999" style="stop-color:gold"/>
              <stop  offset="1" style="stop-color:crimson"/>
            </radialGradient>
            <rect fill="url(#closeOffsetsRadialGradient)" x="35" y="80" width="80" height="40" />
            <text class="legend" x="75" y="135">Two close offsets</text>
            <text class="legend" x="75" y="150">&lt;radialGradient&gt;</text>
        </g>

        <g id="closeOffsetsRadial2" transform="translate(150, 100)">
            <title>Close Offsets on radialGradient</title>
            <desc>The last two offsets of this radialGradient are 
                  very close (0.00000001) to one-another</desc>
            <radialGradient id="closeOffsetsRadialGradient2" gradientUnits="objectBoundingBox" r=".1">
              <stop  offset="0" style="stop-color:crimson"/>
              <stop  offset="0.99999999" style="stop-color:gold"/>
              <stop  offset="1" style="stop-color:crimson"/>
            </radialGradient>
            <rect fill="url(#closeOffsetsRadialGradient2)" x="35" y="80" width="80" height="40" />
            <text class="legend" x="75" y="135">Two very close offsets</text>
            <text class="legend" x="75" y="150">&lt;radialGradient&gt;</text>
        </g>

        <g id="closeControlsLinear" transform="translate(300, 0)">
            <title>Close control points on linearGradient</title>
            <desc>The y coordinates on the gradient are close
                  (0.00000001) to one-another</desc>
            <linearGradient id="closeControlsLinearGradient" gradientUnits="objectBoundingBox" x1="0" y1="0" x2="0" y2="0.00000001">
              <stop  offset="0" stop-color="gold"/>
              <stop  offset="1" stop-color="crimson"/>
            </linearGradient>
            <rect fill="url(#closeControlsLinearGradient)" x=".35" y=".8" width=".8" height=".4" transform="scale(100)"/>
            <text class="legend" x="75" y="135">Close control points</text>
            <text class="legend" x="75" y="150">&lt;linearGradient&gt;</text>
        </g>

        <g id="smallRadiusRadial" transform="translate(300, 100)">
            <title>Small radius on radialGradient</title>
            <desc>The radialGradient has a very small
                  (0.00000001) radius</desc>
            <radialGradient id="smallRadiusRadialGradient" gradientUnits="objectBoundingBox" r="0.00000001">
              <stop  offset="0" stop-color="gold"/>
              <stop  offset="1" stop-color="crimson"/>
            </radialGradient>
            <rect fill="url(#smallRadiusRadialGradient)" x=".35" y=".8" width=".8" height=".4" transform="scale(100)"/>
            <text class="legend" x="75" y="135">Small Radius</text>
            <text class="legend" x="75" y="150">&lt;radialGradient&gt;</text>
        </g>
    </g>

    <!-- ============================================================= -->
    <!-- Batik sample mark                                             -->
    <!-- ============================================================= -->
    <use xlink:href="../resources/batikLogo.svg#Batik_Tag_Box" />

</svg>
