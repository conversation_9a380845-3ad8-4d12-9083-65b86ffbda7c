# Copyright 2022 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/android/rules.gni")

android_library("java") {
  deps = [
    "//base:jni_java",
    "//components/autofill/android:autofill_payments_java_resources",
    "//components/autofill/android:main_autofill_java",
    "//components/browser_ui/bottomsheet/android:java",
    "//components/payments/content/android:java_resources",
    "//url:gurl_java",
  ]
  sources = [
    "java/src/org/chromium/chrome/browser/ui/fast_checkout/FastCheckoutComponent.java",
    "java/src/org/chromium/chrome/browser/ui/fast_checkout/data/FastCheckoutAutofillProfile.java",
    "java/src/org/chromium/chrome/browser/ui/fast_checkout/data/FastCheckoutCreditCard.java",
  ]
  resources_package = "org.chromium.chrome.browser.ui.fast_checkout"
}

generate_jni("jni_headers") {
  sources = [
    "internal/java/src/org/chromium/chrome/browser/ui/fast_checkout/FastCheckoutBridge.java",
    "java/src/org/chromium/chrome/browser/ui/fast_checkout/data/FastCheckoutAutofillProfile.java",
    "java/src/org/chromium/chrome/browser/ui/fast_checkout/data/FastCheckoutCreditCard.java",
  ]
}
