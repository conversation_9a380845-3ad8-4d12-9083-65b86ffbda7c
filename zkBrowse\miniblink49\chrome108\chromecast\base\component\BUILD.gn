# Copyright 2016 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//chromecast/chromecast.gni")
import("//testing/test.gni")

cast_source_set("component") {
  sources = [
    "component.cc",
    "component.h",
    "component_internal.h",
  ]

  deps = [ "//base" ]
}

test("cast_component_unittests") {
  sources = [ "component_unittest.cc" ]

  deps = [
    ":component",
    "//base",
    "//base/test:run_all_unittests",
    "//base/test:test_support",
    "//testing/gtest",
  ]
}
