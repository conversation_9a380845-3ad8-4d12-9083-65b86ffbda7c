<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background applied to elements with 'display' set to 'table-caption'</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="G<PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-12-14 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <link rel="match" href="../reference/ref-filled-black-96px-square.xht" />

        <meta name="assert" content="The 'background' property applies to elements with 'display' set to 'table-caption'." />
        <style type="text/css">
            #table
            {
                display: table;
            }
            #caption
            {
                background: black;
                display: table-caption;
                height: 1in;
                width: 1in;
            }
            #row
            {
                display: table-row;
            }
            #cell
            {
                display: table-cell;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is a filled black square.</p>
        <div id="table">
            <div id="caption"></div>
            <div id="row">
                <div id="cell"></div>
            </div>
        </div>
    </body>
</html>