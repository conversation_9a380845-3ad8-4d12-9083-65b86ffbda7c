// Copyright 2018 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

module audio.mojom;

import "mojo/public/mojom/base/file.mojom";

enum DebugRecordingStreamType {
  kInput = 0,
  kOutput = 1,
};

// Implemented by the browser process to create audio debug recording files on
// behalf of the sandboxed audio service's DebugRecording interface. The created
// files are scoped inside a directory controlled by the browser process, and
// only the file handle is returned to the audio service.
interface DebugRecordingFileProvider {

  // Creates a file at the base file path, with appended extensions ".output" or
  // ".input" (depending on |stream_type|), ".|id|" and ".wav".
  // E.g.: from base file path "/path/base_name", stream type kOutput, and id =
  // 1, "/path/base_name.output.1.wav" will be created.
  // Returns invalid file in case of failure.
  CreateWavFile(DebugRecordingStreamType stream_type, uint32 id)
  => (mojo_base.mojom.File? file);

  // Creates a file at the base file path, with appended extensions ".|id|" and
  // ".aecdump".
  // E.g.: from base file path "/path/base_name" and id = 1,
  // "/path/base_name.1.aecdump" will be created.
  // Returns invalid file in case of failure.
  CreateAecdumpFile(uint32 id)
  => (mojo_base.mojom.File? file);
};

// Controls audio debug recording. Hosted in the audio service, called by the
// browser process. To enable, bind interface and call Enable with a bound
// DebugRecordingFileProvider interface pointer. To disable, close debug
// recording message pipe or bind a new DebugRecording to the audio service.
interface DebugRecording {
  Enable(pending_remote<DebugRecordingFileProvider> file_provider);
};
