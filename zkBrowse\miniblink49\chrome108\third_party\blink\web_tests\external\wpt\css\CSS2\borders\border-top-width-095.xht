<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Border-top-width set to inherit</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="G<PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-06-24 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#propdef-border-top-width" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#border-width-properties" />
		<link rel="match" href="border-top-width-095-ref.xht" />

        <meta name="assert" content="The 'border-top-width' property supports a value of inherit and gets its computed value from its parent." />
        <style type="text/css">
            body
            {
                border-top-color: transparent;
                border-top-style: solid;
                border-top-width: 1in;
                padding-top: 20px;
            }
            div
            {
                border-top-color: black;
                border-top-style: solid;
                border-top-width: inherit;
                width: 1in;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is a filled black square.</p>
        <div></div>
    </body>
</html>