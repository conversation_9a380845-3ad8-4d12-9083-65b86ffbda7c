# This is a generated file.
# Full coverage: 59%, with partial coverage: 80%
create_shortcut_Standalone_Windowed🌕	launch_from_menu_option_Standalone🌕	check_app_title_Standalone_StandaloneOriginal🌑
create_shortcut_Standalone_Windowed🌕	launch_from_launch_icon_Standalone🌕	check_app_title_Standalone_StandaloneOriginal🌑
create_shortcut_Standalone_Windowed🌕	launch_from_chrome_apps_Standalone🌓	check_app_title_Standalone_StandaloneOriginal🌑
create_shortcut_Standalone_Windowed🌕	launch_from_platform_shortcut_Standalone🌓	check_app_title_Standalone_StandaloneOriginal🌑
install_omnibox_icon_Standalone🌕	launch_from_menu_option_Standalone🌕	check_app_title_Standalone_StandaloneOriginal🌑
install_omnibox_icon_Standalone🌕	launch_from_launch_icon_Standalone🌕	check_app_title_Standalone_StandaloneOriginal🌑
install_omnibox_icon_Standalone🌕	launch_from_chrome_apps_Standalone🌓	check_app_title_Standalone_StandaloneOriginal🌑
install_omnibox_icon_Standalone🌕	launch_from_platform_shortcut_Standalone🌓	check_app_title_Standalone_StandaloneOriginal🌑
install_menu_option_Standalone🌕	launch_from_menu_option_Standalone🌕	check_app_title_Standalone_StandaloneOriginal🌑
install_menu_option_Standalone🌕	launch_from_launch_icon_Standalone🌕	check_app_title_Standalone_StandaloneOriginal🌑
install_menu_option_Standalone🌕	launch_from_chrome_apps_Standalone🌓	check_app_title_Standalone_StandaloneOriginal🌑
install_menu_option_Standalone🌕	launch_from_platform_shortcut_Standalone🌓	check_app_title_Standalone_StandaloneOriginal🌑
create_shortcut_Standalone_Windowed🌕	manifest_update_title_Standalone_StandaloneUpdated🌑	accept_app_id_update_dialog🌑	await_manifest_update_Standalone🌑	launch_from_menu_option_Standalone🌑	check_app_title_Standalone_StandaloneUpdated🌑
create_shortcut_Standalone_Windowed🌕	manifest_update_title_Standalone_StandaloneUpdated🌑	accept_app_id_update_dialog🌑	await_manifest_update_Standalone🌑	launch_from_launch_icon_Standalone🌑	check_app_title_Standalone_StandaloneUpdated🌑
create_shortcut_Standalone_Windowed🌕	manifest_update_title_Standalone_StandaloneUpdated🌑	accept_app_id_update_dialog🌑	await_manifest_update_Standalone🌑	launch_from_chrome_apps_Standalone🌑	check_app_title_Standalone_StandaloneUpdated🌑
create_shortcut_Standalone_Windowed🌕	manifest_update_title_Standalone_StandaloneUpdated🌑	accept_app_id_update_dialog🌑	await_manifest_update_Standalone🌑	launch_from_platform_shortcut_Standalone🌑	check_app_title_Standalone_StandaloneUpdated🌑
install_omnibox_icon_Standalone🌕	manifest_update_title_Standalone_StandaloneUpdated🌑	accept_app_id_update_dialog🌑	await_manifest_update_Standalone🌑	launch_from_menu_option_Standalone🌑	check_app_title_Standalone_StandaloneUpdated🌑
install_omnibox_icon_Standalone🌕	manifest_update_title_Standalone_StandaloneUpdated🌑	accept_app_id_update_dialog🌑	await_manifest_update_Standalone🌑	launch_from_launch_icon_Standalone🌑	check_app_title_Standalone_StandaloneUpdated🌑
install_omnibox_icon_Standalone🌕	manifest_update_title_Standalone_StandaloneUpdated🌑	accept_app_id_update_dialog🌑	await_manifest_update_Standalone🌑	launch_from_chrome_apps_Standalone🌑	check_app_title_Standalone_StandaloneUpdated🌑
install_omnibox_icon_Standalone🌕	manifest_update_title_Standalone_StandaloneUpdated🌑	accept_app_id_update_dialog🌑	await_manifest_update_Standalone🌑	launch_from_platform_shortcut_Standalone🌑	check_app_title_Standalone_StandaloneUpdated🌑
install_menu_option_Standalone🌕	manifest_update_title_Standalone_StandaloneUpdated🌑	accept_app_id_update_dialog🌑	await_manifest_update_Standalone🌑	launch_from_menu_option_Standalone🌑	check_app_title_Standalone_StandaloneUpdated🌑
install_menu_option_Standalone🌕	manifest_update_title_Standalone_StandaloneUpdated🌑	accept_app_id_update_dialog🌑	await_manifest_update_Standalone🌑	launch_from_launch_icon_Standalone🌑	check_app_title_Standalone_StandaloneUpdated🌑
install_menu_option_Standalone🌕	manifest_update_title_Standalone_StandaloneUpdated🌑	accept_app_id_update_dialog🌑	await_manifest_update_Standalone🌑	launch_from_chrome_apps_Standalone🌑	check_app_title_Standalone_StandaloneUpdated🌑
install_menu_option_Standalone🌕	manifest_update_title_Standalone_StandaloneUpdated🌑	accept_app_id_update_dialog🌑	await_manifest_update_Standalone🌑	launch_from_platform_shortcut_Standalone🌑	check_app_title_Standalone_StandaloneUpdated🌑
create_shortcut_Standalone_Windowed🌕	manifest_update_title_Standalone_StandaloneUpdated🌑	deny_app_update_dialog🌑	check_app_not_in_list_Standalone🌑	check_platform_shortcut_not_exists_Standalone🌑
install_omnibox_icon_Standalone🌕	manifest_update_title_Standalone_StandaloneUpdated🌑	deny_app_update_dialog🌑	check_app_not_in_list_Standalone🌑	check_platform_shortcut_not_exists_Standalone🌑
install_menu_option_Standalone🌕	manifest_update_title_Standalone_StandaloneUpdated🌑	deny_app_update_dialog🌑	check_app_not_in_list_Standalone🌑	check_platform_shortcut_not_exists_Standalone🌑
create_shortcut_Standalone_Windowed🌕	manifest_update_icon_Standalone🌑	await_manifest_update_Standalone🌑	check_app_in_list_icon_correct_Standalone🌑
install_omnibox_icon_Standalone🌕	manifest_update_icon_Standalone🌑	await_manifest_update_Standalone🌑	check_app_in_list_icon_correct_Standalone🌑
install_menu_option_Standalone🌕	manifest_update_icon_Standalone🌑	await_manifest_update_Standalone🌑	check_app_in_list_icon_correct_Standalone🌑
create_shortcut_Standalone_Windowed🌕	manifest_update_icon_Standalone🌑	await_manifest_update_Standalone🌑	check_platform_shortcut_and_icon_Standalone🌑
install_omnibox_icon_Standalone🌕	manifest_update_icon_Standalone🌑	await_manifest_update_Standalone🌑	check_platform_shortcut_and_icon_Standalone🌑
install_menu_option_Standalone🌕	manifest_update_icon_Standalone🌑	await_manifest_update_Standalone🌑	check_platform_shortcut_and_icon_Standalone🌑
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	manifest_update_title_Standalone_StandaloneUpdated🌑	check_update_dialog_not_shown🌑	await_manifest_update_Standalone🌑	launch_from_menu_option_Standalone🌑	check_app_title_Standalone_StandaloneUpdated🌑
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	manifest_update_title_Standalone_StandaloneUpdated🌑	check_update_dialog_not_shown🌑	await_manifest_update_Standalone🌑	launch_from_launch_icon_Standalone🌑	check_app_title_Standalone_StandaloneUpdated🌑
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	manifest_update_title_Standalone_StandaloneUpdated🌑	check_update_dialog_not_shown🌑	await_manifest_update_Standalone🌑	launch_from_chrome_apps_Standalone🌑	check_app_title_Standalone_StandaloneUpdated🌑
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	manifest_update_title_Standalone_StandaloneUpdated🌑	check_update_dialog_not_shown🌑	await_manifest_update_Standalone🌑	launch_from_platform_shortcut_Standalone🌑	check_app_title_Standalone_StandaloneUpdated🌑
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	manifest_update_title_Standalone_StandaloneUpdated🌑	check_update_dialog_not_shown🌑	await_manifest_update_Standalone🌑	launch_from_menu_option_Standalone🌑	check_app_title_Standalone_StandaloneUpdated🌑
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	manifest_update_title_Standalone_StandaloneUpdated🌑	check_update_dialog_not_shown🌑	await_manifest_update_Standalone🌑	launch_from_launch_icon_Standalone🌑	check_app_title_Standalone_StandaloneUpdated🌑
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	manifest_update_title_Standalone_StandaloneUpdated🌑	check_update_dialog_not_shown🌑	await_manifest_update_Standalone🌑	launch_from_chrome_apps_Standalone🌑	check_app_title_Standalone_StandaloneUpdated🌑
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	manifest_update_title_Standalone_StandaloneUpdated🌑	check_update_dialog_not_shown🌑	await_manifest_update_Standalone🌑	launch_from_platform_shortcut_Standalone🌑	check_app_title_Standalone_StandaloneUpdated🌑
create_shortcut_Standalone_Windowed🌕	apply_run_on_os_login_policy_blocked_Standalone🌕	check_user_cannot_set_run_on_os_login_Standalone🌕
create_shortcut_Standalone_Browser🌕	apply_run_on_os_login_policy_blocked_Standalone🌕	check_user_cannot_set_run_on_os_login_Standalone🌕
install_omnibox_icon_Standalone🌕	apply_run_on_os_login_policy_blocked_Standalone🌕	check_user_cannot_set_run_on_os_login_Standalone🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	apply_run_on_os_login_policy_blocked_Standalone🌕	check_user_cannot_set_run_on_os_login_Standalone🌕
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	apply_run_on_os_login_policy_blocked_Standalone🌕	check_user_cannot_set_run_on_os_login_Standalone🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	apply_run_on_os_login_policy_blocked_Standalone🌕	check_user_cannot_set_run_on_os_login_Standalone🌕
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	apply_run_on_os_login_policy_blocked_Standalone🌕	check_user_cannot_set_run_on_os_login_Standalone🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	apply_run_on_os_login_policy_blocked_Standalone🌕	check_user_cannot_set_run_on_os_login_Standalone🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	apply_run_on_os_login_policy_blocked_Standalone🌕	check_user_cannot_set_run_on_os_login_Standalone🌕
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	apply_run_on_os_login_policy_blocked_Standalone🌕	check_user_cannot_set_run_on_os_login_Standalone🌕
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	apply_run_on_os_login_policy_blocked_Standalone🌕	check_user_cannot_set_run_on_os_login_Standalone🌕
install_menu_option_Standalone🌕	apply_run_on_os_login_policy_blocked_Standalone🌕	check_user_cannot_set_run_on_os_login_Standalone🌕
create_shortcut_Standalone_Windowed🌕	enable_run_on_os_login_Standalone🌕	apply_run_on_os_login_policy_blocked_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
create_shortcut_Standalone_Browser🌕	enable_run_on_os_login_Standalone🌕	apply_run_on_os_login_policy_blocked_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_omnibox_icon_Standalone🌕	enable_run_on_os_login_Standalone🌕	apply_run_on_os_login_policy_blocked_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	enable_run_on_os_login_Standalone🌕	apply_run_on_os_login_policy_blocked_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	enable_run_on_os_login_Standalone🌕	apply_run_on_os_login_policy_blocked_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	enable_run_on_os_login_Standalone🌕	apply_run_on_os_login_policy_blocked_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	enable_run_on_os_login_Standalone🌕	apply_run_on_os_login_policy_blocked_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	enable_run_on_os_login_Standalone🌕	apply_run_on_os_login_policy_blocked_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	enable_run_on_os_login_Standalone🌕	apply_run_on_os_login_policy_blocked_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	enable_run_on_os_login_Standalone🌕	apply_run_on_os_login_policy_blocked_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	enable_run_on_os_login_Standalone🌕	apply_run_on_os_login_policy_blocked_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_menu_option_Standalone🌕	enable_run_on_os_login_Standalone🌕	apply_run_on_os_login_policy_blocked_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
create_shortcut_Standalone_Windowed🌕	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_run_on_os_login_enabled_Standalone🌕
create_shortcut_Standalone_Browser🌕	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_run_on_os_login_enabled_Standalone🌕
install_omnibox_icon_Standalone🌕	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_run_on_os_login_enabled_Standalone🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_run_on_os_login_enabled_Standalone🌕
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_run_on_os_login_enabled_Standalone🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_run_on_os_login_enabled_Standalone🌕
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_run_on_os_login_enabled_Standalone🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_run_on_os_login_enabled_Standalone🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_run_on_os_login_enabled_Standalone🌕
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_run_on_os_login_enabled_Standalone🌕
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_run_on_os_login_enabled_Standalone🌕
install_menu_option_Standalone🌕	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_run_on_os_login_enabled_Standalone🌕
create_shortcut_Standalone_Windowed🌕	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_user_cannot_set_run_on_os_login_Standalone🌕
create_shortcut_Standalone_Browser🌕	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_user_cannot_set_run_on_os_login_Standalone🌕
install_omnibox_icon_Standalone🌕	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_user_cannot_set_run_on_os_login_Standalone🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_user_cannot_set_run_on_os_login_Standalone🌕
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_user_cannot_set_run_on_os_login_Standalone🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_user_cannot_set_run_on_os_login_Standalone🌕
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_user_cannot_set_run_on_os_login_Standalone🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_user_cannot_set_run_on_os_login_Standalone🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_user_cannot_set_run_on_os_login_Standalone🌕
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_user_cannot_set_run_on_os_login_Standalone🌕
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_user_cannot_set_run_on_os_login_Standalone🌕
install_menu_option_Standalone🌕	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_user_cannot_set_run_on_os_login_Standalone🌕
create_shortcut_Standalone_Windowed🌕	enable_run_on_os_login_Standalone🌕	check_run_on_os_login_enabled_Standalone🌕
create_shortcut_Standalone_Browser🌕	enable_run_on_os_login_Standalone🌕	check_run_on_os_login_enabled_Standalone🌕
install_omnibox_icon_Standalone🌕	enable_run_on_os_login_Standalone🌕	check_run_on_os_login_enabled_Standalone🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	enable_run_on_os_login_Standalone🌕	check_run_on_os_login_enabled_Standalone🌕
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	enable_run_on_os_login_Standalone🌕	check_run_on_os_login_enabled_Standalone🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	enable_run_on_os_login_Standalone🌕	check_run_on_os_login_enabled_Standalone🌕
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	enable_run_on_os_login_Standalone🌕	check_run_on_os_login_enabled_Standalone🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	enable_run_on_os_login_Standalone🌕	check_run_on_os_login_enabled_Standalone🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	enable_run_on_os_login_Standalone🌕	check_run_on_os_login_enabled_Standalone🌕
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	enable_run_on_os_login_Standalone🌕	check_run_on_os_login_enabled_Standalone🌕
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	enable_run_on_os_login_Standalone🌕	check_run_on_os_login_enabled_Standalone🌕
install_menu_option_Standalone🌕	enable_run_on_os_login_Standalone🌕	check_run_on_os_login_enabled_Standalone🌕
create_shortcut_Standalone_Windowed🌕	enable_run_on_os_login_Standalone🌕	disable_run_on_os_login_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
create_shortcut_Standalone_Browser🌕	enable_run_on_os_login_Standalone🌕	disable_run_on_os_login_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_omnibox_icon_Standalone🌕	enable_run_on_os_login_Standalone🌕	disable_run_on_os_login_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	enable_run_on_os_login_Standalone🌕	disable_run_on_os_login_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	enable_run_on_os_login_Standalone🌕	disable_run_on_os_login_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	enable_run_on_os_login_Standalone🌕	disable_run_on_os_login_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	enable_run_on_os_login_Standalone🌕	disable_run_on_os_login_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	enable_run_on_os_login_Standalone🌕	disable_run_on_os_login_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	enable_run_on_os_login_Standalone🌕	disable_run_on_os_login_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	enable_run_on_os_login_Standalone🌕	disable_run_on_os_login_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	enable_run_on_os_login_Standalone🌕	disable_run_on_os_login_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_menu_option_Standalone🌕	enable_run_on_os_login_Standalone🌕	disable_run_on_os_login_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
create_shortcut_Standalone_Windowed🌕	apply_run_on_os_login_policy_run_windowed_Standalone🌕	remove_run_on_os_login_policy_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
create_shortcut_Standalone_Browser🌕	apply_run_on_os_login_policy_run_windowed_Standalone🌕	remove_run_on_os_login_policy_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_omnibox_icon_Standalone🌕	apply_run_on_os_login_policy_run_windowed_Standalone🌕	remove_run_on_os_login_policy_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	apply_run_on_os_login_policy_run_windowed_Standalone🌕	remove_run_on_os_login_policy_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	apply_run_on_os_login_policy_run_windowed_Standalone🌕	remove_run_on_os_login_policy_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	apply_run_on_os_login_policy_run_windowed_Standalone🌕	remove_run_on_os_login_policy_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	apply_run_on_os_login_policy_run_windowed_Standalone🌕	remove_run_on_os_login_policy_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	apply_run_on_os_login_policy_run_windowed_Standalone🌕	remove_run_on_os_login_policy_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	apply_run_on_os_login_policy_run_windowed_Standalone🌕	remove_run_on_os_login_policy_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	apply_run_on_os_login_policy_run_windowed_Standalone🌕	remove_run_on_os_login_policy_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	apply_run_on_os_login_policy_run_windowed_Standalone🌕	remove_run_on_os_login_policy_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_menu_option_Standalone🌕	apply_run_on_os_login_policy_run_windowed_Standalone🌕	remove_run_on_os_login_policy_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
create_shortcut_Standalone_Windowed🌕	enable_run_on_os_login_Standalone🌕	apply_run_on_os_login_policy_blocked_Standalone🌕	remove_run_on_os_login_policy_Standalone🌕	check_run_on_os_login_enabled_Standalone🌕
create_shortcut_Standalone_Browser🌕	enable_run_on_os_login_Standalone🌕	apply_run_on_os_login_policy_blocked_Standalone🌕	remove_run_on_os_login_policy_Standalone🌕	check_run_on_os_login_enabled_Standalone🌕
install_omnibox_icon_Standalone🌕	enable_run_on_os_login_Standalone🌕	apply_run_on_os_login_policy_blocked_Standalone🌕	remove_run_on_os_login_policy_Standalone🌕	check_run_on_os_login_enabled_Standalone🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	enable_run_on_os_login_Standalone🌕	apply_run_on_os_login_policy_blocked_Standalone🌕	remove_run_on_os_login_policy_Standalone🌕	check_run_on_os_login_enabled_Standalone🌕
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	enable_run_on_os_login_Standalone🌕	apply_run_on_os_login_policy_blocked_Standalone🌕	remove_run_on_os_login_policy_Standalone🌕	check_run_on_os_login_enabled_Standalone🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	enable_run_on_os_login_Standalone🌕	apply_run_on_os_login_policy_blocked_Standalone🌕	remove_run_on_os_login_policy_Standalone🌕	check_run_on_os_login_enabled_Standalone🌕
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	enable_run_on_os_login_Standalone🌕	apply_run_on_os_login_policy_blocked_Standalone🌕	remove_run_on_os_login_policy_Standalone🌕	check_run_on_os_login_enabled_Standalone🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	enable_run_on_os_login_Standalone🌕	apply_run_on_os_login_policy_blocked_Standalone🌕	remove_run_on_os_login_policy_Standalone🌕	check_run_on_os_login_enabled_Standalone🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	enable_run_on_os_login_Standalone🌕	apply_run_on_os_login_policy_blocked_Standalone🌕	remove_run_on_os_login_policy_Standalone🌕	check_run_on_os_login_enabled_Standalone🌕
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	enable_run_on_os_login_Standalone🌕	apply_run_on_os_login_policy_blocked_Standalone🌕	remove_run_on_os_login_policy_Standalone🌕	check_run_on_os_login_enabled_Standalone🌕
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	enable_run_on_os_login_Standalone🌕	apply_run_on_os_login_policy_blocked_Standalone🌕	remove_run_on_os_login_policy_Standalone🌕	check_run_on_os_login_enabled_Standalone🌕
install_menu_option_Standalone🌕	enable_run_on_os_login_Standalone🌕	apply_run_on_os_login_policy_blocked_Standalone🌕	remove_run_on_os_login_policy_Standalone🌕	check_run_on_os_login_enabled_Standalone🌕
create_shortcut_Standalone_Windowed🌕	switch_profile_clients_Client2🌕	switch_profile_clients_Client1🌕	sync_turn_off🌕	uninstall_from_list_Standalone🌕	switch_profile_clients_Client2🌕	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
create_shortcut_Standalone_Windowed🌕	switch_profile_clients_Client2🌕	switch_profile_clients_Client1🌕	sync_turn_off🌕	uninstall_from_menu_Standalone🌕	switch_profile_clients_Client2🌕	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
create_shortcut_Standalone_Windowed🌕	switch_profile_clients_Client2🌕	switch_profile_clients_Client1🌕	sync_turn_off🌕	uninstall_from_os_Standalone🌕	switch_profile_clients_Client2🌕	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
create_shortcut_Standalone_Windowed🌕	switch_profile_clients_Client2🌕	switch_profile_clients_Client1🌕	sync_turn_off🌕	uninstall_from_app_settings_Standalone🌕	switch_profile_clients_Client2🌕	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_omnibox_icon_Standalone🌕	switch_profile_clients_Client2🌕	switch_profile_clients_Client1🌕	sync_turn_off🌕	uninstall_from_list_Standalone🌕	switch_profile_clients_Client2🌕	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_omnibox_icon_Standalone🌕	switch_profile_clients_Client2🌕	switch_profile_clients_Client1🌕	sync_turn_off🌕	uninstall_from_menu_Standalone🌕	switch_profile_clients_Client2🌕	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_omnibox_icon_Standalone🌕	switch_profile_clients_Client2🌕	switch_profile_clients_Client1🌕	sync_turn_off🌕	uninstall_from_os_Standalone🌕	switch_profile_clients_Client2🌕	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_omnibox_icon_Standalone🌕	switch_profile_clients_Client2🌕	switch_profile_clients_Client1🌕	sync_turn_off🌕	uninstall_from_app_settings_Standalone🌕	switch_profile_clients_Client2🌕	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_menu_option_Standalone🌕	switch_profile_clients_Client2🌕	switch_profile_clients_Client1🌕	sync_turn_off🌕	uninstall_from_list_Standalone🌕	switch_profile_clients_Client2🌕	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_menu_option_Standalone🌕	switch_profile_clients_Client2🌕	switch_profile_clients_Client1🌕	sync_turn_off🌕	uninstall_from_menu_Standalone🌕	switch_profile_clients_Client2🌕	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_menu_option_Standalone🌕	switch_profile_clients_Client2🌕	switch_profile_clients_Client1🌕	sync_turn_off🌕	uninstall_from_os_Standalone🌕	switch_profile_clients_Client2🌕	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
install_menu_option_Standalone🌕	switch_profile_clients_Client2🌕	switch_profile_clients_Client1🌕	sync_turn_off🌕	uninstall_from_app_settings_Standalone🌕	switch_profile_clients_Client2🌕	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕
create_shortcut_Standalone_Windowed🌕	switch_profile_clients_Client2🌕	switch_profile_clients_Client1🌕	sync_turn_off🌕	uninstall_from_list_Standalone🌕	switch_profile_clients_Client2🌕	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕	install_locally_Standalone🌓	check_run_on_os_login_enabled_Standalone🌕
create_shortcut_Standalone_Windowed🌕	switch_profile_clients_Client2🌕	switch_profile_clients_Client1🌕	sync_turn_off🌕	uninstall_from_menu_Standalone🌕	switch_profile_clients_Client2🌕	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕	install_locally_Standalone🌓	check_run_on_os_login_enabled_Standalone🌕
create_shortcut_Standalone_Windowed🌕	switch_profile_clients_Client2🌕	switch_profile_clients_Client1🌕	sync_turn_off🌕	uninstall_from_os_Standalone🌕	switch_profile_clients_Client2🌕	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕	install_locally_Standalone🌓	check_run_on_os_login_enabled_Standalone🌕
create_shortcut_Standalone_Windowed🌕	switch_profile_clients_Client2🌕	switch_profile_clients_Client1🌕	sync_turn_off🌕	uninstall_from_app_settings_Standalone🌕	switch_profile_clients_Client2🌕	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕	install_locally_Standalone🌓	check_run_on_os_login_enabled_Standalone🌕
install_omnibox_icon_Standalone🌕	switch_profile_clients_Client2🌕	switch_profile_clients_Client1🌕	sync_turn_off🌕	uninstall_from_list_Standalone🌕	switch_profile_clients_Client2🌕	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕	install_locally_Standalone🌓	check_run_on_os_login_enabled_Standalone🌕
install_omnibox_icon_Standalone🌕	switch_profile_clients_Client2🌕	switch_profile_clients_Client1🌕	sync_turn_off🌕	uninstall_from_menu_Standalone🌕	switch_profile_clients_Client2🌕	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕	install_locally_Standalone🌓	check_run_on_os_login_enabled_Standalone🌕
install_omnibox_icon_Standalone🌕	switch_profile_clients_Client2🌕	switch_profile_clients_Client1🌕	sync_turn_off🌕	uninstall_from_os_Standalone🌕	switch_profile_clients_Client2🌕	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕	install_locally_Standalone🌓	check_run_on_os_login_enabled_Standalone🌕
install_omnibox_icon_Standalone🌕	switch_profile_clients_Client2🌕	switch_profile_clients_Client1🌕	sync_turn_off🌕	uninstall_from_app_settings_Standalone🌕	switch_profile_clients_Client2🌕	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕	install_locally_Standalone🌓	check_run_on_os_login_enabled_Standalone🌕
install_menu_option_Standalone🌕	switch_profile_clients_Client2🌕	switch_profile_clients_Client1🌕	sync_turn_off🌕	uninstall_from_list_Standalone🌕	switch_profile_clients_Client2🌕	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕	install_locally_Standalone🌓	check_run_on_os_login_enabled_Standalone🌕
install_menu_option_Standalone🌕	switch_profile_clients_Client2🌕	switch_profile_clients_Client1🌕	sync_turn_off🌕	uninstall_from_menu_Standalone🌕	switch_profile_clients_Client2🌕	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕	install_locally_Standalone🌓	check_run_on_os_login_enabled_Standalone🌕
install_menu_option_Standalone🌕	switch_profile_clients_Client2🌕	switch_profile_clients_Client1🌕	sync_turn_off🌕	uninstall_from_os_Standalone🌕	switch_profile_clients_Client2🌕	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕	install_locally_Standalone🌓	check_run_on_os_login_enabled_Standalone🌕
install_menu_option_Standalone🌕	switch_profile_clients_Client2🌕	switch_profile_clients_Client1🌕	sync_turn_off🌕	uninstall_from_app_settings_Standalone🌕	switch_profile_clients_Client2🌕	apply_run_on_os_login_policy_run_windowed_Standalone🌕	check_run_on_os_login_disabled_Standalone🌕	install_locally_Standalone🌓	check_run_on_os_login_enabled_Standalone🌕
create_shortcut_Standalone_Windowed🌕	set_app_badge_Standalone🌑	clear_app_badge_Standalone🌑	check_app_badge_empty_Standalone🌑
install_omnibox_icon_Standalone🌕	set_app_badge_Standalone🌑	clear_app_badge_Standalone🌑	check_app_badge_empty_Standalone🌑
install_menu_option_Standalone🌕	set_app_badge_Standalone🌑	clear_app_badge_Standalone🌑	check_app_badge_empty_Standalone🌑
create_shortcut_Standalone_Windowed🌕	set_app_badge_Standalone🌑	check_app_badge_has_value_Standalone🌑
install_omnibox_icon_Standalone🌕	set_app_badge_Standalone🌑	check_app_badge_has_value_Standalone🌑
install_menu_option_Standalone🌕	set_app_badge_Standalone🌑	check_app_badge_has_value_Standalone🌑
navigate_browser_Standalone🌕	set_app_badge_Standalone🌑	check_platform_shortcut_not_exists_Standalone🌑
create_shortcut_Standalone_Windowed🌕	navigate_pwa_Standalone_MinimalUi🌕	close_custom_toolbar🌕	check_app_navigation_is_start_url🌕
install_omnibox_icon_Standalone🌕	navigate_pwa_Standalone_MinimalUi🌕	close_custom_toolbar🌕	check_app_navigation_is_start_url🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	navigate_pwa_Standalone_MinimalUi🌕	close_custom_toolbar🌕	check_app_navigation_is_start_url🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	navigate_pwa_Standalone_MinimalUi🌕	close_custom_toolbar🌕	check_app_navigation_is_start_url🌕
install_menu_option_Standalone🌕	navigate_pwa_Standalone_MinimalUi🌕	close_custom_toolbar🌕	check_app_navigation_is_start_url🌕
create_shortcut_Standalone_Windowed🌕	navigate_pwa_Standalone_MinimalUi🌕	check_custom_toolbar🌕
install_omnibox_icon_Standalone🌕	navigate_pwa_Standalone_MinimalUi🌕	check_custom_toolbar🌕
install_menu_option_Standalone🌕	navigate_pwa_Standalone_MinimalUi🌕	check_custom_toolbar🌕
navigate_browser_Standalone🌕	check_app_not_in_list_Standalone🌓
navigate_browser_Standalone🌕	check_platform_shortcut_not_exists_Standalone🌑
navigate_browser_NotPromotable🌕	check_app_not_in_list_Standalone🌓
navigate_browser_NotPromotable🌕	check_platform_shortcut_not_exists_Standalone🌑
create_shortcut_Standalone_Windowed🌕	check_app_title_Standalone_StandaloneUpdated🌑
create_shortcut_Standalone_Browser🌕	check_app_title_Standalone_StandaloneUpdated🌑
install_omnibox_icon_Standalone🌕	check_app_title_Standalone_StandaloneUpdated🌑
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	check_app_title_Standalone_StandaloneUpdated🌑
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	check_app_title_Standalone_StandaloneUpdated🌑
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	check_app_title_Standalone_StandaloneUpdated🌑
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	check_app_title_Standalone_StandaloneUpdated🌑
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	check_app_title_Standalone_StandaloneUpdated🌑
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	check_app_title_Standalone_StandaloneUpdated🌑
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	check_app_title_Standalone_StandaloneUpdated🌑
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	check_app_title_Standalone_StandaloneUpdated🌑
install_menu_option_Standalone🌕	check_app_title_Standalone_StandaloneUpdated🌑
install_omnibox_icon_Screenshots🌕
create_shortcut_Standalone_Windowed🌕	check_window_created🌕
install_omnibox_icon_Standalone🌕	check_window_created🌕
install_menu_option_Standalone🌕	check_window_created🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	check_platform_shortcut_not_exists_Standalone🌑
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	check_platform_shortcut_not_exists_Standalone🌑
install_policy_app_NotPromotable_NoShortcut_Windowed_WebApp🌓	check_platform_shortcut_not_exists_NotPromotable🌑
install_policy_app_NotPromotable_NoShortcut_Browser_WebApp🌓	check_platform_shortcut_not_exists_NotPromotable🌑
create_shortcut_Standalone_Browser🌕	check_app_in_list_tabbed_Standalone🌓
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	check_app_in_list_tabbed_Standalone🌓
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	check_app_in_list_tabbed_Standalone🌓
create_shortcut_Standalone_Browser🌕	navigate_browser_Standalone🌕	check_create_shortcut_shown🌑
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	navigate_browser_Standalone🌕	check_create_shortcut_shown🌑
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	navigate_browser_Standalone🌕	check_create_shortcut_shown🌑
create_shortcut_Standalone_Browser🌕	navigate_browser_Standalone🌕	check_install_icon_shown🌕
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	navigate_browser_Standalone🌕	check_install_icon_shown🌕
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	navigate_browser_Standalone🌕	check_install_icon_shown🌕
create_shortcut_Standalone_Browser🌕	navigate_browser_Standalone🌕	check_launch_icon_not_shown🌕
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	navigate_browser_Standalone🌕	check_launch_icon_not_shown🌕
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	navigate_browser_Standalone🌕	check_launch_icon_not_shown🌕
create_shortcut_NotPromotable_Browser🌕	check_app_in_list_tabbed_NotPromotable🌓
install_policy_app_NotPromotable_WithShortcut_Browser_WebApp🌓	check_app_in_list_tabbed_NotPromotable🌓
install_policy_app_NotPromotable_NoShortcut_Browser_WebApp🌓	check_app_in_list_tabbed_NotPromotable🌓
create_shortcut_NotPromotable_Browser🌕	navigate_browser_NotPromotable🌕	check_create_shortcut_shown🌑
install_policy_app_NotPromotable_WithShortcut_Browser_WebApp🌓	navigate_browser_NotPromotable🌕	check_create_shortcut_shown🌑
install_policy_app_NotPromotable_NoShortcut_Browser_WebApp🌓	navigate_browser_NotPromotable🌕	check_create_shortcut_shown🌑
create_shortcut_NotPromotable_Browser🌕	navigate_browser_NotPromotable🌕	check_install_icon_not_shown🌕
install_policy_app_NotPromotable_WithShortcut_Browser_WebApp🌓	navigate_browser_NotPromotable🌕	check_install_icon_not_shown🌕
install_policy_app_NotPromotable_NoShortcut_Browser_WebApp🌓	navigate_browser_NotPromotable🌕	check_install_icon_not_shown🌕
create_shortcut_NotPromotable_Browser🌕	navigate_browser_NotPromotable🌕	check_launch_icon_not_shown🌕
install_policy_app_NotPromotable_WithShortcut_Browser_WebApp🌓	navigate_browser_NotPromotable🌕	check_launch_icon_not_shown🌕
install_policy_app_NotPromotable_NoShortcut_Browser_WebApp🌓	navigate_browser_NotPromotable🌕	check_launch_icon_not_shown🌕
create_shortcut_Standalone_Windowed🌕	check_app_in_list_windowed_Standalone🌓
install_omnibox_icon_Standalone🌕	check_app_in_list_windowed_Standalone🌓
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	check_app_in_list_windowed_Standalone🌓
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	check_app_in_list_windowed_Standalone🌓
install_menu_option_Standalone🌕	check_app_in_list_windowed_Standalone🌓
create_shortcut_Standalone_Windowed🌕	navigate_browser_Standalone🌕	check_create_shortcut_not_shown🌑
install_omnibox_icon_Standalone🌕	navigate_browser_Standalone🌕	check_create_shortcut_not_shown🌑
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	navigate_browser_Standalone🌕	check_create_shortcut_not_shown🌑
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	navigate_browser_Standalone🌕	check_create_shortcut_not_shown🌑
install_menu_option_Standalone🌕	navigate_browser_Standalone🌕	check_create_shortcut_not_shown🌑
create_shortcut_Standalone_Windowed🌕	navigate_browser_Standalone🌕	check_install_icon_not_shown🌕
install_omnibox_icon_Standalone🌕	navigate_browser_Standalone🌕	check_install_icon_not_shown🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	navigate_browser_Standalone🌕	check_install_icon_not_shown🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	navigate_browser_Standalone🌕	check_install_icon_not_shown🌕
install_menu_option_Standalone🌕	navigate_browser_Standalone🌕	check_install_icon_not_shown🌕
create_shortcut_Standalone_Windowed🌕	navigate_browser_Standalone🌕	check_launch_icon_shown🌕
install_omnibox_icon_Standalone🌕	navigate_browser_Standalone🌕	check_launch_icon_shown🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	navigate_browser_Standalone🌕	check_launch_icon_shown🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	navigate_browser_Standalone🌕	check_launch_icon_shown🌕
install_menu_option_Standalone🌕	navigate_browser_Standalone🌕	check_launch_icon_shown🌕
create_shortcut_MinimalUi_Windowed🌕	navigate_browser_MinimalUi🌕	check_launch_icon_shown🌕
install_omnibox_icon_MinimalUi🌕	navigate_browser_MinimalUi🌕	check_launch_icon_shown🌕
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	navigate_browser_MinimalUi🌕	check_launch_icon_shown🌕
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	navigate_browser_MinimalUi🌕	check_launch_icon_shown🌕
install_menu_option_MinimalUi🌕	navigate_browser_MinimalUi🌕	check_launch_icon_shown🌕
create_shortcut_NotPromotable_Windowed🌕	check_app_in_list_windowed_NotPromotable🌓
install_policy_app_NotPromotable_WithShortcut_Windowed_WebApp🌓	check_app_in_list_windowed_NotPromotable🌓
install_policy_app_NotPromotable_NoShortcut_Windowed_WebApp🌓	check_app_in_list_windowed_NotPromotable🌓
create_shortcut_NotPromotable_Windowed🌕	navigate_browser_NotPromotable🌕	check_create_shortcut_not_shown🌑
install_policy_app_NotPromotable_WithShortcut_Windowed_WebApp🌓	navigate_browser_NotPromotable🌕	check_create_shortcut_not_shown🌑
install_policy_app_NotPromotable_NoShortcut_Windowed_WebApp🌓	navigate_browser_NotPromotable🌕	check_create_shortcut_not_shown🌑
create_shortcut_NotPromotable_Windowed🌕	navigate_browser_NotPromotable🌕	check_install_icon_not_shown🌕
install_policy_app_NotPromotable_WithShortcut_Windowed_WebApp🌓	navigate_browser_NotPromotable🌕	check_install_icon_not_shown🌕
install_policy_app_NotPromotable_NoShortcut_Windowed_WebApp🌓	navigate_browser_NotPromotable🌕	check_install_icon_not_shown🌕
create_shortcut_NotPromotable_Windowed🌕	navigate_browser_NotPromotable🌕	check_launch_icon_shown🌕
install_policy_app_NotPromotable_WithShortcut_Windowed_WebApp🌓	navigate_browser_NotPromotable🌕	check_launch_icon_shown🌕
install_policy_app_NotPromotable_NoShortcut_Windowed_WebApp🌓	navigate_browser_NotPromotable🌕	check_launch_icon_shown🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	check_platform_shortcut_and_icon_Standalone🌓
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	check_platform_shortcut_and_icon_Standalone🌓
create_shortcut_Standalone_Windowed🌕	check_platform_shortcut_and_icon_Standalone🌓
create_shortcut_Standalone_Browser🌕	check_platform_shortcut_and_icon_Standalone🌓
install_omnibox_icon_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
install_menu_option_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
install_policy_app_NotPromotable_WithShortcut_Windowed_WebApp🌓	check_platform_shortcut_and_icon_NotPromotable🌓
install_policy_app_NotPromotable_WithShortcut_Browser_WebApp🌓	check_platform_shortcut_and_icon_NotPromotable🌓
create_shortcut_NotPromotable_Windowed🌕	check_platform_shortcut_and_icon_NotPromotable🌓
create_shortcut_NotPromotable_Browser🌕	check_platform_shortcut_and_icon_NotPromotable🌓
create_shortcut_Standalone_Windowed🌕	uninstall_from_list_Standalone🌕	check_app_not_in_list_Standalone🌓
create_shortcut_Standalone_Windowed🌕	uninstall_from_menu_Standalone🌕	check_app_not_in_list_Standalone🌓
create_shortcut_Standalone_Windowed🌕	uninstall_from_os_Standalone🌕	check_app_not_in_list_Standalone🌓
create_shortcut_Standalone_Windowed🌕	uninstall_from_app_settings_Standalone🌕	check_app_not_in_list_Standalone🌓
install_omnibox_icon_Standalone🌕	uninstall_from_list_Standalone🌕	check_app_not_in_list_Standalone🌓
install_omnibox_icon_Standalone🌕	uninstall_from_menu_Standalone🌕	check_app_not_in_list_Standalone🌓
install_omnibox_icon_Standalone🌕	uninstall_from_os_Standalone🌕	check_app_not_in_list_Standalone🌓
install_omnibox_icon_Standalone🌕	uninstall_from_app_settings_Standalone🌕	check_app_not_in_list_Standalone🌓
install_menu_option_Standalone🌕	uninstall_from_list_Standalone🌕	check_app_not_in_list_Standalone🌓
install_menu_option_Standalone🌕	uninstall_from_menu_Standalone🌕	check_app_not_in_list_Standalone🌓
install_menu_option_Standalone🌕	uninstall_from_os_Standalone🌕	check_app_not_in_list_Standalone🌓
install_menu_option_Standalone🌕	uninstall_from_app_settings_Standalone🌕	check_app_not_in_list_Standalone🌓
create_shortcut_Standalone_Windowed🌕	uninstall_from_list_Standalone🌕	navigate_browser_Standalone🌕	check_install_icon_shown🌕
create_shortcut_Standalone_Windowed🌕	uninstall_from_menu_Standalone🌕	navigate_browser_Standalone🌕	check_install_icon_shown🌕
create_shortcut_Standalone_Windowed🌕	uninstall_from_os_Standalone🌕	navigate_browser_Standalone🌕	check_install_icon_shown🌕
create_shortcut_Standalone_Windowed🌕	uninstall_from_app_settings_Standalone🌕	navigate_browser_Standalone🌕	check_install_icon_shown🌕
install_omnibox_icon_Standalone🌕	uninstall_from_list_Standalone🌕	navigate_browser_Standalone🌕	check_install_icon_shown🌕
install_omnibox_icon_Standalone🌕	uninstall_from_menu_Standalone🌕	navigate_browser_Standalone🌕	check_install_icon_shown🌕
install_omnibox_icon_Standalone🌕	uninstall_from_os_Standalone🌕	navigate_browser_Standalone🌕	check_install_icon_shown🌕
install_omnibox_icon_Standalone🌕	uninstall_from_app_settings_Standalone🌕	navigate_browser_Standalone🌕	check_install_icon_shown🌕
install_menu_option_Standalone🌕	uninstall_from_list_Standalone🌕	navigate_browser_Standalone🌕	check_install_icon_shown🌕
install_menu_option_Standalone🌕	uninstall_from_menu_Standalone🌕	navigate_browser_Standalone🌕	check_install_icon_shown🌕
install_menu_option_Standalone🌕	uninstall_from_os_Standalone🌕	navigate_browser_Standalone🌕	check_install_icon_shown🌕
install_menu_option_Standalone🌕	uninstall_from_app_settings_Standalone🌕	navigate_browser_Standalone🌕	check_install_icon_shown🌕
create_shortcut_Standalone_Windowed🌕	uninstall_from_list_Standalone🌕	navigate_browser_Standalone🌕	check_launch_icon_not_shown🌕
create_shortcut_Standalone_Windowed🌕	uninstall_from_menu_Standalone🌕	navigate_browser_Standalone🌕	check_launch_icon_not_shown🌕
create_shortcut_Standalone_Windowed🌕	uninstall_from_os_Standalone🌕	navigate_browser_Standalone🌕	check_launch_icon_not_shown🌕
create_shortcut_Standalone_Windowed🌕	uninstall_from_app_settings_Standalone🌕	navigate_browser_Standalone🌕	check_launch_icon_not_shown🌕
install_omnibox_icon_Standalone🌕	uninstall_from_list_Standalone🌕	navigate_browser_Standalone🌕	check_launch_icon_not_shown🌕
install_omnibox_icon_Standalone🌕	uninstall_from_menu_Standalone🌕	navigate_browser_Standalone🌕	check_launch_icon_not_shown🌕
install_omnibox_icon_Standalone🌕	uninstall_from_os_Standalone🌕	navigate_browser_Standalone🌕	check_launch_icon_not_shown🌕
install_omnibox_icon_Standalone🌕	uninstall_from_app_settings_Standalone🌕	navigate_browser_Standalone🌕	check_launch_icon_not_shown🌕
install_menu_option_Standalone🌕	uninstall_from_list_Standalone🌕	navigate_browser_Standalone🌕	check_launch_icon_not_shown🌕
install_menu_option_Standalone🌕	uninstall_from_menu_Standalone🌕	navigate_browser_Standalone🌕	check_launch_icon_not_shown🌕
install_menu_option_Standalone🌕	uninstall_from_os_Standalone🌕	navigate_browser_Standalone🌕	check_launch_icon_not_shown🌕
install_menu_option_Standalone🌕	uninstall_from_app_settings_Standalone🌕	navigate_browser_Standalone🌕	check_launch_icon_not_shown🌕
create_shortcut_Standalone_Windowed🌕	uninstall_from_list_Standalone🌕	check_platform_shortcut_not_exists_Standalone🌑
create_shortcut_Standalone_Windowed🌕	uninstall_from_menu_Standalone🌕	check_platform_shortcut_not_exists_Standalone🌑
create_shortcut_Standalone_Windowed🌕	uninstall_from_os_Standalone🌕	check_platform_shortcut_not_exists_Standalone🌑
create_shortcut_Standalone_Windowed🌕	uninstall_from_app_settings_Standalone🌕	check_platform_shortcut_not_exists_Standalone🌑
install_omnibox_icon_Standalone🌕	uninstall_from_list_Standalone🌕	check_platform_shortcut_not_exists_Standalone🌑
install_omnibox_icon_Standalone🌕	uninstall_from_menu_Standalone🌕	check_platform_shortcut_not_exists_Standalone🌑
install_omnibox_icon_Standalone🌕	uninstall_from_os_Standalone🌕	check_platform_shortcut_not_exists_Standalone🌑
install_omnibox_icon_Standalone🌕	uninstall_from_app_settings_Standalone🌕	check_platform_shortcut_not_exists_Standalone🌑
install_menu_option_Standalone🌕	uninstall_from_list_Standalone🌕	check_platform_shortcut_not_exists_Standalone🌑
install_menu_option_Standalone🌕	uninstall_from_menu_Standalone🌕	check_platform_shortcut_not_exists_Standalone🌑
install_menu_option_Standalone🌕	uninstall_from_os_Standalone🌕	check_platform_shortcut_not_exists_Standalone🌑
install_menu_option_Standalone🌕	uninstall_from_app_settings_Standalone🌕	check_platform_shortcut_not_exists_Standalone🌑
create_shortcut_NotPromotable_Windowed🌕	uninstall_from_list_NotPromotable🌕	check_app_not_in_list_Standalone🌓
create_shortcut_NotPromotable_Windowed🌕	uninstall_from_menu_NotPromotable🌕	check_app_not_in_list_Standalone🌓
create_shortcut_NotPromotable_Windowed🌕	uninstall_from_os_NotPromotable🌕	check_app_not_in_list_Standalone🌓
create_shortcut_NotPromotable_Windowed🌕	uninstall_from_app_settings_NotPromotable🌕	check_app_not_in_list_Standalone🌓
create_shortcut_NotPromotable_Windowed🌕	uninstall_from_list_NotPromotable🌕	check_platform_shortcut_not_exists_NotPromotable🌑
create_shortcut_NotPromotable_Windowed🌕	uninstall_from_menu_NotPromotable🌕	check_platform_shortcut_not_exists_NotPromotable🌑
create_shortcut_NotPromotable_Windowed🌕	uninstall_from_os_NotPromotable🌕	check_platform_shortcut_not_exists_NotPromotable🌑
create_shortcut_NotPromotable_Windowed🌕	uninstall_from_app_settings_NotPromotable🌕	check_platform_shortcut_not_exists_NotPromotable🌑
create_shortcut_Standalone_Browser🌕	uninstall_from_list_Standalone🌕	check_app_not_in_list_Standalone🌓
create_shortcut_Standalone_Browser🌕	uninstall_from_list_Standalone🌕	navigate_browser_Standalone🌕	check_install_icon_shown🌕
create_shortcut_Standalone_Browser🌕	uninstall_from_list_Standalone🌕	navigate_browser_Standalone🌕	check_launch_icon_not_shown🌕
create_shortcut_Standalone_Browser🌕	uninstall_from_list_Standalone🌕	check_platform_shortcut_not_exists_Standalone🌑
create_shortcut_NotPromotable_Browser🌕	uninstall_from_list_NotPromotable🌕	check_app_not_in_list_Standalone🌓
create_shortcut_NotPromotable_Browser🌕	uninstall_from_list_NotPromotable🌕	check_platform_shortcut_not_exists_NotPromotable🌑
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_not_in_list_Standalone🌓
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_not_in_list_Standalone🌓
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_not_in_list_Standalone🌓
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_not_in_list_Standalone🌓
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_not_in_list_Standalone🌓
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_not_in_list_Standalone🌓
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_not_in_list_Standalone🌓
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_not_in_list_Standalone🌓
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	navigate_browser_Standalone🌕	check_install_icon_shown🌕
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	navigate_browser_Standalone🌕	check_install_icon_shown🌕
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	navigate_browser_Standalone🌕	check_launch_icon_not_shown🌕
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	navigate_browser_Standalone🌕	check_launch_icon_not_shown🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_not_exists_Standalone🌑	check_app_not_in_list_Standalone🌑
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_not_exists_Standalone🌑	check_app_not_in_list_Standalone🌑
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_not_exists_Standalone🌑	check_app_not_in_list_Standalone🌑
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_not_exists_Standalone🌑	check_app_not_in_list_Standalone🌑
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_not_exists_Standalone🌑	check_app_not_in_list_Standalone🌑
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_not_exists_Standalone🌑	check_app_not_in_list_Standalone🌑
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_not_exists_Standalone🌑	check_app_not_in_list_Standalone🌑
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_not_exists_Standalone🌑	check_app_not_in_list_Standalone🌑
create_shortcut_Standalone_Windowed🌕	launch_from_menu_option_Standalone🌕	check_window_created🌕
create_shortcut_Standalone_Windowed🌕	launch_from_launch_icon_Standalone🌕	check_window_created🌕
create_shortcut_Standalone_Windowed🌕	launch_from_chrome_apps_Standalone🌓	check_window_created🌕
create_shortcut_Standalone_Windowed🌕	launch_from_platform_shortcut_Standalone🌓	check_window_created🌕
install_omnibox_icon_Standalone🌕	launch_from_menu_option_Standalone🌕	check_window_created🌕
install_omnibox_icon_Standalone🌕	launch_from_launch_icon_Standalone🌕	check_window_created🌕
install_omnibox_icon_Standalone🌕	launch_from_chrome_apps_Standalone🌓	check_window_created🌕
install_omnibox_icon_Standalone🌕	launch_from_platform_shortcut_Standalone🌓	check_window_created🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	launch_from_menu_option_Standalone🌕	check_window_created🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	launch_from_launch_icon_Standalone🌕	check_window_created🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	launch_from_chrome_apps_Standalone🌓	check_window_created🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	launch_from_platform_shortcut_Standalone🌓	check_window_created🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	launch_from_menu_option_Standalone🌕	check_window_created🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	launch_from_launch_icon_Standalone🌕	check_window_created🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	launch_from_chrome_apps_Standalone🌓	check_window_created🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	launch_from_platform_shortcut_Standalone🌓	check_window_created🌕
install_menu_option_Standalone🌕	launch_from_menu_option_Standalone🌕	check_window_created🌕
install_menu_option_Standalone🌕	launch_from_launch_icon_Standalone🌕	check_window_created🌕
install_menu_option_Standalone🌕	launch_from_chrome_apps_Standalone🌓	check_window_created🌕
install_menu_option_Standalone🌕	launch_from_platform_shortcut_Standalone🌓	check_window_created🌕
create_shortcut_Standalone_Windowed🌕	launch_from_menu_option_Standalone🌕	check_window_display_standalone🌕
create_shortcut_Standalone_Windowed🌕	launch_from_launch_icon_Standalone🌕	check_window_display_standalone🌕
create_shortcut_Standalone_Windowed🌕	launch_from_chrome_apps_Standalone🌓	check_window_display_standalone🌕
create_shortcut_Standalone_Windowed🌕	launch_from_platform_shortcut_Standalone🌓	check_window_display_standalone🌕
install_omnibox_icon_Standalone🌕	launch_from_menu_option_Standalone🌕	check_window_display_standalone🌕
install_omnibox_icon_Standalone🌕	launch_from_launch_icon_Standalone🌕	check_window_display_standalone🌕
install_omnibox_icon_Standalone🌕	launch_from_chrome_apps_Standalone🌓	check_window_display_standalone🌕
install_omnibox_icon_Standalone🌕	launch_from_platform_shortcut_Standalone🌓	check_window_display_standalone🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	launch_from_menu_option_Standalone🌕	check_window_display_standalone🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	launch_from_launch_icon_Standalone🌕	check_window_display_standalone🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	launch_from_chrome_apps_Standalone🌓	check_window_display_standalone🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	launch_from_platform_shortcut_Standalone🌓	check_window_display_standalone🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	launch_from_menu_option_Standalone🌕	check_window_display_standalone🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	launch_from_launch_icon_Standalone🌕	check_window_display_standalone🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	launch_from_chrome_apps_Standalone🌓	check_window_display_standalone🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	launch_from_platform_shortcut_Standalone🌓	check_window_display_standalone🌕
install_menu_option_Standalone🌕	launch_from_menu_option_Standalone🌕	check_window_display_standalone🌕
install_menu_option_Standalone🌕	launch_from_launch_icon_Standalone🌕	check_window_display_standalone🌕
install_menu_option_Standalone🌕	launch_from_chrome_apps_Standalone🌓	check_window_display_standalone🌕
install_menu_option_Standalone🌕	launch_from_platform_shortcut_Standalone🌓	check_window_display_standalone🌕
create_shortcut_Standalone_Browser🌕	set_open_in_window_Standalone🌓	launch_from_menu_option_Standalone🌕	check_window_created🌕
create_shortcut_Standalone_Browser🌕	set_open_in_window_Standalone🌓	launch_from_launch_icon_Standalone🌕	check_window_created🌕
create_shortcut_Standalone_Browser🌕	set_open_in_window_Standalone🌓	launch_from_chrome_apps_Standalone🌓	check_window_created🌕
create_shortcut_Standalone_Browser🌕	set_open_in_window_Standalone🌓	launch_from_platform_shortcut_Standalone🌓	check_window_created🌕
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	set_open_in_window_Standalone🌓	launch_from_menu_option_Standalone🌕	check_window_created🌕
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	set_open_in_window_Standalone🌓	launch_from_launch_icon_Standalone🌕	check_window_created🌕
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	set_open_in_window_Standalone🌓	launch_from_chrome_apps_Standalone🌓	check_window_created🌕
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	set_open_in_window_Standalone🌓	launch_from_platform_shortcut_Standalone🌓	check_window_created🌕
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	set_open_in_window_Standalone🌓	launch_from_menu_option_Standalone🌕	check_window_created🌕
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	set_open_in_window_Standalone🌓	launch_from_launch_icon_Standalone🌕	check_window_created🌕
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	set_open_in_window_Standalone🌓	launch_from_chrome_apps_Standalone🌓	check_window_created🌕
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	set_open_in_window_Standalone🌓	launch_from_platform_shortcut_Standalone🌓	check_window_created🌕
create_shortcut_Standalone_Windowed🌕	set_open_in_tab_Standalone🌓	launch_from_chrome_apps_Standalone🌓	check_tab_created🌕
create_shortcut_Standalone_Windowed🌕	set_open_in_tab_Standalone🌓	launch_from_platform_shortcut_Standalone🌓	check_tab_created🌕
install_omnibox_icon_Standalone🌕	set_open_in_tab_Standalone🌓	launch_from_chrome_apps_Standalone🌓	check_tab_created🌕
install_omnibox_icon_Standalone🌕	set_open_in_tab_Standalone🌓	launch_from_platform_shortcut_Standalone🌓	check_tab_created🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	set_open_in_tab_Standalone🌓	launch_from_chrome_apps_Standalone🌓	check_tab_created🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	set_open_in_tab_Standalone🌓	launch_from_platform_shortcut_Standalone🌓	check_tab_created🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	set_open_in_tab_Standalone🌓	launch_from_chrome_apps_Standalone🌓	check_tab_created🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	set_open_in_tab_Standalone🌓	launch_from_platform_shortcut_Standalone🌓	check_tab_created🌕
install_menu_option_Standalone🌕	set_open_in_tab_Standalone🌓	launch_from_chrome_apps_Standalone🌓	check_tab_created🌕
install_menu_option_Standalone🌕	set_open_in_tab_Standalone🌓	launch_from_platform_shortcut_Standalone🌓	check_tab_created🌕
create_shortcut_NotPromotable_Browser🌕	launch_from_chrome_apps_NotPromotable🌓	check_tab_created🌕
create_shortcut_NotPromotable_Browser🌕	launch_from_platform_shortcut_NotPromotable🌓	check_tab_created🌕
install_policy_app_NotPromotable_WithShortcut_Browser_WebApp🌓	launch_from_chrome_apps_NotPromotable🌓	check_tab_created🌕
install_policy_app_NotPromotable_WithShortcut_Browser_WebApp🌓	launch_from_platform_shortcut_NotPromotable🌓	check_tab_created🌕
install_policy_app_NotPromotable_NoShortcut_Browser_WebApp🌓	launch_from_chrome_apps_NotPromotable🌓	check_tab_created🌕
install_policy_app_NotPromotable_NoShortcut_Browser_WebApp🌓	launch_from_platform_shortcut_NotPromotable🌓	check_tab_created🌕
create_shortcut_MinimalUi_Windowed🌕	launch_from_menu_option_MinimalUi🌕	check_window_display_minimal🌕
create_shortcut_MinimalUi_Windowed🌕	launch_from_launch_icon_MinimalUi🌕	check_window_display_minimal🌕
create_shortcut_MinimalUi_Windowed🌕	launch_from_chrome_apps_MinimalUi🌓	check_window_display_minimal🌕
create_shortcut_MinimalUi_Windowed🌕	launch_from_platform_shortcut_MinimalUi🌓	check_window_display_minimal🌕
install_omnibox_icon_MinimalUi🌕	launch_from_menu_option_MinimalUi🌕	check_window_display_minimal🌕
install_omnibox_icon_MinimalUi🌕	launch_from_launch_icon_MinimalUi🌕	check_window_display_minimal🌕
install_omnibox_icon_MinimalUi🌕	launch_from_chrome_apps_MinimalUi🌓	check_window_display_minimal🌕
install_omnibox_icon_MinimalUi🌕	launch_from_platform_shortcut_MinimalUi🌓	check_window_display_minimal🌕
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	launch_from_menu_option_MinimalUi🌕	check_window_display_minimal🌕
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	launch_from_launch_icon_MinimalUi🌕	check_window_display_minimal🌕
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	launch_from_chrome_apps_MinimalUi🌓	check_window_display_minimal🌕
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	launch_from_platform_shortcut_MinimalUi🌓	check_window_display_minimal🌕
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	launch_from_menu_option_MinimalUi🌕	check_window_display_minimal🌕
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	launch_from_launch_icon_MinimalUi🌕	check_window_display_minimal🌕
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	launch_from_chrome_apps_MinimalUi🌓	check_window_display_minimal🌕
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	launch_from_platform_shortcut_MinimalUi🌓	check_window_display_minimal🌕
install_menu_option_MinimalUi🌕	launch_from_menu_option_MinimalUi🌕	check_window_display_minimal🌕
install_menu_option_MinimalUi🌕	launch_from_launch_icon_MinimalUi🌕	check_window_display_minimal🌕
install_menu_option_MinimalUi🌕	launch_from_chrome_apps_MinimalUi🌓	check_window_display_minimal🌕
install_menu_option_MinimalUi🌕	launch_from_platform_shortcut_MinimalUi🌓	check_window_display_minimal🌕
create_shortcut_NotPromotable_Windowed🌕	launch_from_menu_option_NotPromotable🌕	check_window_created🌕
create_shortcut_NotPromotable_Windowed🌕	launch_from_launch_icon_NotPromotable🌕	check_window_created🌕
create_shortcut_NotPromotable_Windowed🌕	launch_from_chrome_apps_NotPromotable🌓	check_window_created🌕
create_shortcut_NotPromotable_Windowed🌕	launch_from_platform_shortcut_NotPromotable🌓	check_window_created🌕
install_policy_app_NotPromotable_WithShortcut_Windowed_WebApp🌓	launch_from_menu_option_NotPromotable🌕	check_window_created🌕
install_policy_app_NotPromotable_WithShortcut_Windowed_WebApp🌓	launch_from_launch_icon_NotPromotable🌕	check_window_created🌕
install_policy_app_NotPromotable_WithShortcut_Windowed_WebApp🌓	launch_from_chrome_apps_NotPromotable🌓	check_window_created🌕
install_policy_app_NotPromotable_WithShortcut_Windowed_WebApp🌓	launch_from_platform_shortcut_NotPromotable🌓	check_window_created🌕
install_policy_app_NotPromotable_NoShortcut_Windowed_WebApp🌓	launch_from_menu_option_NotPromotable🌕	check_window_created🌕
install_policy_app_NotPromotable_NoShortcut_Windowed_WebApp🌓	launch_from_launch_icon_NotPromotable🌕	check_window_created🌕
install_policy_app_NotPromotable_NoShortcut_Windowed_WebApp🌓	launch_from_chrome_apps_NotPromotable🌓	check_window_created🌕
install_policy_app_NotPromotable_NoShortcut_Windowed_WebApp🌓	launch_from_platform_shortcut_NotPromotable🌓	check_window_created🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	create_shortcuts_from_list_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	create_shortcuts_from_list_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
create_shortcut_Standalone_Windowed🌕	delete_profile🌑	check_app_list_empty🌑
create_shortcut_Standalone_Browser🌕	delete_profile🌑	check_app_list_empty🌑
install_omnibox_icon_Standalone🌕	delete_profile🌑	check_app_list_empty🌑
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	delete_profile🌑	check_app_list_empty🌑
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	delete_profile🌑	check_app_list_empty🌑
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	delete_profile🌑	check_app_list_empty🌑
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	delete_profile🌑	check_app_list_empty🌑
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	delete_profile🌑	check_app_list_empty🌑
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	delete_profile🌑	check_app_list_empty🌑
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	delete_profile🌑	check_app_list_empty🌑
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	delete_profile🌑	check_app_list_empty🌑
install_menu_option_Standalone🌕	delete_profile🌑	check_app_list_empty🌑
create_shortcut_Standalone_Windowed🌕	delete_profile🌑	check_app_not_in_list_Standalone🌑
create_shortcut_Standalone_Browser🌕	delete_profile🌑	check_app_not_in_list_Standalone🌑
install_omnibox_icon_Standalone🌕	delete_profile🌑	check_app_not_in_list_Standalone🌑
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	delete_profile🌑	check_app_not_in_list_Standalone🌑
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	delete_profile🌑	check_app_not_in_list_Standalone🌑
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	delete_profile🌑	check_app_not_in_list_Standalone🌑
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	delete_profile🌑	check_app_not_in_list_Standalone🌑
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	delete_profile🌑	check_app_not_in_list_Standalone🌑
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	delete_profile🌑	check_app_not_in_list_Standalone🌑
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	delete_profile🌑	check_app_not_in_list_Standalone🌑
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	delete_profile🌑	check_app_not_in_list_Standalone🌑
install_menu_option_Standalone🌕	delete_profile🌑	check_app_not_in_list_Standalone🌑
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	delete_profile🌑	check_platform_shortcut_not_exists_Standalone🌑
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	delete_profile🌑	check_platform_shortcut_not_exists_Standalone🌑
create_shortcut_Standalone_Windowed🌕	delete_profile🌑	check_platform_shortcut_not_exists_Standalone🌑
create_shortcut_Standalone_Browser🌕	delete_profile🌑	check_platform_shortcut_not_exists_Standalone🌑
install_omnibox_icon_Standalone🌕	delete_profile🌑	check_platform_shortcut_not_exists_Standalone🌑
install_menu_option_Standalone🌕	delete_profile🌑	check_platform_shortcut_not_exists_Standalone🌑
create_shortcut_Standalone_Browser🌕	delete_platform_shortcut_Standalone🌕	create_shortcuts_from_list_Standalone🌕	launch_from_platform_shortcut_Standalone🌓	check_tab_created🌕
create_shortcut_Standalone_Windowed🌕	delete_platform_shortcut_Standalone🌕	create_shortcuts_from_list_Standalone🌕	launch_from_platform_shortcut_Standalone🌓	check_window_created🌕
install_omnibox_icon_Standalone🌕	delete_platform_shortcut_Standalone🌕	create_shortcuts_from_list_Standalone🌕	launch_from_platform_shortcut_Standalone🌓	check_window_created🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	delete_platform_shortcut_Standalone🌕	create_shortcuts_from_list_Standalone🌕	launch_from_platform_shortcut_Standalone🌓	check_window_created🌕
install_menu_option_Standalone🌕	delete_platform_shortcut_Standalone🌕	create_shortcuts_from_list_Standalone🌕	launch_from_platform_shortcut_Standalone🌓	check_window_created🌕
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	create_shortcuts_from_list_Standalone🌕	launch_from_platform_shortcut_Standalone🌓	check_tab_created🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	create_shortcuts_from_list_Standalone🌕	launch_from_platform_shortcut_Standalone🌓	check_window_created🌕
create_shortcut_Standalone_Windowed🌕	open_in_chrome🌕	check_tab_created🌕
install_omnibox_icon_Standalone🌕	open_in_chrome🌕	check_tab_created🌕
install_menu_option_Standalone🌕	open_in_chrome🌕	check_tab_created🌕
create_shortcut_Standalone_Windowed🌕	navigate_pwa_Standalone_MinimalUi🌕	open_in_chrome🌕	check_tab_created🌕
install_omnibox_icon_Standalone🌕	navigate_pwa_Standalone_MinimalUi🌕	open_in_chrome🌕	check_tab_created🌕
install_menu_option_Standalone🌕	navigate_pwa_Standalone_MinimalUi🌕	open_in_chrome🌕	check_tab_created🌕
create_shortcut_Standalone_Windowed🌕	open_app_settings_from_chrome_apps_Standalone🌕	check_browser_navigation_is_app_settings_Standalone🌕
create_shortcut_Standalone_Windowed🌕	open_app_settings_from_app_menu_Standalone🌕	check_browser_navigation_is_app_settings_Standalone🌕
install_omnibox_icon_Standalone🌕	open_app_settings_from_chrome_apps_Standalone🌕	check_browser_navigation_is_app_settings_Standalone🌕
install_omnibox_icon_Standalone🌕	open_app_settings_from_app_menu_Standalone🌕	check_browser_navigation_is_app_settings_Standalone🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	open_app_settings_from_chrome_apps_Standalone🌕	check_browser_navigation_is_app_settings_Standalone🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	open_app_settings_from_app_menu_Standalone🌕	check_browser_navigation_is_app_settings_Standalone🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	open_app_settings_from_chrome_apps_Standalone🌕	check_browser_navigation_is_app_settings_Standalone🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	open_app_settings_from_app_menu_Standalone🌕	check_browser_navigation_is_app_settings_Standalone🌕
install_menu_option_Standalone🌕	open_app_settings_from_chrome_apps_Standalone🌕	check_browser_navigation_is_app_settings_Standalone🌕
install_menu_option_Standalone🌕	open_app_settings_from_app_menu_Standalone🌕	check_browser_navigation_is_app_settings_Standalone🌕
create_shortcut_Standalone_Windowed🌕	switch_profile_clients_Client2🌕	install_locally_Standalone🌓	check_platform_shortcut_and_icon_Standalone🌓
create_shortcut_Standalone_Browser🌕	switch_profile_clients_Client2🌕	install_locally_Standalone🌓	check_platform_shortcut_and_icon_Standalone🌓
install_omnibox_icon_Standalone🌕	switch_profile_clients_Client2🌕	install_locally_Standalone🌓	check_platform_shortcut_and_icon_Standalone🌓
install_menu_option_Standalone🌕	switch_profile_clients_Client2🌕	install_locally_Standalone🌓	check_platform_shortcut_and_icon_Standalone🌓
create_shortcut_Standalone_Browser🌕	switch_profile_clients_Client2🌕	install_locally_Standalone🌓	check_app_in_list_tabbed_Standalone🌓
create_shortcut_Standalone_Browser🌕	switch_profile_clients_Client2🌕	install_locally_Standalone🌓	navigate_browser_Standalone🌕	check_install_icon_shown🌕
create_shortcut_Standalone_Browser🌕	switch_profile_clients_Client2🌕	install_locally_Standalone🌓	navigate_browser_Standalone🌕	check_launch_icon_not_shown🌕
create_shortcut_Standalone_Windowed🌕	switch_profile_clients_Client2🌕	install_locally_Standalone🌓	check_app_in_list_windowed_Standalone🌓
install_omnibox_icon_Standalone🌕	switch_profile_clients_Client2🌕	install_locally_Standalone🌓	check_app_in_list_windowed_Standalone🌓
install_menu_option_Standalone🌕	switch_profile_clients_Client2🌕	install_locally_Standalone🌓	check_app_in_list_windowed_Standalone🌓
create_shortcut_Standalone_Windowed🌕	switch_profile_clients_Client2🌕	install_locally_Standalone🌓	navigate_browser_Standalone🌕	check_install_icon_not_shown🌕
install_omnibox_icon_Standalone🌕	switch_profile_clients_Client2🌕	install_locally_Standalone🌓	navigate_browser_Standalone🌕	check_install_icon_not_shown🌕
install_menu_option_Standalone🌕	switch_profile_clients_Client2🌕	install_locally_Standalone🌓	navigate_browser_Standalone🌕	check_install_icon_not_shown🌕
create_shortcut_Standalone_Windowed🌕	switch_profile_clients_Client2🌕	install_locally_Standalone🌓	navigate_browser_Standalone🌕	check_launch_icon_shown🌕
install_omnibox_icon_Standalone🌕	switch_profile_clients_Client2🌕	install_locally_Standalone🌓	navigate_browser_Standalone🌕	check_launch_icon_shown🌕
install_menu_option_Standalone🌕	switch_profile_clients_Client2🌕	install_locally_Standalone🌓	navigate_browser_Standalone🌕	check_launch_icon_shown🌕
create_shortcut_NotPromotable_Browser🌕	switch_profile_clients_Client2🌕	install_locally_NotPromotable🌓	check_app_in_list_tabbed_NotPromotable🌓
create_shortcut_NotPromotable_Browser🌕	switch_profile_clients_Client2🌕	install_locally_NotPromotable🌓	navigate_browser_NotPromotable🌕	check_launch_icon_not_shown🌕
create_shortcut_NotPromotable_Windowed🌕	switch_profile_clients_Client2🌕	install_locally_NotPromotable🌓	check_app_in_list_windowed_NotPromotable🌓
create_shortcut_NotPromotable_Windowed🌕	switch_profile_clients_Client2🌕	install_locally_NotPromotable🌓	navigate_browser_NotPromotable🌕	check_install_icon_not_shown🌕
create_shortcut_NotPromotable_Windowed🌕	switch_profile_clients_Client2🌕	install_locally_NotPromotable🌓	navigate_browser_NotPromotable🌕	check_launch_icon_shown🌕
create_shortcut_NotPromotable_Windowed🌕	switch_profile_clients_Client2🌕	install_locally_NotPromotable🌓	check_platform_shortcut_and_icon_NotPromotable🌓
create_shortcut_NotPromotable_Browser🌕	switch_profile_clients_Client2🌕	install_locally_NotPromotable🌓	check_platform_shortcut_and_icon_NotPromotable🌓
create_shortcut_Standalone_Windowed🌕	switch_profile_clients_Client2🌕	install_locally_Standalone🌓	launch_from_menu_option_Standalone🌕	check_window_created🌕
create_shortcut_Standalone_Windowed🌕	switch_profile_clients_Client2🌕	install_locally_Standalone🌓	launch_from_launch_icon_Standalone🌕	check_window_created🌕
create_shortcut_Standalone_Windowed🌕	switch_profile_clients_Client2🌕	install_locally_Standalone🌓	launch_from_chrome_apps_Standalone🌓	check_window_created🌕
create_shortcut_Standalone_Windowed🌕	switch_profile_clients_Client2🌕	install_locally_Standalone🌓	launch_from_platform_shortcut_Standalone🌓	check_window_created🌕
install_omnibox_icon_Standalone🌕	switch_profile_clients_Client2🌕	install_locally_Standalone🌓	launch_from_menu_option_Standalone🌕	check_window_created🌕
install_omnibox_icon_Standalone🌕	switch_profile_clients_Client2🌕	install_locally_Standalone🌓	launch_from_launch_icon_Standalone🌕	check_window_created🌕
install_omnibox_icon_Standalone🌕	switch_profile_clients_Client2🌕	install_locally_Standalone🌓	launch_from_chrome_apps_Standalone🌓	check_window_created🌕
install_omnibox_icon_Standalone🌕	switch_profile_clients_Client2🌕	install_locally_Standalone🌓	launch_from_platform_shortcut_Standalone🌓	check_window_created🌕
install_menu_option_Standalone🌕	switch_profile_clients_Client2🌕	install_locally_Standalone🌓	launch_from_menu_option_Standalone🌕	check_window_created🌕
install_menu_option_Standalone🌕	switch_profile_clients_Client2🌕	install_locally_Standalone🌓	launch_from_launch_icon_Standalone🌕	check_window_created🌕
install_menu_option_Standalone🌕	switch_profile_clients_Client2🌕	install_locally_Standalone🌓	launch_from_chrome_apps_Standalone🌓	check_window_created🌕
install_menu_option_Standalone🌕	switch_profile_clients_Client2🌕	install_locally_Standalone🌓	launch_from_platform_shortcut_Standalone🌓	check_window_created🌕
create_shortcut_Standalone_Browser🌕	switch_profile_clients_Client2🌕	launch_from_chrome_apps_Standalone🌓	check_tab_created🌕
create_shortcut_Standalone_Browser🌕	switch_profile_clients_Client2🌕	launch_from_platform_shortcut_Standalone🌓	check_tab_created🌕
create_shortcut_Standalone_Browser🌕	switch_profile_clients_Client2🌕	install_locally_Standalone🌓	launch_from_chrome_apps_Standalone🌓	check_tab_created🌕
create_shortcut_Standalone_Browser🌕	switch_profile_clients_Client2🌕	install_locally_Standalone🌓	launch_from_platform_shortcut_Standalone🌓	check_tab_created🌕
create_shortcut_Standalone_Windowed🌕	switch_profile_clients_Client2🌕	launch_from_chrome_apps_Standalone🌓	check_tab_created🌕
create_shortcut_Standalone_Windowed🌕	switch_profile_clients_Client2🌕	launch_from_platform_shortcut_Standalone🌓	check_tab_created🌕
install_omnibox_icon_Standalone🌕	switch_profile_clients_Client2🌕	launch_from_chrome_apps_Standalone🌓	check_tab_created🌕
install_omnibox_icon_Standalone🌕	switch_profile_clients_Client2🌕	launch_from_platform_shortcut_Standalone🌓	check_tab_created🌕
install_menu_option_Standalone🌕	switch_profile_clients_Client2🌕	launch_from_chrome_apps_Standalone🌓	check_tab_created🌕
install_menu_option_Standalone🌕	switch_profile_clients_Client2🌕	launch_from_platform_shortcut_Standalone🌓	check_tab_created🌕
create_shortcut_Standalone_Windowed🌕	switch_profile_clients_Client2🌕	uninstall_from_list_Standalone🌕	check_app_not_in_list_Standalone🌓
create_shortcut_Standalone_Browser🌕	switch_profile_clients_Client2🌕	uninstall_from_list_Standalone🌕	check_app_not_in_list_Standalone🌓
install_omnibox_icon_Standalone🌕	switch_profile_clients_Client2🌕	uninstall_from_list_Standalone🌕	check_app_not_in_list_Standalone🌓
install_menu_option_Standalone🌕	switch_profile_clients_Client2🌕	uninstall_from_list_Standalone🌕	check_app_not_in_list_Standalone🌓
create_shortcut_Standalone_Windowed🌕	switch_profile_clients_Client2🌕	uninstall_from_list_Standalone🌕	switch_profile_clients_Client1🌕	check_app_not_in_list_Standalone🌓
create_shortcut_Standalone_Browser🌕	switch_profile_clients_Client2🌕	uninstall_from_list_Standalone🌕	switch_profile_clients_Client1🌕	check_app_not_in_list_Standalone🌓
install_omnibox_icon_Standalone🌕	switch_profile_clients_Client2🌕	uninstall_from_list_Standalone🌕	switch_profile_clients_Client1🌕	check_app_not_in_list_Standalone🌓
install_menu_option_Standalone🌕	switch_profile_clients_Client2🌕	uninstall_from_list_Standalone🌕	switch_profile_clients_Client1🌕	check_app_not_in_list_Standalone🌓
create_shortcut_Standalone_Windowed🌕	switch_profile_clients_Client2🌕	check_app_in_list_not_locally_installed_Standalone🌓
create_shortcut_Standalone_Browser🌕	switch_profile_clients_Client2🌕	check_app_in_list_not_locally_installed_Standalone🌓
install_omnibox_icon_Standalone🌕	switch_profile_clients_Client2🌕	check_app_in_list_not_locally_installed_Standalone🌓
install_menu_option_Standalone🌕	switch_profile_clients_Client2🌕	check_app_in_list_not_locally_installed_Standalone🌓
create_shortcut_Standalone_Windowed🌕	switch_profile_clients_Client2🌕	check_platform_shortcut_not_exists_Standalone🌑
create_shortcut_Standalone_Browser🌕	switch_profile_clients_Client2🌕	check_platform_shortcut_not_exists_Standalone🌑
install_omnibox_icon_Standalone🌕	switch_profile_clients_Client2🌕	check_platform_shortcut_not_exists_Standalone🌑
install_menu_option_Standalone🌕	switch_profile_clients_Client2🌕	check_platform_shortcut_not_exists_Standalone🌑
create_shortcut_Standalone_Windowed🌕	switch_profile_clients_Client2🌕	navigate_browser_Standalone🌕	check_install_icon_shown🌕
install_omnibox_icon_Standalone🌕	switch_profile_clients_Client2🌕	navigate_browser_Standalone🌕	check_install_icon_shown🌕
install_menu_option_Standalone🌕	switch_profile_clients_Client2🌕	navigate_browser_Standalone🌕	check_install_icon_shown🌕
create_shortcut_Standalone_Windowed🌕	switch_profile_clients_Client2🌕	navigate_browser_Standalone🌕	check_launch_icon_not_shown🌕
install_omnibox_icon_Standalone🌕	switch_profile_clients_Client2🌕	navigate_browser_Standalone🌕	check_launch_icon_not_shown🌕
install_menu_option_Standalone🌕	switch_profile_clients_Client2🌕	navigate_browser_Standalone🌕	check_launch_icon_not_shown🌕
create_shortcut_NotPromotable_Windowed🌕	switch_profile_clients_Client2🌕	check_app_in_list_not_locally_installed_NotPromotable🌓
create_shortcut_NotPromotable_Browser🌕	switch_profile_clients_Client2🌕	check_app_in_list_not_locally_installed_NotPromotable🌓
create_shortcut_NotPromotable_Windowed🌕	switch_profile_clients_Client2🌕	check_platform_shortcut_not_exists_NotPromotable🌑
create_shortcut_NotPromotable_Browser🌕	switch_profile_clients_Client2🌕	check_platform_shortcut_not_exists_NotPromotable🌑
sync_turn_off🌕	create_shortcut_Standalone_Windowed🌕	sync_turn_on🌕	switch_profile_clients_Client2🌕	check_app_in_list_not_locally_installed_Standalone🌓
sync_turn_off🌕	create_shortcut_Standalone_Browser🌕	sync_turn_on🌕	switch_profile_clients_Client2🌕	check_app_in_list_not_locally_installed_Standalone🌓
sync_turn_off🌕	install_omnibox_icon_Standalone🌕	sync_turn_on🌕	switch_profile_clients_Client2🌕	check_app_in_list_not_locally_installed_Standalone🌓
sync_turn_off🌕	install_menu_option_Standalone🌕	sync_turn_on🌕	switch_profile_clients_Client2🌕	check_app_in_list_not_locally_installed_Standalone🌓
sync_turn_off🌕	create_shortcut_NotPromotable_Windowed🌕	sync_turn_on🌕	switch_profile_clients_Client2🌕	check_app_in_list_not_locally_installed_NotPromotable🌓
sync_turn_off🌕	create_shortcut_NotPromotable_Browser🌕	sync_turn_on🌕	switch_profile_clients_Client2🌕	check_app_in_list_not_locally_installed_NotPromotable🌓
create_shortcut_Standalone_Windowed🌕	switch_profile_clients_Client2🌕	sync_turn_off🌕	uninstall_from_list_Standalone🌕	sync_turn_on🌕	check_app_in_list_not_locally_installed_Standalone🌓
create_shortcut_Standalone_Windowed🌕	switch_profile_clients_Client2🌕	sync_turn_off🌕	uninstall_from_menu_Standalone🌕	sync_turn_on🌕	check_app_in_list_not_locally_installed_Standalone🌓
create_shortcut_Standalone_Windowed🌕	switch_profile_clients_Client2🌕	sync_turn_off🌕	uninstall_from_os_Standalone🌕	sync_turn_on🌕	check_app_in_list_not_locally_installed_Standalone🌓
create_shortcut_Standalone_Browser🌕	switch_profile_clients_Client2🌕	sync_turn_off🌕	uninstall_from_list_Standalone🌕	sync_turn_on🌕	check_app_in_list_not_locally_installed_Standalone🌓
create_shortcut_Standalone_Browser🌕	switch_profile_clients_Client2🌕	sync_turn_off🌕	uninstall_from_menu_Standalone🌕	sync_turn_on🌕	check_app_in_list_not_locally_installed_Standalone🌓
create_shortcut_Standalone_Browser🌕	switch_profile_clients_Client2🌕	sync_turn_off🌕	uninstall_from_os_Standalone🌕	sync_turn_on🌕	check_app_in_list_not_locally_installed_Standalone🌓
install_omnibox_icon_Standalone🌕	switch_profile_clients_Client2🌕	sync_turn_off🌕	uninstall_from_list_Standalone🌕	sync_turn_on🌕	check_app_in_list_not_locally_installed_Standalone🌓
install_omnibox_icon_Standalone🌕	switch_profile_clients_Client2🌕	sync_turn_off🌕	uninstall_from_menu_Standalone🌕	sync_turn_on🌕	check_app_in_list_not_locally_installed_Standalone🌓
install_omnibox_icon_Standalone🌕	switch_profile_clients_Client2🌕	sync_turn_off🌕	uninstall_from_os_Standalone🌕	sync_turn_on🌕	check_app_in_list_not_locally_installed_Standalone🌓
install_menu_option_Standalone🌕	switch_profile_clients_Client2🌕	sync_turn_off🌕	uninstall_from_list_Standalone🌕	sync_turn_on🌕	check_app_in_list_not_locally_installed_Standalone🌓
install_menu_option_Standalone🌕	switch_profile_clients_Client2🌕	sync_turn_off🌕	uninstall_from_menu_Standalone🌕	sync_turn_on🌕	check_app_in_list_not_locally_installed_Standalone🌓
install_menu_option_Standalone🌕	switch_profile_clients_Client2🌕	sync_turn_off🌕	uninstall_from_os_Standalone🌕	sync_turn_on🌕	check_app_in_list_not_locally_installed_Standalone🌓
create_shortcut_Standalone_Windowed🌕	switch_profile_clients_Client2🌕	sync_turn_off🌕	uninstall_from_list_Standalone🌕	sync_turn_on🌕	check_platform_shortcut_not_exists_Standalone🌑
create_shortcut_Standalone_Windowed🌕	switch_profile_clients_Client2🌕	sync_turn_off🌕	uninstall_from_menu_Standalone🌕	sync_turn_on🌕	check_platform_shortcut_not_exists_Standalone🌑
create_shortcut_Standalone_Windowed🌕	switch_profile_clients_Client2🌕	sync_turn_off🌕	uninstall_from_os_Standalone🌕	sync_turn_on🌕	check_platform_shortcut_not_exists_Standalone🌑
create_shortcut_Standalone_Browser🌕	switch_profile_clients_Client2🌕	sync_turn_off🌕	uninstall_from_list_Standalone🌕	sync_turn_on🌕	check_platform_shortcut_not_exists_Standalone🌑
create_shortcut_Standalone_Browser🌕	switch_profile_clients_Client2🌕	sync_turn_off🌕	uninstall_from_menu_Standalone🌕	sync_turn_on🌕	check_platform_shortcut_not_exists_Standalone🌑
create_shortcut_Standalone_Browser🌕	switch_profile_clients_Client2🌕	sync_turn_off🌕	uninstall_from_os_Standalone🌕	sync_turn_on🌕	check_platform_shortcut_not_exists_Standalone🌑
install_omnibox_icon_Standalone🌕	switch_profile_clients_Client2🌕	sync_turn_off🌕	uninstall_from_list_Standalone🌕	sync_turn_on🌕	check_platform_shortcut_not_exists_Standalone🌑
install_omnibox_icon_Standalone🌕	switch_profile_clients_Client2🌕	sync_turn_off🌕	uninstall_from_menu_Standalone🌕	sync_turn_on🌕	check_platform_shortcut_not_exists_Standalone🌑
install_omnibox_icon_Standalone🌕	switch_profile_clients_Client2🌕	sync_turn_off🌕	uninstall_from_os_Standalone🌕	sync_turn_on🌕	check_platform_shortcut_not_exists_Standalone🌑
install_menu_option_Standalone🌕	switch_profile_clients_Client2🌕	sync_turn_off🌕	uninstall_from_list_Standalone🌕	sync_turn_on🌕	check_platform_shortcut_not_exists_Standalone🌑
install_menu_option_Standalone🌕	switch_profile_clients_Client2🌕	sync_turn_off🌕	uninstall_from_menu_Standalone🌕	sync_turn_on🌕	check_platform_shortcut_not_exists_Standalone🌑
install_menu_option_Standalone🌕	switch_profile_clients_Client2🌕	sync_turn_off🌕	uninstall_from_os_Standalone🌕	sync_turn_on🌕	check_platform_shortcut_not_exists_Standalone🌑
create_shortcut_Standalone_Browser🌕	install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	check_platform_shortcut_and_icon_Standalone🌓
create_shortcut_Standalone_Browser🌕	install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	check_platform_shortcut_and_icon_Standalone🌓
create_shortcut_Standalone_Windowed🌕	install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	check_platform_shortcut_and_icon_Standalone🌓
install_omnibox_icon_Standalone🌕	install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	check_platform_shortcut_and_icon_Standalone🌓
install_menu_option_Standalone🌕	install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	check_platform_shortcut_and_icon_Standalone🌓
create_shortcut_Standalone_Windowed🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	check_platform_shortcut_and_icon_Standalone🌓
install_omnibox_icon_Standalone🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	check_platform_shortcut_and_icon_Standalone🌓
install_menu_option_Standalone🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	check_platform_shortcut_and_icon_Standalone🌓
create_shortcut_Standalone_Windowed🌕	install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	check_app_in_list_windowed_Standalone🌓
install_omnibox_icon_Standalone🌕	install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	check_app_in_list_windowed_Standalone🌓
install_menu_option_Standalone🌕	install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	check_app_in_list_windowed_Standalone🌓
create_shortcut_Standalone_Windowed🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	check_app_in_list_windowed_Standalone🌓
install_omnibox_icon_Standalone🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	check_app_in_list_windowed_Standalone🌓
install_menu_option_Standalone🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	check_app_in_list_windowed_Standalone🌓
create_shortcut_Standalone_Windowed🌕	install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	navigate_browser_Standalone🌕	check_launch_icon_shown🌕
install_omnibox_icon_Standalone🌕	install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	navigate_browser_Standalone🌕	check_launch_icon_shown🌕
install_menu_option_Standalone🌕	install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	navigate_browser_Standalone🌕	check_launch_icon_shown🌕
create_shortcut_Standalone_Windowed🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	navigate_browser_Standalone🌕	check_launch_icon_shown🌕
install_omnibox_icon_Standalone🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	navigate_browser_Standalone🌕	check_launch_icon_shown🌕
install_menu_option_Standalone🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	navigate_browser_Standalone🌕	check_launch_icon_shown🌕
create_shortcut_Standalone_Browser🌕	install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	check_app_in_list_tabbed_Standalone🌓
create_shortcut_Standalone_Browser🌕	install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	check_app_in_list_tabbed_Standalone🌓
create_shortcut_Standalone_Browser🌕	install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	navigate_browser_Standalone🌕	check_install_icon_shown🌕
create_shortcut_Standalone_Browser🌕	install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	navigate_browser_Standalone🌕	check_install_icon_shown🌕
create_shortcut_Standalone_Windowed🌕	install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	launch_from_menu_option_Standalone🌕	check_window_created🌕
create_shortcut_Standalone_Windowed🌕	install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	launch_from_launch_icon_Standalone🌕	check_window_created🌕
create_shortcut_Standalone_Windowed🌕	install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	launch_from_chrome_apps_Standalone🌓	check_window_created🌕
create_shortcut_Standalone_Windowed🌕	install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	launch_from_platform_shortcut_Standalone🌓	check_window_created🌕
install_omnibox_icon_Standalone🌕	install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	launch_from_menu_option_Standalone🌕	check_window_created🌕
install_omnibox_icon_Standalone🌕	install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	launch_from_launch_icon_Standalone🌕	check_window_created🌕
install_omnibox_icon_Standalone🌕	install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	launch_from_chrome_apps_Standalone🌓	check_window_created🌕
install_omnibox_icon_Standalone🌕	install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	launch_from_platform_shortcut_Standalone🌓	check_window_created🌕
install_menu_option_Standalone🌕	install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	launch_from_menu_option_Standalone🌕	check_window_created🌕
install_menu_option_Standalone🌕	install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	launch_from_launch_icon_Standalone🌕	check_window_created🌕
install_menu_option_Standalone🌕	install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	launch_from_chrome_apps_Standalone🌓	check_window_created🌕
install_menu_option_Standalone🌕	install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	launch_from_platform_shortcut_Standalone🌓	check_window_created🌕
create_shortcut_Standalone_Windowed🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	launch_from_menu_option_Standalone🌕	check_window_created🌕
create_shortcut_Standalone_Windowed🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	launch_from_launch_icon_Standalone🌕	check_window_created🌕
create_shortcut_Standalone_Windowed🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	launch_from_chrome_apps_Standalone🌓	check_window_created🌕
create_shortcut_Standalone_Windowed🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	launch_from_platform_shortcut_Standalone🌓	check_window_created🌕
install_omnibox_icon_Standalone🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	launch_from_menu_option_Standalone🌕	check_window_created🌕
install_omnibox_icon_Standalone🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	launch_from_launch_icon_Standalone🌕	check_window_created🌕
install_omnibox_icon_Standalone🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	launch_from_chrome_apps_Standalone🌓	check_window_created🌕
install_omnibox_icon_Standalone🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	launch_from_platform_shortcut_Standalone🌓	check_window_created🌕
install_menu_option_Standalone🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	launch_from_menu_option_Standalone🌕	check_window_created🌕
install_menu_option_Standalone🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	launch_from_launch_icon_Standalone🌕	check_window_created🌕
install_menu_option_Standalone🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	launch_from_chrome_apps_Standalone🌓	check_window_created🌕
install_menu_option_Standalone🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	launch_from_platform_shortcut_Standalone🌓	check_window_created🌕
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	create_shortcut_Standalone_Windowed🌕	check_app_in_list_windowed_Standalone🌓
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	install_omnibox_icon_Standalone🌕	check_app_in_list_windowed_Standalone🌓
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	install_menu_option_Standalone🌕	check_app_in_list_windowed_Standalone🌓
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	create_shortcut_Standalone_Windowed🌕	check_app_in_list_windowed_Standalone🌓
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	install_omnibox_icon_Standalone🌕	check_app_in_list_windowed_Standalone🌓
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	install_menu_option_Standalone🌕	check_app_in_list_windowed_Standalone🌓
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	create_shortcut_Standalone_Windowed🌕	check_platform_shortcut_and_icon_Standalone🌓
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	install_omnibox_icon_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	install_menu_option_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	create_shortcut_Standalone_Windowed🌕	check_platform_shortcut_and_icon_Standalone🌓
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	install_omnibox_icon_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	install_menu_option_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	create_shortcut_Standalone_Windowed🌕	check_window_created🌕
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	install_omnibox_icon_Standalone🌕	check_window_created🌕
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	install_menu_option_Standalone🌕	check_window_created🌕
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	create_shortcut_Standalone_Windowed🌕	check_window_created🌕
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	install_omnibox_icon_Standalone🌕	check_window_created🌕
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	install_menu_option_Standalone🌕	check_window_created🌕
create_shortcut_Standalone_Browser🌕	install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	launch_from_chrome_apps_Standalone🌓	check_tab_created🌕
create_shortcut_Standalone_Browser🌕	install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	launch_from_platform_shortcut_Standalone🌓	check_tab_created🌕
create_shortcut_Standalone_Browser🌕	install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	launch_from_chrome_apps_Standalone🌓	check_tab_created🌕
create_shortcut_Standalone_Browser🌕	install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	launch_from_platform_shortcut_Standalone🌓	check_tab_created🌕
create_shortcut_Standalone_Browser🌕	install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_in_list_tabbed_Standalone🌓
create_shortcut_Standalone_Browser🌕	install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_in_list_tabbed_Standalone🌓
create_shortcut_Standalone_Browser🌕	install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_in_list_tabbed_Standalone🌓
create_shortcut_Standalone_Browser🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_in_list_tabbed_Standalone🌓
create_shortcut_Standalone_Browser🌕	install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_in_list_tabbed_Standalone🌓
create_shortcut_Standalone_Browser🌕	install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_in_list_tabbed_Standalone🌓
create_shortcut_Standalone_Browser🌕	install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_in_list_tabbed_Standalone🌓
create_shortcut_Standalone_Browser🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_in_list_tabbed_Standalone🌓
create_shortcut_Standalone_Browser🌕	install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
create_shortcut_Standalone_Browser🌕	install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
create_shortcut_Standalone_Browser🌕	install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
create_shortcut_Standalone_Browser🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
create_shortcut_Standalone_Browser🌕	install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
create_shortcut_Standalone_Browser🌕	install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
create_shortcut_Standalone_Browser🌕	install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
create_shortcut_Standalone_Browser🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
create_shortcut_Standalone_Windowed🌕	install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_in_list_windowed_Standalone🌓
install_omnibox_icon_Standalone🌕	install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_in_list_windowed_Standalone🌓
install_menu_option_Standalone🌕	install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_in_list_windowed_Standalone🌓
create_shortcut_Standalone_Windowed🌕	install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_in_list_windowed_Standalone🌓
install_omnibox_icon_Standalone🌕	install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_in_list_windowed_Standalone🌓
install_menu_option_Standalone🌕	install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_in_list_windowed_Standalone🌓
create_shortcut_Standalone_Windowed🌕	install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_in_list_windowed_Standalone🌓
install_omnibox_icon_Standalone🌕	install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_in_list_windowed_Standalone🌓
install_menu_option_Standalone🌕	install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_in_list_windowed_Standalone🌓
create_shortcut_Standalone_Windowed🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_in_list_windowed_Standalone🌓
install_omnibox_icon_Standalone🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_in_list_windowed_Standalone🌓
install_menu_option_Standalone🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_in_list_windowed_Standalone🌓
create_shortcut_Standalone_Windowed🌕	install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_in_list_windowed_Standalone🌓
install_omnibox_icon_Standalone🌕	install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_in_list_windowed_Standalone🌓
install_menu_option_Standalone🌕	install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_in_list_windowed_Standalone🌓
create_shortcut_Standalone_Windowed🌕	install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_in_list_windowed_Standalone🌓
install_omnibox_icon_Standalone🌕	install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_in_list_windowed_Standalone🌓
install_menu_option_Standalone🌕	install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_in_list_windowed_Standalone🌓
create_shortcut_Standalone_Windowed🌕	install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_in_list_windowed_Standalone🌓
install_omnibox_icon_Standalone🌕	install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_in_list_windowed_Standalone🌓
install_menu_option_Standalone🌕	install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_in_list_windowed_Standalone🌓
create_shortcut_Standalone_Windowed🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_in_list_windowed_Standalone🌓
install_omnibox_icon_Standalone🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_in_list_windowed_Standalone🌓
install_menu_option_Standalone🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_app_in_list_windowed_Standalone🌓
create_shortcut_Standalone_Windowed🌕	install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
install_omnibox_icon_Standalone🌕	install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
install_menu_option_Standalone🌕	install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
create_shortcut_Standalone_Windowed🌕	install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
install_omnibox_icon_Standalone🌕	install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
install_menu_option_Standalone🌕	install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
create_shortcut_Standalone_Windowed🌕	install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
install_omnibox_icon_Standalone🌕	install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
install_menu_option_Standalone🌕	install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
create_shortcut_Standalone_Windowed🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
install_omnibox_icon_Standalone🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
install_menu_option_Standalone🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
create_shortcut_Standalone_Windowed🌕	install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
install_omnibox_icon_Standalone🌕	install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
install_menu_option_Standalone🌕	install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
create_shortcut_Standalone_Windowed🌕	install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
install_omnibox_icon_Standalone🌕	install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
install_menu_option_Standalone🌕	install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
create_shortcut_Standalone_Windowed🌕	install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
install_omnibox_icon_Standalone🌕	install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
install_menu_option_Standalone🌕	install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
create_shortcut_Standalone_Windowed🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
install_omnibox_icon_Standalone🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
install_menu_option_Standalone🌕	install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	create_shortcut_Standalone_Windowed🌕	uninstall_policy_app_Standalone🌕	check_app_in_list_windowed_Standalone🌓
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	install_omnibox_icon_Standalone🌕	uninstall_policy_app_Standalone🌕	check_app_in_list_windowed_Standalone🌓
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	install_menu_option_Standalone🌕	uninstall_policy_app_Standalone🌕	check_app_in_list_windowed_Standalone🌓
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	create_shortcut_Standalone_Windowed🌕	uninstall_policy_app_Standalone🌕	check_app_in_list_windowed_Standalone🌓
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	install_omnibox_icon_Standalone🌕	uninstall_policy_app_Standalone🌕	check_app_in_list_windowed_Standalone🌓
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	install_menu_option_Standalone🌕	uninstall_policy_app_Standalone🌕	check_app_in_list_windowed_Standalone🌓
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	create_shortcut_Standalone_Windowed🌕	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	install_omnibox_icon_Standalone🌕	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
install_policy_app_Standalone_WithShortcut_Browser_WebApp🌓	install_menu_option_Standalone🌕	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	create_shortcut_Standalone_Windowed🌕	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	install_omnibox_icon_Standalone🌕	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
install_policy_app_Standalone_NoShortcut_Browser_WebApp🌓	install_menu_option_Standalone🌕	uninstall_policy_app_Standalone🌕	check_platform_shortcut_and_icon_Standalone🌓
install_policy_app_StandaloneNotStartUrl_WithShortcut_Windowed_WebShortcut🌓	launch_from_menu_option_StandaloneNotStartUrl🌕	check_app_navigation_StandaloneNotStartUrl🌕
install_policy_app_StandaloneNotStartUrl_WithShortcut_Windowed_WebShortcut🌓	launch_from_launch_icon_StandaloneNotStartUrl🌕	check_app_navigation_StandaloneNotStartUrl🌕
install_policy_app_StandaloneNotStartUrl_WithShortcut_Windowed_WebShortcut🌓	launch_from_chrome_apps_StandaloneNotStartUrl🌓	check_app_navigation_StandaloneNotStartUrl🌕
install_policy_app_StandaloneNotStartUrl_WithShortcut_Windowed_WebShortcut🌓	launch_from_platform_shortcut_StandaloneNotStartUrl🌓	check_app_navigation_StandaloneNotStartUrl🌕
install_policy_app_StandaloneNotStartUrl_WithShortcut_Windowed_WebApp🌓	launch_from_menu_option_Standalone🌕	check_app_navigation_Standalone🌕
install_policy_app_StandaloneNotStartUrl_WithShortcut_Windowed_WebApp🌓	launch_from_launch_icon_Standalone🌕	check_app_navigation_Standalone🌕
install_policy_app_StandaloneNotStartUrl_WithShortcut_Windowed_WebApp🌓	launch_from_chrome_apps_Standalone🌓	check_app_navigation_Standalone🌕
install_policy_app_StandaloneNotStartUrl_WithShortcut_Windowed_WebApp🌓	launch_from_platform_shortcut_Standalone🌓	check_app_navigation_Standalone🌕
install_policy_app_StandaloneNotStartUrl_WithShortcut_Browser_WebApp🌓	launch_from_chrome_apps_Standalone🌓	check_browser_navigation_Standalone🌕
install_policy_app_StandaloneNotStartUrl_WithShortcut_Browser_WebApp🌓	launch_from_platform_shortcut_Standalone🌓	check_browser_navigation_Standalone🌕
install_policy_app_StandaloneNotStartUrl_WithShortcut_Browser_WebShortcut🌓	launch_from_chrome_apps_StandaloneNotStartUrl🌓	check_browser_navigation_StandaloneNotStartUrl🌕
install_policy_app_StandaloneNotStartUrl_WithShortcut_Browser_WebShortcut🌓	launch_from_platform_shortcut_StandaloneNotStartUrl🌓	check_browser_navigation_StandaloneNotStartUrl🌕
install_policy_app_StandaloneNotStartUrl_WithShortcut_Windowed_WebApp🌓	check_app_not_in_list_StandaloneNotStartUrl🌓	check_app_in_list_icon_correct_Standalone🌕
install_policy_app_StandaloneNotStartUrl_WithShortcut_Browser_WebApp🌓	check_app_not_in_list_StandaloneNotStartUrl🌓	check_app_in_list_icon_correct_Standalone🌕
install_policy_app_StandaloneNotStartUrl_WithShortcut_Windowed_WebShortcut🌓	check_app_not_in_list_Standalone🌓	check_app_in_list_icon_correct_StandaloneNotStartUrl🌕
install_policy_app_StandaloneNotStartUrl_WithShortcut_Browser_WebShortcut🌓	check_app_not_in_list_Standalone🌓	check_app_in_list_icon_correct_StandaloneNotStartUrl🌕
create_shortcut_Standalone_Windowed🌕	manifest_update_colors_Standalone🌑	await_manifest_update_Standalone🌑	launch_from_menu_option_Standalone🌑	check_window_color_correct_Standalone🌑
create_shortcut_Standalone_Windowed🌕	manifest_update_colors_Standalone🌑	await_manifest_update_Standalone🌑	launch_from_launch_icon_Standalone🌑	check_window_color_correct_Standalone🌑
create_shortcut_Standalone_Windowed🌕	manifest_update_colors_Standalone🌑	await_manifest_update_Standalone🌑	launch_from_chrome_apps_Standalone🌑	check_window_color_correct_Standalone🌑
create_shortcut_Standalone_Windowed🌕	manifest_update_colors_Standalone🌑	await_manifest_update_Standalone🌑	launch_from_platform_shortcut_Standalone🌑	check_window_color_correct_Standalone🌑
install_omnibox_icon_Standalone🌕	manifest_update_colors_Standalone🌑	await_manifest_update_Standalone🌑	launch_from_menu_option_Standalone🌑	check_window_color_correct_Standalone🌑
install_omnibox_icon_Standalone🌕	manifest_update_colors_Standalone🌑	await_manifest_update_Standalone🌑	launch_from_launch_icon_Standalone🌑	check_window_color_correct_Standalone🌑
install_omnibox_icon_Standalone🌕	manifest_update_colors_Standalone🌑	await_manifest_update_Standalone🌑	launch_from_chrome_apps_Standalone🌑	check_window_color_correct_Standalone🌑
install_omnibox_icon_Standalone🌕	manifest_update_colors_Standalone🌑	await_manifest_update_Standalone🌑	launch_from_platform_shortcut_Standalone🌑	check_window_color_correct_Standalone🌑
install_menu_option_Standalone🌕	manifest_update_colors_Standalone🌑	await_manifest_update_Standalone🌑	launch_from_menu_option_Standalone🌑	check_window_color_correct_Standalone🌑
install_menu_option_Standalone🌕	manifest_update_colors_Standalone🌑	await_manifest_update_Standalone🌑	launch_from_launch_icon_Standalone🌑	check_window_color_correct_Standalone🌑
install_menu_option_Standalone🌕	manifest_update_colors_Standalone🌑	await_manifest_update_Standalone🌑	launch_from_chrome_apps_Standalone🌑	check_window_color_correct_Standalone🌑
install_menu_option_Standalone🌕	manifest_update_colors_Standalone🌑	await_manifest_update_Standalone🌑	launch_from_platform_shortcut_Standalone🌑	check_window_color_correct_Standalone🌑
create_shortcut_Standalone_Windowed🌕	manifest_update_display_Standalone_Browser🌕	await_manifest_update_Standalone🌕	launch_from_menu_option_Standalone🌕	check_tab_not_created🌕
create_shortcut_Standalone_Windowed🌕	manifest_update_display_Standalone_Browser🌕	await_manifest_update_Standalone🌕	launch_from_launch_icon_Standalone🌕	check_tab_not_created🌕
create_shortcut_Standalone_Windowed🌕	manifest_update_display_Standalone_Browser🌕	await_manifest_update_Standalone🌕	launch_from_chrome_apps_Standalone🌓	check_tab_not_created🌕
create_shortcut_Standalone_Windowed🌕	manifest_update_display_Standalone_Browser🌕	await_manifest_update_Standalone🌕	launch_from_platform_shortcut_Standalone🌓	check_tab_not_created🌕
install_omnibox_icon_Standalone🌕	manifest_update_display_Standalone_Browser🌕	await_manifest_update_Standalone🌕	launch_from_menu_option_Standalone🌕	check_tab_not_created🌕
install_omnibox_icon_Standalone🌕	manifest_update_display_Standalone_Browser🌕	await_manifest_update_Standalone🌕	launch_from_launch_icon_Standalone🌕	check_tab_not_created🌕
install_omnibox_icon_Standalone🌕	manifest_update_display_Standalone_Browser🌕	await_manifest_update_Standalone🌕	launch_from_chrome_apps_Standalone🌓	check_tab_not_created🌕
install_omnibox_icon_Standalone🌕	manifest_update_display_Standalone_Browser🌕	await_manifest_update_Standalone🌕	launch_from_platform_shortcut_Standalone🌓	check_tab_not_created🌕
install_menu_option_Standalone🌕	manifest_update_display_Standalone_Browser🌕	await_manifest_update_Standalone🌕	launch_from_menu_option_Standalone🌕	check_tab_not_created🌕
install_menu_option_Standalone🌕	manifest_update_display_Standalone_Browser🌕	await_manifest_update_Standalone🌕	launch_from_launch_icon_Standalone🌕	check_tab_not_created🌕
install_menu_option_Standalone🌕	manifest_update_display_Standalone_Browser🌕	await_manifest_update_Standalone🌕	launch_from_chrome_apps_Standalone🌓	check_tab_not_created🌕
install_menu_option_Standalone🌕	manifest_update_display_Standalone_Browser🌕	await_manifest_update_Standalone🌕	launch_from_platform_shortcut_Standalone🌓	check_tab_not_created🌕
create_shortcut_Standalone_Windowed🌕	manifest_update_display_Standalone_Browser🌕	await_manifest_update_Standalone🌕	launch_from_menu_option_Standalone🌕	check_window_created🌕
create_shortcut_Standalone_Windowed🌕	manifest_update_display_Standalone_Browser🌕	await_manifest_update_Standalone🌕	launch_from_launch_icon_Standalone🌕	check_window_created🌕
create_shortcut_Standalone_Windowed🌕	manifest_update_display_Standalone_Browser🌕	await_manifest_update_Standalone🌕	launch_from_chrome_apps_Standalone🌓	check_window_created🌕
create_shortcut_Standalone_Windowed🌕	manifest_update_display_Standalone_Browser🌕	await_manifest_update_Standalone🌕	launch_from_platform_shortcut_Standalone🌓	check_window_created🌕
install_omnibox_icon_Standalone🌕	manifest_update_display_Standalone_Browser🌕	await_manifest_update_Standalone🌕	launch_from_menu_option_Standalone🌕	check_window_created🌕
install_omnibox_icon_Standalone🌕	manifest_update_display_Standalone_Browser🌕	await_manifest_update_Standalone🌕	launch_from_launch_icon_Standalone🌕	check_window_created🌕
install_omnibox_icon_Standalone🌕	manifest_update_display_Standalone_Browser🌕	await_manifest_update_Standalone🌕	launch_from_chrome_apps_Standalone🌓	check_window_created🌕
install_omnibox_icon_Standalone🌕	manifest_update_display_Standalone_Browser🌕	await_manifest_update_Standalone🌕	launch_from_platform_shortcut_Standalone🌓	check_window_created🌕
install_menu_option_Standalone🌕	manifest_update_display_Standalone_Browser🌕	await_manifest_update_Standalone🌕	launch_from_menu_option_Standalone🌕	check_window_created🌕
install_menu_option_Standalone🌕	manifest_update_display_Standalone_Browser🌕	await_manifest_update_Standalone🌕	launch_from_launch_icon_Standalone🌕	check_window_created🌕
install_menu_option_Standalone🌕	manifest_update_display_Standalone_Browser🌕	await_manifest_update_Standalone🌕	launch_from_chrome_apps_Standalone🌓	check_window_created🌕
install_menu_option_Standalone🌕	manifest_update_display_Standalone_Browser🌕	await_manifest_update_Standalone🌕	launch_from_platform_shortcut_Standalone🌓	check_window_created🌕
create_shortcut_Standalone_Windowed🌕	manifest_update_display_Standalone_Browser🌕	await_manifest_update_Standalone🌕	launch_from_menu_option_Standalone🌕	check_window_display_minimal🌕
create_shortcut_Standalone_Windowed🌕	manifest_update_display_Standalone_Browser🌕	await_manifest_update_Standalone🌕	launch_from_launch_icon_Standalone🌕	check_window_display_minimal🌕
create_shortcut_Standalone_Windowed🌕	manifest_update_display_Standalone_Browser🌕	await_manifest_update_Standalone🌕	launch_from_chrome_apps_Standalone🌓	check_window_display_minimal🌕
create_shortcut_Standalone_Windowed🌕	manifest_update_display_Standalone_Browser🌕	await_manifest_update_Standalone🌕	launch_from_platform_shortcut_Standalone🌓	check_window_display_minimal🌕
install_omnibox_icon_Standalone🌕	manifest_update_display_Standalone_Browser🌕	await_manifest_update_Standalone🌕	launch_from_menu_option_Standalone🌕	check_window_display_minimal🌕
install_omnibox_icon_Standalone🌕	manifest_update_display_Standalone_Browser🌕	await_manifest_update_Standalone🌕	launch_from_launch_icon_Standalone🌕	check_window_display_minimal🌕
install_omnibox_icon_Standalone🌕	manifest_update_display_Standalone_Browser🌕	await_manifest_update_Standalone🌕	launch_from_chrome_apps_Standalone🌓	check_window_display_minimal🌕
install_omnibox_icon_Standalone🌕	manifest_update_display_Standalone_Browser🌕	await_manifest_update_Standalone🌕	launch_from_platform_shortcut_Standalone🌓	check_window_display_minimal🌕
install_menu_option_Standalone🌕	manifest_update_display_Standalone_Browser🌕	await_manifest_update_Standalone🌕	launch_from_menu_option_Standalone🌕	check_window_display_minimal🌕
install_menu_option_Standalone🌕	manifest_update_display_Standalone_Browser🌕	await_manifest_update_Standalone🌕	launch_from_launch_icon_Standalone🌕	check_window_display_minimal🌕
install_menu_option_Standalone🌕	manifest_update_display_Standalone_Browser🌕	await_manifest_update_Standalone🌕	launch_from_chrome_apps_Standalone🌓	check_window_display_minimal🌕
install_menu_option_Standalone🌕	manifest_update_display_Standalone_Browser🌕	await_manifest_update_Standalone🌕	launch_from_platform_shortcut_Standalone🌓	check_window_display_minimal🌕
create_shortcut_Standalone_Windowed🌕	manifest_update_display_Standalone_MinimalUi🌕	await_manifest_update_Standalone🌕	launch_from_menu_option_Standalone🌕	check_window_display_minimal🌕
create_shortcut_Standalone_Windowed🌕	manifest_update_display_Standalone_MinimalUi🌕	await_manifest_update_Standalone🌕	launch_from_launch_icon_Standalone🌕	check_window_display_minimal🌕
create_shortcut_Standalone_Windowed🌕	manifest_update_display_Standalone_MinimalUi🌕	await_manifest_update_Standalone🌕	launch_from_chrome_apps_Standalone🌓	check_window_display_minimal🌕
create_shortcut_Standalone_Windowed🌕	manifest_update_display_Standalone_MinimalUi🌕	await_manifest_update_Standalone🌕	launch_from_platform_shortcut_Standalone🌓	check_window_display_minimal🌕
install_omnibox_icon_Standalone🌕	manifest_update_display_Standalone_MinimalUi🌕	await_manifest_update_Standalone🌕	launch_from_menu_option_Standalone🌕	check_window_display_minimal🌕
install_omnibox_icon_Standalone🌕	manifest_update_display_Standalone_MinimalUi🌕	await_manifest_update_Standalone🌕	launch_from_launch_icon_Standalone🌕	check_window_display_minimal🌕
install_omnibox_icon_Standalone🌕	manifest_update_display_Standalone_MinimalUi🌕	await_manifest_update_Standalone🌕	launch_from_chrome_apps_Standalone🌓	check_window_display_minimal🌕
install_omnibox_icon_Standalone🌕	manifest_update_display_Standalone_MinimalUi🌕	await_manifest_update_Standalone🌕	launch_from_platform_shortcut_Standalone🌓	check_window_display_minimal🌕
install_menu_option_Standalone🌕	manifest_update_display_Standalone_MinimalUi🌕	await_manifest_update_Standalone🌕	launch_from_menu_option_Standalone🌕	check_window_display_minimal🌕
install_menu_option_Standalone🌕	manifest_update_display_Standalone_MinimalUi🌕	await_manifest_update_Standalone🌕	launch_from_launch_icon_Standalone🌕	check_window_display_minimal🌕
install_menu_option_Standalone🌕	manifest_update_display_Standalone_MinimalUi🌕	await_manifest_update_Standalone🌕	launch_from_chrome_apps_Standalone🌓	check_window_display_minimal🌕
install_menu_option_Standalone🌕	manifest_update_display_Standalone_MinimalUi🌕	await_manifest_update_Standalone🌕	launch_from_platform_shortcut_Standalone🌓	check_window_display_minimal🌕
create_shortcut_StandaloneNestedA_Windowed🌕	manifest_update_scope_to_StandaloneNestedA_Standalone🌕	await_manifest_update_StandaloneNestedA🌕	launch_from_platform_shortcut_StandaloneNestedA🌓	navigate_browser_Standalone🌕	check_install_icon_not_shown🌕
install_omnibox_icon_StandaloneNestedA🌕	manifest_update_scope_to_StandaloneNestedA_Standalone🌕	await_manifest_update_StandaloneNestedA🌕	launch_from_platform_shortcut_StandaloneNestedA🌓	navigate_browser_Standalone🌕	check_install_icon_not_shown🌕
install_menu_option_StandaloneNestedA🌕	manifest_update_scope_to_StandaloneNestedA_Standalone🌕	await_manifest_update_StandaloneNestedA🌕	launch_from_platform_shortcut_StandaloneNestedA🌓	navigate_browser_Standalone🌕	check_install_icon_not_shown🌕
create_shortcut_StandaloneNestedA_Windowed🌕	manifest_update_scope_to_StandaloneNestedA_Standalone🌕	await_manifest_update_StandaloneNestedA🌕	launch_from_platform_shortcut_StandaloneNestedA🌓	navigate_browser_Standalone🌕	check_launch_icon_shown🌕
install_omnibox_icon_StandaloneNestedA🌕	manifest_update_scope_to_StandaloneNestedA_Standalone🌕	await_manifest_update_StandaloneNestedA🌕	launch_from_platform_shortcut_StandaloneNestedA🌓	navigate_browser_Standalone🌕	check_launch_icon_shown🌕
install_menu_option_StandaloneNestedA🌕	manifest_update_scope_to_StandaloneNestedA_Standalone🌕	await_manifest_update_StandaloneNestedA🌕	launch_from_platform_shortcut_StandaloneNestedA🌓	navigate_browser_Standalone🌕	check_launch_icon_shown🌕
create_shortcut_StandaloneNestedA_Windowed🌕	manifest_update_scope_to_StandaloneNestedA_Standalone🌕	await_manifest_update_StandaloneNestedA🌕	launch_from_menu_option_StandaloneNestedA🌕	navigate_pwa_StandaloneNestedA_StandaloneNestedB🌕	check_no_toolbar🌕
create_shortcut_StandaloneNestedA_Windowed🌕	manifest_update_scope_to_StandaloneNestedA_Standalone🌕	await_manifest_update_StandaloneNestedA🌕	launch_from_launch_icon_StandaloneNestedA🌕	navigate_pwa_StandaloneNestedA_StandaloneNestedB🌕	check_no_toolbar🌕
create_shortcut_StandaloneNestedA_Windowed🌕	manifest_update_scope_to_StandaloneNestedA_Standalone🌕	await_manifest_update_StandaloneNestedA🌕	launch_from_chrome_apps_StandaloneNestedA🌓	navigate_pwa_StandaloneNestedA_StandaloneNestedB🌕	check_no_toolbar🌕
create_shortcut_StandaloneNestedA_Windowed🌕	manifest_update_scope_to_StandaloneNestedA_Standalone🌕	await_manifest_update_StandaloneNestedA🌕	launch_from_platform_shortcut_StandaloneNestedA🌓	navigate_pwa_StandaloneNestedA_StandaloneNestedB🌕	check_no_toolbar🌕
install_omnibox_icon_StandaloneNestedA🌕	manifest_update_scope_to_StandaloneNestedA_Standalone🌕	await_manifest_update_StandaloneNestedA🌕	launch_from_menu_option_StandaloneNestedA🌕	navigate_pwa_StandaloneNestedA_StandaloneNestedB🌕	check_no_toolbar🌕
install_omnibox_icon_StandaloneNestedA🌕	manifest_update_scope_to_StandaloneNestedA_Standalone🌕	await_manifest_update_StandaloneNestedA🌕	launch_from_launch_icon_StandaloneNestedA🌕	navigate_pwa_StandaloneNestedA_StandaloneNestedB🌕	check_no_toolbar🌕
install_omnibox_icon_StandaloneNestedA🌕	manifest_update_scope_to_StandaloneNestedA_Standalone🌕	await_manifest_update_StandaloneNestedA🌕	launch_from_chrome_apps_StandaloneNestedA🌓	navigate_pwa_StandaloneNestedA_StandaloneNestedB🌕	check_no_toolbar🌕
install_omnibox_icon_StandaloneNestedA🌕	manifest_update_scope_to_StandaloneNestedA_Standalone🌕	await_manifest_update_StandaloneNestedA🌕	launch_from_platform_shortcut_StandaloneNestedA🌓	navigate_pwa_StandaloneNestedA_StandaloneNestedB🌕	check_no_toolbar🌕
install_menu_option_StandaloneNestedA🌕	manifest_update_scope_to_StandaloneNestedA_Standalone🌕	await_manifest_update_StandaloneNestedA🌕	launch_from_menu_option_StandaloneNestedA🌕	navigate_pwa_StandaloneNestedA_StandaloneNestedB🌕	check_no_toolbar🌕
install_menu_option_StandaloneNestedA🌕	manifest_update_scope_to_StandaloneNestedA_Standalone🌕	await_manifest_update_StandaloneNestedA🌕	launch_from_launch_icon_StandaloneNestedA🌕	navigate_pwa_StandaloneNestedA_StandaloneNestedB🌕	check_no_toolbar🌕
install_menu_option_StandaloneNestedA🌕	manifest_update_scope_to_StandaloneNestedA_Standalone🌕	await_manifest_update_StandaloneNestedA🌕	launch_from_chrome_apps_StandaloneNestedA🌓	navigate_pwa_StandaloneNestedA_StandaloneNestedB🌕	check_no_toolbar🌕
install_menu_option_StandaloneNestedA🌕	manifest_update_scope_to_StandaloneNestedA_Standalone🌕	await_manifest_update_StandaloneNestedA🌕	launch_from_platform_shortcut_StandaloneNestedA🌓	navigate_pwa_StandaloneNestedA_StandaloneNestedB🌕	check_no_toolbar🌕
create_shortcut_StandaloneNestedA_Windowed🌕	manifest_update_scope_to_StandaloneNestedA_Standalone🌕	await_manifest_update_StandaloneNestedA🌕	navigate_browser_StandaloneNestedB🌕	check_install_icon_not_shown🌕
install_omnibox_icon_StandaloneNestedA🌕	manifest_update_scope_to_StandaloneNestedA_Standalone🌕	await_manifest_update_StandaloneNestedA🌕	navigate_browser_StandaloneNestedB🌕	check_install_icon_not_shown🌕
install_menu_option_StandaloneNestedA🌕	manifest_update_scope_to_StandaloneNestedA_Standalone🌕	await_manifest_update_StandaloneNestedA🌕	navigate_browser_StandaloneNestedB🌕	check_install_icon_not_shown🌕
create_shortcut_StandaloneNestedA_Windowed🌕	manifest_update_scope_to_StandaloneNestedA_Standalone🌕	await_manifest_update_StandaloneNestedA🌕	navigate_browser_StandaloneNestedB🌕	check_launch_icon_shown🌕
install_omnibox_icon_StandaloneNestedA🌕	manifest_update_scope_to_StandaloneNestedA_Standalone🌕	await_manifest_update_StandaloneNestedA🌕	navigate_browser_StandaloneNestedB🌕	check_launch_icon_shown🌕
install_menu_option_StandaloneNestedA🌕	manifest_update_scope_to_StandaloneNestedA_Standalone🌕	await_manifest_update_StandaloneNestedA🌕	navigate_browser_StandaloneNestedB🌕	check_launch_icon_shown🌕
create_shortcut_StandaloneNestedA_Windowed🌕	manifest_update_scope_to_StandaloneNestedA_Standalone🌕	await_manifest_update_StandaloneNestedA🌕	navigate_browser_StandaloneNestedA🌕	check_install_icon_not_shown🌕
install_omnibox_icon_StandaloneNestedA🌕	manifest_update_scope_to_StandaloneNestedA_Standalone🌕	await_manifest_update_StandaloneNestedA🌕	navigate_browser_StandaloneNestedA🌕	check_install_icon_not_shown🌕
install_menu_option_StandaloneNestedA🌕	manifest_update_scope_to_StandaloneNestedA_Standalone🌕	await_manifest_update_StandaloneNestedA🌕	navigate_browser_StandaloneNestedA🌕	check_install_icon_not_shown🌕
create_shortcut_StandaloneNestedA_Windowed🌕	manifest_update_scope_to_StandaloneNestedA_Standalone🌕	await_manifest_update_StandaloneNestedA🌕	navigate_browser_StandaloneNestedA🌕	check_launch_icon_shown🌕
install_omnibox_icon_StandaloneNestedA🌕	manifest_update_scope_to_StandaloneNestedA_Standalone🌕	await_manifest_update_StandaloneNestedA🌕	navigate_browser_StandaloneNestedA🌕	check_launch_icon_shown🌕
install_menu_option_StandaloneNestedA🌕	manifest_update_scope_to_StandaloneNestedA_Standalone🌕	await_manifest_update_StandaloneNestedA🌕	navigate_browser_StandaloneNestedA🌕	check_launch_icon_shown🌕
switch_incognito_profile🌕	navigate_browser_Standalone🌕	check_create_shortcut_not_shown🌑
switch_incognito_profile🌕	navigate_browser_NotPromotable🌕	check_create_shortcut_not_shown🌑
navigate_crashed_url🌑	check_create_shortcut_not_shown🌑
navigate_crashed_url🌑	check_install_icon_not_shown🌑
navigate_notfound_url🌕	check_create_shortcut_not_shown🌑
navigate_notfound_url🌕	check_install_icon_not_shown🌕
navigate_browser_Standalone🌕	check_create_shortcut_shown🌑
navigate_browser_StandaloneNestedA🌕	check_install_icon_shown🌕
navigate_browser_NotPromotable🌕	check_create_shortcut_shown🌑
navigate_browser_NotPromotable🌕	check_install_icon_not_shown🌕
create_shortcut_StandaloneNestedA_Windowed🌕	navigate_browser_NotInstalled🌕	check_install_icon_shown🌕
install_omnibox_icon_StandaloneNestedA🌕	navigate_browser_NotInstalled🌕	check_install_icon_shown🌕
install_policy_app_StandaloneNestedA_WithShortcut_Windowed_WebApp🌓	navigate_browser_NotInstalled🌕	check_install_icon_shown🌕
install_policy_app_StandaloneNestedA_NoShortcut_Windowed_WebApp🌓	navigate_browser_NotInstalled🌕	check_install_icon_shown🌕
install_menu_option_StandaloneNestedA🌕	navigate_browser_NotInstalled🌕	check_install_icon_shown🌕
create_shortcut_StandaloneNestedA_Windowed🌕	navigate_browser_NotInstalled🌕	check_launch_icon_not_shown🌕
install_omnibox_icon_StandaloneNestedA🌕	navigate_browser_NotInstalled🌕	check_launch_icon_not_shown🌕
install_policy_app_StandaloneNestedA_WithShortcut_Windowed_WebApp🌓	navigate_browser_NotInstalled🌕	check_launch_icon_not_shown🌕
install_policy_app_StandaloneNestedA_NoShortcut_Windowed_WebApp🌓	navigate_browser_NotInstalled🌕	check_launch_icon_not_shown🌕
install_menu_option_StandaloneNestedA🌕	navigate_browser_NotInstalled🌕	check_launch_icon_not_shown🌕
create_shortcut_Standalone_Windowed🌕	navigate_browser_StandaloneNestedA🌕	check_install_icon_not_shown🌕
install_omnibox_icon_Standalone🌕	navigate_browser_StandaloneNestedA🌕	check_install_icon_not_shown🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	navigate_browser_StandaloneNestedA🌕	check_install_icon_not_shown🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	navigate_browser_StandaloneNestedA🌑	check_install_icon_not_shown🌑
install_menu_option_Standalone🌕	navigate_browser_StandaloneNestedA🌕	check_install_icon_not_shown🌕
create_shortcut_Standalone_Windowed🌕	navigate_browser_StandaloneNestedA🌕	check_launch_icon_shown🌕
install_omnibox_icon_Standalone🌕	navigate_browser_StandaloneNestedA🌕	check_launch_icon_shown🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	navigate_browser_StandaloneNestedA🌕	check_launch_icon_shown🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	navigate_browser_StandaloneNestedA🌑	check_launch_icon_shown🌑
install_menu_option_Standalone🌕	navigate_browser_StandaloneNestedA🌕	check_launch_icon_shown🌕
create_shortcut_Standalone_Windowed🌕	navigate_browser_MinimalUi🌕	check_install_icon_shown🌕
install_omnibox_icon_Standalone🌕	navigate_browser_MinimalUi🌕	check_install_icon_shown🌕
install_menu_option_Standalone🌕	navigate_browser_MinimalUi🌕	check_install_icon_shown🌕
create_shortcut_Standalone_Windowed🌕	navigate_browser_MinimalUi🌕	check_launch_icon_not_shown🌕
install_omnibox_icon_Standalone🌕	navigate_browser_MinimalUi🌕	check_launch_icon_not_shown🌕
install_menu_option_Standalone🌕	navigate_browser_MinimalUi🌕	check_launch_icon_not_shown🌕
create_shortcut_Standalone_Windowed🌕	navigate_pwa_Standalone_MinimalUi🌕	check_app_title_Standalone_StandaloneOriginal🌑
install_omnibox_icon_Standalone🌕	navigate_pwa_Standalone_MinimalUi🌕	check_app_title_Standalone_StandaloneOriginal🌑
install_menu_option_Standalone🌕	navigate_pwa_Standalone_MinimalUi🌕	check_app_title_Standalone_StandaloneOriginal🌑
create_shortcut_Standalone_Windowed🌕	switch_incognito_profile🌕	navigate_browser_Standalone🌕	check_launch_icon_not_shown🌕
install_omnibox_icon_Standalone🌕	switch_incognito_profile🌕	navigate_browser_Standalone🌕	check_launch_icon_not_shown🌕
install_menu_option_Standalone🌕	switch_incognito_profile🌕	navigate_browser_Standalone🌕	check_launch_icon_not_shown🌕
create_shortcut_Standalone_Windowed🌕	set_open_in_tab_Standalone🌓	check_app_in_list_tabbed_Standalone🌓
install_omnibox_icon_Standalone🌕	set_open_in_tab_Standalone🌓	check_app_in_list_tabbed_Standalone🌓
install_menu_option_Standalone🌕	set_open_in_tab_Standalone🌓	check_app_in_list_tabbed_Standalone🌓
create_shortcut_Standalone_Windowed🌕	set_open_in_tab_Standalone🌓	navigate_browser_Standalone🌕	check_install_icon_shown🌕
install_omnibox_icon_Standalone🌕	set_open_in_tab_Standalone🌓	navigate_browser_Standalone🌕	check_install_icon_shown🌕
install_menu_option_Standalone🌕	set_open_in_tab_Standalone🌓	navigate_browser_Standalone🌕	check_install_icon_shown🌕
create_shortcut_Standalone_Browser🌕	set_open_in_window_Standalone🌓	check_app_in_list_windowed_Standalone🌓
create_shortcut_Standalone_Browser🌕	set_open_in_window_Standalone🌓	navigate_browser_Standalone🌕	check_install_icon_not_shown🌕
create_shortcut_Standalone_Browser🌕	set_open_in_window_Standalone🌓	navigate_browser_Standalone🌕	check_launch_icon_shown🌕
create_shortcut_Wco_Windowed🌕	check_window_controls_overlay_toggle_Wco_Shown🌕
install_omnibox_icon_Wco🌕	check_window_controls_overlay_toggle_Wco_Shown🌕
install_policy_app_Wco_WithShortcut_Windowed_WebApp🌓	check_window_controls_overlay_toggle_Wco_Shown🌕
install_policy_app_Wco_NoShortcut_Windowed_WebApp🌓	check_window_controls_overlay_toggle_Wco_Shown🌕
install_menu_option_Wco🌕	check_window_controls_overlay_toggle_Wco_Shown🌕
create_shortcut_Standalone_Windowed🌕	check_window_controls_overlay_toggle_Standalone_NotShown🌕
install_omnibox_icon_Standalone🌕	check_window_controls_overlay_toggle_Standalone_NotShown🌕
install_policy_app_Standalone_WithShortcut_Windowed_WebApp🌓	check_window_controls_overlay_toggle_Standalone_NotShown🌕
install_policy_app_Standalone_NoShortcut_Windowed_WebApp🌓	check_window_controls_overlay_toggle_Standalone_NotShown🌕
install_menu_option_Standalone🌕	check_window_controls_overlay_toggle_Standalone_NotShown🌕
create_shortcut_Wco_Windowed🌕	enable_window_controls_overlay_Wco🌕	check_window_controls_overlay_Wco_On🌕
install_omnibox_icon_Wco🌕	enable_window_controls_overlay_Wco🌕	check_window_controls_overlay_Wco_On🌕
install_policy_app_Wco_WithShortcut_Windowed_WebApp🌓	enable_window_controls_overlay_Wco🌕	check_window_controls_overlay_Wco_On🌕
install_policy_app_Wco_NoShortcut_Windowed_WebApp🌓	enable_window_controls_overlay_Wco🌕	check_window_controls_overlay_Wco_On🌕
install_menu_option_Wco🌕	enable_window_controls_overlay_Wco🌕	check_window_controls_overlay_Wco_On🌕
create_shortcut_Wco_Windowed🌕	enable_window_controls_overlay_Wco🌕	check_window_controls_overlay_toggle_Wco_Shown🌕
install_omnibox_icon_Wco🌕	enable_window_controls_overlay_Wco🌕	check_window_controls_overlay_toggle_Wco_Shown🌕
install_policy_app_Wco_WithShortcut_Windowed_WebApp🌓	enable_window_controls_overlay_Wco🌕	check_window_controls_overlay_toggle_Wco_Shown🌕
install_policy_app_Wco_NoShortcut_Windowed_WebApp🌓	enable_window_controls_overlay_Wco🌕	check_window_controls_overlay_toggle_Wco_Shown🌕
install_menu_option_Wco🌕	enable_window_controls_overlay_Wco🌕	check_window_controls_overlay_toggle_Wco_Shown🌕
create_shortcut_Wco_Windowed🌕	enable_window_controls_overlay_Wco🌕	disable_window_controls_overlay_Wco🌕	check_window_controls_overlay_Wco_Off🌕
install_omnibox_icon_Wco🌕	enable_window_controls_overlay_Wco🌕	disable_window_controls_overlay_Wco🌕	check_window_controls_overlay_Wco_Off🌕
install_policy_app_Wco_WithShortcut_Windowed_WebApp🌓	enable_window_controls_overlay_Wco🌕	disable_window_controls_overlay_Wco🌕	check_window_controls_overlay_Wco_Off🌕
install_policy_app_Wco_NoShortcut_Windowed_WebApp🌓	enable_window_controls_overlay_Wco🌕	disable_window_controls_overlay_Wco🌕	check_window_controls_overlay_Wco_Off🌕
install_menu_option_Wco🌕	enable_window_controls_overlay_Wco🌕	disable_window_controls_overlay_Wco🌕	check_window_controls_overlay_Wco_Off🌕
create_shortcut_Wco_Windowed🌕	enable_window_controls_overlay_Wco🌕	disable_window_controls_overlay_Wco🌕	check_window_controls_overlay_toggle_Wco_Shown🌕
install_omnibox_icon_Wco🌕	enable_window_controls_overlay_Wco🌕	disable_window_controls_overlay_Wco🌕	check_window_controls_overlay_toggle_Wco_Shown🌕
install_policy_app_Wco_WithShortcut_Windowed_WebApp🌓	enable_window_controls_overlay_Wco🌕	disable_window_controls_overlay_Wco🌕	check_window_controls_overlay_toggle_Wco_Shown🌕
install_policy_app_Wco_NoShortcut_Windowed_WebApp🌓	enable_window_controls_overlay_Wco🌕	disable_window_controls_overlay_Wco🌕	check_window_controls_overlay_toggle_Wco_Shown🌕
install_menu_option_Wco🌕	enable_window_controls_overlay_Wco🌕	disable_window_controls_overlay_Wco🌕	check_window_controls_overlay_toggle_Wco_Shown🌕
create_shortcut_Wco_Windowed🌕	enable_window_controls_overlay_Wco🌕	launch_from_menu_option_Wco🌕	check_window_controls_overlay_Wco_On🌕
create_shortcut_Wco_Windowed🌕	enable_window_controls_overlay_Wco🌕	launch_from_launch_icon_Wco🌕	check_window_controls_overlay_Wco_On🌕
create_shortcut_Wco_Windowed🌕	enable_window_controls_overlay_Wco🌕	launch_from_chrome_apps_Wco🌓	check_window_controls_overlay_Wco_On🌕
create_shortcut_Wco_Windowed🌕	enable_window_controls_overlay_Wco🌕	launch_from_platform_shortcut_Wco🌓	check_window_controls_overlay_Wco_On🌕
install_omnibox_icon_Wco🌕	enable_window_controls_overlay_Wco🌕	launch_from_menu_option_Wco🌕	check_window_controls_overlay_Wco_On🌕
install_omnibox_icon_Wco🌕	enable_window_controls_overlay_Wco🌕	launch_from_launch_icon_Wco🌕	check_window_controls_overlay_Wco_On🌕
install_omnibox_icon_Wco🌕	enable_window_controls_overlay_Wco🌕	launch_from_chrome_apps_Wco🌓	check_window_controls_overlay_Wco_On🌕
install_omnibox_icon_Wco🌕	enable_window_controls_overlay_Wco🌕	launch_from_platform_shortcut_Wco🌓	check_window_controls_overlay_Wco_On🌕
install_policy_app_Wco_WithShortcut_Windowed_WebApp🌓	enable_window_controls_overlay_Wco🌕	launch_from_menu_option_Wco🌕	check_window_controls_overlay_Wco_On🌕
install_policy_app_Wco_WithShortcut_Windowed_WebApp🌓	enable_window_controls_overlay_Wco🌕	launch_from_launch_icon_Wco🌕	check_window_controls_overlay_Wco_On🌕
install_policy_app_Wco_WithShortcut_Windowed_WebApp🌓	enable_window_controls_overlay_Wco🌕	launch_from_chrome_apps_Wco🌓	check_window_controls_overlay_Wco_On🌕
install_policy_app_Wco_WithShortcut_Windowed_WebApp🌓	enable_window_controls_overlay_Wco🌕	launch_from_platform_shortcut_Wco🌓	check_window_controls_overlay_Wco_On🌕
install_policy_app_Wco_NoShortcut_Windowed_WebApp🌓	enable_window_controls_overlay_Wco🌕	launch_from_menu_option_Wco🌕	check_window_controls_overlay_Wco_On🌕
install_policy_app_Wco_NoShortcut_Windowed_WebApp🌓	enable_window_controls_overlay_Wco🌕	launch_from_launch_icon_Wco🌕	check_window_controls_overlay_Wco_On🌕
install_policy_app_Wco_NoShortcut_Windowed_WebApp🌓	enable_window_controls_overlay_Wco🌕	launch_from_chrome_apps_Wco🌓	check_window_controls_overlay_Wco_On🌕
install_policy_app_Wco_NoShortcut_Windowed_WebApp🌓	enable_window_controls_overlay_Wco🌕	launch_from_platform_shortcut_Wco🌓	check_window_controls_overlay_Wco_On🌕
install_menu_option_Wco🌕	enable_window_controls_overlay_Wco🌕	launch_from_menu_option_Wco🌕	check_window_controls_overlay_Wco_On🌕
install_menu_option_Wco🌕	enable_window_controls_overlay_Wco🌕	launch_from_launch_icon_Wco🌕	check_window_controls_overlay_Wco_On🌕
install_menu_option_Wco🌕	enable_window_controls_overlay_Wco🌕	launch_from_chrome_apps_Wco🌓	check_window_controls_overlay_Wco_On🌕
install_menu_option_Wco🌕	enable_window_controls_overlay_Wco🌕	launch_from_platform_shortcut_Wco🌓	check_window_controls_overlay_Wco_On🌕
create_shortcut_MinimalUi_Windowed🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_menu_option_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
create_shortcut_MinimalUi_Windowed🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_launch_icon_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
create_shortcut_MinimalUi_Windowed🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_chrome_apps_MinimalUi🌓	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
create_shortcut_MinimalUi_Windowed🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_platform_shortcut_MinimalUi🌓	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_omnibox_icon_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_menu_option_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_omnibox_icon_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_launch_icon_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_omnibox_icon_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_chrome_apps_MinimalUi🌓	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_omnibox_icon_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_platform_shortcut_MinimalUi🌓	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_menu_option_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_launch_icon_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_chrome_apps_MinimalUi🌓	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_platform_shortcut_MinimalUi🌓	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_menu_option_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_launch_icon_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_chrome_apps_MinimalUi🌓	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_platform_shortcut_MinimalUi🌓	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_menu_option_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_menu_option_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_menu_option_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_launch_icon_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_menu_option_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_chrome_apps_MinimalUi🌓	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_menu_option_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_platform_shortcut_MinimalUi🌓	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
create_shortcut_MinimalUi_Windowed🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_menu_option_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
create_shortcut_MinimalUi_Windowed🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_launch_icon_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
create_shortcut_MinimalUi_Windowed🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_chrome_apps_MinimalUi🌓	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
create_shortcut_MinimalUi_Windowed🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_platform_shortcut_MinimalUi🌓	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_omnibox_icon_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_menu_option_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_omnibox_icon_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_launch_icon_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_omnibox_icon_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_chrome_apps_MinimalUi🌓	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_omnibox_icon_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_platform_shortcut_MinimalUi🌓	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_menu_option_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_launch_icon_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_chrome_apps_MinimalUi🌓	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_platform_shortcut_MinimalUi🌓	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_menu_option_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_launch_icon_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_chrome_apps_MinimalUi🌓	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_platform_shortcut_MinimalUi🌓	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_menu_option_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_menu_option_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_menu_option_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_launch_icon_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_menu_option_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_chrome_apps_MinimalUi🌓	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_menu_option_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_platform_shortcut_MinimalUi🌓	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
create_shortcut_MinimalUi_Windowed🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_menu_option_MinimalUi🌕	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_MinimalUi_On🌕
create_shortcut_MinimalUi_Windowed🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_launch_icon_MinimalUi🌕	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_MinimalUi_On🌕
create_shortcut_MinimalUi_Windowed🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_chrome_apps_MinimalUi🌓	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_MinimalUi_On🌕
create_shortcut_MinimalUi_Windowed🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_platform_shortcut_MinimalUi🌓	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_MinimalUi_On🌕
install_omnibox_icon_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_menu_option_MinimalUi🌕	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_MinimalUi_On🌕
install_omnibox_icon_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_launch_icon_MinimalUi🌕	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_MinimalUi_On🌕
install_omnibox_icon_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_chrome_apps_MinimalUi🌓	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_MinimalUi_On🌕
install_omnibox_icon_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_platform_shortcut_MinimalUi🌓	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_MinimalUi_On🌕
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_menu_option_MinimalUi🌕	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_MinimalUi_On🌕
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_launch_icon_MinimalUi🌕	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_MinimalUi_On🌕
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_chrome_apps_MinimalUi🌓	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_MinimalUi_On🌕
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_platform_shortcut_MinimalUi🌓	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_MinimalUi_On🌕
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_menu_option_MinimalUi🌕	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_MinimalUi_On🌕
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_launch_icon_MinimalUi🌕	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_MinimalUi_On🌕
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_chrome_apps_MinimalUi🌓	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_MinimalUi_On🌕
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_platform_shortcut_MinimalUi🌓	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_MinimalUi_On🌕
install_menu_option_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_menu_option_MinimalUi🌕	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_MinimalUi_On🌕
install_menu_option_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_launch_icon_MinimalUi🌕	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_MinimalUi_On🌕
install_menu_option_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_chrome_apps_MinimalUi🌓	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_MinimalUi_On🌕
install_menu_option_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_platform_shortcut_MinimalUi🌓	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_MinimalUi_On🌕
create_shortcut_MinimalUi_Windowed🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_menu_option_MinimalUi🌕	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
create_shortcut_MinimalUi_Windowed🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_launch_icon_MinimalUi🌕	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
create_shortcut_MinimalUi_Windowed🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_chrome_apps_MinimalUi🌓	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
create_shortcut_MinimalUi_Windowed🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_platform_shortcut_MinimalUi🌓	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_omnibox_icon_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_menu_option_MinimalUi🌕	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_omnibox_icon_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_launch_icon_MinimalUi🌕	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_omnibox_icon_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_chrome_apps_MinimalUi🌓	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_omnibox_icon_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_platform_shortcut_MinimalUi🌓	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_menu_option_MinimalUi🌕	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_launch_icon_MinimalUi🌕	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_chrome_apps_MinimalUi🌓	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_platform_shortcut_MinimalUi🌓	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_menu_option_MinimalUi🌕	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_launch_icon_MinimalUi🌕	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_chrome_apps_MinimalUi🌓	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_platform_shortcut_MinimalUi🌓	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_menu_option_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_menu_option_MinimalUi🌕	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_menu_option_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_launch_icon_MinimalUi🌕	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_menu_option_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_chrome_apps_MinimalUi🌓	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_menu_option_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_platform_shortcut_MinimalUi🌓	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
create_shortcut_MinimalUi_Windowed🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_menu_option_MinimalUi🌕	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
create_shortcut_MinimalUi_Windowed🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_launch_icon_MinimalUi🌕	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
create_shortcut_MinimalUi_Windowed🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_chrome_apps_MinimalUi🌓	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
create_shortcut_MinimalUi_Windowed🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_platform_shortcut_MinimalUi🌓	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_omnibox_icon_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_menu_option_MinimalUi🌕	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_omnibox_icon_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_launch_icon_MinimalUi🌕	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_omnibox_icon_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_chrome_apps_MinimalUi🌓	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_omnibox_icon_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_platform_shortcut_MinimalUi🌓	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_menu_option_MinimalUi🌕	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_launch_icon_MinimalUi🌕	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_chrome_apps_MinimalUi🌓	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_platform_shortcut_MinimalUi🌓	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_menu_option_MinimalUi🌕	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_launch_icon_MinimalUi🌕	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_chrome_apps_MinimalUi🌓	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_platform_shortcut_MinimalUi🌓	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_menu_option_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_menu_option_MinimalUi🌕	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_menu_option_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_launch_icon_MinimalUi🌕	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_menu_option_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_chrome_apps_MinimalUi🌓	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
install_menu_option_MinimalUi🌕	manifest_update_display_MinimalUi_Wco🌕	await_manifest_update_MinimalUi🌕	launch_from_platform_shortcut_MinimalUi🌓	enable_window_controls_overlay_MinimalUi🌕	check_window_controls_overlay_toggle_MinimalUi_Shown🌕
create_shortcut_Wco_Windowed🌕	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_menu_option_Wco🌕	check_window_controls_overlay_toggle_Wco_NotShown🌕
create_shortcut_Wco_Windowed🌕	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_launch_icon_Wco🌕	check_window_controls_overlay_toggle_Wco_NotShown🌕
create_shortcut_Wco_Windowed🌕	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_chrome_apps_Wco🌓	check_window_controls_overlay_toggle_Wco_NotShown🌕
create_shortcut_Wco_Windowed🌕	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_platform_shortcut_Wco🌓	check_window_controls_overlay_toggle_Wco_NotShown🌕
install_omnibox_icon_Wco🌕	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_menu_option_Wco🌕	check_window_controls_overlay_toggle_Wco_NotShown🌕
install_omnibox_icon_Wco🌕	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_launch_icon_Wco🌕	check_window_controls_overlay_toggle_Wco_NotShown🌕
install_omnibox_icon_Wco🌕	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_chrome_apps_Wco🌓	check_window_controls_overlay_toggle_Wco_NotShown🌕
install_omnibox_icon_Wco🌕	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_platform_shortcut_Wco🌓	check_window_controls_overlay_toggle_Wco_NotShown🌕
install_policy_app_Wco_WithShortcut_Windowed_WebApp🌓	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_menu_option_Wco🌕	check_window_controls_overlay_toggle_Wco_NotShown🌕
install_policy_app_Wco_WithShortcut_Windowed_WebApp🌓	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_launch_icon_Wco🌕	check_window_controls_overlay_toggle_Wco_NotShown🌕
install_policy_app_Wco_WithShortcut_Windowed_WebApp🌓	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_chrome_apps_Wco🌓	check_window_controls_overlay_toggle_Wco_NotShown🌕
install_policy_app_Wco_WithShortcut_Windowed_WebApp🌓	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_platform_shortcut_Wco🌓	check_window_controls_overlay_toggle_Wco_NotShown🌕
install_policy_app_Wco_NoShortcut_Windowed_WebApp🌓	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_menu_option_Wco🌕	check_window_controls_overlay_toggle_Wco_NotShown🌕
install_policy_app_Wco_NoShortcut_Windowed_WebApp🌓	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_launch_icon_Wco🌕	check_window_controls_overlay_toggle_Wco_NotShown🌕
install_policy_app_Wco_NoShortcut_Windowed_WebApp🌓	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_chrome_apps_Wco🌓	check_window_controls_overlay_toggle_Wco_NotShown🌕
install_policy_app_Wco_NoShortcut_Windowed_WebApp🌓	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_platform_shortcut_Wco🌓	check_window_controls_overlay_toggle_Wco_NotShown🌕
install_menu_option_Wco🌕	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_menu_option_Wco🌕	check_window_controls_overlay_toggle_Wco_NotShown🌕
install_menu_option_Wco🌕	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_launch_icon_Wco🌕	check_window_controls_overlay_toggle_Wco_NotShown🌕
install_menu_option_Wco🌕	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_chrome_apps_Wco🌓	check_window_controls_overlay_toggle_Wco_NotShown🌕
install_menu_option_Wco🌕	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_platform_shortcut_Wco🌓	check_window_controls_overlay_toggle_Wco_NotShown🌕
create_shortcut_Wco_Windowed🌕	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_menu_option_Wco🌕	check_window_controls_overlay_Wco_Off🌕
create_shortcut_Wco_Windowed🌕	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_launch_icon_Wco🌕	check_window_controls_overlay_Wco_Off🌕
create_shortcut_Wco_Windowed🌕	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_chrome_apps_Wco🌓	check_window_controls_overlay_Wco_Off🌕
create_shortcut_Wco_Windowed🌕	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_platform_shortcut_Wco🌓	check_window_controls_overlay_Wco_Off🌕
install_omnibox_icon_Wco🌕	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_menu_option_Wco🌕	check_window_controls_overlay_Wco_Off🌕
install_omnibox_icon_Wco🌕	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_launch_icon_Wco🌕	check_window_controls_overlay_Wco_Off🌕
install_omnibox_icon_Wco🌕	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_chrome_apps_Wco🌓	check_window_controls_overlay_Wco_Off🌕
install_omnibox_icon_Wco🌕	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_platform_shortcut_Wco🌓	check_window_controls_overlay_Wco_Off🌕
install_policy_app_Wco_WithShortcut_Windowed_WebApp🌓	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_menu_option_Wco🌕	check_window_controls_overlay_Wco_Off🌕
install_policy_app_Wco_WithShortcut_Windowed_WebApp🌓	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_launch_icon_Wco🌕	check_window_controls_overlay_Wco_Off🌕
install_policy_app_Wco_WithShortcut_Windowed_WebApp🌓	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_chrome_apps_Wco🌓	check_window_controls_overlay_Wco_Off🌕
install_policy_app_Wco_WithShortcut_Windowed_WebApp🌓	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_platform_shortcut_Wco🌓	check_window_controls_overlay_Wco_Off🌕
install_policy_app_Wco_NoShortcut_Windowed_WebApp🌓	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_menu_option_Wco🌕	check_window_controls_overlay_Wco_Off🌕
install_policy_app_Wco_NoShortcut_Windowed_WebApp🌓	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_launch_icon_Wco🌕	check_window_controls_overlay_Wco_Off🌕
install_policy_app_Wco_NoShortcut_Windowed_WebApp🌓	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_chrome_apps_Wco🌓	check_window_controls_overlay_Wco_Off🌕
install_policy_app_Wco_NoShortcut_Windowed_WebApp🌓	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_platform_shortcut_Wco🌓	check_window_controls_overlay_Wco_Off🌕
install_menu_option_Wco🌕	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_menu_option_Wco🌕	check_window_controls_overlay_Wco_Off🌕
install_menu_option_Wco🌕	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_launch_icon_Wco🌕	check_window_controls_overlay_Wco_Off🌕
install_menu_option_Wco🌕	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_chrome_apps_Wco🌓	check_window_controls_overlay_Wco_Off🌕
install_menu_option_Wco🌕	manifest_update_display_Wco_Standalone🌕	await_manifest_update_Wco🌕	launch_from_platform_shortcut_Wco🌓	check_window_controls_overlay_Wco_Off🌕
create_shortcut_MinimalUi_Windowed🌕	check_site_handles_file_MinimalUi_Txt🌑	check_site_handles_file_MinimalUi_Png🌑
create_shortcut_MinimalUi_Browser🌕	check_site_handles_file_MinimalUi_Txt🌑	check_site_handles_file_MinimalUi_Png🌑
install_omnibox_icon_MinimalUi🌕	check_site_handles_file_MinimalUi_Txt🌑	check_site_handles_file_MinimalUi_Png🌑
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	check_site_handles_file_MinimalUi_Txt🌑	check_site_handles_file_MinimalUi_Png🌑
install_policy_app_MinimalUi_WithShortcut_Browser_WebApp🌓	check_site_handles_file_MinimalUi_Txt🌑	check_site_handles_file_MinimalUi_Png🌑
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	check_site_handles_file_MinimalUi_Txt🌑	check_site_handles_file_MinimalUi_Png🌑
install_policy_app_MinimalUi_NoShortcut_Browser_WebApp🌓	check_site_handles_file_MinimalUi_Txt🌑	check_site_handles_file_MinimalUi_Png🌑
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	check_site_handles_file_MinimalUi_Txt🌑	check_site_handles_file_MinimalUi_Png🌑
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	check_site_handles_file_MinimalUi_Txt🌑	check_site_handles_file_MinimalUi_Png🌑
install_policy_app_MinimalUi_WithShortcut_Browser_WebApp🌓	check_site_handles_file_MinimalUi_Txt🌑	check_site_handles_file_MinimalUi_Png🌑
install_policy_app_MinimalUi_NoShortcut_Browser_WebApp🌓	check_site_handles_file_MinimalUi_Txt🌑	check_site_handles_file_MinimalUi_Png🌑
install_menu_option_MinimalUi🌕	check_site_handles_file_MinimalUi_Txt🌑	check_site_handles_file_MinimalUi_Png🌑
create_shortcut_MinimalUi_Windowed🌕	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
create_shortcut_MinimalUi_Browser🌕	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_omnibox_icon_MinimalUi🌕	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_WithShortcut_Browser_WebApp🌓	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_NoShortcut_Browser_WebApp🌓	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_WithShortcut_Browser_WebApp🌓	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_NoShortcut_Browser_WebApp🌓	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_menu_option_MinimalUi🌕	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
create_shortcut_MinimalUi_Windowed🌕	launch_file_OneTextFile🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_One🌑	check_files_loaded_in_site_MinimalUi_OneTextFile🌑
create_shortcut_MinimalUi_Browser🌕	launch_file_OneTextFile🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_One🌑	check_files_loaded_in_site_MinimalUi_OneTextFile🌑
install_omnibox_icon_MinimalUi🌕	launch_file_OneTextFile🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_One🌑	check_files_loaded_in_site_MinimalUi_OneTextFile🌑
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_One🌑	check_files_loaded_in_site_MinimalUi_OneTextFile🌑
install_policy_app_MinimalUi_WithShortcut_Browser_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_One🌑	check_files_loaded_in_site_MinimalUi_OneTextFile🌑
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_One🌑	check_files_loaded_in_site_MinimalUi_OneTextFile🌑
install_policy_app_MinimalUi_NoShortcut_Browser_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_One🌑	check_files_loaded_in_site_MinimalUi_OneTextFile🌑
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_One🌑	check_files_loaded_in_site_MinimalUi_OneTextFile🌑
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_One🌑	check_files_loaded_in_site_MinimalUi_OneTextFile🌑
install_policy_app_MinimalUi_WithShortcut_Browser_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_One🌑	check_files_loaded_in_site_MinimalUi_OneTextFile🌑
install_policy_app_MinimalUi_NoShortcut_Browser_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_One🌑	check_files_loaded_in_site_MinimalUi_OneTextFile🌑
install_menu_option_MinimalUi🌕	launch_file_OneTextFile🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_One🌑	check_files_loaded_in_site_MinimalUi_OneTextFile🌑
create_shortcut_MinimalUi_Windowed🌕	launch_file_MultipleTextFiles🌑	check_file_handling_dialog_Shown🌑
create_shortcut_MinimalUi_Browser🌕	launch_file_MultipleTextFiles🌑	check_file_handling_dialog_Shown🌑
install_omnibox_icon_MinimalUi🌕	launch_file_MultipleTextFiles🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	launch_file_MultipleTextFiles🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_WithShortcut_Browser_WebApp🌓	launch_file_MultipleTextFiles🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	launch_file_MultipleTextFiles🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_NoShortcut_Browser_WebApp🌓	launch_file_MultipleTextFiles🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	launch_file_MultipleTextFiles🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	launch_file_MultipleTextFiles🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_WithShortcut_Browser_WebApp🌓	launch_file_MultipleTextFiles🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_NoShortcut_Browser_WebApp🌓	launch_file_MultipleTextFiles🌑	check_file_handling_dialog_Shown🌑
install_menu_option_MinimalUi🌕	launch_file_MultipleTextFiles🌑	check_file_handling_dialog_Shown🌑
create_shortcut_MinimalUi_Windowed🌕	launch_file_MultipleTextFiles🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_One🌑	check_files_loaded_in_site_MinimalUi_MultipleTextFiles🌑
create_shortcut_MinimalUi_Browser🌕	launch_file_MultipleTextFiles🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_One🌑	check_files_loaded_in_site_MinimalUi_MultipleTextFiles🌑
install_omnibox_icon_MinimalUi🌕	launch_file_MultipleTextFiles🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_One🌑	check_files_loaded_in_site_MinimalUi_MultipleTextFiles🌑
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	launch_file_MultipleTextFiles🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_One🌑	check_files_loaded_in_site_MinimalUi_MultipleTextFiles🌑
install_policy_app_MinimalUi_WithShortcut_Browser_WebApp🌓	launch_file_MultipleTextFiles🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_One🌑	check_files_loaded_in_site_MinimalUi_MultipleTextFiles🌑
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	launch_file_MultipleTextFiles🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_One🌑	check_files_loaded_in_site_MinimalUi_MultipleTextFiles🌑
install_policy_app_MinimalUi_NoShortcut_Browser_WebApp🌓	launch_file_MultipleTextFiles🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_One🌑	check_files_loaded_in_site_MinimalUi_MultipleTextFiles🌑
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	launch_file_MultipleTextFiles🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_One🌑	check_files_loaded_in_site_MinimalUi_MultipleTextFiles🌑
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	launch_file_MultipleTextFiles🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_One🌑	check_files_loaded_in_site_MinimalUi_MultipleTextFiles🌑
install_policy_app_MinimalUi_WithShortcut_Browser_WebApp🌓	launch_file_MultipleTextFiles🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_One🌑	check_files_loaded_in_site_MinimalUi_MultipleTextFiles🌑
install_policy_app_MinimalUi_NoShortcut_Browser_WebApp🌓	launch_file_MultipleTextFiles🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_One🌑	check_files_loaded_in_site_MinimalUi_MultipleTextFiles🌑
install_menu_option_MinimalUi🌕	launch_file_MultipleTextFiles🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_One🌑	check_files_loaded_in_site_MinimalUi_MultipleTextFiles🌑
create_shortcut_MinimalUi_Windowed🌕	launch_file_OnePngFile🌑	check_file_handling_dialog_Shown🌑
create_shortcut_MinimalUi_Browser🌕	launch_file_OnePngFile🌑	check_file_handling_dialog_Shown🌑
install_omnibox_icon_MinimalUi🌕	launch_file_OnePngFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	launch_file_OnePngFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_WithShortcut_Browser_WebApp🌓	launch_file_OnePngFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	launch_file_OnePngFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_NoShortcut_Browser_WebApp🌓	launch_file_OnePngFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	launch_file_OnePngFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	launch_file_OnePngFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_WithShortcut_Browser_WebApp🌓	launch_file_OnePngFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_NoShortcut_Browser_WebApp🌓	launch_file_OnePngFile🌑	check_file_handling_dialog_Shown🌑
install_menu_option_MinimalUi🌕	launch_file_OnePngFile🌑	check_file_handling_dialog_Shown🌑
create_shortcut_MinimalUi_Windowed🌕	launch_file_OnePngFile🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_One🌑	check_files_loaded_in_site_MinimalUi_OnePngFile🌑
create_shortcut_MinimalUi_Browser🌕	launch_file_OnePngFile🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_One🌑	check_files_loaded_in_site_MinimalUi_OnePngFile🌑
install_omnibox_icon_MinimalUi🌕	launch_file_OnePngFile🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_One🌑	check_files_loaded_in_site_MinimalUi_OnePngFile🌑
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	launch_file_OnePngFile🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_One🌑	check_files_loaded_in_site_MinimalUi_OnePngFile🌑
install_policy_app_MinimalUi_WithShortcut_Browser_WebApp🌓	launch_file_OnePngFile🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_One🌑	check_files_loaded_in_site_MinimalUi_OnePngFile🌑
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	launch_file_OnePngFile🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_One🌑	check_files_loaded_in_site_MinimalUi_OnePngFile🌑
install_policy_app_MinimalUi_NoShortcut_Browser_WebApp🌓	launch_file_OnePngFile🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_One🌑	check_files_loaded_in_site_MinimalUi_OnePngFile🌑
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	launch_file_OnePngFile🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_One🌑	check_files_loaded_in_site_MinimalUi_OnePngFile🌑
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	launch_file_OnePngFile🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_One🌑	check_files_loaded_in_site_MinimalUi_OnePngFile🌑
install_policy_app_MinimalUi_WithShortcut_Browser_WebApp🌓	launch_file_OnePngFile🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_One🌑	check_files_loaded_in_site_MinimalUi_OnePngFile🌑
install_policy_app_MinimalUi_NoShortcut_Browser_WebApp🌓	launch_file_OnePngFile🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_One🌑	check_files_loaded_in_site_MinimalUi_OnePngFile🌑
install_menu_option_MinimalUi🌕	launch_file_OnePngFile🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_One🌑	check_files_loaded_in_site_MinimalUi_OnePngFile🌑
create_shortcut_MinimalUi_Windowed🌕	launch_file_MultiplePngFiles🌑	check_file_handling_dialog_Shown🌑
create_shortcut_MinimalUi_Browser🌕	launch_file_MultiplePngFiles🌑	check_file_handling_dialog_Shown🌑
install_omnibox_icon_MinimalUi🌕	launch_file_MultiplePngFiles🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	launch_file_MultiplePngFiles🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_WithShortcut_Browser_WebApp🌓	launch_file_MultiplePngFiles🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	launch_file_MultiplePngFiles🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_NoShortcut_Browser_WebApp🌓	launch_file_MultiplePngFiles🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	launch_file_MultiplePngFiles🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	launch_file_MultiplePngFiles🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_WithShortcut_Browser_WebApp🌓	launch_file_MultiplePngFiles🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_NoShortcut_Browser_WebApp🌓	launch_file_MultiplePngFiles🌑	check_file_handling_dialog_Shown🌑
install_menu_option_MinimalUi🌕	launch_file_MultiplePngFiles🌑	check_file_handling_dialog_Shown🌑
create_shortcut_MinimalUi_Windowed🌕	launch_file_MultiplePngFiles🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_Two🌑	check_files_loaded_in_site_MinimalUi_MultiplePngFiles🌑
create_shortcut_MinimalUi_Browser🌕	launch_file_MultiplePngFiles🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_Two🌑	check_files_loaded_in_site_MinimalUi_MultiplePngFiles🌑
install_omnibox_icon_MinimalUi🌕	launch_file_MultiplePngFiles🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_Two🌑	check_files_loaded_in_site_MinimalUi_MultiplePngFiles🌑
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	launch_file_MultiplePngFiles🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_Two🌑	check_files_loaded_in_site_MinimalUi_MultiplePngFiles🌑
install_policy_app_MinimalUi_WithShortcut_Browser_WebApp🌓	launch_file_MultiplePngFiles🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_Two🌑	check_files_loaded_in_site_MinimalUi_MultiplePngFiles🌑
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	launch_file_MultiplePngFiles🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_Two🌑	check_files_loaded_in_site_MinimalUi_MultiplePngFiles🌑
install_policy_app_MinimalUi_NoShortcut_Browser_WebApp🌓	launch_file_MultiplePngFiles🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_Two🌑	check_files_loaded_in_site_MinimalUi_MultiplePngFiles🌑
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	launch_file_MultiplePngFiles🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_Two🌑	check_files_loaded_in_site_MinimalUi_MultiplePngFiles🌑
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	launch_file_MultiplePngFiles🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_Two🌑	check_files_loaded_in_site_MinimalUi_MultiplePngFiles🌑
install_policy_app_MinimalUi_WithShortcut_Browser_WebApp🌓	launch_file_MultiplePngFiles🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_Two🌑	check_files_loaded_in_site_MinimalUi_MultiplePngFiles🌑
install_policy_app_MinimalUi_NoShortcut_Browser_WebApp🌓	launch_file_MultiplePngFiles🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_Two🌑	check_files_loaded_in_site_MinimalUi_MultiplePngFiles🌑
install_menu_option_MinimalUi🌕	launch_file_MultiplePngFiles🌑	file_handling_dialog_Allow_AskAgain🌑	check_pwa_window_created_MinimalUi_Two🌑	check_files_loaded_in_site_MinimalUi_MultiplePngFiles🌑
create_shortcut_MinimalUi_Windowed🌕	launch_file_OneTextFile🌑	file_handling_dialog_Allow_Remember🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_NotShown🌑	check_pwa_window_created_MinimalUi_One🌑
create_shortcut_MinimalUi_Browser🌕	launch_file_OneTextFile🌑	file_handling_dialog_Allow_Remember🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_NotShown🌑	check_pwa_window_created_MinimalUi_One🌑
install_omnibox_icon_MinimalUi🌕	launch_file_OneTextFile🌑	file_handling_dialog_Allow_Remember🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_NotShown🌑	check_pwa_window_created_MinimalUi_One🌑
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Allow_Remember🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_NotShown🌑	check_pwa_window_created_MinimalUi_One🌑
install_policy_app_MinimalUi_WithShortcut_Browser_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Allow_Remember🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_NotShown🌑	check_pwa_window_created_MinimalUi_One🌑
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Allow_Remember🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_NotShown🌑	check_pwa_window_created_MinimalUi_One🌑
install_policy_app_MinimalUi_NoShortcut_Browser_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Allow_Remember🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_NotShown🌑	check_pwa_window_created_MinimalUi_One🌑
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Allow_Remember🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_NotShown🌑	check_pwa_window_created_MinimalUi_One🌑
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Allow_Remember🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_NotShown🌑	check_pwa_window_created_MinimalUi_One🌑
install_policy_app_MinimalUi_WithShortcut_Browser_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Allow_Remember🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_NotShown🌑	check_pwa_window_created_MinimalUi_One🌑
install_policy_app_MinimalUi_NoShortcut_Browser_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Allow_Remember🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_NotShown🌑	check_pwa_window_created_MinimalUi_One🌑
install_menu_option_MinimalUi🌕	launch_file_OneTextFile🌑	file_handling_dialog_Allow_Remember🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_NotShown🌑	check_pwa_window_created_MinimalUi_One🌑
create_shortcut_MinimalUi_Windowed🌕	launch_file_OneTextFile🌑	file_handling_dialog_Allow_AskAgain🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
create_shortcut_MinimalUi_Browser🌕	launch_file_OneTextFile🌑	file_handling_dialog_Allow_AskAgain🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_omnibox_icon_MinimalUi🌕	launch_file_OneTextFile🌑	file_handling_dialog_Allow_AskAgain🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Allow_AskAgain🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_WithShortcut_Browser_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Allow_AskAgain🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Allow_AskAgain🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_NoShortcut_Browser_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Allow_AskAgain🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Allow_AskAgain🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Allow_AskAgain🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_WithShortcut_Browser_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Allow_AskAgain🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_NoShortcut_Browser_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Allow_AskAgain🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_menu_option_MinimalUi🌕	launch_file_OneTextFile🌑	file_handling_dialog_Allow_AskAgain🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
create_shortcut_MinimalUi_Windowed🌕	launch_file_OneTextFile🌑	file_handling_dialog_Deny_AskAgain🌑	check_window_not_created🌑	check_site_handles_file_MinimalUi_Txt🌑	check_site_handles_file_MinimalUi_Png🌑
create_shortcut_MinimalUi_Browser🌕	launch_file_OneTextFile🌑	file_handling_dialog_Deny_AskAgain🌑	check_window_not_created🌑	check_site_handles_file_MinimalUi_Txt🌑	check_site_handles_file_MinimalUi_Png🌑
install_omnibox_icon_MinimalUi🌕	launch_file_OneTextFile🌑	file_handling_dialog_Deny_AskAgain🌑	check_window_not_created🌑	check_site_handles_file_MinimalUi_Txt🌑	check_site_handles_file_MinimalUi_Png🌑
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Deny_AskAgain🌑	check_window_not_created🌑	check_site_handles_file_MinimalUi_Txt🌑	check_site_handles_file_MinimalUi_Png🌑
install_policy_app_MinimalUi_WithShortcut_Browser_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Deny_AskAgain🌑	check_window_not_created🌑	check_site_handles_file_MinimalUi_Txt🌑	check_site_handles_file_MinimalUi_Png🌑
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Deny_AskAgain🌑	check_window_not_created🌑	check_site_handles_file_MinimalUi_Txt🌑	check_site_handles_file_MinimalUi_Png🌑
install_policy_app_MinimalUi_NoShortcut_Browser_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Deny_AskAgain🌑	check_window_not_created🌑	check_site_handles_file_MinimalUi_Txt🌑	check_site_handles_file_MinimalUi_Png🌑
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Deny_AskAgain🌑	check_window_not_created🌑	check_site_handles_file_MinimalUi_Txt🌑	check_site_handles_file_MinimalUi_Png🌑
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Deny_AskAgain🌑	check_window_not_created🌑	check_site_handles_file_MinimalUi_Txt🌑	check_site_handles_file_MinimalUi_Png🌑
install_policy_app_MinimalUi_WithShortcut_Browser_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Deny_AskAgain🌑	check_window_not_created🌑	check_site_handles_file_MinimalUi_Txt🌑	check_site_handles_file_MinimalUi_Png🌑
install_policy_app_MinimalUi_NoShortcut_Browser_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Deny_AskAgain🌑	check_window_not_created🌑	check_site_handles_file_MinimalUi_Txt🌑	check_site_handles_file_MinimalUi_Png🌑
install_menu_option_MinimalUi🌕	launch_file_OneTextFile🌑	file_handling_dialog_Deny_AskAgain🌑	check_window_not_created🌑	check_site_handles_file_MinimalUi_Txt🌑	check_site_handles_file_MinimalUi_Png🌑
create_shortcut_MinimalUi_Windowed🌕	launch_file_OneTextFile🌑	file_handling_dialog_Deny_AskAgain🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
create_shortcut_MinimalUi_Browser🌕	launch_file_OneTextFile🌑	file_handling_dialog_Deny_AskAgain🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_omnibox_icon_MinimalUi🌕	launch_file_OneTextFile🌑	file_handling_dialog_Deny_AskAgain🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Deny_AskAgain🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_WithShortcut_Browser_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Deny_AskAgain🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Deny_AskAgain🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_NoShortcut_Browser_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Deny_AskAgain🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Deny_AskAgain🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Deny_AskAgain🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_WithShortcut_Browser_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Deny_AskAgain🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_NoShortcut_Browser_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Deny_AskAgain🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_menu_option_MinimalUi🌕	launch_file_OneTextFile🌑	file_handling_dialog_Deny_AskAgain🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
create_shortcut_MinimalUi_Windowed🌕	launch_file_OneTextFile🌑	file_handling_dialog_Deny_Remember🌑	check_window_not_created🌑	check_site_not_handles_file_MinimalUi_Txt🌑	check_site_not_handles_file_MinimalUi_Png🌑
create_shortcut_MinimalUi_Browser🌕	launch_file_OneTextFile🌑	file_handling_dialog_Deny_Remember🌑	check_window_not_created🌑	check_site_not_handles_file_MinimalUi_Txt🌑	check_site_not_handles_file_MinimalUi_Png🌑
install_omnibox_icon_MinimalUi🌕	launch_file_OneTextFile🌑	file_handling_dialog_Deny_Remember🌑	check_window_not_created🌑	check_site_not_handles_file_MinimalUi_Txt🌑	check_site_not_handles_file_MinimalUi_Png🌑
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Deny_Remember🌑	check_window_not_created🌑	check_site_not_handles_file_MinimalUi_Txt🌑	check_site_not_handles_file_MinimalUi_Png🌑
install_policy_app_MinimalUi_WithShortcut_Browser_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Deny_Remember🌑	check_window_not_created🌑	check_site_not_handles_file_MinimalUi_Txt🌑	check_site_not_handles_file_MinimalUi_Png🌑
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Deny_Remember🌑	check_window_not_created🌑	check_site_not_handles_file_MinimalUi_Txt🌑	check_site_not_handles_file_MinimalUi_Png🌑
install_policy_app_MinimalUi_NoShortcut_Browser_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Deny_Remember🌑	check_window_not_created🌑	check_site_not_handles_file_MinimalUi_Txt🌑	check_site_not_handles_file_MinimalUi_Png🌑
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Deny_Remember🌑	check_window_not_created🌑	check_site_not_handles_file_MinimalUi_Txt🌑	check_site_not_handles_file_MinimalUi_Png🌑
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Deny_Remember🌑	check_window_not_created🌑	check_site_not_handles_file_MinimalUi_Txt🌑	check_site_not_handles_file_MinimalUi_Png🌑
install_policy_app_MinimalUi_WithShortcut_Browser_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Deny_Remember🌑	check_window_not_created🌑	check_site_not_handles_file_MinimalUi_Txt🌑	check_site_not_handles_file_MinimalUi_Png🌑
install_policy_app_MinimalUi_NoShortcut_Browser_WebApp🌓	launch_file_OneTextFile🌑	file_handling_dialog_Deny_Remember🌑	check_window_not_created🌑	check_site_not_handles_file_MinimalUi_Txt🌑	check_site_not_handles_file_MinimalUi_Png🌑
install_menu_option_MinimalUi🌕	launch_file_OneTextFile🌑	file_handling_dialog_Deny_Remember🌑	check_window_not_created🌑	check_site_not_handles_file_MinimalUi_Txt🌑	check_site_not_handles_file_MinimalUi_Png🌑
create_shortcut_MinimalUi_Windowed🌕	add_file_handling_policy_approval_MinimalUi🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_NotShown🌑	check_pwa_window_created_MinimalUi_One🌑
create_shortcut_MinimalUi_Browser🌕	add_file_handling_policy_approval_MinimalUi🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_NotShown🌑	check_pwa_window_created_MinimalUi_One🌑
install_omnibox_icon_MinimalUi🌕	add_file_handling_policy_approval_MinimalUi🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_NotShown🌑	check_pwa_window_created_MinimalUi_One🌑
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	add_file_handling_policy_approval_MinimalUi🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_NotShown🌑	check_pwa_window_created_MinimalUi_One🌑
install_policy_app_MinimalUi_WithShortcut_Browser_WebApp🌓	add_file_handling_policy_approval_MinimalUi🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_NotShown🌑	check_pwa_window_created_MinimalUi_One🌑
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	add_file_handling_policy_approval_MinimalUi🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_NotShown🌑	check_pwa_window_created_MinimalUi_One🌑
install_policy_app_MinimalUi_NoShortcut_Browser_WebApp🌓	add_file_handling_policy_approval_MinimalUi🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_NotShown🌑	check_pwa_window_created_MinimalUi_One🌑
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	add_file_handling_policy_approval_MinimalUi🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_NotShown🌑	check_pwa_window_created_MinimalUi_One🌑
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	add_file_handling_policy_approval_MinimalUi🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_NotShown🌑	check_pwa_window_created_MinimalUi_One🌑
install_policy_app_MinimalUi_WithShortcut_Browser_WebApp🌓	add_file_handling_policy_approval_MinimalUi🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_NotShown🌑	check_pwa_window_created_MinimalUi_One🌑
install_policy_app_MinimalUi_NoShortcut_Browser_WebApp🌓	add_file_handling_policy_approval_MinimalUi🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_NotShown🌑	check_pwa_window_created_MinimalUi_One🌑
install_menu_option_MinimalUi🌕	add_file_handling_policy_approval_MinimalUi🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_NotShown🌑	check_pwa_window_created_MinimalUi_One🌑
create_shortcut_MinimalUi_Windowed🌕	add_file_handling_policy_approval_MinimalUi🌑	remove_file_handling_policy_approval_MinimalUi🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
create_shortcut_MinimalUi_Browser🌕	add_file_handling_policy_approval_MinimalUi🌑	remove_file_handling_policy_approval_MinimalUi🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_omnibox_icon_MinimalUi🌕	add_file_handling_policy_approval_MinimalUi🌑	remove_file_handling_policy_approval_MinimalUi🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	add_file_handling_policy_approval_MinimalUi🌑	remove_file_handling_policy_approval_MinimalUi🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_WithShortcut_Browser_WebApp🌓	add_file_handling_policy_approval_MinimalUi🌑	remove_file_handling_policy_approval_MinimalUi🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	add_file_handling_policy_approval_MinimalUi🌑	remove_file_handling_policy_approval_MinimalUi🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_NoShortcut_Browser_WebApp🌓	add_file_handling_policy_approval_MinimalUi🌑	remove_file_handling_policy_approval_MinimalUi🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_WithShortcut_Windowed_WebApp🌓	add_file_handling_policy_approval_MinimalUi🌑	remove_file_handling_policy_approval_MinimalUi🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_NoShortcut_Windowed_WebApp🌓	add_file_handling_policy_approval_MinimalUi🌑	remove_file_handling_policy_approval_MinimalUi🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_WithShortcut_Browser_WebApp🌓	add_file_handling_policy_approval_MinimalUi🌑	remove_file_handling_policy_approval_MinimalUi🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_policy_app_MinimalUi_NoShortcut_Browser_WebApp🌓	add_file_handling_policy_approval_MinimalUi🌑	remove_file_handling_policy_approval_MinimalUi🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
install_menu_option_MinimalUi🌕	add_file_handling_policy_approval_MinimalUi🌑	remove_file_handling_policy_approval_MinimalUi🌑	launch_file_OneTextFile🌑	check_file_handling_dialog_Shown🌑
