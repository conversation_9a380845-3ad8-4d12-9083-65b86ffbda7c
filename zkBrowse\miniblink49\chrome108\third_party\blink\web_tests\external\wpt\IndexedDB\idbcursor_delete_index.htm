<!DOCTYPE html>
<title>IDBCursor.delete() - index - remove a record from the object store</title>
<link rel="author" title="Microsoft" href="http://www.microsoft.com">
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<script src="resources/support.js"></script>

<script>

    var db,
      count = 0,
      t = async_test(),
      records = [ { pKey: "primaryKey_0", iKey: "indexKey_0" },
                  { pKey: "primaryKey_1", iKey: "indexKey_1" } ];

    var open_rq = createdb(t);
    open_rq.onupgradeneeded = function(e) {
        db = e.target.result;

        var objStore = db.createObjectStore("test", { keyPath: "pKey" });
        objStore.createIndex("index", "iKey");

        for (var i = 0; i < records.length; i++)
            objStore.add(records[i]);
    };

    open_rq.onsuccess = t.step_func(CursorDeleteRecord);


    function CursorDeleteRecord(e) {
        var txn = db.transaction("test", "readwrite"),
          cursor_rq = txn.objectStore("test")
                         .index("index")
                         .openCursor();

        cursor_rq.onsuccess = t.step_func(function(e) {
            var cursor = e.target.result;

            assert_true(cursor instanceof IDBCursor, "cursor exist");
            cursor.delete();
        });

        txn.oncomplete = t.step_func(VerifyRecordWasDeleted);
    }


    function VerifyRecordWasDeleted(e) {
        var cursor_rq = db.transaction("test")
                          .objectStore("test")
                          .openCursor();

        cursor_rq.onsuccess = t.step_func(function(e) {
            var cursor = e.target.result;

            if (!cursor) {
                assert_equals(count, 1, 'count');
                t.done();
            }

            assert_equals(cursor.value.pKey, records[1].pKey);
            assert_equals(cursor.value.iKey, records[1].iKey);
            cursor.continue();
            count++;
        });
    }

</script>

<div id="log"></div>
