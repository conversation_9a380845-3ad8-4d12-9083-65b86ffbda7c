<!DOCTYPE html>
<body>
<script>
if (window.testRunner)
    testRunner.waitUntilDone();
var img = new Image();
img.onload = function() {
    setTimeout(function() {
        if (window.testRunner)
            testRunner.notifyDone();
    }, 100);
}
img.src = "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='100' height='100'><rect width='100' height='100' fill='red'><animate attributeName='fill' from='green' to='green' dur='10s'/></rect></svg>";
document.body.appendChild(img);
</script>
