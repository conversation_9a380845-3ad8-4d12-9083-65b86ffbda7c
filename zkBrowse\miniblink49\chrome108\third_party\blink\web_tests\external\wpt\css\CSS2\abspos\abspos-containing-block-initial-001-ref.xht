<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml"><head>
<title>CSS Test: Test for containing block for absolutely positioned elements being initial containing block</title>
<link rel="author" title="<PERSON>" href="mailto:<EMAIL>" />
<link rel="author" title="Mozilla Corporation" href="http://mozilla.com/" />
<style type="text/css">
body { margin:0; }
.pos { position:absolute; width:100px; height:100px; }
</style>
</head>
<body>
<div style="height:10000px; margin:0; margin:10px; border:20px solid black; padding:30px;"></div>
<script type="text/javascript">
window.scrollTo(0,50);
</script>
<div style="top:0; left:60px; background:yellow;" class="pos"></div>
<div style="right:0; top:60px; background:orange;" class="pos"></div>
<div style="bottom:0; left:60px; background:brown;" class="pos"></div>
<div style="left:0; top:60px; background:pink;" class="pos"></div>



</body></html>