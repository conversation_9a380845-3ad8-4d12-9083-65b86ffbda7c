﻿<?xml version="1.0" encoding="utf-8"?>
<VisualStudioToolFile
	Name="installer archive"
	Version="8.00"
	>
	<Rules>
		<CustomBuildRule
			Name="Create Installer Archive"
			DisplayName="Create Installer Archive"
			CommandLine="$(SolutionDir)..\third_party\python_24\python.exe $(SolutionDir)tools\build\win\create_installer_archive.py --output_dir=&quot;$(OutDir)&quot; --input_file=&quot;$(InputPath)&quot; --distribution=$(CHROMIUM_BUILD) --enable_hidpi=$(ENABLE_HIDPI) --enable_metro=$(ENABLE_METRO) [LastChromeInstaller] [SkipRebuildArchive] [SetupExeFormat] [DiffAlgorithm]"
			Outputs="$(OutDir)/$(InputName).7z;$(OutDir)/$(InputName).packed.7z;$(OutDir)/setup.ex_;$(OutDir)/packed_files.txt;"
			AdditionalDependencies="$(SolutionDir)\tools\build\win\create_installer_archive.py;$(OutDir)\chrome.exe;$(OutDir)\crash_reporter.exe;$(OutDir)\chrome.dll;$(OutDir)\locales\en-US.dll;$(OutDir)\icudt38.dll"
			FileExtensions="*.release"
			ExecutionDescription="create installer archive"
			>
			<Properties>
				<StringProperty
					Name="LastChromeInstaller"
					DisplayName="Last Chrome Installer Directory"
					Description="Directory where old version of installer is present (setup.exe and chrome.7z)"
					Switch="--last_chrome_installer=&quot;[value]&quot;"
				/>
				<StringProperty
					Name="SkipRebuildArchive"
					DisplayName="Skip Rebuilding Archive"
					Description="Skips rebuilding chrome.7z archive if it already exists"
					Switch="--skip_rebuild_archive=&quot;[value]&quot;"
				/>
				<StringProperty
					Name="SetupExeFormat"
					DisplayName="Setup.exe format"
					Description="The format that should be used to bundle setup.exe. Possible values - {COMPRESSED|DIFF|FULL}."
					Switch="--setup_exe_format=&quot;[value]&quot;"
				/>
				<StringProperty
					Name="DiffAlgorithm"
					DisplayName="Differential Algorithm"
					Description="Differential algorithm to use when generating differential patch. Possible values - {BSDIFF|COURGETTE}."
					Switch="--diff_algorithm=&quot;[value]&quot;"
				/>
			</Properties>
		</CustomBuildRule>
	</Rules>
</VisualStudioToolFile>
