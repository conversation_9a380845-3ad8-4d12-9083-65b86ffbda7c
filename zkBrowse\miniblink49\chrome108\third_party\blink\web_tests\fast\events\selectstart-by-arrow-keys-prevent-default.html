<!DOCTYPE html>
<html>
<body>
<p>This test ensures selectstart event fires when selection is created by arrow key and script can prevent the selection change.</p>
If running this test manually, click on the div ("Hello World") and try to select the text using arrow keys.<br>
Expected result: SelectStart event will fire when user starts extending the selection, but due to script preventDefault it prevents the selection change.<br>
<div contenteditable>Hello World</div>
<script>


var selectStartCount = 0;
var div = document.getElementsByTagName('div')[0];
div.addEventListener('selectstart', function (event) { event.preventDefault(); selectStartCount++; });
div.focus();
window.getSelection().collapse(div.firstChild, 1);

if (window.testRunner) {
    testRunner.dumpAsText();
    eventSender.keyDown("ArrowRight", ["shiftKey"]);
    logResult("Check Right arrow + Shift", 1);

    eventSender.keyDown("ArrowRight", ["shiftKey"], ["ctrlKey"]);
    logResult("Check Right arrow + Shift + Control", 2);

    eventSender.keyDown("End", ["shiftKey"]);
    logResult("Check End + Shift", 3);
}

function logResult(title, expectedCount) {
    var range = window.getSelection().getRangeAt(0);
    document.write(title + ': ');
    if (selectStartCount != expectedCount)
        document.write('FAIL -  expected ' + expectedCount + ' events but got ' + selectStartCount + ' events');
    else if (range.startOffset != 1 || range.endOffset != 1)
        document.write('FAIL - selection changed');
    else
        document.write('PASS');
    document.write('<br>');
}

</script>
</body>
</html>
