<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN"
"http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">

<!--

   Copyright 2001-2002  The Apache Software Foundation 

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

-->
<!-- ========================================================================= -->
<!-- Test description here                                                     -->
<!--                                                                           -->
<!-- <AUTHOR>                                      -->
<!-- @version $Id: textStyles.svg,v 1.5 2004/08/18 07:12:23 vhardy Exp $       -->
<!-- ========================================================================= -->
<?xml-stylesheet type="text/css" href="../resources/test.css" ?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="450" height="500" viewBox="0 0 450 500">
<title>Text Font Faces and Styles</title>

    <!-- ============================================================= -->
    <!-- Test content                                                  -->
    <!-- ============================================================= -->

    <defs>
     <style type="text/css"><![CDATA[
       .Caption { font-size: 12px; font-family: dialog; font-weight: normal;
                  font-style: normal; stroke: none; stroke-width: 0; 
                  fill: black } ]]>
     </style>

     <style type="text/css"><![CDATA[
       .SansSerifPlain { font-family: SansSerif } ]]>
     </style>

     <style type="text/css"><![CDATA[
       .SansSerifBold { font-family: SansSerif; font-weight: bold } ]]>
     </style>

     <style type="text/css"><![CDATA[
       .SansSerifOblique { font-family: SansSerif; font-style: oblique } ]]>
     </style>

     <style type="text/css"><![CDATA[
       .SerifPlain { font-family: Serif } ]]>
     </style>

     <style type="text/css"><![CDATA[
       .SerifBold { font-family: Serif; font-weight: bold } ]]>
     </style>

     <style type="text/css"><![CDATA[
       .SerifOblique { font-family: Serif; font-style: oblique } ]]>
     </style>

     <style type="text/css"><![CDATA[
       .Monospaced { font-family: Monospaced } ]]>
     </style>

     <style type="text/css"><![CDATA[
       .MonospacedBold { font-family: Monospaced; font-weight:bold } ]]>
     </style>

     <style type="text/css"><![CDATA[
       .MonospacedOblique { font-family: Monospaced; font-style:oblique } ]]>
     </style>

     <style type="text/css"><![CDATA[
       .Default {   } ]]>
     </style>

     <style type="text/css"><![CDATA[
       .DefaultBoldOblique { font-style: oblique; font-weight: bold } ]]>
     </style>

     <style type="text/css"><![CDATA[
       .DefaultOutline { stroke-width: 1; stroke: black; fill: none } ]]>
     </style>

     <style type="text/css"><![CDATA[
       .Times { font-family: Times  } ]]>
     </style>

     <style type="text/css"><![CDATA[
       .Arial { font-family: Arial  } ]]>
     </style>

     <style type="text/css"><![CDATA[
       .Verdana { font-family: Verdana  } ]]>
     </style>

     <style type="text/css"><![CDATA[
       .Helvetica { font-family: Helvetica  } ]]>
     </style>

     <style type="text/css"><![CDATA[
       .Impact { font-family: Impact  } ]]>
     </style>

     <style type="text/css"><![CDATA[
       .AvantGarde { font-family: AvantGarde  } ]]>
     </style>

     <text x="0" y="0" text-anchor="middle" id="sample">sample</text>

    </defs>

    <g id="testContent" text-anchor="middle" font-size="30">
        <text class="title" x="50%" y="10%" font-size="15" >
            Text Font Faces and Styles</text>
       
       <text x="50%" y="15%" font-size="10">Standard Font Faces</text>

        <g transform="translate(100, 110)" class="SansSerifPlain">
            <use xlink:href="#sample"/>
            <text font-size="15" y="15" class="Caption">SansSerif, normal weight</text>
        </g>

        <g transform="translate(225, 110)" class="SansSerifBold">
            <use xlink:href="#sample"/>
            <text font-size="15"  y="15" class="Caption">SansSerif, bold</text>
        </g>

        <g transform="translate(350, 110)" class="SansSerifOblique">
            <use xlink:href="#sample"/>
            <text  font-size="15" y="15" class="Caption">SansSerif, oblique</text>
        </g>

        <g transform="translate(100, 160)" class="SerifPlain">
            <use xlink:href="#sample"/>
            <text y="15" class="Caption">Serif, normal weight</text>
        </g>

        <g transform="translate(225, 160)" class="SerifBold">
            <use xlink:href="#sample"/>
            <text y="15" class="Caption">Serif, bold</text>
        </g>

        <g transform="translate(350, 160)" class="SerifOblique">
            <use xlink:href="#sample"/>
            <text y="15" class="Caption">Serif, oblique</text>
        </g>

        <g transform="translate(100, 210)" class="Monospaced">
            <use xlink:href="#sample"/>
            <text y="15" class="Caption">Monospaced, normal weight</text>
        </g>

        <g transform="translate(225, 210)" class="MonospacedBold">
            <use xlink:href="#sample"/>
            <text y="15" class="Caption">Monospaced, bold</text>
        </g>

        <g transform="translate(350, 210)" class="MonospacedOblique">
            <use xlink:href="#sample"/>
            <text y="15" class="Caption">Monospaced, oblique</text>
        </g>

        <g transform="translate(100, 260)" class="Default">
            <use xlink:href="#sample"/>
            <text y="15" class="Caption">(default)</text>
        </g>

        <g transform="translate(225, 260)" class="DefaultBoldOblique">
            <use xlink:href="#sample"/>
            <text y="15" class="Caption">default, bold, oblique</text>
        </g>

        <g transform="translate(350, 260)" class="DefaultOutline">
            <use xlink:href="#sample"/>
            <text y="15" class="Caption">stroke-width=1, fill=none</text>
        </g>

        <text x="50%" y="62%" font-size="10">Named Font Families</text>
        <text x="50%" y="65%" font-size="10" font-style="oblique">
            (Not all typefaces are available on all systems.)
        </text>

        <g transform="translate(100, 360)" class="Times">
            <use xlink:href="#sample"/>
            <text y="15" class="Caption">Times</text>
        </g>

        <g transform="translate(225, 360)" class="Verdana">
            <use xlink:href="#sample"/>
            <text y="15" class="Caption">Verdana</text>
        </g>

        <g transform="translate(350, 360)" class="Helvetica">
            <use xlink:href="#sample"/>
            <text y="15" class="Caption">Helvetica</text>
        </g>

        <g transform="translate(100, 410)" class="Arial">
            <text><tref xlink:href="#sample" text-decoration="underline"/></text>
            <text y="15" class="Caption">Arial (underlined)</text>
        </g>

        <g transform="translate(225, 410)" class="Impact">
            <use xlink:href="#sample"/>
            <text y="15" class="Caption">Impact</text>
        </g>

        <g transform="translate(350, 410)" class="AvantGarde">
            <text><tref xlink:href="#sample" text-decoration="line-through"/></text>
            <text y="15" class="Caption">AvantGarde (line-through)</text>
        </g>

    </g>

    <!-- ============================================================= -->
    <!-- Batik sample mark                                             -->
    <!-- ============================================================= -->
    <use xlink:href="../resources/batikLogo.svg#Batik_Tag_Box" />
    
</svg>
