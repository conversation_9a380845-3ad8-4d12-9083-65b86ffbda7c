// GENERATED CONTENT - DO NOT EDIT
// Content was automatically extracted by <PERSON><PERSON> into webref
// (https://github.com/w3c/webref)
// Source: WebGL WEBGL_compressed_texture_s3tc Khronos Ratified Extension Specification (https://registry.khronos.org/webgl/extensions/WEBGL_compressed_texture_s3tc/)

[Exposed=(Window,Worker), LegacyNoInterfaceObject]
interface WEBGL_compressed_texture_s3tc {
    /* Compressed Texture Formats */
    const GLenum COMPRESSED_RGB_S3TC_DXT1_EXT        = 0x83F0;
    const GLenum COMPRESSED_RGBA_S3TC_DXT1_EXT       = 0x83F1;
    const GLenum COMPRESSED_RGBA_S3TC_DXT3_EXT       = 0x83F2;
    const GLenum COMPRESSED_RGBA_S3TC_DXT5_EXT       = 0x83F3;
};
