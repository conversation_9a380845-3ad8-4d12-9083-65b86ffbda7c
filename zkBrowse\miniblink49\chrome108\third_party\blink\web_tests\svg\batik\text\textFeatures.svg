<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN"
"http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">

<!--

   Copyright 2001-2003  The Apache Software Foundation 

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

-->
<!-- ========================================================================= -->
<!-- Test description here                                                     -->
<!--                                                                           -->
<!-- <AUTHOR>                                      -->
<!-- @version $Id: textFeatures.svg,v 1.6 2004/08/18 07:12:21 vhardy Exp $                                                             -->
<!-- ========================================================================= -->
<?xml-stylesheet type="text/css" href="../resources/test.css" ?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="450" height="500" viewBox="0 0 450 500">
    <!-- ============================================================= -->
    <!-- Test content                                                  -->
    <!-- ============================================================= -->

    <defs>
        <filter id="blur"  filterUnits="userSpaceOnUse" x="0" y="-80" width="200" height="100" filterRes="200">
            <feGaussianBlur stdDeviation="2 2" x="0" y="-80" width="200" height="100"/> 
        </filter>
    </defs>

    <g id="testContent">
        <text class="title" x="50%" y="10%" font-size="15" text-anchor="middle" >
            Text Element Features</text>
      <g font-family="dialog" font-size="15" 
                  text-anchor="start" fill="MidnightBlue">
        <text x="10%" y="20%">
        Text can change 
        <tspan font-size="30">size,</tspan>
        <tspan font-size="20" font-family="Serif">typeface,</tspan> 
        <tspan fill="green">color,</tspan>
        or 
        <tspan fill="none" font-style="oblique" stroke="red" stroke-width="1">style</tspan>
        </text>
        <text fill="MidnightBlue" x="10%" y="25%"> 
        within a single text element.
        </text> 
        <text x="10%" y="35%">Styling features include 
            <tspan font-weight="bold">weight,</tspan> 
            <tspan font-style="oblique">posture,</tspan> and 
            <tspan font-family="serif">typeface.</tspan>
        </text>
        <rect x="10%" y="40%" width="45%" height="10%" fill="DodgerBlue"/>
        <text x="10%" y="45%">
        Graphics attributes such as 
            <tspan fill="red" opacity="0.3">opacity</tspan> can be applied.
        </text>
        <text x="10%" y="55%">"text decoration" can include 
            <tspan text-decoration="underline">underline,</tspan>
            <tspan text-decoration="overline">overline,</tspan> and
        </text> 
        <text x="10%" y="60%">
        <tspan text-decoration="line-through">strikethrough.</tspan>
        <tspan baseline-shift="super" font-size="75%">super</tspan>-and-
        <tspan baseline-shift="sub" font-size="75%">sub</tspan>-scripts 
	are available.</text>
        <text x="10%" y="65%">Baseline can also be shifted
        <tspan baseline-shift="50%" font-size="75%">up</tspan> and
        <tspan baseline-shift="-50%" font-size="75%">down</tspan> 
	by percentage.</text>
        <text x="10%" y="75%" stroke-linecap="round" stroke-linejoin="round">
            Various 
            <tspan stroke="red" fill="none" stroke-width="0.5">outline
            </tspan>styles, 
            <tspan stroke="red" stroke-width="0.5" fill="blue" >fill colors
            </tspan>and
            <tspan stroke="red" stroke-width="1" fill="blue" >thicknesses
            </tspan>can be
        </text>
        <text x="10%" y="80%">used, and the outline stroke can be 
            <tspan stroke="black" fill="none" stroke-width="0.5" 
               stroke-linecap="round" stroke-linejoin="round">rounded</tspan>
            or 
            <tspan stroke="black" fill="none" stroke-width="0.5" 
               stroke-linecap="square" stroke-linejoin="miter">mitered.</tspan>
        </text>
        <text x="10%" y="90%">Text elements also can be</text>
        <text x="10%" y="95%">filtered and transformed.</text>
        <g font-size="40" transform="translate(250, 475)">  
        <text x="0" y="0" filter="url(#blur)" fill="black" opacity="0.7"
         transform="skewX(40) translate(-6,-6)">
            Shadow
        </text>
        <text x="0" y="0" stroke="black" stroke-width="0.2" fill="white">
            Shadow
        </text>
        </g>
      </g>
    </g>

    <!-- ============================================================= -->
    <!-- Batik sample mark                                             -->
    <!-- ============================================================= -->
    <use xlink:href="../resources/batikLogo.svg#Batik_Tag_Box" />
    
</svg>
