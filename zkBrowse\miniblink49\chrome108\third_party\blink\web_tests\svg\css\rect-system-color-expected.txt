Test that fill and stroke properties accept system colors

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".



Testing system colorActiveBorder
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorActiveCaption
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorActiveText
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorAppWorkspace
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorBackground
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorButtonBorder
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorButtonFace
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorButtonHighlight
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorButtonShadow
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorButtonText
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorCanvas
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorCanvasText
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorCaptionText
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorField
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorFieldText
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorGrayText
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorHighlight
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorHighlightText
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorInactiveBorder
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorInactiveCaption
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorInactiveCaptionText
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorInfoBackground
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorInfoText
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorLinkText
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorMark
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorMarkText
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorMenu
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorMenuText
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorScrollbar
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorSelectedItem
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorSelectedItemText
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorThreeDDarkShadow
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorThreeDFace
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorThreeDHighlight
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorThreeDLightShadow
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorThreeDShadow
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorVisitedText
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorWindow
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorWindowFrame
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
Testing system colorWindowText
PASS computedStyleText.fill is computedStyleDiv.get('color').toString()
PASS computedStyleText.stroke is computedStyleDiv.get('color').toString()
PASS successfullyParsed is true

TEST COMPLETE

