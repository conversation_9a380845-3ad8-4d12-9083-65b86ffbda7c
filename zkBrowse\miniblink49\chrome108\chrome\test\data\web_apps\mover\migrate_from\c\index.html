<!DOCTYPE html>

<head>
  <title id="title">Migration has happened!</title>
  <link rel="manifest" href="./manifest.webmanifest" />
  <meta id="themeColor" name="theme-color" content="#ec9740" />
  <meta content="text/html;charset=utf-8" http-equiv="Content-Type">
  <meta content="utf-8" http-equiv="encoding">
  <link
    rel="icon"
    type="image/png"
    href="icon144.png"
  />
</head>

<style>
  * {
    font-family: monospace;
    background-color: #ec9740;
  }
</style>

<h1>
  App to migrate from! App C.
</h1>

<div>
  This is app we are trying from!
</div>

<div style="border: solid 1px grey; margin: 5px; padding: 5px;">
  Directory:
  <ul>
    <li><a href="../a/index.html">migrate_from/a/index.html</a></li>
    <li><a href="../b/index.html">migrate_from/b/index.html</a></li>
    <li><a href="index.html">migrate_from/c/index.html (here)</a></li>
    <li><a href="../../migrate_to/index.html">migrate_to/index.html</a></li>
    <li><a href="../../index.html">index.html</a></li>
  </ul>
</div>

<script>
navigator.serviceWorker.register('./serviceworker.js')
</script>
