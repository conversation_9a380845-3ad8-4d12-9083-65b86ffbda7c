<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Border-top-width using pixels with a minimum value with a minus sign, -0px</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="<PERSON><PERSON>" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-06-24 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#propdef-border-top-width" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#border-width-properties" />
		<link rel="match" href="../reference/ref-if-there-is-no-red.xht" />

        <meta name="assert" content="The 'border-top-width' property supports a minimum length value in pixels that that has a minus sign before it." />
        <style type="text/css">
            div
            {
                border-top-style: solid;
                border-top-width: -0px;
                border-top-color: red;
                height: 1in;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is <strong>no red</strong>.</p>
        <div></div>
    </body>
</html>