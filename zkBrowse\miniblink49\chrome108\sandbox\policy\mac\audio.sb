; Copyright 2018 The Chromium Authors
; Use of this source code is governed by a BSD-style license that can be
; found in the LICENSE file.

; --- The contents of common.sb implicitly included here. ---

; File access.
(allow file-read*
  (path (user-homedir-path "/Library/Caches/com.apple.coreaudio.components.plist"))
  (regex (user-homedir-path #"/Library/Preferences/com.apple.coreaudio.*"))
  (subpath "/Library/Audio/Plug-Ins")
  (subpath "/Library/QuickTime")
  (subpath "/System/Library/Components")
  (subpath "/System/Library/Extensions")
  (subpath (user-homedir-path "/Library/Audio/Plug-Ins"))
)

(allow device-microphone)

(allow iokit-open
  (iokit-user-client-class "IOAudioControlUserClient")
  (iokit-user-client-class "IOAudioEngineUserClient")
)

(allow ipc-posix-shm-read* ipc-posix-shm-write-data
  (ipc-posix-name-regex #"^AudioIO"))

; Mach IPC.
(allow mach-lookup
  (global-name "com.apple.audio.SystemSoundServer-OSX")
  (global-name "com.apple.audio.VDCAssistant")
  (global-name "com.apple.audio.audiohald")
  (global-name "com.apple.audio.coreaudiod")
)

(if (>= os-version 1013)
  (allow mach-lookup
    (global-name "com.apple.audio.AudioComponentRegistrar")
    (xpc-service-name "com.apple.audio.SandboxHelper")
  ))

; sysctls.
(allow sysctl-read
  (sysctl-name "hw.optional.avx1_0")
  (sysctl-name "hw.optional.avx2_0")
  (sysctl-name "hw.optional.sse2")
  (sysctl-name "hw.optional.sse3")
  (sysctl-name "hw.optional.sse4_1")
  (sysctl-name "hw.optional.sse4_2")
)

; This is available in 10.15+, and rolled out as a Finch experiment.
(if (param-true? filter-syscalls-debug)
  (when (defined? 'syscall-unix)
    (deny syscall-unix (with send-signal SIGSYS))
    (allow syscall-unix
      (syscall-number SYS_csrctl)
      (syscall-number SYS_mlock)
      (syscall-number SYS_poll)
      (syscall-number SYS_proc_rlimit_control)
      (syscall-number SYS_psynch_cvbroad)
      (syscall-number SYS_psynch_cvwait)
      (syscall-number SYS_setsockopt)
      (syscall-number SYS_socketpair)
      (syscall-number SYS_work_interval_ctl)
      (syscall-number SYS_write)
)))
