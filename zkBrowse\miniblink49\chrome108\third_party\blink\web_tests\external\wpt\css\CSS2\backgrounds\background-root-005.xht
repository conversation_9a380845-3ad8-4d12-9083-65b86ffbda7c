<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>CSS Test: Backgrounds on &lt;body&gt; and &lt;html&gt; - color vs. transparent</title>
  <link rel="author" title="<PERSON>" href="mailto:<EMAIL>"/>
  <link rel="alternate" href="http://www.hixie.ch/tests/adhoc/css/background/02.html" type="text/html"/>
  <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background"/>
  <link rel="match" href="background-root-005-ref.xht" />

<style type="text/css"><![CDATA[
   body { border: solid lime; background: green; color: white; }
   html { border: solid blue; background: transparent; color: yellow; }
   :link, :visited { color: inherit; background: transparent; }
   * { margin: 1em; padding: 1em; }
]]></style>

</head>
<body>
<p>This text should be inside a bright green rectangle, which itself
should be enclosed in a bright blue rectangle. The background around,
between, and inside these borders and under this text be green.</p>

</body>
</html>
