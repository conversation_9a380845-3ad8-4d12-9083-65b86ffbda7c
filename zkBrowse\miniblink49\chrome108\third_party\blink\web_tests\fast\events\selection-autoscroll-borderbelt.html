<!DOCTYPE html>
<script src="../../resources/testharness.js"></script>
<script src="../../resources/testharnessreport.js"></script>
For manual testing, Select some text and drag the mouse close to the bottom
(do not reach the border) before releasing.
    <div id="scrollable" style="height: 100px; overflow: auto; border: solid 3px #cc0000">
        <p>This is a very long sentence to take up some space.</p>
        <p>This is a very long sentence to take up some space.</p>
        <p>This is a very long sentence to take up some space.</p>
        <p>This is a very long sentence to take up some space.</p>
        <p>This is a very long sentence to take up some space.</p>
        <p>This is a very long sentence to take up some space.</p>
        <p>This is a very long sentence to take up some space.</p>
        <p>This is a very long sentence to take up some space.</p>
        <p>This is a very long sentence to take up some space.</p>
        <p>This is a very long sentence to take up some space.</p>
    </div>
</body>
<script>
var scrollable = document.getElementById("scrollable");
var retryCount = 0;
var testAutoscroll = async_test('Selection-autoscroll should be triggered when the mouse is within the border belt');
testAutoscroll.step(function() {
    if (!window.eventSender)
        return;
        
    var dragStartX = scrollable.offsetLeft + 20;
    var dragStartY = scrollable.offsetTop + 5;
    var dragEndX = dragStartX;
    var dragEndY = scrollable.offsetTop + scrollable.offsetHeight - 10;

    eventSender.dragMode = false;
    eventSender.mouseMoveTo(dragStartX, dragStartY);
    eventSender.mouseDown();
    eventSender.mouseMoveTo(dragEndX, dragEndY);

    requestAnimationFrame(function() {
        assert_not_equals(scrollable.scrollTop, 0);
        testAutoscroll.done();
    });
});
</script>
