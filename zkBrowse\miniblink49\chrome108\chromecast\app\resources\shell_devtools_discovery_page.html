<html>
<head>
<title>Cast shell remote debugging</title>
<style>
  .help {
    font-size: 11px;
  }
</style>
</head>
<body>

<div id='caption'>Inspectable WebContents</div>
<div id='items'></div>

<script>
window.addEventListener('load', function() {
  var tabs_list_request = new XMLHttpRequest();
  tabs_list_request.open("GET", "/json/list?t=" + new Date().getTime(), true);
  tabs_list_request.onreadystatechange = function() {
    if (this.readyState == 4 && this.status == 200 && this.response) {
      var responseJSON = JSON.parse(this.response);
      for (var i = 0; i < responseJSON.length; ++i) {
        appendItem(responseJSON[i]);
      }
    }
  };
  tabs_list_request.send();
});

function appendItem(metadata) {
  var item_container = document.createElement('div');
  var frontend_header = document.createElement('h3');
  frontend_header.textContent = metadata.title || "(untitled tab)";
  item_container.appendChild(frontend_header);

  if (metadata.devtoolsFrontendUrl) {
    var frontend_link = document.createElement('a');
    frontend_link.textContent = 'Remote Debugging (AppEngine)'
    frontend_link.href = metadata.devtoolsFrontendUrl;
    item_container.appendChild(frontend_link);
  } else {
    frontend_header.textContent += " (already has active debugging session)";
  }

  document.getElementById("items").appendChild(item_container);
}
</script>

<h3>Help</h3>
<div id="help">
  You may have to select the shield icon in the address bar to establish a connection.
  See the <a href="https://support.google.com/chrome/answer/1342714?hl=en">help
  center</a> for more information.
</div>

</body>
</html>
