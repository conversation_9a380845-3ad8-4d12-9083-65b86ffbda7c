<script>
if (window.testRunner)
    testRunner.dumpAsText();

var key = 0;
function test()
{
    if (!window.testRunner)
        return;
 
    var elem = document.getElementById('focusMe')
    key = -1;
    elem.focus();
    eventSender.keyDown("\t"); 
    
    if (!key) { // first test passed, continue with second test
        key = 1;
        elem.focus();
        eventSender.keyDown("\t",["shiftKey"]);

        if (!key) { // second test passed
            document.write("PASSED");
            document.close();
            return;
        }
    }
    document.write("FAILED");     
    document.close();
}
</script>
<body onload="test()">
<input onfocus="key-=1">
<input id="focusMe" tabindex="-1">
<input onfocus="key+=1">
<div id="results"></div>
</body>
