<!DOCTYPE html>
<script src='../../resources/gesture-util.js'></script>
<script src="../../resources/testharness.js"></script>
<script src="../../resources/testharnessreport.js"></script>
<div style="height:900px;width:400px">
  <div style="background-color:red;height:900px;width:200px;position:relative;left:0px;top:0px"></div>
  <div style="background-color:green;height:900px;width:200px;position:relative;left:200px;top:-900px"></div>
</div>
<div style="height:900px;width:400px">
  <div style="background-color:blue;height:900px;width:200px;position:relative;left:0px;top:0px"></div>
  <div style="background-color:yellow;height:900px;width:200px;position:relative;left:200px;top:-900px"></div>
</div>

<script>
var givenScrollLeft = 2;
var expectedScrollLeft = 0;
var last_event = null;
var source = GestureSourceType.MOUSE_INPUT;
const numTicksX = givenScrollLeft / pixelsPerTick();
const expectedWheelDeltaX = numTicksX * LEGACY_MOUSE_WHEEL_TICK_MULTIPLIER;

function mousewheelHandler(e)
{
    last_event = e;
}

promise_test(async () => {
    document.body.addEventListener("mousewheel", mousewheelHandler, false);

    await smoothScroll(givenScrollLeft, 100, 110, source, 'right', SPEED_INSTANT, false /* precise_scrolling_deltas */, true /* scroll_by_page */);
    await conditionHolds( () => {
        return document.scrollingElement.scrollLeft == window.expectedScrollLeft;
    });
    assert_equals(last_event.wheelDeltaX, -Math.floor(expectedWheelDeltaX));
    assert_equals(last_event.wheelDelta, -Math.floor(expectedWheelDeltaX));
}, 'This test checks one page of scroll on document moves the content by 0 pixels.');
</script>
