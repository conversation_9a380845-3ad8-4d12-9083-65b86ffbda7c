// Copyright 2019 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include "chrome/updater/win/ui/ui_constants.h"

namespace updater {
namespace ui {

const wchar_t kLegacyUiDisplayedEventEnvironmentVariableName[] =
    L"GOOGLE_UPDATE_UI_DISPLAYED_EVENT_NAME";

const wchar_t kDialogFont[] = L"Segoe UI";

}  // namespace ui
}  // namespace updater
