<!DOCTYPE HTML>
<script src='../../resources/testharness.js'></script>
<script src='../../resources/testharnessreport.js'></script>
<script src='../../resources/gesture-util.js'></script>

<div id="dv" style="overflow: auto; width: 200px; height: 200px;
     whitespace: nowrap;">
    <div style="width:300px; height:300px">
        Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do
        eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad
        minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip
        ex ea commodo consequat. Duis aute irure dolor in reprehenderit in
        voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur
        sint occaecat cupidatat non proident, sunt in culpa qui officia
        deserunt mollit anim id est laborum
    </div>
</div>


<script>
promise_test(async () => {
    var scrollCount = 0;
    var target = document.getElementById('dv');
    var target_center = elementCenter(target);
    target.addEventListener('scroll', function(event) {
        if(this.firstElementChild)
            this.removeChild(this.firstElementChild);
        scrollCount++;
    });

    await wheelTick(0, 1, target_center);
    await wheelTick(0, 1, target_center);
    assert_greater_than(scrollCount, 1);
    assert_equals(target.children.length, 0);
}, "No crash when a child is removed while its parent is scrolling.");

</script>
