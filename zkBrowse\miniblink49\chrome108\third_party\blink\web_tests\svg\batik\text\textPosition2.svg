<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN"
"http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">

<!--

   Copyright 2001-2002  The Apache Software Foundation 

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

-->
<!-- ========================================================================= -->
<!-- Test the x,dx and y,dy attributes                                         -->
<!--                                                                           -->
<!-- <AUTHOR>                                                  -->
<!-- @version $Id: textPosition2.svg,v 1.4 2004/08/18 07:12:23 vhardy Exp $ -->
<!-- ========================================================================= -->
<?xml-stylesheet type="text/css" href="../resources/test.css" ?>

<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="450" height="500" viewBox="0 0 450 500">
<title>Text Position 2</title>

  <style type="text/css"><![CDATA[
    .info {
      font-family: Arial;
      font-size: 10px;
    }
  ]]>
  </style>

<defs>
<font horiz-adv-x="904" ><font-face
    font-family="SVGArial"
    units-per-em="2048"
    panose-1="2 11 6 4 2 2 2 2 2 4"
    ascent="1854"
    descent="-434"
    alphabetic="0" />
<missing-glyph horiz-adv-x="1536" d="M256 0V1280H1280V0H256ZM288 32H1248V1248H288V32Z" />
<glyph unicode="B" glyph-name="B" horiz-adv-x="1366" d="M150 0V1466H700Q868 1466 969 1422T1128 1285T1186 1091Q1186 997 1135 914T981 780Q1114 741 1185 647T1257 425Q1257 322 1214 234T1106 97T946 25T709 0H150ZM344 850H661Q790 850 846 867Q920 889
957 940T995 1068Q995 1141 960 1196T860 1272T637 1293H344V850ZM344 173H709Q803 173 841 180Q908 192 953 220T1027 301T1056 425Q1056 507 1014 567T898 652T683 677H344V173Z" />
<glyph unicode="a" glyph-name="a" horiz-adv-x="1139" d="M828 131Q728 46 636 11T437 -24Q262 -24 168 61T74 280Q74 358 109 422T202 526T332 585Q385 599 492 612Q710 638 813 674Q814 711 814 721Q814 831 763 876Q694 937 558 937Q431 937 371 893T281 735L105
759Q129 872 184 941T343 1048T584 1086Q720 1086 805 1054T930 974T986 851Q995 805 995 685V445Q995 194 1006 128T1052 0H864Q836 56 828 131ZM813 533Q715 493 519 465Q408 449 362 429T291 371T266 285Q266 213 320 165T480 117Q584 117 665 162T784 287Q813
348 813 467V533Z" />
<glyph unicode="i" glyph-name="i" horiz-adv-x="455" d="M136 1259V1466H316V1259H136ZM136 0V1062H316V0H136Z" />
<glyph unicode="k" glyph-name="k" horiz-adv-x="1024" d="M136 0V1466H316V630L742 1062H975L569 668L1016 0H794L443 543L316 421V0H136Z" />
<glyph unicode="t" glyph-name="t" horiz-adv-x="569" d="M528 161L554 2Q478 -14 418 -14Q320 -14 266 17T190 98T168 311V922H36V1062H168V1325L347 1433V1062H528V922H347V301Q347 224 356 202T387 167T449 154Q479 154 528 161Z" />
</font>

</defs>


    <!-- ============================================================= -->
    <!-- Test content                                                  -->
    <!-- ============================================================= -->

<text class="title" x="50%" y="30">Text Position 2</text>

<g id="testContent" style="font-family:SVGArial; font-size:24px">

<!-- ################################################################ -->

<text x="50" y="80">Batik</text>
<text x="50" y="100" class="info">&lt;text x="100" y="80"></text>

<text x="240" y="80" style="letter-spacing:20">Batik</text>
<text x="240" y="100" class="info">&lt;text x="240" y="80" letter-spacing="20"></text>


<text x="50 70 90 110 130" y="140">Batik</text>
<text x="50" y="160" class="info">&lt;text x="50 70 90 110 130" y="140"></text>

<text dx="240 20 20 20 20" y="140">Batik</text>
<text x="240" y="160" class="info">&lt;text dx="240 20 20 20 20" y="140"></text>

<!-- ################################################################ -->

<text><tspan x="50" y="200">Batik</tspan></text>
<text x="50" y="220" class="info">&lt;tspan x="50" y="200"></text>

<text><tspan x="240" y="200" style="letter-spacing:20">Batik</tspan></text>
<text><tspan x="240" y="220" class="info">&lt;tspan x="240" y="200" letter-spacing="20"></tspan></text>


<text><tspan x="50 70 90 110 130" y="260">Batik</tspan></text>
<text x="50" y="280" class="info">&lt;tspan x="50 70 90 110 130" y="260"></text>

<text><tspan dx="240 20 20 20 20" y="260">Batik</tspan></text>
<text x="240" y="280" class="info">&lt;tspan dx="240 20 20 20 20" y="260"></text>

<!-- ################################################################ -->

<text><tspan x="50 60 70 80 90" dx="0 10 20 30 40" y="320">Batik</tspan></text>
<text x="50" y="340" class="info">&lt;tspan x="50 60 70 80 90" <tspan x="83" dy="1.1em">dx="0 10 20 30 40" y="320"></tspan></text>


<text><tspan x="240" dx="0 10 10 10 10" y="320" style="letter-spacing:10">Batik</tspan></text>
<text x="240" y="340" class="info">&lt;tspan x="240" dx="0 10 10 10 10" <tspan x="273" dy="1.1em">y="320" style="letter-spacing:10"></tspan></text>



</g>

    <!-- ============================================================= -->
    <!-- Batik sample mark                                             -->
    <!-- ============================================================= -->
    <use xlink:href="../resources/batikLogo.svg#Batik_Tag_Box" />
    
</svg>

