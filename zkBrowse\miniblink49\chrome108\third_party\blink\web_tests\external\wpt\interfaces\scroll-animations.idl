// GENERATED CONTENT - DO NOT EDIT
// Content was automatically extracted by <PERSON><PERSON> into webref
// (https://github.com/w3c/webref)
// Source: Scroll-linked Animations (https://drafts.csswg.org/scroll-animations-1/)

enum ScrollAxis {
  "block",
  "inline",
  "horizontal",
  "vertical"
};

dictionary ScrollTimelineOptions {
  Element? source;
  ScrollAxis axis = "block";
};

[Exposed=Window]
interface ScrollTimeline : AnimationTimeline {
  constructor(optional ScrollTimelineOptions options = {});
  readonly attribute Element? source;
  readonly attribute ScrollAxis axis;
};

dictionary ViewTimelineOptions {
  Element subject;
  ScrollAxis axis = "block";
};

[Exposed=Window]
interface ViewTimeline : ScrollTimeline {
  constructor(optional ViewTimelineOptions options = {});
  readonly attribute Element subject;
  readonly attribute CSSNumericValue startOffset;
  readonly attribute CSSNumericValue endOffset;
};

[Exposed=Window]
interface AnimationTimeline {
  readonly attribute CSSNumberish? currentTime;
  CSSNumericValue? getCurrentTime(optional CSSOMString rangeName);
};
