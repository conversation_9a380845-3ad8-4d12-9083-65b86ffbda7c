<!DOCTYPE html>
<script src='../../resources/gesture-util.js'></script>
<script src="../../resources/testharness.js"></script>
<script src="../../resources/testharnessreport.js"></script>
<!-- This div is 200 pixels high. The content results in scrool bars on
 both edges, resulting in an effective content area of 185 x 185 on
 linux. The paging context overlap is 24 pixels. So one page of scroll
 moves the content by 185 - 24 -= 161 pixels. -->
<div id="overflow" style="border:2px solid black;overflow:auto;height:200px;width:200px;">
    <div style="height:300px;width:600px">
      <div style="background-color:red;height:300px;width:300px;position:relative;left:0px;top:0px"></div>
      <div style="background-color:green;height:300px;width:300px;position:relative;left:300px;top:-300px"></div>
    </div>
    <div style="height:300px;width:600px">
      <div style="background-color:blue;height:300px;width:300px;position:relative;left:0px;top:0px"></div>
      <div style="background-color:yellow;height:300px;width:300px;position:relative;left:300px;top:-300px"></div>
    </div>
</div>

<script>
var givenScrollLeft = 2; // When paging, this is ignored. Every event is one page.
var expectedScrollLeft = 161;
var last_event = null;
var source = GestureSourceType.MOUSE_INPUT;
const numTicksX = givenScrollLeft / pixelsPerTick();
const expectedWheelDeltaX = numTicksX * LEGACY_MOUSE_WHEEL_TICK_MULTIPLIER;

function mousewheelHandler(e)
{
    last_event = e;
}

promise_test(async () => {
    var overflowElement = document.getElementById("overflow");
    overflowElement.addEventListener("mousewheel", mousewheelHandler, false);

    await smoothScroll(givenScrollLeft, 100, 110, source, 'right', SPEED_INSTANT, false /* precise_scrolling_deltas */, true /* scroll_by_page */);
    await waitFor( () => {
        return overflowElement.scrollLeft == window.expectedScrollLeft;
    });
    assert_equals(last_event.wheelDeltaX, -Math.floor(expectedWheelDeltaX));
    assert_equals(last_event.wheelDelta, -Math.floor(expectedWheelDeltaX));
}, 'This test checks one page of scroll on div moves the content by 161 pixels.');
</script>
