<html>
<head>
<script>
if (window.testRunner) {
  testRunner.dumpAsText();
  testRunner.waitUntilDone();
}

function test() {
    // Test for select that's not inside a form element
    var ctrl1 = document.getElementById('control1');
    ctrl1.focus();

    if (window.testRunner) {
        // change the option selection
        eventSender.keyDown("e", []);
    }
    // hit enter
    var enterEvent = document.createEvent("KeyboardEvents");
    enterEvent.initKeyboardEvent("keypress", true, false, window, "Enter", 0, false, false, false, false, false);
    ctrl1.dispatchEvent(enterEvent);
    // Change focus to trigger the "Enter" press.
    var ctrl2 = document.getElementById('control2');
    ctrl2.focus();
}

function onpopup() {
  // We rely on sequential ordering of POST processing.
  var form = document.getElementById('form');
  form.submit();
  var form2 = document.getElementById('form2');
  form2.submit();
}
</script>
</head>
<body onload="test()">
<select onchange="onpopup()" id="control1"><option value="0">abcd</option><option value="0">efgh</option></select>
If the pop-up was not blocked then there will be an PASS message. Otherwise, the test fails.
<form id="form" action="data:text/html,<script>console.log('PASSED')</script>" target="target">
<input id="control2" type="submit" value="Submit to new window"/>
</form>
<form id="form2" action="data:text/html,<b>hello!</b><script>window.testRunner && testRunner.notifyDone()</script>" target="panel">
<input type="submit" value="Submit local page Javascript"/>
</form>
<iframe name="target"></iframe><iframe name="panel"></iframe>
</body>
</html>
