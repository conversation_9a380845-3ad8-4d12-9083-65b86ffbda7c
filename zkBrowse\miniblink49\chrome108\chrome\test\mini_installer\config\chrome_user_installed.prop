{"Files": {"$LOCAL_APPDATA\\$CHROME_DIR\\Application\\chrome.exe": {"exists": true}, "$LOCAL_APPDATA\\$CHROME_DIR\\Application\\chrome.VisualElementsManifest.xml": {"exists": true}, "$LOCAL_APPDATA\\$CHROME_DIR\\Application\\chrome_proxy.exe": {"exists": true}, "$LOCAL_APPDATA\\$CHROME_DIR\\Application\\$MINI_INSTALLER_FILE_VERSION\\chrome.dll": {"exists": true}, "$LOCAL_APPDATA\\$CHROME_DIR\\Application\\$MINI_INSTALLER_FILE_VERSION\\chrome_elf.dll": {"exists": true}, "$LOCAL_APPDATA\\$CHROME_DIR\\Application\\$MINI_INSTALLER_FILE_VERSION\\chrome_wer.dll": {"exists": true}, "$LOCAL_APPDATA\\$CHROME_DIR\\Application\\$MINI_INSTALLER_FILE_VERSION\\Installer\\chrome.7z": {"exists": true}, "$LOCAL_APPDATA\\$CHROME_DIR\\Application\\$MINI_INSTALLER_FILE_VERSION\\Installer\\setup.exe": {"exists": true}, "$LOCAL_APPDATA\\$CHROME_DIR\\Application\\$MINI_INSTALLER_FILE_VERSION\\$MINI_INSTALLER_FILE_VERSION.manifest": {"exists": true}, "$LOCAL_APPDATA\\$CHROME_DIR\\Application\\$PREVIOUS_VERSION_MINI_INSTALLER_FILE_VERSION": {"exists": false}}, "RegistryEntries": {"HKEY_CURRENT_USER\\$CHROME_CLIENT_STATE_KEY": {"exists": "required", "values": {"CleanInstallRequiredForVersionBelow": {"type": "SZ", "data": "$LAST_INSTALLER_BREAKING_VERSION"}, "DowngradeCleanupCommand": {"type": "SZ", "data": "\"$LOCAL_APPDATA\\$CHROME_DIR\\Application\\$MINI_INSTALLER_FILE_VERSION\\Installer\\setup.exe\" --cleanup-for-downgrade-version=$$1 --cleanup-for-downgrade-operation=$$2 --verbose-logging"}}, "wow_key": "KEY_WOW64_32KEY"}, "HKEY_CURRENT_USER\\$CHROME_UPDATE_REGISTRY_SUBKEY": {"exists": "required", "values": {"pv": {"type": "SZ", "data": "$MINI_INSTALLER_FILE_VERSION"}, "opv": {}, "cmd": {}}, "wow_key": "KEY_WOW64_32KEY"}, "HKEY_CURRENT_USER\\$BINARIES_UPDATE_REGISTRY_SUBKEY": {"exists": "forbidden", "wow_key": "KEY_WOW64_32KEY"}, "HKEY_CURRENT_USER\\$LAUNCHER_UPDATE_REGISTRY_SUBKEY": {"condition": "'$CHROME_SHORT_NAME' == 'Chrome'", "exists": "forbidden", "wow_key": "KEY_WOW64_32KEY"}, "HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\$CHROME_LONG_NAME": {"exists": "required", "values": {"UninstallString": {"type": "SZ", "data": "\"$LOCAL_APPDATA\\$CHROME_DIR\\Application\\$MINI_INSTALLER_FILE_VERSION\\Installer\\setup.exe\" --uninstall --verbose-logging"}, "Version": {"type": "SZ", "data": "$MINI_INSTALLER_FILE_VERSION"}}, "wow_key": "KEY_WOW64_32KEY"}, "HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\Windows Error Reporting\\RuntimeExceptionHelperModules": {"exists": "required", "values": {"$LOCAL_APPDATA\\$CHROME_DIR\\Application\\$MINI_INSTALLER_FILE_VERSION\\chrome_wer.dll": {"type": "DWORD"}}}, "HKEY_CURRENT_USER\\Software\\Classes\\CLSID\\$CHROME_TOAST_ACTIVATOR_CLSID\\LocalServer32": {"exists": "required", "values": {"type": "SZ", "data": "$LOCAL_APPDATA\\$CHROME_DIR\\Application\\$MINI_INSTALLER_FILE_VERSION\\notification_helper.exe"}}, "HKEY_CURRENT_USER\\Software\\Classes\\CLSID\\$CHROME_ELEVATOR_CLSID": {"exists": "forbidden"}, "HKEY_CURRENT_USER\\Software\\Classes\\AppID\\$CHROME_ELEVATOR_CLSID": {"exists": "forbidden"}, "HKEY_CURRENT_USER\\Software\\Classes\\Interface\\$CHROME_ELEVATOR_IID": {"exists": "forbidden"}, "HKEY_CURRENT_USER\\Software\\Classes\\TypeLib\\$CHROME_ELEVATOR_IID": {"exists": "forbidden"}, "HKEY_CURRENT_USER\\Software\\Classes\\$CHROME_SHORT_NAME$USER_SPECIFIC_REGISTRY_SUFFIX": {"exists": "forbidden"}}}