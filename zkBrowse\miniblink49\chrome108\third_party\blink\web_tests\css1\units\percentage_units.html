<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 6.2 Percentage Units</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
.zero { background: yellow }
.one { margin-left: 25%; margin-right: 25%; background: white }
.two { margin-left: 50%; margin-right: 0%; background: white }
.three {margin-left: 25%;}
</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.zero { background: yellow }
.one { margin-left: 25%; margin-right: 25%; background: white }
.two { margin-left: 50%; margin-right: 0%; background: white }
.three {margin-left: 25%;}

</PRE>
<HR>
<DIV CLASS="zero">

<DIV CLASS="one">
<P>This paragraph should be centered within its yellow containing block and its width should be half of the containing block.</P>
</DIV>

<DIV CLASS="two">
<P>This paragraph should be right-aligned within its yellow containing block and its width should be half of the containing block.</P>
</DIV>

</DIV>

<P class="three">
This paragraph should have a left margin of 25% the width of its parent element, which should require some extra text in order to test effectively.
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><DIV CLASS="zero">

<DIV CLASS="one">
<P>This paragraph should be centered within its yellow containing block and its width should be half of the containing block.</P>
</DIV>

<DIV CLASS="two">
<P>This paragraph should be right-aligned within its yellow containing block and its width should be half of the containing block.</P>
</DIV>

</DIV>

<P class="three">
This paragraph should have a left margin of 25% the width of its parent element, which should require some extra text in order to test effectively.
</P>
</TD></TR></TABLE></BODY>
</HTML>
