CONSOLE ERROR: Error: <mask> attribute x: Expected length, "auto".
CONSOLE ERROR: Error: <mask> attribute x: Expected length, "100   px".
CONSOLE ERROR: Error: <mask> attribute x: Expected length, "100px;".
CONSOLE ERROR: Error: <mask> attribute x: Expected length, "100px !important".
CONSOLE ERROR: Error: <mask> attribute x: Expected length, "{ 100px }".
CONSOLE ERROR: Error: <mask> attribute y: Expected length, "auto".
CONSOLE ERROR: Error: <mask> attribute y: Expected length, "100   px".
CONSOLE ERROR: Error: <mask> attribute y: Expected length, "100px;".
CONSOLE ERROR: Error: <mask> attribute y: Expected length, "100px !important".
CONSOLE ERROR: Error: <mask> attribute y: Expected length, "{ 100px }".
CONSOLE ERROR: Error: <svg> attribute x: Expected length, "auto".
CONSOLE ERROR: Error: <svg> attribute x: Expected length, "100   px".
CONSOLE ERROR: Error: <svg> attribute x: Expected length, "100px;".
CONSOLE ERROR: Error: <svg> attribute x: Expected length, "100px !important".
CONSOLE ERROR: Error: <svg> attribute x: Expected length, "{ 100px }".
CONSOLE ERROR: Error: <svg> attribute y: Expected length, "auto".
CONSOLE ERROR: Error: <svg> attribute y: Expected length, "100   px".
CONSOLE ERROR: Error: <svg> attribute y: Expected length, "100px;".
CONSOLE ERROR: Error: <svg> attribute y: Expected length, "100px !important".
CONSOLE ERROR: Error: <svg> attribute y: Expected length, "{ 100px }".
CONSOLE ERROR: Error: <rect> attribute x: Expected length, "auto".
CONSOLE ERROR: Error: <rect> attribute x: Expected length, "100   px".
CONSOLE ERROR: Error: <rect> attribute x: Expected length, "100px;".
CONSOLE ERROR: Error: <rect> attribute x: Expected length, "100px !important".
CONSOLE ERROR: Error: <rect> attribute x: Expected length, "{ 100px }".
CONSOLE ERROR: Error: <rect> attribute y: Expected length, "auto".
CONSOLE ERROR: Error: <rect> attribute y: Expected length, "100   px".
CONSOLE ERROR: Error: <rect> attribute y: Expected length, "100px;".
CONSOLE ERROR: Error: <rect> attribute y: Expected length, "100px !important".
CONSOLE ERROR: Error: <rect> attribute y: Expected length, "{ 100px }".
CONSOLE ERROR: Error: <image> attribute x: Expected length, "auto".
CONSOLE ERROR: Error: <image> attribute x: Expected length, "100   px".
CONSOLE ERROR: Error: <image> attribute x: Expected length, "100px;".
CONSOLE ERROR: Error: <image> attribute x: Expected length, "100px !important".
CONSOLE ERROR: Error: <image> attribute x: Expected length, "{ 100px }".
CONSOLE ERROR: Error: <image> attribute y: Expected length, "auto".
CONSOLE ERROR: Error: <image> attribute y: Expected length, "100   px".
CONSOLE ERROR: Error: <image> attribute y: Expected length, "100px;".
CONSOLE ERROR: Error: <image> attribute y: Expected length, "100px !important".
CONSOLE ERROR: Error: <image> attribute y: Expected length, "{ 100px }".
CONSOLE ERROR: Error: <foreignObject> attribute x: Expected length, "auto".
CONSOLE ERROR: Error: <foreignObject> attribute x: Expected length, "100   px".
CONSOLE ERROR: Error: <foreignObject> attribute x: Expected length, "100px;".
CONSOLE ERROR: Error: <foreignObject> attribute x: Expected length, "100px !important".
CONSOLE ERROR: Error: <foreignObject> attribute x: Expected length, "{ 100px }".
CONSOLE ERROR: Error: <foreignObject> attribute y: Expected length, "auto".
CONSOLE ERROR: Error: <foreignObject> attribute y: Expected length, "100   px".
CONSOLE ERROR: Error: <foreignObject> attribute y: Expected length, "100px;".
CONSOLE ERROR: Error: <foreignObject> attribute y: Expected length, "100px !important".
CONSOLE ERROR: Error: <foreignObject> attribute y: Expected length, "{ 100px }".
CONSOLE ERROR: Error: <rect> attribute rx: A negative value is not valid. ("-200px")
CONSOLE ERROR: Error: <rect> attribute rx: Expected length, "auto".
CONSOLE ERROR: Error: <rect> attribute rx: Expected length, "100   px".
CONSOLE ERROR: Error: <rect> attribute rx: Expected length, "100px;".
CONSOLE ERROR: Error: <rect> attribute rx: Expected length, "100px !important".
CONSOLE ERROR: Error: <rect> attribute rx: Expected length, "{ 100px }".
CONSOLE ERROR: Error: <rect> attribute ry: A negative value is not valid. ("-200px")
CONSOLE ERROR: Error: <rect> attribute ry: Expected length, "auto".
CONSOLE ERROR: Error: <rect> attribute ry: Expected length, "100   px".
CONSOLE ERROR: Error: <rect> attribute ry: Expected length, "100px;".
CONSOLE ERROR: Error: <rect> attribute ry: Expected length, "100px !important".
CONSOLE ERROR: Error: <rect> attribute ry: Expected length, "{ 100px }".
CONSOLE ERROR: Error: <ellipse> attribute rx: A negative value is not valid. ("-200px")
CONSOLE ERROR: Error: <ellipse> attribute rx: Expected length, "auto".
CONSOLE ERROR: Error: <ellipse> attribute rx: Expected length, "100   px".
CONSOLE ERROR: Error: <ellipse> attribute rx: Expected length, "100px;".
CONSOLE ERROR: Error: <ellipse> attribute rx: Expected length, "100px !important".
CONSOLE ERROR: Error: <ellipse> attribute rx: Expected length, "{ 100px }".
CONSOLE ERROR: Error: <ellipse> attribute ry: A negative value is not valid. ("-200px")
CONSOLE ERROR: Error: <ellipse> attribute ry: Expected length, "auto".
CONSOLE ERROR: Error: <ellipse> attribute ry: Expected length, "100   px".
CONSOLE ERROR: Error: <ellipse> attribute ry: Expected length, "100px;".
CONSOLE ERROR: Error: <ellipse> attribute ry: Expected length, "100px !important".
CONSOLE ERROR: Error: <ellipse> attribute ry: Expected length, "{ 100px }".
CONSOLE ERROR: Error: <circle> attribute r: A negative value is not valid. ("-200px")
CONSOLE ERROR: Error: <circle> attribute r: Expected length, "auto".
CONSOLE ERROR: Error: <circle> attribute r: Expected length, "100   px".
CONSOLE ERROR: Error: <circle> attribute r: Expected length, "100px;".
CONSOLE ERROR: Error: <circle> attribute r: Expected length, "100px !important".
CONSOLE ERROR: Error: <circle> attribute r: Expected length, "{ 100px }".
Test that 'length' presentation attribute values are parsed with CSS presentation rules.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".

PASS computedStyle("mask", "x", "  100") is "100px"
PASS computedStyle("mask", "x", "100   ") is "100px"
PASS computedStyle("mask", "x", "100px") is "100px"
PASS computedStyle("mask", "x", "1em") is "16px"
PASS computedStyle("mask", "x", "20%") is "20%"
PASS computedStyle("mask", "x", "-200px") is "-200px"
PASS computedStyle("mask", "x", "auto") is "-10%"
PASS computedStyle("mask", "x", "100   px") is "-10%"
PASS computedStyle("mask", "x", "100px;") is "-10%"
PASS computedStyle("mask", "x", "100px !important") is "-10%"
PASS computedStyle("mask", "x", "{ 100px }") is "-10%"
PASS computedStyle("mask", "y", "  100") is "100px"
PASS computedStyle("mask", "y", "100   ") is "100px"
PASS computedStyle("mask", "y", "100px") is "100px"
PASS computedStyle("mask", "y", "1em") is "16px"
PASS computedStyle("mask", "y", "20%") is "20%"
PASS computedStyle("mask", "y", "-200px") is "-200px"
PASS computedStyle("mask", "y", "auto") is "-10%"
PASS computedStyle("mask", "y", "100   px") is "-10%"
PASS computedStyle("mask", "y", "100px;") is "-10%"
PASS computedStyle("mask", "y", "100px !important") is "-10%"
PASS computedStyle("mask", "y", "{ 100px }") is "-10%"
PASS computedStyle("svg", "x", "  100") is "100px"
PASS computedStyle("svg", "x", "100   ") is "100px"
PASS computedStyle("svg", "x", "100px") is "100px"
PASS computedStyle("svg", "x", "1em") is "16px"
PASS computedStyle("svg", "x", "20%") is "20%"
PASS computedStyle("svg", "x", "-200px") is "-200px"
PASS computedStyle("svg", "x", "auto") is "0px"
PASS computedStyle("svg", "x", "100   px") is "0px"
PASS computedStyle("svg", "x", "100px;") is "0px"
PASS computedStyle("svg", "x", "100px !important") is "0px"
PASS computedStyle("svg", "x", "{ 100px }") is "0px"
PASS computedStyle("svg", "y", "  100") is "100px"
PASS computedStyle("svg", "y", "100   ") is "100px"
PASS computedStyle("svg", "y", "100px") is "100px"
PASS computedStyle("svg", "y", "1em") is "16px"
PASS computedStyle("svg", "y", "20%") is "20%"
PASS computedStyle("svg", "y", "-200px") is "-200px"
PASS computedStyle("svg", "y", "auto") is "0px"
PASS computedStyle("svg", "y", "100   px") is "0px"
PASS computedStyle("svg", "y", "100px;") is "0px"
PASS computedStyle("svg", "y", "100px !important") is "0px"
PASS computedStyle("svg", "y", "{ 100px }") is "0px"
PASS computedStyle("rect", "x", "  100") is "100px"
PASS computedStyle("rect", "x", "100   ") is "100px"
PASS computedStyle("rect", "x", "100px") is "100px"
PASS computedStyle("rect", "x", "1em") is "16px"
PASS computedStyle("rect", "x", "20%") is "20%"
PASS computedStyle("rect", "x", "-200px") is "-200px"
PASS computedStyle("rect", "x", "auto") is "0px"
PASS computedStyle("rect", "x", "100   px") is "0px"
PASS computedStyle("rect", "x", "100px;") is "0px"
PASS computedStyle("rect", "x", "100px !important") is "0px"
PASS computedStyle("rect", "x", "{ 100px }") is "0px"
PASS computedStyle("rect", "y", "  100") is "100px"
PASS computedStyle("rect", "y", "100   ") is "100px"
PASS computedStyle("rect", "y", "100px") is "100px"
PASS computedStyle("rect", "y", "1em") is "16px"
PASS computedStyle("rect", "y", "20%") is "20%"
PASS computedStyle("rect", "y", "-200px") is "-200px"
PASS computedStyle("rect", "y", "auto") is "0px"
PASS computedStyle("rect", "y", "100   px") is "0px"
PASS computedStyle("rect", "y", "100px;") is "0px"
PASS computedStyle("rect", "y", "100px !important") is "0px"
PASS computedStyle("rect", "y", "{ 100px }") is "0px"
PASS computedStyle("image", "x", "  100") is "100px"
PASS computedStyle("image", "x", "100   ") is "100px"
PASS computedStyle("image", "x", "100px") is "100px"
PASS computedStyle("image", "x", "1em") is "16px"
PASS computedStyle("image", "x", "20%") is "20%"
PASS computedStyle("image", "x", "-200px") is "-200px"
PASS computedStyle("image", "x", "auto") is "0px"
PASS computedStyle("image", "x", "100   px") is "0px"
PASS computedStyle("image", "x", "100px;") is "0px"
PASS computedStyle("image", "x", "100px !important") is "0px"
PASS computedStyle("image", "x", "{ 100px }") is "0px"
PASS computedStyle("image", "y", "  100") is "100px"
PASS computedStyle("image", "y", "100   ") is "100px"
PASS computedStyle("image", "y", "100px") is "100px"
PASS computedStyle("image", "y", "1em") is "16px"
PASS computedStyle("image", "y", "20%") is "20%"
PASS computedStyle("image", "y", "-200px") is "-200px"
PASS computedStyle("image", "y", "auto") is "0px"
PASS computedStyle("image", "y", "100   px") is "0px"
PASS computedStyle("image", "y", "100px;") is "0px"
PASS computedStyle("image", "y", "100px !important") is "0px"
PASS computedStyle("image", "y", "{ 100px }") is "0px"
PASS computedStyle("foreignObject", "x", "  100") is "100px"
PASS computedStyle("foreignObject", "x", "100   ") is "100px"
PASS computedStyle("foreignObject", "x", "100px") is "100px"
PASS computedStyle("foreignObject", "x", "1em") is "16px"
PASS computedStyle("foreignObject", "x", "20%") is "20%"
PASS computedStyle("foreignObject", "x", "-200px") is "-200px"
PASS computedStyle("foreignObject", "x", "auto") is "0px"
PASS computedStyle("foreignObject", "x", "100   px") is "0px"
PASS computedStyle("foreignObject", "x", "100px;") is "0px"
PASS computedStyle("foreignObject", "x", "100px !important") is "0px"
PASS computedStyle("foreignObject", "x", "{ 100px }") is "0px"
PASS computedStyle("foreignObject", "y", "  100") is "100px"
PASS computedStyle("foreignObject", "y", "100   ") is "100px"
PASS computedStyle("foreignObject", "y", "100px") is "100px"
PASS computedStyle("foreignObject", "y", "1em") is "16px"
PASS computedStyle("foreignObject", "y", "20%") is "20%"
PASS computedStyle("foreignObject", "y", "-200px") is "-200px"
PASS computedStyle("foreignObject", "y", "auto") is "0px"
PASS computedStyle("foreignObject", "y", "100   px") is "0px"
PASS computedStyle("foreignObject", "y", "100px;") is "0px"
PASS computedStyle("foreignObject", "y", "100px !important") is "0px"
PASS computedStyle("foreignObject", "y", "{ 100px }") is "0px"
PASS computedStyle("rect", "rx", "  100") is "100px"
PASS computedStyle("rect", "rx", "100   ") is "100px"
PASS computedStyle("rect", "rx", "100px") is "100px"
PASS computedStyle("rect", "rx", "1em") is "16px"
PASS computedStyle("rect", "rx", "20%") is "20%"
PASS computedStyle("rect", "rx", "-200px") is "-200px"
PASS computedStyle("rect", "rx", "auto") is "0px"
PASS computedStyle("rect", "rx", "100   px") is "0px"
PASS computedStyle("rect", "rx", "100px;") is "0px"
PASS computedStyle("rect", "rx", "100px !important") is "0px"
PASS computedStyle("rect", "rx", "{ 100px }") is "0px"
PASS computedStyle("rect", "ry", "  100") is "100px"
PASS computedStyle("rect", "ry", "100   ") is "100px"
PASS computedStyle("rect", "ry", "100px") is "100px"
PASS computedStyle("rect", "ry", "1em") is "16px"
PASS computedStyle("rect", "ry", "20%") is "20%"
PASS computedStyle("rect", "ry", "-200px") is "-200px"
PASS computedStyle("rect", "ry", "auto") is "0px"
PASS computedStyle("rect", "ry", "100   px") is "0px"
PASS computedStyle("rect", "ry", "100px;") is "0px"
PASS computedStyle("rect", "ry", "100px !important") is "0px"
PASS computedStyle("rect", "ry", "{ 100px }") is "0px"
PASS computedStyle("ellipse", "rx", "  100") is "100px"
PASS computedStyle("ellipse", "rx", "100   ") is "100px"
PASS computedStyle("ellipse", "rx", "100px") is "100px"
PASS computedStyle("ellipse", "rx", "1em") is "16px"
PASS computedStyle("ellipse", "rx", "20%") is "20%"
PASS computedStyle("ellipse", "rx", "-200px") is "-200px"
PASS computedStyle("ellipse", "rx", "auto") is "0px"
PASS computedStyle("ellipse", "rx", "100   px") is "0px"
PASS computedStyle("ellipse", "rx", "100px;") is "0px"
PASS computedStyle("ellipse", "rx", "100px !important") is "0px"
PASS computedStyle("ellipse", "rx", "{ 100px }") is "0px"
PASS computedStyle("ellipse", "ry", "  100") is "100px"
PASS computedStyle("ellipse", "ry", "100   ") is "100px"
PASS computedStyle("ellipse", "ry", "100px") is "100px"
PASS computedStyle("ellipse", "ry", "1em") is "16px"
PASS computedStyle("ellipse", "ry", "20%") is "20%"
PASS computedStyle("ellipse", "ry", "-200px") is "-200px"
PASS computedStyle("ellipse", "ry", "auto") is "0px"
PASS computedStyle("ellipse", "ry", "100   px") is "0px"
PASS computedStyle("ellipse", "ry", "100px;") is "0px"
PASS computedStyle("ellipse", "ry", "100px !important") is "0px"
PASS computedStyle("ellipse", "ry", "{ 100px }") is "0px"
PASS computedStyle("circle", "r", "  100") is "100px"
PASS computedStyle("circle", "r", "100   ") is "100px"
PASS computedStyle("circle", "r", "100px") is "100px"
PASS computedStyle("circle", "r", "1em") is "16px"
PASS computedStyle("circle", "r", "20%") is "20%"
PASS computedStyle("circle", "r", "-200px") is "-200px"
PASS computedStyle("circle", "r", "auto") is "0px"
PASS computedStyle("circle", "r", "100   px") is "0px"
PASS computedStyle("circle", "r", "100px;") is "0px"
PASS computedStyle("circle", "r", "100px !important") is "0px"
PASS computedStyle("circle", "r", "{ 100px }") is "0px"
PASS successfullyParsed is true

TEST COMPLETE

