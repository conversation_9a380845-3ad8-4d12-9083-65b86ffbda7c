<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.5.26 clear</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
body {overflow: hidden;}
P { margin: 0; padding: 0; text-align:  justify;}

DIV.menu {float: left; clear: left; width: 11em;
          margin: 0; margin-bottom: 10px; padding: 0.5em;}
DIV.menu H1 {font-size: 1.2em; margin: 0; padding: 0;}
DIV.menu UL {margin: 0.2em 0.3em 0.2em 1em; padding: 0;}
DIV.article {padding: 0.5em; margin: 0; margin-left: 14em; margin-right: 2em;
             color: black; background: yellow; clear: none;}
</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>P { margin: 0; padding: 0; text-align:  justify;}

DIV.menu {float: left; clear: left; width: 11em;
          margin: 0; margin-bottom: 10px; padding: 0.5em;}
DIV.menu H1 {font-size: 1.2em; margin: 0; padding: 0;}
DIV.menu UL {margin: 0.2em 0.3em 0.2em 1em; padding: 0;}
DIV.article {padding: 0.5em; margin: 0; margin-left: 14em; margin-right: 2em;
             color: black; background: yellow; clear: none;}

</PRE>
<HR>
<div class=menu style="color: white; background: green">
  <h1>Top menu</h1>
  <ul>
    <li>green
    <li>white text
    <li>0.5em padding
    <li>0.5em margin
  </ul>
</div>

<div class=menu style="color: white; background: blue">
  <h1>Bottom menu</h1>
  <ul>
    <li>blue background
    <li>white text
    <li>0.5em padding
    <li>0.5em margin
  </ul>
</div>

<div class=article>
 <h1>The 'clear' property</h1>
 <p>This page has two floating "menus" on the side; one has a green background, and the other a blue background.  Due to settings on the 'float' and 'clear' properties, the two menus should appear on the left side of the page, the blue below the green. There should be a 10px gap between the two menus. There should also be a 10px gap between the top menu and the top of the page.
 </p>
 <P>The top of the yellow rectangle (to which this paragraph belongs) should be vertically aligned with the top of the green rectangle.
 </P>
</div>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><div class=menu style="color: white; background: green">
  <h1>Top menu</h1>
  <ul>
    <li>green
    <li>white text
    <li>0.5em padding
    <li>0.5em margin
  </ul>
</div>

<div class=menu style="color: white; background: blue">
  <h1>Bottom menu</h1>
  <ul>
    <li>blue background
    <li>white text
    <li>0.5em padding
    <li>0.5em margin
  </ul>
</div>

<div class=article>
 <h1>The 'clear' property</h1>
 <p>This page has two floating "menus" on the side; one has a green background, and the other a blue background.  Due to settings on the 'float' and 'clear' properties, the two menus should appear on the left side of the page, the blue below the green. There should be a 10px gap between the two menus. There should also be a 10px gap between the top menu and the top of the page.
 </p>
 <P>The top of the yellow rectangle (to which this paragraph belongs) should be vertically aligned with the top of the green rectangle.
 </P>
</div>
</TD></TR></TABLE></BODY>
</HTML>
