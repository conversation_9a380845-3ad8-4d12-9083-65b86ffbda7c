<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<script src="../resources/js-test.js"></script>
<script src="../resources/gc.js"></script>
</head>
<body>
<script>

description("Test that window.crypto wrapper preserves custom properties.");
jsTestIsAsync = true;

function startTest()
{
    window.crypto.foo = "bar";
    gc();
    setTimeout(continueTest, 10);
}

function continueTest()
{
    gc();
    setTimeout(finishTest, 10);
}

function finishTest()
{
    gc();
    shouldBe('window.crypto.foo', '"bar"');
    finishJSTest();
}

window.onload = startTest;

</script>
</body>
</html>
