<html>
<head>
<script src="../../resources/js-test.js"></script>
</head>
<body>
<p id="description"></p>
<input id="input1" type="text"></input>
<input id="input2" type="text"></input>
<input id="input3" type="text"></input>
<script>
jsTestIsAsync = true;

description("Checks that the relatedTarget attribute for FocusEvent objects is being set correctly when focusin/focusout events are dispatched.  <b>Press tab four times</b> to dispatch a focusin and focusout event for each of the inputs below.");

var input1 = document.getElementById('input1');
var input2 = document.getElementById('input2');
var input3 = document.getElementById('input3');

input1.addEventListener("focusin", function(event) { shouldBeNull('event.relatedTarget'); });
input1.addEventListener("focusout", function(event) { shouldBe('event.relatedTarget', 'input2'); });
input1.addEventListener("focus", function(event) { shouldBeNull('event.relatedTarget'); });
input1.addEventListener("blur", function(event) { shouldBe('event.relatedTarget', 'input2'); });
input2.addEventListener("focusin", function(event) { shouldBe('event.relatedTarget', 'input1'); });
input2.addEventListener("focusout", function(event) { shouldBe('event.relatedTarget', 'input3'); });
input2.addEventListener("focus", function(event) { shouldBe('event.relatedTarget', 'input1'); });
input2.addEventListener("blur", function(event) { shouldBe('event.relatedTarget', 'input3'); finishJSTest(); });

input1.focus();
for (var i = 0; i < 2; i++) {
    eventSender.keyDown('\t');
}
</script>
</body>
</html>
