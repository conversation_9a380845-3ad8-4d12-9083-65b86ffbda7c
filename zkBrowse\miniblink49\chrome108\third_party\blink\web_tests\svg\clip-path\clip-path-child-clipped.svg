<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<defs>
    <path id="star" d="m 100,0 60,170 -160,-110 200,0 -170,110 z" transform="translate(40,40)"/>
    <clipPath id="clipClip">
        <use xlink:href="#star" clip-rule="evenodd"/>
    </clipPath>
    <clipPath id="clip">
        <circle cx="138" cy="130" r="50" />
        <use xlink:href="#star" clip-rule="evenodd" transform="translate(20,20)"/>
    </clipPath>
</defs>

<rect x="40" y="40" height="300" width="300" style="fill:green;clip-path:url(#clip);"/>
</svg>
