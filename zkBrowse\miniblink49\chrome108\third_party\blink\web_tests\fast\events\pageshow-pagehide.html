<p>Test pageshow/pagehide event behavior in subframes.</p>
<iframe src="resources/pageshow-pagehide-subframe.html"></iframe>
<pre id="log"></pre>
<script>
if (window.testRunner) {
    testRunner.dumpAsText();
    testRunner.waitUntilDone();
}

function log(message)
{
    var log = document.getElementById("log");
    log.innerHTML += message + "\n";
}

var state = 0;

log("***Original load - onload and pageshow events should fire for subframes, and then for main frame***");

window.onload = function(evt) {
    log("Main frame window.onload");
}

window.addEventListener("pageshow", function(evt) {
    log("Main frame window.onpageshow" + ", target = " + evt.target + ", persisted = " + evt.persisted);

    log("***Navigating bottom-level subframe, onpagehide events should fire for subsubframe***");
    frames[0].frames[0].location = "resources/pageshow-pagehide-subsubframe-2.html";
}, true);

window.onpagehide = function(evt) {
    log("Main frame window.onpagehide" + ", target = " + evt.target + ", persisted = " + evt.persisted);
}

window.addEventListener("message", function(evt) {
    if (evt.data == "subframe-loaded") {
        if (state == 1) {
            log("***Done***");
            if (window.testRunner)
                testRunner.notifyDone();
        }
    } else if (evt.data == "subsubframe-loaded") {
        if (state == 0) {
            log("***Navigating mid-level subframe, onpagehide events should fire for both subframes***");
            frames[0].location = "resources/pageshow-pagehide-subframe-2.html";
            state = 1;
        }
    }
}, true);

</script>
