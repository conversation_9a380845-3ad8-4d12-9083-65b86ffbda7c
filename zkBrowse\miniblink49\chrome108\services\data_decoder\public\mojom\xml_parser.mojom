// Copyright 2017 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// Interface used to parse XML files into a base::Value tree.
// Ignores comments and white-spaces.
// Note also that the text content of elements is returned as base::Value of
// type STRING (UTF-8 encoded): no effort is made by the parser to parse numeric
// values.

module data_decoder.mojom;

import "mojo/public/mojom/base/values.mojom";

interface XmlParser {
  const string kTypeKey = "type";
  const string kTagKey = "tag";
  const string kTextKey = "text";
  const string kAttributesKey = "attributes";
  const string kChildrenKey = "children";
  const string kNamespacesKey = "namespaces";

  const string kElementType = "element";
  const string kTextNodeType = "text";
  const string kCDataNodeType = "cdata";

  enum WhitespaceBehavior {
    // Ignore any whitespace in the parsed XML.
    kIgnore,
    // Preserve significant whitespace in the parsed XML. "Significant"
    // whitespace is determined by libxml.
    kPreserveSignificant,
  };

  // Parses the input XML and returns a Value with its content. If parsing
  // failed that value is empty and an error is set:
  //
  // For example the following XML:
  //
  // <library xmlns='http://library' xmlns:foo='http://foo.com'>
  //   <book foo:id="k123">
  //     <author>Isaac Newton</author>
  //     <title><![CDATA[Philosophiae Naturalis Principia Mathematica]]</title>
  //     <price>40.95</price>
  //   </book>
  //   <book foo:id="k456">
  //     <author>Dr. Seuss</author>
  //     <title>Green Eggs and Ham</title>
  //     <foo:price>4.95</price>
  //   </book>
  // </library>
  //
  // becomes (base::Value dictionary represented as JSON):
  //
  // {"type": "element",
  //  "tag": "library",
  //  "namespaces": {"": "http://library", "foo": "http://foo.com"},
  //  "children": [
  //    {"type": "element",
  //     "tag": "book",
  //     "attributes": {"foo:id": "k123"},
  //     "children": [
  //       {"type": "element",
  //        "tag": "author",
  //        "children": [{"type": "text", "text": "Isaac Newton"}]},
  //        {"type": "element",
  //         "tag": "title",
  //         "children": [
  //           {"type": "cdata",
  //            "text": "Philosophiae Naturalis Principia Mathematica"}]},
  //        {"type": "element",
  //         "tag": "price",
  //         "children": [{"type": "text", "text": "40.95"}]}
  //     ]},
  //     {"type": "element",
  //      "tag": "book",
  //      "attributes": {"foo:id": "k456"},
  //      "children": [
  //        {"type": "element",
  //         "tag": "author",
  //         "children": [{"type": "text", "text": "Dr. Seuss"}]},
  //         {"type": "element",
  //          "tag": "title",
  //          "children": [{"type": "text", "text": "Green Eggs and Ham"}]},
  //         {"type": "element", "tag": "foo:kids"},
  //         {"type": "element",
  //          "tag": "foo:price",
  //          "children": [{"type": "text", "text": "4.95"}],
  //         }
  //       }
  //      ]
  //    }
  // ]}
  //
  // Note that the client library provides convenience methods for accessing
  // data from the returned base::Value dictionary structure (see
  // safe_parser_xml.h).
  Parse(string xml, WhitespaceBehavior whitespace_behavior) =>
      (mojo_base.mojom.Value? result, string? error);
};
