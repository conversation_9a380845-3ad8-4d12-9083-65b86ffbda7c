<!DOCTYPE html>
<p>This test ensures <PERSON>link does fire a click event on the parent element of a clicked text node
even when the text node has been removed in mouseup event.</p>
<div id="test"></div>
<div id="target" style="display:inline-block" onmouseup="mouseup()" onclick="test.innerHTML = 'PASS';">Click Here</div>
<script>
const test = document.getElementById('test');
const target = document.getElementById('target');

function mouseup() {
  test.innerHTML = 'FAIL';
  target.firstChild.remove();
}

if (window.testRunner) {
  testRunner.dumpAsText();
  if (!window.eventSender) {
    test.innerHTML = 'FAIL - this test requires eventSender';
  } else {
    eventSender.mouseMoveTo(target.offsetLeft + target.offsetWidth / 2,
                            target.offsetTop + target.offsetHeight / 2);
    eventSender.mouseDown();
    eventSender.leapForward(200);
    eventSender.mouseUp();
  }
}
</script>
