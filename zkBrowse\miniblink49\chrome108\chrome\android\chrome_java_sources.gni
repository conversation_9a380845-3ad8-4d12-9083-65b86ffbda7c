# Copyright 2019 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

chrome_java_sources = [
  "java/src/com/google/android/apps/chrome/appwidget/bookmarks/BookmarkThumbnailWidgetProvider.java",
  "java/src/org/chromium/chrome/browser/ActivityTabProvider.java",
  "java/src/org/chromium/chrome/browser/ActivityUtils.java",
  "java/src/org/chromium/chrome/browser/AppHooks.java",
  "java/src/org/chromium/chrome/browser/AppHooksModule.java",
  "java/src/org/chromium/chrome/browser/AppIndexingUtil.java",
  "java/src/org/chromium/chrome/browser/ApplicationLifetime.java",
  "java/src/org/chromium/chrome/browser/BackPressHelper.java",
  "java/src/org/chromium/chrome/browser/BackupSigninProcessor.java",
  "java/src/org/chromium/chrome/browser/BrowserRestartActivity.java",
  "java/src/org/chromium/chrome/browser/ChromeActionModeHandler.java",
  "java/src/org/chromium/chrome/browser/ChromeActivitySessionTracker.java",
  "java/src/org/chromium/chrome/browser/ChromeApplicationImpl.java",
  "java/src/org/chromium/chrome/browser/ChromeBackgroundServiceImpl.java",
  "java/src/org/chromium/chrome/browser/ChromeBackupAgentImpl.java",
  "java/src/org/chromium/chrome/browser/ChromeBackupWatcher.java",
  "java/src/org/chromium/chrome/browser/ChromeBaseAppCompatActivity.java",
  "java/src/org/chromium/chrome/browser/ChromeInactivityTracker.java",
  "java/src/org/chromium/chrome/browser/ChromeKeyboardVisibilityDelegate.java",
  "java/src/org/chromium/chrome/browser/ChromeLocalizationUtils.java",
  "java/src/org/chromium/chrome/browser/ChromePowerModeVoter.java",
  "java/src/org/chromium/chrome/browser/ChromeSemanticColorUtils.java",
  "java/src/org/chromium/chrome/browser/ChromeStrictMode.java",
  "java/src/org/chromium/chrome/browser/ChromeStringConstants.java",
  "java/src/org/chromium/chrome/browser/ChromeTabbedActivity.java",
  "java/src/org/chromium/chrome/browser/ChromeTabbedActivity2.java",
  "java/src/org/chromium/chrome/browser/ChromeWindow.java",
  "java/src/org/chromium/chrome/browser/DefaultBrowserInfo.java",
  "java/src/org/chromium/chrome/browser/DefaultBrowserInfo2.java",
  "java/src/org/chromium/chrome/browser/DelayedScreenLockIntentHandler.java",
  "java/src/org/chromium/chrome/browser/DevToolsServer.java",
  "java/src/org/chromium/chrome/browser/FileProviderHelper.java",
  "java/src/org/chromium/chrome/browser/IntentHandler.java",
  "java/src/org/chromium/chrome/browser/KeyboardShortcuts.java",
  "java/src/org/chromium/chrome/browser/LaunchIntentDispatcher.java",
  "java/src/org/chromium/chrome/browser/LauncherShortcutActivity.java",
  "java/src/org/chromium/chrome/browser/NearOomMonitor.java",
  "java/src/org/chromium/chrome/browser/PlayServicesVersionInfo.java",
  "java/src/org/chromium/chrome/browser/PowerBroadcastReceiver.java",
  "java/src/org/chromium/chrome/browser/ServiceTabLauncher.java",
  "java/src/org/chromium/chrome/browser/ShortcutHelper.java",
  "java/src/org/chromium/chrome/browser/SnackbarActivity.java",
  "java/src/org/chromium/chrome/browser/SwipeRefreshHandler.java",
  "java/src/org/chromium/chrome/browser/SynchronousInitializationActivity.java",
  "java/src/org/chromium/chrome/browser/TabUsageTracker.java",
  "java/src/org/chromium/chrome/browser/TabbedModeTabDelegateFactory.java",
  "java/src/org/chromium/chrome/browser/UndoRefocusHelper.java",
  "java/src/org/chromium/chrome/browser/WarmupManager.java",
  "java/src/org/chromium/chrome/browser/WebContentsFactory.java",
  "java/src/org/chromium/chrome/browser/ZoomController.java",
  "java/src/org/chromium/chrome/browser/about_settings/AboutChromePreferenceOSVersion.java",
  "java/src/org/chromium/chrome/browser/about_settings/AboutChromeSettings.java",
  "java/src/org/chromium/chrome/browser/about_settings/AboutSettingsBridge.java",
  "java/src/org/chromium/chrome/browser/about_settings/HyperlinkPreference.java",
  "java/src/org/chromium/chrome/browser/about_settings/LegalInformationSettings.java",
  "java/src/org/chromium/chrome/browser/accessibility/AccessibilityTabHelper.java",
  "java/src/org/chromium/chrome/browser/accessibility/settings/ChromeAccessibilitySettingsDelegate.java",
  "java/src/org/chromium/chrome/browser/accessibility_tab_switcher/AccessibilityTabModelAdapter.java",
  "java/src/org/chromium/chrome/browser/accessibility_tab_switcher/AccessibilityTabModelListItem.java",
  "java/src/org/chromium/chrome/browser/accessibility_tab_switcher/AccessibilityTabModelListView.java",
  "java/src/org/chromium/chrome/browser/accessibility_tab_switcher/AccessibilityTabModelWrapper.java",
  "java/src/org/chromium/chrome/browser/accessibility_tab_switcher/OverviewListLayout.java",
  "java/src/org/chromium/chrome/browser/announcement/AnnouncementNotificationManager.java",
  "java/src/org/chromium/chrome/browser/app/ChromeActivity.java",
  "java/src/org/chromium/chrome/browser/app/appmenu/AppMenuPropertiesDelegateImpl.java",
  "java/src/org/chromium/chrome/browser/app/appmenu/DividerLineMenuItemViewBinder.java",
  "java/src/org/chromium/chrome/browser/app/appmenu/IncognitoMenuItemViewBinder.java",
  "java/src/org/chromium/chrome/browser/app/appmenu/UpdateMenuItemViewBinder.java",
  "java/src/org/chromium/chrome/browser/app/bluetooth/BluetoothNotificationServiceImpl.java",
  "java/src/org/chromium/chrome/browser/app/bookmarks/BookmarkActivity.java",
  "java/src/org/chromium/chrome/browser/app/bookmarks/BookmarkAddEditFolderActivity.java",
  "java/src/org/chromium/chrome/browser/app/bookmarks/BookmarkEditActivity.java",
  "java/src/org/chromium/chrome/browser/app/bookmarks/BookmarkFolderSelectActivity.java",
  "java/src/org/chromium/chrome/browser/app/creator/CreatorActivity.java",
  "java/src/org/chromium/chrome/browser/app/download/DownloadMessageUiDelegate.java",
  "java/src/org/chromium/chrome/browser/app/download/home/<USER>",
  "java/src/org/chromium/chrome/browser/app/download/home/<USER>",
  "java/src/org/chromium/chrome/browser/app/download/home/<USER>",
  "java/src/org/chromium/chrome/browser/app/download/home/<USER>",
  "java/src/org/chromium/chrome/browser/app/download/home/<USER>",
  "java/src/org/chromium/chrome/browser/app/download/home/<USER>",
  "java/src/org/chromium/chrome/browser/app/feature_guide/notifications/FeatureNotificationGuideDelegate.java",
  "java/src/org/chromium/chrome/browser/app/feed/FeedActionDelegateImpl.java",
  "java/src/org/chromium/chrome/browser/app/feed/FeedServiceDependencyProviderFactoryImpl.java",
  "java/src/org/chromium/chrome/browser/app/feed/FeedServiceUtilImpl.java",
  "java/src/org/chromium/chrome/browser/app/feed/NavigationRecorder.java",
  "java/src/org/chromium/chrome/browser/app/feed/feedmanagement/FeedManagementActivity.java",
  "java/src/org/chromium/chrome/browser/app/feed/followmanagement/FollowManagementActivity.java",
  "java/src/org/chromium/chrome/browser/app/flags/ChromeCachedFlags.java",
  "java/src/org/chromium/chrome/browser/app/metrics/LaunchCauseMetrics.java",
  "java/src/org/chromium/chrome/browser/app/metrics/TabbedActivityLaunchCauseMetrics.java",
  "java/src/org/chromium/chrome/browser/app/notifications/ContextualNotificationPermissionRequesterImpl.java",
  "java/src/org/chromium/chrome/browser/app/omnibox/OmniboxPedalDelegateImpl.java",
  "java/src/org/chromium/chrome/browser/app/reengagement/ReengagementActivity.java",
  "java/src/org/chromium/chrome/browser/app/send_tab_to_self/SendTabToSelfNotificationReceiver.java",
  "java/src/org/chromium/chrome/browser/app/tab_activity_glue/ActivityTabWebContentsDelegateAndroid.java",
  "java/src/org/chromium/chrome/browser/app/tab_activity_glue/ReparentingDelegateFactory.java",
  "java/src/org/chromium/chrome/browser/app/tab_activity_glue/ReparentingTask.java",
  "java/src/org/chromium/chrome/browser/app/tab_activity_glue/TabReparentingController.java",
  "java/src/org/chromium/chrome/browser/app/tabmodel/AsyncTabParamsManagerSingleton.java",
  "java/src/org/chromium/chrome/browser/app/tabmodel/ChromeNextTabPolicySupplier.java",
  "java/src/org/chromium/chrome/browser/app/tabmodel/ChromeTabModelFilterFactory.java",
  "java/src/org/chromium/chrome/browser/app/tabmodel/CustomTabsTabModelOrchestrator.java",
  "java/src/org/chromium/chrome/browser/app/tabmodel/DefaultTabModelSelectorFactory.java",
  "java/src/org/chromium/chrome/browser/app/tabmodel/TabModelOrchestrator.java",
  "java/src/org/chromium/chrome/browser/app/tabmodel/TabWindowManagerSingleton.java",
  "java/src/org/chromium/chrome/browser/app/tabmodel/TabbedModeTabModelOrchestrator.java",
  "java/src/org/chromium/chrome/browser/app/usb/UsbNotificationServiceImpl.java",
  "java/src/org/chromium/chrome/browser/app/video_tutorials/ChromeLanguageInfoProvider.java",
  "java/src/org/chromium/chrome/browser/app/video_tutorials/NewTabPageVideoIPHManager.java",
  "java/src/org/chromium/chrome/browser/app/video_tutorials/VideoPlayerActivity.java",
  "java/src/org/chromium/chrome/browser/app/video_tutorials/VideoTutorialListActivity.java",
  "java/src/org/chromium/chrome/browser/app/video_tutorials/VideoTutorialShareHelper.java",
  "java/src/org/chromium/chrome/browser/app/video_tutorials/VideoTutorialsServiceUtils.java",
  "java/src/org/chromium/chrome/browser/autofill/AutofillExpirationDateFixFlowBridge.java",
  "java/src/org/chromium/chrome/browser/autofill/AutofillExpirationDateFixFlowPrompt.java",
  "java/src/org/chromium/chrome/browser/autofill/AutofillLogger.java",
  "java/src/org/chromium/chrome/browser/autofill/AutofillMessageConfirmFlowBridge.java",
  "java/src/org/chromium/chrome/browser/autofill/AutofillNameFixFlowBridge.java",
  "java/src/org/chromium/chrome/browser/autofill/AutofillNameFixFlowPrompt.java",
  "java/src/org/chromium/chrome/browser/autofill/AutofillPopupBridge.java",
  "java/src/org/chromium/chrome/browser/autofill/AutofillSaveCardConfirmFlowPrompt.java",
  "java/src/org/chromium/chrome/browser/autofill/AutofillSaveCardPromptBase.java",
  "java/src/org/chromium/chrome/browser/autofill/AutofillSnackbarController.java",
  "java/src/org/chromium/chrome/browser/autofill/AutofillUiUtils.java",
  "java/src/org/chromium/chrome/browser/autofill/CardUnmaskBridge.java",
  "java/src/org/chromium/chrome/browser/autofill/CardUnmaskPrompt.java",
  "java/src/org/chromium/chrome/browser/autofill/CreditCardScanner.java",
  "java/src/org/chromium/chrome/browser/autofill/CreditCardScannerBridge.java",
  "java/src/org/chromium/chrome/browser/autofill/LegalMessageLine.java",
  "java/src/org/chromium/chrome/browser/autofill/PersonalDataManager.java",
  "java/src/org/chromium/chrome/browser/autofill/PhoneNumberUtil.java",
  "java/src/org/chromium/chrome/browser/autofill/SaveUpdateAddressProfilePrompt.java",
  "java/src/org/chromium/chrome/browser/autofill/SaveUpdateAddressProfilePromptController.java",
  "java/src/org/chromium/chrome/browser/autofill/VirtualCardEnrollmentDelegate.java",
  "java/src/org/chromium/chrome/browser/autofill/VirtualCardEnrollmentDialogViewBridge.java",
  "java/src/org/chromium/chrome/browser/autofill/prefeditor/DropdownFieldAdapter.java",
  "java/src/org/chromium/chrome/browser/autofill/prefeditor/EditorBase.java",
  "java/src/org/chromium/chrome/browser/autofill/prefeditor/EditorDialog.java",
  "java/src/org/chromium/chrome/browser/autofill/prefeditor/EditorDialogToolbar.java",
  "java/src/org/chromium/chrome/browser/autofill/prefeditor/EditorDropdownField.java",
  "java/src/org/chromium/chrome/browser/autofill/prefeditor/EditorIconsField.java",
  "java/src/org/chromium/chrome/browser/autofill/prefeditor/EditorLabelField.java",
  "java/src/org/chromium/chrome/browser/autofill/prefeditor/EditorModel.java",
  "java/src/org/chromium/chrome/browser/autofill/prefeditor/ExpandableGridView.java",
  "java/src/org/chromium/chrome/browser/autofill/prefeditor/HintedDropDownAdapter.java",
  "java/src/org/chromium/chrome/browser/autofill/prefeditor/HintedDropDownAdapterWithPlusIcon.java",
  "java/src/org/chromium/chrome/browser/autofill/settings/AddressEditor.java",
  "java/src/org/chromium/chrome/browser/autofill/settings/AndroidPaymentAppPreference.java",
  "java/src/org/chromium/chrome/browser/autofill/settings/AndroidPaymentAppsFragment.java",
  "java/src/org/chromium/chrome/browser/autofill/settings/AutofillCreditCardEditor.java",
  "java/src/org/chromium/chrome/browser/autofill/settings/AutofillEditLinkPreference.java",
  "java/src/org/chromium/chrome/browser/autofill/settings/AutofillEditorBase.java",
  "java/src/org/chromium/chrome/browser/autofill/settings/AutofillLocalCardEditor.java",
  "java/src/org/chromium/chrome/browser/autofill/settings/AutofillPaymentMethodsDelegate.java",
  "java/src/org/chromium/chrome/browser/autofill/settings/AutofillPaymentMethodsFragment.java",
  "java/src/org/chromium/chrome/browser/autofill/settings/AutofillProfileBridge.java",
  "java/src/org/chromium/chrome/browser/autofill/settings/AutofillProfileEditorPreference.java",
  "java/src/org/chromium/chrome/browser/autofill/settings/AutofillProfilesFragment.java",
  "java/src/org/chromium/chrome/browser/autofill/settings/AutofillServerCardEditor.java",
  "java/src/org/chromium/chrome/browser/autofill/settings/AutofillVirtualCardEnrollmentDialog.java",
  "java/src/org/chromium/chrome/browser/autofill/settings/AutofillVirtualCardUnenrollmentDialog.java",
  "java/src/org/chromium/chrome/browser/autofill/settings/CardEditor.java",
  "java/src/org/chromium/chrome/browser/autofill/settings/CreditCardNumberFormattingTextWatcher.java",
  "java/src/org/chromium/chrome/browser/autofill/settings/VirtualCardEnrollmentFields.java",
  "java/src/org/chromium/chrome/browser/autofill_assistant/AutofillAssistantPreferenceFragment.java",
  "java/src/org/chromium/chrome/browser/background_sync/BackgroundSyncBackgroundTask.java",
  "java/src/org/chromium/chrome/browser/background_sync/BackgroundSyncBackgroundTaskScheduler.java",
  "java/src/org/chromium/chrome/browser/background_sync/GooglePlayServicesChecker.java",
  "java/src/org/chromium/chrome/browser/background_sync/PeriodicBackgroundSyncChromeWakeUpTask.java",
  "java/src/org/chromium/chrome/browser/background_task_scheduler/ChromeBackgroundTaskFactory.java",
  "java/src/org/chromium/chrome/browser/background_task_scheduler/ChromeNativeBackgroundTaskDelegate.java",
  "java/src/org/chromium/chrome/browser/background_task_scheduler/ProxyNativeTask.java",
  "java/src/org/chromium/chrome/browser/bookmarks/BookmarkActionBar.java",
  "java/src/org/chromium/chrome/browser/bookmarks/BookmarkBridge.java",
  "java/src/org/chromium/chrome/browser/bookmarks/BookmarkDelegate.java",
  "java/src/org/chromium/chrome/browser/bookmarks/BookmarkFeatures.java",
  "java/src/org/chromium/chrome/browser/bookmarks/BookmarkFolderRow.java",
  "java/src/org/chromium/chrome/browser/bookmarks/BookmarkItemRow.java",
  "java/src/org/chromium/chrome/browser/bookmarks/BookmarkItemsAdapter.java",
  "java/src/org/chromium/chrome/browser/bookmarks/BookmarkListEntry.java",
  "java/src/org/chromium/chrome/browser/bookmarks/BookmarkManager.java",
  "java/src/org/chromium/chrome/browser/bookmarks/BookmarkModel.java",
  "java/src/org/chromium/chrome/browser/bookmarks/BookmarkModelObserver.java",
  "java/src/org/chromium/chrome/browser/bookmarks/BookmarkPage.java",
  "java/src/org/chromium/chrome/browser/bookmarks/BookmarkPromoHeader.java",
  "java/src/org/chromium/chrome/browser/bookmarks/BookmarkRow.java",
  "java/src/org/chromium/chrome/browser/bookmarks/BookmarkSaveFlowCoordinator.java",
  "java/src/org/chromium/chrome/browser/bookmarks/BookmarkSaveFlowMediator.java",
  "java/src/org/chromium/chrome/browser/bookmarks/BookmarkSaveFlowProperties.java",
  "java/src/org/chromium/chrome/browser/bookmarks/BookmarkSaveFlowViewBinder.java",
  "java/src/org/chromium/chrome/browser/bookmarks/BookmarkTextInputLayout.java",
  "java/src/org/chromium/chrome/browser/bookmarks/BookmarkUIObserver.java",
  "java/src/org/chromium/chrome/browser/bookmarks/BookmarkUIState.java",
  "java/src/org/chromium/chrome/browser/bookmarks/BookmarkUndoController.java",
  "java/src/org/chromium/chrome/browser/bookmarks/BookmarkUtils.java",
  "java/src/org/chromium/chrome/browser/bookmarks/PowerBookmarkMetrics.java",
  "java/src/org/chromium/chrome/browser/bookmarks/PowerBookmarkShoppingItemRow.java",
  "java/src/org/chromium/chrome/browser/bookmarks/PowerBookmarkTagChipList.java",
  "java/src/org/chromium/chrome/browser/bookmarks/PowerBookmarkUtils.java",
  "java/src/org/chromium/chrome/browser/bookmarks/ReadingListFeatures.java",
  "java/src/org/chromium/chrome/browser/bookmarks/ReadingListSectionHeader.java",
  "java/src/org/chromium/chrome/browser/bookmarks/TabBookmarker.java",
  "java/src/org/chromium/chrome/browser/bookmarkswidget/BookmarkWidgetProvider.java",
  "java/src/org/chromium/chrome/browser/bookmarkswidget/BookmarkWidgetProxy.java",
  "java/src/org/chromium/chrome/browser/bookmarkswidget/BookmarkWidgetServiceImpl.java",
  "java/src/org/chromium/chrome/browser/browserservices/BrowserServicesStore.java",
  "java/src/org/chromium/chrome/browser/browserservices/ClearDataDialogActivity.java",
  "java/src/org/chromium/chrome/browser/browserservices/ClearDataDialogResultRecorder.java",
  "java/src/org/chromium/chrome/browser/browserservices/InstalledWebappBroadcastReceiver.java",
  "java/src/org/chromium/chrome/browser/browserservices/InstalledWebappDataRecorder.java",
  "java/src/org/chromium/chrome/browser/browserservices/InstalledWebappDataRegister.java",
  "java/src/org/chromium/chrome/browser/browserservices/InstalledWebappRegistrar.java",
  "java/src/org/chromium/chrome/browser/browserservices/ManageTrustedWebActivityDataActivity.java",
  "java/src/org/chromium/chrome/browser/browserservices/PostMessageHandler.java",
  "java/src/org/chromium/chrome/browser/browserservices/QualityEnforcer.java",
  "java/src/org/chromium/chrome/browser/browserservices/SessionDataHolder.java",
  "java/src/org/chromium/chrome/browser/browserservices/SessionHandler.java",
  "java/src/org/chromium/chrome/browser/browserservices/TrustedWebActivityClient.java",
  "java/src/org/chromium/chrome/browser/browserservices/TrustedWebActivityClientWrappers.java",
  "java/src/org/chromium/chrome/browser/browserservices/TrustedWebActivitySettingsLauncher.java",
  "java/src/org/chromium/chrome/browser/browserservices/digitalgoods/AcknowledgeConverter.java",
  "java/src/org/chromium/chrome/browser/browserservices/digitalgoods/ConsumeConverter.java",
  "java/src/org/chromium/chrome/browser/browserservices/digitalgoods/DigitalGoodsAdapter.java",
  "java/src/org/chromium/chrome/browser/browserservices/digitalgoods/DigitalGoodsConverter.java",
  "java/src/org/chromium/chrome/browser/browserservices/digitalgoods/DigitalGoodsFactoryFactory.java",
  "java/src/org/chromium/chrome/browser/browserservices/digitalgoods/DigitalGoodsFactoryImpl.java",
  "java/src/org/chromium/chrome/browser/browserservices/digitalgoods/DigitalGoodsImpl.java",
  "java/src/org/chromium/chrome/browser/browserservices/digitalgoods/GetDetailsConverter.java",
  "java/src/org/chromium/chrome/browser/browserservices/digitalgoods/ListPurchaseHistoryConverter.java",
  "java/src/org/chromium/chrome/browser/browserservices/digitalgoods/ListPurchasesConverter.java",
  "java/src/org/chromium/chrome/browser/browserservices/digitalgoods/SiteIsolator.java",
  "java/src/org/chromium/chrome/browser/browserservices/permissiondelegation/InstalledWebappBridge.java",
  "java/src/org/chromium/chrome/browser/browserservices/permissiondelegation/InstalledWebappGeolocationBridge.java",
  "java/src/org/chromium/chrome/browser/browserservices/permissiondelegation/InstalledWebappPermissionManager.java",
  "java/src/org/chromium/chrome/browser/browserservices/permissiondelegation/InstalledWebappPermissionStore.java",
  "java/src/org/chromium/chrome/browser/browserservices/permissiondelegation/LocationPermissionUpdater.java",
  "java/src/org/chromium/chrome/browser/browserservices/permissiondelegation/NotificationChannelPreserver.java",
  "java/src/org/chromium/chrome/browser/browserservices/permissiondelegation/NotificationPermissionUpdater.java",
  "java/src/org/chromium/chrome/browser/browserservices/permissiondelegation/PermissionStatus.java",
  "java/src/org/chromium/chrome/browser/browserservices/permissiondelegation/PermissionUpdater.java",
  "java/src/org/chromium/chrome/browser/browserservices/trustedwebactivityui/TwaFinishHandler.java",
  "java/src/org/chromium/chrome/browser/browserservices/trustedwebactivityui/TwaIntentHandlingStrategy.java",
  "java/src/org/chromium/chrome/browser/browserservices/trustedwebactivityui/controller/TrustedWebActivityBrowserControlsVisibilityManager.java",
  "java/src/org/chromium/chrome/browser/browserservices/trustedwebactivityui/sharing/TwaSharingController.java",
  "java/src/org/chromium/chrome/browser/browserservices/ui/SharedActivityCoordinator.java",
  "java/src/org/chromium/chrome/browser/browserservices/ui/TrustedWebActivityModel.java",
  "java/src/org/chromium/chrome/browser/browserservices/ui/controller/CurrentPageVerifier.java",
  "java/src/org/chromium/chrome/browser/browserservices/ui/controller/DisclosureController.java",
  "java/src/org/chromium/chrome/browser/browserservices/ui/controller/EmptyVerifier.java",
  "java/src/org/chromium/chrome/browser/browserservices/ui/controller/Verifier.java",
  "java/src/org/chromium/chrome/browser/browserservices/ui/controller/trustedwebactivity/ClientPackageNameProvider.java",
  "java/src/org/chromium/chrome/browser/browserservices/ui/controller/trustedwebactivity/TrustedWebActivityDisclosureController.java",
  "java/src/org/chromium/chrome/browser/browserservices/ui/controller/trustedwebactivity/TrustedWebActivityOpenTimeRecorder.java",
  "java/src/org/chromium/chrome/browser/browserservices/ui/controller/trustedwebactivity/TwaVerifier.java",
  "java/src/org/chromium/chrome/browser/browserservices/ui/controller/webapps/AddToHomescreenVerifier.java",
  "java/src/org/chromium/chrome/browser/browserservices/ui/controller/webapps/WebApkVerifier.java",
  "java/src/org/chromium/chrome/browser/browserservices/ui/controller/webapps/WebappDisclosureController.java",
  "java/src/org/chromium/chrome/browser/browserservices/ui/controller/webapps/WebappVerifier.java",
  "java/src/org/chromium/chrome/browser/browserservices/ui/splashscreen/SplashController.java",
  "java/src/org/chromium/chrome/browser/browserservices/ui/splashscreen/SplashDelegate.java",
  "java/src/org/chromium/chrome/browser/browserservices/ui/splashscreen/SplashscreenObserver.java",
  "java/src/org/chromium/chrome/browser/browserservices/ui/splashscreen/trustedwebactivity/SplashImageHolder.java",
  "java/src/org/chromium/chrome/browser/browserservices/ui/splashscreen/trustedwebactivity/TwaSplashController.java",
  "java/src/org/chromium/chrome/browser/browserservices/ui/splashscreen/webapps/WebappSplashController.java",
  "java/src/org/chromium/chrome/browser/browserservices/ui/trustedwebactivity/DisclosureAcceptanceBroadcastReceiver.java",
  "java/src/org/chromium/chrome/browser/browserservices/ui/trustedwebactivity/DisclosureUiPicker.java",
  "java/src/org/chromium/chrome/browser/browserservices/ui/trustedwebactivity/TrustedWebActivityCoordinator.java",
  "java/src/org/chromium/chrome/browser/browserservices/ui/view/DisclosureInfobar.java",
  "java/src/org/chromium/chrome/browser/browserservices/ui/view/DisclosureNotification.java",
  "java/src/org/chromium/chrome/browser/browserservices/ui/view/DisclosureSnackbar.java",
  "java/src/org/chromium/chrome/browser/browsing_data/BrowsingDataBridge.java",
  "java/src/org/chromium/chrome/browser/browsing_data/BrowsingDataCounterBridge.java",
  "java/src/org/chromium/chrome/browser/browsing_data/ClearBrowsingDataCheckBoxPreference.java",
  "java/src/org/chromium/chrome/browser/browsing_data/ClearBrowsingDataFetcher.java",
  "java/src/org/chromium/chrome/browser/browsing_data/ClearBrowsingDataFragment.java",
  "java/src/org/chromium/chrome/browser/browsing_data/ClearBrowsingDataFragmentAdvanced.java",
  "java/src/org/chromium/chrome/browser/browsing_data/ClearBrowsingDataFragmentBasic.java",
  "java/src/org/chromium/chrome/browser/browsing_data/ClearBrowsingDataTabsFragment.java",
  "java/src/org/chromium/chrome/browser/browsing_data/ConfirmImportantSitesDialogFragment.java",
  "java/src/org/chromium/chrome/browser/browsing_data/OtherFormsOfHistoryDialogFragment.java",
  "java/src/org/chromium/chrome/browser/browsing_data/UrlFilter.java",
  "java/src/org/chromium/chrome/browser/browsing_data/UrlFilterBridge.java",
  "java/src/org/chromium/chrome/browser/browsing_data/UrlFilters.java",
  "java/src/org/chromium/chrome/browser/complex_tasks/TaskTabHelper.java",
  "java/src/org/chromium/chrome/browser/compositor/CompositorSurfaceManager.java",
  "java/src/org/chromium/chrome/browser/compositor/CompositorSurfaceManagerImpl.java",
  "java/src/org/chromium/chrome/browser/compositor/CompositorView.java",
  "java/src/org/chromium/chrome/browser/compositor/CompositorViewHolder.java",
  "java/src/org/chromium/chrome/browser/compositor/Invalidator.java",
  "java/src/org/chromium/chrome/browser/compositor/LayerTitleCache.java",
  "java/src/org/chromium/chrome/browser/compositor/bottombar/OverlayContentDelegate.java",
  "java/src/org/chromium/chrome/browser/compositor/bottombar/OverlayContentProgressObserver.java",
  "java/src/org/chromium/chrome/browser/compositor/bottombar/OverlayPanel.java",
  "java/src/org/chromium/chrome/browser/compositor/bottombar/OverlayPanelAnimation.java",
  "java/src/org/chromium/chrome/browser/compositor/bottombar/OverlayPanelBase.java",
  "java/src/org/chromium/chrome/browser/compositor/bottombar/OverlayPanelContent.java",
  "java/src/org/chromium/chrome/browser/compositor/bottombar/OverlayPanelContentFactory.java",
  "java/src/org/chromium/chrome/browser/compositor/bottombar/OverlayPanelInflater.java",
  "java/src/org/chromium/chrome/browser/compositor/bottombar/OverlayPanelManager.java",
  "java/src/org/chromium/chrome/browser/compositor/bottombar/OverlayPanelRepaddingTextView.java",
  "java/src/org/chromium/chrome/browser/compositor/bottombar/OverlayPanelTextViewInflater.java",
  "java/src/org/chromium/chrome/browser/compositor/bottombar/contextualsearch/ContextualSearchBarControl.java",
  "java/src/org/chromium/chrome/browser/compositor/bottombar/contextualsearch/ContextualSearchCaptionControl.java",
  "java/src/org/chromium/chrome/browser/compositor/bottombar/contextualsearch/ContextualSearchCardIconControl.java",
  "java/src/org/chromium/chrome/browser/compositor/bottombar/contextualsearch/ContextualSearchContextControl.java",
  "java/src/org/chromium/chrome/browser/compositor/bottombar/contextualsearch/ContextualSearchImageControl.java",
  "java/src/org/chromium/chrome/browser/compositor/bottombar/contextualsearch/ContextualSearchPanel.java",
  "java/src/org/chromium/chrome/browser/compositor/bottombar/contextualsearch/ContextualSearchPanelCoordinator.java",
  "java/src/org/chromium/chrome/browser/compositor/bottombar/contextualsearch/ContextualSearchPanelInterface.java",
  "java/src/org/chromium/chrome/browser/compositor/bottombar/contextualsearch/ContextualSearchPanelMetrics.java",
  "java/src/org/chromium/chrome/browser/compositor/bottombar/contextualsearch/ContextualSearchPromoControl.java",
  "java/src/org/chromium/chrome/browser/compositor/bottombar/contextualsearch/ContextualSearchQuickActionControl.java",
  "java/src/org/chromium/chrome/browser/compositor/bottombar/contextualsearch/ContextualSearchSheetContent.java",
  "java/src/org/chromium/chrome/browser/compositor/bottombar/contextualsearch/ContextualSearchTermControl.java",
  "java/src/org/chromium/chrome/browser/compositor/bottombar/contextualsearch/NoSystemGestureFrameLayout.java",
  "java/src/org/chromium/chrome/browser/compositor/bottombar/contextualsearch/RelatedSearchesControl.java",
  "java/src/org/chromium/chrome/browser/compositor/bottombar/ephemeraltab/EphemeralTabCoordinator.java",
  "java/src/org/chromium/chrome/browser/compositor/bottombar/ephemeraltab/EphemeralTabMediator.java",
  "java/src/org/chromium/chrome/browser/compositor/bottombar/ephemeraltab/EphemeralTabSheetContent.java",
  "java/src/org/chromium/chrome/browser/compositor/layouts/Layout.java",
  "java/src/org/chromium/chrome/browser/compositor/layouts/LayoutManagerChrome.java",
  "java/src/org/chromium/chrome/browser/compositor/layouts/LayoutManagerChromePhone.java",
  "java/src/org/chromium/chrome/browser/compositor/layouts/LayoutManagerChromeTablet.java",
  "java/src/org/chromium/chrome/browser/compositor/layouts/LayoutManagerHost.java",
  "java/src/org/chromium/chrome/browser/compositor/layouts/LayoutManagerImpl.java",
  "java/src/org/chromium/chrome/browser/compositor/layouts/LayoutProvider.java",
  "java/src/org/chromium/chrome/browser/compositor/layouts/LayoutRenderHost.java",
  "java/src/org/chromium/chrome/browser/compositor/layouts/LayoutUpdateHost.java",
  "java/src/org/chromium/chrome/browser/compositor/layouts/SceneChangeObserver.java",
  "java/src/org/chromium/chrome/browser/compositor/layouts/StaticLayout.java",
  "java/src/org/chromium/chrome/browser/compositor/layouts/ToolbarSwipeLayout.java",
  "java/src/org/chromium/chrome/browser/compositor/layouts/components/CompositorButton.java",
  "java/src/org/chromium/chrome/browser/compositor/layouts/components/LayoutTab.java",
  "java/src/org/chromium/chrome/browser/compositor/layouts/components/TintedCompositorButton.java",
  "java/src/org/chromium/chrome/browser/compositor/layouts/content/ContentOffsetProvider.java",
  "java/src/org/chromium/chrome/browser/compositor/layouts/content/InvalidationAwareThumbnailProvider.java",
  "java/src/org/chromium/chrome/browser/compositor/layouts/content/TabContentManager.java",
  "java/src/org/chromium/chrome/browser/compositor/layouts/content/TabContentManagerHandler.java",
  "java/src/org/chromium/chrome/browser/compositor/layouts/content/TitleBitmapFactory.java",
  "java/src/org/chromium/chrome/browser/compositor/layouts/eventfilter/AreaGestureEventFilter.java",
  "java/src/org/chromium/chrome/browser/compositor/layouts/eventfilter/BlackHoleEventFilter.java",
  "java/src/org/chromium/chrome/browser/compositor/layouts/eventfilter/GestureEventFilter.java",
  "java/src/org/chromium/chrome/browser/compositor/layouts/eventfilter/GestureHandler.java",
  "java/src/org/chromium/chrome/browser/compositor/layouts/eventfilter/OverlayPanelEventFilter.java",
  "java/src/org/chromium/chrome/browser/compositor/layouts/phone/SimpleAnimationLayout.java",
  "java/src/org/chromium/chrome/browser/compositor/layouts/phone/stack/StackScroller.java",
  "java/src/org/chromium/chrome/browser/compositor/overlays/strip/CascadingStripStacker.java",
  "java/src/org/chromium/chrome/browser/compositor/overlays/strip/ScrollingStripStacker.java",
  "java/src/org/chromium/chrome/browser/compositor/overlays/strip/StripLayoutHelper.java",
  "java/src/org/chromium/chrome/browser/compositor/overlays/strip/StripLayoutHelperManager.java",
  "java/src/org/chromium/chrome/browser/compositor/overlays/strip/StripLayoutTab.java",
  "java/src/org/chromium/chrome/browser/compositor/overlays/strip/StripScrim.java",
  "java/src/org/chromium/chrome/browser/compositor/overlays/strip/StripStacker.java",
  "java/src/org/chromium/chrome/browser/compositor/overlays/strip/TabLoadTracker.java",
  "java/src/org/chromium/chrome/browser/compositor/resources/StaticResourcePreloads.java",
  "java/src/org/chromium/chrome/browser/compositor/resources/SystemResourcePreloads.java",
  "java/src/org/chromium/chrome/browser/compositor/scene_layer/ContextualSearchSceneLayer.java",
  "java/src/org/chromium/chrome/browser/compositor/scene_layer/StaticTabSceneLayer.java",
  "java/src/org/chromium/chrome/browser/compositor/scene_layer/TabListSceneLayer.java",
  "java/src/org/chromium/chrome/browser/compositor/scene_layer/TabStripSceneLayer.java",
  "java/src/org/chromium/chrome/browser/compositor/scene_layer/ToolbarSwipeSceneLayer.java",
  "java/src/org/chromium/chrome/browser/contacts_picker/ChromePickerAdapter.java",
  "java/src/org/chromium/chrome/browser/content/ContentUtils.java",
  "java/src/org/chromium/chrome/browser/content_capture/ContentCaptureHistoryDeletionObserver.java",
  "java/src/org/chromium/chrome/browser/contextmenu/ChromeContextMenuItem.java",
  "java/src/org/chromium/chrome/browser/contextmenu/ChromeContextMenuPopulator.java",
  "java/src/org/chromium/chrome/browser/contextmenu/ChromeContextMenuPopulatorFactory.java",
  "java/src/org/chromium/chrome/browser/contextmenu/ContextMenuChipController.java",
  "java/src/org/chromium/chrome/browser/contextmenu/ContextMenuCoordinator.java",
  "java/src/org/chromium/chrome/browser/contextmenu/ContextMenuHeaderCoordinator.java",
  "java/src/org/chromium/chrome/browser/contextmenu/ContextMenuHeaderMediator.java",
  "java/src/org/chromium/chrome/browser/contextmenu/ContextMenuHeaderProperties.java",
  "java/src/org/chromium/chrome/browser/contextmenu/ContextMenuHeaderViewBinder.java",
  "java/src/org/chromium/chrome/browser/contextmenu/ContextMenuHelper.java",
  "java/src/org/chromium/chrome/browser/contextmenu/ContextMenuItemProperties.java",
  "java/src/org/chromium/chrome/browser/contextmenu/ContextMenuItemViewBinder.java",
  "java/src/org/chromium/chrome/browser/contextmenu/ContextMenuItemWithIconButtonProperties.java",
  "java/src/org/chromium/chrome/browser/contextmenu/ContextMenuItemWithIconButtonViewBinder.java",
  "java/src/org/chromium/chrome/browser/contextmenu/ContextMenuListView.java",
  "java/src/org/chromium/chrome/browser/contextmenu/ContextMenuUi.java",
  "java/src/org/chromium/chrome/browser/contextmenu/ContextMenuUtils.java",
  "java/src/org/chromium/chrome/browser/contextmenu/LensChipDelegate.java",
  "java/src/org/chromium/chrome/browser/contextualsearch/ContextualSearchContext.java",
  "java/src/org/chromium/chrome/browser/contextualsearch/ContextualSearchFieldTrial.java",
  "java/src/org/chromium/chrome/browser/contextualsearch/ContextualSearchHeuristic.java",
  "java/src/org/chromium/chrome/browser/contextualsearch/ContextualSearchHeuristics.java",
  "java/src/org/chromium/chrome/browser/contextualsearch/ContextualSearchInternalStateController.java",
  "java/src/org/chromium/chrome/browser/contextualsearch/ContextualSearchInternalStateHandler.java",
  "java/src/org/chromium/chrome/browser/contextualsearch/ContextualSearchManagementDelegate.java",
  "java/src/org/chromium/chrome/browser/contextualsearch/ContextualSearchManager.java",
  "java/src/org/chromium/chrome/browser/contextualsearch/ContextualSearchNetworkCommunicator.java",
  "java/src/org/chromium/chrome/browser/contextualsearch/ContextualSearchObserver.java",
  "java/src/org/chromium/chrome/browser/contextualsearch/ContextualSearchPolicy.java",
  "java/src/org/chromium/chrome/browser/contextualsearch/ContextualSearchPreferenceFragment.java",
  "java/src/org/chromium/chrome/browser/contextualsearch/ContextualSearchRequest.java",
  "java/src/org/chromium/chrome/browser/contextualsearch/ContextualSearchSelectionController.java",
  "java/src/org/chromium/chrome/browser/contextualsearch/ContextualSearchSelectionHandler.java",
  "java/src/org/chromium/chrome/browser/contextualsearch/ContextualSearchTabHelper.java",
  "java/src/org/chromium/chrome/browser/contextualsearch/ContextualSearchTapState.java",
  "java/src/org/chromium/chrome/browser/contextualsearch/ContextualSearchTranslation.java",
  "java/src/org/chromium/chrome/browser/contextualsearch/ContextualSearchTranslationImpl.java",
  "java/src/org/chromium/chrome/browser/contextualsearch/ContextualSearchUma.java",
  "java/src/org/chromium/chrome/browser/contextualsearch/DisableablePromoTapCounter.java",
  "java/src/org/chromium/chrome/browser/contextualsearch/RelatedSearchesList.java",
  "java/src/org/chromium/chrome/browser/contextualsearch/RelatedSearchesStamp.java",
  "java/src/org/chromium/chrome/browser/contextualsearch/RelatedSearchesUma.java",
  "java/src/org/chromium/chrome/browser/contextualsearch/ResolvedSearchTerm.java",
  "java/src/org/chromium/chrome/browser/contextualsearch/SelectionClientManager.java",
  "java/src/org/chromium/chrome/browser/contextualsearch/TapFarFromPreviousSuppression.java",
  "java/src/org/chromium/chrome/browser/contextualsearch/TapSuppressionHeuristics.java",
  "java/src/org/chromium/chrome/browser/crash/ChromeMinidumpUploadJobServiceImpl.java",
  "java/src/org/chromium/chrome/browser/crash/ChromeMinidumpUploaderDelegate.java",
  "java/src/org/chromium/chrome/browser/crash/ChromePureJavaExceptionReporter.java",
  "java/src/org/chromium/chrome/browser/crash/CrashUploadCountStore.java",
  "java/src/org/chromium/chrome/browser/crash/LogcatExtractionRunnable.java",
  "java/src/org/chromium/chrome/browser/crash/MinidumpUploadRetry.java",
  "java/src/org/chromium/chrome/browser/crash/MinidumpUploadServiceImpl.java",
  "java/src/org/chromium/chrome/browser/cryptids/ProbabilisticCryptidRenderer.java",
  "java/src/org/chromium/chrome/browser/customtabs/BaseCustomTabActivity.java",
  "java/src/org/chromium/chrome/browser/customtabs/BaseCustomTabRootUiCoordinator.java",
  "java/src/org/chromium/chrome/browser/customtabs/ClientManager.java",
  "java/src/org/chromium/chrome/browser/customtabs/CloseButtonNavigator.java",
  "java/src/org/chromium/chrome/browser/customtabs/CloseButtonVisibilityManager.java",
  "java/src/org/chromium/chrome/browser/customtabs/CustomButtonParamsImpl.java",
  "java/src/org/chromium/chrome/browser/customtabs/CustomTabActivity.java",
  "java/src/org/chromium/chrome/browser/customtabs/CustomTabActivityClientConnectionKeeper.java",
  "java/src/org/chromium/chrome/browser/customtabs/CustomTabActivityLifecycleUmaTracker.java",
  "java/src/org/chromium/chrome/browser/customtabs/CustomTabAppMenuPropertiesDelegate.java",
  "java/src/org/chromium/chrome/browser/customtabs/CustomTabBottomBarDelegate.java",
  "java/src/org/chromium/chrome/browser/customtabs/CustomTabColorProviderImpl.java",
  "java/src/org/chromium/chrome/browser/customtabs/CustomTabCompositorContentInitializer.java",
  "java/src/org/chromium/chrome/browser/customtabs/CustomTabDelegateFactory.java",
  "java/src/org/chromium/chrome/browser/customtabs/CustomTabDownloadObserver.java",
  "java/src/org/chromium/chrome/browser/customtabs/CustomTabHeightStrategy.java",
  "java/src/org/chromium/chrome/browser/customtabs/CustomTabIncognitoManager.java",
  "java/src/org/chromium/chrome/browser/customtabs/CustomTabIntentDataProvider.java",
  "java/src/org/chromium/chrome/browser/customtabs/CustomTabLaunchCauseMetrics.java",
  "java/src/org/chromium/chrome/browser/customtabs/CustomTabNavigationEventObserver.java",
  "java/src/org/chromium/chrome/browser/customtabs/CustomTabNightModeStateController.java",
  "java/src/org/chromium/chrome/browser/customtabs/CustomTabObserver.java",
  "java/src/org/chromium/chrome/browser/customtabs/CustomTabOrientationController.java",
  "java/src/org/chromium/chrome/browser/customtabs/CustomTabSessionHandler.java",
  "java/src/org/chromium/chrome/browser/customtabs/CustomTabStatusBarColorProvider.java",
  "java/src/org/chromium/chrome/browser/customtabs/CustomTabTabPersistencePolicy.java",
  "java/src/org/chromium/chrome/browser/customtabs/CustomTabTaskDescriptionHelper.java",
  "java/src/org/chromium/chrome/browser/customtabs/CustomTabTaskDescriptionIconGenerator.java",
  "java/src/org/chromium/chrome/browser/customtabs/CustomTabTopBarDelegate.java",
  "java/src/org/chromium/chrome/browser/customtabs/CustomTabTrustedCdnPublisherUrlVisibility.java",
  "java/src/org/chromium/chrome/browser/customtabs/CustomTabsClientFileProcessor.java",
  "java/src/org/chromium/chrome/browser/customtabs/CustomTabsConnection.java",
  "java/src/org/chromium/chrome/browser/customtabs/CustomTabsConnectionServiceImpl.java",
  "java/src/org/chromium/chrome/browser/customtabs/CustomTabsOpenTimeRecorder.java",
  "java/src/org/chromium/chrome/browser/customtabs/CustomTabsShareBroadcastReceiver.java",
  "java/src/org/chromium/chrome/browser/customtabs/DefaultBrowserProviderImpl.java",
  "java/src/org/chromium/chrome/browser/customtabs/FirstMeaningfulPaintObserver.java",
  "java/src/org/chromium/chrome/browser/customtabs/HiddenTabHolder.java",
  "java/src/org/chromium/chrome/browser/customtabs/IncognitoCustomTabColorProvider.java",
  "java/src/org/chromium/chrome/browser/customtabs/IncognitoCustomTabIntentDataProvider.java",
  "java/src/org/chromium/chrome/browser/customtabs/IncognitoCustomTabSnapshotController.java",
  "java/src/org/chromium/chrome/browser/customtabs/NavigationInfoCaptureTrigger.java",
  "java/src/org/chromium/chrome/browser/customtabs/PageLoadMetricsObserver.java",
  "java/src/org/chromium/chrome/browser/customtabs/PartialCustomTabHandleStrategy.java",
  "java/src/org/chromium/chrome/browser/customtabs/PartialCustomTabHeightStrategy.java",
  "java/src/org/chromium/chrome/browser/customtabs/PartialCustomTabInputMethodWrapper.java",
  "java/src/org/chromium/chrome/browser/customtabs/PartialCustomTabTabObserver.java",
  "java/src/org/chromium/chrome/browser/customtabs/ReparentingTaskProvider.java",
  "java/src/org/chromium/chrome/browser/customtabs/RequestThrottler.java",
  "java/src/org/chromium/chrome/browser/customtabs/TranslucentCustomTabActivity.java",
  "java/src/org/chromium/chrome/browser/customtabs/content/CustomTabActivityNavigationController.java",
  "java/src/org/chromium/chrome/browser/customtabs/content/CustomTabActivityTabController.java",
  "java/src/org/chromium/chrome/browser/customtabs/content/CustomTabActivityTabFactory.java",
  "java/src/org/chromium/chrome/browser/customtabs/content/CustomTabActivityTabProvider.java",
  "java/src/org/chromium/chrome/browser/customtabs/content/CustomTabIntentHandler.java",
  "java/src/org/chromium/chrome/browser/customtabs/content/CustomTabIntentHandlingStrategy.java",
  "java/src/org/chromium/chrome/browser/customtabs/content/DefaultCustomTabIntentHandlingStrategy.java",
  "java/src/org/chromium/chrome/browser/customtabs/content/TabCreationMode.java",
  "java/src/org/chromium/chrome/browser/customtabs/content/TabObserverRegistrar.java",
  "java/src/org/chromium/chrome/browser/customtabs/dependency_injection/BaseCustomTabActivityComponent.java",
  "java/src/org/chromium/chrome/browser/customtabs/dependency_injection/BaseCustomTabActivityModule.java",
  "java/src/org/chromium/chrome/browser/customtabs/features/CustomTabNavigationBarController.java",
  "java/src/org/chromium/chrome/browser/customtabs/features/ImmersiveModeController.java",
  "java/src/org/chromium/chrome/browser/customtabs/features/TabInteractionRecorder.java",
  "java/src/org/chromium/chrome/browser/customtabs/features/branding/BrandingChecker.java",
  "java/src/org/chromium/chrome/browser/customtabs/features/branding/BrandingController.java",
  "java/src/org/chromium/chrome/browser/customtabs/features/branding/BrandingDecision.java",
  "java/src/org/chromium/chrome/browser/customtabs/features/branding/SharedPreferencesBrandingTimeStorage.java",
  "java/src/org/chromium/chrome/browser/customtabs/features/branding/ToolbarBrandingDelegate.java",
  "java/src/org/chromium/chrome/browser/customtabs/features/toolbar/BrandingSecurityButtonAnimationDelegate.java",
  "java/src/org/chromium/chrome/browser/customtabs/features/toolbar/CustomTabBrowserControlsVisibilityDelegate.java",
  "java/src/org/chromium/chrome/browser/customtabs/features/toolbar/CustomTabToolbar.java",
  "java/src/org/chromium/chrome/browser/customtabs/features/toolbar/CustomTabToolbarAnimationDelegate.java",
  "java/src/org/chromium/chrome/browser/customtabs/features/toolbar/CustomTabToolbarColorController.java",
  "java/src/org/chromium/chrome/browser/customtabs/features/toolbar/CustomTabToolbarCoordinator.java",
  "java/src/org/chromium/chrome/browser/customtabs/features/toolbar/InterceptTouchLayout.java",
  "java/src/org/chromium/chrome/browser/customtabs/features/toolbar/NoOpkeyboardVisibilityDelegate.java",
  "java/src/org/chromium/chrome/browser/datareduction/DataSaverOSSetting.java",
  "java/src/org/chromium/chrome/browser/dependency_injection/ChromeActivityCommonsModule.java",
  "java/src/org/chromium/chrome/browser/dependency_injection/ChromeActivityComponent.java",
  "java/src/org/chromium/chrome/browser/dependency_injection/ChromeAppComponent.java",
  "java/src/org/chromium/chrome/browser/dependency_injection/ChromeAppModule.java",
  "java/src/org/chromium/chrome/browser/dependency_injection/ChromeCommonQualifiers.java",
  "java/src/org/chromium/chrome/browser/dependency_injection/ModuleFactoryOverrides.java",
  "java/src/org/chromium/chrome/browser/device_dialog/ChromeBluetoothChooserAndroidDelegate.java",
  "java/src/org/chromium/chrome/browser/device_dialog/ChromeBluetoothScanningPromptAndroidDelegate.java",
  "java/src/org/chromium/chrome/browser/device_dialog/UsbChooserDialog.java",
  "java/src/org/chromium/chrome/browser/directactions/ChromeDirectActionIds.java",
  "java/src/org/chromium/chrome/browser/directactions/CloseTabDirectActionHandler.java",
  "java/src/org/chromium/chrome/browser/directactions/DirectActionCoordinator.java",
  "java/src/org/chromium/chrome/browser/directactions/DirectActionHandler.java",
  "java/src/org/chromium/chrome/browser/directactions/DirectActionInitializer.java",
  "java/src/org/chromium/chrome/browser/directactions/DirectActionReporter.java",
  "java/src/org/chromium/chrome/browser/directactions/DirectActionUsageHistogram.java",
  "java/src/org/chromium/chrome/browser/directactions/FindInPageDirectActionHandler.java",
  "java/src/org/chromium/chrome/browser/directactions/GoBackDirectActionHandler.java",
  "java/src/org/chromium/chrome/browser/directactions/MenuDirectActionHandler.java",
  "java/src/org/chromium/chrome/browser/directactions/SimpleDirectActionHandler.java",
  "java/src/org/chromium/chrome/browser/display_cutout/ActivityDisplayCutoutModeSupplier.java",
  "java/src/org/chromium/chrome/browser/display_cutout/DisplayCutoutTabHelper.java",
  "java/src/org/chromium/chrome/browser/document/ChromeLauncherActivity.java",
  "java/src/org/chromium/chrome/browser/document/DocumentWebContentsDelegate.java",
  "java/src/org/chromium/chrome/browser/dom_distiller/DistilledPagePrefsView.java",
  "java/src/org/chromium/chrome/browser/dom_distiller/DomDistillerServiceFactory.java",
  "java/src/org/chromium/chrome/browser/dom_distiller/DomDistillerTabUtils.java",
  "java/src/org/chromium/chrome/browser/dom_distiller/DomDistillerUIUtils.java",
  "java/src/org/chromium/chrome/browser/dom_distiller/ReaderModeManager.java",
  "java/src/org/chromium/chrome/browser/dom_distiller/ReaderModeToolbarButtonController.java",
  "java/src/org/chromium/chrome/browser/dom_distiller/TabDistillabilityProvider.java",
  "java/src/org/chromium/chrome/browser/download/ChromeDownloadDelegate.java",
  "java/src/org/chromium/chrome/browser/download/DownloadBroadcastManagerImpl.java",
  "java/src/org/chromium/chrome/browser/download/DownloadController.java",
  "java/src/org/chromium/chrome/browser/download/DownloadForegroundServiceImpl.java",
  "java/src/org/chromium/chrome/browser/download/DownloadForegroundServiceManager.java",
  "java/src/org/chromium/chrome/browser/download/DownloadForegroundServiceObservers.java",
  "java/src/org/chromium/chrome/browser/download/DownloadItem.java",
  "java/src/org/chromium/chrome/browser/download/DownloadManagerService.java",
  "java/src/org/chromium/chrome/browser/download/DownloadMessageBridge.java",
  "java/src/org/chromium/chrome/browser/download/DownloadMetrics.java",
  "java/src/org/chromium/chrome/browser/download/DownloadNotificationFactory.java",
  "java/src/org/chromium/chrome/browser/download/DownloadNotificationService.java",
  "java/src/org/chromium/chrome/browser/download/DownloadNotificationServiceObserver.java",
  "java/src/org/chromium/chrome/browser/download/DownloadNotificationUmaHelper.java",
  "java/src/org/chromium/chrome/browser/download/DownloadNotifier.java",
  "java/src/org/chromium/chrome/browser/download/DownloadServiceDelegate.java",
  "java/src/org/chromium/chrome/browser/download/DownloadSharedPreferenceEntry.java",
  "java/src/org/chromium/chrome/browser/download/DownloadSharedPreferenceHelper.java",
  "java/src/org/chromium/chrome/browser/download/DownloadSnackbarController.java",
  "java/src/org/chromium/chrome/browser/download/DownloadUpdate.java",
  "java/src/org/chromium/chrome/browser/download/DownloadUtils.java",
  "java/src/org/chromium/chrome/browser/download/DuplicateDownloadClickableSpan.java",
  "java/src/org/chromium/chrome/browser/download/DuplicateDownloadDialog.java",
  "java/src/org/chromium/chrome/browser/download/DuplicateDownloadDialogBridge.java",
  "java/src/org/chromium/chrome/browser/download/FileAccessPermissionHelper.java",
  "java/src/org/chromium/chrome/browser/download/OMADownloadHandler.java",
  "java/src/org/chromium/chrome/browser/download/OfflineContentAvailabilityStatusProvider.java",
  "java/src/org/chromium/chrome/browser/download/SystemDownloadNotifier.java",
  "java/src/org/chromium/chrome/browser/download/items/OfflineContentAggregatorNotificationBridgeUi.java",
  "java/src/org/chromium/chrome/browser/download/items/OfflineContentAggregatorNotificationBridgeUiFactory.java",
  "java/src/org/chromium/chrome/browser/download/service/DownloadBackgroundTask.java",
  "java/src/org/chromium/chrome/browser/download/service/DownloadTaskScheduler.java",
  "java/src/org/chromium/chrome/browser/explore_sites/CategoryCardAdapter.java",
  "java/src/org/chromium/chrome/browser/explore_sites/CategoryCardViewHolderFactory.java",
  "java/src/org/chromium/chrome/browser/explore_sites/CategoryCardViewHolderFactoryDenseTitleBottom.java",
  "java/src/org/chromium/chrome/browser/explore_sites/CategoryCardViewHolderFactoryDenseTitleRight.java",
  "java/src/org/chromium/chrome/browser/explore_sites/ExploreSitesBackgroundTask.java",
  "java/src/org/chromium/chrome/browser/explore_sites/ExploreSitesBridge.java",
  "java/src/org/chromium/chrome/browser/explore_sites/ExploreSitesCategory.java",
  "java/src/org/chromium/chrome/browser/explore_sites/ExploreSitesCategoryCardView.java",
  "java/src/org/chromium/chrome/browser/explore_sites/ExploreSitesIPH.java",
  "java/src/org/chromium/chrome/browser/explore_sites/ExploreSitesPage.java",
  "java/src/org/chromium/chrome/browser/explore_sites/ExploreSitesSite.java",
  "java/src/org/chromium/chrome/browser/explore_sites/ExploreSitesTileView.java",
  "java/src/org/chromium/chrome/browser/explore_sites/StableScrollLayoutManager.java",
  "java/src/org/chromium/chrome/browser/externalnav/ExternalNavigationDelegateImpl.java",
  "java/src/org/chromium/chrome/browser/externalnav/IntentWithRequestMetadataHandler.java",
  "java/src/org/chromium/chrome/browser/feature_guide/notifications/FeatureNotificationGuideBridge.java",
  "java/src/org/chromium/chrome/browser/feature_guide/notifications/FeatureNotificationGuideServiceFactory.java",
  "java/src/org/chromium/chrome/browser/feature_guide/notifications/FeatureNotificationUtils.java",
  "java/src/org/chromium/chrome/browser/feedback/ChromeFeedbackCollector.java",
  "java/src/org/chromium/chrome/browser/feedback/ConnectivityChecker.java",
  "java/src/org/chromium/chrome/browser/feedback/ConnectivityFeedbackSource.java",
  "java/src/org/chromium/chrome/browser/feedback/ConnectivityTask.java",
  "java/src/org/chromium/chrome/browser/feedback/FeedFeedbackCollector.java",
  "java/src/org/chromium/chrome/browser/feedback/HelpAndFeedbackLauncherImpl.java",
  "java/src/org/chromium/chrome/browser/feedback/ScreenshotTask.java",
  "java/src/org/chromium/chrome/browser/findinpage/FindToolbar.java",
  "java/src/org/chromium/chrome/browser/findinpage/FindToolbarManager.java",
  "java/src/org/chromium/chrome/browser/findinpage/FindToolbarObserver.java",
  "java/src/org/chromium/chrome/browser/findinpage/FindToolbarPhone.java",
  "java/src/org/chromium/chrome/browser/findinpage/FindToolbarTablet.java",
  "java/src/org/chromium/chrome/browser/firstrun/ChildAccountStatusSupplier.java",
  "java/src/org/chromium/chrome/browser/firstrun/DefaultSearchEngineFirstRunFragment.java",
  "java/src/org/chromium/chrome/browser/firstrun/FirstRunActivity.java",
  "java/src/org/chromium/chrome/browser/firstrun/FirstRunActivityBase.java",
  "java/src/org/chromium/chrome/browser/firstrun/FirstRunAppRestrictionInfo.java",
  "java/src/org/chromium/chrome/browser/firstrun/FirstRunChooserView.java",
  "java/src/org/chromium/chrome/browser/firstrun/FirstRunFlowSequencer.java",
  "java/src/org/chromium/chrome/browser/firstrun/FirstRunFragment.java",
  "java/src/org/chromium/chrome/browser/firstrun/FirstRunPage.java",
  "java/src/org/chromium/chrome/browser/firstrun/FirstRunPageDelegate.java",
  "java/src/org/chromium/chrome/browser/firstrun/FirstRunPagerAdapter.java",
  "java/src/org/chromium/chrome/browser/firstrun/FirstRunSignInProcessor.java",
  "java/src/org/chromium/chrome/browser/firstrun/FirstRunUtils.java",
  "java/src/org/chromium/chrome/browser/firstrun/ForcedSigninProcessor.java",
  "java/src/org/chromium/chrome/browser/firstrun/FreIntentCreator.java",
  "java/src/org/chromium/chrome/browser/firstrun/LightweightFirstRunActivity.java",
  "java/src/org/chromium/chrome/browser/firstrun/PolicyLoadListener.java",
  "java/src/org/chromium/chrome/browser/firstrun/SkipTosDialogPolicyListener.java",
  "java/src/org/chromium/chrome/browser/firstrun/SyncConsentFirstRunFragment.java",
  "java/src/org/chromium/chrome/browser/firstrun/TabbedModeFirstRunActivity.java",
  "java/src/org/chromium/chrome/browser/firstrun/ToSAndUMAFirstRunFragment.java",
  "java/src/org/chromium/chrome/browser/firstrun/TosAndUmaFirstRunFragmentWithEnterpriseSupport.java",
  "java/src/org/chromium/chrome/browser/firstrun/TosAndUmaFragmentView.java",
  "java/src/org/chromium/chrome/browser/firstrun/TosDialogBehaviorSharedPrefInvalidator.java",
  "java/src/org/chromium/chrome/browser/flags/BadFlagsSnackbarManager.java",
  "java/src/org/chromium/chrome/browser/fonts/FontPreloader.java",
  "java/src/org/chromium/chrome/browser/fullscreen/BrowserControlsManager.java",
  "java/src/org/chromium/chrome/browser/fullscreen/BrowserControlsManagerSupplier.java",
  "java/src/org/chromium/chrome/browser/fullscreen/FullscreenHtmlApiHandler.java",
  "java/src/org/chromium/chrome/browser/gcore/ChromeGoogleApiClient.java",
  "java/src/org/chromium/chrome/browser/gcore/ChromeGoogleApiClientImpl.java",
  "java/src/org/chromium/chrome/browser/gcore/ConnectedTask.java",
  "java/src/org/chromium/chrome/browser/gcore/GoogleApiClientHelper.java",
  "java/src/org/chromium/chrome/browser/gcore/LifecycleHook.java",
  "java/src/org/chromium/chrome/browser/gesturenav/AndroidUiNavigationGlow.java",
  "java/src/org/chromium/chrome/browser/gesturenav/BackActionDelegate.java",
  "java/src/org/chromium/chrome/browser/gesturenav/GestureNavMetrics.java",
  "java/src/org/chromium/chrome/browser/gesturenav/GestureNavigationProperties.java",
  "java/src/org/chromium/chrome/browser/gesturenav/GestureNavigationViewBinder.java",
  "java/src/org/chromium/chrome/browser/gesturenav/HistoryNavigationCoordinator.java",
  "java/src/org/chromium/chrome/browser/gesturenav/HistoryNavigationLayout.java",
  "java/src/org/chromium/chrome/browser/gesturenav/NavigationBubble.java",
  "java/src/org/chromium/chrome/browser/gesturenav/NavigationGlow.java",
  "java/src/org/chromium/chrome/browser/gesturenav/NavigationHandler.java",
  "java/src/org/chromium/chrome/browser/gesturenav/NavigationSheet.java",
  "java/src/org/chromium/chrome/browser/gesturenav/NavigationSheetCoordinator.java",
  "java/src/org/chromium/chrome/browser/gesturenav/NavigationSheetMediator.java",
  "java/src/org/chromium/chrome/browser/gesturenav/NavigationSheetView.java",
  "java/src/org/chromium/chrome/browser/gesturenav/OverscrollGlowOverlay.java",
  "java/src/org/chromium/chrome/browser/gesturenav/OverscrollSceneLayer.java",
  "java/src/org/chromium/chrome/browser/gesturenav/SideSlideLayout.java",
  "java/src/org/chromium/chrome/browser/gesturenav/TabbedSheetDelegate.java",
  "java/src/org/chromium/chrome/browser/history/BrowsingHistoryBridge.java",
  "java/src/org/chromium/chrome/browser/history/HistoryActivity.java",
  "java/src/org/chromium/chrome/browser/history/HistoryAdapter.java",
  "java/src/org/chromium/chrome/browser/history/HistoryContentManager.java",
  "java/src/org/chromium/chrome/browser/history/HistoryDeletionBridge.java",
  "java/src/org/chromium/chrome/browser/history/HistoryDeletionInfo.java",
  "java/src/org/chromium/chrome/browser/history/HistoryItem.java",
  "java/src/org/chromium/chrome/browser/history/HistoryItemView.java",
  "java/src/org/chromium/chrome/browser/history/HistoryManager.java",
  "java/src/org/chromium/chrome/browser/history/HistoryManagerToolbar.java",
  "java/src/org/chromium/chrome/browser/history/HistoryManagerUtils.java",
  "java/src/org/chromium/chrome/browser/history/HistoryPage.java",
  "java/src/org/chromium/chrome/browser/history/HistoryProvider.java",
  "java/src/org/chromium/chrome/browser/historyreport/AppIndexingReporter.java",
  "java/src/org/chromium/chrome/browser/historyreport/DeltaFileEntry.java",
  "java/src/org/chromium/chrome/browser/historyreport/HistoryReportJniBridge.java",
  "java/src/org/chromium/chrome/browser/historyreport/SearchJniBridge.java",
  "java/src/org/chromium/chrome/browser/historyreport/UsageReport.java",
  "java/src/org/chromium/chrome/browser/homepage/HomepageManager.java",
  "java/src/org/chromium/chrome/browser/homepage/HomepagePolicyManager.java",
  "java/src/org/chromium/chrome/browser/homepage/settings/HomepageMetricsEnums.java",
  "java/src/org/chromium/chrome/browser/homepage/settings/HomepageSettings.java",
  "java/src/org/chromium/chrome/browser/homepage/settings/RadioButtonGroupHomepagePreference.java",
  "java/src/org/chromium/chrome/browser/identity_disc/IdentityDiscController.java",
  "java/src/org/chromium/chrome/browser/incognito/IncognitoNotificationManager.java",
  "java/src/org/chromium/chrome/browser/incognito/IncognitoNotificationPresenceController.java",
  "java/src/org/chromium/chrome/browser/incognito/IncognitoNotificationServiceImpl.java",
  "java/src/org/chromium/chrome/browser/incognito/IncognitoProfileDestroyer.java",
  "java/src/org/chromium/chrome/browser/incognito/IncognitoSnapshotController.java",
  "java/src/org/chromium/chrome/browser/incognito/IncognitoTabLauncher.java",
  "java/src/org/chromium/chrome/browser/incognito/IncognitoTabbedSnapshotController.java",
  "java/src/org/chromium/chrome/browser/infobar/AutofillCreditCardFillingInfoBar.java",
  "java/src/org/chromium/chrome/browser/infobar/AutofillOfferNotificationInfoBar.java",
  "java/src/org/chromium/chrome/browser/infobar/AutofillSaveCardInfoBar.java",
  "java/src/org/chromium/chrome/browser/infobar/AutofillVirtualCardEnrollmentInfoBar.java",
  "java/src/org/chromium/chrome/browser/infobar/CardDetail.java",
  "java/src/org/chromium/chrome/browser/infobar/DuplicateDownloadInfoBar.java",
  "java/src/org/chromium/chrome/browser/infobar/FramebustBlockInfoBar.java",
  "java/src/org/chromium/chrome/browser/infobar/GeneratedPasswordSavedInfoBar.java",
  "java/src/org/chromium/chrome/browser/infobar/GeneratedPasswordSavedInfoBarDelegate.java",
  "java/src/org/chromium/chrome/browser/infobar/IPHBubbleDelegateImpl.java",
  "java/src/org/chromium/chrome/browser/infobar/IPHInfoBarSupport.java",
  "java/src/org/chromium/chrome/browser/infobar/InfoBarContainer.java",
  "java/src/org/chromium/chrome/browser/infobar/InfoBarContainerView.java",
  "java/src/org/chromium/chrome/browser/infobar/InstantAppsInfoBar.java",
  "java/src/org/chromium/chrome/browser/infobar/InstantAppsInfoBarDelegate.java",
  "java/src/org/chromium/chrome/browser/infobar/KnownInterceptionDisclosureInfoBar.java",
  "java/src/org/chromium/chrome/browser/infobar/NearOomInfoBar.java",
  "java/src/org/chromium/chrome/browser/infobar/NearOomReductionInfoBar.java",
  "java/src/org/chromium/chrome/browser/infobar/PasswordInfoBarUtils.java",
  "java/src/org/chromium/chrome/browser/infobar/PermissionInfoBar.java",
  "java/src/org/chromium/chrome/browser/infobar/ReaderModeInfoBar.java",
  "java/src/org/chromium/chrome/browser/infobar/SafetyTipInfoBar.java",
  "java/src/org/chromium/chrome/browser/infobar/SavePasswordInfoBar.java",
  "java/src/org/chromium/chrome/browser/infobar/SubPanelListener.java",
  "java/src/org/chromium/chrome/browser/infobar/SurveyInfoBar.java",
  "java/src/org/chromium/chrome/browser/infobar/SurveyInfoBarDelegate.java",
  "java/src/org/chromium/chrome/browser/infobar/TextViewEllipsizerSafe.java",
  "java/src/org/chromium/chrome/browser/infobar/TranslateCompactInfoBar.java",
  "java/src/org/chromium/chrome/browser/infobar/UpdatePasswordInfoBar.java",
  "java/src/org/chromium/chrome/browser/init/ActivityLifecycleDispatcherImpl.java",
  "java/src/org/chromium/chrome/browser/init/AsyncInitTaskRunner.java",
  "java/src/org/chromium/chrome/browser/init/AsyncInitializationActivity.java",
  "java/src/org/chromium/chrome/browser/init/BrowserParts.java",
  "java/src/org/chromium/chrome/browser/init/ChromeActivityNativeDelegate.java",
  "java/src/org/chromium/chrome/browser/init/ChromeBrowserInitializer.java",
  "java/src/org/chromium/chrome/browser/init/ChromeLifetimeController.java",
  "java/src/org/chromium/chrome/browser/init/ChromeStartupDelegate.java",
  "java/src/org/chromium/chrome/browser/init/EmptyBrowserParts.java",
  "java/src/org/chromium/chrome/browser/init/MinimalBrowserStartupUtils.java",
  "java/src/org/chromium/chrome/browser/init/NativeInitializationController.java",
  "java/src/org/chromium/chrome/browser/init/NativeStartupBridge.java",
  "java/src/org/chromium/chrome/browser/init/ProcessInitializationHandler.java",
  "java/src/org/chromium/chrome/browser/init/SingleWindowKeyboardVisibilityDelegate.java",
  "java/src/org/chromium/chrome/browser/installedapp/InstalledAppProviderFactory.java",
  "java/src/org/chromium/chrome/browser/instantapps/AuthenticatedProxyActivity.java",
  "java/src/org/chromium/chrome/browser/instantapps/InstantAppsBannerData.java",
  "java/src/org/chromium/chrome/browser/instantapps/InstantAppsHandler.java",
  "java/src/org/chromium/chrome/browser/instantapps/InstantAppsMessageDelegate.java",
  "java/src/org/chromium/chrome/browser/instantapps/InstantAppsSettings.java",
  "java/src/org/chromium/chrome/browser/invalidation/ResumableDelayedTaskRunner.java",
  "java/src/org/chromium/chrome/browser/invalidation/SessionsInvalidationManager.java",
  "java/src/org/chromium/chrome/browser/javascript/WebContextFetcher.java",
  "java/src/org/chromium/chrome/browser/lens/LensDebugBridge.java",
  "java/src/org/chromium/chrome/browser/lens/LensPolicyUtils.java",
  "java/src/org/chromium/chrome/browser/login/ChromeHttpAuthHandler.java",
  "java/src/org/chromium/chrome/browser/media/FullscreenVideoPictureInPictureController.java",
  "java/src/org/chromium/chrome/browser/media/MediaCaptureDevicesDispatcherAndroid.java",
  "java/src/org/chromium/chrome/browser/media/MediaCaptureNotificationServiceImpl.java",
  "java/src/org/chromium/chrome/browser/media/MediaLauncherActivity.java",
  "java/src/org/chromium/chrome/browser/media/MediaViewerUtils.java",
  "java/src/org/chromium/chrome/browser/media/PictureInPicture.java",
  "java/src/org/chromium/chrome/browser/media/PictureInPictureActivity.java",
  "java/src/org/chromium/chrome/browser/media/router/ChromeMediaRouterClient.java",
  "java/src/org/chromium/chrome/browser/media/ui/ChromeMediaNotificationControllerDelegate.java",
  "java/src/org/chromium/chrome/browser/media/ui/ChromeMediaNotificationManager.java",
  "java/src/org/chromium/chrome/browser/media/ui/MediaSessionTabHelper.java",
  "java/src/org/chromium/chrome/browser/messages/ChromeMessageAutodismissDurationProvider.java",
  "java/src/org/chromium/chrome/browser/messages/ChromeMessageQueueMediator.java",
  "java/src/org/chromium/chrome/browser/messages/MessageContainerCoordinator.java",
  "java/src/org/chromium/chrome/browser/messages/MessageContainerObserver.java",
  "java/src/org/chromium/chrome/browser/metrics/ActivityTabStartupMetricsTracker.java",
  "java/src/org/chromium/chrome/browser/metrics/AndroidSessionDurationsServiceState.java",
  "java/src/org/chromium/chrome/browser/metrics/LaunchMetrics.java",
  "java/src/org/chromium/chrome/browser/metrics/MainIntentBehaviorMetrics.java",
  "java/src/org/chromium/chrome/browser/metrics/PackageMetrics.java",
  "java/src/org/chromium/chrome/browser/metrics/PageLoadMetrics.java",
  "java/src/org/chromium/chrome/browser/metrics/UmaSessionStats.java",
  "java/src/org/chromium/chrome/browser/metrics/VariationsSession.java",
  "java/src/org/chromium/chrome/browser/metrics/WebApkSplashscreenMetrics.java",
  "java/src/org/chromium/chrome/browser/metrics/WebApkUninstallUmaTracker.java",
  "java/src/org/chromium/chrome/browser/modaldialog/ChromeTabModalPresenter.java",
  "java/src/org/chromium/chrome/browser/modaldialog/TabModalLifetimeHandler.java",
  "java/src/org/chromium/chrome/browser/modules/ModuleInstallUi.java",
  "java/src/org/chromium/chrome/browser/mojo/ChromeInterfaceRegistrar.java",
  "java/src/org/chromium/chrome/browser/multiwindow/MultiInstanceChromeTabbedActivity.java",
  "java/src/org/chromium/chrome/browser/multiwindow/MultiInstanceManager.java",
  "java/src/org/chromium/chrome/browser/multiwindow/MultiInstanceManagerApi31.java",
  "java/src/org/chromium/chrome/browser/multiwindow/MultiWindowModeStateDispatcher.java",
  "java/src/org/chromium/chrome/browser/multiwindow/MultiWindowModeStateDispatcherImpl.java",
  "java/src/org/chromium/chrome/browser/multiwindow/MultiWindowUtils.java",
  "java/src/org/chromium/chrome/browser/native_page/ContextMenuManager.java",
  "java/src/org/chromium/chrome/browser/native_page/NativePageAssassin.java",
  "java/src/org/chromium/chrome/browser/native_page/NativePageFactory.java",
  "java/src/org/chromium/chrome/browser/native_page/NativePageNavigationDelegate.java",
  "java/src/org/chromium/chrome/browser/native_page/NativePageNavigationDelegateImpl.java",
  "java/src/org/chromium/chrome/browser/navigation_predictor/NavigationPredictorBridge.java",
  "java/src/org/chromium/chrome/browser/net/connectivitydetector/ConnectivityDetector.java",
  "java/src/org/chromium/chrome/browser/net/nqe/NetworkQualityObserver.java",
  "java/src/org/chromium/chrome/browser/net/nqe/NetworkQualityProvider.java",
  "java/src/org/chromium/chrome/browser/notifications/ActionInfo.java",
  "java/src/org/chromium/chrome/browser/notifications/NotificationJobServiceImpl.java",
  "java/src/org/chromium/chrome/browser/notifications/NotificationPlatformBridge.java",
  "java/src/org/chromium/chrome/browser/notifications/NotificationServiceImpl.java",
  "java/src/org/chromium/chrome/browser/notifications/NotificationTriggerBackgroundTask.java",
  "java/src/org/chromium/chrome/browser/notifications/NotificationTriggerScheduler.java",
  "java/src/org/chromium/chrome/browser/notifications/WebPlatformNotificationMetrics.java",
  "java/src/org/chromium/chrome/browser/notifications/scheduler/DisplayAgent.java",
  "java/src/org/chromium/chrome/browser/notifications/scheduler/NotificationSchedulerTask.java",
  "java/src/org/chromium/chrome/browser/ntp/FeedPositionUtils.java",
  "java/src/org/chromium/chrome/browser/ntp/ForeignSessionHelper.java",
  "java/src/org/chromium/chrome/browser/ntp/IncognitoCookieControlsManager.java",
  "java/src/org/chromium/chrome/browser/ntp/IncognitoDescriptionView.java",
  "java/src/org/chromium/chrome/browser/ntp/IncognitoNewTabPage.java",
  "java/src/org/chromium/chrome/browser/ntp/IncognitoNewTabPageView.java",
  "java/src/org/chromium/chrome/browser/ntp/LegacyIncognitoDescriptionView.java",
  "java/src/org/chromium/chrome/browser/ntp/NativePageRootFrameLayout.java",
  "java/src/org/chromium/chrome/browser/ntp/NewTabPage.java",
  "java/src/org/chromium/chrome/browser/ntp/NewTabPageLaunchOrigin.java",
  "java/src/org/chromium/chrome/browser/ntp/NewTabPageLayout.java",
  "java/src/org/chromium/chrome/browser/ntp/NewTabPageManager.java",
  "java/src/org/chromium/chrome/browser/ntp/NewTabPageScrollView.java",
  "java/src/org/chromium/chrome/browser/ntp/NewTabPageUma.java",
  "java/src/org/chromium/chrome/browser/ntp/NewTabPageUtils.java",
  "java/src/org/chromium/chrome/browser/ntp/RecentTabCountDrawable.java",
  "java/src/org/chromium/chrome/browser/ntp/RecentTabsExpandableListView.java",
  "java/src/org/chromium/chrome/browser/ntp/RecentTabsGroupView.java",
  "java/src/org/chromium/chrome/browser/ntp/RecentTabsManager.java",
  "java/src/org/chromium/chrome/browser/ntp/RecentTabsPage.java",
  "java/src/org/chromium/chrome/browser/ntp/RecentTabsPagePrefs.java",
  "java/src/org/chromium/chrome/browser/ntp/RecentTabsRowAdapter.java",
  "java/src/org/chromium/chrome/browser/ntp/RecentlyClosedBridge.java",
  "java/src/org/chromium/chrome/browser/ntp/RecentlyClosedBulkEvent.java",
  "java/src/org/chromium/chrome/browser/ntp/RecentlyClosedEntry.java",
  "java/src/org/chromium/chrome/browser/ntp/RecentlyClosedGroup.java",
  "java/src/org/chromium/chrome/browser/ntp/RecentlyClosedTab.java",
  "java/src/org/chromium/chrome/browser/ntp/RecentlyClosedTabManager.java",
  "java/src/org/chromium/chrome/browser/ntp/RevampedIncognitoDescriptionView.java",
  "java/src/org/chromium/chrome/browser/ntp/SnapScrollHelperImpl.java",
  "java/src/org/chromium/chrome/browser/ntp/TitleUtil.java",
  "java/src/org/chromium/chrome/browser/ntp/cards/SignInPromo.java",
  "java/src/org/chromium/chrome/browser/ntp/search/SearchBoxContainerView.java",
  "java/src/org/chromium/chrome/browser/ntp/search/SearchBoxCoordinator.java",
  "java/src/org/chromium/chrome/browser/ntp/search/SearchBoxMediator.java",
  "java/src/org/chromium/chrome/browser/ntp/search/SearchBoxProperties.java",
  "java/src/org/chromium/chrome/browser/ntp/search/SearchBoxViewBinder.java",
  "java/src/org/chromium/chrome/browser/offlinepages/AutoFetchNotifier.java",
  "java/src/org/chromium/chrome/browser/offlinepages/BackgroundScheduler.java",
  "java/src/org/chromium/chrome/browser/offlinepages/BackgroundSchedulerBridge.java",
  "java/src/org/chromium/chrome/browser/offlinepages/BackgroundSchedulerProcessor.java",
  "java/src/org/chromium/chrome/browser/offlinepages/ClientId.java",
  "java/src/org/chromium/chrome/browser/offlinepages/DeletedPageInfo.java",
  "java/src/org/chromium/chrome/browser/offlinepages/GetPagesByNamespaceForLivePageSharingCallback.java",
  "java/src/org/chromium/chrome/browser/offlinepages/OfflineBackgroundTask.java",
  "java/src/org/chromium/chrome/browser/offlinepages/OfflinePageArchivePublisherBridge.java",
  "java/src/org/chromium/chrome/browser/offlinepages/OfflinePageBridge.java",
  "java/src/org/chromium/chrome/browser/offlinepages/OfflinePageItem.java",
  "java/src/org/chromium/chrome/browser/offlinepages/OfflinePageOrigin.java",
  "java/src/org/chromium/chrome/browser/offlinepages/OfflinePageTabData.java",
  "java/src/org/chromium/chrome/browser/offlinepages/OfflinePageTabObserver.java",
  "java/src/org/chromium/chrome/browser/offlinepages/OfflinePageUtils.java",
  "java/src/org/chromium/chrome/browser/offlinepages/PublishPageCallback.java",
  "java/src/org/chromium/chrome/browser/offlinepages/RequestCoordinatorBridge.java",
  "java/src/org/chromium/chrome/browser/offlinepages/SavePageAndShareCallback.java",
  "java/src/org/chromium/chrome/browser/offlinepages/SavePageRequest.java",
  "java/src/org/chromium/chrome/browser/offlinepages/TaskExtrasPacker.java",
  "java/src/org/chromium/chrome/browser/offlinepages/TriggerConditions.java",
  "java/src/org/chromium/chrome/browser/offlinepages/downloads/OfflinePageDownloadBridge.java",
  "java/src/org/chromium/chrome/browser/offlinepages/indicator/OfflineDetector.java",
  "java/src/org/chromium/chrome/browser/offlinepages/indicator/OfflineIndicatorController.java",
  "java/src/org/chromium/chrome/browser/offlinepages/indicator/OfflineIndicatorControllerV2.java",
  "java/src/org/chromium/chrome/browser/offlinepages/indicator/OfflineIndicatorInProductHelpController.java",
  "java/src/org/chromium/chrome/browser/offlinepages/indicator/OfflineIndicatorMetricsDelegate.java",
  "java/src/org/chromium/chrome/browser/offlinepages/indicator/TopSnackbarManager.java",
  "java/src/org/chromium/chrome/browser/offlinepages/indicator/TopSnackbarView.java",
  "java/src/org/chromium/chrome/browser/offlinepages/measurements/OfflineMeasurementsBackgroundTask.java",
  "java/src/org/chromium/chrome/browser/offlinepages/prefetch/PrefetchBackgroundTask.java",
  "java/src/org/chromium/chrome/browser/offlinepages/prefetch/PrefetchBackgroundTaskScheduler.java",
  "java/src/org/chromium/chrome/browser/omaha/MarketURLGetter.java",
  "java/src/org/chromium/chrome/browser/omaha/OmahaBase.java",
  "java/src/org/chromium/chrome/browser/omaha/OmahaClientImpl.java",
  "java/src/org/chromium/chrome/browser/omaha/OmahaDelegate.java",
  "java/src/org/chromium/chrome/browser/omaha/OmahaDelegateBase.java",
  "java/src/org/chromium/chrome/browser/omaha/OmahaService.java",
  "java/src/org/chromium/chrome/browser/omaha/RequestData.java",
  "java/src/org/chromium/chrome/browser/omaha/RequestGenerator.java",
  "java/src/org/chromium/chrome/browser/omaha/ResponseParser.java",
  "java/src/org/chromium/chrome/browser/omaha/UpdateConfigs.java",
  "java/src/org/chromium/chrome/browser/omaha/UpdateMenuItemHelper.java",
  "java/src/org/chromium/chrome/browser/omaha/UpdateStatusProvider.java",
  "java/src/org/chromium/chrome/browser/omaha/VersionNumberGetter.java",
  "java/src/org/chromium/chrome/browser/omaha/metrics/HistogramUtils.java",
  "java/src/org/chromium/chrome/browser/omaha/metrics/TrackingProvider.java",
  "java/src/org/chromium/chrome/browser/omaha/metrics/UpdateSuccessMetrics.java",
  "java/src/org/chromium/chrome/browser/page_info/AboutThisSiteView.java",
  "java/src/org/chromium/chrome/browser/page_info/ChromePageInfo.java",
  "java/src/org/chromium/chrome/browser/page_info/ChromePageInfoControllerDelegate.java",
  "java/src/org/chromium/chrome/browser/page_info/PageInfoAboutThisSiteController.java",
  "java/src/org/chromium/chrome/browser/page_info/PageInfoHistoryController.java",
  "java/src/org/chromium/chrome/browser/page_info/SiteSettingsHelper.java",
  "java/src/org/chromium/chrome/browser/paint_preview/StartupPaintPreviewHelper.java",
  "java/src/org/chromium/chrome/browser/paint_preview/StartupPaintPreviewHelperSupplier.java",
  "java/src/org/chromium/chrome/browser/partnerbookmarks/PartnerBookmark.java",
  "java/src/org/chromium/chrome/browser/partnerbookmarks/PartnerBookmarksFaviconThrottle.java",
  "java/src/org/chromium/chrome/browser/partnerbookmarks/PartnerBookmarksProviderIterator.java",
  "java/src/org/chromium/chrome/browser/partnerbookmarks/PartnerBookmarksReader.java",
  "java/src/org/chromium/chrome/browser/partnerbookmarks/PartnerBookmarksShim.java",
  "java/src/org/chromium/chrome/browser/password_manager/AccountChooserDialog.java",
  "java/src/org/chromium/chrome/browser/password_manager/AutoSigninFirstRunDialog.java",
  "java/src/org/chromium/chrome/browser/password_manager/AutoSigninSnackbarController.java",
  "java/src/org/chromium/chrome/browser/password_manager/Credential.java",
  "java/src/org/chromium/chrome/browser/password_manager/CredentialLeakDialogBridge.java",
  "java/src/org/chromium/chrome/browser/password_manager/GooglePasswordManagerUIProvider.java",
  "java/src/org/chromium/chrome/browser/password_manager/PasswordChangeLauncher.java",
  "java/src/org/chromium/chrome/browser/password_manager/PasswordCheckupLauncher.java",
  "java/src/org/chromium/chrome/browser/password_manager/PasswordGenerationDialogBridge.java",
  "java/src/org/chromium/chrome/browser/password_manager/PasswordGenerationDialogCoordinator.java",
  "java/src/org/chromium/chrome/browser/password_manager/PasswordGenerationDialogCustomView.java",
  "java/src/org/chromium/chrome/browser/password_manager/PasswordGenerationDialogMediator.java",
  "java/src/org/chromium/chrome/browser/password_manager/PasswordGenerationDialogModel.java",
  "java/src/org/chromium/chrome/browser/password_manager/PasswordGenerationDialogViewBinder.java",
  "java/src/org/chromium/chrome/browser/password_manager/PasswordGenerationPopupAdapter.java",
  "java/src/org/chromium/chrome/browser/password_manager/PasswordGenerationPopupBridge.java",
  "java/src/org/chromium/chrome/browser/password_manager/PasswordManagerDialogContents.java",
  "java/src/org/chromium/chrome/browser/password_manager/PasswordManagerDialogCoordinator.java",
  "java/src/org/chromium/chrome/browser/password_manager/PasswordManagerDialogMediator.java",
  "java/src/org/chromium/chrome/browser/password_manager/PasswordManagerDialogProperties.java",
  "java/src/org/chromium/chrome/browser/password_manager/PasswordManagerDialogView.java",
  "java/src/org/chromium/chrome/browser/password_manager/PasswordManagerDialogViewBinder.java",
  "java/src/org/chromium/chrome/browser/password_manager/PasswordManagerLauncher.java",
  "java/src/org/chromium/chrome/browser/password_manager/settings/CallbackDelayer.java",
  "java/src/org/chromium/chrome/browser/password_manager/settings/DialogManager.java",
  "java/src/org/chromium/chrome/browser/password_manager/settings/ExportErrorDialogFragment.java",
  "java/src/org/chromium/chrome/browser/password_manager/settings/ExportFlow.java",
  "java/src/org/chromium/chrome/browser/password_manager/settings/ExportWarningDialogFragment.java",
  "java/src/org/chromium/chrome/browser/password_manager/settings/ManualCallbackDelayer.java",
  "java/src/org/chromium/chrome/browser/password_manager/settings/PasswordManagerHandler.java",
  "java/src/org/chromium/chrome/browser/password_manager/settings/PasswordManagerHandlerProvider.java",
  "java/src/org/chromium/chrome/browser/password_manager/settings/PasswordSettings.java",
  "java/src/org/chromium/chrome/browser/password_manager/settings/PasswordUIView.java",
  "java/src/org/chromium/chrome/browser/password_manager/settings/ProgressBarDialogFragment.java",
  "java/src/org/chromium/chrome/browser/password_manager/settings/SavedPasswordEntry.java",
  "java/src/org/chromium/chrome/browser/password_manager/settings/SingleThreadBarrierClosure.java",
  "java/src/org/chromium/chrome/browser/password_manager/settings/TimedCallbackDelayer.java",
  "java/src/org/chromium/chrome/browser/payments/AddressEditor.java",
  "java/src/org/chromium/chrome/browser/payments/AutofillAddress.java",
  "java/src/org/chromium/chrome/browser/payments/AutofillContact.java",
  "java/src/org/chromium/chrome/browser/payments/AutofillPaymentInstrument.java",
  "java/src/org/chromium/chrome/browser/payments/ChromePaymentRequestFactory.java",
  "java/src/org/chromium/chrome/browser/payments/ChromePaymentRequestService.java",
  "java/src/org/chromium/chrome/browser/payments/ChromePaymentResponseHelper.java",
  "java/src/org/chromium/chrome/browser/payments/ContactEditor.java",
  "java/src/org/chromium/chrome/browser/payments/PaymentPreferencesUtil.java",
  "java/src/org/chromium/chrome/browser/payments/ServiceWorkerPaymentAppBridge.java",
  "java/src/org/chromium/chrome/browser/payments/SettingsAutofillAndPaymentsObserver.java",
  "java/src/org/chromium/chrome/browser/payments/ShippingStrings.java",
  "java/src/org/chromium/chrome/browser/payments/handler/PaymentHandlerActionModeCallback.java",
  "java/src/org/chromium/chrome/browser/payments/handler/PaymentHandlerCoordinator.java",
  "java/src/org/chromium/chrome/browser/payments/handler/PaymentHandlerMediator.java",
  "java/src/org/chromium/chrome/browser/payments/handler/PaymentHandlerProperties.java",
  "java/src/org/chromium/chrome/browser/payments/handler/PaymentHandlerView.java",
  "java/src/org/chromium/chrome/browser/payments/handler/PaymentHandlerViewBinder.java",
  "java/src/org/chromium/chrome/browser/payments/handler/toolbar/PaymentHandlerToolbarCoordinator.java",
  "java/src/org/chromium/chrome/browser/payments/handler/toolbar/PaymentHandlerToolbarMediator.java",
  "java/src/org/chromium/chrome/browser/payments/handler/toolbar/PaymentHandlerToolbarProperties.java",
  "java/src/org/chromium/chrome/browser/payments/handler/toolbar/PaymentHandlerToolbarView.java",
  "java/src/org/chromium/chrome/browser/payments/handler/toolbar/PaymentHandlerToolbarViewBinder.java",
  "java/src/org/chromium/chrome/browser/payments/ui/AnimatorProperties.java",
  "java/src/org/chromium/chrome/browser/payments/ui/ContactDetailsSection.java",
  "java/src/org/chromium/chrome/browser/payments/ui/DimmingDialog.java",
  "java/src/org/chromium/chrome/browser/payments/ui/LineItem.java",
  "java/src/org/chromium/chrome/browser/payments/ui/PaymentAppComparator.java",
  "java/src/org/chromium/chrome/browser/payments/ui/PaymentInformation.java",
  "java/src/org/chromium/chrome/browser/payments/ui/PaymentRequestBottomBar.java",
  "java/src/org/chromium/chrome/browser/payments/ui/PaymentRequestHeader.java",
  "java/src/org/chromium/chrome/browser/payments/ui/PaymentRequestSection.java",
  "java/src/org/chromium/chrome/browser/payments/ui/PaymentRequestUI.java",
  "java/src/org/chromium/chrome/browser/payments/ui/PaymentUiService.java",
  "java/src/org/chromium/chrome/browser/payments/ui/SectionInformation.java",
  "java/src/org/chromium/chrome/browser/payments/ui/SectionUiUtils.java",
  "java/src/org/chromium/chrome/browser/payments/ui/ShoppingCart.java",
  "java/src/org/chromium/chrome/browser/permissions/NotificationBlockedDialog.java",
  "java/src/org/chromium/chrome/browser/permissions/PermissionSettingsBridge.java",
  "java/src/org/chromium/chrome/browser/permissions/PermissionUpdateRequester.java",
  "java/src/org/chromium/chrome/browser/photo_picker/DecoderServiceImpl.java",
  "java/src/org/chromium/chrome/browser/policy/PolicyAuditor.java",
  "java/src/org/chromium/chrome/browser/policy/PolicyAuditorBridge.java",
  "java/src/org/chromium/chrome/browser/prerender/ChromePrerenderServiceImpl.java",
  "java/src/org/chromium/chrome/browser/printing/TabPrinter.java",
  "java/src/org/chromium/chrome/browser/privacy/settings/DoNotTrackSettings.java",
  "java/src/org/chromium/chrome/browser/privacy/settings/IncognitoLockSettings.java",
  "java/src/org/chromium/chrome/browser/privacy/settings/PrivacyPreferencesManagerImpl.java",
  "java/src/org/chromium/chrome/browser/privacy/settings/PrivacySettings.java",
  "java/src/org/chromium/chrome/browser/provider/BaseColumns.java",
  "java/src/org/chromium/chrome/browser/provider/BookmarkColumns.java",
  "java/src/org/chromium/chrome/browser/provider/ChromeBrowserProviderImpl.java",
  "java/src/org/chromium/chrome/browser/provider/ChromeBrowserProviderSuggestionsCursor.java",
  "java/src/org/chromium/chrome/browser/provider/SearchColumns.java",
  "java/src/org/chromium/chrome/browser/push_messaging/PushMessagingServiceObserver.java",
  "java/src/org/chromium/chrome/browser/query_tiles/QueryTileSection.java",
  "java/src/org/chromium/chrome/browser/query_tiles/QueryTileUtils.java",
  "java/src/org/chromium/chrome/browser/query_tiles/TileProviderFactory.java",
  "java/src/org/chromium/chrome/browser/query_tiles/TileServiceUtils.java",
  "java/src/org/chromium/chrome/browser/quickactionsearchwidget/QuickActionSearchWidgetProvider.java",
  "java/src/org/chromium/chrome/browser/read_later/ReadLaterIPHController.java",
  "java/src/org/chromium/chrome/browser/read_later/ReadingListBackPressHandler.java",
  "java/src/org/chromium/chrome/browser/read_later/ReadingListBridge.java",
  "java/src/org/chromium/chrome/browser/read_later/ReadingListUtils.java",
  "java/src/org/chromium/chrome/browser/reengagement/ReengagementNotificationController.java",
  "java/src/org/chromium/chrome/browser/renderer_host/ChromeNavigationUIData.java",
  "java/src/org/chromium/chrome/browser/resources/ResourceMapper.java",
  "java/src/org/chromium/chrome/browser/rlz/RevenueStats.java",
  "java/src/org/chromium/chrome/browser/rlz/RlzPingHandler.java",
  "java/src/org/chromium/chrome/browser/safe_browsing/SafeBrowsingPasswordReuseDialogBridge.java",
  "java/src/org/chromium/chrome/browser/safe_browsing/SafeBrowsingReferringAppBridge.java",
  "java/src/org/chromium/chrome/browser/safe_browsing/SafeBrowsingSettingsLauncher.java",
  "java/src/org/chromium/chrome/browser/safety_check/SafetyCheckUpdatesDelegateImpl.java",
  "java/src/org/chromium/chrome/browser/searchwidget/SearchActivity.java",
  "java/src/org/chromium/chrome/browser/searchwidget/SearchActivityLocationBarLayout.java",
  "java/src/org/chromium/chrome/browser/searchwidget/SearchBoxDataProvider.java",
  "java/src/org/chromium/chrome/browser/searchwidget/SearchType.java",
  "java/src/org/chromium/chrome/browser/searchwidget/SearchWidgetProvider.java",
  "java/src/org/chromium/chrome/browser/segmentation_platform/ContextualPageActionController.java",
  "java/src/org/chromium/chrome/browser/segmentation_platform/PriceTrackingActionProvider.java",
  "java/src/org/chromium/chrome/browser/segmentation_platform/ReaderModeActionProvider.java",
  "java/src/org/chromium/chrome/browser/segmentation_platform/SignalAccumulator.java",
  "java/src/org/chromium/chrome/browser/services/gcm/ChromeGcmListenerServiceImpl.java",
  "java/src/org/chromium/chrome/browser/services/gcm/GCMBackgroundServiceImpl.java",
  "java/src/org/chromium/chrome/browser/services/gcm/GCMBackgroundTask.java",
  "java/src/org/chromium/chrome/browser/services/gcm/GcmUma.java",
  "java/src/org/chromium/chrome/browser/settings/MainSettings.java",
  "java/src/org/chromium/chrome/browser/settings/SettingsActivity.java",
  "java/src/org/chromium/chrome/browser/settings/SettingsLauncherImpl.java",
  "java/src/org/chromium/chrome/browser/share/LensUtils.java",
  "java/src/org/chromium/chrome/browser/share/ShareButtonController.java",
  "java/src/org/chromium/chrome/browser/share/ShareDelegateImpl.java",
  "java/src/org/chromium/chrome/browser/share/ShareDelegateSupplier.java",
  "java/src/org/chromium/chrome/browser/share/ShareHelper.java",
  "java/src/org/chromium/chrome/browser/share/ShareUtils.java",
  "java/src/org/chromium/chrome/browser/share/crow/CrowButtonDelegateImpl.java",
  "java/src/org/chromium/chrome/browser/sharing/SharingAdapter.java",
  "java/src/org/chromium/chrome/browser/sharing/SharingJNIBridge.java",
  "java/src/org/chromium/chrome/browser/sharing/SharingNotificationUtil.java",
  "java/src/org/chromium/chrome/browser/sharing/SharingServiceProxy.java",
  "java/src/org/chromium/chrome/browser/sharing/click_to_call/ClickToCallMessageHandler.java",
  "java/src/org/chromium/chrome/browser/sharing/click_to_call/ClickToCallUma.java",
  "java/src/org/chromium/chrome/browser/sharing/shared_clipboard/SharedClipboardMessageHandler.java",
  "java/src/org/chromium/chrome/browser/sharing/sms_fetcher/SmsFetcherMessageHandler.java",
  "java/src/org/chromium/chrome/browser/signin/SigninBridge.java",
  "java/src/org/chromium/chrome/browser/signin/SigninCheckerProvider.java",
  "java/src/org/chromium/chrome/browser/signin/SigninFirstRunFragment.java",
  "java/src/org/chromium/chrome/browser/signin/SigninManagerImpl.java",
  "java/src/org/chromium/chrome/browser/signin/SyncConsentActivity.java",
  "java/src/org/chromium/chrome/browser/signin/SyncConsentActivityLauncherImpl.java",
  "java/src/org/chromium/chrome/browser/signin/SyncConsentFragment.java",
  "java/src/org/chromium/chrome/browser/signin/SyncPromoView.java",
  "java/src/org/chromium/chrome/browser/site_settings/ChromeSiteSettingsDelegate.java",
  "java/src/org/chromium/chrome/browser/site_settings/CookieControlsServiceBridge.java",
  "java/src/org/chromium/chrome/browser/site_settings/ManageSpaceActivity.java",
  "java/src/org/chromium/chrome/browser/status_indicator/StatusIndicatorCoordinator.java",
  "java/src/org/chromium/chrome/browser/status_indicator/StatusIndicatorMediator.java",
  "java/src/org/chromium/chrome/browser/status_indicator/StatusIndicatorProperties.java",
  "java/src/org/chromium/chrome/browser/status_indicator/StatusIndicatorSceneLayer.java",
  "java/src/org/chromium/chrome/browser/status_indicator/StatusIndicatorViewBinder.java",
  "java/src/org/chromium/chrome/browser/stylus_handwriting/StylusWritingCoordinator.java",
  "java/src/org/chromium/chrome/browser/suggestions/DestructionObserver.java",
  "java/src/org/chromium/chrome/browser/suggestions/ImageFetcher.java",
  "java/src/org/chromium/chrome/browser/suggestions/OfflinableSuggestion.java",
  "java/src/org/chromium/chrome/browser/suggestions/SiteSuggestion.java",
  "java/src/org/chromium/chrome/browser/suggestions/SuggestionsConfig.java",
  "java/src/org/chromium/chrome/browser/suggestions/SuggestionsDependencyFactory.java",
  "java/src/org/chromium/chrome/browser/suggestions/SuggestionsMetrics.java",
  "java/src/org/chromium/chrome/browser/suggestions/SuggestionsNavigationDelegate.java",
  "java/src/org/chromium/chrome/browser/suggestions/SuggestionsOfflineModelObserver.java",
  "java/src/org/chromium/chrome/browser/suggestions/SuggestionsUiDelegate.java",
  "java/src/org/chromium/chrome/browser/suggestions/SuggestionsUiDelegateImpl.java",
  "java/src/org/chromium/chrome/browser/suggestions/mostvisited/MostVisitedSites.java",
  "java/src/org/chromium/chrome/browser/suggestions/mostvisited/MostVisitedSitesBridge.java",
  "java/src/org/chromium/chrome/browser/suggestions/mostvisited/MostVisitedSitesMetadataUtils.java",
  "java/src/org/chromium/chrome/browser/suggestions/tile/MostVisitedTilesCarouselLayout.java",
  "java/src/org/chromium/chrome/browser/suggestions/tile/MostVisitedTilesCoordinator.java",
  "java/src/org/chromium/chrome/browser/suggestions/tile/MostVisitedTilesGridLayout.java",
  "java/src/org/chromium/chrome/browser/suggestions/tile/MostVisitedTilesMediator.java",
  "java/src/org/chromium/chrome/browser/suggestions/tile/MostVisitedTilesProperties.java",
  "java/src/org/chromium/chrome/browser/suggestions/tile/MostVisitedTilesViewBinder.java",
  "java/src/org/chromium/chrome/browser/suggestions/tile/SuggestionsTileView.java",
  "java/src/org/chromium/chrome/browser/suggestions/tile/Tile.java",
  "java/src/org/chromium/chrome/browser/suggestions/tile/TileGroup.java",
  "java/src/org/chromium/chrome/browser/suggestions/tile/TileGroupDelegateImpl.java",
  "java/src/org/chromium/chrome/browser/suggestions/tile/TileRenderer.java",
  "java/src/org/chromium/chrome/browser/suggestions/tile/TopSitesTileView.java",
  "java/src/org/chromium/chrome/browser/supervised_user/ChildAccountFeedbackReporter.java",
  "java/src/org/chromium/chrome/browser/supervised_user/ChildAccountService.java",
  "java/src/org/chromium/chrome/browser/survey/ChromeSurveyController.java",
  "java/src/org/chromium/chrome/browser/survey/SurveyController.java",
  "java/src/org/chromium/chrome/browser/survey/SurveyHttpClientBridge.java",
  "java/src/org/chromium/chrome/browser/sync/SyncErrorNotifier.java",
  "java/src/org/chromium/chrome/browser/sync/TrustedVaultClient.java",
  "java/src/org/chromium/chrome/browser/sync/settings/AccountManagementFragment.java",
  "java/src/org/chromium/chrome/browser/sync/settings/ClearDataProgressDialog.java",
  "java/src/org/chromium/chrome/browser/sync/settings/GoogleServicesSettings.java",
  "java/src/org/chromium/chrome/browser/sync/settings/ManageSyncSettings.java",
  "java/src/org/chromium/chrome/browser/sync/settings/SignInPreference.java",
  "java/src/org/chromium/chrome/browser/sync/settings/SyncErrorCardPreference.java",
  "java/src/org/chromium/chrome/browser/sync/settings/SyncPromoPreference.java",
  "java/src/org/chromium/chrome/browser/sync/settings/SyncSettingsUtils.java",
  "java/src/org/chromium/chrome/browser/sync/ui/PassphraseActivity.java",
  "java/src/org/chromium/chrome/browser/sync/ui/PassphraseCreationDialogFragment.java",
  "java/src/org/chromium/chrome/browser/sync/ui/PassphraseDialogFragment.java",
  "java/src/org/chromium/chrome/browser/sync/ui/PassphraseTypeDialogFragment.java",
  "java/src/org/chromium/chrome/browser/sync/ui/SyncErrorMessage.java",
  "java/src/org/chromium/chrome/browser/sync/ui/SyncErrorPromptUtils.java",
  "java/src/org/chromium/chrome/browser/sync/ui/SyncTrustedVaultProxyActivity.java",
  "java/src/org/chromium/chrome/browser/tab/AccessibilityVisibilityHandler.java",
  "java/src/org/chromium/chrome/browser/tab/AutofillSessionLifetimeController.java",
  "java/src/org/chromium/chrome/browser/tab/InterceptNavigationDelegateClientImpl.java",
  "java/src/org/chromium/chrome/browser/tab/InterceptNavigationDelegateTabHelper.java",
  "java/src/org/chromium/chrome/browser/tab/RedirectHandlerTabHelper.java",
  "java/src/org/chromium/chrome/browser/tab/RequestDesktopUtils.java",
  "java/src/org/chromium/chrome/browser/tab/TabBrowserControlsConstraintsHelper.java",
  "java/src/org/chromium/chrome/browser/tab/TabBrowserControlsOffsetHelper.java",
  "java/src/org/chromium/chrome/browser/tab/TabBuilder.java",
  "java/src/org/chromium/chrome/browser/tab/TabContextMenuItemDelegate.java",
  "java/src/org/chromium/chrome/browser/tab/TabContextMenuPopulator.java",
  "java/src/org/chromium/chrome/browser/tab/TabContextMenuPopulatorFactory.java",
  "java/src/org/chromium/chrome/browser/tab/TabFavicon.java",
  "java/src/org/chromium/chrome/browser/tab/TabGestureStateListener.java",
  "java/src/org/chromium/chrome/browser/tab/TabHelpers.java",
  "java/src/org/chromium/chrome/browser/tab/TabImpl.java",
  "java/src/org/chromium/chrome/browser/tab/TabImportanceManager.java",
  "java/src/org/chromium/chrome/browser/tab/TabParentIntent.java",
  "java/src/org/chromium/chrome/browser/tab/TabStateBrowserControlsVisibilityDelegate.java",
  "java/src/org/chromium/chrome/browser/tab/TabStateExtractor.java",
  "java/src/org/chromium/chrome/browser/tab/TabThemeColorHelper.java",
  "java/src/org/chromium/chrome/browser/tab/TabUma.java",
  "java/src/org/chromium/chrome/browser/tab/TabUtils.java",
  "java/src/org/chromium/chrome/browser/tab/TabViewAndroidDelegate.java",
  "java/src/org/chromium/chrome/browser/tab/TabViewManagerImpl.java",
  "java/src/org/chromium/chrome/browser/tab/TabWebContentsDelegateAndroidImpl.java",
  "java/src/org/chromium/chrome/browser/tab/TabWebContentsObserver.java",
  "java/src/org/chromium/chrome/browser/tab/tab_restore/HistoricalEntry.java",
  "java/src/org/chromium/chrome/browser/tab/tab_restore/HistoricalTabModelObserver.java",
  "java/src/org/chromium/chrome/browser/tab/tab_restore/HistoricalTabSaver.java",
  "java/src/org/chromium/chrome/browser/tab/tab_restore/HistoricalTabSaverImpl.java",
  "java/src/org/chromium/chrome/browser/tabbed_mode/TabbedAppMenuPropertiesDelegate.java",
  "java/src/org/chromium/chrome/browser/tabbed_mode/TabbedNavigationBarColorController.java",
  "java/src/org/chromium/chrome/browser/tabbed_mode/TabbedRootUiCoordinator.java",
  "java/src/org/chromium/chrome/browser/tabbed_mode/TabbedSystemUiCoordinator.java",
  "java/src/org/chromium/chrome/browser/tabmodel/ChromeTabCreator.java",
  "java/src/org/chromium/chrome/browser/tabmodel/IncognitoTabModelImplCreator.java",
  "java/src/org/chromium/chrome/browser/tabmodel/PendingTabClosureManager.java",
  "java/src/org/chromium/chrome/browser/tabmodel/TabModelDelegate.java",
  "java/src/org/chromium/chrome/browser/tabmodel/TabModelImpl.java",
  "java/src/org/chromium/chrome/browser/tabmodel/TabModelJniBridge.java",
  "java/src/org/chromium/chrome/browser/tabmodel/TabModelObserverJniBridge.java",
  "java/src/org/chromium/chrome/browser/tabmodel/TabModelSelectorBase.java",
  "java/src/org/chromium/chrome/browser/tabmodel/TabModelSelectorImpl.java",
  "java/src/org/chromium/chrome/browser/tabmodel/TabModelSelectorProfileSupplier.java",
  "java/src/org/chromium/chrome/browser/tabmodel/TabPersistencePolicy.java",
  "java/src/org/chromium/chrome/browser/tabmodel/TabPersistentStore.java",
  "java/src/org/chromium/chrome/browser/tabmodel/TabbedModeTabPersistencePolicy.java",
  "java/src/org/chromium/chrome/browser/tabmodel/document/TabDelegate.java",
  "java/src/org/chromium/chrome/browser/tasks/ConditionalTabStripUtils.java",
  "java/src/org/chromium/chrome/browser/tasks/EngagementTimeUtil.java",
  "java/src/org/chromium/chrome/browser/tasks/JourneyManager.java",
  "java/src/org/chromium/chrome/browser/tasks/ReturnToChromeUtil.java",
  "java/src/org/chromium/chrome/browser/tasks/TasksUma.java",
  "java/src/org/chromium/chrome/browser/toolbar/AppThemeColorProvider.java",
  "java/src/org/chromium/chrome/browser/toolbar/ToolbarButtonInProductHelpController.java",
  "java/src/org/chromium/chrome/browser/toolbar/ToolbarColors.java",
  "java/src/org/chromium/chrome/browser/toolbar/ToolbarManager.java",
  "java/src/org/chromium/chrome/browser/tracing/TracingController.java",
  "java/src/org/chromium/chrome/browser/tracing/TracingNotificationManager.java",
  "java/src/org/chromium/chrome/browser/tracing/TracingNotificationServiceImpl.java",
  "java/src/org/chromium/chrome/browser/tracing/settings/DeveloperSettings.java",
  "java/src/org/chromium/chrome/browser/tracing/settings/TracingCategoriesSettings.java",
  "java/src/org/chromium/chrome/browser/tracing/settings/TracingSettings.java",
  "java/src/org/chromium/chrome/browser/translate/TranslateAssistContent.java",
  "java/src/org/chromium/chrome/browser/translate/TranslateIntentHandler.java",
  "java/src/org/chromium/chrome/browser/translate/TranslateUtils.java",
  "java/src/org/chromium/chrome/browser/ui/AppLaunchDrawBlocker.java",
  "java/src/org/chromium/chrome/browser/ui/BottomContainer.java",
  "java/src/org/chromium/chrome/browser/ui/BottomSheetManager.java",
  "java/src/org/chromium/chrome/browser/ui/IncognitoRestoreAppLaunchDrawBlocker.java",
  "java/src/org/chromium/chrome/browser/ui/IncognitoRestoreAppLaunchDrawBlockerFactory.java",
  "java/src/org/chromium/chrome/browser/ui/MediaCaptureOverlayController.java",
  "java/src/org/chromium/chrome/browser/ui/RootUiCoordinator.java",
  "java/src/org/chromium/chrome/browser/ui/ViewDrawBlocker.java",
  "java/src/org/chromium/chrome/browser/ui/system/StatusBarColorController.java",
  "java/src/org/chromium/chrome/browser/ui/tablet/emptybackground/EmptyBackgroundViewTablet.java",
  "java/src/org/chromium/chrome/browser/ui/tablet/emptybackground/EmptyBackgroundViewWrapper.java",
  "java/src/org/chromium/chrome/browser/ui/tablet/emptybackground/incognitotoggle/IncognitoToggleButton.java",
  "java/src/org/chromium/chrome/browser/ui/tablet/emptybackground/incognitotoggle/IncognitoToggleButtonTablet.java",
  "java/src/org/chromium/chrome/browser/undo_tab_close_snackbar/UndoBarController.java",
  "java/src/org/chromium/chrome/browser/upgrade/PackageReplacedBroadcastReceiver.java",
  "java/src/org/chromium/chrome/browser/usage_stats/DigitalWellbeingClient.java",
  "java/src/org/chromium/chrome/browser/usage_stats/EventTracker.java",
  "java/src/org/chromium/chrome/browser/usage_stats/NotificationSuspender.java",
  "java/src/org/chromium/chrome/browser/usage_stats/PageViewObserver.java",
  "java/src/org/chromium/chrome/browser/usage_stats/SuspendedTab.java",
  "java/src/org/chromium/chrome/browser/usage_stats/SuspensionTracker.java",
  "java/src/org/chromium/chrome/browser/usage_stats/TokenGenerator.java",
  "java/src/org/chromium/chrome/browser/usage_stats/TokenTracker.java",
  "java/src/org/chromium/chrome/browser/usage_stats/UsageStatsBridge.java",
  "java/src/org/chromium/chrome/browser/usage_stats/UsageStatsConsentActivity.java",
  "java/src/org/chromium/chrome/browser/usage_stats/UsageStatsConsentDialog.java",
  "java/src/org/chromium/chrome/browser/usage_stats/UsageStatsMetricsEvent.java",
  "java/src/org/chromium/chrome/browser/usage_stats/UsageStatsMetricsReporter.java",
  "java/src/org/chromium/chrome/browser/usage_stats/UsageStatsService.java",
  "java/src/org/chromium/chrome/browser/usage_stats/WebsiteEvent.java",
  "java/src/org/chromium/chrome/browser/vr/ArDelegateProvider.java",
  "java/src/org/chromium/chrome/browser/webapps/ActivateWebApkActivity.java",
  "java/src/org/chromium/chrome/browser/webapps/AddToHomescreenMostVisitedTileClickObserver.java",
  "java/src/org/chromium/chrome/browser/webapps/ChromeWebApkHost.java",
  "java/src/org/chromium/chrome/browser/webapps/GooglePlayWebApkInstallDelegate.java",
  "java/src/org/chromium/chrome/browser/webapps/SameTaskWebApkActivity.java",
  "java/src/org/chromium/chrome/browser/webapps/WebApkActivityCoordinator.java",
  "java/src/org/chromium/chrome/browser/webapps/WebApkActivityLifecycleUmaTracker.java",
  "java/src/org/chromium/chrome/browser/webapps/WebApkDataProvider.java",
  "java/src/org/chromium/chrome/browser/webapps/WebApkHandlerDelegate.java",
  "java/src/org/chromium/chrome/browser/webapps/WebApkIconNameUpdateCustomView.java",
  "java/src/org/chromium/chrome/browser/webapps/WebApkIconNameUpdateDialog.java",
  "java/src/org/chromium/chrome/browser/webapps/WebApkInstallCoordinatorBridge.java",
  "java/src/org/chromium/chrome/browser/webapps/WebApkInstallCoordinatorServiceImpl.java",
  "java/src/org/chromium/chrome/browser/webapps/WebApkInstallService.java",
  "java/src/org/chromium/chrome/browser/webapps/WebApkInstaller.java",
  "java/src/org/chromium/chrome/browser/webapps/WebApkIntentDataProviderFactory.java",
  "java/src/org/chromium/chrome/browser/webapps/WebApkOfflineDialog.java",
  "java/src/org/chromium/chrome/browser/webapps/WebApkPostShareTargetNavigator.java",
  "java/src/org/chromium/chrome/browser/webapps/WebApkServiceClient.java",
  "java/src/org/chromium/chrome/browser/webapps/WebApkShareTargetUtil.java",
  "java/src/org/chromium/chrome/browser/webapps/WebApkSplashNetworkErrorObserver.java",
  "java/src/org/chromium/chrome/browser/webapps/WebApkUpdateDataFetcher.java",
  "java/src/org/chromium/chrome/browser/webapps/WebApkUpdateManager.java",
  "java/src/org/chromium/chrome/browser/webapps/WebApkUpdateReportAbuseDialog.java",
  "java/src/org/chromium/chrome/browser/webapps/WebApkUpdateTask.java",
  "java/src/org/chromium/chrome/browser/webapps/WebappActionsNotificationManager.java",
  "java/src/org/chromium/chrome/browser/webapps/WebappActiveTabUmaTracker.java",
  "java/src/org/chromium/chrome/browser/webapps/WebappActivity.java",
  "java/src/org/chromium/chrome/browser/webapps/WebappActivityCoordinator.java",
  "java/src/org/chromium/chrome/browser/webapps/WebappAuthenticator.java",
  "java/src/org/chromium/chrome/browser/webapps/WebappCustomTabTimeSpentLogger.java",
  "java/src/org/chromium/chrome/browser/webapps/WebappDataStorage.java",
  "java/src/org/chromium/chrome/browser/webapps/WebappDeferredStartupWithStorageHandler.java",
  "java/src/org/chromium/chrome/browser/webapps/WebappDirectoryManager.java",
  "java/src/org/chromium/chrome/browser/webapps/WebappIntentDataProvider.java",
  "java/src/org/chromium/chrome/browser/webapps/WebappIntentDataProviderFactory.java",
  "java/src/org/chromium/chrome/browser/webapps/WebappLaunchCauseMetrics.java",
  "java/src/org/chromium/chrome/browser/webapps/WebappLauncherActivity.java",
  "java/src/org/chromium/chrome/browser/webapps/WebappLocator.java",
  "java/src/org/chromium/chrome/browser/webapps/WebappRegistry.java",
  "java/src/org/chromium/chrome/browser/webapps/launchpad/LaunchpadActivity.java",
  "java/src/org/chromium/chrome/browser/webapps/launchpad/LaunchpadUtils.java",
  "java/src/org/chromium/chrome/browser/webauth/authenticator/CableAuthenticatorActivity.java",
  "java/src/org/chromium/chrome/browser/webauth/authenticator/CableAuthenticatorUSBActivity.java",
  "java/src/org/chromium/chrome/browser/webshare/ShareServiceImplementationFactory.java",
]
