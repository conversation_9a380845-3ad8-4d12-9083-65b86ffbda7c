Tests exportKey() given bad inputs.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".

error is: TypeError: Failed to execute 'exportKey' on 'SubtleCrypto': parameter 2 is not of type 'CryptoKey'.
error is: TypeError: Failed to execute 'exportKey' on 'SubtleCrypto': parameter 2 is not of type 'CryptoKey'.
error is: TypeError: Invalid keyFormat argument
error is: TypeError: Invalid keyFormat argument
error is: TypeError: Invalid keyFormat argument
PASS successfullyParsed is true

TEST COMPLETE

