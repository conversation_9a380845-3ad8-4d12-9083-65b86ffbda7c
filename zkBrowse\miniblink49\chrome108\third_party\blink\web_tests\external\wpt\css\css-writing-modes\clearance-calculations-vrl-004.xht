<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

 <head>

  <title>CSS Writing Modes Test: Clearance calculations - clearing box has narrower margin than clearance</title>

  <!--
  This test is the verticalized version of
  http://test.csswg.org/suites/css2.1/latest/html4/clear-clearance-calculation-002.htm
  -->

  <link rel="author" title="<PERSON><PERSON>" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" />
  <link rel="help" href="http://www.w3.org/TR/css-writing-modes-3/#vertical-layout" title="7.1 Principles of Layout in Vertical Writing Modes" />
  <link rel="match" href="clearance-calculations-vrl-004-ref.xht" />

  <meta content="ahem image" name="flags" />
  <meta content="This test checks that if, after margin collapsing, the position of the clearing element is on the right of the float or would be hypothetically positioned within the layout position of the float, then the clearing element is positioned directly on the left of the floated element." name="assert" />

  <link rel="stylesheet" type="text/css" href="/fonts/ahem.css" />
  <style type="text/css"><![CDATA[
  div
    {
      height: 5em; /* computes to 100px */
    }

  div#parent
    {
      background: red url("support/clearance-calculation-vrl-004.png");
      font: 20px/1 Ahem; /* computes to 20px/20px */
      min-width: 8em;
      writing-mode: vertical-rl;
    }

  div > div
    {
      background-color: green;
      width: 1em;
    }

  div#preceding-sibling
    {
      margin-left: 4em;
    }

  div#floated-left
    {
      float: left;
      width: 2em;
    }

  div#clearing-left
    {
      clear: left;
      margin-right: 3em;
    }
  ]]></style>

 </head>

 <body>

  <p>Test passes if there is <strong>no red</strong>.</p>

  <div id="parent">
    <div id="preceding-sibling"></div>
    <div id="floated-left"></div>
    <div id="clearing-left"></div>
  </div>

 </body>
</html>