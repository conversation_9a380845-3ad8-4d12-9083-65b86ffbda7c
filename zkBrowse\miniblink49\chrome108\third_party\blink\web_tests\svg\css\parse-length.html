<!DOCTYPE html>
<html>
<script src="../../resources/ahem.js"></script>
<style>
* { font-size: 16px; }
svg, rect { font-family: 'Ahem'; }
div { font-size: 8px; } 
</style>
<html>
<svg id="svg" width="0" height="0"></svg>
<script src="../../resources/js-test.js"></script>
<script>
description("Test that 'length' presentation attribute values are parsed with CSS presentation rules.");

function computedStyle(elementname, property, value) {
  var elm = document.createElementNS('http://www.w3.org/2000/svg', elementname);
  document.getElementById('svg').appendChild(elm);
  elm.setAttribute(property, value);
  var computedValue = getComputedStyle(elm).getPropertyValue(property);
  document.getElementById('svg').removeChild(elm);
  return computedValue;
}

function testComputed(elementname, property, value, expected) {
  shouldBeEqualToString('computedStyle("' + elementname + '", "' + property + '", "' + value + '")', expected);
}

function negativeTest(elementname, property, value, expected) {
  testComputed(elementname, property, value, expected);
}

function testAttributeOnElement(elementname, attributename, initial_value) {
  testComputed(elementname, attributename, "  100", "100px");
  testComputed(elementname, attributename, "100   ", "100px");
  testComputed(elementname, attributename, "100px", "100px");
  testComputed(elementname, attributename, "1em", "16px");
  // testComputed(elementname, attributename, "1ex", "12.8000001907349px"); // enable this again once http://crbug.com/441840 is fixed
  testComputed(elementname, attributename, "20%", "20%");
  testComputed(elementname, attributename, "-200px", "-200px");

  negativeTest(elementname, attributename, "auto", initial_value);
  negativeTest(elementname, attributename, "100   px", initial_value);
  negativeTest(elementname, attributename, "100px;", initial_value);
  negativeTest(elementname, attributename, "100px !important", initial_value);
  negativeTest(elementname, attributename, "{ 100px }", initial_value);
}

var xyelements = [ "mask", "svg", "rect", "image", "foreignObject" ];
for (var elm of xyelements) {
  let initial_value = elm === "mask" ? "-10%" : "0px";
  testAttributeOnElement(elm, "x", initial_value);
  testAttributeOnElement(elm, "y", initial_value);
}

var rxryelements = [ "rect", "ellipse" ];
for (var elm of rxryelements) {
  let initial_value = "0px";
  testAttributeOnElement(elm, "rx", initial_value);
  testAttributeOnElement(elm, "ry", initial_value);
}

testAttributeOnElement("circle", "r", "0px");
</script>
</body>
</html>
