This test verifies that the Home/End/PageUp/PageDown keys work correctly for <select> elements. Since it requires eventSender.keyDown, it will not run solo in the web browser; it must be run with run_web_tests.py.

0
1
2
3
4
5
6
 
0
1
2
3
4
5
6
7
 
0
1
2
3
4
5
6

PASS sendKeyAndExpectIndex("ss", "PageDown", 0, 3) is true
PASS sendKeyAndExpectIndex("ss", "PageDown", 1, 4) is true
PASS sendKeyAndExpectIndex("ss", "PageDown", 2, 5) is true
PASS sendKeyAndExpectIndex("ss", "PageDown", 3, 6) is true
PASS sendKeyAndExpectIndex("ss", "PageDown", 4, 6) is true
PASS sendKeyAndExpectIndex("ss", "PageDown", 5, 6) is true
PASS sendKeyAndExpectIndex("ss", "PageDown", 6, 6) is true
PASS sendKeyAndExpectIndex("ss", "PageUp", 6, 3) is true
PASS sendKeyAndExpectIndex("ss", "PageUp", 5, 2) is true
PASS sendKeyAndExpectIndex("ss", "PageUp", 4, 1) is true
PASS sendKeyAndExpectIndex("ss", "PageUp", 3, 0) is true
PASS sendKeyAndExpectIndex("ss", "PageUp", 2, 0) is true
PASS sendKeyAndExpectIndex("ss", "PageUp", 1, 0) is true
PASS sendKeyAndExpectIndex("ss", "PageUp", 0, 0) is true
PASS sendKeyAndExpectIndex("ss", "Home", 6, 0) is true
PASS sendKeyAndExpectIndex("ss", "Home", 5, 0) is true
PASS sendKeyAndExpectIndex("ss", "Home", 4, 0) is true
PASS sendKeyAndExpectIndex("ss", "Home", 3, 0) is true
PASS sendKeyAndExpectIndex("ss", "Home", 2, 0) is true
PASS sendKeyAndExpectIndex("ss", "Home", 1, 0) is true
PASS sendKeyAndExpectIndex("ss", "Home", 0, 0) is true
PASS sendKeyAndExpectIndex("ss", "End", 6, 6) is true
PASS sendKeyAndExpectIndex("ss", "End", 5, 6) is true
PASS sendKeyAndExpectIndex("ss", "End", 4, 6) is true
PASS sendKeyAndExpectIndex("ss", "End", 3, 6) is true
PASS sendKeyAndExpectIndex("ss", "End", 2, 6) is true
PASS sendKeyAndExpectIndex("ss", "End", 1, 6) is true
PASS sendKeyAndExpectIndex("ss", "End", 0, 6) is true
PASS sendKeyAndExpectIndex("ssd", "PageDown", 0, 4) is true
PASS sendKeyAndExpectIndex("ssd", "PageDown", 1, 4) is true
PASS sendKeyAndExpectIndex("ssd", "PageDown", 2, 5) is true
PASS sendKeyAndExpectIndex("ssd", "PageDown", 4, 6) is true
PASS sendKeyAndExpectIndex("ssd", "PageDown", 5, 6) is true
PASS sendKeyAndExpectIndex("ssd", "PageDown", 6, 6) is true
PASS sendKeyAndExpectIndex("ssd", "PageDown", 7, 6) is true
PASS sendKeyAndExpectIndex("ssd", "PageUp", 7, 4) is true
PASS sendKeyAndExpectIndex("ssd", "PageUp", 6, 2) is true
PASS sendKeyAndExpectIndex("ssd", "PageUp", 5, 2) is true
PASS sendKeyAndExpectIndex("ssd", "PageUp", 4, 1) is true
PASS sendKeyAndExpectIndex("ssd", "PageUp", 2, 1) is true
PASS sendKeyAndExpectIndex("ssd", "PageUp", 1, 1) is true
PASS sendKeyAndExpectIndex("ssd", "PageUp", 0, 1) is true
PASS sendKeyAndExpectIndex("ssd", "Home", 7, 1) is true
PASS sendKeyAndExpectIndex("ssd", "Home", 6, 1) is true
PASS sendKeyAndExpectIndex("ssd", "Home", 5, 1) is true
PASS sendKeyAndExpectIndex("ssd", "Home", 4, 1) is true
PASS sendKeyAndExpectIndex("ssd", "Home", 3, 1) is true
PASS sendKeyAndExpectIndex("ssd", "Home", 2, 1) is true
PASS sendKeyAndExpectIndex("ssd", "Home", 1, 1) is true
PASS sendKeyAndExpectIndex("ssd", "Home", 0, 1) is true
PASS sendKeyAndExpectIndex("ssd", "End", 7, 6) is true
PASS sendKeyAndExpectIndex("ssd", "End", 6, 6) is true
PASS sendKeyAndExpectIndex("ssd", "End", 5, 6) is true
PASS sendKeyAndExpectIndex("ssd", "End", 4, 6) is true
PASS sendKeyAndExpectIndex("ssd", "End", 3, 6) is true
PASS sendKeyAndExpectIndex("ssd", "End", 2, 6) is true
PASS sendKeyAndExpectIndex("ssd", "End", 1, 6) is true
PASS sendKeyAndExpectIndex("ssd", "End", 0, 6) is true
PASS sendWithShiftKeyAndExpectIndices("ss", "PageDown", [0, 1, 2, 3]) is true
PASS sendWithShiftKeyAndExpectIndices("ss", "PageDown", [0, 1, 2, 3, 4, 5, 6]) is true
PASS sendWithShiftKeyAndExpectIndices("ss", "PageDown", [0, 1, 2, 3, 4, 5, 6]) is true
PASS sendWithShiftKeyAndExpectIndices("ss", "PageUp", [0, 1, 2, 3]) is true
PASS sendWithShiftKeyAndExpectIndices("ss", "PageUp", [0]) is true
PASS sendWithShiftKeyAndExpectIndices("ss", "PageUp", [0]) is true
PASS sendWithShiftKeyAndExpectIndices("ss", "End", [0, 1, 2, 3, 4, 5, 6]) is true
PASS sendWithShiftKeyAndExpectIndices("ss", "End", [0, 1, 2, 3, 4, 5, 6]) is true
PASS sendWithShiftKeyAndExpectIndices("ss", "Home", [0]) is true
PASS sendWithShiftKeyAndExpectIndices("ss", "Home", [0]) is true
PASS sendWithShiftKeyAndExpectIndices("ss", "PageDown", [3, 4, 5, 6]) is true
PASS sendWithShiftKeyAndExpectIndices("ss", "PageDown", [3, 4, 5, 6]) is true
PASS sendWithShiftKeyAndExpectIndices("ss", "PageUp", [3]) is true
PASS sendWithShiftKeyAndExpectIndices("ss", "PageUp", [0, 1, 2, 3]) is true
PASS sendWithShiftKeyAndExpectIndices("ss", "PageUp", [0, 1, 2, 3]) is true
PASS sendWithShiftKeyAndExpectIndices("ss", "PageUp", [0, 1, 2, 3]) is true
PASS sendWithShiftKeyAndExpectIndices("ss", "End", [3, 4, 5, 6]) is true
PASS sendWithShiftKeyAndExpectIndices("ss", "End", [3, 4, 5, 6]) is true
PASS sendWithShiftKeyAndExpectIndices("ss", "Home", [0, 1, 2, 3]) is true
PASS sendWithShiftKeyAndExpectIndices("ss", "Home", [0, 1, 2, 3]) is true
PASS sendWithShiftKeyAndExpectIndices("ssd", "PageDown", [1, 2, 4]) is true
PASS sendWithShiftKeyAndExpectIndices("ssd", "PageDown", [1, 2, 4, 5, 6]) is true
PASS sendWithShiftKeyAndExpectIndices("ssd", "PageDown", [1, 2, 4, 5, 6]) is true
PASS sendWithShiftKeyAndExpectIndices("ssd", "PageUp", [1, 2]) is true
PASS sendWithShiftKeyAndExpectIndices("ssd", "PageUp", [1]) is true
PASS sendWithShiftKeyAndExpectIndices("ssd", "PageUp", [1]) is true
PASS sendWithShiftKeyAndExpectIndices("ssd", "End", [1, 2, 4, 5, 6]) is true
PASS sendWithShiftKeyAndExpectIndices("ssd", "End", [1, 2, 4, 5, 6]) is true
PASS sendWithShiftKeyAndExpectIndices("ssd", "Home", [1]) is true
PASS sendWithShiftKeyAndExpectIndices("ssd", "Home", [1]) is true
PASS sendWithShiftKeyAndExpectIndices("ssd", "PageDown", [4, 5, 6]) is true
PASS sendWithShiftKeyAndExpectIndices("ssd", "PageDown", [4, 5, 6]) is true
PASS sendWithShiftKeyAndExpectIndices("ssd", "PageUp", [2, 4]) is true
PASS sendWithShiftKeyAndExpectIndices("ssd", "PageUp", [1, 2, 4]) is true
PASS sendWithShiftKeyAndExpectIndices("ssd", "PageUp", [1, 2, 4]) is true
PASS sendWithShiftKeyAndExpectIndices("ssd", "End", [4, 5, 6]) is true
PASS sendWithShiftKeyAndExpectIndices("ssd", "End", [4, 5, 6]) is true
PASS sendWithShiftKeyAndExpectIndices("ssd", "Home", [1, 2, 4]) is true
PASS sendWithShiftKeyAndExpectIndices("ssd", "Home", [1, 2, 4]) is true
PASS sendKeyAndExpectIndex("ssg", "PageDown", 0, 3) is true
PASS sendKeyAndExpectIndex("ssg", "PageDown", 1, 3) is true
PASS sendKeyAndExpectIndex("ssg", "PageDown", 2, 4) is true
PASS sendKeyAndExpectIndex("ssg", "PageDown", 3, 5) is true
PASS sendKeyAndExpectIndex("ssg", "PageDown", 4, 6) is true
PASS sendKeyAndExpectIndex("ssg", "PageDown", 5, 6) is true
PASS sendKeyAndExpectIndex("ssg", "PageDown", 6, 6) is true
PASS sendKeyAndExpectIndex("ssg", "PageUp", 6, 4) is true
PASS sendKeyAndExpectIndex("ssg", "PageUp", 5, 3) is true
PASS sendKeyAndExpectIndex("ssg", "PageUp", 4, 2) is true
PASS sendKeyAndExpectIndex("ssg", "PageUp", 3, 1) is true
PASS sendKeyAndExpectIndex("ssg", "PageUp", 2, 0) is true
PASS sendKeyAndExpectIndex("ssg", "PageUp", 1, 0) is true
PASS sendKeyAndExpectIndex("ssg", "PageUp", 0, 0) is true
PASS successfullyParsed is true

TEST COMPLETE

