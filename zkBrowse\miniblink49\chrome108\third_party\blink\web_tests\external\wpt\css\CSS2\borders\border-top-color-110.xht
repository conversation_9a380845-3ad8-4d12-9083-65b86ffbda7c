<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Border-top-color set to rgb() using percentages with blue set to the minimum plus one value, rgb(0%, 0%, 1%)</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="author" title="<PERSON> Hauck" href="mailto:<EMAIL>" /> <!-- converted to reftest 01-08-2013 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#propdef-border-top-color" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#border-color-properties" />
        <link rel="match" href="border-bottom-color-110-ref.xht"/>
        <meta name="assert" content="The 'border-top-color' set to rgb(0%, 0%, 1%) renders the correct color." />
        <style type="text/css">
            div
            {
                height: 1in;
                width: 1in;
            }
            #test
            {
                border-top-style: solid;
                border-top-width: 1in;
                border-top-color: rgb(0%, 0%, 1%);
                height: 0;
            }
            #reference
            {
                background-color: rgb(0%, 0%, 1%);
                margin-top: 10px;
            }
        </style>
    </head>
    <body>
        <p>Test passes if the boxes below are the same color.</p>
        <div id="test"></div>
        <div id="reference"></div>
    </body>
</html>
