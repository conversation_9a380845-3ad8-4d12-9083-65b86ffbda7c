<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright 2022 The Chromium Authors
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file.
-->

<LinearLayout
  xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  xmlns:tools="http://schemas.android.com/tools"
  android:layout_height="wrap_content"
  android:layout_width="match_parent"
  android:paddingLeft="@dimen/fast_checkout_sheet_margin"
  android:paddingRight="@dimen/fast_checkout_sheet_margin"
  android:orientation="vertical">

 <ImageView
    android:id="@+id/drag_handlebar"
    android:layout_height="wrap_content"
    android:layout_width="32dp"
    android:layout_gravity="center_horizontal"
    android:layout_marginTop="8dp"
    android:layout_marginBottom="12dp"
    android:importantForAccessibility="no"
    app:srcCompat="@drawable/drag_handlebar" />

  <ViewFlipper
    android:id="@+id/fast_checkout_bottom_sheet_view_flipper"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:measureAllChildren="false" >

      <include
          android:id="@+id/fast_checkout_home_screen_sheet"
          layout="@layout/fast_checkout_home_screen_sheet" />
      <include
          android:id="@+id/fast_checkout_detail_screen_sheet"
          layout="@layout/fast_checkout_detail_screen_sheet" />
  </ViewFlipper>
</LinearLayout>
