<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
    <head>
        <title>CSS Test: Outline-color set to '#00000'</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/">
        <link rel="help" href="http://www.w3.org/TR/CSS21/ui.html#propdef-outline-color">
        <link rel="help" href="http://www.w3.org/TR/CSS21/ui.html#dynamic-outlines">
        <meta name="flags" content="invalid">
        <meta name="assert" content="Outline color #00000 (invalid) falls back to a default color.">
        <style type="text/css">
            #div1
            {
                outline-color: #00000;
                outline-style: solid;
                outline-width: 50px;
                margin: 100px 0 0 100px;
                width: 0;
            }
            #reference
            {
                background-color: black;
                height: 100px;
                margin: 200px 0 0 50px;
                width: 100px;
            }
        </style>
    </head>
    <body>
        <p>Test passes if the two boxes below are the same color.</p>
        <div id="div1"></div>
        <div id="reference"></div>
    </body>
</html>