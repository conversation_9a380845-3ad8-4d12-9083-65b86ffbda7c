# Copyright 2017 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

component("cpp") {
  sources = [
    "audio_system_to_service_adapter.cc",
    "audio_system_to_service_adapter.h",
    "debug_recording_session.cc",
    "debug_recording_session.h",
    "debug_recording_session_factory.cc",
    "debug_recording_session_factory.h",
    "device_factory.cc",
    "device_factory.h",
    "input_ipc.cc",
    "input_ipc.h",
    "output_device.cc",
    "output_device.h",
    "sounds/audio_stream_handler.cc",
    "sounds/audio_stream_handler.h",
    "sounds/sounds_manager.cc",
    "sounds/sounds_manager.h",
  ]

  defines = [ "IS_AUDIO_PUBLIC_CPP_IMPL" ]

  configs += [ "//build/config/compiler:wexit_time_destructors" ]

  public_deps = [
    "//base",
    "//media",
    "//media/mojo/common",
    "//services/audio/public/mojom",
  ]

  output_name = "audio_public_cpp"
}

source_set("test_support") {
  testonly = true

  sources = [
    "fake_stream_factory.cc",
    "fake_stream_factory.h",
    "fake_system_info.cc",
    "fake_system_info.h",
    "sounds/test_data.cc",
    "sounds/test_data.h",
  ]

  deps = [ "//testing/gmock" ]

  public_deps = [
    ":cpp",
    "//base",
    "//media",
    "//services/audio",
    "//services/audio/public/mojom",
  ]
}
