<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Border-width set using two values</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="<PERSON><PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-06-26 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#border-width-properties" />
		<link rel="match" href="border-width-shorthand-002-ref.xht" />

        <meta name="assert" content="Applying two values to the border-width property applies the first value to the top and bottom and the second to the left and right." />
        <style type="text/css">
            div
            {
                border-width: 3px 10px;
                border-style: solid;
                height: 1in;
            }
        </style>
    </head>
    <body>
        <p>Test passes if the left and right borders match in width and are wider than the matching top and bottom borders.</p>
        <div></div>
    </body>
</html>