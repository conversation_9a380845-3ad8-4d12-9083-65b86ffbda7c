<!DOCTYPE html>
<title>
  Test for 'object-fit: cover' on SVG image w/ aspect ratio but no
  intrinsic size
</title>
<style type="text/css">
  img {
      width: 160px;
      height: 80px;
  }
</style>
<img src="data:image/svg+xml,
          <svg viewBox='0 0 160 80' xmlns='http://www.w3.org/2000/svg'>
            <rect stroke-width='10' stroke='black'
               x='45' y='5' width='70' height='70' fill='lime'/>
          </svg>">
