include_rules = [
  # The test directory can do whatever it wants in chrome, and may
  # rely on components.
  "+ash",
  "+chrome",
  "+chrome/android/java/src/org/chromium/chrome/browser/app/ChromeActivity.java",
  "+chrome/android/java/src/org/chromium/chrome/browser/ChromeTabbedActivity.java",
  "+chrome/grit",
  "+chromeos",
  "+components/account_id",
  "+components/account_manager_core",
  "+components/autofill/content",
  "+components/autofill/core",
  "+components/bookmarks/browser",
  "+components/bookmarks/common",
  "+components/captive_portal",
  "+components/component_updater",
  "+components/constrained_window",
  "+components/content_settings/core/browser",
  "+components/contextual_search/core/browser",
  "+components/crash/core/app",
  "+components/domain_reliability",
  "+components/download/public/common",
  "+components/favicon/core",
  "+components/feature_engagement",
  "+components/find_in_page",
  "+components/gcm_driver/instance_id",
  "+components/global_media_controls",
  "+components/google/core/common",
  "+components/guest_view/browser",
  "+components/history/content",
  "+components/history/core",
  "+components/infobars/content",
  "+components/infobars/core",
  "+components/javascript_dialogs",
  "+components/keep_alive_registry",
  "+components/keyed_service/content",
  "+components/keyed_service/core",
  "+components/leveldb_proto/public",
  "+components/live_caption",
  "+components/media_router/browser",
  "+components/metrics",
  "+components/nacl/browser",
  "+components/nacl/common",
  "+components/nacl/renderer",
  "+components/network_time",
  "+components/offline_pages/buildflags",
  "+components/offline_pages/core",
  "+components/omnibox/browser",
  "+components/optimization_guide",
  "+components/optimization_guide/content",
  "+components/os_crypt",
  "+components/permissions",
  "+components/policy",
  "+components/prefs",
  "+components/profile_metrics",
  "+components/safe_browsing/buildflags.h",
  "+components/safe_browsing/core/browser/db",
  "+components/search_engines",
  "+components/security_interstitials/content",
  "+components/services/language_detection/public/cpp",
  "+components/services/quarantine",
  "+components/sessions",
  "+components/signin/public",
  "+components/spellcheck",
  "+components/startup_metric_utils",
  "+components/storage_monitor",
  "+components/subresource_filter/content/browser",
  "+components/federated_learning",
  "+components/sync/test",
  "+components/sync_preferences",
  "+components/unified_consent",
  "+components/update_client",
  "+components/user_education",
  "+components/user_manager",
  "+components/user_prefs",
  "+components/version_info",
  "+components/web_modal",
  "+components/webdata_services",
  "+components/zoom",
  "+device/bluetooth/dbus",
  "+extensions",
  "+mojo",
  "+rlz/buildflags",
  "+services",
  "+storage/browser",
  "+storage/common",

  # Tests under chrome/ shouldn't need to access the internals of content/ and
  # as such are allowed only content/public. If you find yourself wanting to
  # write such a test, or a test that depends primarily on content, think about
  # whether the test belongs under content/, or should be split up into a test
  # within content/ and a test within chrome/.
  "+content/public",

  "+gin/public",
  "+media/base",
  "+mojo/core/embedder",
  "+sandbox/win/tests",
  "+third_party/blink/public",
  "+third_party/webrtc",
  "+third_party/re2",
]
