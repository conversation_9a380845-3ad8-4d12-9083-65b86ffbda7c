<!DOCTYPE html>
<html>
<head>
    <title>preserveAspectRatio test 2/2</title>
    <style>
        img, object {
            border: 2px dashed maroon;
            height: 50px;
            left: 100px;
            overflow: visible;
            padding: 3px;
            top: 20px;
            width: 200px;
            box-sizing: border-box;
            -moz-box-sizing: border-box;
        }

        object {
            border-color: green;
            border-width: 1px;
        }
    </style>
</head>
<body>
    <h1>No size specified</h1>
    <img style="height: auto; width: auto;" src="resources/circle-default-default.svg"/>
    <object style="height: auto; width: auto;" data="resources/circle-default-default.svg"></object>
</body>
</html>
