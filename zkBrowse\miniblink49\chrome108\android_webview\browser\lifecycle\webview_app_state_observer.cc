// Copyright 2020 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include "android_webview/browser/lifecycle/webview_app_state_observer.h"

namespace android_webview {

WebViewAppStateObserver::WebViewAppStateObserver() = default;

WebViewAppStateObserver::~WebViewAppStateObserver() = default;

}  // namespace android_webview
