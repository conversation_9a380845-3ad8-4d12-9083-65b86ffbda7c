<!DOCTYPE html>
<script src='../../resources/gesture-util.js'></script>
<script src="../../resources/testharness.js"></script>
<script src="../../resources/testharnessreport.js"></script>
<div style="margin:0;padding:0;height:200px;width:2400px">
  <div style="margin:0;padding:0;background-color:red;height:200px;width:1200px;position:relative;left:0px;top:0px"></div>
  <div style="margin:0;padding:0;background-color:green;height:200px;width:1200px;position:relative;left:1200px;top:-200px"></div>
</div>
<div style="margin:0;padding:0;height:200px;width:2400px">
  <div style="margin:0;padding:0;background-color:blue;height:200px;width:1200px;position:relative;left:0px;top:0px"></div>
  <div style="margin:0;padding:0;background-color:yellow;height:200px;width:1200px;position:relative;left:1200px;top:-200px"></div>
</div>

<script>
var givenScrollLeft = 2; // When paging, this is ignored. Every event is one page.
var expectedScrollLeft = 700; // Window is 800x600. Scroll 87.5% of visible.
var last_event = null;
var source = GestureSourceType.MOUSE_INPUT;
const numTicksX = givenScrollLeft / pixelsPerTick();
const expectedWheelDeltaX = numTicksX * LEGACY_MOUSE_WHEEL_TICK_MULTIPLIER;

function mousewheelHandler(e)
{
    last_event = e;
}

promise_test(async () => {
    document.body.addEventListener("mousewheel", mousewheelHandler, false);

    await smoothScroll(givenScrollLeft, 100, 110, source, 'right', SPEED_INSTANT, false /* precise_scrolling_deltas */, true /* scroll_by_page */);
    await waitForAnimationEndTimeBased( () => {
        return document.scrollingElement.scrollLeft == window.expectedScrollLeft;
    });
    assert_equals(last_event.wheelDeltaX, -Math.floor(expectedWheelDeltaX));
    assert_equals(last_event.wheelDelta, -Math.floor(expectedWheelDeltaX));
}, 'This test checks one page of scroll on document moves the content by 700 pixels.');
</script>
