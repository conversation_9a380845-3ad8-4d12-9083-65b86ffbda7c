// Copyright 2019 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include "chrome/updater/win/test/test_strings.h"

namespace updater {

// Command line switches.
const char kTestSleepMinutesSwitch[] = "test-sleep-minutes";
const char kTestEventToSignal[] = "test-event-to-signal";
const char kTestEventToSignalIfMediumIntegrity[] =
    "test-event-to-signal-if-medium-integrity";
const char kTestEventToWaitOn[] = "test-event-to-wait-on";
const char kTestExitCode[] = "test-exit-code";

}  // namespace updater
