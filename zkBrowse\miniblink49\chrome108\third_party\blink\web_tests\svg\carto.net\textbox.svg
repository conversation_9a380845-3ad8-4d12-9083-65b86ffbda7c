<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" [
]>
<svg viewBox="0 0 1024 768" width="100%" height="100%" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" onload="initialize(evt)" zoomAndPan="disable" xml:space="preserve">
	<script type="text/ecmascript" xlink:href="resources/helper_functions.js"/>
	<script type="text/ecmascript" xlink:href="resources/mapApp.js"/>
	<script type="text/ecmascript" xlink:href="resources/timer.js"/>
	<script type="text/ecmascript" xlink:href="resources/textbox.js"/>
	<script type="text/ecmascript">
		<![CDATA[	
		var myMapApp = new mapApp(false,undefined);
		var textbox1;
		var textbox2;
		function initialize(evt) {
            //styles
            var textStyles = {"font-family":"Arial,Helvetica","font-size":15,"fill":"dimgray"};
            var boxStyles = {"fill":"white","stroke":"dimgray","stroke-width":1.5};
            var cursorStyles = {"stroke":"red","stroke-width":1.5};
            var selBoxStyles = {"fill":"blue","opacity":0.5};
			var textYOffset = 22;
			//create new textboxes
			textbox1 = new textbox("textbox1","textbox1","",25,100,100,200,30,textYOffset,textStyles,boxStyles,cursorStyles,selBoxStyles,"[a-zA-Z ]",writeOutTextContent);
			textbox2 = new textbox("textbox2","textbox2","50",5,100,30,60,30,textYOffset,textStyles,boxStyles,cursorStyles,selBoxStyles,"[0-9]",writeOutTextContent);
			textbox3 = new textbox("textbox3","textbox3","This is a transformed textbox with a very, very, very long content",300,0,0,230,30,textYOffset,textStyles,boxStyles,cursorStyles,selBoxStyles,undefined,writeOutTextContent);
		}
		function writeOutTextContent(textboxId,value,changeType) {
			if (changeType == "release") {
				document.getElementById("resultText").firstChild.nodeValue = "Content of "+textboxId+" is \""+value+"\"";
			}
			if (textboxId == "textbox2" && changeType == "change") {
				document.getElementById("resultText").firstChild.nodeValue = "Content of "+textboxId+" is \""+value+"\"";
			}
		}
		]]>
	</script>
	<rect x="-1000" y="-1000" width="3000" height="3000" fill="white" stroke="none"/>
	<g id="textbox1"/>
	<g id="textbox2"/>
	<g id="textbox3" transform="translate(100,300)"/>
	<g font-size="15px">
		<text id="resultText" x="100" y="230" xml:space="preserve">ResultText</text>	
		<text x="170" y="50">This textbox (textbox 2) only accepts up to 5 digits.</text>	
		<text x="310" y="120">This textbox (textbox 1) accepts up to 25 characters [a-zA-Z ].</text>	
		<text x="350" y="315">This textbox (textbox 3) is transformed<tspan x="350" dy="23">and accepts all characters (up to 300)</tspan></text>	
		<text x="620" y="315" onclick="textbox3.setValue('bla bla',false)">Click on this text to reset content of textbox nr 3</text>
		<text x="620" y="338" onclick="posX = -30 + Math.random()*40;posY = -25 + Math.random()*50;textbox3.moveTo(posX,posY)">Click on this text to randomly reposition textbox nr 3</text>
		<text x="620" y="361" onclick="newWidth = 100 + Math.random()*130;textbox3.resize(newWidth)">Click on this text to randomly resize textbox nr 3</text>
	</g>
</svg>
