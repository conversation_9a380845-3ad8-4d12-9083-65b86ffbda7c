// GENERATED CONTENT - DO NOT EDIT
// Content was automatically extracted by <PERSON><PERSON> into webref
// (https://github.com/w3c/webref)
// Source: WebXR Augmented Reality Module - Level 1 (https://immersive-web.github.io/webxr-ar-module/)

enum XREnvironmentBlendMode {
  "opaque",
  "alpha-blend",
  "additive"
};

partial interface XRSession {
  // Attributes
  readonly attribute XREnvironmentBlendMode environmentBlendMode;
};

enum XRInteractionMode {
    "screen-space",
    "world-space",
};

partial interface XRSession {
  // Attributes
  readonly attribute XRInteractionMode interactionMode;
};

partial interface XRView {
  readonly attribute boolean isFirstPersonObserver;
};
