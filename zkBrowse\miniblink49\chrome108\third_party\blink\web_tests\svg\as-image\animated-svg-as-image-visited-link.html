<!DOCTYPE html>
<script src="../../resources/testharness.js"></script>
<script src="../../resources/testharnessreport.js"></script>
<script>
async_test(function(asyncHandle) {
  var svg = new Image();
  svg.src = "resources/animated-visited-link.svg";
  svg.onload = function() {
    var canvas = document.createElement('canvas');
    var context = canvas.getContext("2d");
    context.drawImage(svg, 0, 0);
    var fillData = context.getImageData(10, 10, 1, 1).data;
    var strokeData = context.getImageData(0, 0, 1, 1).data;
    asyncHandle.step(function() {
      assert_equals(fillData[0], 0, 'red fill');
      assert_equals(fillData[1], 192, 'green fill');
      assert_equals(fillData[2], 0, 'blue fill');
      assert_equals(strokeData[0], 0, 'red stroke');
      assert_equals(strokeData[1], 192, 'green stroke');
      assert_equals(strokeData[2], 0, 'blue stroke');
    });
    asyncHandle.done();
  };
}, 'CSS animated SVGs used as Images on a canvas must not leak visited information.');
</script>
