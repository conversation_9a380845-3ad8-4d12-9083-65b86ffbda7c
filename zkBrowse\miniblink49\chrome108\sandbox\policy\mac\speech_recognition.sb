; Copyright 2020 The Chromium Authors
; Use of this source code is governed by a BSD-style license that can be
; found in the LICENSE file.

; --- The contents of common.sb implicitly included here. ---

; Required to load the libsoda.so binary downloaded by the component
; updater.
(define soda-component-path "SODA_COMPONENT_PATH")
(allow file-read* (subpath (param soda-component-path)))

; Required to load the language pack files used by the Speech On-Device
; API (SODA).
(define soda-language-pack-path "SODA_LANGUAGE_PACK_PATH")
(allow file-read* (subpath (param soda-language-pack-path)))