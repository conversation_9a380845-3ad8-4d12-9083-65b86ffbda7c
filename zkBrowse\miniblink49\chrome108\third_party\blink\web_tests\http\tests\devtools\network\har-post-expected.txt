Verifies that HAR exports have correct POST data

request: {
  "url": "http://127.0.0.1:8000/devtools/network/resources/echo-payload.php",
  "bodySize": 20,
  "postData": {
    "mimeType": "text/plain;charset=UTF-8",
    "text": "1 byte utf-8 char: a"
  }
}

request: {
  "url": "http://127.0.0.1:8000/devtools/network/resources/echo-payload.php",
  "bodySize": 21,
  "postData": {
    "mimeType": "text/plain;charset=UTF-8",
    "text": "2 byte utf-8 char: ž"
  }
}

request: {
  "url": "http://127.0.0.1:8000/devtools/network/resources/echo-payload.php",
  "bodySize": 22,
  "postData": {
    "mimeType": "text/plain;charset=UTF-8",
    "text": "3 byte utf-8 char: ツ"
  }
}

request: {
  "url": "http://127.0.0.1:8000/devtools/network/resources/echo-payload.php",
  "bodySize": 23,
  "postData": {
    "mimeType": "text/plain;charset=UTF-8",
    "text": "4 byte utf-8 char: 🔥"
  }
}

request: {
  "url": "http://127.0.0.1:8000/devtools/network/resources/echo-payload.php",
  "bodySize": 2,
  "postData": {
    "mimeType": "",
    "text": "Cr"
  }
}

