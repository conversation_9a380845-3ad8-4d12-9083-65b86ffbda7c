<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 20010904//EN" 
"http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">

<!--

   Copyright 2004  The Apache Software Foundation 

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

-->
<!-- ====================================================================== -->
<!-- Tests "proper" handling of xml:space                                   -->
<!--                                                                        -->
<!-- <AUTHOR>                                             -->
<!-- @version $Id: xmlSpace.svg,v 1.1 2005/01/03 10:48:05 deweese Exp $    -->
<!-- ====================================================================== -->

<?xml-stylesheet type="text/css" href="../resources/test.css" ?>

<svg width="450" height="500" viewBox="0 0 450 500"
     xmlns="http://www.w3.org/2000/svg" 
     xmlns:xlink="http://www.w3.org/1999/xlink" 
     xmlns:foo="http://graphics.apache.org/batik/foo">

    <text class="title" x="50%" y="40">Test of xml:space handling</text>

      <font horiz-adv-x="600" id="stroke">
         <font-face
             font-family="BlockSpace"
             font-weight="normal"
             units-per-em="1000"
             ascent="1000"
             descent="250"
             alphabetic="0"/>

         <missing-glyph horiz-adv-x="600" d="M0 0 V800 H500 V0 z"/>

         <glyph unicode=" " glyph-name=" " horiz-adv-x="600" 
                d="M20 0 V800 H520 V0 z M 50 30 H490 V770 H50 z"/>
      </font>
      <style type="text/css"><![CDATA[
        .cap { font-size: 8px; font-family: monospace;
               stroke: none; stroke-width: 0; 
                   fill: black } ]]>
      </style>

      <g font-size="20" font-family="BlockSpace">
         <text x="10" y="75">  X  X  </text>
            <text class="cap" x="120" y="75">no xml:space attr</text>

         <text x="10" y="100" xml:space="default">  X  X  </text>
            <text class="cap" x="120" y="100">xml:space="default"</text>

         <text x="10" y="150" xml:space="preserve">  X  X  <tspan fill="red" xml:space="default"/></text>
            <text class="cap" x="120" y="140">Empty tspan at end
                  <tspan x="120" dy="1.2em">xml:space="default"</tspan></text>

         <text x="10" y="175" xml:space="preserve">  X  X<tspan fill="red" xml:space="default">  </tspan></text>
            <text class="cap" x="120" y="165">tspan end 2spc
                  <tspan x="120" dy="1.2em">xml:space="default"</tspan></text>

         <text x="10" y="200" xml:space="preserve">  X  X <tspan fill="red" xml:space="default"> </tspan></text>
            <text class="cap" x="120" y="190">tspan end
                  <tspan x="120" dy="1.2em">xml:space="default"</tspan></text>

         <text x="10" y="225" xml:space="preserve"><tspan fill="red" xml:space="default"> </tspan> X  X  </text>
            <text class="cap" x="120" y="215">tspan start
                  <tspan x="120" dy="1.2em">xml:space="default"</tspan></text>

         <text x="10" y="250" xml:space="preserve"> <tspan fill="red" xml:space="default"> </tspan>X  X  </text>
            <text class="cap" x="120" y="240">tspan near start
                  <tspan x="120" dy="1.2em">xml:space="default"</tspan></text>

         <text x="10" y="275" xml:space="preserve">  X<tspan fill="red" xml:space="default"> </tspan> X  </text>
            <text class="cap" x="120" y="265">tspan middle front 
                  <tspan x="120" dy="1.2em">xml:space="default"</tspan></text>

         <text x="10" y="300" xml:space="preserve">  X <tspan fill="red" xml:space="default"> </tspan>X  </text>
            <text class="cap" x="120" y="290">tspan middle end
                  <tspan x="120" dy="1.2em">xml:space="default"</tspan></text>

         <text x="10" y="325" xml:space="preserve"> <foo:xxx/> X  X  </text>
            <text class="cap" x="120" y="315">unknown element 
                  <tspan x="120" dy="1.2em">start</tspan></text>

         <text x="10" y="350" xml:space="preserve">  X <foo:xxx/> X  </text>
            <text class="cap" x="120" y="340">unknown element 
                  <tspan x="120" dy="1.2em">middle</tspan></text>

         <text x="10" y="375" xml:space="preserve">  X  X <foo:xxx/> </text>
            <text class="cap" x="120" y="365">unknown element 
                  <tspan x="120" dy="1.2em">end</tspan></text>

         <!--      -->


         <text x="235" y="100" xml:space="preserve">  X  X  </text>
            <text class="cap" x="345" y="100">xml:space="preserve"</text>

         <text x="235" y="150" xml:space="default">  X  X  <tspan fill="red" xml:space="preserve"/></text>
            <text class="cap" x="345" y="140">Empty tspan at end
                  <tspan x="345" dy="1.2em">xml:space="preserve"</tspan></text>

         <text x="235" y="175" xml:space="default">  X  X<tspan fill="red" xml:space="preserve">  </tspan></text>
            <text class="cap" x="345" y="165">tspan end 2spc
                  <tspan x="345" dy="1.2em">xml:space="preserve"</tspan></text>

         <text x="235" y="200" xml:space="default">  X  X <tspan fill="red" xml:space="preserve"> </tspan></text>
            <text class="cap" x="345" y="190">tspan end
                  <tspan x="345" dy="1.2em">xml:space="preserve"</tspan></text>

         <text x="235" y="225" xml:space="default"><tspan fill="red" xml:space="preserve"> </tspan> X  X  </text>
            <text class="cap" x="345" y="215">tspan start
                  <tspan x="345" dy="1.2em">xml:space="preserve"</tspan></text>

         <text x="235" y="250" xml:space="default"> <tspan fill="red" xml:space="preserve"> </tspan>X  X  </text>
            <text class="cap" x="345" y="240">tspan near start
                  <tspan x="345" dy="1.2em">xml:space="preserve"</tspan></text>

         <text x="235" y="275" xml:space="default">  X<tspan fill="red" xml:space="preserve"> </tspan> X  </text>
            <text class="cap" x="345" y="265">tspan middle front 
                  <tspan x="345" dy="1.2em">xml:space="preserve"</tspan></text>

         <text x="235" y="300" xml:space="default">  X <tspan fill="red" xml:space="preserve"> </tspan>X  </text>
            <text class="cap" x="345" y="290">tspan middle end
                  <tspan x="345" dy="1.2em">xml:space="preserve"</tspan></text>

         <text x="235" y="325" xml:space="default"> <foo:xxx/> X  X  </text>
            <text class="cap" x="345" y="315">unknown element 
                  <tspan x="345" dy="1.2em">start</tspan></text>

         <text x="235" y="350" xml:space="default">  X <foo:xxx/> X  </text>
            <text class="cap" x="345" y="340">unknown element 
                  <tspan x="345" dy="1.2em">middle</tspan></text>

         <text x="236" y="375" xml:space="default">  X  X <foo:xxx/> </text>
            <text class="cap" x="345" y="365">unknown element 
                  <tspan x="345" dy="1.2em">end</tspan></text>

      </g>
</svg>
