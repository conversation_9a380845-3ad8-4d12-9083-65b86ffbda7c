<HEAD>

<STYLE type="text/css">

.wholeSearch
{
    float: left;
    background-color: lightblue;
    padding: 5px;
    margin: 5px;
}

.triangle
{
    cursor: hand;
}

.searchField
{
    display: none;
}

</STYLE>

</HEAD>

<BODY>

<SCRIPT language="JavaScript">
function toggleStatsSearch(triangleSpan)
{
    var arrow = triangleSpan.innerHTML;
    if (arrow == "\u25B8") {
        triangleSpan.innerHTML = "\u25BE";
        triangleSpan.nextSibling.nextSibling.style.display = 'block';
    } else {
        triangleSpan.innerHTML = "\u25B8";
        triangleSpan.nextSibling.nextSibling.style.display = 'none';
    }
}

</SCRIPT>

<DIV class="wholeSearch">
    <SPAN class="triangle" id="tri" onclick="toggleStatsSearch(this)">&#x25b8;</SPAN> Project
    <DIV class="searchField" name="divProject"><P></P>Call me <PERSON><PERSON><PERSON>.</DIV>
</DIV>
    <script>
        if (window.eventSender) {
            eventSender.mouseMoveTo(23,26);
            eventSender.mouseDown();
            eventSender.mouseUp();
            eventSender.mouseDown();
            eventSender.mouseUp();
            eventSender.mouseDown();
            eventSender.mouseUp();
            eventSender.mouseDown();
            eventSender.mouseUp();
       }
    </script>

</BODY>
