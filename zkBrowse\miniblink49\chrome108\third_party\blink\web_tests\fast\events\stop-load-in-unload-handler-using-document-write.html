<pre id="console"></pre>
<script>
    if (window.testRunner) {
        testRunner.dumpAsText();
        testRunner.waitUntilDone();
    }
    function log(msg)
    {
        document.getElementById('console').appendChild(document.createTextNode(msg + "\n"));
    }

    function pass()
    {
        log("PASS");
        if (window.testRunner)
            testRunner.notifyDone();
    }

    function fail()
    {
        log("FAIL");
        if (window.testRunner)
            testRunner.notifyDone();
    }
</script>
<iframe src="resources/subframe-stop-load-in-unload-handler-using-document-write.html"></iframe>
