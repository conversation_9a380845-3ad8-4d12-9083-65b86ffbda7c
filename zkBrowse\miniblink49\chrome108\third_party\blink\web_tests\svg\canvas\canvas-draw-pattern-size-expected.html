<!DOCTYPE html>
<html>
<head>
<script>
function runTest() {
  var context = document.getElementsByTagName('canvas')[0].getContext('2d');
  var forceLayout = document.body.offsetWidth;

  var domImage = document.getElementsByTagName('img')[0];
  context.drawImage(domImage, 10, 10, 80, 80);

  var newImage = new Image();
  newImage.src = domImage.src;
  context.fillStyle = context.createPattern(newImage, 'repeat');
  context.fillRect(10, 10, 180, 80);

  domImage.parentNode.removeChild(domImage);
}
</script>
</head>
<body onload='runTest()'>
Test for crbug.com/227481: This test passes if there is a green rectangle with a two blue circles.<br/>
<canvas width="200px" height="100px"></canvas>
<img style="width: 50px;" src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='100' height='100'><rect width='100' height='100' fill='green' /><circle cx='30' cy='30' r='15' fill='blue'/></svg>" >
</body>
</html>
