<head>
<script>
function log(t) {
    document.getElementById('console').innerHTML += t +'<br>';
}

if (window.testRunner)
    testRunner.dumpAsText();

</script>
<body>
<p>Test that right-clicking on a text area properly focuses it.</p>
<textarea></textarea>
<div id=console></div>
<script>
if (window.eventSender) {
    var elt = document.getElementsByTagName('textarea')[0];
    eventSender.mouseMoveTo(elt.offsetLeft + 5, elt.offsetTop + 5);
    eventSender.contextClick();
    eventSender.keyDown("z");
    log(document.getElementsByTagName("textarea")[0].value == "z" ? "PASS" : "FAIL: No text in textarea");
} else {
    log("Please try right-clicking on the text area below, dismissing the context menu that appears, and typing some text. The caret should be visible, and typing should work.");
}
</script>
