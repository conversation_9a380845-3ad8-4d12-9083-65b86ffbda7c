<html>
<head>
    <script>
    function init() {
        if (!window.testRunner)
            return;

        testRunner.waitUntilDone();
        testRunner.setBackingScaleFactor(2, function () { testRunner.notifyDone(); });
    }
    window.onload = init;
    </script>
<style>
.bg1 {
  background: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='128' height='128'><circle fill='black' cx='64' cy='64' r='64' /></svg>");
}
.bg2 {
  background: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 128 128'><circle fill='black' cx='64' cy='64' r='64' /></svg>");
}
</style>
</head>
<p>This test passes if all circles have sharp edges when deviceScaleFactor=2.</p>
<img style="width: 128px;" src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='128' height='128'><circle fill='black' cx='64' cy='64' r='64' /></svg>">
<img style="width: 128px;" src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 128 128'><circle fill='black' cx='64' cy='64' r='64' /></svg>">
<div class="bg1" style="width: 128px; height: 128px; border-radius: 10px; display: inline-block;"></div>
<div class="bg2" style="width: 128px; height: 128px; border-radius: 10px; display: inline-block;"></div>
</html>
