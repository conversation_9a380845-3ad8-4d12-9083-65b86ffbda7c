<!DOCTYPE html>
<html>
<head>
</head>
<body>
<p>This test verifies that updating the slider for an input element with type=range fires a change event.</p>

<!-- See bug 84674 -->
<input id="slider" type="range" min="0" max="3" value="0"></input>
<pre id="console"></pre>
<script src="../../resources/js-test.js"></script>
<script>
var receivedChangeEvent = false;
var slider;

function onChange(e)
{
    testPassed('Change event fired.');
    receivedChangeEvent = true;
    shouldBeEqualToString("slider.value", "0");
}

window.onload = function()
{
    if (!window.testRunner)
        return;

    slider = document.getElementById("slider");
    slider.addEventListener("change", onChange);

    // Programmatically changing an input value should not fire a change event.
    slider.value = 1;

    // Changing back to original value should fire a change event.
    var x = slider.offsetLeft + 1;
    var y = slider.offsetTop + slider.clientHeight / 2;

    eventSender.mouseMoveTo(x, y);
    eventSender.mouseDown();
    eventSender.mouseUp();

    if (!receivedChangeEvent)
        testFailed('Change event not fired.');
}
</script>
</body>
</html>
