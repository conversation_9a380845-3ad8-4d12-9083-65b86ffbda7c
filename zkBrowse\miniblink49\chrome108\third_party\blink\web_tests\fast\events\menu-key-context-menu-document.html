<!DOCTYPE html>
<body>
<script src="../../resources/js-test.js"></script>
<script src="../dom/resources/event-sender-util.js"></script>
<style>
#anchor {
    background-color: orange;
    height: 100px;
    margin: 10px;
    width: 100px;
}
</style>
<div id="anchor" tabindex="0">Target</div>
<div id="console"></div>
<script>
description('This test checks if contextmenu event target is correct when handled at the document level. To test' +
            ' manually, first right-click on the "Target" text in the orange box, then press the menu key. Both' +
            ' interactions should result in the message: "PASS anchor is anchor".');

var count = 0;
var anchorCount = 0;
function handleContextMenu(e) {
    shouldBe(e.target.id, 'anchor');
    e.preventDefault();
}
document.addEventListener('contextmenu', handleContextMenu, true);

if (!window.eventSender || !window.testRunner) {
    testFailed('This test needs to run in a test environment.');
} else {
    var anchor = document.getElementById("anchor");
    mouseMoveToElem(anchor);
    eventSender.contextClick();
    // Esc key to hide context menu
    eventSender.keyDown("Escape");
    anchor.focus();
    eventSender.keyDown("ContextMenu");
}
</script>
</body>
