<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 4.1.2 Horizontal Formatting</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
body {overflow: hidden;}
.ruler {padding: 0px; margin: 0px; border-width: 0px;}
P#ruled {background-color: aqua; width: 400px;
     border-style: solid; border-color: silver;
     border-top-width: 0px; border-bottom-width: 0px;
     border-left-width: 40px; border-right-width: 40px;
     padding-left: 40px; padding-right: 40px;
     margin-top: 0px; margin-bottom: 0px; margin-left: 40px; margin-right: 40px;}

P.one {margin-left: 10px;}
DIV.two {margin-left: 10px;}
P.three {margin-left: 0; width: 50%; margin-right: auto;
         background-color: gray;}
P.four {margin-left: auto; width: 50%; margin-right: auto;
        background-color: gray;}
P.five {margin-left: auto; width: 50%; margin-right: 0;
        background-color: gray;}
P.six {margin-left: auto; width: auto; margin-right: 0;
       background-color: gray; }
P.seven {margin-left: 0; width: auto; margin-right: auto;
         background-color: gray;}
P.eight {margin-left: auto; width: auto; margin-right: auto;
         background-color: gray;}
P.nine {padding-left: auto; padding-right: auto; margin-left: 0; margin-right: 0;
        width: 50%; background-color: gray;}
P.ten {margin-left: auto; width: 100%; margin-right: auto;
       background-color: gray;}
</STYLE>

</HEAD>

<BODY><P CLASS="one">
This paragraph should be indented ten pixels.
</P>

<DIV CLASS="two">
<P CLASS="one">
This paragraph should be indented twenty pixels, since horizontal margins do not collapse.
</P>
</DIV>

<P class="three">This element has a width of 50%, and due to 'auto'
values applied to the right margin, the element should be left
justified within its parent.  The gray rectangle should therefore
appear on the left edge of the viewport (e.g. the browser window). The
text inside the gray rectangle should not be centered.</P>

<P class="four">This element has a width of 50%, and due to 'auto'
values applied to the left and right margins, the element should be
centered within its parent.  The gray rectangle should therefore appear
in the middle of the viewport (e.g. the browser window). The text
inside the gray rectangle should not be centered.</P>

<P class="five">This element has a width of 50%, and due to 'auto'
values applied to the left margin, the element should be right
justified within its parent.  The gray rectangle should therefore
appear on the right edge of the viewport (e.g. the browser window). The
text inside the gray rectangle should not be centered.</P>

<P CLASS="six">
Since the width is "auto," the margins that are set to "auto" become
zero and this paragraph should have width 100% and the text should be
left justified.
</P>

<P CLASS="seven">
Since the width is "auto," the margins that are set to "auto" become
zero and this paragraph should have width 100% and the text should be
left justified.
</P>

<P CLASS="eight">
Since the width is "auto," the margins that are set to "auto" become
zero and this paragraph should have width 100% and the text should be
left justified.
</P>

<P CLASS="nine">
Since auto is an invalid value for padding, the right-margin of this
paragraph should be reset to <CODE>auto</CODE> and thus be expanded to 50% and it should only occupy the left
half of the viewport.
</P>

<P CLASS="ten">
Because this paragraph has width 100%, the auto margins become zero, so
it should not be centered.
</P>
</TD></TR></TABLE></BODY>
</HTML>
