<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.5.13 border-bottom-width</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
.zero {background-color: silver; border-bottom-width: 0;}
.one {border-bottom-width: 50px; border-style: solid;}
.two {border-bottom-width: thick; border-style: solid;}
.three {border-bottom-width: medium; border-style: solid;}
.four {border-bottom-width: thin; border-style: solid;}
.five {border-bottom-width: 25px;}</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.zero {background-color: silver; border-bottom-width: 0;}
.one {border-bottom-width: 50px; border-style: solid;}
.two {border-bottom-width: thick; border-style: solid;}
.three {border-bottom-width: medium; border-style: solid;}
.four {border-bottom-width: thin; border-style: solid;}
.five {border-bottom-width: 25px;}
</PRE>
<HR>
<P>
(These will only work if <CODE>border-style</CODE> is supported.)
</P>
<P class="zero">
This element has a class of zero.
</P>
<P class="one">
This element should have a bottom border width of 50 pixels.
</P>
<P class="two">
This element should have a thick bottom border width.
</P>
<P class="three">
This element should have a medium bottom border width.
</P>
<P class="four">
This element should have a thin bottom border width.
</P>
<P class="five">
This element should have no border and no extra "padding" on its bottom side, as no <CODE>border-style</CODE> was set.
</P>
<P class="zero">
This element has a class of zero.
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P>
(These will only work if <CODE>border-style</CODE> is supported.)
</P>
<P class="zero">
This element has a class of zero.
</P>
<P class="one">
This element should have a bottom border width of 50 pixels.
</P>
<P class="two">
This element should have a thick bottom border width.
</P>
<P class="three">
This element should have a medium bottom border width.
</P>
<P class="four">
This element should have a thin bottom border width.
</P>
<P class="five">
This element should have no border and no extra "padding" on its bottom side, as no <CODE>border-style</CODE> was set.
</P>
<P class="zero">
This element has a class of zero.
</P>
</TD></TR></TABLE></BODY>
</HTML>
