<!DOCTYPE html>
<html>
  <p>Test passes if loaded into Chromium with <PERSON><PERSON> enabled and does not crash.</p>
  <img id="image" src='resources/animated-href-on-use.svg'></img>
  <script type="text/javascript">
    if (window.testRunner) {
      testRunner.waitUntilDone();
      testRunner.dumpAsText();
    }

    window.onload = function() {
      internals.advanceImageAnimation(image);
      window.requestAnimationFrame(function() {
        testRunner.notifyDone()
      });
    }
  </script>
</html>

