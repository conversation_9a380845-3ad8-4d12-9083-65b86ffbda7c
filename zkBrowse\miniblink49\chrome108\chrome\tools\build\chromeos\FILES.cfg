# -*- python -*-
# ex: set syntax=python:

# Copyright 2012 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# NOTE: Configurations for 'dev' builders will not be used from here anymore.
# The new configs can be found in the json files within infra/archive_config.
# This config is used only for official builds for now, however this file will
# become obsolete soon. crbug/1260176

# This is a buildbot configuration file containing a tagged list of files
# processed by the stage/archive scripts. The known tags are:
#
# filename: Name of the file in the build output directory.
# arch:     List of CPU architectures for which this file should be processed.
#           Leave this unspecified to prcoess for all architectures.
#           Acceptable values are 64bit, 32bit and arm.
# buildtype: List of build types for which this file should be processed.
# archive: The name of the archive file to store filename in. If not specified,
#          filename is added to the default archive (e.g. platform.zip). If
#          archive == filename, filename is archived directly, not zipped.
# direct_archive: Force a file to be archived as-is, bypassing zip creation.
#                 NOTE: This flag will not apply if more than one file has the
#                 same 'archive' name, which will create a zip of all the
#                 files instead.
# type: 'file' or 'folder'. Defaults to 'file'.
# optional: List of buildtypes for which the file might not exist, and it's not
#           considered an error.

FILES = [
  {
    'filename': 'chrome',
    'buildtype': ['official'],
  },
  {
    'filename': 'chrome-wrapper',
    'buildtype': ['official'],
  },
  {
    'filename': 'chrome_100_percent.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'chrome_200_percent.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'chrome_crashpad_handler',
    'buildtype': ['official'],
  },
  {
    'filename': 'icudtl.dat',
    'buildtype': ['official'],
  },
  {
    'filename': 'keyboard_resources.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'installer',
    'buildtype': ['official'],
  },
  {
    'filename': 'libminigbm.so',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales',
    'buildtype': ['official'],
    'type': 'folder',
  },
  {
    'filename': 'snapshot_blob.bin',
    'buildtype': ['official'],
  },
  {
    'filename': 'product_logo_48.png',
    'buildtype': ['official'],
  },
  {
    'filename': 'resources',
    'buildtype': ['official'],
    'type': 'folder',
  },
  {
    'filename': 'resources.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'xdg-mime',
    'buildtype': ['official'],
  },
  {
    'filename': 'xdg-settings',
    'buildtype': ['official'],
  },
  # CDM files
  {
    'filename': 'libclearkeycdm.so',
    'buildtype': ['official'],
  },
  {
    'filename': 'WidevineCdm',
    'buildtype': ['official'],
    'direct_archive': 1,
    'type': 'folder',
  },
  # Native Client plugin files:
  {
    'filename': 'nacl_irt_x86_32.nexe',
    'arch': ['32bit'],
    'buildtype': ['official'],
  },
  {
    'filename': 'nacl_irt_x86_64.nexe',
    'arch': ['64bit'],
    'buildtype': ['official'],
  },
  {
    'filename': 'nacl_helper',
    'buildtype': ['official'],
  },
  {
    'filename': 'nacl_helper_bootstrap',
    'buildtype': ['official'],
  },
  # MEI Preload files:
  {
    'filename': 'MEIPreload/manifest.json',
    'buildtype': ['official'],
  },
  {
    'filename': 'MEIPreload/preloaded_data.pb',
    'buildtype': ['official'],
  },
]
