Test generating an RSA key pair for RSASSA-PKCS1-v1_5 when exponent has leading zeroes.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".

Generating a key pair...
PASS keyPair.toString() is '[object Object]'
PASS keyPair.publicKey.type is 'public'
PASS keyPair.publicKey.algorithm.name is 'RSASSA-PKCS1-v1_5'
PASS keyPair.publicKey.algorithm.modulusLength is 512
PASS bytesToHexString(keyPair.publicKey.algorithm.publicExponent) is '010001'
PASS bytesToHexString(keyPair.privateKey.algorithm.publicExponent) is '010001'
PASS keyPair.publicKey.usages is ["verify"]
PASS keyPair.privateKey.type is 'private'
PASS keyPair.privateKey.algorithm.name is 'RSASSA-PKCS1-v1_5'
PASS keyPair.privateKey.algorithm.modulusLength is 512
PASS keyPair.privateKey.usages is ["sign"]
PASS successfullyParsed is true

TEST COMPLETE

