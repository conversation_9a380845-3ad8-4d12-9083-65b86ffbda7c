<!DOCTYPE html>
<html>
<head>
<script>
function runTest() {
  var context = document.getElementsByTagName('canvas')[0].getContext('2d');
  var forceLayout = document.body.offsetWidth;

  var domImage = document.getElementsByTagName('img')[0];
  context.drawImage(domImage, 10, 10, 80, 80);

  var newImage = new Image();
  newImage.src = domImage.src;
  context.drawImage(newImage, 10, 10, 80, 80);

  domImage.parentNode.removeChild(domImage);
}
</script>
</head>
<body onload='runTest()'>
Test for crbug.com/227481: This test passes if there is a green square with a blue circle in the top-left corner.<br/>
<canvas width="100px" height="100px"></canvas>
<img style="width: 30px;" src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='100' height='100'><rect width='100' height='100' fill='green' /><circle cx='30' cy='30' r='15' fill='blue'/></svg>" >
</body>
</html>
