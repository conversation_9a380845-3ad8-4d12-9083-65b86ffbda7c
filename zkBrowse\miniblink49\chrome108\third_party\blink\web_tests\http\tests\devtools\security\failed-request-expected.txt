Tests that origins with failed requests are shown correctly in the security panel origins list.

Group: Main origin
Group: Secure origins
<SPAN >
    <SPAN class=url-scheme-secure >
https
    </SPAN>
    <SPAN class=url-scheme-separator >
://
    </SPAN>
    <SPAN >
foo.test
    </SPAN>
</SPAN>
<SPAN class=url-scheme-secure >
https
</SPAN>
<SPAN class=url-scheme-separator >
://
</SPAN>
<SPAN >
foo.test
</SPAN>
Group: Unknown / canceled
<SPAN >
    <SPAN class=url-scheme-unknown >
https
    </SPAN>
    <SPAN class=url-scheme-separator >
://
    </SPAN>
    <SPAN >
does-not-resolve.test
    </SPAN>
</SPAN>
<SPAN class=url-scheme-unknown >
https
</SPAN>
<SPAN class=url-scheme-separator >
://
</SPAN>
<SPAN >
does-not-resolve.test
</SPAN>

