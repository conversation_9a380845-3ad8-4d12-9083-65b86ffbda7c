include_rules = [
  "-android_webview",
  "+android_webview/browser",
  "+android_webview/browser_jni_headers",
  "+android_webview/common",
  "+android_webview/grit",
  "+android_webview/proto",
  "+android_webview/test/android_webview_unittests_jni",
  "+cc",
  "+components/android_autofill/browser",
  "+components/autofill/content/browser",
  "+components/autofill/content/common",
  "+components/autofill/core/browser",
  "+components/autofill/core/common",
  "+components/cdm/browser",
  "+components/component_updater/android",
  "+components/component_updater/installer_policies",
  "+components/crash/content/browser",
  "+components/crash/core",
  "+components/download/public/common",
  "+components/embedder_support",
  "+components/favicon_base",
  "+components/flags_ui",
  "+components/heap_profiling",
  "+components/keyed_service/core",
  "+components/minidump_uploader",
  "+components/navigation_interception",
  "+components/optimization_guide/core/bloom_filter.h",
  "+components/page_load_metrics/browser",
  "+components/permissions",
  "+components/policy/core/browser",
  "+components/policy/core/common",
  "+components/policy/content",
  "+components/power_metrics",
  "+components/pref_registry",
  "+components/printing/browser",
  "+components/printing/common",
  "+components/profile_metrics",
  "+components/safe_browsing/android",
  "+components/safe_browsing/content/browser",
  "+components/safe_browsing/core/browser",
  "+components/safe_browsing/core/common",
  "+components/security_interstitials",
  "+components/services/heap_profiling",
  "+components/spellcheck/browser",
  "+components/spellcheck/common",
  "+components/strings",
  "+components/tracing/common",
  "+components/url_formatter",
  "+components/url_matcher",
  "+components/user_prefs",
  "+components/variations",
  "+components/visitedlink/browser",
  "+components/viz/common",
  "+components/viz/service/display",
  "+components/viz/service/display_embedder",
  "+components/viz/service/frame_sinks",
  "+components/viz/test",
  "+components/webdata/common",

  "+content/public/browser",
  "+content/public/test",

  # Explicitly disallow using SyncMessageFilter to prevent browser from
  # sending synchronous IPC messages on non-UI threads.
  "-ipc/ipc_sync_message_filter.h",

  "+media/base/android",
  "+media/mojo/buildflags.h",

  "+components/policy/policy_constants.h",
  "+components/embedder_support/android",

  "+printing",

  "+services/cert_verifier/public/mojom",

  "+services/metrics/public/cpp/ukm_source_id.h",

  "+services/network/public",
  # This is needed for the CookieManager to configure the CookieStore directly.
  "+services/network/cookie_access_delegate_impl.h",
  "+services/network/network_service.h",
  "+services/resource_coordinator/public/cpp",
  "+services/service_manager/public/cpp",

  # This is needed for AwPacResolver
  "+services/proxy_resolver",

  # network service related unit tests
  "+services/network/test",
  "+mojo/core/embedder/embedder.h",
  "+mojo/public/cpp/system",

  # Background tracing support
  "+services/tracing/public/cpp",

  "+storage/browser/quota",
  "+storage/common/quota",

  "+third_party/blink/public/common/client_hints/enabled_client_hints.h",
  "+third_party/blink/public/common/features.h",
  "+third_party/blink/public/common/page_state/page_state.h",
  "+third_party/blink/public/common/permissions/permission_utils.h",
  "+third_party/blink/public/common/storage_key/storage_key.h",
  "+third_party/blink/public/common/user_agent/user_agent_metadata.h",
  "+third_party/blink/public/mojom/loader/resource_load_info.mojom-shared.h",
  "+third_party/crashpad/crashpad/client",
  "+third_party/crashpad/crashpad/util",

  "+ui/display",
  "+ui/gfx",
  "+ui/gl",
  "+ui/touch_selection/touch_handle.h",

  # Temporary until we bundle our own favicon. See
  # AwContentBrowserClient::GetDefaultFavicon
  "!ui/resources/grit/ui_resources.h",

  # For using the same legacy IPC channel for mojo.
  "+third_party/blink/public/common/associated_interfaces",
  # Required for AwURLLoaderThrottle.
  "+third_party/blink/public/common/loader/url_loader_throttle.h",
  # For messaging between embedders and Javascript content.
  "+third_party/blink/public/common/messaging",
  # Required for MediaStreams.
  "+third_party/blink/public/common/mediastream",
  # QuotaStatusCode required by AwQuotaManagerBridge.
  "+third_party/blink/public/mojom/quota",
  "+third_party/blink/public/mojom/mediastream/media_stream.mojom-shared.h",
  "+third_party/blink/public/mojom/mediastream/media_stream.mojom.h",
  # For favicon url
  "+third_party/blink/public/mojom/favicon",
  # For find-in-page
  "+third_party/blink/public/mojom/frame",
  # Required for the Web Speech API.
  "+third_party/blink/public/mojom/speech",
  # Required by AwSettings.
  "+third_party/blink/public/common/renderer_preferences/renderer_preferences.h",
  "+third_party/blink/public/mojom/webpreferences/web_preferences.mojom.h",
  # For the content_browser overlay manifest
  "+third_party/blink/public/mojom/input",
  # For background tracing setup.
  "+third_party/metrics_proto",
]
