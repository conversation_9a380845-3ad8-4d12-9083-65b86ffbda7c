<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background-color set to rgb() using integers with blue set to the minimum minus one value, rgb(0, 0, -1)</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="<PERSON><PERSON>" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2013-04-08 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background-color" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <link rel="match" href="background-color-002-ref.xht" />

        <meta name="flags" content="image invalid" />
        <meta name="assert" content="Background-color set to rgb(0, 0, -1) truncates to a valid value." />
        <style type="text/css">
            img, #test
            {
                height: 100px;
                width: 100px;
            }
            #test
            {
                background-color: rgb(0, 0, -1);
                margin-bottom: 10px;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there are 2 squares with the <strong>same color</strong>.</p>
        <div id="test"></div>
        <div id="reference">
            <img alt="#000 color swatch" src="support/000_color.png" />
        </div>
    </body>
</html>