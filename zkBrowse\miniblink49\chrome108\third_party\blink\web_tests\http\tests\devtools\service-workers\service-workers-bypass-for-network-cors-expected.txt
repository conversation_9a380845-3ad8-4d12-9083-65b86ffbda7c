Tests "Bypass for network" checkbox works with CORS requests. crbug.com/771742

Loading an iframe.
The iframe loaded.
CORS fetch(): 1
CORS XHR: 1
CORS image: 1
Enable bypassServiceWorker
CORS fetch(): 2
CORS XHR: 2
CORS image: 2
Disable bypassServiceWorker
CORS fetch(): 3
CORS XHR: 3
CORS image: 3
Intercepted requests:
 url: http://127.0.0.1:8000/devtools/service-workers/resources/service-workers-bypass-for-network-cors-iframe.html
 mode: navigate
 url: http://localhost:8000/devtools/service-workers/resources/service-workers-bypass-for-network-cors-target.php?type=txt&fetch1
 mode: cors
 url: http://localhost:8000/devtools/service-workers/resources/service-workers-bypass-for-network-cors-target.php?type=txt&xhr1
 mode: cors
 url: http://localhost:8000/devtools/service-workers/resources/service-workers-bypass-for-network-cors-target.php?type=img&img1
 mode: cors
 url: http://localhost:8000/devtools/service-workers/resources/service-workers-bypass-for-network-cors-target.php?type=txt&fetch3
 mode: cors
 url: http://localhost:8000/devtools/service-workers/resources/service-workers-bypass-for-network-cors-target.php?type=txt&xhr3
 mode: cors
 url: http://localhost:8000/devtools/service-workers/resources/service-workers-bypass-for-network-cors-target.php?type=img&img3
 mode: cors

