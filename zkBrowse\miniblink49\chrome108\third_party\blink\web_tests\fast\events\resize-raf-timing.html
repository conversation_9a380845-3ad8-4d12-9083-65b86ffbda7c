<!DOCTYPE html>

<script src="../../resources/js-test.js"></script>

<iframe id="iframe"></iframe>

<script>
description("Test that resize events are fired at requestAnimationFrame timing");

var jsTestIsAsync = true;
var resizeEventCount = 0;
var rafScheduled = false;
var iframe = document.getElementById('iframe');

iframe.contentWindow.onresize = function() {
    ++resizeEventCount;
    if (rafScheduled)
        return;
    rafScheduled = true;
    requestAnimationFrame(function() {
        shouldBe("resizeEventCount", "1");
        finishJSTest();
    });
};

for (var i = 0; i < 5; ++i) {
  iframe.style.width = i + 'px';
  iframe.offsetTop;
}
</script>

