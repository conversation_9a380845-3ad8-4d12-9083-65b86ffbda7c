<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.5.22 border</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
.one {border: medium black solid;}
.two {border: thin maroon ridge;}
.three {border: 10px teal outset;}
.four {border: 10px olive inset;}
.five {border: 10px maroon;}
.six {border: maroon double;}
.seven {border: left red solid;}
.eight {border: 0px;}
TD {border: 2px solid green;}</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.one {border: medium black solid;}
.two {border: thin maroon ridge;}
.three {border: 10px teal outset;}
.four {border: 10px olive inset;}
.five {border: 10px maroon;}
.six {border: maroon double;}
.seven {border: left red solid;}
.eight {border: 0px;}
TD {border: 2px solid green;}
</PRE>
<HR>
<P>
Note that all table cells on this page should have a two-pixel solid green border along all four sides.  This border applies only to the cells, not the rows which contain them.
</P>

<P class="one">
This paragraph should have a medium black solid border all the way around.
</P>
<P class="two">
This paragraph should have a thin maroon ridged border all the way around.
</P>
<P class="three">
This paragraph should have a ten-pixel-wide teal outset border all the way around.
</P>
<P class="four">
This paragraph should have a ten-pixel-wide olive inset border all the way around.
</P>
<P class="five">
This paragraph should have no border around it, as the <TT>border-style</TT> was not set, and it should not be offset in any way.
</P>
<P class="six">
This paragraph should have a medium maroon double border around it, even though <CODE>border-width</CODE> was not explicitly set.
</P>
<P class="seven">
This paragraph should have no border around it, as its declaration is invalid and should be ignored.
</P>
<P>
<A NAME="top">The</A> following image is also an anchor which points to a target on this page, but it should not have a border around it: <A HREF="sec5522.htm#top"><IMG SRC="../resources/oransqr.gif" class="eight" alt="[Image]"></A>.
</P>

<TABLE cellspacing="5" border>
<TR>
<TD colspan="2">Every cell in this table should have a 2-pixel solid green border.  This is also true of the table-testing section in the second half of the test page.
</TD>
</TR>
<TR>
<TD>Cell one</TD><TD>Cell two<TABLE border><TR><TD>Nested single-cell table!</TD></TR></TABLE></TD>
</TR>
</TABLE>

<P>
This is an unstyled element.
</P>

<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P>
Note that all table cells on this page should have a two-pixel solid green border along all four sides.  This border applies only to the cells, not the rows which contain them.
</P>

<P class="one">
This paragraph should have a medium black solid border all the way around.
</P>
<P class="two">
This paragraph should have a thin maroon ridged border all the way around.
</P>
<P class="three">
This paragraph should have a ten-pixel-wide teal outset border all the way around.
</P>
<P class="four">
This paragraph should have a ten-pixel-wide olive inset border all the way around.
</P>
<P class="five">
This paragraph should have no border around it, as the <TT>border-style</TT> was not set, and it should not be offset in any way.
</P>
<P class="six">
This paragraph should have a medium maroon double border around it, even though <CODE>border-width</CODE> was not explicitly set.
</P>
<P class="seven">
This paragraph should have no border around it, as its declaration is invalid and should be ignored.
</P>
<P>
<A NAME="top">The</A> following image is also an anchor which points to a target on this page, but it should not have a border around it: <A HREF="sec5522.htm#top"><IMG SRC="../resources/oransqr.gif" class="eight" alt="[Image]"></A>.
</P>

<TABLE cellspacing="5" border>
<TR>
<TD colspan="2">Every cell in this table should have a 2-pixel solid green border.  This is also true of the table-testing section in the second half of the test page.
</TD>
</TR>
<TR>
<TD>Cell one</TD><TD>Cell two<TABLE border><TR><TD>Nested single-cell table!</TD></TR></TABLE></TD>
</TR>
</TABLE>

<P>
This is an unstyled element.
</P></TD></TR></TABLE></BODY>
</HTML>
