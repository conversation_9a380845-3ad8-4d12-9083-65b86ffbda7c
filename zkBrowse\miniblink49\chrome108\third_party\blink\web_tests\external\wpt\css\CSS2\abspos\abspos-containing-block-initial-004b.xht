<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" style="position:fixed; left:100px; top:100px; width:100px; height:100px;
             background:yellow; border:10px solid black;"><head><title>CSS Test: Test for containing block for absolutely positioned elements being initial containing block</title>
<link rel="author" title="<PERSON>" href="mailto:<EMAIL>" />
<link rel="author" title="Mozilla Corporation" href="http://mozilla.com/" />
<link rel="help" href="http://www.w3.org/TR/CSS21/visudet.html#containing-block-details" />
<link rel="match" href="abspos-containing-block-initial-004-ref.xht"/>
<meta name="assert" content="If there is no such ancestor, the containing block is the initial containing block." />
</head><body>


</body></html>