<!doctype html>
<script src="../resources/testharness.js"></script>
<script src="../resources/testharnessreport.js"></script>

<style>
#fail { @foo blah } /* should not affect the next rule */
#target { color: green } /* should not affect the next rule */
#target { font-size: 20px; }
</style>

<div id="target"></div>

<script>
test(function(){
 assert_equals(getComputedStyle(document.getElementById('target')).color, 'rgb(0, 128, 0)');
 assert_equals(getComputedStyle(document.getElementById('target')).fontSize, '20px');
}, "Test to ensure that things in a declaration block that do not end with a semicolon are handled correctly");
</script>
