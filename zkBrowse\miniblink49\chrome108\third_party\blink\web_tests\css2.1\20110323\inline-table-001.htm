<html>
    <head>
        <title>CSS Test: Inline-table</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/">
        <link rel="help" href="http://www.w3.org/TR/CSS21/tables.html#table-display">
        <meta name="flags" content="">
        <meta name="assert" content="An element with 'display: inline-table' is rendered as if it were an inline.">
        <style type="text/css">
            #table
            {
                background: orange;
                display: inline-table;
            }
            #div
            {
                background: orange;
                display: inline-block;
                height: 1in;
                width: 1in;
            }
            #div2
            {
                height: 1in;
                width: 1in;
            }
            #row
            {
                display: table-row;
            }
            #cell
            {
                display: table-cell;
                height: 1in;
                width: 1in;
            }
        </style>
    </head>
    <body>
        <p>Test passes if the "Filler Text" below is all on the same line.</p>
        <div>
            <span>Filler Text</span>
            <div id="table">
                <div id="row">
                    <div id="cell">Filler Text</div>
                </div>
            </div>
        </div>
    </body>
</html>