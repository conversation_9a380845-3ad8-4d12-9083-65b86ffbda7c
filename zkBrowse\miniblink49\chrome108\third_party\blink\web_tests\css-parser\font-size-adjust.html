<!DOCTYPE html>
<script src="../resources/testharness.js"></script>
<script src="../resources/testharnessreport.js"></script>
<script src="resources/property-parsing-test.js"></script>
<script>
// Verifies that font-size-adjust property and its value are properly parsed

assert_valid_value("fontSizeAdjust", "1.5");
assert_valid_value("fontSizeAdjust", "0.5");
assert_valid_value("fontSizeAdjust", "0");
assert_valid_value("fontSizeAdjust", "none");
assert_invalid_value("fontSizeAdjust", "-0.5");
assert_invalid_value("fontSizeAdjust", "-1.5");
</script>
