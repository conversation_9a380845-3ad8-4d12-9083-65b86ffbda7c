<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN"
"http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">

<!--

   Copyright 2001-2002  The Apache Software Foundation 

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

-->
<!-- ========================================================================= -->
<!-- Test description here                                                     -->
<!--                                                                           -->
<!-- <AUTHOR>                                      -->
<!-- @version $Id: textProperties2.svg,v 1.1 2004/11/21 21:20:23 deweese Exp $                                                             -->
<!-- ========================================================================= -->
<?xml-stylesheet type="text/css" href="../resources/test.css" ?>

<svg width="450" height="500" viewBox="0 0 450 500"
  xmlns="http://www.w3.org/2000/svg" 
  xmlns:xlink="http://www.w3.org/1999/xlink" >

<title>Text Properties</title>

    <!-- ============================================================= -->
    <!-- Test content                                                  -->
    <!-- ============================================================= -->

    <defs>

     <style type="text/css"><![CDATA[
       .Caption { font-size: 8; font-family: dialog; font-weight: normal;
                  font-style: normal; stroke: none; stroke-width: 0; 
                  fill: black } 
       text > tspan { fill: blue; }
       text > tspan > tspan { fill: purple; }]]>
     </style>
    </defs>

    <g id="testContent">
        <text class="title" x="50%" y="40" font-size="15px" >
            Text display and visibility.</text>

        <text x="50%" y="74" text-anchor="middle"
         >Visibility on text &amp; tspans</text>
        <text x="50%" y="200" text-anchor="middle"
         >Display on text &amp; tspans</text>

         <g stroke="black" stroke-width="1">
         <rect x="12.5" y="82" width="425" height="25" fill="#EEE"/>
         <rect x="12.5" y="107" width="425" height="25" fill="#CCC"/>
         <rect x="12.5" y="132" width="425" height="25" fill="#EEE"/>
         <rect x="12.5" y="157" width="425" height="25" fill="#CCC"/>
         <line x1="340" x2="340" y1="82" y2="182"/>

         <rect x="12.5" y="207" width="425" height="25" fill="#EEE"/>
         <rect x="12.5" y="232" width="425" height="25" fill="#CCC"/>
         <rect x="12.5" y="257" width="425" height="25" fill="#EEE"/>
         <rect x="12.5" y="282" width="425" height="25" fill="#CCC"/>
         <rect x="12.5" y="307" width="425" height="25" fill="#EEE"/>
         <line x1="340" x2="340" y1="207" y2="332"/>
         </g>

         <text class="Caption" x="360" y="97" >vis, vis, vis</text>
         <text class="Caption" x="360" y="122">vis, hid, vis</text>
         <text class="Caption" x="360" y="147">vis, hid, hid</text>
         <text class="Caption" x="360" y="172">hid, vis, hid</text>

         <text class="Caption" x="360" y="223">inline, inline, inline</text>
         <text class="Caption" x="360" y="248">inline, inline, none</text>
         <text class="Caption" x="360" y="273">inline, none, inline</text>
         <text class="Caption" x="360" y="298">inline, none, none</text>
         <text class="Caption" x="360" y="323">none, inline, none</text>

       <g transform="scale(1.25), translate(0, 10)">
        <text x="20" y="70">Text <tspan>tspan 1 <tspan> tspan 2</tspan> more span 1</tspan> finish text</text>

        <text x="20" y="90">Text <tspan visibility="hidden">tspan 1 <tspan visibility="visible"> tspan 2</tspan> more span 1</tspan> finish text</text>

        <text x="20" y="110">Text <tspan visibility="hidden">tspan 1 <tspan visibility="hidden"> tspan 2</tspan> more span 1</tspan> finish text</text>

        <text x="20" y="130" visibility="hidden">Text <tspan visibility="visible">tspan 1 <tspan visibility="hidden"> tspan 2</tspan> more span 1</tspan> finish text</text>


        <text x="20" y="170">Text <tspan>tspan 1 <tspan>tspan 2 </tspan>more span 1</tspan> finish text</text>
        <text x="20" y="190">Text <tspan display="inline">tspan 1 <tspan display="none"> tspan 2</tspan> more span 1</tspan> finish text</text>
        <text x="20" y="210">Text <tspan display="none">tspan 1 <tspan display="inline"> tspan 2</tspan> more span 1</tspan> finish text</text>
        <text x="20" y="230">Text <tspan display="none">tspan 1 <tspan display="none"> tspan 2</tspan> more span 1</tspan> finish text</text>
        <text x="20" y="250" display="none">Text <tspan display="inline">tspan 1 <tspan display="none"> tspan 2</tspan> more span 1</tspan> finish text</text>
    </g>
    </g>

    <!-- ============================================================= -->
    <!-- Batik sample mark                                             -->
    <!-- ============================================================= -->
    <use xlink:href="../resources/batikLogo.svg#Batik_Tag_Box" />
    
</svg>
