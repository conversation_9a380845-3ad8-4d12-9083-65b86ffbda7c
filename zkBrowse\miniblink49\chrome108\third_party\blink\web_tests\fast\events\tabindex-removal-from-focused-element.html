<!DOCTYPE html>
<body>
<div tabindex="0" onclick="this.disabled = true;" id="target">div</div>
<script src="../../resources/js-test.js"></script>
<script>
jsTestIsAsync = true;
var target = document.getElementById('target');
target.focus();
shouldBe('document.activeElement', 'target');
debug('Remove tabindex.');
window.onload = function() {
    target.addEventListener('blur', function() {
        testPassed('blur event was disaptched.');
        target.remove();
        finishJSTest();
    }, false);
    // Need to wait until CheckFocusedElementTask is unqueued.
    setTimeout(function() {
        target.removeAttribute('tabindex');
    }, 10);
    setTimeout(function() {
        testFailed('No blur event');
        finishJSTest();
    }, 1000);
};
</script>
</body>
