<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background-position applied to elements with 'display' set to 'inline'</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background-position" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <meta name="flags" content="image" />
        <meta name="assert" content="The 'background-position' property applies to elements with 'display' set to 'inline'." />
        <style type="text/css">
            div
            {
                background-image: url('support/blue15x15.png');
                background-position: bottom right;
                background-repeat: no-repeat;
                border: solid black;
                display: inline;
                font: 96px/1em serif;
            }
        </style>
    </head>
    <body>
        <p>Test passes if the blue box is in the lower-right corner of the black box.</p>
        <div>&nbsp;&nbsp;&nbsp;</div>
    </body>
</html>