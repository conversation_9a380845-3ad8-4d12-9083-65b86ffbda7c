<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright 2021 The Chromium Authors
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file.
-->

<resources xmlns:tools="http://schemas.android.com/tools">

    <style name="Base.Theme.Chromium.TabbedMode" parent="Theme.Chromium.WithWindowAnimation" />
    <style name="Theme.Chromium.TabbedMode" parent="Base.Theme.Chromium.TabbedMode" />

    <!-- Omnibox -->
    <style name="TextAppearance.OmniboxVerboseStatus" parent="@android:style/TextAppearance.Medium">
        <item name="android:textSize">@dimen/location_bar_url_text_size</item>
    </style>

    <!-- Omnibox suggestions -->
    <style name="TextAppearance.OmniboxAnswerDescriptionNegativeSmall">
        <item name="android:textSize">@dimen/text_size_small</item>
        <item name="android:textColor">@color/answers_description_text_negative</item>
    </style>

    <style name="TextAppearance.OmniboxAnswerDescriptionPositiveSmall">
        <item name="android:textSize">@dimen/text_size_small</item>
        <item name="android:textColor">@color/answers_description_text_positive</item>
    </style>

    <style name="OmniboxIcon" parent="LocationBarButton">
        <item name="android:layout_width">@dimen/location_bar_status_icon_width</item>
        <item name="android:layout_height">match_parent</item>
    </style>

    <style name="OmniboxPedalChipThemeOverlay">
        <item name="chipStyle">@style/OmniboxPedalChip</item>
    </style>

    <style name="OmniboxPedalChip" parent="SuggestionChip">
        <item name="iconWidth">@dimen/omnibox_pedal_suggestion_icon_size</item>
        <item name="iconHeight">@dimen/omnibox_pedal_suggestion_icon_size</item>
        <item name="allowMultipleLines">true</item>
        <item name="multiLineVerticalPadding">@dimen/omnibox_pedal_suggestion_text_vertical_padding</item>
        <item name="textAlignStart">true</item>
    </style>

</resources>
