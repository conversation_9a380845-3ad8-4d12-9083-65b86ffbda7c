<!doctype html>
<html>
<head>
<script>
if (window.testRunner)
  testRunner.dumpAsText();

function windowHandler() {
  var result = document.getElementById('result');
  result.innerHTML='FAIL: window.onload event handler did run.';
}

function bodyHandler() {
  var result = document.getElementById('result');
  if (result.textContent.startsWith('PASS:'))
    result.textContent += ", but the document body onload did.";
}

window.onload = windowHandler;
</script>
</head>
<body onload="bodyHandler()">
This tests that a <body> onload handler clears and replaces the attribute listener on the window.
You should see "PASS" below.

<div id='result'>PASS: window.onload event handler did not run<div>
</body>
</html>
