<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.6.4 list-style-image</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
.one {list-style-image: url(../resources/oransqr.gif);}
.two {list-style-image: none;}
</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.one {list-style-image: url(../resources/oransqr.gif);}
.two {list-style-image: none;}

</PRE>
<HR>
<UL class="one">
<LI>This list...
<LI>...should feature...
<LI>...images for each item.
</UL>

<UL class="two">
<LI>This list...
<LI>...should feature...
<LI>...standard list markers for each item.
</UL>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><UL class="one">
<LI>This list...
<LI>...should feature...
<LI>...images for each item.
</UL>

<UL class="two">
<LI>This list...
<LI>...should feature...
<LI>...standard list markers for each item.
</UL>
</TD></TR></TABLE>
<SCRIPT>
// Force layout to ensure image loads block onload.
document.body.offsetTop;
</SCRIPT>
</BODY>
</HTML>
