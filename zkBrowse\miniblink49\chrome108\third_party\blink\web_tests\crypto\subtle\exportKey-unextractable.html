<!DOCTYPE html>
<html>
<head>
<script src="../../resources/js-test.js"></script>
<script src="resources/common.js"></script>
<script src="resources/keys.js"></script>
</head>
<body>
<p id="description"></p>
<div id="console"></div>

<script>
description("Tests that an unextractable key cannot be exported.");

jsTestIsAsync = true;

// FIXME: Should also test unextractable keys created from generateKey().

function importUnextractableAesKey()
{
    var keyData = new Uint8Array(16);
    var usages = ['encrypt'];
    var extractable = false;
    var algorithm = {name: 'aes-cbc'};

    return crypto.subtle.importKey('raw', keyData, algorithm, extractable, usages);
}

function importUnextractableHmacKey()
{
    var keyData = new Uint8Array(16);
    var usages = ['sign'];
    var extractable = false;
    var algorithm = {name: 'HMAC', hash: {name: 'sha-1'}};

    return crypto.subtle.importKey('raw', keyData, algorithm, extractable, usages);
}

function importUnextractableRsaPrivateKey()
{
    var keyData = hexStringToUint8Array(kKeyData.rsa1.pkcs8);
    var usages = ['sign'];
    var extractable = false;
    var algorithm = {name: 'RSASSA-PKCS1-v1_5', hash: {name: "sha-1"}};

    return crypto.subtle.importKey('pkcs8', keyData, algorithm, extractable, usages);
}

importUnextractableAesKey().then(function(key) {
    return crypto.subtle.exportKey('raw', key);
}).then(failAndFinishJSTest, function(result) {
    logError(result);

    return importUnextractableHmacKey();
}).then(function(key) {
    return crypto.subtle.exportKey('raw', key);
}).then(failAndFinishJSTest, function(result) {
    logError(result);

    return importUnextractableRsaPrivateKey();
}).then(function(key) {
    return crypto.subtle.exportKey('pkcs8', key);
}).then(failAndFinishJSTest, function(result) {
    logError(result);
}).then(finishJSTest, failAndFinishJSTest);

</script>

</body>
</html>
