#!/usr/bin/env python
# Copyright 2021 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.
"""sha256_file.py takes one or more files, computes a SHA-256 hash over it,
and writes a .h/.cc file pair containing variables with the digest bytes.

Usage:
    sha256_file.py path/to/hashes file1.txt file2.pak

Which will create path/to/hashes.h and path/to/hashes.cc with two variable
declarations, one for each of the specified input file's hash.
"""

import hashlib
import os.path
import sys


def main(argv):
    if len(argv) < 3:
        print('Usage: {} output_path_prefix file1.pak...'.format(argv[0]),
              file=sys.stderr)
        return 1

    output_path_prefix = argv[1]

    h_guard = output_path_prefix.upper().replace('/', '_') + '_H_'
    h_contents = '#ifndef {guard}\n#define {guard}\n\n'.format(guard=h_guard)
    cc_contents = '#include "{}.h"\n\n'.format(
        os.path.basename(output_path_prefix))
    for (name, value) in _hash_files(argv[2:]):
        name = 'kSha256_' + os.path.basename(name).replace('.', '_')
        h_contents += 'extern const std::array<uint8_t, 32> {};\n\n'.format(
            name)
        cc_contents += 'const std::array<uint8_t, 32> {} = {{'.format(name)
        cc_contents += ', '.join(map(hex, value))
        cc_contents += '};\n\n'
    h_contents += '#endif  // {}'.format(h_guard)

    with open(output_path_prefix + '.h', 'w') as f:
        f.write(FILE_TEMPLATE.format(contents=h_contents))

    with open(output_path_prefix + '.cc', 'w') as f:
        f.write(FILE_TEMPLATE.format(contents=cc_contents))


def _hash_files(files):
    for path in files:
        with open(path, 'rb') as f:
            yield path, _hash_file_contents(f)


def _hash_file_contents(f):
    sha2 = hashlib.sha256()
    while True:
        data = f.read(4096)
        if not data:
            break
        sha2.update(data)
    return sha2.digest()


FILE_TEMPLATE = """// Generated by chrome/tools/build/sha256_file.py
// !! DO NOT EDIT !!

#include <stdint.h>

#include <array>

{contents}
"""

if __name__ == '__main__':
    sys.exit(main(sys.argv))
