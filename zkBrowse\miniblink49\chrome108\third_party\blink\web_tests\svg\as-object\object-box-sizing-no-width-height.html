<!DOCTYPE html>
<html>
<head>
    <style>
         object {
            border: 2px dashed maroon;
            padding: 3px;
        }

        .object1 {
            height: auto;
            width: auto;
        }

        .object2 {
            height: 220px;
            width: 220px;
        }

        .object3 {
            box-sizing: border-box;
            -moz-box-sizing: border-box;
            height: auto;
            width: auto;
        }

        .object4 {
            box-sizing: border-box;
            -moz-box-sizing: border-box;
            height: 220px;
            width: 220px;
        }
    </style>
</head>
<body>
    <!-- All objects in the first row need to look identical -->
    <object class="object1" data="../as-image/resources/circle-default-default.svg"></object>
    <object class="object2" data="../as-image/resources/circle-default-default.svg"></object>
    <object class="object3" data="../as-image/resources/circle-default-default.svg"></object>
    <br/>

    <!-- Part of this object is clipped, because of the different box sizing strategy, matches Opera/FF -->
    <object class="object4" data="../as-image/resources/circle-default-default.svg"></object>
</body>
</html>
