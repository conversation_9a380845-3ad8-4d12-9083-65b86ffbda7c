<svg xmlns="http://www.w3.org/2000/svg">
<defs>
    <clipPath id="clip0" clipPathUnits="userSpaceOnUse">
        <circle cx="100" cy="100" r="50"/>
    </clipPath>

    <clipPath id="clip1" clipPathUnits="objectBoundingBox">
        <circle cx="0" cy="0" r="0.5" />
        <circle cx="0" cy="1" r="0.5" />
    </clipPath>

    <clipPath id="clip2" clipPathUnits="userSpaceOnUse">
        <circle cx="100" cy="0" r="50" transform="scale(0.01 0.01)"/>
        <rect x="0" y="0" width="100" height="100" transform="scale(0.01 0.01)" clip-path="url(#clip0)"/>
    </clipPath>

    <clipPath id="clip" clipPathUnits="objectBoundingBox">
        <rect x="0" y="0" width="1" height="1" clip-path="url(#clip1)"/>
        <rect x="0" y="0" width="1" height="1" clip-path="url(#clip2)"/>
    </clipPath>

    <mask id="mask1" x="0" y="0" width="1" height="1" maskContentUnits="objectBoundingBox">
        <rect x="0" y="0" width="1" height="1" fill="white"/>
        <rect x="0" y="0" width="1" height="1" fill="black" clip-path="url(#clip)" />
    </mask>
</defs>

<text x="125" y="225" text-anchor="middle">Both shapes should look identical</text>

<circle cx="50" cy="50" r="50" fill="black" mask="url(#mask1)"/>
<circle cx="150" cy="150" r="50" fill="black" mask="url(#mask1)"/>

</svg>
