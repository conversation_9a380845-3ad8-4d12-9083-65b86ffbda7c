<html>
<body>
<script src="../../resources/js-test.js"></script>
<script>
description("This test checks that the page visibility event is fired when frame is unloaded.");

var jsTestIsAsync = true;
var frameDocs = [];
var docsLoaded = 0;
var numFrames = 3;

function startTest() {
    if (++docsLoaded < numFrames)
        return;

    debug("Loaded all frames.");

    frameDocs.push(window[0].document);
    frameDocs.push(window[0][0].document);
    frameDocs.push(window[0][1].document);

    for (var i = 0; i < frameDocs.length; ++i) {
        frameDocs[i].addEventListener(
            "visibilitychange",
            onVisibilityChange.bind(null, i), false);
    }

    document.body.removeChild(document.getElementById("frame1"));

    finishJSTest();
}

function onVisibilityChange(i) {
    shouldBe('frameDocs[' + i + '].visibilityState', '"hidden"');
}

</script>
<iframe id="frame1" src="resources/page-visibility-iframe-with-subframes.html"></iframe>
</body>
</html>
