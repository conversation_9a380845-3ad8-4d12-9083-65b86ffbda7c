<!DOCTYPE html>
<script src='../../resources/testharness.js'></script>
<script src='../../resources/testharnessreport.js'></script>
<p>This test ensures drag-n-drop does not start when extending an existing selecting with shift + mouse drag, starting over a #text.</p>
<span id='span' style='font-size: 50px; padding: 10px;'>hello world</span>
<div id='log'></div>
<script>
test(function() {
    assert_not_equals(window.testRunner, undefined, 'Requires testRunner.');
    assert_not_equals(window.eventSender, undefined, 'Requires eventSender.');

    var span = document.getElementById('span');
    span.focus();

    var dragStartCount = 0;
    document.addEventListener('dragstart', function (event) { dragStartCount++; });

    var y = span.offsetTop + span.offsetHeight / 2;
    eventSender.mouseMoveTo(span.offsetLeft + 5, y);
    eventSender.mouseDown();
    eventSender.leapForward(200);
    eventSender.mouseUp();

    eventSender.mouseMoveTo(span.offsetLeft + span.offsetWidth / 4, y);
    eventSender.mouseDown(0, ['shiftKey']);
    eventSender.leapForward(200);
    eventSender.mouseUp();

    eventSender.mouseMoveTo(span.offsetLeft + span.offsetWidth / 2, y);
    eventSender.mouseDown(0, ['shiftKey']);
    eventSender.leapForward(200);
    eventSender.mouseMoveTo(span.offsetLeft + span.offsetWidth, y);
    eventSender.leapForward(200);
    eventSender.mouseUp();

    assert_equals(dragStartCount, 0);
});
</script>
