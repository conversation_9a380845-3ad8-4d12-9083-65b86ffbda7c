Link to Bottom of the page
https://bugs.webkit.org/show_bug.cgi?id=20270
This test verifies that a anchor link overrides the overflow:hidden attribute by scrolling somewhere on a page.
To do the test manually you have to click on the anchor link above. If the scroll occurs to the bottom of the page : the test has PASSED.
If you stay here after the click, the test has failed.




Automated test : PASSED
Bottom of the page Manual Test : PASSED
