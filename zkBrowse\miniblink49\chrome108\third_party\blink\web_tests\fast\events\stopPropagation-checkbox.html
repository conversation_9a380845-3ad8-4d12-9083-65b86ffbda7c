<p>This test checks whether event.stopPropagation() prevents the
default action. It should not! If the default was prevented, you'll
see a link below and the text FAIL. On success you will see PASS. The
test should say PASS 4 times.

</p>
<input type="checkbox" id="checkbox1" onclick="event.stopPropagation()" value="click here">
<input type="checkbox" id="checkbox2" onclick="event.preventDefault()" value="click here">
<p>

<script>
if (window.testRunner)
    testRunner.dumpAsText();

var checkbox1 = document.getElementById("checkbox1");
checkbox1.click();
var checkbox2 = document.getElementById("checkbox2");
checkbox2.click();

document.write(checkbox1.checked ? "PASS<br>" : "FAIL<br>");
document.write(checkbox2.checked ? "FAIL<br>" : "PASS<br>");

</script>
