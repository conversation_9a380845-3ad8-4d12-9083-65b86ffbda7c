<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

 <head>

  <title>CSS Reftest Reference</title>
  <link rel="author" title="Gérard Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" />

  <style type="text/css"><![CDATA[
  body
  {
  background: navy url("support/diamond.png") no-repeat;
  border: blue solid 1em;
  margin: 1em;
  padding: 2em;
  }

  div
  {
  background-color: green;
  border: solid lime;
  color: white;
  padding: 3em;
  }
  ]]></style>

 </head>

 <body>

  <div>This time, there should be a single purple diamond at the upper left hand corner of the viewport. It should be bathed in blue. In this sea of blue there should be a bright thick blue border, containing more blue and a thin bright green border, which itself contains only green and this text.</div>

 </body>
</html>