<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright 2022 The Chromium Authors
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file.
-->
<LinearLayout
    android:id="@+id/fast_checkout_autofill_profile_item"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_height="wrap_content"
    android:layout_width="match_parent"
    android:orientation="horizontal"
    android:paddingTop="@dimen/fast_checkout_detail_sheet_padding_vertical"
    android:paddingBottom="@dimen/fast_checkout_detail_sheet_padding_vertical">

    <ImageView
        android:id="@+id/fast_checkout_credit_card_icon"
        android:layout_width="@dimen/fast_checkout_credit_card_icon_width"
        android:layout_height="@dimen/fast_checkout_credit_card_icon_height"
        android:layout_marginStart="@dimen/fast_checkout_credit_card_icon_margin_horizontal"
        android:layout_marginEnd="@dimen/fast_checkout_credit_card_icon_margin_horizontal"
        android:layout_gravity="center_vertical"
        android:importantForAccessibility="no"
        app:srcCompat="@drawable/mc_card" />

    <LinearLayout
        android:layout_height="wrap_content"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:orientation="vertical">
        <TextView
          android:id="@+id/fast_checkout_credit_card_item_number"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:textAppearance="@style/TextAppearance.TextMedium.Primary" />
        <TextView
          android:id="@+id/fast_checkout_credit_card_item_name"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:textAppearance="@style/TextAppearance.TextMedium.Primary" />
        <TextView
          android:id="@+id/fast_checkout_credit_card_item_expiration_date"
          android:layout_width="match_parent"
          android:layout_height="wrap_content"
          android:textAppearance="@style/TextAppearance.TextMedium.Primary" />
    </LinearLayout>
    <ImageView
        android:id="@+id/fast_checkout_credit_card_item_selected_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/fast_checkout_detail_sheet_selected_icon_margin_start"
        android:layout_marginEnd="@dimen/fast_checkout_detail_sheet_selected_icon_margin_end"
        android:layout_gravity="center_vertical"
        android:contentDescription="@null"
        app:tint="?attr/colorPrimary"
        app:srcCompat="@drawable/ic_check_circle_filled_green_24dp" />
</LinearLayout>
