// Copyright 2019 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef CHROME_UPDATER_WIN_INSTALLER_INSTALLER_CONSTANTS_H_
#define CHROME_UPDATER_WIN_INSTALLER_INSTALLER_CONSTANTS_H_

namespace updater {

// Various filenames and prefixes.
extern const wchar_t kUpdaterArchivePrefix[];

// The resource types that would be unpacked from the mini installer.
extern const wchar_t kLZMAResourceType[];

}  // namespace updater

#endif  // CHROME_UPDATER_WIN_INSTALLER_INSTALLER_CONSTANTS_H_
