Test to ensure the consistency of front-end patterns vs backend patterns for request interception.

Setting Pattern: **bar.js
Requesting: (MASKED_URL_PATH)/bar.js
Intercepted Request: (MASKED_URL_PATH)/bar.js
Response Received: (MASKED_URL_PATH)/bar.js

Setting Pattern: (MASKED_URL_PATH)/bar.js
Requesting: (MASKED_URL_PATH)/bar.js
Intercepted Request: (MASKED_URL_PATH)/bar.js
Response Received: (MASKED_URL_PATH)/bar.js

Setting Pattern: *bar.js
Requesting: (MASKED_URL_PATH)/bar.js
Intercepted Request: (MASKED_URL_PATH)/bar.js
Response Received: (MASKED_URL_PATH)/bar.js

Setting Pattern: bar.js
Requesting: (MASKED_URL_PATH)/bar.js
Response Received: (MASKED_URL_PATH)/bar.js

Setting Pattern: *bar.js
Requesting: (MASKED_URL_PATH)/bar.js
Intercepted Request: (MASKED_URL_PATH)/bar.js
Response Received: (MASKED_URL_PATH)/bar.js

Setting Pattern: *b*
Requesting: (MASKED_URL_PATH)/bar.js
Intercepted Request: (MASKED_URL_PATH)/bar.js
Response Received: (MASKED_URL_PATH)/bar.js

Setting Pattern: *
Requesting: (MASKED_URL_PATH)/bar.js
Intercepted Request: (MASKED_URL_PATH)/bar.js
Response Received: (MASKED_URL_PATH)/bar.js

Setting Pattern: *bar_js
Requesting: (MASKED_URL_PATH)/bar.js
Response Received: (MASKED_URL_PATH)/bar.js

Setting Pattern: *bar?js
Requesting: (MASKED_URL_PATH)/bar.js
Intercepted Request: (MASKED_URL_PATH)/bar.js
Response Received: (MASKED_URL_PATH)/bar.js


