<!DOCTYPE html>
<html>
<head>
    <script src="../../resources/js-test.js"></script>
</head>
<body>
    <script>
        window.jsTestIsAsync = true;
        description("This test should trigger 'window.onerror' inside the sandboxed iframe, and successfully handle the error.");
        var message = '';
        window.onmessage = function (e) {
            message = e.data;
            shouldBeEqualToString("message", "Uncaught ReferenceError: hahaha_good_luck_finding_me is not defined");
            finishJSTest();
        };
    </script>
    <iframe sandbox="allow-scripts" srcdoc="
    <script>
        window.onerror = function (message) {
            window.top.postMessage(message, '*');
        };
        hahaha_good_luck_finding_me(); // caught by window.onerror
</script>"></iframe>
</body>
</html>
