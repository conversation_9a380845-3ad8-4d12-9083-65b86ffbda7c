@echo off
echo Testing V8 10.8 Debug x64 Build Configuration
echo ============================================

REM Set Visual Studio environment for x64
call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Enterprise\VC\Auxiliary\Build\vcvars64.bat"
set VisualStudioVersion=16.0

echo.
echo Copying V8 10.8 DLL files to output directory...
if not exist "..\out\x64Debug" mkdir "..\out\x64Debug"
copy "..\v8_10_8\v8.dll" "..\out\x64Debug\" /Y
copy "..\v8_10_8\v8_libbase.dll" "..\out\x64Debug\" /Y
copy "..\v8_10_8\v8_libplatform.dll" "..\out\x64Debug\" /Y

echo.
echo Building Debug|x64 configuration with V8 10.8...
devenv miniblink.sln /build "Debug|x64" /Out ../out/x64Debug/build_v8_10_8.log /Project miniblink

echo.
echo Build completed. Check ../out/x64Debug/build_v8_10_8.log for details.
echo.

pause
