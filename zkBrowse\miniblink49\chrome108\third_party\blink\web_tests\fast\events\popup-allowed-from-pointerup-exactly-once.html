<script src="../../resources/testharness.js"></script>
<script src="../../resources/testharnessreport.js"></script>
<p>
    Test that only a single popup is allowed in response to a single
    pointer up user action. The test passes if only one popup is created.
</p>
<button id="button">Tap Here</button>
<div id="console"></div>
<script>
    setup({single_test: true});

    document.getElementById('button').addEventListener('pointerup', pointer_popup);
    function pointer_popup() {
        assert_not_equals(window.open("about:blank", "window1"), null, "Popup didn't open in pointerup handler.");
        assert_equals(window.open("about:blank", "window2"), null, "Popup should open only once in pointerup handler.");
        done();
    }

    if (window.testRunner) {
        testRunner.setPopupBlockingEnabled(true);

        if (window.eventSender) {
            var button = document.getElementById("button");
            eventSender.addTouchPoint(button.offsetLeft + button.offsetWidth / 2, button.offsetTop + button.offsetHeight / 2);
            eventSender.touchStart();
            eventSender.releaseTouchPoint(0);
            eventSender.touchEnd();
        }
    }
</script>
