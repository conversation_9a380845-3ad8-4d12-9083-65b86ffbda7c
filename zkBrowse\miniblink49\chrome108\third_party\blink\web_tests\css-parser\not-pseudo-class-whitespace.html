<!doctype html>
<script src="../resources/testharness.js"></script>
<script src="../resources/testharnessreport.js"></script>

<div id="div1"></div>
<div id="div2"></div>

<script>
test(function () {
    assert_equals(div1, document.querySelector("div:not(#div2)"));
    assert_equals(div1, document.querySelector("div:not( #div2)"));
    assert_equals(div2, document.querySelector("div:not( #div1 )"));
    assert_equals(div2, document.querySelector("div:not(#div1 )"));
}, "Checks that whitespaces are allowed in the :not pseudo-class");
</script>
