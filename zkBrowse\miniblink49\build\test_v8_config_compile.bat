@echo off
echo Testing V8 Configuration Macros
echo ================================

call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Enterprise\VC\Auxiliary\Build\vcvars32.bat"

echo.
echo Compiling V8 configuration test with project macros...
cl /std:c++17 /DUSING_V8_SHARED /DV8_MAJOR_VERSION=10 /I"../v8_10_8/include" test_v8_config.cpp /Fe:test_v8_config.exe

if exist test_v8_config.exe (
    echo.
    echo Compilation successful! Running configuration test...
    echo.
    test_v8_config.exe
    echo.
    echo V8 configuration is correct!
) else (
    echo.
    echo ERROR: V8 configuration test compilation failed!
)

pause
