<!DOCTYPE html>
<body onload="runTest()">
<iframe id="testframe" src="resources/left-right.html" width="100%" height="300px" frameborder="0"></iframe>
<script>
if (window.testRunner)
    testRunner.waitUntilDone();

function runTest() {
    if (sessionStorage.didNav) {
        delete sessionStorage.didNav;
        if (window.testRunner)
            testRunner.notifyDone();
    } else {
        // Navigate a timeout to make sure we generate a history entry that we
        // can go back to.
        setTimeout(function() {location.href = '../../resources/back.html';}, 0);
        sessionStorage.didNav = true;
    }
}
</script>
</body>
