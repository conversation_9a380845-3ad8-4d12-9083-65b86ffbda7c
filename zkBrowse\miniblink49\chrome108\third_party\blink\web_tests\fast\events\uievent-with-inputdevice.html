<script src="../../resources/js-test.js"></script>
<script>
    description("This tests that UIEvent and its subclass will have sourceCapabilities set to be null by default, and it can also be passed when initialization.")

    // Creating UIEvent.
    var uievent = document.createEvent('UIEvent');
    shouldBeNonNull("uievent");
    shouldBeNull("uievent.sourceCapabilities");  
    
    uievent = new UIEvent('eventType', { sourceCapabilities: new InputDeviceCapabilities({ firesTouchEvents: false }) });
    shouldBeNonNull("uievent.sourceCapabilities");
    shouldBeFalse("uievent.sourceCapabilities.firesTouchEvents");
    
    uievent = new UIEvent('eventType', { sourceCapabilities: new InputDeviceCapabilities({ firesTouchEvents: true }) });
    shouldBeNonNull("uievent.sourceCapabilities");
    shouldBeTrue("uievent.sourceCapabilities.firesTouchEvents");
    
    // Creating TouchEvent.
    var touchevent = document.createEvent("TouchEvent");
    shouldBeNonNull("touchevent");
    shouldBeNull("touchevent.sourceCapabilities");
    
    // Creating MouseEvent.
    var mouseevent = new MouseEvent('mousedown');
    shouldBeNonNull("mouseevent");
    shouldBeNull("mouseevent.sourceCapabilities");
    
    var mouseevent = new MouseEvent('mousedown', { sourceCapabilities: new InputDeviceCapabilities({ firesTouchEvents: false }) });
    shouldBeNonNull("mouseevent.sourceCapabilities");
    shouldBeFalse("mouseevent.sourceCapabilities.firesTouchEvents");

    
    // Creating KeyboardEvent.
    var keyboardevent = new KeyboardEvent("keydown");
    shouldBeNonNull("keyboardevent");
    shouldBeNull("keyboardevent.sourceCapabilities");
    
    keyboardevent = new KeyboardEvent("keydown", { sourceCapabilities: new InputDeviceCapabilities({ firesTouchEvents: false }) });
    shouldBeNonNull("keyboardevent.sourceCapabilities");
    shouldBeFalse("keyboardevent.sourceCapabilities.firesTouchEvents");
    
    // Creating FocusEvent.
    var focusevent = new FocusEvent("focusevent");
    shouldBeNonNull("focusevent");
    shouldBeNull("focusevent.sourceCapabilities");

    focusevent = new FocusEvent("focusevent", { sourceCapabilities: new InputDeviceCapabilities({ firesTouchEvents: false }) });
    shouldBeNonNull("focusevent.sourceCapabilities");
    shouldBeFalse("focusevent.sourceCapabilities.firesTouchEvents");

    // Creating CompositionEvent.
    var compositionevent = new CompositionEvent("compositionstart");
    shouldBeNonNull("compositionevent");
    shouldBeNull("compositionevent.sourceCapabilities");
    
    compositionevent = new CompositionEvent("compositionstart", { sourceCapabilities: new InputDeviceCapabilities({ firesTouchEvents: false }) });
    shouldBeNonNull("compositionevent.sourceCapabilities");
    shouldBeFalse("compositionevent.sourceCapabilities.firesTouchEvents");

</script>
