<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

 <head>

  <title>CSS Test: Background-position applied to elements with 'display' set to 'table-header-group'</title>

  <link rel="author" title="Gérard Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" />
  <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" title="14.2.1 Background properties: 'background-color', 'background-image', 'background-repeat', 'background-attachment', 'background-position', and 'background'" />
  <link rel="match" href="background-position-applies-to-001a-ref.xht" />

  <meta name="flags" content="image" />
  <meta name="assert" content="The 'background-position' property applies to elements with 'display' set to 'table-header-group'." />

  <style type="text/css"><![CDATA[
  thead
  {
  background-image: url("support/swatch-blue.png");
  background-position: top right;
  background-repeat: no-repeat;
  }

  td {border: transparent solid 25px;}
  ]]></style>

 </head>

 <body>

  <p>Test passes if there is <strong>one and only 1 small filled blue square</strong>.</p>

  <table>
    <thead>
      <tr><td></td><td></td></tr>
      <tr><td></td><td></td></tr>
    </thead>
    <tbody>
      <tr><td></td><td></td></tr>
      <tr><td></td><td></td></tr>
    </tbody>
  </table>

 </body>
</html>
