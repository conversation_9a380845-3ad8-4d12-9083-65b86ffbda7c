<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 20010904//EN" 
"http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">

<!--

   Copyright 2001-2002  The Apache Software Foundation 

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

-->
<!-- ====================================================================== -->
<!-- Tests text-anchor on multiple text chunks on single tspan element      -->
<!--                                                                        -->
<!-- <AUTHOR>                                               -->
<!-- @version $Id: textAnchor2.svg,v 1.5 2005/01/03 10:48:05 deweese Exp $  -->
<!-- ====================================================================== -->
<?xml-stylesheet type="text/css" href="../resources/test.css" ?>  

<svg width="450" height="500" viewBox="0 0 450 500"
     xmlns="http://www.w3.org/2000/svg" 
     xmlns:xlink="http://www.w3.org/1999/xlink" >

<text class="title" x="50%" y="30">text-anchor on a single &lt;tspan&gt;</text>

<g id="testContent">

<!-- ################################################################# -->
<!-- table                                                             -->
<!-- ################################################################# -->
<g>
	<rect x="75" y="50"  width="300" height="20" style="fill:black; stroke:black" />
	<rect x="75" y="70" width="300" height="40" style="fill:#eee; stroke:black" />
	<rect x="75" y="110" width="300" height="40" style="fill:white; stroke:black" />
	<rect x="75" y="150" width="300" height="40" style="fill:#eee; stroke:black" />
	<text x="90" y="64" style="fill:white;font-size:14px">x attribute define new text chunks</text>
	<g style="stroke:crimson; fill:none">
		<line x1="145" y1="70" x2="145" y2="190" />
		<line x1="185" y1="70" x2="185" y2="190" />
		<line x1="225" y1="70" x2="225" y2="190" />
		<line x1="265" y1="70" x2="265" y2="190" />
		<line x1="305" y1="70" x2="305" y2="190" />
	</g>
</g>

<g transform="translate(0 150)" stroke="black" >
	<rect x="75" y="50"  width="300" height="20" style="fill:black" />
	<rect x="75" y="70" width="300" height="40" style="fill:#eee" />
	<rect x="75" y="110" width="300" height="40" style="fill:white" />
	<rect x="75" y="150" width="300" height="40" style="fill:#eee" />
	<text x="90" y="64" style="fill:white;stroke:none;font-size:14px"
         >y attribute does not define new text chunk</text>
	<g style="stroke:crimson; fill:none">
		<line x1="225" y1="70" x2="225" y2="190" />
	</g>
</g>

<g transform="translate(0 300)">
	<rect x="75" y="50"  width="300" height="20" style="fill:black; stroke:black" />
	<rect x="75" y="70" width="300" height="40" style="fill:#eee; stroke:black" />
	<rect x="75" y="110" width="300" height="40" style="fill:white; stroke:black" />
	<rect x="75" y="150" width="300" height="40" style="fill:#eee; stroke:black" />
	<text x="90" y="64" style="fill:white;font-size:14px">x &amp; y attributes define new text chunks</text>
	<g style="stroke:crimson; fill:none">
		<line x1="145" y1="70" x2="145" y2="190" />
		<line x1="185" y1="70" x2="185" y2="190" />
		<line x1="225" y1="70" x2="225" y2="190" />
		<line x1="265" y1="70" x2="265" y2="190" />
		<line x1="305" y1="70" x2="305" y2="190" />
	</g>
</g>

<!-- ################################################################# -->
<!-- x attribute defines new text chunks                               -->
<!-- ################################################################# -->

<text transform="translate(145 94)"><tspan x="0 40 80 120 160" style="text-anchor:start">Batik</tspan></text>
<text transform="translate(145 134)"><tspan x="0 40 80 120 160" style="text-anchor:middle">Batik</tspan></text>
<text transform="translate(145 174)"><tspan x="0 40 80 120 160" style="text-anchor:end">Batik</tspan></text>

<!-- ################################################################# -->
<!-- y attribute does not define new text chunk                        -->
<!-- ################################################################# -->
<text transform="translate(225 240)"><tspan y="0 10 0 10 0" style="text-anchor:start">Batik</tspan></text>
<text transform="translate(225 280)"><tspan y="0 10 0 10 0" style="text-anchor:middle">Batik</tspan></text>
<text transform="translate(225 320)"><tspan y="0 10 0 10 0" style="text-anchor:end">Batik</tspan></text>

<!-- ################################################################# -->
<!-- both x and y attributes define new text chunks                    -->
<!-- ################################################################# -->
<text transform="translate(145 390)"><tspan x="0 40 80 120 160" y="0 10 0 10 0" style="text-anchor:start">Batik</tspan></text>
<text transform="translate(145 430)"><tspan x="0 40 80 120 160" y="0 10 0 10 0" style="text-anchor:middle">Batik</tspan></text>
<text transform="translate(145 470)"><tspan x="0 40 80 120 160" y="0 10 0 10 0" style="text-anchor:end">Batik</tspan></text>

</g>

</svg>
