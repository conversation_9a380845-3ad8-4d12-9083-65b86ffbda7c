<!DOCTYPE html>
<title>Loading invalid SVG image with reference data:uri image should dispatch 'error' event</title>
<script src="../../resources/testharness.js"></script>
<script src="../../resources/testharnessreport.js"></script>
<script>
async_test(function(t) {
    var img = new Image();
    img.src = 'resources/invalid-image-with-data-uri.svg';
    img.onload = t.unreached_func();
    img.onerror = t.step_func_done();
});
</script>
