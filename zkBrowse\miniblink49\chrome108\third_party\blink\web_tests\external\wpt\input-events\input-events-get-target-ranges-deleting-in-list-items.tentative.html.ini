[input-events-get-target-ranges-deleting-in-list-items.tentative.html?Backspace,ol]
  expected: [OK, ERROR]

  [Backspace at "<ol><li>list-\[item1</li><li>\]list-item2</li></ol>"]
    expected: FAIL

  [Backspace at "<ol><li>list-item1{</li><li>list\]-item2</li></ol>"]
    expected: FAIL

  [Backspace at "<ol><li>list-item1\[</li><li>\]list-item2</li></ol>"]
    expected: FAIL

  [Backspace at "<ol><li>list-item1</li><li>\[\]list-item2</li></ol>"]
    expected: FAIL

  [Backspace at "<ol><li>list-item1<br></li><li>\[\]list-item2</li></ol>"]
    expected: FAIL

  [Backspace at "<ol><li>list-item1<br><br></li><li>\[\]list-item2</li></ol>"]
    expected: FAIL

  [Backspace at "<ol><li>list-item1</li><li>\[\]list-item2<br>second line of list-item2</li></ol>"]
    expected: FAIL

  [Backspace at "<ol><li><p>list-item1</p></li><li>\[\]list-item2</li></ol>"]
    expected: FAIL

  [Backspace at "<ol><li>list-item1</li><li><p>\[\]list-item2</p></li></ol>"]
    expected: FAIL

  [Backspace at "<ol>{<li>list-item1</li>}<li>list-item2</li></ol>"]
    expected: FAIL

  [Backspace at "<ol><li>{}<br></li></ol>"]
    expected: FAIL

  [Backspace at "{<ol><li><br></li></ol>}"]
    expected: FAIL

  [Backspace at "<ol><li>list-item1</li>{<li>list-item2</li>}<li>list-item3</li></ol>"]
    expected: FAIL

  [Backspace at "<ol><li>list-item1</li>{<li>list-item2</li>}<li><ul><li><br></li></ul></li></ol>"]
    expected: FAIL

  [Backspace at "<ol><li>list-item1</li>{<li>list-item2</li>}<ul><li><br></li></ul></ol>"]
    expected: FAIL

  [Backspace at "<ol><li>list-item1</li><li>list-item2</li>{<ul><li><br></li></ul>}</ol>"]
    expected: FAIL

  [Backspace at "<ol><li>list-item1</li>{<li>list-item2</li>}<li><ol><li><br></li></ol></li></ol>"]
    expected: FAIL

  [Backspace at "<ol><li>list-item1</li>{<li>list-item2</li>}<ol><li><br></li></ol></ol>"]
    expected: FAIL

  [Backspace at "<ol><li>list-item1</li><li>list-item2</li>{<ol><li><br></li></ol>}</ol>"]
    expected: FAIL

  [Backspace at "<ol><ol><li>\[list-item1</li></ol><li>list-item2\]</li></ol>"]
    expected: FAIL

  [Backspace at "<ol><li><ul><li>{}<br></li></ul></li></ol>"]
    expected: FAIL

  [Backspace at "<ol><li><ul><li>{}<br></li></ul></li><li>list-item2</li></ol>"]
    expected: FAIL

  [Backspace at "<ol><li>list-item1</li><li><ul><li>{}<br></li></ul></li></ol>"]
    expected: FAIL

  [Backspace at "<ol><ul><li>{}<br></li></ul></ol>"]
    expected: FAIL

  [Backspace at "<ol><ul><li>{}<br></li></ul><li>list-item2</li></ol>"]
    expected: FAIL

  [Backspace at "<ol><li>list-item1</li><ul><li>{}<br></li></ul></ol>"]
    expected: FAIL

  [Backspace at "<ol><li><ol><li>{}<br></li></ol></li></ol>"]
    expected: FAIL

  [Backspace at "<ol><li><ol><li>{}<br></li></ol></li><li>list-item2</li></ol>"]
    expected: FAIL

  [Backspace at "<ol><li>list-item1</li><li><ol><li>{}<br></li></ol></li></ol>"]
    expected: FAIL

  [Backspace at "<ol><ol><li>{}<br></li></ol></ol>"]
    expected: FAIL

  [Backspace at "<ol><ol><li>{}<br></li></ol><li>list-item2</li></ol>"]
    expected: FAIL

  [Backspace at "<ol><li>list-item1</li><ol><li>{}<br></li></ol></ol>"]
    expected: FAIL

  [Backspace at "<ol><li><ul><li>\[list-item1</li></ul></li></ol><ul><li>}list-item2</li></ul>"]
    expected: FAIL

  [Backspace at "<ol><ul><li>\[list-item1</li></ul></ol><ul><li>}list-item2</li></ul>"]
    expected: FAIL

  [Backspace at "<ol><li><ul><li>\[list-item1</li></ul></li></ol><ol><li>}list-item2</li></ol>"]
    expected: FAIL

  [Backspace at "<ol><ul><li>\[list-item1</li></ul></ol><ol><li>}list-item2</li></ol>"]
    expected: FAIL

  [Backspace at "<ol><li><ol><li>\[list-item1</li></ol></li></ol><ul><li>}list-item2</li></ul>"]
    expected: FAIL

  [Backspace at "<ol><ol><li>\[list-item1</li></ol></ol><ul><li>}list-item2</li></ul>"]
    expected: FAIL

  [Backspace at "<ol><li><ol><li>\[list-item1</li></ol></li></ol><ol><li>}list-item2</li></ol>"]
    expected: FAIL

  [Backspace at "<ol><ol><li>\[list-item1</li></ol></ol><ol><li>}list-item2</li></ol>"]
    expected: FAIL

  [Backspace at "<ol>{<li>list-item1</li>}<li>list-item2</li></ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><li>list-item1</li>{<li>list-item2</li>}<li>list-item3</li></ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><li>list-item1</li><li>list-item2</li>{<ul><li><br></li></ul>}</ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><li>list-item1</li>{<li>list-item2</li>}<li><ol><li><br></li></ol></li></ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><li>list-item1</li>{<li>list-item2</li>}<ol><li><br></li></ol></ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><li>list-item1\[</li><li>list-item2\]</li></ol><ul><li>list-item3</li></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><li>list-item1\[</li><li>list-item2\]</li></ol><ol><li>list-item3</li></ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><li>\[list-item1</li><ul><li>list-item2\]</li></ul></ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><ul><li>list-item1</li><li>\[list-item2</li></ul><li>}list-item3</li></ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><li>\[list-item1</li><li><ul><li>list-item2\]</li></ul></li></ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><li>\[list-item1</li><ol><li>list-item2\]</li></ol></ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><ol><li>list-item1</li><li>\[list-item2</li></ol><li>}list-item3</li></ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><li>\[list-item1</li><li><ol><li>list-item2\]</li></ol></li></ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><li><ol><li>\[list-item1</li></ol><li>}list-item2</li></ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><li><ul><li>{}<br></li></ul></li></ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><li><ul><li>{}<br></li></ul></li><li>list-item2</li></ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><li>list-item1</li><li><ul><li>{}<br></li></ul></li></ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><li>list-item1</li><ul><li>{}<br></li></ul></ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><li><ol><li>{}<br></li></ol></li></ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><li><ol><li>{}<br></li></ol></li><li>list-item2</li></ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><li>list-item1</li><li><ol><li>{}<br></li></ol></li></ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><li>\[list-item1</li></ol><ol><li>}list-item2</li></ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><li>list-item1\[</li></ol><ol><li>}list-item2</li></ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><li>first line in list-item1<br>list-item1\[</li></ol><ol><li>}list-item2</li></ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><li>list-item1\[</li></ol><ol><li>}list-item2<br>second line in list-item2</li></ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><li>list-item1</li><li>list-item2\[</li></ol><ol><li>}list-item3</li></ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><li>list-item1\[</li></ol><ol><li>}list-item2</li><li>list-item3</li></ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><li>list-item1\[</li></ol><ul><li><ol><li>}list-item2</li></ol></li></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><li>\[list-item1</li></ol><ul><li><ol><li>}list-item2</li></ol></li></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><li>list-item1\[</li></ol><ul><li><ol><li>}list-item2<br>second line of list-item2</li></ol></li></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><li>list-item1\[</li></ol><ul><ol><li>}list-item2</li></ol></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><li>\[list-item1</li></ol><ul><ol><li>}list-item2</li></ol></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><li>list-item1\[</li></ol><ul><ol><li>}list-item2<br>second line of list-item2</li></ol></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><li>list-item1\[</li></ol><ol><li><ol><li>}list-item2</li></ol></li></ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><li>\[list-item1</li></ol><ol><li><ol><li>}list-item2</li></ol></li></ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><li>list-item1\[</li></ol><ol><li><ol><li>}list-item2<br>second line of list-item2</li></ol></li></ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><li>list-item1\[</li></ol><ol><ol><li>}list-item2</li></ol></ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><li>\[list-item1</li></ol><ol><ol><li>}list-item2</li></ol></ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ol><li>list-item1\[</li></ol><ol><ol><li>}list-item2<br>second line of list-item2</li></ol></ol>" - comparing innerHTML]
    expected: FAIL

[input-events-get-target-ranges-deleting-in-list-items.tentative.html?Backspace,ul]
  expected: [OK, ERROR]

  [Backspace at "<ul><li>list-\[item1</li><li>\]list-item2</li></ul>"]
    expected: FAIL

  [Backspace at "<ul><li>list-item1{</li><li>list\]-item2</li></ul>"]
    expected: FAIL

  [Backspace at "<ul><li>list-item1\[</li><li>\]list-item2</li></ul>"]
    expected: FAIL

  [Backspace at "<ul><li>list-item1</li><li>\[\]list-item2</li></ul>"]
    expected: FAIL

  [Backspace at "<ul><li>list-item1<br></li><li>\[\]list-item2</li></ul>"]
    expected: FAIL

  [Backspace at "<ul><li>list-item1<br><br></li><li>\[\]list-item2</li></ul>"]
    expected: FAIL

  [Backspace at "<ul><li>list-item1</li><li>\[\]list-item2<br>second line of list-item2</li></ul>"]
    expected: FAIL

  [Backspace at "<ul><li><p>list-item1</p></li><li>\[\]list-item2</li></ul>"]
    expected: FAIL

  [Backspace at "<ul><li>list-item1</li><li><p>\[\]list-item2</p></li></ul>"]
    expected: FAIL

  [Backspace at "<ul>{<li>list-item1</li>}<li>list-item2</li></ul>"]
    expected: FAIL

  [Backspace at "<ul><li>{}<br></li></ul>"]
    expected: FAIL

  [Backspace at "{<ul><li><br></li></ul>}"]
    expected: FAIL

  [Backspace at "<ul><li>list-item1</li>{<li>list-item2</li>}<li>list-item3</li></ul>"]
    expected: FAIL

  [Backspace at "<ul><li>list-item1</li>{<li>list-item2</li>}<li><ul><li><br></li></ul></li></ul>"]
    expected: FAIL

  [Backspace at "<ul><li>list-item1</li>{<li>list-item2</li>}<ul><li><br></li></ul></ul>"]
    expected: FAIL

  [Backspace at "<ul><li>list-item1</li><li>list-item2</li>{<ul><li><br></li></ul>}</ul>"]
    expected: FAIL

  [Backspace at "<ul><li>list-item1</li>{<li>list-item2</li>}<li><ol><li><br></li></ol></li></ul>"]
    expected: FAIL

  [Backspace at "<ul><li>list-item1</li>{<li>list-item2</li>}<ol><li><br></li></ol></ul>"]
    expected: FAIL

  [Backspace at "<ul><li>list-item1</li><li>list-item2</li>{<ol><li><br></li></ol>}</ul>"]
    expected: FAIL

  [Backspace at "<ul><ul><li>\[list-item1</li></ul><li>list-item2\]</li></ul>"]
    expected: FAIL

  [Backspace at "<ul><li><ul><li>{}<br></li></ul></li></ul>"]
    expected: FAIL

  [Backspace at "<ul><li><ul><li>{}<br></li></ul></li><li>list-item2</li></ul>"]
    expected: FAIL

  [Backspace at "<ul><li>list-item1</li><li><ul><li>{}<br></li></ul></li></ul>"]
    expected: FAIL

  [Backspace at "<ul><ul><li>{}<br></li></ul></ul>"]
    expected: FAIL

  [Backspace at "<ul><ul><li>{}<br></li></ul><li>list-item2</li></ul>"]
    expected: FAIL

  [Backspace at "<ul><li>list-item1</li><ul><li>{}<br></li></ul></ul>"]
    expected: FAIL

  [Backspace at "<ul><li><ol><li>{}<br></li></ol></li></ul>"]
    expected: FAIL

  [Backspace at "<ul><li><ol><li>{}<br></li></ol></li><li>list-item2</li></ul>"]
    expected: FAIL

  [Backspace at "<ul><li>list-item1</li><li><ol><li>{}<br></li></ol></li></ul>"]
    expected: FAIL

  [Backspace at "<ul><ol><li>{}<br></li></ol></ul>"]
    expected: FAIL

  [Backspace at "<ul><ol><li>{}<br></li></ol><li>list-item2</li></ul>"]
    expected: FAIL

  [Backspace at "<ul><li>list-item1</li><ol><li>{}<br></li></ol></ul>"]
    expected: FAIL

  [Backspace at "<ul><li><ul><li>\[list-item1</li></ul></li></ul><ul><li>}list-item2</li></ul>"]
    expected: FAIL

  [Backspace at "<ul><ul><li>\[list-item1</li></ul></ul><ul><li>}list-item2</li></ul>"]
    expected: FAIL

  [Backspace at "<ul><li><ul><li>\[list-item1</li></ul></li></ul><ol><li>}list-item2</li></ol>"]
    expected: FAIL

  [Backspace at "<ul><ul><li>\[list-item1</li></ul></ul><ol><li>}list-item2</li></ol>"]
    expected: FAIL

  [Backspace at "<ul><li><ol><li>\[list-item1</li></ol></li></ul><ul><li>}list-item2</li></ul>"]
    expected: FAIL

  [Backspace at "<ul><ol><li>\[list-item1</li></ol></ul><ul><li>}list-item2</li></ul>"]
    expected: FAIL

  [Backspace at "<ul><li><ol><li>\[list-item1</li></ol></li></ul><ol><li>}list-item2</li></ol>"]
    expected: FAIL

  [Backspace at "<ul><ol><li>\[list-item1</li></ol></ul><ol><li>}list-item2</li></ol>"]
    expected: FAIL

  [Backspace at "<ul>{<li>list-item1</li>}<li>list-item2</li></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><li>list-item1</li>{<li>list-item2</li>}<li>list-item3</li></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><li>list-item1</li>{<li>list-item2</li>}<li><ul><li><br></li></ul></li></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><li>list-item1</li>{<li>list-item2</li>}<ul><li><br></li></ul></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><li>list-item1</li><li>list-item2</li>{<ol><li><br></li></ol>}</ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><li>list-item1\[</li><li>list-item2\]</li></ul><ul><li>list-item3</li></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><li>list-item1\[</li><li>list-item2\]</li></ul><ol><li>list-item3</li></ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><li>\[list-item1</li><ul><li>list-item2\]</li></ul></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><ul><li>list-item1</li><li>\[list-item2</li></ul><li>}list-item3</li></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><li>\[list-item1</li><li><ul><li>list-item2\]</li></ul></li></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><li><ul><li>\[list-item1</li></ul><li>}list-item2</li></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><li>\[list-item1</li><ol><li>list-item2\]</li></ol></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><ol><li>list-item1</li><li>\[list-item2</li></ol><li>}list-item3</li></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><li>\[list-item1</li><li><ol><li>list-item2\]</li></ol></li></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><li><ul><li>{}<br></li></ul></li></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><li><ul><li>{}<br></li></ul></li><li>list-item2</li></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><li>list-item1</li><li><ul><li>{}<br></li></ul></li></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><li><ol><li>{}<br></li></ol></li></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><li><ol><li>{}<br></li></ol></li><li>list-item2</li></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><li>list-item1</li><li><ol><li>{}<br></li></ol></li></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><li>list-item1</li><ol><li>{}<br></li></ol></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><li>\[list-item1</li></ul><ul><li>}list-item2</li></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><li>list-item1\[</li></ul><ul><li>}list-item2</li></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><li>first line in list-item1<br>list-item1\[</li></ul><ul><li>}list-item2</li></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><li>list-item1\[</li></ul><ul><li>}list-item2<br>second line in list-item2</li></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><li>list-item1</li><li>list-item2\[</li></ul><ul><li>}list-item3</li></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><li>list-item1\[</li></ul><ul><li>}list-item2</li><li>list-item3</li></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><li>list-item1\[</li></ul><ul><li><ul><li>}list-item2</li></ul></li></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><li>\[list-item1</li></ul><ul><li><ul><li>}list-item2</li></ul></li></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><li>list-item1\[</li></ul><ul><li><ul><li>}list-item2<br>second line of list-item2</li></ul></li></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><li>list-item1\[</li></ul><ul><ul><li>}list-item2</li></ul></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><li>\[list-item1</li></ul><ul><ul><li>}list-item2</li></ul></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><li>list-item1\[</li></ul><ul><ul><li>}list-item2<br>second line of list-item2</li></ul></ul>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><li>list-item1\[</li></ul><ol><li><ul><li>}list-item2</li></ul></li></ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><li>\[list-item1</li></ul><ol><li><ul><li>}list-item2</li></ul></li></ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><li>list-item1\[</li></ul><ol><li><ul><li>}list-item2<br>second line of list-item2</li></ul></li></ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><li>list-item1\[</li></ul><ol><ul><li>}list-item2</li></ul></ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><li>\[list-item1</li></ul><ol><ul><li>}list-item2</li></ul></ol>" - comparing innerHTML]
    expected: FAIL

  [Backspace at "<ul><li>list-item1\[</li></ul><ol><ul><li>}list-item2<br>second line of list-item2</li></ul></ol>" - comparing innerHTML]
    expected: FAIL

[input-events-get-target-ranges-deleting-in-list-items.tentative.html?Delete,ol]
  expected: [OK, ERROR]

  [Delete at "<ol><li>list-\[item1</li><li>\]list-item2</li></ol>"]
    expected: FAIL

  [Delete at "<ol><li>list-item1{</li><li>list\]-item2</li></ol>"]
    expected: FAIL

  [Delete at "<ol><li>list-item1\[</li><li>\]list-item2</li></ol>"]
    expected: FAIL

  [Delete at "<ol><li>list-item1\[\]</li><li>list-item2</li></ol>"]
    expected: FAIL

  [Delete at "<ol><li>list-item1\[\]<br></li><li>list-item2</li></ol>"]
    expected: FAIL

  [Delete at "<ol><li>list-item1\[\]<br><br></li><li>list-item2</li></ol>"]
    expected: FAIL

  [Delete at "<ol><li>list-item1\[\]</li><li>list-item2<br>second line of list-item2</li></ol>"]
    expected: FAIL

  [Delete at "<ol><li><p>list-item1\[\]</p></li><li>list-item2</li></ol>"]
    expected: FAIL

  [Delete at "<ol><li>list-item1\[\]</li><li><p>list-item2</p></li></ol>"]
    expected: FAIL

  [Delete at "<ol>{<li>list-item1</li>}<li>list-item2</li></ol>"]
    expected: FAIL

  [Delete at "<ol><li>{}<br></li></ol>"]
    expected: FAIL

  [Delete at "{<ol><li><br></li></ol>}"]
    expected: FAIL

  [Delete at "<ol><li>list-item1</li>{<li>list-item2</li>}<li>list-item3</li></ol>"]
    expected: FAIL

  [Delete at "<ol><li>list-item1</li>{<li>list-item2</li>}<li><ul><li><br></li></ul></li></ol>"]
    expected: FAIL

  [Delete at "<ol><li>list-item1</li>{<li>list-item2</li>}<ul><li><br></li></ul></ol>"]
    expected: FAIL

  [Delete at "<ol><li>list-item1</li><li>list-item2</li>{<ul><li><br></li></ul>}</ol>"]
    expected: FAIL

  [Delete at "<ol><li>list-item1</li>{<li>list-item2</li>}<li><ol><li><br></li></ol></li></ol>"]
    expected: FAIL

  [Delete at "<ol><li>list-item1</li>{<li>list-item2</li>}<ol><li><br></li></ol></ol>"]
    expected: FAIL

  [Delete at "<ol><li>list-item1</li><li>list-item2</li>{<ol><li><br></li></ol>}</ol>"]
    expected: FAIL

  [Delete at "<ol><ol><li>\[list-item1</li></ol><li>list-item2\]</li></ol>"]
    expected: FAIL

  [Delete at "<ol><li><ul><li>{}<br></li></ul></li></ol>"]
    expected: FAIL

  [Delete at "<ol><li><ul><li>{}<br></li></ul></li><li>list-item2</li></ol>"]
    expected: FAIL

  [Delete at "<ol><li>list-item1</li><li><ul><li>{}<br></li></ul></li></ol>"]
    expected: FAIL

  [Delete at "<ol><ul><li>{}<br></li></ul></ol>"]
    expected: FAIL

  [Delete at "<ol><ul><li>{}<br></li></ul><li>list-item2</li></ol>"]
    expected: FAIL

  [Delete at "<ol><li>list-item1</li><ul><li>{}<br></li></ul></ol>"]
    expected: FAIL

  [Delete at "<ol><li><ol><li>{}<br></li></ol></li></ol>"]
    expected: FAIL

  [Delete at "<ol><li><ol><li>{}<br></li></ol></li><li>list-item2</li></ol>"]
    expected: FAIL

  [Delete at "<ol><li>list-item1</li><li><ol><li>{}<br></li></ol></li></ol>"]
    expected: FAIL

  [Delete at "<ol><ol><li>{}<br></li></ol></ol>"]
    expected: FAIL

  [Delete at "<ol><ol><li>{}<br></li></ol><li>list-item2</li></ol>"]
    expected: FAIL

  [Delete at "<ol><li>list-item1</li><ol><li>{}<br></li></ol></ol>"]
    expected: FAIL

  [Delete at "<ol><li><ul><li>\[list-item1</li></ul></li></ol><ul><li>}list-item2</li></ul>"]
    expected: FAIL

  [Delete at "<ol><ul><li>\[list-item1</li></ul></ol><ul><li>}list-item2</li></ul>"]
    expected: FAIL

  [Delete at "<ol><li><ul><li>\[list-item1</li></ul></li></ol><ol><li>}list-item2</li></ol>"]
    expected: FAIL

  [Delete at "<ol><ul><li>\[list-item1</li></ul></ol><ol><li>}list-item2</li></ol>"]
    expected: FAIL

  [Delete at "<ol><li><ol><li>\[list-item1</li></ol></li></ol><ul><li>}list-item2</li></ul>"]
    expected: FAIL

  [Delete at "<ol><ol><li>\[list-item1</li></ol></ol><ul><li>}list-item2</li></ul>"]
    expected: FAIL

  [Delete at "<ol><li><ol><li>\[list-item1</li></ol></li></ol><ol><li>}list-item2</li></ol>"]
    expected: FAIL

  [Delete at "<ol><ol><li>\[list-item1</li></ol></ol><ol><li>}list-item2</li></ol>"]
    expected: FAIL

  [Delete at "<ol><li>list-item1\[\]<br><br></li><li>list-item2</li></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol>{<li>list-item1</li>}<li>list-item2</li></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li>{}<br></li></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "{<ol><li><br></li></ol>}" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li>list-item1</li>{<li>list-item2</li>}<li>list-item3</li></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li>list-item1</li><li>list-item2</li>{<ul><li><br></li></ul>}</ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li>list-item1</li>{<li>list-item2</li>}<li><ol><li><br></li></ol></li></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li>list-item1</li>{<li>list-item2</li>}<ol><li><br></li></ol></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li>list-item1</li><li>list-item2</li>{<ol><li><br></li></ol>}</ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li>list-item1\[</li><li>list-item2\]</li></ol><ul><li>list-item3</li></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li>list-item1\[</li><li>list-item2\]</li></ol><ol><li>list-item3</li></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li>\[list-item1</li><ul><li>list-item2\]</li></ul></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><ul><li>list-item1</li><li>\[list-item2</li></ul><li>}list-item3</li></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li>\[list-item1</li><li><ul><li>list-item2\]</li></ul></li></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li>\[list-item1</li><ol><li>list-item2\]</li></ol></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><ol><li>list-item1</li><li>\[list-item2</li></ol><li>}list-item3</li></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li>\[list-item1</li><li><ol><li>list-item2\]</li></ol></li></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li><ol><li>\[list-item1</li></ol><li>}list-item2</li></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li><ul><li>{}<br></li></ul></li></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li><ul><li>{}<br></li></ul></li><li>list-item2</li></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li>list-item1</li><li><ul><li>{}<br></li></ul></li></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><ul><li>{}<br></li></ul></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><ul><li>{}<br></li></ul><li>list-item2</li></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li>list-item1</li><ul><li>{}<br></li></ul></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li><ol><li>{}<br></li></ol></li></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li><ol><li>{}<br></li></ol></li><li>list-item2</li></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li>list-item1</li><li><ol><li>{}<br></li></ol></li></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><ol><li>{}<br></li></ol></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><ol><li>{}<br></li></ol><li>list-item2</li></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li>list-item1</li><ol><li>{}<br></li></ol></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li>\[list-item1</li></ol><ol><li>}list-item2</li></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li>list-item1\[</li></ol><ol><li>}list-item2</li></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li>first line in list-item1<br>list-item1\[</li></ol><ol><li>}list-item2</li></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li>list-item1\[</li></ol><ol><li>}list-item2<br>second line in list-item2</li></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li>list-item1</li><li>list-item2\[</li></ol><ol><li>}list-item3</li></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li>list-item1\[</li></ol><ol><li>}list-item2</li><li>list-item3</li></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li>list-item1\[</li></ol><ul><li><ol><li>}list-item2</li></ol></li></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li>\[list-item1</li></ol><ul><li><ol><li>}list-item2</li></ol></li></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li>list-item1\[</li></ol><ul><li><ol><li>}list-item2<br>second line of list-item2</li></ol></li></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li>list-item1\[</li></ol><ul><ol><li>}list-item2</li></ol></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li>\[list-item1</li></ol><ul><ol><li>}list-item2</li></ol></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li>list-item1\[</li></ol><ul><ol><li>}list-item2<br>second line of list-item2</li></ol></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li>list-item1\[</li></ol><ol><li><ol><li>}list-item2</li></ol></li></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li>\[list-item1</li></ol><ol><li><ol><li>}list-item2</li></ol></li></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li>list-item1\[</li></ol><ol><li><ol><li>}list-item2<br>second line of list-item2</li></ol></li></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li>list-item1\[</li></ol><ol><ol><li>}list-item2</li></ol></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li>\[list-item1</li></ol><ol><ol><li>}list-item2</li></ol></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ol><li>list-item1\[</li></ol><ol><ol><li>}list-item2<br>second line of list-item2</li></ol></ol>" - comparing innerHTML]
    expected: FAIL

[input-events-get-target-ranges-deleting-in-list-items.tentative.html?Delete,ul]
  expected: [OK, ERROR]

  [Delete at "<ul><li>list-\[item1</li><li>\]list-item2</li></ul>"]
    expected: FAIL

  [Delete at "<ul><li>list-item1{</li><li>list\]-item2</li></ul>"]
    expected: FAIL

  [Delete at "<ul><li>list-item1\[</li><li>\]list-item2</li></ul>"]
    expected: FAIL

  [Delete at "<ul><li>list-item1\[\]</li><li>list-item2</li></ul>"]
    expected: FAIL

  [Delete at "<ul><li>list-item1\[\]<br></li><li>list-item2</li></ul>"]
    expected: FAIL

  [Delete at "<ul><li>list-item1\[\]<br><br></li><li>list-item2</li></ul>"]
    expected: FAIL

  [Delete at "<ul><li>list-item1\[\]</li><li>list-item2<br>second line of list-item2</li></ul>"]
    expected: FAIL

  [Delete at "<ul><li><p>list-item1\[\]</p></li><li>list-item2</li></ul>"]
    expected: FAIL

  [Delete at "<ul><li>list-item1\[\]</li><li><p>list-item2</p></li></ul>"]
    expected: FAIL

  [Delete at "<ul>{<li>list-item1</li>}<li>list-item2</li></ul>"]
    expected: FAIL

  [Delete at "<ul><li>{}<br></li></ul>"]
    expected: FAIL

  [Delete at "{<ul><li><br></li></ul>}"]
    expected: FAIL

  [Delete at "<ul><li>list-item1</li>{<li>list-item2</li>}<li>list-item3</li></ul>"]
    expected: FAIL

  [Delete at "<ul><li>list-item1</li>{<li>list-item2</li>}<li><ul><li><br></li></ul></li></ul>"]
    expected: FAIL

  [Delete at "<ul><li>list-item1</li>{<li>list-item2</li>}<ul><li><br></li></ul></ul>"]
    expected: FAIL

  [Delete at "<ul><li>list-item1</li><li>list-item2</li>{<ul><li><br></li></ul>}</ul>"]
    expected: FAIL

  [Delete at "<ul><li>list-item1</li>{<li>list-item2</li>}<li><ol><li><br></li></ol></li></ul>"]
    expected: FAIL

  [Delete at "<ul><li>list-item1</li>{<li>list-item2</li>}<ol><li><br></li></ol></ul>"]
    expected: FAIL

  [Delete at "<ul><li>list-item1</li><li>list-item2</li>{<ol><li><br></li></ol>}</ul>"]
    expected: FAIL

  [Delete at "<ul><ul><li>\[list-item1</li></ul><li>list-item2\]</li></ul>"]
    expected: FAIL

  [Delete at "<ul><li><ul><li>{}<br></li></ul></li></ul>"]
    expected: FAIL

  [Delete at "<ul><li><ul><li>{}<br></li></ul></li><li>list-item2</li></ul>"]
    expected: FAIL

  [Delete at "<ul><li>list-item1</li><li><ul><li>{}<br></li></ul></li></ul>"]
    expected: FAIL

  [Delete at "<ul><ul><li>{}<br></li></ul></ul>"]
    expected: FAIL

  [Delete at "<ul><ul><li>{}<br></li></ul><li>list-item2</li></ul>"]
    expected: FAIL

  [Delete at "<ul><li>list-item1</li><ul><li>{}<br></li></ul></ul>"]
    expected: FAIL

  [Delete at "<ul><li><ol><li>{}<br></li></ol></li></ul>"]
    expected: FAIL

  [Delete at "<ul><li><ol><li>{}<br></li></ol></li><li>list-item2</li></ul>"]
    expected: FAIL

  [Delete at "<ul><li>list-item1</li><li><ol><li>{}<br></li></ol></li></ul>"]
    expected: FAIL

  [Delete at "<ul><ol><li>{}<br></li></ol></ul>"]
    expected: FAIL

  [Delete at "<ul><ol><li>{}<br></li></ol><li>list-item2</li></ul>"]
    expected: FAIL

  [Delete at "<ul><li>list-item1</li><ol><li>{}<br></li></ol></ul>"]
    expected: FAIL

  [Delete at "<ul><li><ul><li>\[list-item1</li></ul></li></ul><ul><li>}list-item2</li></ul>"]
    expected: FAIL

  [Delete at "<ul><ul><li>\[list-item1</li></ul></ul><ul><li>}list-item2</li></ul>"]
    expected: FAIL

  [Delete at "<ul><li><ul><li>\[list-item1</li></ul></li></ul><ol><li>}list-item2</li></ol>"]
    expected: FAIL

  [Delete at "<ul><ul><li>\[list-item1</li></ul></ul><ol><li>}list-item2</li></ol>"]
    expected: FAIL

  [Delete at "<ul><li><ol><li>\[list-item1</li></ol></li></ul><ul><li>}list-item2</li></ul>"]
    expected: FAIL

  [Delete at "<ul><ol><li>\[list-item1</li></ol></ul><ul><li>}list-item2</li></ul>"]
    expected: FAIL

  [Delete at "<ul><li><ol><li>\[list-item1</li></ol></li></ul><ol><li>}list-item2</li></ol>"]
    expected: FAIL

  [Delete at "<ul><ol><li>\[list-item1</li></ol></ul><ol><li>}list-item2</li></ol>"]
    expected: FAIL

  [Delete at "<ul><li>list-item1\[\]<br><br></li><li>list-item2</li></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul>{<li>list-item1</li>}<li>list-item2</li></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li>{}<br></li></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "{<ul><li><br></li></ul>}" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li>list-item1</li>{<li>list-item2</li>}<li>list-item3</li></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li>list-item1</li>{<li>list-item2</li>}<li><ul><li><br></li></ul></li></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li>list-item1</li>{<li>list-item2</li>}<ul><li><br></li></ul></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li>list-item1</li><li>list-item2</li>{<ul><li><br></li></ul>}</ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li>list-item1</li><li>list-item2</li>{<ol><li><br></li></ol>}</ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li>list-item1\[</li><li>list-item2\]</li></ul><ul><li>list-item3</li></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li>list-item1\[</li><li>list-item2\]</li></ul><ol><li>list-item3</li></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li>\[list-item1</li><ul><li>list-item2\]</li></ul></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><ul><li>list-item1</li><li>\[list-item2</li></ul><li>}list-item3</li></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li>\[list-item1</li><li><ul><li>list-item2\]</li></ul></li></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li><ul><li>\[list-item1</li></ul><li>}list-item2</li></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li>\[list-item1</li><ol><li>list-item2\]</li></ol></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><ol><li>list-item1</li><li>\[list-item2</li></ol><li>}list-item3</li></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li>\[list-item1</li><li><ol><li>list-item2\]</li></ol></li></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li><ul><li>{}<br></li></ul></li></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li><ul><li>{}<br></li></ul></li><li>list-item2</li></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li>list-item1</li><li><ul><li>{}<br></li></ul></li></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><ul><li>{}<br></li></ul></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><ul><li>{}<br></li></ul><li>list-item2</li></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li>list-item1</li><ul><li>{}<br></li></ul></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li><ol><li>{}<br></li></ol></li></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li><ol><li>{}<br></li></ol></li><li>list-item2</li></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li>list-item1</li><li><ol><li>{}<br></li></ol></li></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><ol><li>{}<br></li></ol></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><ol><li>{}<br></li></ol><li>list-item2</li></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li>list-item1</li><ol><li>{}<br></li></ol></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li>\[list-item1</li></ul><ul><li>}list-item2</li></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li>list-item1\[</li></ul><ul><li>}list-item2</li></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li>first line in list-item1<br>list-item1\[</li></ul><ul><li>}list-item2</li></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li>list-item1\[</li></ul><ul><li>}list-item2<br>second line in list-item2</li></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li>list-item1</li><li>list-item2\[</li></ul><ul><li>}list-item3</li></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li>list-item1\[</li></ul><ul><li>}list-item2</li><li>list-item3</li></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li>list-item1\[</li></ul><ul><li><ul><li>}list-item2</li></ul></li></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li>\[list-item1</li></ul><ul><li><ul><li>}list-item2</li></ul></li></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li>list-item1\[</li></ul><ul><li><ul><li>}list-item2<br>second line of list-item2</li></ul></li></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li>list-item1\[</li></ul><ul><ul><li>}list-item2</li></ul></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li>\[list-item1</li></ul><ul><ul><li>}list-item2</li></ul></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li>list-item1\[</li></ul><ul><ul><li>}list-item2<br>second line of list-item2</li></ul></ul>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li>list-item1\[</li></ul><ol><li><ul><li>}list-item2</li></ul></li></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li>\[list-item1</li></ul><ol><li><ul><li>}list-item2</li></ul></li></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li>list-item1\[</li></ul><ol><li><ul><li>}list-item2<br>second line of list-item2</li></ul></li></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li>list-item1\[</li></ul><ol><ul><li>}list-item2</li></ul></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li>\[list-item1</li></ul><ol><ul><li>}list-item2</li></ul></ol>" - comparing innerHTML]
    expected: FAIL

  [Delete at "<ul><li>list-item1\[</li></ul><ol><ul><li>}list-item2<br>second line of list-item2</li></ul></ol>" - comparing innerHTML]
    expected: FAIL
