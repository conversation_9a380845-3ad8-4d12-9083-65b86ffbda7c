<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<defs>
    <path id="star" d="m 100,0 60,170 -160,-110 200,0 -170,110 z" transform="translate(40,40)"/>
    <clipPath id="clipClip">
        <circle cx="138" cy="130" r="50"/>
    </clipPath>
    <clipPath id="clip" clip-path="url(#clipClip)">
        <use x="0" y="0" xlink:href="#star" clip-rule="evenodd"/>
    </clipPath>
</defs>

<rect x="40" y="40" height="300" width="300" style="fill:green;clip-path:url(#clip);"/>
</svg>
