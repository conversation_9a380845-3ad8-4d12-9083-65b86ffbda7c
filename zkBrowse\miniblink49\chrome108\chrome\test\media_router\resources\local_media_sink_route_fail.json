{
  // Define the return value for getAvailableSinks API defined in
  // TestProvider.js.
  "getAvailableSinks": {
    // This is the URN used by LOCAL_FILE casting mode
    "urn:x-org.chromium.media:source:tab:0": [
      {"id": "id1", "friendlyName": "test-sink-1"},
      {"id": "id2", "friendlyName": "test-sink-2"}
    ]
  },

  // Define the return value for canRoute API, the return value should be
  // either 'true' or 'false'. The default value is 'true'.
  "canRoute": "false"
}