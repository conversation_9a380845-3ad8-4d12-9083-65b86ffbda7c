<!DOCTYPE html>
<title>Test that intrinsic height can be resolved</title>
<script src="../../resources/testharness.js"></script>
<script src="../../resources/testharnessreport.js"></script>
<img src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='300' viewBox='0 180 150 220' style='background:blue'></svg>">
<img src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' height='440' viewBox='0 180 150 220' style='background:purple'></svg>">
<script>
  var imgs = document.getElementsByTagName('img');
  function waitForComplete(img, i) {
      if (img.complete) {
          assert_equals(img.width, 300);
          assert_equals(img.height, 440);
          this.done();
      } else {
          setTimeout(this.step_func(waitForComplete.bind(this, img, i + 1)), 1);
      }
  }

  var t1 = async_test("Test that intrinsic height can be calculated from intrinsic width and intrinsic ratio");
  t1.step(waitForComplete.bind(t1, imgs[0], 0));

  var t2 = async_test("Test that intrinsic width can be calculated from intrinsic height and intrinsic ratio");
  t2.step(waitForComplete.bind(t2, imgs[1], 0));
</script>
