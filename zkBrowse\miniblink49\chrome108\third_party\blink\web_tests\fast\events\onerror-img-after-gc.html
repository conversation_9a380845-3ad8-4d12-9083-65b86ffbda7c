<!DOCTYPE html>
<html>
<head>
<script src="../../resources/js-test.js"></script>
<script>
    if (window.testRunner)
        testRunner.waitUntilDone();

    function test() {
        (function () {
            var img = document.createElement('img');
            img.onload = function () {
                alert('FAIL (1/1)');
                finishTesting();
            };
            img.onerror = function () {
                alert('PASS (1/1)');
                finishTesting();
            };
            img.src = "foo";
        })();
        gc();
    }

    function finishTesting() {
        if (window.testRunner)
            setTimeout(function () { testRunner.notifyDone(); }, 0);
        return true;
    }
</script>
</head>
<body onload='test();'>
    <p>
        This test ensures that a normal image error doesn't crash if GC occurs
        before the error event fires.
    </p>
</body>
</html>
