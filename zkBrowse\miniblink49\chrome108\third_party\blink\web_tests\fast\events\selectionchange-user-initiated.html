<!DOCTYPE html>
<html>
<head>
<script>

function expected(text) {
    return text.substr(text.indexOf(':') + 2);
}

var i = 0;

function logger(event) {
    var console = document.getElementById('console');
    var listItem = null;
    if (i < console.childNodes.length) {
        var text = null;
        do {
            listItem = console.childNodes[i++];
            text = listItem.innerText;
        } while (text.indexOf('OPTIONAL:') >= 0 && expected(text) != event.type && i < console.childNodes.length);
    }

    if (!listItem) {
        var listItem = document.createElement('li');
        listItem.appendChild(document.createTextNode('FAIL: ' + event.type + ' but didn\'t expect to fire'));
        console.appendChild(listItem);
        i = console.childNodes.length;
        return;
    }

    if (expected(listItem.innerText) == event.type)
        listItem.innerHTML = 'PASS: ' + event.type;
    else
        listItem.innerHTML = 'FAIL: ' + event.type + ' but expected ' + expected(listItem.innerText);
}

</script>
</head>
<body onselectionchange="logger(event)">
<p>To test this manually, click on "hello", and extend selection to right by pressing right arrow keys with shift twice.
Then click again on "world" and extend selection twice the same way.</p>
<div id="hello" contenteditable>hello</div>
<div id="world" contenteditable>world</div>
<ul id="console"
><li>EXPECT: mousedown</li
><li>EXPECT: selectionchange</li
><li>EXPECT: mouseup</li
><li>EXPECT: click</li
><li>EXPECT: keydown</li
><li>EXPECT: keyup</li
><li>EXPECT: selectionchange</li
><li>EXPECT: keydown</li
><li>EXPECT: keyup</li
><li>EXPECT: selectionchange</li
><li>EXPECT: mousedown</li
><li>EXPECT: selectionchange</li
><li>OPTIONAL: selectionchange</li
><li>EXPECT: mouseup</li
><li>EXPECT: click</li
><li>EXPECT: keydown</li
><li>EXPECT: keyup</li
><li>EXPECT: selectionchange</li
><li>EXPECT: keydown</li
><li>EXPECT: keyup</li
><li>EXPECT: selectionchange</li
></ul>
<script>

function attachLogger(element, name) {
    element.addEventListener('mousedown', logger, false);
    element.addEventListener('mouseup', logger, false);
    element.addEventListener('click', logger, false);
    element.addEventListener('keydown', logger, false);
    element.addEventListener('keyup', logger, false);
}

var helloDiv = document.getElementById('hello');
var worldDiv = document.getElementById('world');

attachLogger(helloDiv);
attachLogger(worldDiv);

if (window.testRunner && window.eventSender) {
    testRunner.dumpAsText();
    testRunner.waitUntilDone();

    var events = [
        function () { eventSender.mouseMoveTo(helloDiv.offsetLeft + 10, helloDiv.offsetTop + 2); },
        function () { eventSender.mouseDown(); },
        function () { eventSender.mouseUp(); },
        function () { eventSender.keyDown("ArrowLeft", ['shiftKey']); },
        function () { eventSender.keyDown("ArrowRight", ['shiftKey']); },
        function () { eventSender.mouseMoveTo(worldDiv.offsetLeft + 10, worldDiv.offsetTop + 2); },
        function () { eventSender.mouseDown(); },
        function () { eventSender.mouseUp(); },
        function () { eventSender.keyDown("ArrowLeft", ['shiftKey']); },
        function () { eventSender.keyDown("ArrowRight", ['shiftKey']); },
    ];
    var j = 0;

    function timer() {
        // Need to wait one more iteration for events to be processed
        if (j >= events.length) {
            testRunner.notifyDone();
            return;
        }
        events[j]();
        j++;
        setTimeout(timer, 0);
    }

    timer();
}

</script>
</body>
</html>
