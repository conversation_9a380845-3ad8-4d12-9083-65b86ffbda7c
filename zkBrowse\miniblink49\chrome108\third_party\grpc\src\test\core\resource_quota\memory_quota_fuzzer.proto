// Copyright 2021 gRPC authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package memory_quota_fuzzer;

message Empty {}

message Reclaimer {
    enum Pass {
        BENIGN = 0;
        IDLE = 1;
        DESTRUCTIVE = 2;
    }
    bool synchronous = 1;
    Pass pass = 2;
    Msg msg = 3;
}

message AllocationRequest {
    uint32 min = 1;
    uint32 max = 2;
}

message Action {
    reserved 15;
    int32 quota = 1;
    int32 allocator = 2;
    int32 allocation = 3;
    oneof action_type {
        Empty flush_exec_ctx = 7;
        Empty create_quota = 10;
        Empty delete_quota = 11;
        Empty create_allocator = 12;
        Empty delete_allocator = 13;
        uint64 set_quota_size = 14;
        AllocationRequest create_allocation = 16;
        Empty delete_allocation = 17;
        Reclaimer post_reclaimer = 18;
    }
}

message Msg {
    repeated Action actions = 2;
}
