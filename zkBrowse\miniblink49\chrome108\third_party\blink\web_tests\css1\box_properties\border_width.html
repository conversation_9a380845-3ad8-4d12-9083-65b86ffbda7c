<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.5.15 border-width</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
.zero {background-color: silver; border-width: 0;}
.one {border-width: 50px; border-style: solid;}
.two {border-width: thick; border-style: solid;}
.three {border-width: medium; border-style: solid;}
.four {border-width: thin; border-style: solid;}
.five {border-width: 25px;}</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.zero {background-color: silver; border-width: 0;}
.one {border-width: 50px; border-style: solid;}
.two {border-width: thick; border-style: solid;}
.three {border-width: medium; border-style: solid;}
.four {border-width: thin; border-style: solid;}
.five {border-width: 25px;}
</PRE>
<HR>
<P>
(These will only work if <CODE>border-style</CODE> is supported.)
</P>
<P class="zero">
This element has a class of zero.
</P>
<P class="one">
This element should have an overall border width of 50 pixels.
</P>
<P class="two">
This element should have a thick overall border width.
</P>
<P class="three">
This element should have a medium overall border width.
</P>
<P class="four">
This element should have a thin overall border width.
</P>
<P class="five">
This element should have no border and no extra "padding" on any side, as no <CODE>border-style</CODE> was set.
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P>
(These will only work if <CODE>border-style</CODE> is supported.)
</P>
<P class="zero">
This element has a class of zero.
</P>
<P class="one">
This element should have an overall border width of 50 pixels.
</P>
<P class="two">
This element should have a thick overall border width.
</P>
<P class="three">
This element should have a medium overall border width.
</P>
<P class="four">
This element should have a thin overall border width.
</P>
<P class="five">
This element should have no border and no extra "padding" on any side, as no <CODE>border-style</CODE> was set.
</P>
</TD></TR></TABLE></BODY>
</HTML>
