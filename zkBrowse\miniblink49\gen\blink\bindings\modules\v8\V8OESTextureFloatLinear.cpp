// Copyright 2014 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// This file has been auto-generated by code_generator_v8.py. DO NOT MODIFY!

#include "config.h"
#include "V8OESTextureFloatLinear.h"

#include "bindings/core/v8/ExceptionState.h"
#include "bindings/core/v8/V8DOMConfiguration.h"
#include "bindings/core/v8/V8GCController.h"
#include "bindings/core/v8/V8ObjectConstructor.h"
#include "core/dom/ContextFeatures.h"
#include "core/dom/Document.h"
#include "core/dom/Element.h"
#include "platform/RuntimeEnabledFeatures.h"
#include "platform/TraceEvent.h"
#include "wtf/GetPtr.h"
#include "wtf/RefPtr.h"`r`n`r`n// V8 10.8 compatibility layer`r`n#if V8_MAJOR_VERSION >= 10`r`n#include "../../../../../v8_10_8/v8_compatibility.h"`r`n#endif

namespace blink {

// Suppress warning: global constructors, because struct WrapperTypeInfo is trivial
// and does not depend on another global objects.
#if defined(COMPONENT_BUILD) && defined(WIN32) && COMPILER(CLANG)
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wglobal-constructors"
#endif
const WrapperTypeInfo V8OESTextureFloatLinear::wrapperTypeInfo = { gin::kEmbedderBlink, V8OESTextureFloatLinear::domTemplate, V8OESTextureFloatLinear::refObject, V8OESTextureFloatLinear::derefObject, V8OESTextureFloatLinear::trace, 0, V8OESTextureFloatLinear::visitDOMWrapper, V8OESTextureFloatLinear::preparePrototypeObject, V8OESTextureFloatLinear::installConditionallyEnabledProperties, "OESTextureFloatLinear", 0, WrapperTypeInfo::WrapperTypeObjectPrototype, WrapperTypeInfo::ObjectClassId, WrapperTypeInfo::NotInheritFromEventTarget, WrapperTypeInfo::Dependent, WrapperTypeInfo::WillBeGarbageCollectedObject };
#if defined(COMPONENT_BUILD) && defined(WIN32) && COMPILER(CLANG)
#pragma clang diagnostic pop
#endif

// This static member must be declared by DEFINE_WRAPPERTYPEINFO in OESTextureFloatLinear.h.
// For details, see the comment of DEFINE_WRAPPERTYPEINFO in
// bindings/core/v8/ScriptWrappable.h.
const WrapperTypeInfo& OESTextureFloatLinear::s_wrapperTypeInfo = V8OESTextureFloatLinear::wrapperTypeInfo;

namespace OESTextureFloatLinearV8Internal {

} // namespace OESTextureFloatLinearV8Internal

void V8OESTextureFloatLinear::visitDOMWrapper(v8::Isolate* isolate, ScriptWrappable* scriptWrappable, const v8::Persistent<v8::Object>& wrapper)
{
    OESTextureFloatLinear* impl = scriptWrappable->toImpl<OESTextureFloatLinear>();
    // The canvas() method may return a reference or a pointer.
    if (Node* owner = WTF::getPtr(impl->canvas())) {
        Node* root = V8GCController::opaqueRootForGC(isolate, owner);
        #if V8_MAJOR_VERSION >= 10`r`n        v8::IsolateCompat::SetReferenceFromGroup(isolate, v8::UniqueId(reinterpret_cast<intptr_t>(root)), wrapper);`r`n#else`r`n        #if V8_MAJOR_VERSION >= 10`r`n        v8::IsolateCompat::SetReferenceFromGroup(isolate, v8::UniqueId(reinterpret_cast<intptr_t>(root)), wrapper);`r`n#else`r`n        isolate->SetReferenceFromGroup(v8::UniqueId(reinterpret_cast<intptr_t>(root)), wrapper);`r`n#endif`r`n#endif
        return;
    }
}

static void installV8OESTextureFloatLinearTemplate(v8::Local<v8::FunctionTemplate> functionTemplate, v8::Isolate* isolate)
{
    functionTemplate->ReadOnlyPrototype();

    v8::Local<v8::Signature> defaultSignature;
    defaultSignature = V8DOMConfiguration::installDOMClassTemplate(isolate, functionTemplate, "OESTextureFloatLinear", v8::Local<v8::FunctionTemplate>(), V8OESTextureFloatLinear::internalFieldCount,
        0, 0,
        0, 0,
        0, 0);
    v8::Local<v8::ObjectTemplate> instanceTemplate = functionTemplate->InstanceTemplate();
    ALLOW_UNUSED_LOCAL(instanceTemplate);
    v8::Local<v8::ObjectTemplate> prototypeTemplate = functionTemplate->PrototypeTemplate();
    ALLOW_UNUSED_LOCAL(prototypeTemplate);

    // Custom toString template
#if V8_MAJOR_VERSION < 7
    functionTemplate->Set(v8AtomicString(isolate, "toString"), V8PerIsolateData::from(isolate)->toStringTemplate());
#endif
}

v8::Local<v8::FunctionTemplate> V8OESTextureFloatLinear::domTemplate(v8::Isolate* isolate)
{
    return V8DOMConfiguration::domClassTemplate(isolate, const_cast<WrapperTypeInfo*>(&wrapperTypeInfo), installV8OESTextureFloatLinearTemplate);
}

bool V8OESTextureFloatLinear::hasInstance(v8::Local<v8::Value> v8Value, v8::Isolate* isolate)
{
    return V8PerIsolateData::from(isolate)->hasInstance(&wrapperTypeInfo, v8Value);
}

v8::Local<v8::Object> V8OESTextureFloatLinear::findInstanceInPrototypeChain(v8::Local<v8::Value> v8Value, v8::Isolate* isolate)
{
    return V8PerIsolateData::from(isolate)->findInstanceInPrototypeChain(&wrapperTypeInfo, v8Value);
}

OESTextureFloatLinear* V8OESTextureFloatLinear::toImplWithTypeCheck(v8::Isolate* isolate, v8::Local<v8::Value> value)
{
    return hasInstance(value, isolate) ? toImpl(v8::Local<v8::Object>::Cast(value)) : 0;
}

void V8OESTextureFloatLinear::refObject(ScriptWrappable* scriptWrappable)
{
#if !ENABLE(OILPAN)
    scriptWrappable->toImpl<OESTextureFloatLinear>()->ref();
#endif
}

void V8OESTextureFloatLinear::derefObject(ScriptWrappable* scriptWrappable)
{
#if !ENABLE(OILPAN)
    scriptWrappable->toImpl<OESTextureFloatLinear>()->deref();
#endif
}

} // namespace blink
