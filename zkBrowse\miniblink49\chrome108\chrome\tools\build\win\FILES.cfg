# -*- python -*-
# ex: set syntax=python:

# Copyright 2012 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# NOTE: This file is now obsolete for 'dev' and 'official' builders with
# respect to archiving. The new configs can be found in the json files within
# infra/archive_config for dev builders and
# src-internal/testing/buildbot/archive for official builders. crbug/1260176

# This is a buildbot configuration file containing a tagged list of files
# processed by the stage/archive scripts. The known tags are:
#
# filename: Name of the file in the build output directory.
# arch:     List of CPU architectures for which this file should be processed
#           Leave this unspecified to process for all architectures.
#           Acceptable values are 64bit, 32bit and arm.
# buildtype: List of build types for which this file should be processed.
# archive: The name of the archive file to store filename in. If not specified,
#          filename is added to the default archive (e.g. platform.zip). If
#          archive == filename, filename is archived directly, not zipped.
# direct_archive: Force a file to be archived as-is, bypassing zip creation.
#                 NOTE: This flag will not apply if more than one file has the
#                 same 'archive' name, which will create a zip of all the
#                 files instead.
# filegroup: List of named groups to which this file belongs.
#            default: Legacy "default archive". TODO(mmoss): These should
#                     be updated to specify an 'archive' name and then this
#                     filegroup and the related archive_utils.ParseLegacyList()
#                     should go away.
#            symsrc: Files to upload to the symbol server.
# optional: List of buildtypes for which the file might not exist, and it's not
#           considered an error.

FILES = [
  {
    'filename': 'browser_tests.exe',
    'buildtype': ['official'],
    'archive':  'browser_tests.exe',
    'optional': ['official'],
  },
  {
    'filename': 'sync_integration_tests.exe',
    'buildtype': ['official'],
    'archive':  'sync_integration_tests.exe',
    'optional': ['official'],
  },
  {
    'filename': 'chrome.exe',
    'buildtype': ['official'],
    'filegroup': ['default', 'symsrc'],
  },
  {
    'filename': 'nacl64.exe',
    'arch': ['32bit'],
    'buildtype': ['official'],
    'filegroup': ['default', 'symsrc'],
  },
  {
    'filename': 'chrome.dll',
    'buildtype': ['official'],
    'filegroup': ['default', 'symsrc'],
  },
  {
    'filename': 'chrome_child.dll',
    'buildtype': ['official'],
    'filegroup': ['default', 'symsrc'],
    'optional': ['official'],
  },
  {
    'filename': 'chrome_elf.dll',
    'buildtype': ['official'],
    'filegroup': ['default', 'symsrc'],
  },
  {
    'filename': 'chrome_wer.dll',
    'buildtype': ['official'],
    'filegroup': ['default', 'symsrc'],
  },
  {
    'filename': 'eventlog_provider.dll',
    'buildtype': ['official'],
    'filegroup': ['default'],
  },
  {
    'filename': '*.manifest',
    'buildtype': ['official'],
    'filegroup': ['default'],
  },
  {
    'filename': 'chrome_100_percent.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'chrome_200_percent.pak',
    'buildtype': ['official'],
    'optional': ['official'],
  },
  {
    'filename': 'First Run',
    'buildtype': ['official'],
  },
  {
    'filename': 'icudtl.dat',
    'buildtype': ['official'],
    'optional': ['official'],
  },
  {
    'filename': 'icudt.dll',
    'buildtype': ['official'],
    'optional': ['official'],
  },
  {
    'filename': 'mojo_core.dll',
    'buildtype': ['official'],
    'optional': ['official'],
    'filegroup': ['default', 'symsrc'],
  },
  {
    'filename': 'v8_context_snapshot.bin',
    'buildtype': ['official'],
    'optional': ['official'],
  },
  {
    'filename': 'locales/ar.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/bg.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/bn.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/ca.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/cs.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/da.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/de.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/el.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/en-GB.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/en-US.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/es-419.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/es.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/et.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/fi.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/fil.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/fr.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/gu.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/he.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/hi.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/hr.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/hu.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/id.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/it.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/ja.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/kn.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/ko.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/lt.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/lv.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/ml.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/mr.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/ms.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/nb.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/nl.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/pl.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/pt-BR.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/pt-PT.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/ro.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/ru.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/sk.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/sl.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/sr.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/sv.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/ta.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/te.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/th.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/tr.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/uk.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/vi.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/zh-CN.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'locales/zh-TW.pak',
    'buildtype': ['official'],
  },
  {
    'filename': 'policy_templates.zip',
    'buildtype': ['official'],
    'archive': 'policy_templates.zip',
  },
  {
    'filename': 'resources.pak',
    'buildtype': ['official'],
  },
  # PNaCl translator (archive only, component updater used for shipping).
  {
    'filename': 'pnacl',
    'buildtype': ['official'],
    'archive': 'pnacl.zip',
  },
  # Widevine CDM files:
  {
    'filename': 'WidevineCdm/manifest.json',
    'buildtype': ['official'],
  },
  {
    'filename': 'WidevineCdm/LICENSE',
    'buildtype': ['official'],
  },
  {
    'filename': 'WidevineCdm/_platform_specific/win_x86/widevinecdm.dll',
    'arch': ['32bit'],
    'buildtype': ['official'],
  },
  {
    'filename': 'WidevineCdm/_platform_specific/win_x86/widevinecdm.dll.sig',
    'arch': ['32bit'],
    'buildtype': ['official'],
  },
  {
    'filename': 'WidevineCdm/_platform_specific/win_x64/widevinecdm.dll',
    'arch': ['64bit'],
    'buildtype': ['official'],
  },
  {
    'filename': 'WidevineCdm/_platform_specific/win_x64/widevinecdm.dll.sig',
    'arch': ['64bit'],
    'buildtype': ['official'],
  },
  # ANGLE files:
  {
    'filename': 'D3DCompiler_47.dll',
    'buildtype': ['official'],
  },
  {
    'filename': 'libEGL.dll',
    'buildtype': ['official'],
    'filegroup': ['default', 'symsrc'],
  },
  {
    'filename': 'libGLESv2.dll',
    'buildtype': ['official'],
    'filegroup': ['default', 'symsrc'],
  },
  {
    'filename': 'vulkan-1.dll',
    'buildtype': ['official'],
    'filegroup': ['default', 'symsrc'],
  },
  # SwiftShader files:
  {
    'filename': 'vk_swiftshader.dll',
    'buildtype': ['official'],
    'filegroup': ['default', 'symsrc'],
  },
  {
    'filename': 'vk_swiftshader_icd.json',
    'buildtype': ['official'],
  },
  # Native Client plugin files:
  {
    'filename': 'nacl_irt_x86_32.nexe',
    'arch': ['32bit'],
    'buildtype': ['official'],
  },
  {
    'filename': 'nacl_irt_x86_64.nexe',
    'buildtype': ['official'],
  },
  # Remoting files:
  {
    'filename': 'chromoting.msi',
    'arch': ['32bit'],
    'buildtype': ['official'],
    'archive': 'remoting-host.msi',
    'direct_archive': 1,
    'optional': ['dev'],
  },
  {
    'filename': 'remoting-me2me-host-win.zip',
    'arch': ['32bit'],
    'buildtype': ['official'],
    'archive': 'remoting-me2me-host-win.zip',
    'direct_archive': 1,
    'optional': ['dev'],
  },
  {
    'filename': 'remoting-me2me-host-win-unsupported.zip',
    'arch': ['64bit'],
    'buildtype': ['official'],
    'archive': 'remoting-me2me-host-win-unsupported.zip',
    'direct_archive': 1,
    'optional': ['dev'],
  },
  {
    'filename': 'remote_assistance_host.exe',
    'buildtype': ['official'],
    'archive': 'remoting-win32.zip',
    'filegroup': ['symsrc'],
  },
  {
    'filename': 'remote_assistance_host.exe.pdb',
    'buildtype': ['official'],
    'archive': 'remoting-win32.zip',
  },
  {
    'filename': 'remote_assistance_host_uiaccess.exe',
    'buildtype': ['official'],
    'archive': 'remoting-win32.zip',
    'filegroup': ['symsrc'],
  },
  {
    'filename': 'remote_assistance_host_uiaccess.exe.pdb',
    'buildtype': ['official'],
    'archive': 'remoting-win32.zip',
  },
  {
    'filename': 'remote_security_key.exe',
    'buildtype': ['official'],
    'archive': 'remoting-win32.zip',
    'filegroup': ['symsrc'],
  },
  {
    'filename': 'remote_security_key.exe.pdb',
    'buildtype': ['official'],
    'archive': 'remoting-win32.zip',
  },
  {
    'filename': 'remoting_core.dll',
    'buildtype': ['official'],
    'archive': 'remoting-win32.zip',
    'filegroup': ['symsrc'],
  },
  {
    'filename': 'remoting_core.dll.pdb',
    'buildtype': ['official'],
    'archive': 'remoting-win32.zip',
    'optional': ['official'],
  },
  {
    'filename': 'remoting_desktop.exe',
    'buildtype': ['official'],
    'archive': 'remoting-win32.zip',
    'filegroup': ['symsrc'],
  },
  {
    'filename': 'remoting_desktop.exe.pdb',
    'buildtype': ['official'],
    'archive': 'remoting-win32.zip',
    'optional': ['official'],
  },
  {
    'filename': 'remoting_host.exe',
    'buildtype': ['official'],
    'archive': 'remoting-win32.zip',
    'filegroup': ['symsrc'],
  },
  {
    'filename': 'remoting_host.exe.pdb',
    'buildtype': ['official'],
    'archive': 'remoting-win32.zip',
  },
  {
    'filename': 'remoting_native_messaging_host.exe',
    'buildtype': ['official'],
    'archive': 'remoting-win32.zip',
    'filegroup': ['symsrc'],
  },
  {
    'filename': 'remoting_native_messaging_host.exe.pdb',
    'buildtype': ['official'],
    'archive': 'remoting-win32.zip',
  },
  {
    'filename': 'remoting_start_host.exe',
    'buildtype': ['official'],
    'archive': 'remoting-win32.zip',
    'filegroup': ['symsrc'],
  },
  {
    'filename': 'remoting_start_host.exe.pdb',
    'buildtype': ['official'],
    'archive': 'remoting-win32.zip',
  },
  # Credential Provider:
  {
    'filename': 'gcp_setup.exe',
    'buildtype': ['official'],
    'optional': ['official'],
    'filegroup': ['symsrc'],
  },
  {
    'filename': 'gcp_setup.exe.pdb',
    'buildtype': ['official'],
    'optional': ['official'],
    'archive': 'chrome-win32-syms.zip',
  },
  {
    'filename': 'gaia1_0.dll',
    'buildtype': ['official'],
    'optional': ['official'],
    'filegroup': ['symsrc'],
  },
  {
    'filename': 'gaia1_0.dll.pdb',
    'buildtype': ['official'],
    'optional': ['official'],
    'archive': 'chrome-win32-syms.zip',
  },
  {
    'filename': 'gcp_installer.exe',
    'buildtype': ['official'],
    'archive': 'gcp_installer.exe',
    'filegroup': ['symsrc'],
  },
  # Test binaries for external QA:
  {
    'filename': 'interactive_ui_tests.exe',
    'buildtype': ['official'],
    'optional': ['official'],
  },
  # Notification helper files:
  {
    'filename': 'notification_helper.exe',
    'buildtype': ['official'],
    'filegroup': ['default', 'symsrc'],
  },
  {
    'filename': 'notification_helper.exe.pdb',
    'buildtype': ['official'],
    'archive': 'chrome-win32-syms.zip',
  },
  # Installer files (official build only):
  {
    'filename': 'setup.exe',
    'buildtype': ['official'],
    'archive': 'setup.exe',
    'filegroup': ['symsrc'],
  },
  {
    'filename': 'mini_installer.exe',
    'buildtype': ['official'],
    'archive': 'mini_installer.exe',
    'filegroup': ['symsrc'],
  },
  {
    'filename': 'chrome.packed.7z',
    'buildtype': ['official'],
    'archive': 'chrome.packed.7z',
  },
  {
    'filename': 'mini_installer_exe_version.rc',
    'buildtype': ['official'],
    'archive': 'mini_installer_exe_version.rc',
  },
  {
    'filename': 'courgette.exe',
    'buildtype': ['official'],
    'archive': 'courgette.exe',
  },
  {
    'filename': 'courgette64.exe',
    'buildtype': ['official'],
    'archive': 'courgette64.exe',
  },
  {
    'filename': 'zucchini.exe',
    'buildtype': ['official'],
    'optional': ['official'],
    'archive': 'zucchini.exe',
  },
  {
    'filename': 'zucchini.exe.pdb',
    'buildtype': ['official'],
    'optional': ['official'],
    'archive': 'chrome-win32-syms.zip',
  },
  {
    'filename': 'chrome.dll.pdb',
    'buildtype': ['official'],
    'archive': 'chrome-win32-syms.zip',
  },
  {
    'filename': 'chrome_child.dll.pdb',
    'buildtype': ['official'],
    'optional': ['official'],
    'archive': 'chrome-win32-syms.zip',
  },
  {
    'filename': 'chrome_elf.dll.pdb',
    'buildtype': ['official'],
    'archive': 'chrome-win32-syms.zip',
  },
  {
    'filename': 'chrome_wer.dll.pdb',
    'buildtype': ['official'],
    'archive': 'chrome-win32-syms.zip',
  },
  {
    'filename': 'chrome.exe.pdb',
    'buildtype': ['official'],
    'archive': 'chrome-win32-syms.zip',
  },
  {
    'filename': 'eventlog_provider.dll.pdb',
    'buildtype': ['official'],
    'archive': 'chrome-win32-syms.zip',
  },
  {
    'filename': 'libEGL.dll.pdb',
    'buildtype': ['official'],
    'archive': 'chrome-win32-syms.zip',
  },
  {
    'filename': 'libGLESv2.dll.pdb',
    'buildtype': ['official'],
    'archive': 'chrome-win32-syms.zip',
  },
  {
    'filename': 'vulkan-1.dll.pdb',
    'buildtype': ['official'],
    'archive': 'chrome-win32-syms.zip',
  },
  {
     'filename': 'mojo_core.dll.pdb',
     'buildtype': ['official'],
     'archive': 'chrome-win32-syms.zip',
  },
  {
    'filename': 'vk_swiftshader.dll.pdb',
    'buildtype': ['official'],
    'archive': 'chrome-win32-syms.zip',
  },
  {
    'filename': 'mini_installer.exe.pdb',
    'buildtype': ['official'],
    'archive': 'chrome-win32-syms.zip',
  },
  {
    'filename': 'nacl64.exe.pdb',
    'arch': ['32bit'],
    'buildtype': ['official'],
    'archive': 'chrome-win32-syms.zip',
  },
  {
    'filename': 'setup.exe.pdb',
    'buildtype': ['official'],
    'archive': 'chrome-win32-syms.zip',
  },
  # Updater files:
  {
    'filename': 'updater.zip',
    'buildtype': ['official'],
    'optional': ['official'],
    'archive': 'updater.zip'
  },
  # Partner API files.
  {
    'filename': 'gcapi.h',
    'buildtype': ['official'],
    'archive': 'gcapi.zip',
  },
  {
    'filename': 'gcapi_dll.dll',
    'buildtype': ['official'],
    'archive': 'gcapi.zip',
    'filegroup': ['symsrc'],
  },
  {
    'filename': 'gcapi_dll.dll.lib',
    'buildtype': ['official'],
    'archive': 'gcapi.zip',
  },
  {
    'filename': 'gcapi_dll.dll.pdb',
    'buildtype': ['official'],
    'archive': 'chrome-win32-syms.zip',
  },
  {
    'filename': 'nacl_irt_x86_32.nexe.debug',
    'arch': ['32bit'],
    'buildtype': ['official'],
    'archive': 'chrome-win32-nacl-irt-syms.zip',
  },
  {
    'filename': 'nacl_irt_x86_64.nexe.debug',
    'buildtype': ['official'],
    'archive': 'chrome-win32-nacl-irt-syms.zip',
  },
  # Metrics metadata files:
  {
    'filename': 'actions.xml',
    'buildtype': ['official'],
    'archive': 'metrics-metadata.zip',
    'optional': ['official'],
  },
  {
    'filename': 'histograms.xml',
    'buildtype': ['official'],
    'archive': 'metrics-metadata.zip',
    'optional': ['official'],
  },
  {
    'filename': 'ukm.xml',
    'buildtype': ['official'],
    'archive': 'metrics-metadata.zip',
    'optional': ['official'],
  },
  # MEI Preload files:
  {
    'filename': 'MEIPreload/manifest.json',
    'buildtype': ['official'],
  },
  {
    'filename': 'MEIPreload/preloaded_data.pb',
    'buildtype': ['official'],
  },
  # ChromeDriver binary:
  {
    'filename': 'chromedriver.exe',
    'arch': ['32bit'],
    'buildtype': ['official'],
    'archive': 'chromedriver_win32.zip',
    'optional': ['official'],
    'filegroup': ['symsrc'],
  },
  {
    'filename': 'chromedriver.exe.pdb',
    'buildtype': ['official'],
    'archive': 'chromedriver_win32-syms.zip',
    'optional': ['official'],
  },
  # Elevation service files:
  {
    'filename': 'elevation_service.exe',
    'buildtype': ['official'],
    'filegroup': ['default', 'symsrc'],
  },
  {
    'filename': 'elevation_service.exe.pdb',
    'buildtype': ['official'],
    'archive': 'chrome-win32-syms.zip',
  },
  # Bookmark apps shortcut target:
  {
    'filename': 'chrome_proxy.exe',
    'buildtype': ['official'],
    'filegroup': ['default', 'symsrc'],
  },
  {
    'filename': 'chrome_proxy.exe.pdb',
    'buildtype': ['official'],
    'archive': 'chrome-win32-syms.zip',
  },
  # DevTools front-end (internal) files:
  {
    'filename': 'gen/third_party/devtools-frontend-internal/devtools-frontend/front_end',
    'buildtype': ['official'],
    'archive': 'devtools-frontend.zip',
  },
  # Policy cloud documentation source files:
  {
    'filename': 'gen/chrome/app/policy/translations/policy_templates_de-DE.json',
    'buildtype': ['official'],
    'archive': 'policy_templates_de-DE.json',
    'direct_archive': 1,
    'optional': ['official'],
  },
  {
    'filename': 'gen/chrome/app/policy/translations/policy_templates_en-US.json',
    'buildtype': ['official'],
    'archive': 'policy_templates_en-US.json',
    'direct_archive': 1,
    'optional': ['official'],
  },
  {
    'filename': 'gen/chrome/app/policy/translations/policy_templates_es-419.json',
    'buildtype': ['official'],
    'archive': 'policy_templates_es-419.json',
    'direct_archive': 1,
    'optional': ['official'],
  },
  {
    'filename': 'gen/chrome/app/policy/translations/policy_templates_es-ES.json',
    'buildtype': ['official'],
    'archive': 'policy_templates_es-ES.json',
    'direct_archive': 1,
    'optional': ['official'],
  },
  {
    'filename': 'gen/chrome/app/policy/translations/policy_templates_fr-FR.json',
    'buildtype': ['official'],
    'archive': 'policy_templates_fr-FR.json',
    'direct_archive': 1,
    'optional': ['official'],
  },
  {
    'filename': 'gen/chrome/app/policy/translations/policy_templates_id-ID.json',
    'buildtype': ['official'],
    'archive': 'policy_templates_id-ID.json',
    'direct_archive': 1,
    'optional': ['official'],
  },
  {
    'filename': 'gen/chrome/app/policy/translations/policy_templates_it-IT.json',
    'buildtype': ['official'],
    'archive': 'policy_templates_it-IT.json',
    'direct_archive': 1,
    'optional': ['official'],
  },
  {
    'filename': 'gen/chrome/app/policy/translations/policy_templates_ja-JP.json',
    'buildtype': ['official'],
    'archive': 'policy_templates_ja-JP.json',
    'direct_archive': 1,
    'optional': ['official'],
  },
  {
    'filename': 'gen/chrome/app/policy/translations/policy_templates_ko-KR.json',
    'buildtype': ['official'],
    'archive': 'policy_templates_ko-KR.json',
    'direct_archive': 1,
    'optional': ['official'],
  },
  {
    'filename': 'gen/chrome/app/policy/translations/policy_templates_nl-NL.json',
    'buildtype': ['official'],
    'archive': 'policy_templates_nl-NL.json',
    'direct_archive': 1,
    'optional': ['official'],
  },
  {
    'filename': 'gen/chrome/app/policy/translations/policy_templates_pt-BR.json',
    'buildtype': ['official'],
    'archive': 'policy_templates_pt-BR.json',
    'direct_archive': 1,
    'optional': ['official'],
  },
  {
    'filename': 'gen/chrome/app/policy/translations/policy_templates_ru-RU.json',
    'buildtype': ['official'],
    'archive': 'policy_templates_ru-RU.json',
    'direct_archive': 1,
    'optional': ['official'],
  },
  {
    'filename': 'gen/chrome/app/policy/translations/policy_templates_th-TH.json',
    'buildtype': ['official'],
    'archive': 'policy_templates_th-TH.json',
    'direct_archive': 1,
    'optional': ['official'],
  },
  {
    'filename': 'gen/chrome/app/policy/translations/policy_templates_tr-TR.json',
    'buildtype': ['official'],
    'archive': 'policy_templates_tr-TR.json',
    'direct_archive': 1,
    'optional': ['official'],
  },
  {
    'filename': 'gen/chrome/app/policy/translations/policy_templates_uk-UA.json',
    'buildtype': ['official'],
    'archive': 'policy_templates_uk-UA.json',
    'direct_archive': 1,
    'optional': ['official'],
  },
  {
    'filename': 'gen/chrome/app/policy/translations/policy_templates_vi-VN.json',
    'buildtype': ['official'],
    'archive': 'policy_templates_vi-VN.json',
    'direct_archive': 1,
    'optional': ['official'],
  },
  {
    'filename': 'gen/chrome/app/policy/translations/policy_templates_zh-CN.json',
    'buildtype': ['official'],
    'archive': 'policy_templates_zh-CN.json',
    'direct_archive': 1,
    'optional': ['official'],
  },
  {
    'filename': 'gen/chrome/app/policy/translations/policy_templates_zh-TW.json',
    'buildtype': ['official'],
    'archive': 'policy_templates_zh-TW.json',
    'direct_archive': 1,
    'optional': ['official'],
  },
  # Progressive Web App launcher executable:
  {
    'filename': 'chrome_pwa_launcher.exe',
    'buildtype': ['official'],
    'filegroup': ['default', 'symsrc'],
  },
  {
    'filename': 'chrome_pwa_launcher.exe.pdb',
    'buildtype': ['official'],
    'archive': 'chrome-win32-syms.zip',
  },
  # Hyphenation dictionaries:
  {
    'filename': 'hyphens-data.zip',
    'buildtype': ['official'],
    'archive': 'hyphens-data.zip',
  },
]
