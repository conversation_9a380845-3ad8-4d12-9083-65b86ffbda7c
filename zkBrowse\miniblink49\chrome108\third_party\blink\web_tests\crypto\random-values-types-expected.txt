Tests which types are valid for crypto.randomValues.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".

PASS 'crypto' in self is true
PASS 'getRandomValues' in self.crypto is true
PASS crypto.getRandomValues() threw exception TypeError: Failed to execute 'getRandomValues' on 'Crypto': 1 argument required, but only 0 present..
PASS crypto.getRandomValues(undefined) threw exception TypeError: Failed to execute 'getRandomValues' on 'Crypto': parameter 1 is not of type 'ArrayBufferView'..
PASS crypto.getRandomValues(null) threw exception TypeError: Failed to execute 'getRandomValues' on 'Crypto': parameter 1 is not of type 'ArrayBufferView'..
PASS random = crypto.getRandomValues(new Uint8Array(3)) is defined.
PASS random is an instance of Uint8Array
PASS view = new Uint8Array(3) is defined.
PASS random = crypto.getRandomValues(view) is defined.
PASS random is view
PASS random = crypto.getRandomValues(new Int8Array(3)) is defined.
PASS random is an instance of Int8Array
PASS view = new Int8Array(3) is defined.
PASS random = crypto.getRandomValues(view) is defined.
PASS random is view
PASS random = crypto.getRandomValues(new Uint8ClampedArray(3)) is defined.
PASS random is an instance of Uint8ClampedArray
PASS view = new Uint8ClampedArray(3) is defined.
PASS random = crypto.getRandomValues(view) is defined.
PASS random is view
PASS random = crypto.getRandomValues(new Uint16Array(3)) is defined.
PASS random is an instance of Uint16Array
PASS view = new Uint16Array(3) is defined.
PASS random = crypto.getRandomValues(view) is defined.
PASS random is view
PASS random = crypto.getRandomValues(new Int16Array(3)) is defined.
PASS random is an instance of Int16Array
PASS view = new Int16Array(3) is defined.
PASS random = crypto.getRandomValues(view) is defined.
PASS random is view
PASS random = crypto.getRandomValues(new Uint32Array(3)) is defined.
PASS random is an instance of Uint32Array
PASS view = new Uint32Array(3) is defined.
PASS random = crypto.getRandomValues(view) is defined.
PASS random is view
PASS random = crypto.getRandomValues(new Int32Array(3)) is defined.
PASS random is an instance of Int32Array
PASS view = new Int32Array(3) is defined.
PASS random = crypto.getRandomValues(view) is defined.
PASS random is view
PASS crypto.getRandomValues(new Float32Array(3)) threw exception TypeMismatchError: Failed to execute 'getRandomValues' on 'Crypto': The provided ArrayBufferView is of type 'Float32', which is not an integer array type..
PASS crypto.getRandomValues(new Float64Array(3)) threw exception TypeMismatchError: Failed to execute 'getRandomValues' on 'Crypto': The provided ArrayBufferView is of type 'Float64', which is not an integer array type..
PASS buffer = new Uint8Array(32) is defined.
PASS buffer.buffer is defined.
PASS view = new DataView(buffer.buffer) is defined.
PASS crypto.getRandomValues(view) threw exception TypeMismatchError: Failed to execute 'getRandomValues' on 'Crypto': The provided ArrayBufferView is of type 'DataView', which is not an integer array type..
PASS successfullyParsed is true

TEST COMPLETE

