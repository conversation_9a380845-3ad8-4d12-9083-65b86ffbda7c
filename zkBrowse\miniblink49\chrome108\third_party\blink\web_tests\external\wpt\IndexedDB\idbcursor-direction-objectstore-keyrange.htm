<!DOCTYPE html>
<meta charset=utf-8>
<title>IDBCursor direction - object store with keyrange</title>
<link rel="author" href="mailto:<EMAIL>" title="Odin H<PERSON> O<PERSON>">
<link rel=help href="http://dvcs.w3.org/hg/IndexedDB/raw-file/tip/Overview.html#cursor-iteration-operation">
<link rel=assert title='If direction is "next", let found record be the first record in records which satisfy all of the following requirements'>
<link rel=assert title='If direction is "prev", let found record be the last record in records which satisfy all of the following requirements'>
<link rel=assert title="If range is defined, the record's key is in range.">
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<script src="resources/support.js"></script>

<script>
var records = [ 1337, "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", ["<PERSON>"] ];
var directions = ["next", "prev", "nextunique", "prevunique"];
var cases = [
  {dir: 'next', expect: ['<PERSON>', '<PERSON>', '<PERSON>']},
  {dir: 'prev', expect: ['<PERSON>', '<PERSON>', '<PERSON>']},
  {dir: 'nextunique', expect: ['Alice', 'Bob', 'Greg']},
  {dir: 'prevunique', expect: ['Greg', 'Bob', 'Alice']},
];

cases.forEach(function(testcase) {
  var dir = testcase.dir;
  var expect = testcase.expect;
  indexeddb_test(
    function(t, db, tx) {
        var objStore = db.createObjectStore("test");
        for (var i = 0; i < records.length; i++)
          objStore.add(records[i], records[i]);
    },
    function(t, db) {
      var count = 0;
      var rq = db.transaction("test").objectStore("test").openCursor(IDBKeyRange.bound("AA", "ZZ"), dir);
      rq.onsuccess = t.step_func(function(e) {
        var cursor = e.target.result;
        if (!cursor) {
          assert_equals(count, expect.length, "cursor runs");
          t.done();
        }
        assert_equals(cursor.value, expect[count], "cursor.value");
        count++;
        cursor.continue();
      });
      rq.onerror = t.step_func(function(e) {
        e.preventDefault();
        e.stopPropagation();
        assert_unreached("rq.onerror - " + e.message);
      });
    },
    document.title + ' - ' + dir
  );
});
</script>
