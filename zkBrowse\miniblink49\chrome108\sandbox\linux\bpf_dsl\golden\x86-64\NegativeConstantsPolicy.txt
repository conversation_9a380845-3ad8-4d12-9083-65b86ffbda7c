  1) LOAD 4  // Architecture
  2) if A == 0xc000003e; then JMP 3 else JMP 13
  3) LOAD 0  // System call number
  4) if A & 0x40000000; then JMP 13 else JMP 5
  5) if A >= 0x49; then JMP 6 else JMP 7
  6) if A >= 0x401; then JMP 18 else JMP 17
  7) if A >= 0x48; then JMP 8 else JMP 17
  8) LOAD 20  // Argument 0 (MSB)
  9) if A == 0x0; then JMP 14 else JMP 10
 10) if A == 0xffffffff; then JMP 11 else JMP 13
 11) LOAD 16  // Argument 0 (LSB)
 12) if A & 0x80000000; then JMP 14 else JMP 13
 13) RET 0x0  // Kill
 14) LOAD 16  // Argument 0 (LSB)
 15) if A == 0xfffffec6; then JMP 16 else JMP 17
 16) RET 0x50001  // errno = 1
 17) RET 0x7fff0000  // Allowed
 18) RET 0x50026  // errno = 38
