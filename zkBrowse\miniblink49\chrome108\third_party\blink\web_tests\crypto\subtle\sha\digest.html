<!DOCTYPE html>
<html>
<head>
<script src="../../../resources/js-test.js"></script>
<script src="../resources/common.js"></script>
</head>
<body>
<p id="description"></p>
<div id="console"></div>

<script>
description("Tests the digest() operation for SHA-*");

jsTestIsAsync = true;

// These SHA-* test vectors were taking from:
// http://csrc.nist.gov/groups/STM/cavp/documents/shs/shabytetestvectors.zip
//
// This is not intended to be an exhaustive test, but rather give basic
// confidence that things work.
//
// Both inputs and outputs are written as a hex-encoded string.
kDigestTestVectors = [
  {
    algorithm: "SHA-1",
    input: "",
    output: "da39a3ee5e6b4b0d3255bfef95601890afd80709"
  },
  {
    algorithm: "SHA-256",
    input: "",
    output: "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"
  },
  {
    algorithm: "SHA-384",
    input: "",
    output: "38b060a751ac96384cd9327eb1b1e36a21fdb71114be07434c0cc7bf63f6e1da274edebfe76f65fbd51ad2f14898b95b"
  },
  {
    algorithm: "SHA-512",
    input: "",
    output: "cf83e1357eefb8bdf1542850d66d8007d620e4050b5715dc83f4a921d36ce9ce47d0d13c5d85f2b0ff8318d2877eec2f63b931bd47417a81a538327af927da3e"
  },
  {
    algorithm: "SHA-1",
    input: "00",
    output: "5ba93c9db0cff93f52b521d7420e43f6eda2784f"
  },
  {
    algorithm: "SHA-256",
    input: "00",
    output: "6e340b9cffb37a989ca544e6bb780a2c78901d3fb33738768511a30617afa01d"
  },
  {
    algorithm: "SHA-384",
    input: "00",
    output: "bec021b4f368e3069134e012c2b4307083d3a9bdd206e24e5f0d86e13d6636655933ec2b413465966817a9c208a11717"
  },
  {
    algorithm: "SHA-512",
    input: "00",
    output: "b8244d028981d693af7b456af8efa4cad63d282e19ff14942c246e50d9351d22704a802a71c3580b6370de4ceb293c324a8423342557d4e5c38438f0e36910ee"
  },
  {
    algorithm: "SHA-1",
    input: "000102030405",
    output: "868460d98d09d8bbb93d7b6cdd15cc7fbec676b9"
  },
  {
    algorithm: "SHA-256",
    input: "000102030405",
    output: "17e88db187afd62c16e5debf3e6527cd006bc012bc90b51a810cd80c2d511f43"
  },
  {
    algorithm: "SHA-384",
    input: "000102030405",
    output: "79f4738706fce9650ac60266675c3cd07298b09923850d525604d040e6e448adc7dc22780d7e1b95bfeaa86a678e4552"
  },
  {
    algorithm: "SHA-512",
    input: "000102030405",
    output: "2f3831bccc94cf061bcfa5f8c23c1429d26e3bc6b76edad93d9025cb91c903af6cf9c935dc37193c04c2c66e7d9de17c358284418218afea2160147aaa912f4c"
  },
];

function runTest(testCase)
{
    return crypto.subtle.digest({name: testCase.algorithm}, hexStringToUint8Array(testCase.input)).then(function(result) {
        var testDescription = testCase.algorithm + " of [" + testCase.input + "]";
        bytesShouldMatchHexString(testDescription, testCase.output, result);
    });
}

var lastPromise = Promise.resolve(null);

kDigestTestVectors.forEach(function(test) {
    lastPromise = lastPromise.then(runTest.bind(null, test));
});

lastPromise.then(finishJSTest, failAndFinishJSTest);

</script>

</body>
</html>
