<!DOCTYPE HTML>
<title>test behavior of PROXY configuration (PAC)</title>
<meta name="pac" content="resources/proxy.sub.pac">
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<script>
    promise_test(async t => {
        const response = await fetch('http://not-a-real-domain.wpt.test/infrastructure/resources/ok.txt');
        const text = await response.text();
        assert_equals(text, 'OK');
    }, 'test that PAC metadata is respected');
</script>
