[input-events-exec-command.html]
  [Calling execCommand("insertHorizontalRule", false, null) (inputType value)]
    expected: FAIL

  [Calling execCommand("backColor", false, #FF0000) (inputType value)]
    expected: FAIL

  [Calling execCommand("backColor", false, #FF0000) (data value)]
    expected: FAIL

  [Calling execCommand("backColor", false, #00FF00FF) (inputType value)]
    expected: FAIL

  [Calling execCommand("backColor", false, #00FF00FF) (data value)]
    expected: FAIL

  [Calling execCommand("backColor", false, #0000FF88) (inputType value)]
    expected: FAIL

  [Calling execCommand("backColor", false, #0000FF88) (data value)]
    expected: FAIL

  [Calling execCommand("backColor", false, orange) (inputType value)]
    expected: FAIL

  [Calling execCommand("backColor", false, orange) (data value)]
    expected: FAIL

  [Calling execCommand("backColor", false, Inherit) (inputType value)]
    expected: FAIL

  [Calling execCommand("backColor", false, Inherit) (data value)]
    expected: FAIL

  [Calling execCommand("backColor", false, Initial) (inputType value)]
    expected: FAIL

  [Calling execCommand("backColor", false, Initial) (data value)]
    expected: FAIL

  [Calling execCommand("backColor", false, Reset) (inputType value)]
    expected: FAIL

  [Calling execCommand("backColor", false, Reset) (data value)]
    expected: FAIL

  [Calling execCommand("backColor", false, transparent) (inputType value)]
    expected: FAIL

  [Calling execCommand("backColor", false, transparent) (data value)]
    expected: FAIL

  [Calling execCommand("backColor", false, CurrentColor) (inputType value)]
    expected: FAIL

  [Calling execCommand("backColor", false, CurrentColor) (data value)]
    expected: FAIL

  [Calling execCommand("backColor", false, Invalid-Value) (inputType value)]
    expected: FAIL

  [Calling execCommand("backColor", false, Invalid-Value) (data value)]
    expected: FAIL

  [Calling execCommand("foreColor", false, #FF0000) (inputType value)]
    expected: FAIL

  [Calling execCommand("foreColor", false, #FF0000) (data value)]
    expected: FAIL

  [Calling execCommand("foreColor", false, #00FF00FF) (inputType value)]
    expected: FAIL

  [Calling execCommand("foreColor", false, #00FF00FF) (data value)]
    expected: FAIL

  [Calling execCommand("foreColor", false, #0000FF88) (inputType value)]
    expected: FAIL

  [Calling execCommand("foreColor", false, #0000FF88) (data value)]
    expected: FAIL

  [Calling execCommand("foreColor", false, orange) (inputType value)]
    expected: FAIL

  [Calling execCommand("foreColor", false, orange) (data value)]
    expected: FAIL

  [Calling execCommand("foreColor", false, Inherit) (inputType value)]
    expected: FAIL

  [Calling execCommand("foreColor", false, Inherit) (data value)]
    expected: FAIL

  [Calling execCommand("foreColor", false, Initial) (inputType value)]
    expected: FAIL

  [Calling execCommand("foreColor", false, Initial) (data value)]
    expected: FAIL

  [Calling execCommand("foreColor", false, Reset) (inputType value)]
    expected: FAIL

  [Calling execCommand("foreColor", false, Reset) (data value)]
    expected: FAIL

  [Calling execCommand("foreColor", false, transparent) (inputType value)]
    expected: FAIL

  [Calling execCommand("foreColor", false, transparent) (data value)]
    expected: FAIL

  [Calling execCommand("foreColor", false, CurrentColor) (inputType value)]
    expected: FAIL

  [Calling execCommand("foreColor", false, CurrentColor) (data value)]
    expected: FAIL

  [Calling execCommand("foreColor", false, Invalid-Value) (inputType value)]
    expected: FAIL

  [Calling execCommand("foreColor", false, Invalid-Value) (data value)]
    expected: FAIL

  [Calling execCommand("hiliteColor", false, #FF0000) (inputType value)]
    expected: FAIL

  [Calling execCommand("hiliteColor", false, #FF0000) (data value)]
    expected: FAIL

  [Calling execCommand("hiliteColor", false, #00FF00FF) (inputType value)]
    expected: FAIL

  [Calling execCommand("hiliteColor", false, #00FF00FF) (data value)]
    expected: FAIL

  [Calling execCommand("hiliteColor", false, #0000FF88) (inputType value)]
    expected: FAIL

  [Calling execCommand("hiliteColor", false, #0000FF88) (data value)]
    expected: FAIL

  [Calling execCommand("hiliteColor", false, orange) (inputType value)]
    expected: FAIL

  [Calling execCommand("hiliteColor", false, orange) (data value)]
    expected: FAIL

  [Calling execCommand("hiliteColor", false, Inherit) (inputType value)]
    expected: FAIL

  [Calling execCommand("hiliteColor", false, Inherit) (data value)]
    expected: FAIL

  [Calling execCommand("hiliteColor", false, Initial) (inputType value)]
    expected: FAIL

  [Calling execCommand("hiliteColor", false, Initial) (data value)]
    expected: FAIL

  [Calling execCommand("hiliteColor", false, Reset) (inputType value)]
    expected: FAIL

  [Calling execCommand("hiliteColor", false, Reset) (data value)]
    expected: FAIL

  [Calling execCommand("hiliteColor", false, transparent) (inputType value)]
    expected: FAIL

  [Calling execCommand("hiliteColor", false, transparent) (data value)]
    expected: FAIL

  [Calling execCommand("hiliteColor", false, CurrentColor) (inputType value)]
    expected: FAIL

  [Calling execCommand("hiliteColor", false, CurrentColor) (data value)]
    expected: FAIL

  [Calling execCommand("hiliteColor", false, Invalid-Value) (inputType value)]
    expected: FAIL

  [Calling execCommand("hiliteColor", false, Invalid-Value) (data value)]
    expected: FAIL

  [Calling execCommand("fontName", false, monospace) (inputType value)]
    expected: FAIL

  [Calling execCommand("fontName", false, monospace) (data value)]
    expected: FAIL

  [Calling execCommand("fontName", false,  monospace ) (inputType value)]
    expected: FAIL

  [Calling execCommand("fontName", false,  monospace ) (data value)]
    expected: FAIL

  [Calling execCommand("fontName", false,   monospace  ) (inputType value)]
    expected: FAIL

  [Calling execCommand("fontName", false,   monospace  ) (data value)]
    expected: FAIL

  [Calling execCommand("cut", false, null) (inputType value)]
    expected: FAIL

  [Calling execCommand("paste", false, null) (inputType value)]
    expected: FAIL

  [Calling execCommand("createLink", false, https://example.com/) (inputType value)]
    expected: FAIL

  [Calling execCommand("createLink", false, https://example.com/) (data value)]
    expected: FAIL

  [Calling execCommand("createLink", false, foo.html) (inputType value)]
    expected: FAIL

  [Calling execCommand("createLink", false, foo.html) (data value)]
    expected: FAIL
