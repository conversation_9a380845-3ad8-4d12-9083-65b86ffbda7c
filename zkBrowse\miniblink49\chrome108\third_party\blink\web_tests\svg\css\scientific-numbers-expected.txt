Test scientific numbers on values for SVG presentation attributes.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".



Test positive exponent values with 'e'
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "50"
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "50"
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "50"
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "50"
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "50"
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "50"
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "50"
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "50"
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "50"


Test positive exponent values with 'E'
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "50"
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "50"
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "50"
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "50"
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "50"
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "50"
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "50"
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "50"
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "50"


Test negative exponent values with 'e'
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "50"
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "50"
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "50"
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "50"
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "50px"
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "50px"


Test negative exponent values with 'E'
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "50"
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "50"
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "50"
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "50"
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "50px"
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "50px"


Test negative numbers with exponents
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "-50px"
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "-50px"
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "-50px"


Test if value and 'em' still works
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "50em"


Test if value and 'ex' still works
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "50ex"


Trailing and leading whitespaces
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "50"
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "50"


Test behavior on overflow
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "3.40282e+38"
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "-3.40282e+38"


Invalid values
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "baseline"
PASS text.style.baselineShift is "baseline"
PASS successfullyParsed is true

TEST COMPLETE

