<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

 <head>

  <title>CSS Reftest Reference</title>
  <link rel="author" title="Gérard Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" />

  <style type="text/css"><![CDATA[
  body
  {
  background: green url("support/square-white.png") repeat-y 19px 2px;
  border: blue solid 3px;
  color: white;
  margin: 1em;
  padding: 2em;
  }

  div
  {
  border: solid lime;
  padding: 3em;
  }
  ]]></style>

 </head>

 <body>

  <div>There should be a blue rectangle enclosing the bright green rectangle
enclosing this text. Just inside the left hand edge of the blue rectangle
should be a line of white square tiles, which should continue up and down the entire
height of the page. The rest of the page, around, between, and inside the
boxes, should be green.</div>

 </body>
</html>
