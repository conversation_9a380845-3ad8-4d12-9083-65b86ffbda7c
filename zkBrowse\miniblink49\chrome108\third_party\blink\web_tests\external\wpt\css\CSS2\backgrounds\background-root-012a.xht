<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>CSS Test: Background propagation on &lt;body&gt; and &lt;html&gt; - propagated position</title>
  <link rel="author" title="<PERSON>" href="mailto:<EMAIL>"/>
  <link rel="alternate" href="http://www.hixie.ch/tests/adhoc/css/background/09.html" type="text/html"/>
  <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background"/>
  <link rel="match" href="background-root-012a-ref.xht" />

  <meta name="flags" content="image interact"/>
<style type="text/css"><![CDATA[
   body { border: solid lime; background: green url(support/square-purple.png) no-repeat 50% 50%; color: white; }
   html { border: solid blue; background: transparent; color: yellow; }
   :link, :visited { color: inherit; background: transparent; }
   * { margin: 1em; padding: 1em; }
]]></style>

</head>
<body>
<p> In the middle of the bright blue rectangle, there
should be a purple tile. Resizing the window should make its
position relative to this outer box stay the same.

</p></body>
</html>
