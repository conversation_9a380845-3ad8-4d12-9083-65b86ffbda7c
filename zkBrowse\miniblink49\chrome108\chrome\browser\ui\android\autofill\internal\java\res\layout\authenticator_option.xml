<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright 2021 The Chromium Authors
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file.
-->

<LinearLayout
  xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools"
  android:layout_height="wrap_content"
  android:layout_width="wrap_content"
  android:orientation="horizontal">
  <RadioButton
    android:id="@+id/authenticator_option_radio_btn"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center_vertical"/>
  <ImageView
    android:id="@+id/authenticator_option_icon"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center_vertical"
    tools:ignore="ContentDescription"
    tools:src="@drawable/ic_settings_gear_24dp"/>
  <LinearLayout
    android:layout_height="wrap_content"
    android:layout_width="wrap_content"
    android:layout_marginStart="@dimen/authenticator_option_details_margin_start"
    android:orientation="vertical">
      <TextView
        android:id="@+id/authenticator_option_title"
        android:layout_width="match_parent"
        android:gravity="center_vertical"
        android:layout_height="wrap_content"/>
      <TextView
        android:id="@+id/authenticator_option_description"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/authenticator_option_description_margin_top"
        android:gravity="center_vertical"/>
    </LinearLayout>
</LinearLayout>
