<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background-repeat set to 'repeat-x'</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="G<PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-05-04 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background-repeat" />
        <link rel="match" href="background-repeat-002-ref.xht" />

        <meta name="flags" content="image" />
        <meta name="assert" content="Background-repeat set to 'repeat-x' repeats the image horizontally to fill the space." />
        <style type="text/css">
            div
            {
                background-color: black;
                background-image: url("support/blue15x15.png");
                background-repeat: repeat-x;
                height: 200px;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is an horizontal blue stripe directly above a filled black rectangle.</p>
        <div></div>
    </body>
</html>