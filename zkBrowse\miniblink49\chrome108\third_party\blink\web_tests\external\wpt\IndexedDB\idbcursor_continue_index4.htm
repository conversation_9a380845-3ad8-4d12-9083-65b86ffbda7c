<!DOCTYPE html>
<title>IDBCursor.continue() - index - attempt to iterate to the next record when the direction is set for the previous record</title>
<link rel="author" title="Microsoft" href="http://www.microsoft.com">
<link rel=help href="http://dvcs.w3.org/hg/IndexedDB/raw-file/tip/Overview.html#widl-IDBCursor-continue-void-any-key">
<link rel=assert title="The parameter is greater than or equal to this cursor's position and this cursor's direction is 'prev' or 'prevunique'.">
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<script src="resources/support.js"></script>

<script>

    var db,
      t = async_test(),
      records = [ { pKey: "primaryKey_0", iKey: "indexKey_0" },
                  { pKey: "primaryKey_1", iKey: "indexKey_1" },
                  { pKey: "primaryKey_2", iKey: "indexKey_2" } ];

    var open_rq = createdb(t);
    open_rq.onupgradeneeded = function(e) {
        db = e.target.result;
        var objStore = db.createObjectStore("test", { keyPath: "pKey" });

        objStore.createIndex("index", "iKey");

        for (var i = 0; i < records.length; i++)
            objStore.add(records[i]);
    };

    open_rq.onsuccess = function(e) {
        var count = 0,
          cursor_rq = db.transaction("test")
                        .objectStore("test")
                        .index("index")
                        .openCursor(undefined, "prev"); // XXX Fx issues w undefined

        cursor_rq.onsuccess = t.step_func(function(e) {
            var cursor = e.target.result,
              record = cursor.value;

            switch(count) {
            case 0:
                assert_equals(record.pKey, records[2].pKey, "first pKey");
                assert_equals(record.iKey, records[2].iKey, "first iKey");
                cursor.continue();
                break;

            case 1:
                assert_equals(record.pKey, records[1].pKey, "second pKey");
                assert_equals(record.iKey, records[1].iKey, "second iKey");
                assert_throws_dom("DataError",
                    function() { cursor.continue("indexKey_2"); });
                t.done();
                break;

            default:
                assert_unreached("Unexpected count value: " + count);
            }

            count++;
        });
    };

</script>

<div id="log"></div>
