<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>CSS Test: Background propagation on &lt;body&gt; and &lt;html&gt; - canvas positioning</title>
  <link rel="author" title="<PERSON>" href="mailto:<EMAIL>"/>
  <link rel="alternate" href="http://www.hixie.ch/tests/adhoc/css/background/13.html" type="text/html"/>
  <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background"/>
  <link rel="match" href="background-root-016-ref.xht" />

<style type="text/css"><![CDATA[
   body { border: solid lime; background: green url(support/square-purple.png) center top no-repeat; color: white; }
   html { border: solid blue; background: white url(support/square-teal.png) repeat-x top center; color: black; }
   :link, :visited { color: inherit; background: transparent; }
   p { margin: 1em; padding: 1em; }
   html, body { margin: 17px; }
]]></style>

</head>
<body>
<p>There should be a horizontal line of teal tiles positioned between
the top border of the blue box and the top border of the bright green
box, and extending all the way past the sides of the blue box to the
left and right edges of the page.
One of those tiles must be centered horizontally within the page, so
that it is exactly above the purple square that appears at the top of
the inner green box. </p>

</body>
</html>
