<script>
function xhr(url) {
  return new Promise((resolve, reject) => {
      let request = new XMLHttpRequest();
      request.addEventListener('error',() => { reject(new Error()); });
      request.addEventListener('load', (e) => { resolve(request.response); });
      request.open('GET', url);
      request.send();
    });
}

function load_cors_image(url) {
  return new Promise(function(resolve, reject) {
      let img = document.createElement('img');
      document.body.appendChild(img);
      img.onload = () => { resolve(); };
      img.onerror = () => { reject(new Error()); };
      img.crossOrigin = 'anonymous';
      img.src = url;
    });
}
</script>
