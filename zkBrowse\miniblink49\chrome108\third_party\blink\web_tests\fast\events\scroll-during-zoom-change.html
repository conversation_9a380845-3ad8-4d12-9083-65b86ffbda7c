<div>
    Test for <i><a href="rdar://problem/6634768">rdar://problem/6634768</a>
    Reproducible crash in Layer::updateScrollCornerStyle() using full-page zoom at MobileMe Contacts</i>.
</div>
<div>
    The test passed if it did not crash.
</div>
<div id="target" style="
    overflow: auto;
    height: 200px;
    width: 200px;
">
    <div style="height: 500px;"></div>
</div>
<script>
    function scrolled(event)
    {
        event.target.parentNode.removeChild(event.target);
        if (window.testRunner) {
            testRunner.notifyDone();
        }
    }

    function test()
    {
        var target = document.getElementById("target");
        target.scrollTop = 50;
        document.body.offsetTop;
        target.addEventListener("scroll", scrolled, false);
        document.body.style.zoom = 2;
    }

    if (window.testRunner) {
        testRunner.dumpAsText();
        testRunner.waitUntilDone();
    }
    test();
</script>
