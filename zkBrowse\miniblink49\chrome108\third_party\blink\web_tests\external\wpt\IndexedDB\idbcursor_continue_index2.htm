<!DOCTYPE html>
<title>IDBCursor.continue() - index - attempt to pass a key parameter that is not a valid key</title>
<link rel="author" title="Microsoft" href="http://www.microsoft.com">
<link rel=help href="http://dvcs.w3.org/hg/IndexedDB/raw-file/tip/Overview.html#widl-IDBCursor-continue-void-any-key">
<link rel=assert title="If the key parameter is specified and fulfills any of these conditions this method must throw a DOMException of type DataError">
<link rel=assert title="The parameter is not a valid key.">
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<script src="resources/support.js"></script>

<script>

    var db,
      t = async_test(),
      records = [ { pKey: "primaryKey_0", iKey: "indexKey_0" },
                  { pKey: "primaryKey_1", iKey: "indexKey_1" } ];

    var open_rq = createdb(t);
    open_rq.onupgradeneeded = function(e) {
        db = e.target.result;
        var objStore = db.createObjectStore("test", {keyPath:"pKey"});

        objStore.createIndex("index", "iKey");

        for(var i = 0; i < records.length; i++)
            objStore.add(records[i]);
    };

    open_rq.onsuccess = function(e) {
        var cursor_rq = db.transaction("test")
                          .objectStore("test")
                          .index("index")
                          .openCursor();

        cursor_rq.onsuccess = t.step_func(function(e) {
            var cursor = e.target.result;

            assert_throws_dom("DataError",
                function() { cursor.continue(document); });

            assert_true(cursor instanceof IDBCursorWithValue, "cursor");

            t.done();
        });
    };

</script>

<div id="log"></div>
