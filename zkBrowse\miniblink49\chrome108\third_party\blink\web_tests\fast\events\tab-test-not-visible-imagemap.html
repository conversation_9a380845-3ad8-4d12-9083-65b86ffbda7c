<!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML//EN">
<html>
<head>
<script src="../../resources/js-test.js"></script>
<body id="body">

<form>
<table>
<tbody>
    <tr><td><input id="input1" type="text"/></td></tr>
    <tr style="display: none"><td>

    <map name="mymap">
        <area id="area1" shape="circle" coords="70,84,51" href="1">
        <area id="area2" shape="rect" coords="25,180,125,280" href="2">
        <area id="area3" shape="poly" coords="153,106,186,225,340,193,315,81,304,167" href="3">
        <area id="area4" shape="rect" coords="420,19,478,278" nohref>
        <area id="area5" shape="circle" coords="220,150,100" href="4">
        <area id="area6" shape="default" coords="0,0,500,300" href="5">
        <area id="area7" shape="rect" coords="1, 1, 10, 10" tabindex=-1 href="6">
    </map>

    <img src="resources/abe.png" width="500" height="300" alt="Image Map" usemap="#mymap" ismap>

    </td></tr>
    <tr><td><input id ="input2" type="text"/></td></tr>

    <tr style="visibility: hidden"><td>

    <map name="map">
        <area id="area8" shape="rect" coords=coords="5,48,247,97" href="1">
    </map>

    <img src="resources/abe.png" width="500" height="300" alt="Image Map" usemap="#map" ismap>

    </td></tr>
    <tr><td><input id ="input3" type="text"/></td></tr>

</tbody>
</table>
</form>


<p id="description"></p>
<div id="console"></div>

<script>

    description("Test that tabbing does not focus area element when its associated image is not rendered.");

    eventSender.keyDown('\t');
    shouldBe("document.activeElement.id", "'input1'");

    eventSender.keyDown('\t');
    shouldBe("document.activeElement.id", "'input2'");

    eventSender.keyDown('\t');
    shouldBe("document.activeElement.id", "'input3'");

</script>

</body>
</html>
