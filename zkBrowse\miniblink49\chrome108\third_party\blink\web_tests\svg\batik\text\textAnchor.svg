<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 20010904//EN" 
"http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">

<!--

   Copyright 2001  The Apache Software Foundation 

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

-->
<!-- ====================================================================== -->
<!-- Tests text-anchor on tspan elements                                    -->
<!--                                                                        -->
<!-- <AUTHOR>                                               -->
<!-- @version $Id: textAnchor.svg,v 1.4 2005/01/03 10:48:05 deweese Exp $                                                          -->
<!-- ====================================================================== -->
<?xml-stylesheet type="text/css" href="../resources/test.css" ?>  

<svg width="450" height="500" viewBox="0 0 450 500"
     xmlns="http://www.w3.org/2000/svg" 
     xmlns:xlink="http://www.w3.org/1999/xlink" >

    <text class="title" x="50%" y="40">text-anchor on &lt;tspan&gt;</text>

    <g id="testContent" transform="translate(56.25, 82.5) scale(.75,.75)">

        <g transform="translate(0,0)">
            <rect width="450" height="167" fill="black" opacity=".1" />
            <rect y="167" width="450" height="167" fill="white" />
            <rect y="334" width="450" height="166" fill="black" opacity=".1" />

            <line x1="150" y1="0" x2="150" y2="500" style="stroke:red; fill:none" />
            <line x1="320" y1="0" x2="320" y2="500" style="stroke:red; fill:none" />
            <rect width="450" height="500" stroke="red" fill="none" />
        </g>

        <g transform="translate(0, 0)">
        <!-- ############################## -->
        <!-- textChunk1: text-anchor:start  -->
        <!-- textChunk2: text-anchor:start  -->
        <!-- ############################## -->
        <g transform="translate(0, 0)">
    
            <text y="50">
                <tspan x="150" style="text-anchor:start">I am the</tspan>
                <tspan style="fill:red"> same text chunk</tspan>
                <tspan x="320" y="30" style="text-anchor:start">another text chunk</tspan>
            </text>

        </g>


        <!-- ############################## -->
        <!-- textChunk1: text-anchor:middle -->
        <!-- textChunk2: text-anchor:start  -->
        <!-- ############################## -->
        <g transform="translate(0 40)">

            <text y="50">
                <tspan x="150" style="text-anchor:middle">I am the</tspan>
                <tspan style="fill:red"> same text chunk</tspan>
                <tspan x="320" y="30" style="text-anchor:start">another text chunk</tspan>
            </text>

        </g>

        <!-- ############################## -->
        <!-- textChunk1: text-anchor:end    -->
        <!-- textChunk2: text-anchor:start  -->
        <!-- ############################## -->
        <g transform="translate(0 80)">

            <text y="50">
                <tspan x="150" style="text-anchor:end">I am the</tspan>
                <tspan style="fill:red"> same text chunk</tspan>
                <tspan x="320" y="30" style="text-anchor:start">another text chunk</tspan>
            </text>

        </g>
        </g>

        <!-- ############################################################################ -->
        <!-- ############################################################################ -->

        <g transform="translate(0 50)">
        <!-- ############################## -->
        <!-- textChunk1: text-anchor:start  -->
        <!-- textChunk2: text-anchor:middle -->
        <!-- ############################## -->
        <g transform="translate(0 120)">

            <text y="50">
                <tspan x="150" style="text-anchor:start">I am the</tspan>
                <tspan style="fill:red"> same text chunk</tspan>
                <tspan x="320" y="30" style="text-anchor:middle">another text chunk</tspan>
            </text>

        </g>


        <!-- ############################## -->
        <!-- textChunk1: text-anchor:middle -->
        <!-- textChunk2: text-anchor:middle -->
        <!-- ############################## -->
        <g transform="translate(0 160)">

            <text y="50">
                <tspan x="150" style="text-anchor:middle">I am the</tspan>
                <tspan style="fill:red"> same text chunk</tspan>
                <tspan x="320" y="30" style="text-anchor:middle">another text chunk</tspan>
            </text>

        </g>

        <!-- ############################## -->
        <!-- textChunk1: text-anchor:end    -->
        <!-- textChunk2: text-anchor:middle -->
        <!-- ############################## -->
        <g transform="translate(0 200)">

            <text y="50">
                <tspan x="150" style="text-anchor:end">I am the</tspan>
                <tspan style="fill:red"> same text chunk</tspan>
                <tspan x="320" y="30" style="text-anchor:middle">another text chunk</tspan>
            </text>

        </g>
        </g>

        <!-- ############################################################################ -->
        <!-- ############################################################################ -->
        <g transform="translate(0, 80)">

        <!-- ############################# -->
        <!-- textChunk1: text-anchor:start -->
        <!-- textChunk2: text-anchor:end   -->
        <!-- ############################# -->
        <g transform="translate(0 260)">

            <text y="50">
                <tspan x="150" style="text-anchor:start">I am the</tspan>
                <tspan style="fill:red"> same text chunk</tspan>
                <tspan x="320" y="30" style="text-anchor:end">another text chunk</tspan>
                </text>

        </g>


        <!-- ############################## -->
        <!-- textChunk1: text-anchor:middle -->
        <!-- textChunk2: text-anchor:end    -->
        <!-- ############################## -->
        <g transform="translate(0 300)">

            <text y="50">
                <tspan x="150" style="text-anchor:middle">I am the</tspan>
                <tspan style="fill:red"> same text chunk</tspan>
                <tspan x="320" y="30" style="text-anchor:end">another text chunk</tspan>
            </text>

        </g>

        <!-- ############################## -->
        <!-- textChunk1: text-anchor:end    -->
        <!-- textChunk2: text-anchor:end    -->
        <!-- ############################## -->
        <g transform="translate(0 340)">

            <text y="50">
                <tspan x="150" style="text-anchor:end">I am the</tspan>
                <tspan style="fill:red"> same text chunk</tspan>
                <tspan x="320" y="30" style="text-anchor:end">another text chunk</tspan>
            </text>

        </g>
        </g>
    </g>

    <!-- ============================================================= -->
    <!-- Batik sample mark                                             -->
    <!-- ============================================================= -->
    <use xlink:href="../resources/batikLogo.svg#Batik_Tag_Box" />
</svg>
