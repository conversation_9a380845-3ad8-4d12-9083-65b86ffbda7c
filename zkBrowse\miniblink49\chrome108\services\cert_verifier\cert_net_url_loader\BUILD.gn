# Copyright 2020 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//crypto/features.gni")
import("//testing/test.gni")

component("cert_net_url_loader") {
  sources = [
    "cert_net_fetcher_url_loader.cc",
    "cert_net_fetcher_url_loader.h",
  ]

  deps = [
    "//base",
    "//mojo/public/cpp/bindings",
    "//net",
    "//services/network/public/cpp",
    "//services/network/public/mojom",
  ]

  defines = [ "IS_CERT_VERIFIER_CPP_IMPL" ]
}

source_set("tests") {
  testonly = true

  sources = []

  if (!is_ios) {
    # Need TestServer
    sources += [ "cert_net_fetcher_url_loader_unittest.cc" ]
  }

  deps = [
    ":cert_net_url_loader",
    ":test_support",
    "//base",
    "//mojo/public/cpp/bindings",
    "//net",
    "//net:test_support",
    "//services/network:network_service",
    "//services/network:test_support",
    "//services/network/public/cpp",
    "//services/network/public/mojom",
    "//testing/gtest",
  ]
}

source_set("test_support") {
  testonly = true

  sources = [
    "cert_net_fetcher_test.cc",
    "cert_net_fetcher_test.h",
  ]

  deps = [
    ":cert_net_url_loader",
    "//base",
    "//mojo/public/cpp/bindings",
    "//net",
    "//services/network:test_support",
    "//services/network/public/cpp",
    "//services/network/public/mojom",
  ]
}
