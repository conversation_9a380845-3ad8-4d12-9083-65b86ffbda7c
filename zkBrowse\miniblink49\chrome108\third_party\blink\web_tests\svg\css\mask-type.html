<!DOCTYPE html>
<html>
<style>
* { font-size: 16px; }
div { font-size: 8px; }
</style>
<body>
<svg><mask id="mask"></mask></svg>
<script src="../../resources/js-test.js"></script>
<script>
description('Test that clip-path shapes accept different length units');

var mask = document.getElementById('mask');

function computedStyle(property, value) {
    mask.style.setProperty(property, value);
    return getComputedStyle(mask).getPropertyValue(property);
}

function test(property, value, expected) {
    if (expected === null)
        shouldBeNull('computedStyle("' + property + '", "' + value + '")');
    else
        shouldBeEqualToString('computedStyle("' + property + '", "' + value + '")', expected);
}

test("mask-type", "", "luminance");
test("mask-type", "alpha", "alpha");
test("mask-type", "luminance", "luminance");

// negative tests
test("mask-type", "0", "luminance");
test("mask-type", "1", "luminance");
test("mask-type", "linearRGB", "luminance");

</script>
</svg>