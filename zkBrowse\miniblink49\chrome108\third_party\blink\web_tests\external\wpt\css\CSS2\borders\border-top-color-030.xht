<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Border-top-color set to hex with three digits with the maximum plus one value of #1000</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="<PERSON><PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-05-26 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#propdef-border-top-color" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#border-color-properties" />
        <link rel="match" href="../reference/ref-transparent-or-black-square-black.xht"/>
        <link rel="match" href="../reference/ref-transparent-or-black-square-transparent.xht"/>

        <meta name="flags" content="invalid" />
        <meta name="assert" content="The 'border-top-color' set to #1000 is a transparent dark red square." />
        <style type="text/css">
            div.test
            {
                border: 5px solid blue;
                height: 1in;
                width: 1in;
            }
            div.test div
            {
                border-top-style: solid;
                border-top-width: 1in;
                border-top-color: #1000;
                height: 0;
                width: 1in;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is a filled black or transparent square surrounded by a blue border.</p>
        <div class="test"><div></div></div>
    </body>
</html>
