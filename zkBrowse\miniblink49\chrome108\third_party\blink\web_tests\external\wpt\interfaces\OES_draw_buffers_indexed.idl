// GENERATED CONTENT - DO NOT EDIT
// Content was automatically extracted by <PERSON><PERSON> into webref
// (https://github.com/w3c/webref)
// Source: WebGL OES_draw_buffers_indexed Extension Specification (https://registry.khronos.org/webgl/extensions/OES_draw_buffers_indexed/)

[Exposed=(Window,Worker), LegacyNoInterfaceObject]
interface OES_draw_buffers_indexed {
  undefined enableiOES(GLenum target, GLuint index);

  undefined disableiOES(GLenum target, GLuint index);

  undefined blendEquationiOES(GLuint buf, GLenum mode);

  undefined blendEquationSeparateiOES(GLuint buf,
                                 GLenum modeRGB, GLenum modeAlpha);

  undefined blendFunciOES(GLuint buf,
                     GLenum src, GLenum dst);

  undefined blendFuncSeparateiOES(GLuint buf,
                             GLenum srcRGB, GLenum dstRGB,
                             GLenum srcAlpha, GLenum dstAlpha);

  undefined colorMaskiOES(<PERSON><PERSON><PERSON><PERSON> buf,
                     GL<PERSON>lean r, GLboolean g, GLboolean b, G<PERSON><PERSON><PERSON> a);
};
