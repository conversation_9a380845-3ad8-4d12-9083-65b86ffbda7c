This test should trigger 'window.onerror' for the syntax error in the attribute handler, regardless of how it's set.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".

window.onerror: "Uncaught SyntaxError: Unexpected token '%'" at window-onerror-11.html (Line: 11, Column: 25)
Stack Trace:
SyntaxError: Unexpected token '%'
    at window-onerror-11.html:31:24

Returning 'true': the error should not be reported in the console as an unhandled exception.



window.onerror: "Uncaught SyntaxError: Unexpected token '%'" at window-onerror-11.html (Line: 33, Column: 34)
Stack Trace:
SyntaxError: Unexpected token '%'
    at window-onerror-11.html:31:24

Returning 'true': the error should not be reported in the console as an unhandled exception.



window.onerror: "Uncaught SyntaxError: Unexpected token '%'" at window-onerror-11.html (Line: 33, Column: 52)
Stack Trace:
SyntaxError: Unexpected token '%'
    at window-onerror-11.html:31:24

Returning 'true': the error should not be reported in the console as an unhandled exception.



PASS successfullyParsed is true

TEST COMPLETE

Button. Button 2 Button 3
