<html>
<head>
<script src="../../resources/js-test.js"></script>
</head>
<body>
<p>This test verifies that the Home/End/PageUp/PageDown keys work correctly for &lt;select&gt; elements.
Since it requires <CODE>eventSender.keyDown</CODE>, it will not run solo in the web browser; it must be run with <KBD>run_web_tests.py</KBD>.</p>
<hr>
<form>
    <select name="singleselect" id="ss" size="4" multiple="true">
        <option value="0">0 </option>
        <option value="1">1 </option>
        <option value="2">2 </option>
        <option value="3">3 </option>
        <option value="4">4 </option>
        <option value="5">5 </option>
        <option value="6">6 </option>
    </select>
    <select name="singleselectwithdisabled" id="ssd" size="4" multiple="false">
        <option value="0" disabled="true">0 </option>
        <option value="1">1 </option>
        <option value="2">2 </option>
        <option value="3" disabled="true">3 </option>
        <option value="4">4 </option>
        <option value="5">5 </option>
        <option value="6">6 </option>
        <option value="7" disabled = "true">7 </option>
    </select>
    <select name="singleselectwithgroup" id="ssg" size="4" multiple="false">
        <optgroup label="gp0">
            <option value="0">0 </option>
            <option value="1">1 </option>
            <option value="2">2 </option>
        </optgroup>
        <optgroup label="gp1">
            <option value="3">3 </option>
        </optgroup>
        <option value="4">4 </option>
        <optgroup label="gp2">
            <option value="5">5 </option>
        </optgroup>
        <option value="6">6 </option>
    </select>
</form>

<p id="description"></p>
<div id="console"></div>
<div id="log"></div>

<script>

function log(message) {
    document.getElementById('log').appendChild(document.createTextNode(message + "\n"));
}

function sendKeyAndExpectIndex(selectId, key, initialIndex, expectedIndex) {
    var select = document.getElementById(selectId);
    clearSelection(select);
    select.focus();
    select.selectedIndex = initialIndex;
    if (select.selectedIndex != initialIndex) {
        log("can't set selectedIndex to " + initialIndex + ' (is ' + select.selectedIndex + ')');
        return false;
    }
    if (window.testRunner)
        eventSender.keyDown(key);
    if (select.selectedIndex != expectedIndex) {
        log('selectedIndex should be ' + expectedIndex + ' (is ' + select.selectedIndex + ') after a ' + key + ' from index ' + initialIndex);
        return false;
    }
    return true;
}

function equalArrays(a1, a2) {
    if (a1.length != a2.length)
        return false;
    for (i = 0; i < a1.length; i++) {
        if (a1[i] != a2[i])
            return false;
    }
    return true;
}

function dumpArray(a) {
    s = "[";
    for (i = 0; i < a.length; i++) {
        s = s + a[i];
        if (i < a.length - 1)
             s = s + ", ";
    }
    return s + "]";
}

function getSelectedIndices(select) {
    nowSelected = [];
    for (i = 0; i < select.options.length; i++)
        if (select.options[i].selected)
              nowSelected.push(i);
    return nowSelected;
}

function clearSelection(select) {
    for (i = 0; i < select.options.length; i++)
        select.options[i].selected = false;
}

// expectedIndices should be in sorted order
function sendWithShiftKeyAndExpectIndices(selectId, key, expectedIndices) {
    var select = document.getElementById(selectId);
    select.focus();
    if (window.testRunner)
        eventSender.keyDown(key, ["shiftKey"]);
    nowSelected = getSelectedIndices(select);
    if (!equalArrays(nowSelected, expectedIndices)) {
        log('selected indices should be ' + dumpArray(expectedIndices) + ' (is ' +
            dumpArray(nowSelected) + ') after a ' + key);
        return false;
    }
    return true;
}

function testPageDownNoDisabledElements() {
    shouldBe('sendKeyAndExpectIndex("ss", "PageDown", 0, 3)', 'true');
    shouldBe('sendKeyAndExpectIndex("ss", "PageDown", 1, 4)', 'true');
    shouldBe('sendKeyAndExpectIndex("ss", "PageDown", 2, 5)', 'true');
    shouldBe('sendKeyAndExpectIndex("ss", "PageDown", 3, 6)', 'true');
    shouldBe('sendKeyAndExpectIndex("ss", "PageDown", 4, 6)', 'true');
    shouldBe('sendKeyAndExpectIndex("ss", "PageDown", 5, 6)', 'true');
    shouldBe('sendKeyAndExpectIndex("ss", "PageDown", 6, 6)', 'true');
}

function testPageUpNoDisabledElements() {
    shouldBe('sendKeyAndExpectIndex("ss", "PageUp", 6, 3)', 'true');
    shouldBe('sendKeyAndExpectIndex("ss", "PageUp", 5, 2)', 'true');
    shouldBe('sendKeyAndExpectIndex("ss", "PageUp", 4, 1)', 'true');
    shouldBe('sendKeyAndExpectIndex("ss", "PageUp", 3, 0)', 'true');
    shouldBe('sendKeyAndExpectIndex("ss", "PageUp", 2, 0)', 'true');
    shouldBe('sendKeyAndExpectIndex("ss", "PageUp", 1, 0)', 'true');
    shouldBe('sendKeyAndExpectIndex("ss", "PageUp", 0, 0)', 'true');
}

function testHomeNoDisabledElements() {
    shouldBe('sendKeyAndExpectIndex("ss", "Home", 6, 0)', 'true');
    shouldBe('sendKeyAndExpectIndex("ss", "Home", 5, 0)', 'true');
    shouldBe('sendKeyAndExpectIndex("ss", "Home", 4, 0)', 'true');
    shouldBe('sendKeyAndExpectIndex("ss", "Home", 3, 0)', 'true');
    shouldBe('sendKeyAndExpectIndex("ss", "Home", 2, 0)', 'true');
    shouldBe('sendKeyAndExpectIndex("ss", "Home", 1, 0)', 'true');
    shouldBe('sendKeyAndExpectIndex("ss", "Home", 0, 0)', 'true');
}

function testEndNoDisabledElements() {
    shouldBe('sendKeyAndExpectIndex("ss", "End", 6, 6)', 'true');
    shouldBe('sendKeyAndExpectIndex("ss", "End", 5, 6)', 'true');
    shouldBe('sendKeyAndExpectIndex("ss", "End", 4, 6)', 'true');
    shouldBe('sendKeyAndExpectIndex("ss", "End", 3, 6)', 'true');
    shouldBe('sendKeyAndExpectIndex("ss", "End", 2, 6)', 'true');
    shouldBe('sendKeyAndExpectIndex("ss", "End", 1, 6)', 'true');
    shouldBe('sendKeyAndExpectIndex("ss", "End", 0, 6)', 'true');
}

function testPageDownWithDisabledElements() {
    shouldBe('sendKeyAndExpectIndex("ssd", "PageDown", 0, 4)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssd", "PageDown", 1, 4)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssd", "PageDown", 2, 5)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssd", "PageDown", 4, 6)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssd", "PageDown", 5, 6)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssd", "PageDown", 6, 6)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssd", "PageDown", 7, 6)', 'true');
}

function testPageUpWithDisabledElements() {
    shouldBe('sendKeyAndExpectIndex("ssd", "PageUp", 7, 4)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssd", "PageUp", 6, 2)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssd", "PageUp", 5, 2)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssd", "PageUp", 4, 1)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssd", "PageUp", 2, 1)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssd", "PageUp", 1, 1)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssd", "PageUp", 0, 1)', 'true');
}

function testHomeWithDisabledElements() {
    shouldBe('sendKeyAndExpectIndex("ssd", "Home", 7, 1)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssd", "Home", 6, 1)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssd", "Home", 5, 1)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssd", "Home", 4, 1)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssd", "Home", 3, 1)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssd", "Home", 2, 1)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssd", "Home", 1, 1)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssd", "Home", 0, 1)', 'true');
}

function testEndWithDisabledElements() {
    shouldBe('sendKeyAndExpectIndex("ssd", "End", 7, 6)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssd", "End", 6, 6)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssd", "End", 5, 6)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssd", "End", 4, 6)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssd", "End", 3, 6)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssd", "End", 2, 6)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssd", "End", 1, 6)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssd", "End", 0, 6)', 'true');
}

function testVariousShiftKeysNoDisabledElements() {
    var select = document.getElementById("ss");
    select.focus();
    clearSelection(select);
    select.selectedIndex = 0;

    shouldBe('sendWithShiftKeyAndExpectIndices("ss", "PageDown", [0, 1, 2, 3])', 'true');
    shouldBe('sendWithShiftKeyAndExpectIndices("ss", "PageDown", [0, 1, 2, 3, 4, 5, 6])', 'true');
    shouldBe('sendWithShiftKeyAndExpectIndices("ss", "PageDown", [0, 1, 2, 3, 4, 5, 6])', 'true');
    shouldBe('sendWithShiftKeyAndExpectIndices("ss", "PageUp", [0, 1, 2, 3])', 'true');
    shouldBe('sendWithShiftKeyAndExpectIndices("ss", "PageUp", [0])', 'true');
    shouldBe('sendWithShiftKeyAndExpectIndices("ss", "PageUp", [0])', 'true');
    shouldBe('sendWithShiftKeyAndExpectIndices("ss", "End", [0, 1, 2, 3, 4, 5, 6])', 'true');
    shouldBe('sendWithShiftKeyAndExpectIndices("ss", "End", [0, 1, 2, 3, 4, 5, 6])', 'true');
    shouldBe('sendWithShiftKeyAndExpectIndices("ss", "Home", [0])', 'true');
    shouldBe('sendWithShiftKeyAndExpectIndices("ss", "Home", [0])', 'true');

    clearSelection(select);
    select.selectedIndex = 3;
    shouldBe('sendWithShiftKeyAndExpectIndices("ss", "PageDown", [3, 4, 5, 6])', 'true');
    shouldBe('sendWithShiftKeyAndExpectIndices("ss", "PageDown", [3, 4, 5, 6])', 'true');
    shouldBe('sendWithShiftKeyAndExpectIndices("ss", "PageUp", [3])', 'true');
    shouldBe('sendWithShiftKeyAndExpectIndices("ss", "PageUp", [0, 1, 2, 3])', 'true');
    shouldBe('sendWithShiftKeyAndExpectIndices("ss", "PageUp", [0, 1, 2, 3])', 'true');
    shouldBe('sendWithShiftKeyAndExpectIndices("ss", "PageUp", [0, 1, 2, 3])', 'true');
    shouldBe('sendWithShiftKeyAndExpectIndices("ss", "End", [3, 4, 5, 6])', 'true');
    shouldBe('sendWithShiftKeyAndExpectIndices("ss", "End", [3, 4, 5, 6])', 'true');
    shouldBe('sendWithShiftKeyAndExpectIndices("ss", "Home", [0, 1, 2, 3])', 'true');
    shouldBe('sendWithShiftKeyAndExpectIndices("ss", "Home", [0, 1, 2, 3])', 'true');
}

function testVariousShiftKeysWithDisabledElements() {
    var select = document.getElementById('ssd');
    select.focus();
    clearSelection(select);
    select.selectedIndex = 1;

    shouldBe('sendWithShiftKeyAndExpectIndices("ssd", "PageDown", [1, 2, 4])', 'true');
    shouldBe('sendWithShiftKeyAndExpectIndices("ssd", "PageDown", [1, 2, 4, 5, 6])', 'true');
    shouldBe('sendWithShiftKeyAndExpectIndices("ssd", "PageDown", [1, 2, 4, 5, 6])', 'true');
    shouldBe('sendWithShiftKeyAndExpectIndices("ssd", "PageUp", [1, 2])', 'true');
    shouldBe('sendWithShiftKeyAndExpectIndices("ssd", "PageUp", [1])', 'true');
    shouldBe('sendWithShiftKeyAndExpectIndices("ssd", "PageUp", [1])', 'true');
    shouldBe('sendWithShiftKeyAndExpectIndices("ssd", "End", [1, 2, 4, 5, 6])', 'true');
    shouldBe('sendWithShiftKeyAndExpectIndices("ssd", "End", [1, 2, 4, 5, 6])', 'true');
    shouldBe('sendWithShiftKeyAndExpectIndices("ssd", "Home", [1])', 'true');
    shouldBe('sendWithShiftKeyAndExpectIndices("ssd", "Home", [1])', 'true');

    clearSelection(select);
    select.selectedIndex = 4;
    shouldBe('sendWithShiftKeyAndExpectIndices("ssd", "PageDown", [4, 5, 6])', 'true');
    shouldBe('sendWithShiftKeyAndExpectIndices("ssd", "PageDown", [4, 5, 6])', 'true');
    shouldBe('sendWithShiftKeyAndExpectIndices("ssd", "PageUp", [2, 4])', 'true');
    shouldBe('sendWithShiftKeyAndExpectIndices("ssd", "PageUp", [1, 2, 4])', 'true');
    shouldBe('sendWithShiftKeyAndExpectIndices("ssd", "PageUp", [1, 2, 4])', 'true');
    shouldBe('sendWithShiftKeyAndExpectIndices("ssd", "End", [4, 5, 6])', 'true');
    shouldBe('sendWithShiftKeyAndExpectIndices("ssd", "End", [4, 5, 6])', 'true');
    shouldBe('sendWithShiftKeyAndExpectIndices("ssd", "Home", [1, 2, 4])', 'true');
    shouldBe('sendWithShiftKeyAndExpectIndices("ssd", "Home", [1, 2, 4])', 'true');
}

function testPageDownWithGroup() {
    shouldBe('sendKeyAndExpectIndex("ssg", "PageDown", 0, 3)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssg", "PageDown", 1, 3)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssg", "PageDown", 2, 4)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssg", "PageDown", 3, 5)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssg", "PageDown", 4, 6)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssg", "PageDown", 5, 6)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssg", "PageDown", 6, 6)', 'true');
}

function testPageUpWithGroup() {
    shouldBe('sendKeyAndExpectIndex("ssg", "PageUp", 6, 4)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssg", "PageUp", 5, 3)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssg", "PageUp", 4, 2)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssg", "PageUp", 3, 1)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssg", "PageUp", 2, 0)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssg", "PageUp", 1, 0)', 'true');
    shouldBe('sendKeyAndExpectIndex("ssg", "PageUp", 0, 0)', 'true');
}

testPageDownNoDisabledElements();
testPageUpNoDisabledElements();
testHomeNoDisabledElements();
testEndNoDisabledElements();
testPageDownWithDisabledElements();
testPageUpWithDisabledElements();
testHomeWithDisabledElements();
testEndWithDisabledElements();
testVariousShiftKeysNoDisabledElements();
testVariousShiftKeysWithDisabledElements();
testPageDownWithGroup();
testPageUpWithGroup();
</script>

</body>
</html>
