# Copyright 2015 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

source_set("lib") {
  # Production code shouldn't be depending on this.
  testonly = true

  sources = [
    "aff_reader.cc",
    "aff_reader.h",
    "dic_reader.cc",
    "dic_reader.h",
    "hunspell_reader.cc",
    "hunspell_reader.h",
  ]
  configs += [ "//build/config/compiler:wexit_time_destructors" ]
  deps = [
    "//base",
    "//base:i18n",
  ]
}

executable("convert_dict") {
  # Production code shouldn't be depending on this.
  testonly = true

  sources = [ "convert_dict.cc" ]
  configs += [ "//build/config/compiler:wexit_time_destructors" ]
  deps = [
    ":lib",
    "//base",
    "//base:i18n",
    "//build/win:default_exe_manifest",
    "//third_party/hunspell",
  ]
}
