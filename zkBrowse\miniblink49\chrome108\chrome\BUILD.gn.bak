# Copyright 2014 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/chrome_build.gni")
import("//build/config/chromeos/ui_mode.gni")
import("//build/config/compiler/compiler.gni")
import("//build/config/compiler/pgo/pgo.gni")
import("//build/config/features.gni")
import("//build/config/locales.gni")
import("//build/config/ozone.gni")
import("//build/config/python.gni")
import("//build/config/sanitizers/sanitizers.gni")
import("//build/config/ui.gni")
import("//build/config/win/console_app.gni")
import("//build/config/win/manifest.gni")
import("//build/private_code_test/private_code_test.gni")
import("//build/toolchain/toolchain.gni")
import("//chrome/browser/buildflags.gni")
import("//chrome/chrome_paks.gni")
import("//chrome/common/features.gni")
import("//chrome/process_version_rc_template.gni")
import("//components/nacl/features.gni")
import("//components/optimization_guide/features.gni")
import("//extensions/buildflags/buildflags.gni")
import("//media/media_options.gni")
import("//ppapi/buildflags/buildflags.gni")
import("//third_party/blink/public/public_features.gni")
import("//third_party/widevine/cdm/widevine.gni")
import("//tools/resources/generate_resource_allowlist.gni")
import("//tools/v8_context_snapshot/v8_context_snapshot.gni")
import("//ui/gl/features.gni")
import("//v8/gni/v8.gni")

assert(!is_fuchsia, "Fuchsia shouldn't use anything in //chrome")

if (is_android) {
  import("//build/config/android/rules.gni")
} else if (is_linux || is_chromeos) {
  import("//build/linux/extract_symbols.gni")
  import("//build/linux/strip_binary.gni")
} else if (is_mac) {
  import("//build/apple/compile_entitlements.gni")
  import("//build/apple/compile_plist.gni")
  import("//build/apple/tweak_info_plist.gni")
  import("//build/compiled_action.gni")
  import("//build/config/apple/symbols.gni")
  import("//build/config/mac/mac_sdk.gni")
  import("//build/config/mac/rules.gni")
  import("//build/util/branding.gni")
  import("//chrome/browser/buildflags.gni")
  import("//chrome/updater/branding.gni")
  import("//chrome/version.gni")
  import("//content/public/app/mac_helpers.gni")
  import("//media/cdm/library_cdm/cdm_paths.gni")
  import("//services/on_device_model/on_device_model.gni")
  import("//third_party/icu/config.gni")
}

if (is_chromeos_ash) {
  import("//build/chromeos/embed_sections.gni")
}

declare_args() {
  # On macOS, `is_chrome_branded` builds that have been signed locally will not
  # launch because certain entitlements are tied to the official Google code
  # signing identity. If `include_branded_entitlements` is set to false, these
  # entitlements will be skipped.
  include_branded_entitlements = true
}

assert(!is_ios, "Chromium/iOS shouldn't use anything in //chrome")

if (is_win && enable_resource_allowlist_generation) {
  _chrome_resource_allowlist = "$target_gen_dir/chrome_resource_allowlist.txt"
}

if (is_win) {
  action("reorder_imports") {
    script = "//build/win/reorder-imports.py"

    # See comment in chrome_dll.gypi in the hardlink_to_output
    # target for why this cannot be 'initial' like the DLL.
    inputs = [ "$root_out_dir/initialexe/chrome.exe" ]
    outputs = [
      "$root_out_dir/chrome.exe",
      "$root_out_dir/chrome.exe.pdb",
    ]
    args = [
      "-i",
      rebase_path("$root_out_dir/initialexe", root_build_dir),
      "-o",
      rebase_path("$root_out_dir", root_build_dir),
      "-a",
      current_cpu,
    ]
    deps = [ ":chrome_initial" ]
  }
}

# This does not currently work. See crbug.com/1311822.
# This target exists above chrome and it's main components in the dependency
# tree as a central place to put assert_no_deps annotations. Since this depends
# on Chrome and the main DLLs it uses, it will transitively assert that those
# targets also have no deps on disallowed things.
group("assert_no_deps") {
  deps = []

  if (is_android) {
    deps += [ "//chrome/android:chrome_public_apk" ]
  } else {
    deps += [ ":chrome" ]
  }

  if (is_win) {
    deps += [ ":chrome_dll" ]
  }

  # This should not pull in installer strings. This is will bloat the binary
  # for no reason and is easy to mess up. See the comment at the top of
  # //chrome/installer/util/BUILD.gn.
  assert_no_deps = [ "//chrome/installer/util:strings" ]
}

if (!is_android && !is_mac) {
  group("chrome") {
    public_deps = [ ":chrome_initial" ]
    data_deps = [ ":chrome_initial" ]

    # Do not add any more deps or data_deps to group("chrome").
    # Because chrome_initial sets its output name to "chrome", running commands
    # such as `ninja chrome` causes chrome_initial to be built instead. All
    # deps and data_deps should be added to the chrome_initial target instead.
    # Targets added here can only be built by explicitly using //chrome:chrome.
    # Windows-only deps are OK because chrome_initial uses initialexe/chrome as
    # the output name for that platform.
    # See crbug.com/1146571.
    if (is_win) {
      public_deps += [ ":reorder_imports" ]
      data_deps += [ ":reorder_imports" ]
    }
  }

  template("_chrome_exe") {
    if (invoker.collect_inputs_only) {
      _type = "shared_library"
    } else {
      _type = "executable"
    }
    target(_type, target_name) {
      configs -= [ "//build/config/compiler:thinlto_optimize_default" ]
      configs += [ "//build/config/compiler:thinlto_optimize_max" ]
      if (is_win) {
        output_name = "initialexe/chrome"
      } else {
        output_name = "chrome"
      }

      # Because the sources list varies so significantly per-platform, generally
      # each platform lists its own files rather than relying on filtering or
      # removing unused files.
      sources = [ "app/chrome_exe_resource.h" ]
      defines = []
      public_deps = []
      deps = [
        "//build:chromeos_buildflags",
        "//printing/buildflags",
      ]
      data = [ "$root_out_dir/resources.pak" ]
      data_deps = []

      if (is_chromeos_ash) {
        data_deps += [
          "//components/variations/cros_evaluate_seed:evaluate_seed",
          "//sandbox/linux:chrome_sandbox",
        ]
        deps += [
          "//components/exo/wayland:test_controller_stub",
          "//components/exo/wayland:ui_controls_protocol_stub",
        ]
      }

      if (also_build_lacros_chrome_for_architecture != "") {
        data_deps += [ "//chrome:chrome(//build/toolchain/cros:lacros_clang)" ]
      }

      if (is_win) {
        sources += [
          "app/chrome_exe.rc",
          "app/chrome_exe_main_win.cc",
          "app/delay_load_failure_hook_win.cc",
          "app/delay_load_failure_hook_win.h",
          "app/main_dll_loader_win.cc",
          "app/main_dll_loader_win.h",
          "common/crash_keys.cc",
          "common/crash_keys.h",
           "../third_party/blink/public/web/web_fp_info.cc",
        ]

        deps += [
          ":chrome_dll",
          ":chrome_exe_version",
          ":copy_first_run",
          ":packed_resources_integrity_header",
          ":visual_elements_resources",
          "//base",
          "//build:branding_buildflags",
          "//chrome/app:chrome_exe_main_exports",
          "//chrome/app:exit_code_watcher",
          "//chrome/app/version_assembly:chrome_exe_manifest",
          "//chrome/browser:active_use_util",
          "//chrome/browser:chrome_process_finder",
          "//chrome/browser/policy:path_parser",
          "//chrome/chrome_elf",
          "//chrome/common:constants",
          "//chrome/common/win:delay_load_failure_support",
          "//chrome/install_static:install_static_util",
          "//chrome/install_static:secondary_module",
          "//chrome/installer/util:constants",
          "//chrome/installer/util:did_run_support",
          "//components/crash/core/app",
          "//components/crash/core/app:run_as_crashpad_handler",
          "//components/crash/core/common",
          "//components/crash/win:chrome_wer",
          "//components/flags_ui:switches",
          "//content:sandbox_helper_win",
          "//content/public/common:static_switches",
          "//crypto",
          "//gpu/command_buffer/service",
          "//sandbox",
          "//sandbox/policy",
          "//sandbox/policy/mojom",
          "//third_party/breakpad:breakpad_handler",
          "//third_party/breakpad:breakpad_sender",
          "//third_party/crashpad/crashpad/util",
          "//ui/gl",
        ]

        data_deps = [
          "//chrome/app/version_assembly:version_assembly_manifest",
          "//chrome/browser/web_applications/chrome_pwa_launcher",
          "//chrome/chrome_proxy",
          "//chrome/elevation_service",
          "//chrome/notification_helper",
        ]

        if (enable_platform_experience) {
          data_deps +=
              [ "//chrome/browser/platform_experience/win:os_update_handler" ]
        }

        defines += [ "CHROME_EXE_MAIN", "FP_NO_ICU" ]

        if (win_console_app) {
          defines += [ "WIN_CONSOLE_APP" ]
        } else {
          # Set /SUBSYSTEM:WINDOWS for chrome.exe itself, unless a console build
          # has been requested.
          configs -= [ "//build/config/win:console" ]
          configs += [ "//build/config/win:windowed" ]
        }

        configs += [
          "//build/config/win:delayloads",
          "//build/config/win:delayloads_not_for_child_dll",
        ]

        if (current_cpu == "x86") {
          # Set the initial stack size to 0.5MiB, instead of the 1.5MiB needed by
          # Chrome's main thread. This saves significant memory on threads (like
          # those in the Windows thread pool, and others) whose stack size we can
          # only control through this setting. Because Chrome's main thread needs
          # a minimum 1.5 MiB stack, the main thread (in 32-bit builds only) uses
          # fibers to switch to a 1.5 MiB stack before running any other code.
          ldflags = [ "/STACK:0x80000" ]
        } else {
          # Increase the initial stack size. The default is 1MB, this is 8MB.
          ldflags = [ "/STACK:0x800000" ]
        }
      } else if (use_aura) {
        # Non-Windows aura entrypoint.
        sources += [ "app/chrome_exe_main_aura.cc" ]
      }

      if (is_linux || is_chromeos) {
        sources += [
          "app/chrome_dll_resource.h",
          "app/chrome_main.cc",
          "app/chrome_main_delegate.cc",
          "app/chrome_main_delegate.h",
          "app/startup_timestamps.h",
        ]

        deps += [
          # On Linux, link the dependencies (libraries) that make up actual
          # Chromium functionality directly into the executable.
          ":dependencies",
          "//chrome/common:version_header",

          # For the sampling profiler.
          "//chrome/common/profiler",

          # Needed to use the master_preferences functions
          "//chrome/installer/util:with_no_strings",
          "//content/public/app",

          # For headless mode.
          "//headless:headless_shell_lib",
        ]

        public_deps = [
          ":xdg_mime",  # Needs to be public for installer to consume files.
          "//chrome/common:buildflags",
        ]

        data_deps += [ "//components/crash/core/app:chrome_crashpad_handler" ]

        ldflags = []

        # On Chrome OS builds (for both ash-chrome and lacros-chrome), put
        # priority to the library in the installed directory.
        # This will avoid conflicting of exposed symbols.
        if (is_chromeos_device) {
          ldflags += [ "-L" + rebase_path(root_out_dir) ]
        }

        # On Chrome OS builds (for both ash-chrome and lacros-chrome), put
        # a metadata.json file in root directory containing Chrome version.
        if (is_chromeos) {
          data_deps += [ "//build:version_metadata" ]
        }

        # Chrome OS debug builds for arm need to pass --long-plt to the linker.
        # See https://bugs.chromium.org/p/chromium/issues/detail?id=583532
        if (is_chromeos_ash && is_debug && target_cpu == "arm") {
          ldflags += [ "-Wl,--long-plt" ]
        }

        if ((is_linux || is_chromeos_lacros) && !is_component_build &&
            !using_sanitizer) {
          version_script = "//build/linux/chrome.map"
          inputs = [ version_script ]
          ldflags += [ "-Wl,--version-script=" +
                       rebase_path(version_script, root_build_dir) ]
        }

        if (is_chromeos_ash) {
          public_deps += [ "//ui/lottie" ]
          deps += [ "//chrome/browser/ash/schedqos" ]
        }

        if (is_chromeos_lacros) {
          deps += [
            "//chromeos/crosapi/cpp:crosapi_constants",
            "//chromeos/crosapi/mojom",
            "//chromeos/lacros:lacros_paths",
            "//chromeos/startup",
            "//ui/base",
          ]
        }

        if (use_ozone) {
          deps += [ "//ui/ozone" ]
          if (is_linux) {
            deps += [ "//ui/linux:display_server_utils" ]
          }
        }
      }

      if (is_chromeos_ash || is_win) {
        data_deps += [ "//mojo/core:shared_library" ]
      }

      # These files are used by the installer so we need a public dep.
      public_deps += [ ":packed_resources" ]

      # The step's output are needed at runtime, so we also need a data_dep.
      data_deps += [ ":packed_resources" ]

      # ChromeOS by design is safe to have rpath=$ORIGIN. This simplifies shared
      # library usage.
      if (is_chromeos_ash && !is_component_build) {
        configs += [ "//build/config/gcc:rpath_for_built_shared_libraries" ]
      }

      if (is_chromeos_lacros && is_chromeos_device) {
        configs += [ "//build/lacros:optional_shared_libgcc" ]
      }

      data_deps += [
        "//chrome/browser/resources/media/mei_preload:component",
        "//components/privacy_sandbox/privacy_sandbox_attestations/preload:component",
        "//third_party/widevine/cdm",
      ]
      if (invoker.collect_inputs_only) {
        output_name = target_name
        data_deps = []
        ldflags += [ "--collect-inputs-only" ]
      }

      if (is_linux) {
        sources += [
          "app/chrome_main_linux.cc",
          "app/chrome_main_linux.h",
        ]
      }
    }
  }
  _chrome_exe("chrome_initial") {
    collect_inputs_only = false
  }
  if (current_toolchain == default_toolchain && is_linux) {
    _chrome_exe("chrome_inputs") {
      collect_inputs_only = true
    }
    private_code_test("chrome_private_code_test") {
      linker_inputs_dep = ":chrome_inputs"
    }
  }
}  # !is_android && !is_mac

if (is_win) {
  shared_library("chrome_dll") {
    configs += [ "//build/config/compiler:wexit_time_destructors" ]
    configs -= [ "//build/config/compiler:thinlto_optimize_default" ]
    configs += [ "//build/config/compiler:thinlto_optimize_max" ]

    defines = []

    sources = [
      "//base/win/dllmain.cc",
      "app/chrome_main.cc",
      "app/chrome_main_delegate.cc",
      "app/chrome_main_delegate.h",
      "app/startup_timestamps.h",
    ]

    output_name = "chrome"

    deps = [
      ":chrome_dll_manifest",
      ":chrome_dll_version",
      ":dependencies",
      "//chrome/app:chrome_dll_resources",
      "//chrome/app:command_ids",
      "//chrome/app/theme:chrome_unscaled_resources",
      "//chrome/chrome_elf",
      "//chrome/common:buildflags",
      "//chrome/common:version_header",
      "//chrome/common/profiler",
      "//chrome/install_static:install_static_util",
      "//chrome/install_static:secondary_module",
      "//components/crash/core/app",
      "//components/memory_system",
      "//components/policy:generated",
      "//content/public/app",
      "//crypto",
      "//headless:headless_non_renderer",
      "//headless:headless_shell_browser_lib",
      "//net:net_resources",
      "//ppapi/buildflags",
      "//sandbox/win:sandbox",
      "//third_party/cld_3/src/src:cld_3",
      "//third_party/wtl",
      "//ui/views",
    ]

    configs += [ "//build/config/win:delayloads" ]

    if (use_aura) {
      deps += [ "//ui/compositor" ]
    }

    if (is_chromeos_ash) {
      deps += [ "//chrome/browser/ash/schedqos" ]
    }
  }

  copy("copy_first_run") {
    sources = [ "app/FirstRun" ]
    outputs = [ "$root_out_dir/First Run" ]
  }
} else if (is_mac) {
  chrome_helper_name = chrome_product_full_name + " Helper"
  chrome_framework_name = chrome_product_full_name + " Framework"
  chrome_framework_version = chrome_version_full

  verify_dynamic_libraries = !is_component_build && !is_asan && !is_ubsan &&
                             !is_ubsan_vptr && !is_ubsan_security
  if (host_os == "mac") {
    objdump_path = mac_bin_path
  } else {
    objdump_path = rebase_path("$clang_base_path/bin/", root_build_dir)
  }

  group("chrome") {
    deps = [ ":chrome_app" ]

    data_deps = [ ":chrome_app" ]

    if (verify_dynamic_libraries) {
      deps += [ ":verify_libraries_chrome_app" ]
    }

    if (is_chrome_branded && is_official_build) {
      deps += [
        ":chrome_dsym_archive",
        ":chrome_dump_syms",
      ]
    }
  }

  tweak_info_plist("chrome_app_plist") {
    info_plist = "app/app-Info.plist"
    args = [
      "--breakpad=0",
      "--scm=1",
      "--bundle_id=$chrome_mac_bundle_id",
    ]
    if (enable_updater) {
      args += [ "--privileged_helper_id=$privileged_helper_name" ]
      if (is_chrome_branded) {
        args += [ "--keystone=1" ]
        if (current_cpu == "arm64") {
          args += [ "--keystone-base-tag=arm64" ]
        }
      } else {
        args += [ "--keystone=0" ]
      }
    } else {
      args += [ "--keystone=0" ]
    }
  }

  mac_app_bundle("chrome_app") {
    output_name = chrome_product_full_name

    info_plist_target = ":chrome_app_plist"
    extra_substitutions = [
      "CHROMIUM_BUNDLE_ID=$chrome_mac_bundle_id",
      "CHROMIUM_SHORT_NAME=$chrome_product_short_name",
      "CHROMIUM_CREATOR=$chrome_mac_creator_code",
    ]

    sources = [ "app/chrome_exe_main_mac.cc" ]

    configs += [ "//build/config/compiler:wexit_time_destructors" ]

    deps = [
      ":chrome_app_strings_bundle_data",
      ":chrome_resources",
      ":chrome_versioned_bundle_data",
      "//base/allocator:early_zone_registration_apple",
      "//build:branding_buildflags",
      "//chrome/common:buildflags",
      "//chrome/common:version_header",
    ]

    if (enable_updater) {
      deps += [ ":chromium_updater_privileged_helper" ]
    }

    if (enable_stripping) {
      # At link time, preserve the global symbols specified in the .exports
      # file. All other global symbols will be marked as private. The default
      # //build/config/apple:strip_all config will then remove the remaining
      # local and debug symbols.
      ldflags = [ "-Wl,-exported_symbols_list," +
                  rebase_path("app/app.exports", root_build_dir) ]
    }

    if (is_component_build) {
      # In a component build, the framework is directly linked to the
      # executable because dlopen() and loading all the dependent dylibs
      # is time-consuming, see https://crbug.com/1197495.
      deps += [ ":chrome_framework+link" ]
      ldflags = [ "-Wl,-rpath,@executable_path/../Frameworks" ]

      # The Framework is packaged inside the .app bundle. But when using the
      # component build, all the dependent shared libraries of :chrome_dll are
      # not packaged within the framework. This data_deps line makes all of
      # those dependent libraries runtime dependencies of the .app bundle.
      # This is a bit of a hack, since GN deliberately terminates its search
      # for runtime_deps at create_bundle nodes (https://crbug.com/1010347).
      data_deps = [ ":chrome_framework" ]
    }
  }

  if (verify_dynamic_libraries) {
    action("verify_libraries_chrome_app") {
      script = "//chrome/tools/build/mac/verify_dynamic_libraries.py"
      inputs = [ "${root_out_dir}/${chrome_product_full_name}.app/Contents/MacOS/${chrome_product_full_name}" ]
      outputs = [ "$target_out_dir/run_$target_name.stamp" ]
      args = [
        "--stamp",
        rebase_path(outputs[0], root_out_dir),
        "-B",
        objdump_path,
        "--image",
        rebase_path(inputs[0], root_out_dir),
        "--allow",
        "/usr/lib/libSystem.B.dylib",
      ]
      deps = [ ":chrome_app" ]
    }
  }

  compiled_action("chrome_app_strings") {
    tool = "//chrome/tools/build/mac:infoplist_strings_util"

    inputs = []

    outputs = []

    foreach(locale, platform_pak_locales) {
      _strings_file = "${branding_path_product}_strings"

      inputs += [ "$root_gen_dir/chrome/${_strings_file}_${locale}.pak" ]
    }

    foreach(locale, locales_as_apple_outputs) {
      outputs += [
        "$target_gen_dir/app_infoplist_strings/$locale.lproj/InfoPlist.strings",
      ]
    }

    args =
        [
          "-b",
          "${branding_path_product}_strings",
          "-v",
          chrome_version_full,
          "-g",
          rebase_path("$root_gen_dir/chrome", root_build_dir),
          "-o",
          rebase_path("$target_gen_dir/app_infoplist_strings", root_build_dir),
          "-t",
          "main",
        ] + platform_pak_locales

    deps = [ "//chrome/app:branded_strings" ]
  }

  foreach(locale, locales_as_apple_outputs) {
    bundle_data("chrome_app_strings_${locale}_bundle_data") {
      sources = [
        "$target_gen_dir/app_infoplist_strings/$locale.lproj/InfoPlist.strings",
      ]
      outputs =
          [ "{{bundle_resources_dir}}/$locale.lproj/{{source_file_part}}" ]
      public_deps = [ ":chrome_app_strings" ]
    }
  }
  group("chrome_app_strings_bundle_data") {
    public_deps = []
    foreach(locale, locales_as_apple_outputs) {
      public_deps += [ ":chrome_app_strings_${locale}_bundle_data" ]
    }
  }

  bundle_data("chrome_app_icon") {
    sources = [ "app/theme/$branding_path_component/mac/app.icns" ]
    outputs = [ "{{bundle_resources_dir}}/{{source_file_part}}" ]
  }

  bundle_data("chrome_resources") {
    sources = [
      "$root_out_dir/$chrome_mac_bundle_id.manifest",
      "app/theme/$branding_path_component/mac/document.icns",
      "browser/ui/cocoa/applescript/scripting.sdef",
    ]
    outputs = [ "{{bundle_resources_dir}}/{{source_file_part}}" ]
    public_deps = [
      ":chrome_app_icon",
      ":chrome_app_strings",
      "//components/policy:chrome_manifest_bundle",
    ]
  }

  bundle_data("chrome_versioned_bundle_data") {
    sources = [ "$root_out_dir/$chrome_framework_name.framework" ]
    outputs = [ "{{bundle_contents_dir}}/Frameworks/{{source_file_part}}" ]
    public_deps = [
      # Before bundling the versioned app components, delete any existing
      # versions.
      ":clean_up_old_versions",

      # verify_chrome_framework_order depends on :chrome_framework and, for
      # non-component builds, will ensure the export symbol table is correct.
      ":verify_chrome_framework_order",
    ]

    if (enable_widevine_cdm_host_verification) {
      # The :chrome_framework_widevine_signature target copies into the
      # :chrome_framework bundle. But because the signing file depends on the
      # framework itself, that would cause a cyclical dependency. Instead,
      # this dependency directly copies the file into the framework's
      # resources directory.
      public_deps += [ ":chrome_framework_widevine_signature" ]
    }
  }

  if (enable_updater) {
    bundle_data("chromium_updater_privileged_helper") {
      sources = [ "$root_out_dir/$privileged_helper_name" ]
      outputs = [
        "{{bundle_contents_dir}}/Library/LaunchServices/{{source_file_part}}",
      ]

      public_deps = [ "//chrome/updater/mac:privileged_helper" ]
    }
  }

  action("clean_up_old_versions") {
    script = "//chrome/tools/build/mac/clean_up_old_versions.py"

    _stamp_file = "$root_gen_dir/run_$target_name.stamp"

    outputs = [ _stamp_file ]

    _versions_dir = "$root_out_dir/$chrome_product_full_name.app/Contents/Frameworks/$chrome_framework_name.framework/Versions"

    args = [
      "--versions-dir",
      rebase_path(_versions_dir, root_build_dir),
      "--stamp",
      rebase_path(_stamp_file, root_build_dir),
      "--keep",
      chrome_version_full,
      "--keep",
      "Current",
    ]
  }

  tweak_info_plist("chrome_helper_plist") {
    info_plist = "app/helper-Info.plist"
    args = [
      "--breakpad=0",
      "--keystone=0",
      "--scm=0",
    ]
  }

  compile_entitlements("entitlements") {
    entitlements_templates = [ "app/app-entitlements.plist" ]
    if (is_chrome_branded && include_branded_entitlements) {
      # These entitlements are bound to the official Google Chrome signing
      # certificate and will not necessarily work in any other build.
      entitlements_templates += [ "app/app-entitlements-chrome.plist" ]
    }
    output_name = "$target_gen_dir/app-entitlements.plist"
    substitutions = [
      "CHROMIUM_BUNDLE_ID=$chrome_mac_bundle_id",
      "CHROMIUM_TEAM_ID=$chrome_mac_team_id",
    ]
    visibility = [ "//chrome/installer/mac:copies" ]
  }

  template("chrome_helper_app") {
    mac_app_bundle(target_name) {
      assert(defined(invoker.helper_name_suffix))
      assert(defined(invoker.helper_bundle_id_suffix))

      output_name = chrome_helper_name + invoker.helper_name_suffix

      if (defined(invoker.info_plist_target)) {
        info_plist_target = invoker.info_plist_target
      } else {
        info_plist_target = ":chrome_helper_plist"
      }

      extra_substitutions = [
        "CHROMIUM_BUNDLE_ID=$chrome_mac_bundle_id",
        "CHROMIUM_SHORT_NAME=$chrome_product_short_name",
        "CHROMIUM_HELPER_SUFFIX=${invoker.helper_name_suffix}",
        "CHROMIUM_HELPER_BUNDLE_ID_SUFFIX=${invoker.helper_bundle_id_suffix}",
      ]

      sources = [ "app/chrome_exe_main_mac.cc" ]

      configs += [ "//build/config/compiler:wexit_time_destructors" ]

      defines = [ "HELPER_EXECUTABLE" ]

      deps = [
        "//base/allocator:early_zone_registration_apple",
        "//build:branding_buildflags",
        "//chrome/common:version_header",
        "//sandbox/mac:seatbelt",
      ]

      if (defined(invoker.deps)) {
        deps += invoker.deps
      }

      ldflags = []

      if (is_component_build) {
        # In a component build, the framework is directly linked to the
        # executable because dlopen() and loading all the dependent dylibs
        # is time-consuming, see https://crbug.com/1197495.
        deps += [ ":chrome_framework+link_nested" ]

        ldflags += [
          # The helper is in Chromium.app/Contents/Frameworks/Chromium Framework.framework/Versions/X/Helpers/Chromium Helper.app/Contents/MacOS
          # so set rpath up to the base.
          "-Wl,-rpath,@loader_path/../../../../../../../../../..",

          # ... and up to Contents/Frameworks.
          "-Wl,-rpath,@executable_path/../../../../../../..",
        ]
      }

      if (enable_stripping) {
        # At link time, preserve the global symbols specified in the .exports
        # file. All other global symbols will be marked as private. The default
        # //build/config/apple:strip_all config will then remove the remaining
        # local and debug symbols.
        ldflags += [ "-Wl,-exported_symbols_list," +
                     rebase_path("app/app.exports", root_build_dir) ]
      }
    }
  }

  # The following *_helper_params are added to the ones provided by //content
  # listed in content_mac_helpers (see //content/public/app/mac_helpers.gni).
  # These allow //chrome to add custom helper apps in addition to the ones
  # provided by //content. The params here have the same form as the content
  # helpers and are defined as a tuple of these elements:
  #   target name - A short name to be used when defining the target for that
  #                 helper variant.
  #   bundle ID suffix - A string fragment to append to the CFBundleIdentifier of
  #                      the helper.
  #   app name suffix - A string fragment to append to the outer bundle name as
  #                     well as the inner executable. This should be reflected in
  #                     the target's output_name.

  # Helper app to display alert notifications. This is necessary as an app can
  # only display either banner or alert style notifications and the main app
  # will display banners.
  alert_helper_params = [
    "alerts",
    ".alerts",
    " (Alerts)",
  ]

  # Merge all helper apps needed by //content and //chrome.
  chrome_mac_helpers = content_mac_helpers + [ alert_helper_params ]

  # Create all helper apps required by //content.
  foreach(helper_params, content_mac_helpers) {
    chrome_helper_app("chrome_helper_app_${helper_params[0]}") {
      helper_name_suffix = helper_params[2]
      helper_bundle_id_suffix = helper_params[1]
    }
  }

  # Create app for the alert helper manually here as we want to modify the plist
  # to set the alert style and add the app icon to its resources.
  tweak_info_plist("chrome_helper_app_alerts_plist") {
    deps = [ ":chrome_helper_plist" ]
    info_plists = get_target_outputs(":chrome_helper_plist") +
                  [ "app/helper-alerts-Info.plist" ]
  }

  # Create and bundle an InfoPlist.strings for the alert helper app.
  # TODO(crbug.com/40751430): Disambiguate and localize alert helper app name.
  compile_plist("chrome_helper_app_alerts_plist_strings") {
    format = "binary1"
    plist_templates = [ "app/helper-alerts-InfoPlist.strings" ]
    substitutions = [ "CHROMIUM_FULL_NAME=$chrome_product_full_name" ]
    output_name = "$target_gen_dir/helper_alerts_infoplist_strings/base.lproj/InfoPlist.strings"
  }
  bundle_data("chrome_helper_app_alerts_resources") {
    sources = get_target_outputs(":chrome_helper_app_alerts_plist_strings")
    outputs = [ "{{bundle_resources_dir}}/base.lproj/{{source_file_part}}" ]
    public_deps = [ ":chrome_helper_app_alerts_plist_strings" ]
  }

  chrome_helper_app("chrome_helper_app_${alert_helper_params[0]}") {
    helper_name_suffix = alert_helper_params[2]
    helper_bundle_id_suffix = alert_helper_params[1]
    info_plist_target = ":chrome_helper_app_alerts_plist"
    deps = [
      ":chrome_app_icon",
      ":chrome_helper_app_alerts_resources",
    ]
  }

  if (verify_dynamic_libraries) {
    foreach(helper_params, chrome_mac_helpers) {
      _helper_target = helper_params[0]
      _helper_bundle_id = helper_params[1]
      _helper_suffix = helper_params[2]

      action("verify_libraries_chrome_helper_app_${_helper_target}") {
        script = "//chrome/tools/build/mac/verify_dynamic_libraries.py"
        inputs = [ "${root_out_dir}/${chrome_helper_name}${_helper_suffix}.app/Contents/MacOS/${chrome_helper_name}${_helper_suffix}" ]
        outputs = [ "$target_out_dir/run_$target_name.stamp" ]
        args = [
          "--stamp",
          rebase_path(outputs[0], root_out_dir),
          "-B",
          objdump_path,
          "--image",
          rebase_path(inputs[0], root_out_dir),

          # Do not --allow more libraries here without consulting with the
          # security team (<EMAIL>).
          "--allow",
          "/usr/lib/libsandbox.1.dylib",
          "--allow",
          "/usr/lib/libSystem.B.dylib",
        ]
        deps = [ ":chrome_helper_app_${_helper_target}" ]
      }
    }
  }

  bundle_data("chrome_framework_helpers") {
    sources = [
      "$root_out_dir/app_mode_loader",
      "$root_out_dir/chrome_crashpad_handler",
      "$root_out_dir/web_app_shortcut_copier",
    ]

    outputs = [ "{{bundle_contents_dir}}/Helpers/{{source_file_part}}" ]

    public_deps = [
      "//chrome/app_shim:app_mode_loader",
      "//chrome/browser/web_applications/os_integration/mac:web_app_shortcut_copier",
      "//components/crash/core/app:chrome_crashpad_handler",
    ]

    foreach(helper_params, chrome_mac_helpers) {
      sources +=
          [ "$root_out_dir/${chrome_helper_name}${helper_params[2]}.app" ]
      public_deps += [ ":chrome_helper_app_${helper_params[0]}" ]
      if (verify_dynamic_libraries) {
        public_deps +=
            [ ":verify_libraries_chrome_helper_app_${helper_params[0]}" ]
      }
    }

    if (enable_updater) {
      if (is_chrome_branded) {
        sources += [ "//third_party/updater/chrome_mac_universal_prod/cipd/${updater_product_full_name}.app" ]
      } else {
        sources += [ "$root_out_dir/${updater_product_full_name}.app" ]

        public_deps += [
          "//chrome/updater/mac:browser_install_script",
          "//chrome/updater/mac:updater_bundle",
          "//chrome/updater/mac:updater_install_script",
        ]
      }
    }
  }

  bundle_data("chrome_framework_resources") {
    sources = [
      "//ui/gl/resources/angle-metal/gpu_shader_cache.bin",

      # This image is used to badge the lock icon in the
      # authentication dialogs, such as those used for installation
      # from disk image and Keystone promotion (if so enabled).  It
      # needs to exist as a file on disk and not just something in a
      # resource bundle because that's the interface that
      # Authorization Services uses.  Also, Authorization Services
      # can't deal with .icns files.
      "$root_gen_dir/chrome/browser/mac/install.sh",
      "app/theme/default_100_percent/$branding_path_component/product_logo_32.png",
    ]

    outputs = [ "{{bundle_resources_dir}}/{{source_file_part}}" ]

    public_deps = [
      ":packed_resources",
      "//chrome/app_shim:app_mode_loader_plist_bundle_data",
      "//chrome/browser/mac:install",
    ]

    if (icu_use_data_file) {
      sources += [ "$root_out_dir/icudtl.dat" ]
      public_deps += [ "//third_party/icu:icudata" ]
    }

    if (v8_use_external_startup_data) {
      public_deps += [ "//v8" ]
      if (use_v8_context_snapshot) {
        sources += [ "$root_out_dir/$v8_context_snapshot_filename" ]
        public_deps += [ "//tools/v8_context_snapshot" ]
      }
      if (!use_v8_context_snapshot || include_both_v8_snapshots) {
        sources += [ "$root_out_dir/snapshot_blob.bin" ]
      }
    }
  }

  if (enable_nacl) {
    bundle_data("chrome_framework_plugins") {
      sources = []
      outputs =
          [ "{{bundle_contents_dir}}/Internet Plug-Ins/{{source_file_part}}" ]
      public_deps = []

      if (enable_nacl) {
        if (current_cpu == "x86") {
          sources += [ "$root_out_dir/nacl_irt_x86_32.nexe" ]
        } else if (current_cpu == "x64") {
          sources += [ "$root_out_dir/nacl_irt_x86_64.nexe" ]
        }
        public_deps += [ "//ppapi/native_client:irt" ]
      }
    }
  } else {
    group("chrome_framework_plugins") {
    }
  }

  # Add the ANGLE .dylibs in the MODULE_DIR of Chromium.app
  bundle_data("angle_binaries") {
    sources = [
      "$root_out_dir/egl_intermediates/libEGL.dylib",
      "$root_out_dir/egl_intermediates/libGLESv2.dylib",
    ]
    outputs = [ "{{bundle_contents_dir}}/Libraries/{{source_file_part}}" ]
    public_deps = [ "//ui/gl:angle_library_copy" ]
  }

  # Add the SwiftShader .dylibs in the MODULE_DIR of Chromium.app
  bundle_data("swiftshader_binaries") {
    sources = [
      "$root_out_dir/vk_intermediates/libvk_swiftshader.dylib",
      "$root_out_dir/vk_intermediates/vk_swiftshader_icd.json",
    ]
    outputs = [ "{{bundle_contents_dir}}/Libraries/{{source_file_part}}" ]
    public_deps = [ "//ui/gl:swiftshader_vk_library_copy" ]
  }

  if (bundle_widevine_cdm) {
    bundle_data("widevine_cdm_library_binaries") {
      sources = [ "$root_out_dir/$widevine_cdm_path/libwidevinecdm.dylib" ]
      if (enable_widevine_cdm_host_verification) {
        sources +=
            [ "$root_out_dir/$widevine_cdm_path/libwidevinecdm.dylib.sig" ]
      }
      outputs = [ "{{bundle_contents_dir}}/Libraries/$widevine_cdm_path/{{source_file_part}}" ]
      public_deps = [ "//third_party/widevine/cdm" ]
    }

    bundle_data("widevine_cdm_library_manifest_and_license_files") {
      sources = [
        "$root_out_dir/WidevineCdm/LICENSE",
        "$root_out_dir/WidevineCdm/manifest.json",
      ]
      outputs = [
        "{{bundle_contents_dir}}/Libraries/WidevineCdm/{{source_file_part}}",
      ]
      public_deps = [ "//third_party/widevine/cdm" ]
    }
  }

  group("widevine_cdm_library") {
    if (bundle_widevine_cdm) {
      deps = [
        ":widevine_cdm_library_binaries",
        ":widevine_cdm_library_manifest_and_license_files",
      ]
    }
  }

  if (enable_widevine_cdm_host_verification) {
    widevine_sign_file("sign_chrome_framework_for_widevine") {
      file = "$root_out_dir/$chrome_framework_name.framework/Versions/$chrome_framework_version/$chrome_framework_name"
      flags = 1
      signature_file = "$root_out_dir/$chrome_framework_name.sig"
      deps = [ ":chrome_framework" ]
    }

    copy("chrome_framework_widevine_signature") {
      deps = [ ":sign_chrome_framework_for_widevine" ]

      sources = [ "$root_out_dir/$chrome_framework_name.sig" ]

      outputs = [ "$root_out_dir/$chrome_framework_name.framework/Resources/{{source_file_part}}" ]
    }
  }

  if (build_with_internal_optimization_guide) {
    # Add the optimization guide .dylib in the MODULE_DIR of Chromium.app
    bundle_data("optimization_guide_library") {
      sources = [
        "$root_out_dir/og_intermediates/liboptimization_guide_internal.dylib",
      ]
      outputs = [ "{{bundle_contents_dir}}/Libraries/{{source_file_part}}" ]
      public_deps = [ "//components/optimization_guide/core:optimization_guide_internal_library_copy" ]
    }
  } else {
    group("optimization_guide_library") {
    }
  }

  tweak_info_plist("chrome_framework_plist") {
    info_plist = "app/framework-Info.plist"
    args = [
      "--breakpad=0",
      "--keystone=0",
      "--scm=1",
      "--branding",
      chrome_product_short_name,
    ]
  }

  # Limit the exported symbols of the framework library.
  config("chrome_dll_symbol_exports") {
    inputs = [ rebase_path("app/framework.exports") ]
    ldflags = [
      "-Wl,-exported_symbols_list",
      "-Wl," + rebase_path("app/framework.exports", root_build_dir),
    ]
  }

  # Control the order of exported symbols in the framework library.
  config("chrome_dll_symbol_order") {
    inputs = [ rebase_path("app/framework.order") ]
    ldflags = [
      "-Wl,-order_file",
      "-Wl," + rebase_path("app/framework.order", root_build_dir),
    ]
  }

  # On Mac, speed up the component build by not re-bundling the framework
  # every time it changes. Instead, place all the sources and their deps in
  # a library that the bundled framework links (and re-exports). That way
  # only the library needs to be re-linked when it changes.
  if (is_component_build) {
    _dll_target_type = "shared_library"
  } else {
    _dll_target_type = "source_set"
  }
  target(_dll_target_type, "chrome_dll") {
    visibility = [
      ":chrome_framework",
      ":chrome_framework_create_bundle",
      ":chrome_framework_shared_library",
    ]

    sources = [
      "app/chrome_crash_reporter_client.cc",
      "app/chrome_crash_reporter_client.h",
      "app/chrome_crash_reporter_client_mac.mm",
      "app/chrome_dll_resource.h",
      "app/chrome_main.cc",
      "app/chrome_main_delegate.cc",
      "app/chrome_main_delegate.h",
      "app/chrome_main_mac.h",
      "app/chrome_main_mac.mm",
      "app/startup_timestamps.h",
    ]

    deps = [
      ":dependencies",
      "//build:chromeos_buildflags",
      "//chrome/app:command_ids",
      "//chrome/common:buildflags",
      "//chrome/common:version_header",
      "//chrome/common/profiler",
      "//components/crash/core/app",
      "//components/memory_system",
      "//components/policy:generated",
      "//content/public/app",
      "//headless:headless_shell_lib",
      "//third_party/cld_3/src/src:cld_3",
    ]

    if (is_chromeos_ash) {
      deps += [ "//chrome/browser/ash/schedqos" ]
    }

    if (is_component_build) {
      frameworks = [ "Carbon.framework" ]
    }

    ldflags = [ "-ObjC" ]

    configs += [
      ":chrome_dll_symbol_order",
      "//build/config/compiler:wexit_time_destructors",
    ]
    if (!is_component_build && !using_sanitizer) {
      configs += [ ":chrome_dll_symbol_exports" ]
    }
  }

  mac_framework_bundle("chrome_framework") {
    output_name = chrome_framework_name

    framework_version = chrome_framework_version
    framework_contents = [
      "Helpers",
      "Libraries",
      "Resources",
    ]

    if (is_chrome_branded) {
      framework_contents += [ "Default Apps" ]
    }

    if (enable_nacl) {
      framework_contents += [ "Internet Plug-Ins" ]
    }

    configs += [ "//build/config/compiler:wexit_time_destructors" ]
    configs -= [ "//build/config/compiler:thinlto_optimize_default" ]
    configs += [ "//build/config/compiler:thinlto_optimize_max" ]

    info_plist_target = ":chrome_framework_plist"
    extra_substitutions = [
      "CHROMIUM_BUNDLE_ID=$chrome_mac_bundle_id",
      "CHROMIUM_SHORT_NAME=$chrome_product_short_name",
    ]

    public_deps = [ ":chrome_dll" ]

    bundle_deps = [
      ":angle_binaries",
      ":chrome_framework_helpers",
      ":chrome_framework_plugins",
      ":chrome_framework_resources",
      ":optimization_guide_library",
      ":swiftshader_binaries",
      ":widevine_cdm_library",
    ]

    if (is_chrome_branded) {
      bundle_deps += [ ":preinstalled_apps" ]
    }

    configs += [ ":chrome_dll_symbol_order" ]
    if (!is_component_build && !using_sanitizer) {
      configs += [ ":chrome_dll_symbol_exports" ]
    }

    ldflags = [
      "-compatibility_version",
      chrome_dylib_version,
      "-current_version",
      chrome_dylib_version,
    ]

    if (!is_component_build) {
      # Specify a sensible install_name for static builds. The library is
      # dlopen()ed so this is not used to resolve the module.
      ldflags += [ "-Wl,-install_name,@executable_path/../Frameworks/$chrome_framework_name.framework/Versions/$chrome_version_full/$chrome_framework_name" ]
    } else {
      # In the component build, both the :chrome_app and various
      # :chrome_helper* targets directly link to the Framework target. Use
      # @rpath-based loading so that the dylib ID does not have to be changed
      # with install_name_tool.
      ldflags += [
        "-Wl,-install_name,@rpath/$chrome_framework_name.framework/$chrome_framework_name",
        "-Wl,-rpath,@loader_path/../../../../../..",
        "-Wl,-reexport_library,libchrome_dll.dylib",
      ]

      data_deps = [ ":chrome_dll" ]
    }
  }

  _framework_binary_path = "$root_out_dir/$chrome_framework_name.framework/Versions/$chrome_framework_version/$chrome_framework_name"
  assert(_framework_binary_path != "",
         "Ignore configuration-dependent unused variable warning")

  # TOOD(crbug/1163903#c8) - thakis@ look into why profile and coverage
  # instrumentation adds these symbols in different orders
  if (!is_component_build && chrome_pgo_phase != 1 && !using_sanitizer) {
    action("verify_chrome_framework_order") {
      script = "//chrome/tools/build/mac/verify_order.py"
      stamp_file = "$target_out_dir/run_$target_name.stamp"
      inputs = [ script ]
      args = [
        "--stamp=" + rebase_path(stamp_file, root_out_dir),
        "--binary=" + rebase_path(_framework_binary_path, root_out_dir),
        "--symbol-file=" + rebase_path("app/framework.order", root_build_dir),
      ]
      if (host_os == "mac") {
        args += [ "--nm-path=$mac_bin_path/nm" ]
      } else {
        args += [ "--nm-path=" +
                  rebase_path("$clang_base_path/bin/llvm-nm", root_build_dir) ]
      }
      outputs = [ stamp_file ]
      public_deps = [ ":chrome_framework" ]
    }
  } else {
    group("verify_chrome_framework_order") {
      if (is_component_build) {
        # In a component build, the framework is directly linked to the
        # executable because dlopen() and loading all the dependent dylibs
        # is time-consuming, see https://crbug.com/1197495.
        public_deps = [ ":chrome_framework+link" ]
      } else {
        public_deps = [ ":chrome_framework" ]
      }
    }
  }

  if (enable_dsyms && !is_component_build) {
    # It is possible to run dump_syms on unstripped products without dSYMs, but
    # doing so isn't logical and won't happen in practice. It's also pointless
    # to run dump_syms or archive dSYMs in a component build, where all of the
    # interesting symbols and debug info are tucked away in other libraries
    # beyond the set explicitly listed here.

    # This list must be updated with the two targets' deps list below, and
    # the list of _dsyms in :chrome_dsym_archive.
    _chrome_symbols_sources = [
      "$root_out_dir/$chrome_product_full_name.app/Contents/MacOS/$chrome_product_full_name",
      "$root_out_dir/chrome_crashpad_handler",
      "$root_out_dir/libEGL.dylib",
      "$root_out_dir/libGLESv2.dylib",
      "$root_out_dir/libvk_swiftshader.dylib",
      _framework_binary_path,
    ]
    if (build_with_internal_optimization_guide) {
      _chrome_symbols_sources +=
          [ "$root_out_dir/liboptimization_guide_internal.dylib" ]
    }

    foreach(helper_params, chrome_mac_helpers) {
      _chrome_symbols_sources += [ "$root_out_dir/${chrome_helper_name}${helper_params[2]}.app/Contents/MacOS/${chrome_helper_name}${helper_params[2]}" ]
    }

    action_foreach("chrome_dump_syms") {
      script = "//build/redirect_stdout.py"

      sources = _chrome_symbols_sources

      outputs =
          [ "$root_out_dir/{{source_file_part}}-$chrome_version_full.breakpad" ]

      dump_syms =
          "//third_party/breakpad:dump_syms($host_system_allocator_toolchain)"
      args = rebase_path(outputs, root_build_dir) + [
               rebase_path(get_label_info(dump_syms, "root_out_dir") + "/" +
                               get_label_info(dump_syms, "name"),
                           root_build_dir),
               "-d",
               "-m",
               "-g",
               rebase_path(
                   "$root_out_dir/{{source_file_part}}.dSYM/Contents/Resources/DWARF/{{source_file_part}}",
                   root_build_dir),
               "{{source}}",
             ]

      deps = [
        ":chrome_app",
        ":chrome_framework",
        "//components/crash/core/app:chrome_crashpad_handler",
        "//third_party/angle:libEGL",
        "//third_party/angle:libGLESv2",
        "//third_party/swiftshader/src/Vulkan:swiftshader_libvulkan",
        dump_syms,
      ]
      if (build_with_internal_optimization_guide) {
        deps += [ "//components/optimization_guide/internal:optimization_guide_internal" ]
      }

      foreach(helper_params, chrome_mac_helpers) {
        deps += [ ":chrome_helper_app_${helper_params[0]}" ]
      }
    }

    action("chrome_dsym_archive") {
      script = "//chrome/tools/build/mac/archive_symbols.py"

      # These are the dSYMs that will be archived. The sources list must be
      # the target outputs that correspond to the dSYMs (since a dSYM is a
      # directory it cannot be listed as a source file). The targets that
      # generate both the dSYM and binary image are listed in deps.
      _dsyms = [
        "$root_out_dir/$chrome_framework_name.dSYM",
        "$root_out_dir/$chrome_product_full_name.dSYM",
        "$root_out_dir/chrome_crashpad_handler.dSYM",
        "$root_out_dir/libEGL.dylib.dSYM",
        "$root_out_dir/libGLESv2.dylib.dSYM",
        "$root_out_dir/libvk_swiftshader.dylib.dSYM",
      ]
      if (build_with_internal_optimization_guide) {
        _dsyms += [ "$root_out_dir/liboptimization_guide_internal.dylib.dSYM" ]
      }

      deps = [
        ":chrome_app",
        ":chrome_framework",
        "//components/crash/core/app:chrome_crashpad_handler",
        "//third_party/angle:libEGL",
        "//third_party/angle:libGLESv2",
        "//third_party/swiftshader/src/Vulkan:swiftshader_libvulkan",
      ]
      if (build_with_internal_optimization_guide) {
        deps += [ "//components/optimization_guide/internal:optimization_guide_internal" ]
      }

      foreach(helper_params, chrome_mac_helpers) {
        _dsyms +=
            [ "$root_out_dir/${chrome_helper_name}${helper_params[2]}.dSYM" ]
        deps += [ ":chrome_helper_app_${helper_params[0]}" ]
      }

      sources = _chrome_symbols_sources

      _output = "$root_out_dir/$chrome_product_full_name.dSYM.tar.bz2"

      outputs = [ _output ]

      args = [ rebase_path(_output, root_out_dir) ] +
             rebase_path(_dsyms, root_out_dir)
    }
  } else {
    group("chrome_dump_syms") {
    }
    group("chrome_dsym_archive") {
    }
  }
}

group("dependencies") {
  public_deps = [
    "//build:branding_buildflags",
    "//build:chromeos_buildflags",
    "//chrome/browser",
    "//chrome/browser:buildflags",
    "//chrome/browser/policy:path_parser",
    "//chrome/child",
    "//chrome/common",
    "//chrome/gpu",
    "//chrome/renderer",
    "//chrome/utility",
    "//components/about_ui",
    "//components/crash/core/app",
    "//components/devtools/devtools_pipe",
    "//components/memory_system",
    "//components/startup_metric_utils",
    "//components/sync",
    "//components/upload_list:upload_list",
    "//content/public/child",
    "//pdf",
    "//services/tracing/public/cpp",
    "//third_party/blink/public:blink_devtools_frontend_resources",
    "//third_party/blink/public:blink_devtools_inspector_resources",
    "//v8:v8_headers",
  ]

  if (enable_ppapi) {
    public_deps += [ "//ppapi/host" ]
  }

  if (enable_printing) {
    public_deps += [ "//printing" ]
  }

  if (enable_nacl) {
    public_deps += [
      "//components/nacl/browser",
      "//components/nacl/renderer/plugin:nacl_trusted_plugin",
    ]
  }

  if (is_chromeos) {
    public_deps += [ "//chromeos/dbus/constants" ]
  }

  if (is_chromeos_lacros) {
    public_deps += [
      "//chromeos/lacros",
      "//chromeos/lacros/dbus",
    ]
  }

  if (is_chromeos_ash) {
    public_deps += [
      "//ash/constants",
      "//chrome/browser/ash",
      "//chromeos",
      "//chromeos/ash/components/memory",
    ]
  }
}

if (is_win) {
  process_version_rc_template("chrome_exe_version") {
    sources = [ "app/chrome_exe.ver" ]
    output = "$target_gen_dir/chrome_exe_version.rc"
  }

  process_version_rc_template("chrome_dll_version") {
    sources = [ "app/chrome_dll.ver" ]
    output = "$target_gen_dir/chrome_dll_version.rc"
  }

  # This manifest matches what GYP produced. It may not even be necessary.
  windows_manifest("chrome_dll_manifest") {
    sources = [
      as_invoker_manifest,
      common_controls_manifest,
    ]
  }

  process_version_rc_template("other_version") {
    sources = [ "app/other.ver" ]
    output = "$target_gen_dir/other_version.rc"
  }
}

copy("visual_elements_resources") {
  sources = [
    "//chrome/app/theme/$branding_path_component/win/tiles/Logo.png",
    "//chrome/app/theme/$branding_path_component/win/tiles/SmallLogo.png",
    "app/visual_elements_resources/chrome.VisualElementsManifest.xml",
  ]

  if (is_chrome_branded) {
    sources += [
      "//chrome/app/theme/$branding_path_component/win/tiles/LogoBeta.png",
      "//chrome/app/theme/$branding_path_component/win/tiles/LogoCanary.png",
      "//chrome/app/theme/$branding_path_component/win/tiles/LogoDev.png",
      "//chrome/app/theme/$branding_path_component/win/tiles/SmallLogoBeta.png",
      "//chrome/app/theme/$branding_path_component/win/tiles/SmallLogoCanary.png",
      "//chrome/app/theme/$branding_path_component/win/tiles/SmallLogoDev.png",
    ]
  }

  outputs = [ "$root_out_dir/{{source_file_part}}" ]
}

group("resources") {
  public_deps = [
    "//chrome/browser:resources",
    "//chrome/common:resources",
    "//chrome/renderer:resources",
  ]
}

group("extra_resources") {
  # Deps should be same as those in chrome_extra_paks() within chrome_paks.gni.
  public_deps = [
    "//chrome/browser/resources:resources",
    "//components/autofill/core/browser:autofill_address_rewriter_resources",
  ]
}

if (is_chrome_branded && !is_android) {
  if (!is_mac) {
    _preinstalled_apps_target_type = "copy"
  } else {
    _preinstalled_apps_target_type = "bundle_data"
  }

  target(_preinstalled_apps_target_type, "preinstalled_apps") {
    visibility = [ ":packed_resources" ]
    if (is_mac) {
      visibility += [
        ":chrome_framework",
        ":chrome_framework_shared_library",
      ]
    }

    sources = [ "browser/resources/default_apps/external_extensions.json" ]

    if (!is_mac) {
      outputs = [ "$root_out_dir/default_apps/{{source_file_part}}" ]
    } else {
      outputs = [ "{{bundle_contents_dir}}/Default Apps/{{source_file_part}}" ]
    }

    # Force anybody that depends on this to get the default apps as data files.
    data = process_file_template(sources, outputs)
  }
}

if (!is_android) {
  chrome_paks("packed_resources") {
    if (is_mac) {
      output_dir = "$root_gen_dir/repack"
      copy_data_to_bundle = true
    } else {
      output_dir = root_out_dir
      mark_as_data = true
    }

    if (enable_resource_allowlist_generation) {
      repack_allowlist = _chrome_resource_allowlist
      deps = [ ":resource_allowlist" ]
    }

    if (is_chrome_branded && !is_mac) {
      public_deps = [ ":preinstalled_apps" ]
    }

    # This needs to be in-sync with //chrome/app/packed_resources_integrity.h.
    files_to_hash = [
      "resources.pak",
      "chrome_100_percent.pak",
    ]
    if (enable_hidpi) {
      files_to_hash += [ "chrome_200_percent.pak" ]
    }
  }

  # This is extracted to deserialize build dependency around
  # :packed_resources_integrity_hash and improve build parallelism.
  source_set("packed_resources_integrity_header") {
    sources = [ "app/packed_resources_integrity.h" ]

    # chrome/app/packed_resources_integrity.cc file is generated in dependency.
    deps = [ ":packed_resources_integrity" ]
  }
}

if (!is_android) {
  repack("browser_tests_pak") {
    testonly = true
    sources = [ "$root_gen_dir/chrome/webui_test_resources.pak" ]
    output = "$root_out_dir/browser_tests.pak"
    deps = [ "//chrome/test/data/webui:resources" ]
  }
}

group("strings") {
  public_deps = [
    "//chrome/app:branded_strings",
    "//chrome/app:generated_resources",
    "//chrome/app/resources:locale_settings",
  ]
}

if (is_android) {
  java_cpp_enum("partner_bookmarks_javagen") {
    sources = [ "browser/android/bookmarks/partner_bookmarks_reader.h" ]
  }

  java_cpp_enum("offline_pages_enum_javagen") {
    sources = [ "browser/offline_pages/offline_page_utils.h" ]
  }

  java_cpp_enum("download_enum_javagen") {
    sources = [
      "browser/download/android/download_open_source.h",
      "browser/download/download_dialog_types.h",
      "browser/download/download_prompt_status.h",
    ]
  }

  source_set("chrome_android_core") {
    sources = [
      "app/android/chrome_jni_onload.cc",
      "app/android/chrome_jni_onload.h",
      "app/android/chrome_main_delegate_android.cc",
      "app/android/chrome_main_delegate_android.h",
      "app/chrome_main_delegate.cc",
      "app/chrome_main_delegate.h",
      "app/startup_timestamps.h",
    ]

    libs = [
      "android",
      "jnigraphics",
    ]

    public_deps = [
      "//chrome/browser",
      "//chrome/utility",
    ]

    deps = [
      ":dependencies",
      "//chrome/browser/ui",
      "//chrome/child",
      "//chrome/common",
      "//chrome/common:version_header",
      "//chrome/common/profiler",
      "//chrome/gpu",
      "//chrome/renderer",
      "//components/minidump_uploader",
      "//components/safe_browsing:buildflags",
      "//components/safe_browsing/android:safe_browsing_api_handler",
      "//components/safe_browsing/android:safe_browsing_mobile",
      "//components/stylus_handwriting/android",
      "//content/public/app",
    ]

    # Explicit dependency required for JNI registration to be able to
    # find the native side functions.
    if (is_android && is_component_build) {
      deps += [
        "//components/viz/service",
        "//device/gamepad",
        "//ui/events/devices",
      ]
    }

    if (is_android) {
      deps += [
        "//chrome/browser/flags:flags_android",
        "//components/crash/android:crash_android",
      ]
    }

    if (is_chromeos_ash) {
      public_deps += [ "//ui/lottie" ]
      deps += [ "//chrome/browser/ash/schedqos" ]
    }
  }
}

# Android also supports this, but uses
# //chrome/android:${_variant}_resource_allowlist.
if (is_win && enable_resource_allowlist_generation) {
  generate_resource_allowlist("resource_allowlist") {
    deps = [ ":chrome_dll" ]
    inputs = [ "$root_out_dir/chrome.dll.pdb" ]
    output = _chrome_resource_allowlist
  }
}

if (is_linux || is_chromeos) {
  if (is_official_build) {
    group("linux_symbols") {
      deps = [
        ":angle_egl_symbols",
        ":angle_gles_symbols",
        ":chrome_crashpad_symbols",
        ":chrome_symbols",
      ]
      if (is_linux) {
        deps += [ ":swiftshader_vk_symbols" ]
      }
      if (!is_chromeos) {
        deps += [ ":angle_libvulkan_symbols" ]
      }
      if (build_with_internal_optimization_guide) {
        deps += [ ":optimization_guide_symbols" ]
      }
    }
    extract_symbols("chrome_symbols") {
      binary = "$root_out_dir/chrome"

      if (current_cpu == "x86") {
        # GYP used "ia32" so keep that naming for back-compat.
        symbol_file = "$root_out_dir/chrome.breakpad.ia32"
      } else {
        symbol_file = "$root_out_dir/chrome.breakpad.$current_cpu"
      }

      deps = [ ":chrome" ]
    }
    extract_symbols("chrome_crashpad_symbols") {
      binary = "$root_out_dir/chrome_crashpad_handler"

      if (current_cpu == "x86") {
        # GYP used "ia32" so keep that naming for back-compat.
        symbol_file = "$root_out_dir/crashpad.breakpad.ia32"
      } else {
        symbol_file = "$root_out_dir/crashpad.breakpad.$current_cpu"
      }

      deps = [ "//components/crash/core/app:chrome_crashpad_handler" ]
    }
    extract_symbols("swiftshader_vk_symbols") {
      binary = "$root_out_dir/libvk_swiftshader.so"

      if (current_cpu == "x86") {
        # GYP used "ia32" so keep that naming for back-compat.
        symbol_file = "$root_out_dir/libvk_swiftshader.breakpad.ia32"
      } else {
        symbol_file = "$root_out_dir/libvk_swiftshader.breakpad.$current_cpu"
      }

      deps = [ "//third_party/swiftshader/src/Vulkan:swiftshader_libvulkan" ]
    }
    extract_symbols("angle_egl_symbols") {
      binary = "$root_out_dir/libEGL.so"

      if (current_cpu == "x86") {
        # GYP used "ia32" so keep that naming for back-compat.
        symbol_file = "$root_out_dir/angle_libegl.breakpad.ia32"
      } else {
        symbol_file = "$root_out_dir/angle_libegl.breakpad.$current_cpu"
      }

      deps = [ "//third_party/angle:libEGL" ]
    }
    extract_symbols("angle_gles_symbols") {
      binary = "$root_out_dir/libGLESv2.so"

      if (current_cpu == "x86") {
        # GYP used "ia32" so keep that naming for back-compat.
        symbol_file = "$root_out_dir/angle_libgles.breakpad.ia32"
      } else {
        symbol_file = "$root_out_dir/angle_libgles.breakpad.$current_cpu"
      }

      deps = [ "//third_party/angle:libGLESv2" ]
    }
    if (!is_chromeos) {
      extract_symbols("angle_libvulkan_symbols") {
        binary = "$root_out_dir/libvulkan.so.1"

        if (current_cpu == "x86") {
          # GYP used "ia32" so keep that naming for back-compat.
          symbol_file = "$root_out_dir/angle_libvulkan.breakpad.ia32"
        } else {
          symbol_file = "$root_out_dir/angle_libvulkan.breakpad.$current_cpu"
        }

        deps = [ "//third_party/vulkan-loader/src:libvulkan" ]
      }
    }
    if (build_with_internal_optimization_guide) {
      extract_symbols("optimization_guide_symbols") {
        binary = "$root_out_dir/liboptimization_guide_internal.so"
        if (current_cpu == "x86") {
          # GYP used "ia32" so keep that naming for back-compat.
          symbol_file =
              "$root_out_dir/optimization_guide_internal.breakpad.ia32"
        } else {
          symbol_file =
              "$root_out_dir/optimization_guide_internal.breakpad.$current_cpu"
        }

        deps = [ "//components/optimization_guide/internal:optimization_guide_internal" ]
      }
    }
  }

  # Copies some scripts and resources that are used for desktop integration.
  copy("xdg_mime") {
    sources = [
      "//chrome/tools/build/linux/chrome-wrapper",
      "//third_party/xdg-utils/scripts/xdg-mime",
      "//third_party/xdg-utils/scripts/xdg-settings",
    ]
    if (is_linux) {
      sources += [
        "//chrome/app/theme/$branding_path_component/linux/product_logo_48.png",
      ]
    } else {
      sources +=
          [ "//chrome/app/theme/$branding_path_component/product_logo_48.png" ]
    }
    outputs = [ "$root_out_dir/{{source_file_part}}" ]
  }
}

if (is_chromeos_ash) {
  embed_sections("section_embedded_chrome_binary") {
    binary_input = "$root_out_dir/chrome"
    sections_embedded_binary_output = "$root_out_dir/chrome.sections_embedded"
    deps = [ ":chrome" ]
  }
}

if (is_chromeos_lacros && is_official_build) {
  # This target is responsible for stripping symbols out of lacros files.
  # Adding/removing targets here, you also need a corresponding change
  # in src-internal for the release builder.
  group("strip_lacros_files") {
    deps = [
      ":strip_chrome_binary",
      ":strip_chrome_crashpad_handler",
      ":strip_libegl_so",
      ":strip_libglesv2_so",
      ":strip_nacl_helper",
    ]
    if (target_cpu == "arm" || target_cpu == "arm64") {
      deps += [ ":strip_nacl_helper_bootstrap" ]
    }
  }

  # This will strip chrome binary and produce chrome.debug with symbols.
  strip_binary("strip_chrome_binary") {
    binary_input = "$root_out_dir/chrome"
    symbol_output = "$root_out_dir/chrome.debug"
    stripped_binary_output = "$root_out_dir/chrome.stripped"
    deps = [ ":chrome" ]
  }
  strip_binary("strip_chrome_crashpad_handler") {
    binary_input = "$root_out_dir/chrome_crashpad_handler"
    symbol_output = "$root_out_dir/chrome_crashpad_handler.debug"
    stripped_binary_output = "$root_out_dir/chrome_crashpad_handler.stripped"
    deps = [ "//components/crash/core/app:chrome_crashpad_handler" ]
  }
  strip_binary("strip_libegl_so") {
    binary_input = "$root_out_dir/libEGL.so"
    symbol_output = "$root_out_dir/libEGL.so.debug"
    stripped_binary_output = "$root_out_dir/libEGL.so.stripped"
    deps = [ "//third_party/angle:libEGL" ]
  }
  strip_binary("strip_libglesv2_so") {
    binary_input = "$root_out_dir/libGLESv2.so"
    symbol_output = "$root_out_dir/libGLESv2.so.debug"
    stripped_binary_output = "$root_out_dir/libGLESv2.so.stripped"
    deps = [ "//third_party/angle:libGLESv2" ]
  }
  strip_binary("strip_nacl_helper") {
    binary_input = "$root_out_dir/nacl_helper"
    symbol_output = "$root_out_dir/nacl_helper.debug"
    stripped_binary_output = "$root_out_dir/nacl_helper.stripped"
    deps = [ "//components/nacl/loader:nacl_helper" ]
  }
  if (target_cpu == "arm" || target_cpu == "arm64") {
    strip_binary("strip_nacl_helper_bootstrap") {
      binary_input = "$root_out_dir/nacl_helper_bootstrap"
      symbol_output = "$root_out_dir/nacl_helper_bootstrap.debug"
      stripped_binary_output = "$root_out_dir/nacl_helper_bootstrap.stripped"
      if (target_cpu == "arm") {
        deps = [ "//native_client/src/trusted/service_runtime/linux:bootstrap" ]
      } else {
        deps = [ "//components/nacl/loader:nacl_helper_bootstrap" ]
      }
    }
  }
}
