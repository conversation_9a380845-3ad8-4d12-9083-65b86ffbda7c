# Copyright 2019 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

source_set("metrics") {
  sources = [
    "aw_component_metrics_provider_delegate.cc",
    "aw_component_metrics_provider_delegate.h",
    "aw_metrics_service_accessor.h",
    "aw_metrics_service_client.cc",
    "aw_metrics_service_client.h",
    "renderer_process_metrics_provider.cc",
    "renderer_process_metrics_provider.h",
    "visibility_metrics_logger.cc",
    "visibility_metrics_logger.h",
    "visibility_metrics_provider.cc",
    "visibility_metrics_provider.h",
  ]
  deps = [
    "//android_webview:browser_jni_headers",
    "//android_webview/browser/lifecycle",
    "//android_webview/common",
    "//base",
    "//components/component_updater",
    "//components/component_updater/android:embedded_component_loader",
    "//components/embedder_support/android/metrics",
    "//components/metrics",
    "//components/metrics:component_metrics",
    "//components/prefs",
    "//components/version_info/android:channel_getter",
    "//content/public/browser",
  ]
}
