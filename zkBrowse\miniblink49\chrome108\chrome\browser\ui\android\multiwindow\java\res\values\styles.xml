<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright 2021 The Chromium Authors
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file.
-->

<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- The dim amount should match the alpha of modal_dialog_scrim_color. -->
    <style name="Theme.Chromium.Multiwindow.CloseConfirmDialog" parent="ThemeOverlay.MaterialComponents.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:backgroundDimAmount">0.65</item>
        <item name="android:windowSoftInputMode">adjustResize|stateHidden</item>
        <item name="buttonBarPositiveButtonStyle">@style/FilledButton</item>
        <item name="buttonBarNegativeButtonStyle">@style/TextButton</item>
        <item name="dualControlLayoutVerticalPadding">@dimen/modal_dialog_control_vertical_padding_filled</item>
        <item name="dualControlLayoutHorizontalPadding">@dimen/modal_dialog_control_horizontal_padding_filled</item>
    </style>
</resources>
