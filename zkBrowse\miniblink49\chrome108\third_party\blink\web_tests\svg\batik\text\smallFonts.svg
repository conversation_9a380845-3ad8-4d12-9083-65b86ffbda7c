<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN"
"http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">

<!--

   Copyright 2001  The Apache Software Foundation 

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

-->
<!-- ========================================================================= -->
<!-- Tests small font sizes (<1) with various text features.                    -->
<!-- This is basically a copy of the textFeatures test file with a small       -->
<!-- viewbox set.                                                               -->
<!--                                                                           -->
<!-- <AUTHOR>                                     -->
<!-- @version $Id: smallFonts.svg,v 1.4 2004/08/18 07:12:21 vhardy Exp $                                                             -->
<!-- ========================================================================= -->
<?xml-stylesheet type="text/css" href="../resources/test.css" ?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="450" height="500" viewBox="0 0 9 10">
    <!-- ============================================================= -->
    <!-- Test content                                                  -->
    <!-- ============================================================= -->

    <defs>
        <filter id="blur" filterUnits="userSpaceOnUse" x="0" y="-1.6" width="4" height="2" filterRes="200">
            <feGaussianBlur stdDeviation="0.04 0.04" x="0" y="-1.6" width="4" height="2"/> 
        </filter> 

        <path id="Path1" style="fill:none; stroke:green; stroke-width:5" transform="scale(0.003,0.003)"
                  d="M 100 200 C 200 100 300 0 400 100 C 500 200 600 300 700 200 C 800 100 900 100 900 100"/>
       
    </defs>

    <g id="testContent" stroke-width="0.01">

        <text  x="50%" y="10%" font-size="0.3" text-anchor="middle" >
            Small font size test (viewBox="0 0 9 10")</text>

      <g font-family="dialog" font-size="0.3" 
                  text-anchor="start" fill="MidnightBlue">
        <text x="10%" y="20%">
        Text can change 
        <tspan font-size="0.6">size,</tspan>
        <tspan font-size="0.4" font-family="Serif">typeface,</tspan> 
        <tspan fill="green">color,</tspan>
        or 
        <tspan fill="none" font-style="oblique" stroke="red" stroke-width="0.02">style</tspan>
        </text>

        <text fill="MidnightBlue" x="10%" y="24%"> 
        within a single text element.
        </text> 

        <text x="10%" y="32%">Styling features include 
            <tspan font-weight="bold">weight,</tspan> 
            <tspan font-style="oblique">posture,</tspan> and 
            <tspan font-family="serif">typeface.</tspan>
        </text>

        <rect x="10%" y="35%" width="45%" height="10%" fill="DodgerBlue"/>
        <text x="10%" y="40%">
        Graphics attributes such as 
            <tspan fill="red" opacity="0.3">opacity</tspan> can be applied.
        </text>

        <text x="10%" y="50%">"text decoration" can include 
            <tspan text-decoration="underline">underline,</tspan>
            <tspan text-decoration="overline">overline,</tspan> and
        </text> 

        <text x="10%" y="54%">
        <tspan text-decoration="line-through">strikethrough. </tspan>
        <tspan dx="0.1" baseline-shift="super" font-size="0.2">Super</tspan> and
        <tspan baseline-shift="sub" font-size="0.2">sub</tspan> scripts are available. 
        </text>

        <text x="10%" y="62%" stroke-linecap="round" stroke-linejoin="round">
            Various 
            <tspan stroke="red" fill="none" stroke-width="0.01">outline
            </tspan>styles, 
            <tspan stroke="red" stroke-width="0.01" fill="blue" >fill colors
            </tspan>and
            <tspan stroke="red" stroke-width="0.02" fill="blue" >thicknesses
            </tspan>can be
        </text>

        <text x="10%" y="66%">used, and the outline stroke can be 
            <tspan stroke="black" fill="none" stroke-width="0.01" 
               stroke-linecap="round" stroke-linejoin="round">rounded</tspan>
            or 
            <tspan stroke="black" fill="none" stroke-width="0.01" 
               stroke-linecap="square" stroke-linejoin="miter">mitered.</tspan>
        </text>

        <text x="10%" y="75%">Text elements also can be filtered, transformed
        <tspan x="10%" y="79%">and placed on a path.</tspan></text>

       <g font-size="0.8" transform="translate(1.5, 9)">  
        <text x="0" y="0" filter="url(#blur)" fill="black" opacity="0.7"
                transform="skewX(40) translate(-0.12,-0.12)">
            Shadow
          </text>
          <text x="0" y="0" stroke="black" stroke-width="0.01" fill="white">
            Shadow
          </text>
        </g>

	  <g transform="translate(5,8)">
            <use xlink:href="#Path1"/>
	      <text font-size="0.3" style="text-anchor:start">
                <textPath xlink:href="#Path1" startOffset="0%">Small text <tspan fill="red" dy="-0.1">on</tspan><tspan dy="0.1"> a Path</tspan></textPath>
            </text>
         </g>

      </g>
    </g>

    <!-- ============================================================= -->
    <!-- Batik sample mark                                             -->
    <!-- ============================================================= -->
    <use transform="scale(0.02, 0.02)" xlink:href="../resources/batikLogo.svg#Batik_Tag_Box" />
    
</svg>
