<div id="dragme">This test verifies that we can get text/html from the drag object
during an ondrop event.  This test requires DRT.</div>

<div id="droptarget" contenteditable ondragover="dragover(event)" ondrop="drop(event)" style="height:200px"></div>
<div id="results">FAIL</div>

<script>
if (window.testRunner)
    testRunner.dumpAsText();

var undefined;
function removeFontName(text)
{
    if (!text)
        return text;
    return text.replace(/font-family: .+?; /g, "");
}

function drop(ev)
{
    console.log("text/plain: " + ev.dataTransfer.getData("text/plain"));
    // Remove the font name because it varies depending on the platform.
    console.log("text/html: " + removeFontName(ev.dataTransfer.getData("text/html")));
    if (ev.dataTransfer.getData("text/html") != undefined)
        document.getElementById("results").innerHTML = "PASS";
}

function dragover(ev)
{
  // drop() will only get called if we prevent the default dragover event.
  ev.preventDefault();
}

var dragMe = document.getElementById("dragme");
var startX = dragMe.offsetLeft + 10;
var startY = dragMe.offsetTop + 5;
var dropTarget = document.getElementById("droptarget");
var endX = dropTarget.offsetLeft + 10;
var endY = dropTarget.offsetTop + dropTarget.offsetHeight / 2;

var selection = window.getSelection();
selection.collapse(dragMe, 0);
selection.modify("extend", "forward", "sentence");

eventSender.mouseMoveTo(startX, startY);
eventSender.mouseDown();
eventSender.leapForward(200);
eventSender.mouseMoveTo(endX, endY);
eventSender.mouseUp();
</script>
