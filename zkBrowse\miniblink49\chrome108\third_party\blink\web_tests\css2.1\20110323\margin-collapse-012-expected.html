<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
    <head>
        <title>CSS Test: Margin collapsing - absolute positioning and siblings</title>
        <script src="../../resources/ahem.js"></script>
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#collapsing-margins">
        <meta name="flags" content="ahem image">
        <meta name="assert" content="Absolutely positioned boxes do not collapse margins with siblings.">
        <style type="text/css">
            #div1
            {
                border-top: 1em solid green;
                font: 20px/1em Ahem;
                height: 2em;
                width: 5em;
                border-bottom: 1em solid green;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is no red visible on the page.</p>
        <div id="div1">
        </div>
    </body>
</html>
