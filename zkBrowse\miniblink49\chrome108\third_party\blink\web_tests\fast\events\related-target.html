<html>
<head>
<script>

function out(event)
{
    var m = document.getElementById("m1");
    while (m.firstChild)
        m.removeChild(m.firstChild);
    m.appendChild(document.createTextNode("Moved out of "
        + event.target.getAttribute("id") + " to "
        + event.relatedTarget.getAttribute("id") + "."));
}

function over(event)
{
    var m = document.getElementById("m2");
    while (m.firstChild)
        m.removeChild(m.firstChild);
    m.appendChild(document.createTextNode("Moved over "
        + event.target.getAttribute("id") + " from "
        + event.relatedTarget.getAttribute("id") + "."));

    if (window.testRunner)
        testRunner.notifyDone();
}

function test()
{
    if (window.testRunner)
        testRunner.dumpAsText();

    if (window.eventSender) {
        eventSender.mouseMoveTo(50, 50);
        eventSender.mouseDown();
        eventSender.mouseUp();
        eventSender.mouseMoveTo(50, 150);
        eventSender.mouseDown();
        eventSender.mouseUp();
    }
}

</script>
</head>
<body onload="test()">
<div>
<div id="A" style="background-color: red; width:100px; height:100px" onmouseout="out(event)"></div>
<div id="B" style="background-color: blue; width:100px; height:100px" onmouseover="over(event)"></div>
</div>
<p>This test checks that the relatedTarget property is set on mouse out and mouse over events.</p>
<p>To perform the test, move the mouse into the red box, and then drag it out of the red box into the blue box.</p>
<p>The text below should say "Moved out of A to B. Moved over B from A."</p>
<span id="m1"></span> <span id="m2"></span>
</body>
</html>
