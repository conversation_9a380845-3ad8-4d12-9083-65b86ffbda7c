// Copyright 2018 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

module audio.mojom;

// This interface is implemented by clients of the audio service (e.g., the
// browser process) to register listeners of audio-device events.
interface DeviceListener {
  DevicesChanged();
};

// This interface is exposed by the audio service to allow trusted clients
// (e.g., the browser process) to subscribe to device-change events.
interface DeviceNotifier {
  // Registers a listener. Closing the pipe removes the subscription.
  RegisterListener(pending_remote<DeviceListener> listener);
};
