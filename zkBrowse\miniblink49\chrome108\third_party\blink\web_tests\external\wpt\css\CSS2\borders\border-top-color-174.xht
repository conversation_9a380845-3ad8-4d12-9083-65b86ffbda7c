<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Border-top-color set to transparent</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="G<PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-05-27 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#propdef-border-top-color" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#border-color-properties" />
        <link rel="match" href="../reference/ref-filled-green-100px-square.xht" />

        <meta name="assert" content="The 'border-top-color' set to transparent properly renders a border with no visible color." />
        <style type="text/css">
            #wrapper
            {
                background-color: green;
                width: 100px;
            }
            #test
            {
                border-top-style: solid;
                border-top-width: 100px;
                border-top-color: red;
                border-top-color: transparent;
                height: 0;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is a filled green square and <strong>no red</strong>.</p>
        <div id="wrapper">
            <div id="test"></div>
        </div>
    </body>
</html>