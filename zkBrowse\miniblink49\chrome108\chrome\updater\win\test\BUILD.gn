# Copyright 2019 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//testing/test.gni")

source_set("test_strings") {
  testonly = true

  sources = [
    "test_strings.cc",
    "test_strings.h",
  ]
}

source_set("test_common") {
  testonly = true

  sources = [
    "test_inheritable_event.cc",
    "test_inheritable_event.h",
    "test_initializer.cc",
    "test_initializer.h",
  ]

  deps = [
    "//base",
    "//chrome/updater:base",
    "//chrome/updater:constants_test",
  ]
}

source_set("test_executables") {
  testonly = true

  sources = [
    "test_executables.cc",
    "test_executables.h",
  ]

  deps = [
    ":test_common",
    ":test_strings",
    "//base",
    "//chrome/updater:base",
  ]
}

executable("updater_test_process") {
  testonly = true

  sources = [ "test_process_main.cc" ]

  deps = [
    ":test_common",
    ":test_strings",
    "//base",
    "//base/test:test_support",
    "//build/win:default_exe_manifest",
    "//chrome/updater:base",
    "//chrome/updater:constants_header",
  ]
}
