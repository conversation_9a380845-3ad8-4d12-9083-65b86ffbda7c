<html>
<head>
<script type="text/javascript">
function test() {

    if (!window.testRunner || !window.eventSender)
        return;

    testRunner.dumpAsText();

    var dragMe = document.getElementById("dragMe");
    var startX = dragMe.offsetLeft + (dragMe.offsetWidth / 2);
    var startY = dragMe.offsetTop + (dragMe.offsetTop / 2);
    var endX = startX + 100;
    var endY = startY + 100;

    eventSender.mouseMoveTo(startX, startY);
    eventSender.mouseDown();
    eventSender.mouseMoveTo(endX, endY);
    eventSender.mouseUp();
}
</script>
</head>
<body onload="test()">
    <img id="dragMe" src='resources/green-fixed-size-rect.svg'>
    <div>
        This test checks that dragging an SVG image doesn't crash the browser.
    </div>
</body>
</html>
