// GENERATED CONTENT - DO NOT EDIT
// Content was automatically extracted by <PERSON><PERSON> into webref
// (https://github.com/w3c/webref)
// Source: Priority Hints (https://wicg.github.io/priority-hints/)

enum FetchPriority { "high", "low", "auto" };

partial interface Request {
  readonly attribute FetchPriority priority;
};

partial dictionary RequestInit {
  FetchPriority priority;
};

partial interface HTMLImageElement {
    [CEReactions] attribute DOMString fetchPriority;
};

partial interface HTMLLinkElement {
    [CEReactions] attribute DOMString fetchPriority;
};

partial interface HTMLScriptElement {
    [CEReactions] attribute DOMString fetchPriority;
};

partial interface HTMLIFrameElement {
    [CEReactions] attribute DOMString fetchPriority;
};
