<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN"
"http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">

<!--

   Copyright 2003  The Apache Software Foundation 

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

-->
<!-- ====================================================================== -->
<!-- Test the handling of text decoration, especially in the presence of    -->
<!-- of dx, dy attributes.                                                  -->
<!-- <AUTHOR>                                             -->
<!-- @version $Id: textDecoration2.svg,v 1.3 2004/08/18 07:12:21 vhardy Exp $ -->
<!-- ====================================================================== -->
<?xml-stylesheet type="text/css" href="../resources/test.css" ?>  

<svg id="body" width="450" height="500" viewBox="0 0 450 500"
     xmlns="http://www.w3.org/2000/svg" 
     xmlns:xlink="http://www.w3.org/1999/xlink">

  <title>Text Decoration with dx,dy Tests</title>
    <text class="title" x="50%" y="40"
    >Text Decoration with dx, dy tests</text>

  <!-- <rect x="0" y="0" width="100%" height="100%" fill="lightgrey"/> -->
  <g transform="translate(0, 50)" 
    fill="rgb(0,0,0)" font-family="Lucida Grande" font-size="15.0">
    <text x="31.0" y="24.0" text-decoration="underline"
        >This is a text<tspan x="31.0" dy="1.2em"
        >that covers multiple</tspan><tspan x="31.0" dy="1.2em"
        >lines</tspan></text>
    
    <text x="231.0" y="24" text-decoration="underline"
        >This is a text<tspan x="231.0" y="41"
        >that covers multiple</tspan><tspan x="231" y="60"
        >lines</tspan></text>

    <text x="31.0" y="100" text-decoration="line-through"
        >Text <tspan dy="-5">that</tspan> <tspan dy="10">shifts</tspan></text>
    
    <text x="31.0" y="130"
        >Text <tspan dy="-5" text-decoration="line-through" 
        >that</tspan> <tspan dy="10">shifts</tspan></text>
    
    <text x="31.0" y="160"
        >Text <tspan dy="-5">that</tspan> <tspan 
        dy="10" text-decoration="line-through">shifts</tspan></text>
    
    <text x="31.0" y="200"
        >Text <tspan dy="-5">that</tspan> <tspan 
        dy="-5" text-decoration="line-through">shifts</tspan></text>

    <text x="231.0" y="100" text-decoration="overline"
        >Text <tspan dy="-5">that</tspan> <tspan dy="10">shifts</tspan></text>
    
    <text x="231.0" y="130"
        >Text <tspan dy="-5" text-decoration="overline" 
        >that</tspan> <tspan dy="10">shifts</tspan></text>
    
    <text x="231.0" y="160"
        >Text <tspan dy="-5">that</tspan> <tspan 
        dy="10" text-decoration="overline">shifts</tspan></text>
    
    <text x="231.0" y="200"
        >Text <tspan dy="-5">that</tspan> <tspan 
        dy="-5" text-decoration="overline">shifts</tspan></text>

    <text x="31.0" y="240" text-decoration="underline" 
        >Text <tspan dy="-5">that</tspan> <tspan dy="10">shifts</tspan></text>
    
    <text x="31.0" y="270"
        >Text <tspan dy="-5" text-decoration="underline" 
        >that</tspan> <tspan dy="10">shifts</tspan></text>
    
    <text x="31.0" y="300"
        >Text <tspan dy="-5">that</tspan> <tspan 
        dy="10" text-decoration="underline">shifts</tspan></text>
    
    <text x="31.0" y="330"
        >Text <tspan dy="-5">that</tspan> <tspan 
        dy="-5" text-decoration="underline">shifts</tspan></text>


    <text x="231.0" y="240" text-decoration="underline" fill="blue"
      ><tspan fill="black" stroke="none"
      >Text <tspan dy="-5">that</tspan> <tspan dy="10"
      >shifts</tspan></tspan></text>
    
    <text x="231.0" y="270" text-decoration="underline" fill="blue"
      ><tspan fill="black"
      >Text <tspan dy="-5" text-decoration="line-through" fill="red"
      ><tspan fill="black">that</tspan></tspan> <tspan dy="10"
      >shifts</tspan></tspan></text>
    
    <text x="231.0" y="300" text-decoration="underline" fill="blue"
      ><tspan fill="black"
        >Text <tspan dy="-5">that</tspan> <tspan 
        dy="10" text-decoration="underline" fill="red">
          <tspan fill="black">shifts</tspan></tspan></tspan></text>
    
    <text x="231.0" y="330" text-decoration="underline" fill="blue"
      ><tspan fill="black"
      >Text <tspan dy="-5" text-decoration="line-through" fill="red"
      ><tspan fill="black"
      >that <tspan dy="10" text-decoration="overline" fill="green"
      ><tspan fill="black"
      >shifts</tspan></tspan></tspan></tspan></tspan></text>

    <text x="131.0" y="370" text-decoration="underline" fill="blue"
      ><tspan fill="black">Text <tspan y="360">that</tspan> 
      <tspan y="375" >shifts</tspan></tspan></text>

    </g>
</svg>

