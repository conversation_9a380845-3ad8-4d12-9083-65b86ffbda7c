<head>
<script>
function log(t) {
document.getElementById('console').innerHTML += t +'<br>';
}

if (window.testRunner)
    testRunner.dumpAsText();
</script>
<body>
These checkboxes call their own and each others click() method from their onchange callbacks. Click the first checkbox. This should not crash or hang.
This also shows that click() is protected on per-element basis.<br>
<input id=cb type="checkbox" onchange="log('checkbox1 onchange enter');this.click();document.getElementById('cb2').click();log('checkbox1 onchange exit');">
<input id=cb2 type="checkbox" onchange="log('checkbox2 onchange enter');document.getElementById('cb').click();log('checkbox2 onchange exit')">
<div id=console></div>
<script>
    if (window.eventSender) {
        var cb = document.getElementById('cb');
        eventSender.mouseMoveTo(cb.offsetLeft + 5, cb.offsetTop + 5);
        eventSender.mouseDown();
        eventSender.mouseUp();
    }  
</script>

