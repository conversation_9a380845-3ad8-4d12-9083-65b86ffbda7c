<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8"/>
<title>direction/unicode-bidi: element as directional character with unicode-bidi unset, rtl</title>

<link rel="author" title="<PERSON>" href='mailto:<EMAIL>'/>
<link rel="help" href='http://www.w3.org/TR/css-writing-modes-3/#text-direction'/>
<link rel="match" href='reference/bidi-normal-005.html'/>
<meta name="assert" content='If direction is set but unicode-bidi is not set on an inline element, that element will NOT interact with the surrounding text like a strong or neutral directional character.'/>
<style type="text/css">
.test span { direction: rtl; }

 /* the following styles are not part of the test */
.test, .ref { font-size: 150%; border: 1px solid orange; margin: 10px; width: 10em; padding: 5px; clear: both; }
input { margin: 5px; }
@font-face {
    font-family: 'ezra_silregular';
    src: url('/fonts/sileot-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;
    }
.test, .ref { font-family: ezra_silregular, serif; }
</style>
</head>
<body>
<p class="instructions" dir="ltr">Test passes if the two boxes are identical.</p>


<!--Notes:
Key to entities used below:
        &#x5d0; ... &#x5d5; - The first six Hebrew letters (strongly RTL).
        &#x202d; - The LRO (left-to-right-override) formatting character.
        &#x202c; - The PDF (pop directional formatting) formatting character; closes LRO.
-->



<div class="test"><div dir="rtl">a > <span>&#x5d0; > &#x5d1;</span> > d</div>
                  <div dir="rtl">a > <span>b > c</span> > d</div>
                  </div>


<div class="ref"><div dir="rtl">&#x202d;d &lt; &#x5d1; &lt; &#x5d0; &lt; a&#x202c;</div>
	     <div dir="rtl">&#x202d;a &gt; b &gt; c &gt; d&#x202c;</div>
	     </div>






</body></html>