This test verifies that the correct sequence of keyboard events is generated for a keypress for certain special keys. To test manually, press keys and compare results to other browsers.

target - type - ctrl<PERSON><PERSON>,alt<PERSON><PERSON>,shift<PERSON>ey,metaKey - key - keyCode - charCode

Ampersand:
INPUT - keydown - false,false,true,false -7- 55 - 0. Value: "".
INPUT - keypress - false,false,true,false -7- 55 - 55. Value: "".
INPUT - textInput - 7. Value: "".
INPUT - keyup - false,false,true,false -7- 55 - 0. Value: "7".

Backspace:
INPUT - keydown - false,false,false,false -Backspace- 8 - 0. Value: "7".
INPUT - keyup - false,false,false,false -Backspace- 8 - 0. Value: "".

Percent:
INPUT - keydown - false,false,true,false -5- 53 - 0. Value: "".
INPUT - keypress - false,false,true,false -5- 53 - 53. Value: "".
INPUT - textInput - 5. Value: "".
INPUT - keyup - false,false,true,false -5- 53 - 0. Value: "5".

Backspace:
INPUT - keydown - false,false,false,false -Backspace- 8 - 0. Value: "5".
INPUT - keyup - false,false,false,false -Backspace- 8 - 0. Value: "".

Left parenthesis:
INPUT - keydown - false,false,true,false -9- 57 - 0. Value: "".
INPUT - keypress - false,false,true,false -9- 57 - 57. Value: "".
INPUT - textInput - 9. Value: "".
INPUT - keyup - false,false,true,false -9- 57 - 0. Value: "9".

Backspace:
INPUT - keydown - false,false,false,false -Backspace- 8 - 0. Value: "9".
INPUT - keyup - false,false,false,false -Backspace- 8 - 0. Value: "".

Right parenthesis:
INPUT - keydown - false,false,true,false -0- 48 - 0. Value: "".
INPUT - keypress - false,false,true,false -0- 48 - 48. Value: "".
INPUT - textInput - 0. Value: "".
INPUT - keyup - false,false,true,false -0- 48 - 0. Value: "0".

Backspace:
INPUT - keydown - false,false,false,false -Backspace- 8 - 0. Value: "0".
INPUT - keyup - false,false,false,false -Backspace- 8 - 0. Value: "".

Print screen:
INPUT - keydown - false,false,false,false -PrintScreen- 44 - 0. Value: "".
INPUT - keyup - false,false,false,false -PrintScreen- 44 - 0. Value: "".

