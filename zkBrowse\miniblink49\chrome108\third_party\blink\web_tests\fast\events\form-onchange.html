<!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML//EN">
<html>
<head>
<script src="../../resources/js-test.js"></script>
</head>
<body>
<p id="description"></p>
<div id="console"></div>

<div onchange="testPassed('div.onchange fired.');">
  <form onchange="testPassed('form.onchange fired.');">
      <input type="text" id="e" onchange="testPassed('input.onchange fired.');">
  </form>
</div>

<script>
description('Tests that the onchange event can be set on any element with an attribute.');

document.getElementById('e').focus();
document.execCommand("InsertText", false, "foo");
document.getElementById('e').blur();

</script>
</body>
</html>
