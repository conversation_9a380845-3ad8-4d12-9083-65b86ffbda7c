<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8"/>
<title>direction/unicode-bidi: element as directional character with unicode-bidi isolate, ltr</title>

<link rel="author" title="<PERSON>" href='mailto:<EMAIL>'/>
<link rel="help" href='http://www.w3.org/TR/css-writing-modes-3/#text-direction'/>
<link rel="match" href='reference/bidi-isolate-006.html'/>
<meta name="assert" content='If unicode-bidi:isolate is applied to an inline element, that element will interact with the surrounding text like a neutral directional character.'/>
<style type="text/css">
.test span { direction: ltr; unicode-bidi: isolate; }

 /* the following styles are not part of the test */
.test, .ref { font-size: 150%; border: 1px solid orange; margin: 10px; width: 10em; padding: 5px; clear: both; }
input { margin: 5px; }
@font-face {
    font-family: 'ezra_silregular';
    src: url('/fonts/sileot-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;
    }
.test, .ref { font-family: ezra_silregular, serif; }
</style>
</head>
<body>
<p class="instructions" dir="ltr">Test passes if the two boxes are identical.</p>


<!--Notes:
Key to entities used below:
        &#x5d0; ... &#x5d5; - The first six Hebrew letters (strongly RTL).
        &#x202d; - The LRO (left-to-right-override) formatting character.
        &#x202c; - The PDF (pop directional formatting) formatting character; closes LRO.
-->



<div class="test"><div dir="ltr">&#x5d0; > <span>b > c</span> > &#x5d3;</div>
                  <div dir="ltr">&#x5d0; > <span>&#x5d1; > &#x5d2;</span> > &#x5d3;</div>
                  </div>


<div class="ref"><div dir="ltr">&#x202d;&#x5d3; &lt; b &gt; c &lt; &#x5d0;&#x202c;</div>
      	     <div dir="ltr">&#x202d;&#x5d3; &lt; &#x5d2; &lt; &#x5d1; &lt; &#x5d0;&#x202c;</div>
      	     </div>






</body></html>