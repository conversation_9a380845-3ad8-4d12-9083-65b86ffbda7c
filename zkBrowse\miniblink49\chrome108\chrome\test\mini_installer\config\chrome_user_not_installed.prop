{"Files": {"$LOCAL_APPDATA\\$CHROME_DIR\\Application": {"exists": false}}, "RegistryEntries": {"HKEY_CURRENT_USER\\$CHROME_UPDATE_REGISTRY_SUBKEY": {"exists": "optional", "wow_key": "KEY_WOW64_32KEY", "values": {"pv": {}}}, "HKEY_CURRENT_USER\\$CHROME_UPDATE_REGISTRY_SUBKEY\\Commands": {"exists": "forbidden", "wow_key": "KEY_WOW64_32KEY"}, "HKEY_CURRENT_USER\\$BINARIES_UPDATE_REGISTRY_SUBKEY": {"exists": "forbidden", "wow_key": "KEY_WOW64_32KEY"}, "HKEY_CURRENT_USER\\$LAUNCHER_UPDATE_REGISTRY_SUBKEY": {"condition": "'$CHROME_SHORT_NAME' == 'Chrome'", "exists": "forbidden", "wow_key": "KEY_WOW64_32KEY"}, "HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\$CHROME_LONG_NAME": {"exists": "forbidden", "wow_key": "KEY_WOW64_32KEY"}, "HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\Windows Error Reporting\\RuntimeExceptionHelperModules": {"exists": "optional", "values": {"$LOCAL_APPDATA\\$CHROME_DIR\\Application\\$MINI_INSTALLER_FILE_VERSION\\chrome_wer.dll": {}}}, "HKEY_CURRENT_USER\\Software\\Classes\\$CHROME_HTML_PROG_ID$USER_SPECIFIC_REGISTRY_SUFFIX": {"exists": "forbidden"}, "HKEY_CURRENT_USER\\Software\\Classes\\$CHROME_SHORT_NAME$USER_SPECIFIC_REGISTRY_SUFFIX": {"exists": "forbidden"}, "HKEY_CURRENT_USER\\Software\\Clients\\StartMenuInternet\\$CHROME_SHORT_NAME$USER_SPECIFIC_REGISTRY_SUFFIX": {"exists": "forbidden"}, "HKEY_CURRENT_USER\\Software\\RegisteredApplications": {"exists": "optional", "values": {"$CHROME_LONG_NAME$USER_SPECIFIC_REGISTRY_SUFFIX": {}}}, "HKEY_CURRENT_USER\\Software\\Classes\\CLSID\\$CHROME_TOAST_ACTIVATOR_CLSID": {"exists": "forbidden"}, "HKEY_CURRENT_USER\\Software\\Classes\\CLSID\\$CHROME_ELEVATOR_CLSID": {"exists": "forbidden"}, "HKEY_CURRENT_USER\\Software\\Classes\\AppID\\$CHROME_ELEVATOR_CLSID": {"exists": "forbidden"}, "HKEY_CURRENT_USER\\Software\\Classes\\Interface\\$CHROME_ELEVATOR_IID": {"exists": "forbidden"}, "HKEY_CURRENT_USER\\Software\\Classes\\TypeLib\\$CHROME_ELEVATOR_IID": {"exists": "forbidden"}}}