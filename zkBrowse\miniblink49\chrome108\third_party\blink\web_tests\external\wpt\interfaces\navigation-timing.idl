// GENERATED CONTENT - DO NOT EDIT
// Content was automatically extracted by <PERSON><PERSON> into webref
// (https://github.com/w3c/webref)
// Source: Navigation Timing Level 2 (https://w3c.github.io/navigation-timing/)

[Exposed=Window]
interface PerformanceNavigationTiming : PerformanceResourceTiming {
    readonly        attribute DOMHighResTimeStamp  unloadEventStart;
    readonly        attribute DOMHighResTimeStamp  unloadEventEnd;
    readonly        attribute DOMHighResTimeStamp  domInteractive;
    readonly        attribute DOMHighResTimeStamp  domContentLoadedEventStart;
    readonly        attribute DOMHighResTimeStamp  domContentLoadedEventEnd;
    readonly        attribute DOMHighResTimeStamp  domComplete;
    readonly        attribute DOMHighResTimeStamp  loadEventStart;
    readonly        attribute DOMHighResTimeStamp  loadEventEnd;
    readonly        attribute NavigationTimingType type;
    readonly        attribute unsigned short       redirectCount;
    [Default] object toJSON();
};

enum NavigationTimingType {
    "navigate",
    "reload",
    "back_forward",
    "prerender"
};

[Exposed=Window]
interface PerformanceTiming {
  readonly attribute unsigned long long navigationStart;
  readonly attribute unsigned long long unloadEventStart;
  readonly attribute unsigned long long unloadEventEnd;
  readonly attribute unsigned long long redirectStart;
  readonly attribute unsigned long long redirectEnd;
  readonly attribute unsigned long long fetchStart;
  readonly attribute unsigned long long domainLookupStart;
  readonly attribute unsigned long long domainLookupEnd;
  readonly attribute unsigned long long connectStart;
  readonly attribute unsigned long long connectEnd;
  readonly attribute unsigned long long secureConnectionStart;
  readonly attribute unsigned long long requestStart;
  readonly attribute unsigned long long responseStart;
  readonly attribute unsigned long long responseEnd;
  readonly attribute unsigned long long domLoading;
  readonly attribute unsigned long long domInteractive;
  readonly attribute unsigned long long domContentLoadedEventStart;
  readonly attribute unsigned long long domContentLoadedEventEnd;
  readonly attribute unsigned long long domComplete;
  readonly attribute unsigned long long loadEventStart;
  readonly attribute unsigned long long loadEventEnd;
  [Default] object toJSON();
};

[Exposed=Window]
interface PerformanceNavigation {
  const unsigned short TYPE_NAVIGATE = 0;
  const unsigned short TYPE_RELOAD = 1;
  const unsigned short TYPE_BACK_FORWARD = 2;
  const unsigned short TYPE_RESERVED = 255;
  readonly attribute unsigned short type;
  readonly attribute unsigned short redirectCount;
  [Default] object toJSON();
};

[Exposed=Window]
partial interface Performance {
  [SameObject]
  readonly attribute PerformanceTiming timing;
  [SameObject]
  readonly attribute PerformanceNavigation navigation;
};
