<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>CSS Test: Height of root element</title>
  <link rel="author" title="<PERSON>" href="mailto:<EMAIL>"/>
  <link rel="alternate" href="http://www.hixie.ch/tests/adhoc/css/background/14.html" type="text/html"/>
  <link rel="help" href="http://www.w3.org/TR/CSS21/visudet.html#Computing_heights_and_margins"/>
  <link rel="help" href="http://www.w3.org/TR/CSS21/visudet.html#containing-block-details"/>
  <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background"/>
  <link rel="match" href="background-root-017-ref.xht" />

<style type="text/css"><![CDATA[
   body { border: solid lime; background: green; color: white; }
   html { border: solid blue; background: navy; color: yellow; }
   :link, :visited { color: inherit; background: transparent; }
   * { margin: 1em; padding: 1em; }
]]></style>

</head>
<body>
<p>This paragraph should be a box that is green with a green
border, which should be in a navy box with a bright blue border.
The rest of the page should also be navy. <strong>The outer sea
of blue should extend to the edges of the viewport, but the
boxes should just wrap around this text and not grow to match
the viewport.</strong></p>
</body>
</html>
