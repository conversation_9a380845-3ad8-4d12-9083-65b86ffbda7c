<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">

 <head>

  <title>CSS Writing Modes Test: position absolute and 'vertical-lr' - block flow direction of block-level boxes</title>

  <link rel="author" title="Gérard Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" />
  <link rel="help" href="http://www.w3.org/TR/css-writing-modes-3/#block-flow" title="3.1 Block Flow Direction: the writing-mode property" />
  <link rel="match" href="block-flow-direction-001-ref.xht" />

  <meta content="ahem" name="flags" />
  <meta content="This test checks that an absolutely positioned box with its 'writing-mode' set to 'vertical-lr' establishes a block formating context with a left-to-right block flow direction." name="assert" />

  <link rel="stylesheet" type="text/css" href="/fonts/ahem.css" />
  <style type="text/css"><![CDATA[
  body
    {
      color: yellow;
      font: 20px/1 Ahem;
    }

  div#abs-pos
    {
      height: 9em;
      left: auto;
      position: absolute;
      writing-mode: vertical-lr;
    }

  div#abs-pos > div
    {
      background-color: blue;
      border-bottom: blue solid 1em;
      border-top: blue solid 1em;
    }

  div.left-border
    {
      border-left: blue solid 1em;
    }

  div#right-border
    {
      border-right: blue solid 1em;
    }
  ]]></style>
 </head>

 <body>

  <div id="abs-pos">

<!-- The 1st left-most line of "P" --> <div class="left-border">AAAAAAA</div>

<!-- The 2nd left-most line of "P" --> <div>B&nbsp; C&nbsp;&nbsp; </div>

<!-- The 3rd left-most line of "P" --> <div>D&nbsp; E&nbsp;&nbsp; </div>

<!-- The 4th left-most line of "P" --> <div>FFFF&nbsp;&nbsp; </div>



<!-- The left-most line of "A" --> <div class="left-border">GGGGGGG</div>

<!-- The 2nd left-most line of "A" --> <div>H&nbsp; J&nbsp;&nbsp; </div>

<!-- The 3rd left-most line of "A" --> <div>K&nbsp; L&nbsp;&nbsp; </div>

<!-- The 4th left-most line of "A" --> <div>MMMMMMM</div>



<!-- The 1st left-most line of left-most "S" --> <div class="left-border">NNNN&nbsp; Q</div>

<!-- The 2nd left-most line of left-most "S" --> <div>R&nbsp; S&nbsp; T</div>

<!-- The 3rd left-most line of left-most "S" --> <div>U&nbsp; V&nbsp; W</div>

<!-- The 4th left-most line of left-most "S" --> <div>X&nbsp; YYYY</div>



<!-- The left-most line of right-most "S" --> <div class="left-border">aaaa&nbsp; b</div>

<!-- The 2nd left-most line of right-most "S" --> <div>c&nbsp; d&nbsp; e</div>

<!-- The 3rd left-most line of right-most "S" --> <div>f&nbsp; g&nbsp; h</div>

<!-- The 4th left-most line of right-most "S" --> <div id="right-border">j&nbsp; kkkk</div>

  </div>

 </body>
</html>