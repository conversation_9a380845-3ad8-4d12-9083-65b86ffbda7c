<!DOCTYPE HTML>
<html>
<body>
<div>
    This test passes if there is a 100 x 100 green rect.<br/>
    <canvas id="canvas" width="200" height="200"></canvas>
</div>
<script>
    function finishTest() {
        var canvas = document.getElementById('canvas');
        var context = canvas.getContext('2d');
        context.fillStyle = "green";
        context.fillRect(0, 0, 100, 100);
        if (window.testRunner)
            testRunner.notifyDone();
    }

    if (window.testRunner) {
        testRunner.waitUntilDone();
        testRunner.setBackingScaleFactor(2, finishTest);
    } else
        finishTest();
</script>
</body>
</html>
