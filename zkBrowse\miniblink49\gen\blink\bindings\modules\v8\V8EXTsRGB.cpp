// Copyright 2014 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// This file has been auto-generated by code_generator_v8.py. DO NOT MODIFY!

#include "config.h"
#include "V8EXTsRGB.h"

#include "bindings/core/v8/ExceptionState.h"
#include "bindings/core/v8/V8DOMConfiguration.h"
#include "bindings/core/v8/V8GCController.h"
#include "bindings/core/v8/V8ObjectConstructor.h"
#include "core/dom/ContextFeatures.h"
#include "core/dom/Document.h"
#include "core/dom/Element.h"
#include "platform/RuntimeEnabledFeatures.h"
#include "platform/TraceEvent.h"
#include "wtf/GetPtr.h"
#include "wtf/RefPtr.h"

// V8 10.8 compatibility layer
#if V8_MAJOR_VERSION >= 10
#include "../../../../../v8_10_8/v8_compatibility.h"
#endif

namespace blink {

// Suppress warning: global constructors, because struct WrapperTypeInfo is trivial
// and does not depend on another global objects.
#if defined(COMPONENT_BUILD) && defined(WIN32) && COMPILER(CLANG)
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wglobal-constructors"
#endif
const WrapperTypeInfo V8EXTsRGB::wrapperTypeInfo = { gin::kEmbedderBlink, V8EXTsRGB::domTemplate, V8EXTsRGB::refObject, V8EXTsRGB::derefObject, V8EXTsRGB::trace, 0, V8EXTsRGB::visitDOMWrapper, V8EXTsRGB::preparePrototypeObject, V8EXTsRGB::installConditionallyEnabledProperties, "EXTsRGB", 0, WrapperTypeInfo::WrapperTypeObjectPrototype, WrapperTypeInfo::ObjectClassId, WrapperTypeInfo::NotInheritFromEventTarget, WrapperTypeInfo::Dependent, WrapperTypeInfo::WillBeGarbageCollectedObject };
#if defined(COMPONENT_BUILD) && defined(WIN32) && COMPILER(CLANG)
#pragma clang diagnostic pop
#endif

// This static member must be declared by DEFINE_WRAPPERTYPEINFO in EXTsRGB.h.
// For details, see the comment of DEFINE_WRAPPERTYPEINFO in
// bindings/core/v8/ScriptWrappable.h.
const WrapperTypeInfo& EXTsRGB::s_wrapperTypeInfo = V8EXTsRGB::wrapperTypeInfo;

namespace EXTsRGBV8Internal {

} // namespace EXTsRGBV8Internal

void V8EXTsRGB::visitDOMWrapper(v8::Isolate* isolate, ScriptWrappable* scriptWrappable, const v8::Persistent<v8::Object>& wrapper)
{
    EXTsRGB* impl = scriptWrappable->toImpl<EXTsRGB>();
    // The canvas() method may return a reference or a pointer.
    if (Node* owner = WTF::getPtr(impl->canvas())) {
        Node* root = V8GCController::opaqueRootForGC(isolate, owner);
#if V8_MAJOR_VERSION >= 10
        v8::IsolateCompat::SetReferenceFromGroup(isolate, v8::UniqueId(reinterpret_cast<intptr_t>(root)), wrapper);
#else
        #if V8_MAJOR_VERSION >= 10`r`n        v8::IsolateCompat::SetReferenceFromGroup(isolate, v8::UniqueId(reinterpret_cast<intptr_t>(root)), wrapper);`r`n#else`r`n        isolate->SetReferenceFromGroup(v8::UniqueId(reinterpret_cast<intptr_t>(root)), wrapper);`r`n#endif
#endif
        return;
    }
}

static void installV8EXTsRGBTemplate(v8::Local<v8::FunctionTemplate> functionTemplate, v8::Isolate* isolate)
{
    functionTemplate->ReadOnlyPrototype();

    v8::Local<v8::Signature> defaultSignature;
    defaultSignature = V8DOMConfiguration::installDOMClassTemplate(isolate, functionTemplate, "EXTsRGB", v8::Local<v8::FunctionTemplate>(), V8EXTsRGB::internalFieldCount,
        0, 0,
        0, 0,
        0, 0);
    v8::Local<v8::ObjectTemplate> instanceTemplate = functionTemplate->InstanceTemplate();
    ALLOW_UNUSED_LOCAL(instanceTemplate);
    v8::Local<v8::ObjectTemplate> prototypeTemplate = functionTemplate->PrototypeTemplate();
    ALLOW_UNUSED_LOCAL(prototypeTemplate);
    static const V8DOMConfiguration::ConstantConfiguration V8EXTsRGBConstants[] = {
        {"SRGB_EXT", 0x8C40, 0, 0, V8DOMConfiguration::ConstantTypeUnsignedLong},
        {"SRGB_ALPHA_EXT", 0x8C42, 0, 0, V8DOMConfiguration::ConstantTypeUnsignedLong},
        {"SRGB8_ALPHA8_EXT", 0x8C43, 0, 0, V8DOMConfiguration::ConstantTypeUnsignedLong},
        {"FRAMEBUFFER_ATTACHMENT_COLOR_ENCODING_EXT", 0x8210, 0, 0, V8DOMConfiguration::ConstantTypeUnsignedLong},
    };
    V8DOMConfiguration::installConstants(isolate, functionTemplate, prototypeTemplate, V8EXTsRGBConstants, WTF_ARRAY_LENGTH(V8EXTsRGBConstants));

    // Custom toString template
#if V8_MAJOR_VERSION < 7
    functionTemplate->Set(v8AtomicString(isolate, "toString"), V8PerIsolateData::from(isolate)->toStringTemplate());
#endif
}

v8::Local<v8::FunctionTemplate> V8EXTsRGB::domTemplate(v8::Isolate* isolate)
{
    return V8DOMConfiguration::domClassTemplate(isolate, const_cast<WrapperTypeInfo*>(&wrapperTypeInfo), installV8EXTsRGBTemplate);
}

bool V8EXTsRGB::hasInstance(v8::Local<v8::Value> v8Value, v8::Isolate* isolate)
{
    return V8PerIsolateData::from(isolate)->hasInstance(&wrapperTypeInfo, v8Value);
}

v8::Local<v8::Object> V8EXTsRGB::findInstanceInPrototypeChain(v8::Local<v8::Value> v8Value, v8::Isolate* isolate)
{
    return V8PerIsolateData::from(isolate)->findInstanceInPrototypeChain(&wrapperTypeInfo, v8Value);
}

EXTsRGB* V8EXTsRGB::toImplWithTypeCheck(v8::Isolate* isolate, v8::Local<v8::Value> value)
{
    return hasInstance(value, isolate) ? toImpl(v8::Local<v8::Object>::Cast(value)) : 0;
}

void V8EXTsRGB::refObject(ScriptWrappable* scriptWrappable)
{
#if !ENABLE(OILPAN)
    scriptWrappable->toImpl<EXTsRGB>()->ref();
#endif
}

void V8EXTsRGB::derefObject(ScriptWrappable* scriptWrappable)
{
#if !ENABLE(OILPAN)
    scriptWrappable->toImpl<EXTsRGB>()->deref();
#endif
}

} // namespace blink
