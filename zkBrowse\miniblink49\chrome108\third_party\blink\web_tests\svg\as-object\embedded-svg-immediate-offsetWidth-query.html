<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<script>
window.enablePixelTesting = true;
window.jsTestIsAsync = true;
</script>
<script src="../../resources/js-test.js"></script>
<script src="../../resources/run-after-layout-and-paint.js"></script>
<script type="text/javascript">
    function repaintTest() {
        // Test initial reported offsetWidth is 0, as we're not in the tree yet.
        newObject = document.createElement("object");
        newObject.setAttribute("style", "border: 1px black solid; background: red");
        shouldBe("newObject.offsetWidth", "0");
        shouldBe("newObject.offsetWidth", "0");

        // Test that the initial width is 300px + 1px border on each side.
        document.body.insertBefore(newObject, document.getElementById("description"));
        shouldBe("newObject.offsetWidth", "302");
        shouldBe("newObject.offsetWidth", "302");

        // Switch to document: "<svg xmlns="http://www.w3.org/2000/svg" width="400" height="200"><rect width="400" height="200" fill="green"/>&lt;script><![CDATA[window.parent.postMessage('running', '*');]]>&lt;/script></svg>".

        // The replacement won't synchronous; wait on its completion.
        window.onmessage = function () {
            shouldBe("newObject.offsetWidth", "402");
            shouldBe("newObject.offsetWidth", "402");
            finishJSTest();
        };
        newObject.setAttribute("data", "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI0MDAiIGhlaWdodD0iMjAwIj48cmVjdCB3aWR0aD0iNDAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iZ3JlZW4iLz48c2NyaXB0PjwhW0NEQVRBW3dpbmRvdy5wYXJlbnQucG9zdE1lc3NhZ2UoJ3J1bm5pbmcnLCAnKicpO11dPjwvc2NyaXB0Pjwvc3ZnPg==");
    }
</script>
</head>
<body style="margin: 0px" onload="runAfterLayoutAndPaint(repaintTest);">

<p id="description"></p>
<div id="console"></div>

<script>
    description("Tests that querying offsetWidth twice leads to the same result - no red background should be visible.");
</script>
</body>
</html>
