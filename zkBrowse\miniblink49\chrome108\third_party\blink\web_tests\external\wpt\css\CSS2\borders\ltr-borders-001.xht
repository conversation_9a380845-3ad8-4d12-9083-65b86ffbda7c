<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Borders drawn in visual order even when direction set to left-to-right</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#bidi-box-model" />
        <meta name="flags" content="ahem image" />
        <meta name="assert" content="Borders are drawn in visual order depending on the direction of content." />
        <link rel="stylesheet" type="text/css" href="/fonts/ahem.css" />
        <style type="text/css">
            div
            {
                margin-top: 10px;
                width: 1.5in;
            }
            span
            {
                border-left: solid blue;
                border-right: solid orange;
                font: 1.5em/1em Ahem;
            }
        </style>
    </head>
    <body>
        <p>Test passes if the shape of the boxes below match the same shape as the reference image. It is ok if the scale is different.</p>
        <div>
            <span>xx xx xx xx xx xx</span>
        </div>
        <div>
            Reference:<br />
            <img alt="Image download support must be enabled" src="support/ltr-borders-001.png" />
        </div>
    </body>
</html>