<!doctype html>
<meta charset=utf-8>
<title>IndexedDB: delete requests are processed as a FIFO queue</title>
<link rel="help" href="https://w3c.github.io/IndexedDB/#request-connection-queue">
<script src="/resources/testharness.js"></script>
<script src="/resources/testharnessreport.js"></script>
<script src="resources/support.js"></script>
<script>

let saw;
indexeddb_test(
    (t, db) => {
        saw = expect(t, ['delete1', 'delete2']);
        let r = indexedDB.deleteDatabase(db.name);
        r.onerror = t.unreached_func('delete should succeed');
        r.onsuccess = t.step_func(e => saw('delete1'));
    },
    (t, db) => {
        let r = indexedDB.deleteDatabase(db.name);
        r.onerror = t.unreached_func('delete should succeed');
        r.onsuccess = t.step_func(e => saw('delete2'));

        db.close();
    },
    'Deletes are processed in order');

</script>
