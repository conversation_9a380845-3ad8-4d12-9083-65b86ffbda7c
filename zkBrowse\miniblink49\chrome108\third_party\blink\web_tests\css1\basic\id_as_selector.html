<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 1.5 ID as selector</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
#one {color: green;}
#a1 {color: green;}
P#two, P#two2 {color: blue;}
P#three, P#three2 {color: purple;}
#four {color: green;}
#a2 {color: green;}
P#five, P#five2 {color: blue;}
P#six, P#six2 {color: purple;}</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>#one {color: green;}
#a1 {color: green;}
P#two, P#two2 {color: blue;}
P#three, P#three2 {color: purple;}
#four {color: green;}
#a2 {color: green;}
P#five, P#five2 {color: blue;}
P#six, P#six2 {color: purple;}</PRE>
<HR>
<P ID="one">
This sentence should be green.
</P>
<P ID="a1">
This sentence should be green.
</P>
<P ID="two">
This paragraph should be blue [<TT>ID="two"</TT>].
</P>
<PRE ID="two2">This sentence should NOT be blue [PRE ID="two2"].
</PRE>
<PRE ID="three">This sentence should be black, not purple [PRE ID="three"].
</PRE>
<UL>
<LI ID="three2">This sentence should NOT be purple.
</UL>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P ID="four">
This sentence should be green.
</P>
<P ID="a2">
This sentence should be green.
</P>
<P ID="five">
This paragraph should be blue [<TT>ID="five"</TT>].
</P>
<PRE ID="five2">This sentence should NOT be blue [PRE ID="five2"].
</PRE>
<PRE ID="six">This sentence should be black [PRE ID="six"].
</PRE>
<UL>
<LI ID="six2">This sentence should NOT be purple.
</UL>
</TD></TR></TABLE></BODY>
</HTML>
