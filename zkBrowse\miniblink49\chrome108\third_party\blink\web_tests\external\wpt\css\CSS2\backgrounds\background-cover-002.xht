<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background over padding</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="<PERSON><PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-04-21 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <meta name="assert" content="Background covers the padding of the box." />
        <style type="text/css">
            div
            {
                background: orange;
                border: solid black;
                padding: 30px;
            }
        </style>
    </head>
    <body>
        <p>Test passes if the box below is orange with a black border.</p>
        <div>Filler Text</div>
    </body>
</html>