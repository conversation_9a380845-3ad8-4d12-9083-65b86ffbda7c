<html>
<head>
<script>
function searchTestUniqueString()
{
    var variable = 0;
    // searchTestUniqueString two occurences on the same line searchTestUniqueString
    var variable2 = 0;
}
</script><script>
function doSomething()
{
    searchTestUniqueString();
    // SEARCHTestUniqueString();
}
</script>
<link href="search.css" rel="stylesheet" type="text/css">
<script>
function searchTestUniqueString2()
{
    var variable = 0;
    /* searchTestUniqueString two occurences on the same line searchTestUniqueString */ } </script><script> function doSomething2() { searchTestUniqueString();
    // SEARCHTestUniqueString();
}
</script>
</script>
<body>
</body>
</html>

