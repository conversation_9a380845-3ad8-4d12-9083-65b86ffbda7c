<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background on root element - background-position</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="G<PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-05-06 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <link rel="match" href="background-root-002-ref.xht" />

        <meta name="flags" content="image" />
        <meta name="assert" content="Background of the root has the initial background-position of 0, 0 and not the canvas." />
        <style type="text/css">
            html
            {
                background: url("support/green_box.png") repeat-x;
                color: white;
                margin: 1in;
            }

            body
            {
            	margin: 8px;
            }
        </style>
    </head>
    <body>
        <p>Test passes if this text is within the green box.</p>
    </body>
</html>
