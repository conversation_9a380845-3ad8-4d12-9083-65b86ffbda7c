<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
    <head>
        <title>CSS Test: Table-caption</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/">
        <link rel="help" href="http://www.w3.org/TR/CSS21/tables.html#table-display">
        <meta name="flags" content="">
        <meta name="assert" content="An element with 'display: table-caption' is rendered as if it were a table caption.">
        <style type="text/css">
            .table
            {
                display: table;
            }
            .caption
            {
                display: table-caption;
            }
            .tr
            {
                display: table-row;
            }
            .td
            {
                background: black;
                display: table-cell;
                height: 3em;
                width: 3em;
            }
        </style>
    </head>
    <body>
        <p>Test passes if the "Filler Text" is above the box.</p>
        <div class="table">
            <div class="caption">Filler Text</div>
            <div class="tr">
                <div class="td"></div>
                <div class="td"></div>
            </div>
            <div class="tr">
                 <div class="td"></div>
                 <div class="td"></div>
            </div>
        </div>
    </body>
</html>