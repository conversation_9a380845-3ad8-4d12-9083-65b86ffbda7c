<html>
<head>
	<title>Untitled</title>
	<script type="text/javascript">

if (window.testRunner)
    testRunner.dumpAsText();
	
function debug(msg)
{
    var span = document.createElement("span");
    span.innerHTML = msg + '<br>';
    document.getElementById("console").appendChild(span);
}

var changeEventCount = 0;

		function main() {
			var cb = document.getElementById("myCheckbox");
			
			// This line should not fire a change event
			cb.checked = !cb.checked;
			
			// This line should fire a change event
			cb.click();
			
			// This line should not fire a change event
			cb.setAttribute("checked", "checked");

			if (changeEventCount != 1) {
			    debug("FAIL: expected 1 change event, got " + changeEventCount); 
			} else {
			    debug("PASS: got exactly one change event.");
			}
		}
	
	</script>
</head>
<body onload="main();">
<p>This test checks that programmaticly changing the checked state of
a checkbox does not fire the "change" event (but user action like
calling "click" does).</p>
<div id="console"></div>
	<input type="checkbox" id="myCheckbox" onchange="changeEventCount++">
</body>
</html>
