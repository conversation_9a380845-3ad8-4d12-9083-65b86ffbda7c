<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.5.01 margin-top</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
.zero {background-color: silver; margin-top: 0;}
.one {margin-top: 0.5in; background-color: aqua;}
.two {margin-top: 25px; background-color: aqua;}
.three {margin-top: 5em; background-color: aqua;}
.four {margin-top: 25%; background-color: aqua;}
.five {margin-top: 25px;}
.six {margin-top: -10px; background-color: aqua;}
P, UL {margin-bottom: 0;}</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.zero {background-color: silver; margin-top: 0;}
.one {margin-top: 0.5in; background-color: aqua;}
.two {margin-top: 25px; background-color: aqua;}
.three {margin-top: 5em; background-color: aqua;}
.four {margin-top: 25%; background-color: aqua;}
.five {margin-top: 25px;}
.six {margin-top: -10px; background-color: aqua;}
P, UL {margin-bottom: 0;}
</PRE>
<HR>
<P class="zero">
This element has a class of zero.
</P>
<P class="zero">
This element also has a class of zero.
</P>

<P class="one">
This element should have a top margin of half an inch, which will require extra text in order to make sure that the margin isn't applied to each line.
</P>
<P class="two">
This element should have a top margin of 25 pixels, which will require extra text in order to make sure that the margin isn't applied to each line.
</P>
<P class="three">
This element should have a top margin of 5 em, which will require extra text in order to make sure that the margin isn't applied to each line.
</P>
<P class="four">
This element should have a top margin of 25%, which is calculated with respect to the width of the parent element.  This will require extra text in order to test.
</P>
<UL class="two">
<LI>This list has a margin-top of 25px, and a light blue background.
<LI>Therefore, it ought to have such a margin.
<LI class="five">This list item has a top margin of 25px, which should cause it to be offset in some fashion.
<LI>This list item has no special styles applied to it.
</UL>
<P class="zero">
This element has a class of zero.
</P>
<P class="six">
This element has a top margin of -10px, which should cause it to be shifted "upward" on the page, and no bottom margin.  No other styles have been applied to it besides a light blue background color.  In all other respects, the element should be normal.
</P>

<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P class="zero">
This element has a class of zero.
</P>
<P class="zero">
This element also has a class of zero.
</P>

<P class="one">
This element should have a top margin of half an inch, which will require extra text in order to make sure that the margin isn't applied to each line.
</P>
<P class="two">
This element should have a top margin of 25 pixels, which will require extra text in order to make sure that the margin isn't applied to each line.
</P>
<P class="three">
This element should have a top margin of 5 em, which will require extra text in order to make sure that the margin isn't applied to each line.
</P>
<P class="four">
This element should have a top margin of 25%, which is calculated with respect to the width of the parent element.  This will require extra text in order to test.
</P>
<UL class="two">
<LI>This list has a margin-top of 25px, and a light blue background.
<LI>Therefore, it ought to have such a margin.
<LI class="five">This list item has a top margin of 25px, which should cause it to be offset in some fashion.
<LI>This list item has no special styles applied to it.
</UL>
<P class="zero">
This element has a class of zero.
</P>
<P class="six">
This element has a top margin of -10px, which should cause it to be shifted "upward" on the page, and no bottom margin.  No other styles have been applied to it besides a light blue background color.  In all other respects, the element should be normal.
</P></TD></TR></TABLE></BODY>
</HTML>
