# Copyright 2021 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/android/rules.gni")

android_library("java") {
  sources = [
    "java/src/org/chromium/chrome/browser/multiwindow/CloseConfirmationDialogView.java",
    "java/src/org/chromium/chrome/browser/multiwindow/InstanceInfo.java",
    "java/src/org/chromium/chrome/browser/multiwindow/InstanceSwitcherCoordinator.java",
    "java/src/org/chromium/chrome/browser/multiwindow/InstanceSwitcherItemProperties.java",
    "java/src/org/chromium/chrome/browser/multiwindow/InstanceSwitcherItemViewBinder.java",
    "java/src/org/chromium/chrome/browser/multiwindow/MultiInstanceIphController.java",
    "java/src/org/chromium/chrome/browser/multiwindow/MultiInstanceState.java",
    "java/src/org/chromium/chrome/browser/multiwindow/TargetSelectorCoordinator.java",
    "java/src/org/chromium/chrome/browser/multiwindow/TargetSelectorItemProperties.java",
    "java/src/org/chromium/chrome/browser/multiwindow/TargetSelectorItemViewBinder.java",
    "java/src/org/chromium/chrome/browser/multiwindow/UiUtils.java",
  ]
  deps = [
    ":java_resources",
    "//base:base_java",
    "//chrome/browser/feature_engagement:java",
    "//chrome/browser/preferences:java",
    "//chrome/browser/profiles/android:java",
    "//chrome/browser/ui/android/appmenu:java",
    "//chrome/browser/ui/android/favicon:java",
    "//chrome/browser/user_education:java",
    "//chrome/browser/util:java",
    "//components/browser_ui/modaldialog/android:java",
    "//components/browser_ui/styles/android:java",
    "//components/browser_ui/widget/android:java",
    "//components/favicon/android:java",
    "//components/feature_engagement/public:public_java",
    "//third_party/androidx:androidx_annotation_annotation_java",
    "//third_party/androidx:androidx_appcompat_appcompat_resources_java",
    "//third_party/androidx:androidx_core_core_java",
    "//ui/android:ui_no_recycler_view_java",
    "//ui/android:ui_utils_java",
    "//url:gurl_java",
  ]
  resources_package = "org.chromium.chrome.browser.multiwindow"
}

android_resources("java_resources") {
  sources = [
    "java/res/drawable/checkmark_24dp.xml",
    "java/res/drawable/circle_green.xml",
    "java/res/layout/close_confirmation_dialog.xml",
    "java/res/layout/instance_switcher_cmd_item.xml",
    "java/res/layout/instance_switcher_dialog.xml",
    "java/res/layout/instance_switcher_item.xml",
    "java/res/layout/instance_switcher_list.xml",
    "java/res/layout/target_selector_dialog.xml",
    "java/res/values/colors.xml",
    "java/res/values/dimens.xml",
    "java/res/values/styles.xml",
  ]
  deps = [
    "//chrome/browser/ui/android/favicon:java_resources",
    "//chrome/browser/ui/android/strings:ui_strings_grd",
    "//chrome/browser/ui/android/toolbar:java_resources",
    "//components/browser_ui/modaldialog/android:java_resources",
    "//components/browser_ui/styles/android:java_resources",
    "//components/browser_ui/widget/android:java_resources",
  ]
}

robolectric_library("junit") {
  sources = [
    "java/src/org/chromium/chrome/browser/multiwindow/MultiInstanceStateUnitTest.java",
    "java/src/org/chromium/chrome/browser/multiwindow/UiUtilsUnitTest.java",
  ]

  deps = [
    ":java",
    "//base:base_java",
    "//base:base_java_test_support",
    "//base:base_junit_test_support",
    "//chrome/browser/profiles/android:java",
    "//chrome/browser/util:java",
    "//components/favicon/android:java",
    "//third_party/android_support_test_runner:runner_java",
    "//third_party/androidx:androidx_test_core_java",
    "//third_party/androidx:androidx_test_runner_java",
    "//third_party/hamcrest:hamcrest_library_java",
    "//third_party/junit",
    "//third_party/mockito:mockito_java",
  ]
}

android_library("javatests") {
  testonly = true

  sources = [
    "java/src/org/chromium/chrome/browser/multiwindow/InstanceSwitcherCoordinatorTest.java",
    "java/src/org/chromium/chrome/browser/multiwindow/TargetSelectorCoordinatorTest.java",
  ]

  deps = [
    ":java",
    "//base:base_java_test_support",
    "//chrome/browser/flags:java",
    "//chrome/browser/preferences:java",
    "//chrome/test/android:chrome_java_unit_test_support",
    "//components/browser_ui/modaldialog/android:java",
    "//components/browser_ui/settings/android:java",
    "//components/browser_ui/widget/android:java",
    "//components/favicon/android:java",
    "//content/public/test/android:content_java_test_support",
    "//third_party/android_deps:espresso_java",
    "//third_party/androidx:androidx_test_runner_java",
    "//third_party/hamcrest:hamcrest_core_java",
    "//third_party/hamcrest:hamcrest_library_java",
    "//third_party/junit",
    "//third_party/mockito:mockito_java",
    "//ui/android:ui_full_java",
    "//ui/android:ui_java_test_support",
  ]
}
