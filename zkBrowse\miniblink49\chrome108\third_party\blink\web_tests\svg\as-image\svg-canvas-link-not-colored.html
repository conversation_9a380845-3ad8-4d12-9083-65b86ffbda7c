<!DOCTYPE html>
<title>':visited' never applies for a link in an SVG image</title>
<script src="../../resources/testharness.js"></script>
<script src="../../resources/testharnessreport.js"></script>
<script>
  async_test(t => {
    let image = new Image();
    image.src = "resources/link.svg";
    image.onload = t.step_func_done(() => {
      let ctx = document.createElement("canvas").getContext("2d");

      let colorChannels = ctx.getImageData(0, 0, 1, 1).data;
      assert_equals(colorChannels[0], 0, 'r before');
      assert_equals(colorChannels[1], 0, 'g before');
      assert_equals(colorChannels[2], 0, 'b before');

      ctx.drawImage(image, 0, 0);

      colorChannels = ctx.getImageData(0, 0, 1, 1).data;
      assert_equals(colorChannels[0], 0, 'r after');
      assert_equals(colorChannels[1], 128, 'g after');
      assert_equals(colorChannels[2], 0, 'b after');
    });
  });
</script>
