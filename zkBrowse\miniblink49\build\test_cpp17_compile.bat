@echo off
echo Testing C++17 Compilation
echo ========================

call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Enterprise\VC\Auxiliary\Build\vcvars32.bat"

echo.
echo Compiling C++17 test with /std:c++17 flag...
cl /std:c++17 test_cpp17.cpp /Fe:test_cpp17.exe

if exist test_cpp17.exe (
    echo.
    echo Compilation successful! Running C++17 test...
    echo.
    test_cpp17.exe
    echo.
    echo C++17 support is working correctly!
) else (
    echo.
    echo ERROR: C++17 compilation failed!
)

pause
