<!DOCTYPE html>
<script src="../../resources/testharness.js"></script>
<script src="../../resources/testharnessreport.js"></script>
<style>
#test { d: path('M 0 0 H 30 V 40'); }
</style>
<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200">
    <path id="test" d="M 10 190 H 190" />

    <path id="test2" d="M 10 190 H 190" />
    <path id="test3" d="" />
    <path id="test4" />
</svg>
<script>
'use strict';
test(function() {
    var test = document.getElementById("test");
    assert_equals(test.getTotalLength(), 70);
}, "Test for getTotalLength using computed style");
test(function() {
    var test = document.getElementById("test");
    assert_equals(test.getPointAtLength(70).x, 30);
    assert_equals(test.getPointAtLength(70).y, 40);
}, "Test for getPointAtLength using computed style");
test(function() {
    var test = document.getElementById("test");
    assert_equals(getComputedStyle(test).d, "path(\"M 0 0 H 30 V 40\")");
}, "Test for CSS d using computed style");
test(function() {
    var test2 = document.getElementById("test2");
    assert_equals(getComputedStyle(test2).d, "path(\"M 10 190 H 190\")");
}, "Test for DOM d using computed style");
test(function() {
    var test3 = document.getElementById("test3");
    assert_equals(getComputedStyle(test3).d, "none");
}, "Test for empty d using computed style");
test(function() {
    var test4 = document.getElementById("test4");
    assert_equals(getComputedStyle(test4).d, "none");
}, "Test for absent d using computed style");
</script>
