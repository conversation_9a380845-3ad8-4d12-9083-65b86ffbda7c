CONSOLE WARNING: The keyword 'inner-spin-button' specified to an 'appearance' property is not standardized. It will be removed in the future.
CONSOLE WARNING: The keyword 'media-slider' specified to an 'appearance' property is not standardized. It will be removed in the future.
CONSOLE WARNING: The keyword 'media-sliderthumb' specified to an 'appearance' property is not standardized. It will be removed in the future.
CONSOLE WARNING: The keyword 'media-volume-slider' specified to an 'appearance' property is not standardized. It will be removed in the future.
CONSOLE WARNING: The keyword 'media-volume-sliderthumb' specified to an 'appearance' property is not standardized. It will be removed in the future.
CONSOLE WARNING: The keyword 'slider-vertical' specified to an 'appearance' property is not standardized. It will be removed in the future.
CONSOLE WARNING: The keyword 'sliderthumb-horizontal' specified to an 'appearance' property is not standardized. It will be removed in the future.
CONSOLE WARNING: The keyword 'sliderthumb-vertical' specified to an 'appearance' property is not standardized. It will be removed in the future.
CONSOLE WARNING: The keyword 'searchfield-cancel-button' specified to an 'appearance' property is not standardized. It will be removed in the future.
This is a testharness.js-based test.
FAIL There should be consle warnings about non-standard appearance keywords assert_true: This should FA<PERSON> to have -expected.txt expected true got false
Ha<PERSON>ss: the test ran to completion.

