<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background-position applied to elements with 'display' set to 'list-item'</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="<PERSON><PERSON>" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-11-29 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background-position" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <meta name="flags" content="image" />
        <meta name="assert" content="The 'background-position' property applies to elements with 'display' set to 'list-item'." />
        <style type="text/css">
            div
            {
                background-image: url('support/blue15x15.png');
                background-position: bottom right;
                background-repeat: no-repeat;
                border: solid black;
                display: list-item;
                height: 1in;
                margin-left: 2em;
                width: 1in;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is 1 and only 1 small filled blue square, if it is located in the lower-right corner of a hollow black square and if there is a marker bullet on the left-hand side of the black square.</p>
        <div></div>
    </body>
</html>