<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.6.2 white-space</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
body {overflow: hidden;}
.one {white-space: pre;}
.two {white-space: nowrap;}
.three {white-space: normal;}
</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.one {white-space: pre;}
.two {white-space: nowrap;}
.three {white-space: normal;}

</PRE>
<HR>
<P class="one">
This sentence should     show extra space  where there    would ordinarily         not be any.
     There should also be preservation of returns
as this sentence
     very clearly demonstrates.
</P>

<P class="two">
This sentence should not word-wrap, no matter how long the sentence is, as it has been set to nowrap and that should have the obvious effect.
</P>

<P class="one">
This sentence      should     show extra   space, <SPAN class="three">except in the       second half</SPAN>.
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P class="one">
This sentence should     show extra space  where there    would ordinarily         not be any.
     There should also be preservation of returns
as this sentence
     very clearly demonstrates.
</P>

<P class="two">
This sentence should not word-wrap, no matter how long the sentence is, as it has been set to nowrap and that should have the obvious effect.
</P>

<P class="one">
This sentence      should     show extra   space, <SPAN class="three">except in the       second half</SPAN>.
</P>
</TD></TR></TABLE></BODY>
</HTML>
