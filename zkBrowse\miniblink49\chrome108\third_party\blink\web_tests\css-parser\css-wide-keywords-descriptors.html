<!DOCTYPE html>
<script src="../resources/testharness.js"></script>
<script src="../resources/testharnessreport.js"></script>
<script src="resources/property-parsing-test.js"></script>
<script>

function testDescriptors(keyword) {
  assert_invalid_value("src", keyword);
  assert_invalid_value("unicode-range", keyword);
  assert_invalid_value("font-display", keyword);
}

testDescriptors("initial");
testDescriptors("inherit");
testDescriptors("unset");
testDescriptors("INITIAL");
testDescriptors("INHERIT");
testDescriptors("UNSET");
testDescriptors("iNiTiAl");
testDescriptors("iNhErIt");
testDescriptors("uNsEt");
</script>
