# Copyright 2015 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/android/rules.gni")

_jni_sources =
    [ "java/src/org/chromium/services/data_decoder/JsonSanitizer.java" ]

generate_jni("safe_json_jni_headers") {
  sources = _jni_sources
}

if (current_toolchain == default_toolchain) {
  android_library("safe_json_java") {
    deps = [
      "//base:base_java",
      "//base:jni_java",
      "//build/android:build_java",
    ]
    annotation_processor_deps = [ "//base/android/jni_generator:jni_processor" ]
    sources = [] + _jni_sources
  }
}
