<!DOCTYPE html>
<script src="../resources/testharness.js"></script>
<script src="../resources/testharnessreport.js"></script>
<script>
test(function() {
    assert_true(CSS.supports("-webkit-line-break", "auto"));
    assert_true(CSS.supports("-webkit-line-break", "loose"));
    assert_true(CSS.supports("-webkit-line-break", "normal"));
    assert_true(CSS.supports("-webkit-line-break", "strict"));
}, '-webkit-line-break accepts valid keyword properties.');

test(function() {
    assert_true(CSS.supports("-webkit-line-break", "after-white-space"));
}, '-webkit-line-break accepts the value after-white-space.');

test(function() {
    assert_false(CSS.supports("-webkit-line-break", "bogus"));
}, '-webkit-line-break does not except obviously invalid values.');

test(function() {
    assert_true(CSS.supports("line-break", "auto"));
    assert_true(CSS.supports("line-break", "loose"));
    assert_true(CSS.supports("line-break", "normal"));
    assert_true(CSS.supports("line-break", "strict"));
}, 'line-break accepts valid keyword properties.');

test(function() {
    assert_false(CSS.supports("line-break", "after-white-space"));
}, 'line-break does not accept the value after-white-space.');

test(function() {
    assert_false(CSS.supports("line-break", "bogus"));
}, 'line-break does not except obviously invalid values.');
</script>
