  1) LOAD 4  // Architecture
  2) if A == 0x40000003; then JMP 3 else JMP 10
  3) LOAD 0  // System call number
  4) if A & 0x40000000; then JMP 10 else JMP 5
  5) if A >= 0x38; then JMP 6 else JMP 7
  6) if A >= 0x401; then JMP 15 else JMP 14
  7) if A >= 0x37; then JMP 8 else JMP 14
  8) LOAD 20  // Argument 0 (MSB)
  9) if A == 0x0; then JMP 11 else JMP 10
 10) RET 0x0  // Kill
 11) LOAD 16  // Argument 0 (LSB)
 12) if A == 0xfffffec6; then JMP 13 else JMP 14
 13) RET 0x50001  // errno = 1
 14) RET 0x7fff0000  // Allowed
 15) RET 0x50026  // errno = 38
