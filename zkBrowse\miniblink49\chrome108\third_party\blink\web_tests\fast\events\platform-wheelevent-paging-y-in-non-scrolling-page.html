<!DOCTYPE html>
<script src='../../resources/testharness.js'></script>
<script src='../../resources/testharnessreport.js'></script>
<script src='../../resources/gesture-util.js'></script>
<div style="height:200px;width:2400px">
  <div style="background-color:red;height:200px;width:1200px;position:relative;left:0px;top:0px"></div>
  <div style="background-color:green;height:200px;width:1200px;position:relative;left:1200px;top:-200px"></div>
</div>
<div style="height:200px;width:2400px">
  <div style="background-color:blue;height:200px;width:1200px;position:relative;left:0px;top:0px"></div>
  <div style="background-color:yellow;height:200px;width:1200px;position:relative;left:1200px;top:-200px"></div>
</div>

<script>
    const scrollDelta = 2;
    var last_event = null;
    var source = GestureSourceType.MOUSE_INPUT;
    const numTicksY = scrollDelta / pixelsPerTick();
    const expectedWheelDeltaY = numTicksY * LEGACY_MOUSE_WHEEL_TICK_MULTIPLIER;

    function mousewheelHandler(e)
    {
        last_event = e;
    }

    promise_test(async () => {
      document.body.addEventListener("mousewheel", mousewheelHandler, false);

      await smoothScroll(scrollDelta, 100, 110, source, 'down', SPEED_INSTANT, false /* precise_scrolling_deltas */, true /* scroll_by_page */);
      await conditionHolds( () => {
          return document.scrollingElement.scrollTop == 0;
      });
      assert_equals(last_event.wheelDeltaY, -Math.floor(expectedWheelDeltaY));
      assert_equals(last_event.wheelDeltaX, 0);
      assert_equals(last_event.wheelDelta, -Math.floor(expectedWheelDeltaY));
    }, "Page-based wheel scrolls provide correct delta in event handler, don't scroll if there's no " +
       "overflow in direction when scrolling on document.");
</script>
