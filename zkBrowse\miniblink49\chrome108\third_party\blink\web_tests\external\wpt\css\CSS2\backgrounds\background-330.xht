<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background with inherit inheriting five values</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <meta name="flags" content="image interact" />
        <meta name="assert" content="Ensure that inherit works when inheriting five values." />
        <style type="text/css">
            #wrapper
            {
                background: black url('support/green15x15.png') repeat-x 0.5in 0.5in scroll;
                height: 1in;
                overflow: scroll;
            }
            #test
            {
                background: red;
                background: inherit;
                height: 2in;
            }
        </style>
    </head>
    <body>
        <p>Test passes if there is no red visible on the page even when scrolling the box below.</p>
        <div id="wrapper">
            <div id="test"></div>
        </div>
    </body>
</html>