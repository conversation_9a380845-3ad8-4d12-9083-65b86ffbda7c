<!DOCTYPE html>
<html>
<script src="../../resources/testdriver.js"></script>
<script src="../../resources/testdriver-vendor.js"></script>
    <body>
        <p>
            Test that a forwarded user gesture that is consumed before the
            time fires is resurrected.
        </p>
        <button id="button" onclick="openPopup()">Click Here</button>
        <div id="console"></div>
        <script>
            function timer() {
                window.open("about:blank", "window2");
                if (window.testRunner) {
                    if (testRunner.windowCount() == windowCount + 1)
                        document.getElementById("console").innerText = "PASSED";
                    else
                        document.getElementById("console").innerText = "FAILED";
                    testRunner.notifyDone();
                }
            }

            function openPopup() {
                setTimeout(timer, 0);
                window.open("about:blank", "window1");
            }

            if (window.testRunner) {
                testRunner.dumpAsText();
                testRunner.waitUntilDone();
                windowCount = testRunner.windowCount();

                var button = document.getElementById("button");
                test_driver.click(button);
            }
        </script>
    </body>
</html>
