<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 2.3 first-line</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
P:first-line {color: green;}
.two:first-line {font-size: 200%;}
.three:first-line {font-variant: small-caps;}
</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>P:first-line {color: green;}
.two:first-line {font-size: 200%;}
.three:first-line {font-variant: small-caps;}

</PRE>
<HR>
<P>
The first line of this paragraph, and only that one, should be green.  If this precise combination does not occur, then the user agent has failed this test.  Remember that in order to ensure a complete test, the paragraph must be displayed on more than one line.
</P>
<P class="two">
The first line of this paragraph, and only that one, should be a larger font size as well as green.  If this precise combination does not occur, then the user agent has failed this test.  Remember that in order to ensure a complete test, the paragraph must be displayed on more than one line.
</P>
<P class="three">
The first line of this paragraph, and only that one, should be displayed in small-caps style.  Thus, if the first line is not in small-caps style, OR if the entire paragraph turns out in small-caps, then the user agent has failed this test (although the problem might be that <CODE>small-caps</CODE> is not supported by your browser).  This is extra text included for the purposes of making the paragraph long enough to have more than one line.
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P>
The first line of this paragraph, and only that one, should be green.  If this precise combination does not occur, then the user agent has failed this test.  Remember that in order to ensure a complete test, the paragraph must be displayed on more than one line.
</P>
<P class="two">
The first line of this paragraph, and only that one, should be a larger font size as well as green.  If this precise combination does not occur, then the user agent has failed this test.  Remember that in order to ensure a complete test, the paragraph must be displayed on more than one line.
</P>
<P class="three">
The first line of this paragraph, and only that one, should be displayed in small-caps style.  Thus, if the first line is not in small-caps style, OR if the entire paragraph turns out in small-caps, then the user agent has failed this test (although the problem might be that <CODE>small-caps</CODE> is not supported by your browser).  This is extra text included for the purposes of making the paragraph long enough to have more than one line.
</P>
</TD></TR></TABLE></BODY>
</HTML>
