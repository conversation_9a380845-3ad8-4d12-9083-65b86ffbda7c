<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>

 <head>
        
  <title>CSS Test: height and width of element set to 'display: table'</title>

  <!--
  Inspired by
  http://test.csswg.org/suites/css2.1/20101210/html4/empty-cells-applies-to-007.htm
  -->

  <link rel="author" title="<PERSON><PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/">
  <link rel="help" href="http://www.w3.org/TR/CSS21/tables.html#height-layout" title="17.5.3 Table height algorithms">
  <link rel="bookmark" href="http://lists.w3.org/Archives/Public/www-style/2011Jan/0178.html" title="[CSS21] Question on section 17.6.1 The separated borders model and width of inline-table">
  <meta name="flags" content="">
  <meta name="assert" content="The height and width of an element styled with display set to 'table' is given by its height and width properties when specified.">

  <style type="text/css">
  div#overlapped-red-reference
  {
  background-color: red;
  height: 100px;
  position: absolute;
  width: 100px;
  z-index: -1;
  }

  div#overlapping-green-test
  {
  background-color: green;
  border: green solid 25px;
  border-collapse: separate;
  display: table;
  height: 50px;
  width: 50px;        
  }
  </style>

 </head>

 <body>

  <p>Test passes if there is a filled green square and <strong>no red</strong>.</p>

  <div id="overlapped-red-reference"></div>

  <div id="overlapping-green-test"></div>

 </body>
</html>