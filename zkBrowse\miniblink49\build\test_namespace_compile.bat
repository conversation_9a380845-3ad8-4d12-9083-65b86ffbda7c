@echo off
echo Testing V8 Compatibility Layer Namespace Fix
echo =============================================

call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Enterprise\VC\Auxiliary\Build\vcvars32.bat"

echo.
echo Compiling namespace fix test...
cl /std:c++17 /I"../v8_10_8/include" test_namespace_fix.cpp ../v8_10_8/v8_compatibility.cpp /Fe:test_namespace_fix.exe

if exist test_namespace_fix.exe (
    echo.
    echo Compilation successful! Running namespace fix test...
    echo.
    test_namespace_fix.exe
    echo.
    echo Namespace fix is working correctly!
) else (
    echo.
    echo ERROR: Namespace fix test compilation failed!
)

pause
