<script src="../../resources/gesture-util.js"></script>
<body style="margin:0">
<script>
if (window.testRunner) {
  testRunner.dumpAsText();
  testRunner.waitUntilDone();
}
</script>

<div id="overflow" style="border:2px solid black;overflow:auto;height:400px;width:400px; position:absolute;">
<div style="background-color:red;height:720px"></div>
<div style="background-color:green;height:1600px"></div>
</div>

<div id="console"></div>

<script>

const scroller = document.getElementById('overflow');
function scrollTop() {
  return scroller.scrollTop;
}

if (window.eventSender) {
  waitForCompositorCommit().then(() => {
    const scrollPromise = waitForScrollEvent(scroller);

    eventSender.mouseMoveTo(390, 300);
    eventSender.mouseDown();
    eventSender.mouseUp();
    eventSender.mouseDown();
    eventSender.mouseUp();

    scrollPromise.then(() => {
      waitForAnimationEndTimeBased(scrollTop).then(finished);
    });
  });
}

function finished() {
  document.getElementById('console').innerHTML = "Scroll offset is " + scroller.scrollTop;
  testRunner.notifyDone();
}

</script>
