<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Border-width shorthand property set using three values</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="G<PERSON> Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-06-25 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#propdef-border-width" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/box.html#border-width-properties" />
		<link rel="match" href="border-width-003-ref.xht" />

        <meta name="assert" content="The 'border-width' shorthand property set using three values correctly sets the border width for the appropriate sides of an element." />
        <style type="text/css">
            #test
            {
                border-style: solid;
                border-width: 0.5in 0.25in 0.75in;
                width: 0;
            }
            #reference
            {
                background-color: black;
                height: 1.25in;
                margin-top: 5px;
                width: 0.5in;
            }
        </style>
    </head>
    <body>
        <p>Test passes if the 2 filled black rectangles have the <strong>same size</strong>.</p>
        <div id="test"></div>
        <div id="reference"></div>
    </body>
</html>