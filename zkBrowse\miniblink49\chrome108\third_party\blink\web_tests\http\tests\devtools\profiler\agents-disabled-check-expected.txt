Test that if a profiler is working all the agents are disabled.

--> SDK.targetManager.suspendAllTargets();
frontend: {"id":<number>,"method":"DOM.disable","params":{}}
frontend: {"id":<number>,"method":"CSS.disable","params":{}}
frontend: {"id":<number>,"method":"Debugger.setAsyncCallStackDepth","params":{"maxDepth":0}}
frontend: {"id":<number>,"method":"Overlay.disable","params":{}}
frontend: {"id":<number>,"method":"Target.setAutoAttach","params":{"autoAttach":true,"waitForDebuggerOnStart":false,"flatten":true}}
frontend: {"id":<number>,"method":"Overlay.setShowGridOverlays","params":{"gridNodeHighlightConfigs":[]}}
frontend: {"id":<number>,"method":"Overlay.setShowFlexOverlays","params":{"flexNodeHighlightConfigs":[]}}
frontend: {"id":<number>,"method":"Overlay.setShowScrollSnapOverlays","params":{"scrollSnapHighlightConfigs":[]}}
frontend: {"id":<number>,"method":"Overlay.setShowContainerQueryOverlays","params":{"containerQueryHighlightConfigs":[]}}
frontend: {"id":<number>,"method":"Overlay.setShowIsolatedElements","params":{"isolatedElementHighlightConfigs":[]}}
frontend: {"id":<number>,"method":"Debugger.disable","params":{}}

--> SDK.targetManager.resumeAllTargets();
frontend: {"id":<number>,"method":"DOM.enable","params":{}}
frontend: {"id":<number>,"method":"CSS.enable","params":{}}
frontend: {"id":<number>,"method":"Debugger.enable","params":{"maxScriptsCacheSize":100000000}}
frontend: {"id":<number>,"method":"Debugger.setPauseOnExceptions","params":{"state":"none"}}
frontend: {"id":<number>,"method":"Debugger.setAsyncCallStackDepth","params":{"maxDepth":32}}
frontend: {"id":<number>,"method":"Overlay.enable","params":{}}
frontend: {"id":<number>,"method":"Overlay.setShowViewportSizeOnResize","params":{"show":true}}
frontend: {"id":<number>,"method":"Target.setAutoAttach","params":{"autoAttach":true,"waitForDebuggerOnStart":true,"flatten":true}}

--> done

