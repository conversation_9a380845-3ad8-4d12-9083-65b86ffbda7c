<!DOCTYPE html>
<script src="../../resources/js-test.js"></script>
<style>
    #image {
        background-image: url('resources/animated-rect-color.svg');
        width: 100px;
        height: 100px;
    }
</style>
<div id="image"></div>
<script>
var SVGSMILAnimationInImageRegardlessOfCache = 768; // From UseCounter.h
window.jsTestIsAsync = true;

shouldBeFalse("internals.isUseCounted(document, SVGSMILAnimationInImageRegardlessOfCache)");

window.onload = function() {
    shouldBeTrue("internals.isUseCounted(document, SVGSMILAnimationInImageRegardlessOfCache)");
    finishJSTest();
}
</script>
</html>