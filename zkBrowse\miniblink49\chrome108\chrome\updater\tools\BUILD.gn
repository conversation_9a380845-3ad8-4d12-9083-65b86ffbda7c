# Copyright 2019 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//testing/test.gni")

group("tools") {
  deps = [ ":certificate_tag" ]
}

source_set("certificate_tag_lib") {
  sources = [
    "certificate_tag.cc",
    "certificate_tag.h",
  ]

  deps = [ "//base" ]
}

executable("certificate_tag") {
  sources = [ "main.cc" ]

  deps = [
    ":certificate_tag_lib",
    "//base",
  ]
}

source_set("unittest") {
  testonly = true

  sources = [ "certificate_tag_unittest.cc" ]

  deps = [
    ":certificate_tag_lib",
    "//base",
    "//base/test:test_support",
    "//testing/gtest",
    "//third_party/zlib/google:compression_utils",
  ]

  data = [ "../test/data/signed.exe.gz" ]
}
