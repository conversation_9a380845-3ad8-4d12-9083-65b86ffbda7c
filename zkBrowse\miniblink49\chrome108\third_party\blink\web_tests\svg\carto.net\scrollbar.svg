<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd" [
<!ATTLIST svg
	xmlns:attrib CDATA #IMPLIED
	xmlns:batik CDATA #IMPLIED
	>
	<!ATTLIST g
	batik:static CDATA #IMPLIED
	>
]>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:batik="http://xml.apache.org/batik/ext" width="100%" height="100%" viewBox="0 0 1000 700" onload="init()">
    <title>Example of SVG Scrollbars</title>
	<script type="text/ecmascript" xlink:href="resources/mapApp.js"></script>
	<script type="text/ecmascript" xlink:href="resources/helper_functions.js"></script>
	<script type="text/ecmascript" xlink:href="resources/timer.js"></script>
	<script type="text/ecmascript" xlink:href="resources/button.js"></script>
	<script type="text/ecmascript" xlink:href="resources/scrollbar.js"></script>
	<script type="text/ecmascript">
		<![CDATA[
			var mapApp;
			var scrolledObject1;
			var scrolledObject2;
			function init() {
				myMapApp = new mapApp(false,undefined);
				//scrollbar styles
				var scrollbarStyles = {"fill":"whitesmoke","stroke":"dimgray","stroke-width":1};
				var scrollerStyles = {"fill":"lightgray","stroke":"dimgray","stroke-width":1};
				var triangleStyles = {"fill":"dimgray"};
				var highlightStyles = {"fill":"dimgray","stroke":"dimgray","stroke-width":1};
				//button styles
				var buttonTextStyles = {"font-family":"Arial,Helvetica","fill":"dimgray","font-size":12};
				var buttonStyles = {"fill":"gainsboro"};
				var shadeLightStyles = {"fill":"white"};
				var shadeDarkStyles = {"fill":"dimgray"};
				//create scrolledObjects to react on scrollbar
				scrolledObject1 = new scrolledObject("content1",0,-2707,0,-220.5,"sb1horiz","sb1vert");
				scrolledObject2 = new scrolledObject("content2",0,-2660.75,0,-489,"sb2horiz","sb2vert");
				//id,parentNode,x,y,width,height,startValue,endValue,initialWidthPerc,initialOffset,scrollButtonLocations,scrollbarStyles,scrollerStyles,triangleStyles,highlightStyles,functionToCall
				myMapApp.scrollbars["sb1horiz"] = new scrollbar("sb1horiz","scrollbars1",50.5,250.5,899,15,scrolledObject1.maxX,scrolledObject1.minX,0.2495,0,0.005,"top_bottom",scrollbarStyles,scrollerStyles,triangleStyles,highlightStyles,scrolledObject1);
				myMapApp.scrollbars["sb1vert"] = new scrollbar("sb1vert","scrollbars1",950.5,50.5,15,199,scrolledObject1.maxY,scrolledObject1.minY,0.4756,0,0.025,"top_bottom",scrollbarStyles,scrollerStyles,triangleStyles,highlightStyles,scrolledObject1);
				myMapApp.scrollbars["sb2horiz"] = new scrollbar("sb2horiz","scrollbars2",50.5,550.5,899,10,scrolledObject2.maxX,scrolledObject2.minX,0.2528,0,0.005,"bottom_bottom",scrollbarStyles,scrollerStyles,triangleStyles,highlightStyles,scrolledObject2);
				myMapApp.scrollbars["sb2vert"] = new scrollbar("sb2vert","scrollbars2",950.5,350.5,10,199,scrolledObject2.maxY,scrolledObject2.minY,0.2903,0,0.02,"bottom_bottom",scrollbarStyles,scrollerStyles,triangleStyles,highlightStyles,scrolledObject2);
				//create buttons
				myMapApp.buttons["showHideButton2"] = new button("showHideButton2","buttons",scrolledObject2,"rect","Hide Scrollbar",undefined,50,580,110,20,buttonTextStyles,buttonStyles,shadeLightStyles,shadeDarkStyles,1);		
				myMapApp.buttons["removeButton2"] = new button("removeButton2","buttons",scrolledObject2,"rect","Remove Scrollbar",undefined,170,580,110,20,buttonTextStyles,buttonStyles,shadeLightStyles,shadeDarkStyles,1);		
		}
			//this object controls the panning and scrolling of the svg elements (panoramas)
			function scrolledObject(contentId,maxX,minX,maxY,minY,sbXId,sbYId) {
				this.content = document.getElementById(contentId);
				this.transX = 0;
				this.transY = 0;
				this.maxX = maxX;
				this.minX = minX;
				this.maxY = maxY;
				this.minY = minY;
				this.sbXId = sbXId;
				this.sbYId = sbYId;
				this.panActive = false;
				this.parent = this.content.parentNode;
			}
			scrolledObject.prototype.scrollbarChanged = function(id,changeType,valueAbs,valuePerc) {
				if (changeType == "scrollChange" || "scrolledStep") {
					if (id.match(/horiz/gi)) {
						this.transX = valueAbs;
					}
					if (id.match(/vert/gi)) {
						this.transY = valueAbs;
					}
					this.content.setAttributeNS(null,"transform","translate("+this.transX+","+this.transY+")");
				}
			}
			scrolledObject.prototype.handleEvent = function(evt) {
				if (evt.type == "mousedown") {
					this.coords = myMapApp.calcCoord(evt,this.parent);
					this.panActive = true;
				}
				if (evt.type == "mousemove" && this.panActive) {
					var coords = myMapApp.calcCoord(evt,this.parent);
					this.transX += coords.x - this.coords.x;
					this.transY += coords.y - this.coords.y;
					if (this.transX < this.minX) {
						this.transX = this.minX;
					}
					if (this.transX > this.maxX) {
						this.transX = this.maxX;
					}
					if (this.transY < this.minY) {
						this.transY = this.minY;
					}
					if (this.transY > this.maxY) {
						this.transY = this.maxY;
					}
					this.content.setAttributeNS(null,"transform","translate("+this.transX+","+this.transY+")");
					//set scrollbars
					if (myMapApp.scrollbars[this.sbXId]) {
						myMapApp.scrollbars[this.sbXId].scrollToValue(this.transX);
					}
					if (myMapApp.scrollbars[this.sbYId]) {
						myMapApp.scrollbars[this.sbYId].scrollToValue(this.transY);
					}
					this.coords = coords;
				}
				if (evt.type == "mouseup" || evt.type == "mouseout") {
					this.panActive = false;
				}
			}
			scrolledObject.prototype.buttonPressed = function(id,evt,buttonText) {
				if (id.match(/removeButton/)) {
						myMapApp.scrollbars[this.sbXId].remove();
						myMapApp.scrollbars[this.sbXId] = undefined;
						myMapApp.scrollbars[this.sbYId].remove();
						myMapApp.scrollbars[this.sbYId] = undefined;
						myMapApp.buttons[id].deactivate();
						//get id number
						var sbId = id.substr(id.length-1);
						myMapApp.buttons["showHideButton"+sbId].deactivate();
				}
				else {
					if (buttonText == "Hide Scrollbar") {
						myMapApp.buttons[id].setTextValue("Show Scrollbar");
						myMapApp.scrollbars[this.sbXId].hide();
						myMapApp.scrollbars[this.sbYId].hide();
					}
					else {
						myMapApp.buttons[id].setTextValue("Hide Scrollbar");
						myMapApp.scrollbars[this.sbXId].show();
						myMapApp.scrollbars[this.sbYId].show();
					}
				}
			}
    	]]>
	</script>
	<rect id="bgRect" x="-1000" y="-1000" width="5000" height="5000" pointer-events="fill" fill="none" stroke="none" />
	<text x="50" y="30" font-weight="bold" font-size="20" font-family="Arial,Helvetica">Demonstration of the SVG Scrollbar</text>
	<text font-size="15" font-family="Arial,Helvetica" text-anchor="end" x="950" y="30">One can also drag the images and the scrollbars will adopt.</text>
	<svg id="svg1" x="50" y="50" width="900" height="200" viewBox="0 0 900 200">
		<g id="content1" cursor="move" batik:static="true" onmousedown="scrolledObject1.handleEvent(evt)" onmousemove="scrolledObject1.handleEvent(evt)" onmouseup="scrolledObject1.handleEvent(evt)" onmouseout="scrolledObject1.handleEvent(evt)">
			<image id="image1" x="0" y="0" width="3607" height="420.5" xlink:href="images/zervreilasee_vals_panorama_low_res.jpg" />
			<g id="labeling" pointer-events="none" font-size="15" font-family="Arial,Helvetica" fill="white">
				<text text-anchor="middle" x="456" y="88.8">Furggeltihorn</text>
				<text text-anchor="middle" x="593.8" y="98.8">P 2821</text>
				<text text-anchor="middle" x="635" y="79.8">Zervreilahorn</text>
				<text text-anchor="middle" x="805" y="112.8">Plattenberg</text>
				<text text-anchor="middle" x="2220.9" y="178.8">Piz Fess</text>
				<text text-anchor="middle" x="2291.4" y="162.6">Crap Grisch</text>
				<text text-anchor="middle" x="2412.7" y="149.6">Piz Tomül</text>
				<text text-anchor="middle" x="1762.6" y="112.6" fill="black">Fruntalp</text>
				<text text-anchor="middle" x="1003.8" y="386.5" fill="black">Zervreilasee</text>
			</g>
		</g>
	</svg>
	<g id="scrollbars1" />
	<text font-size="12" font-family="Arial,Helvetica" text-anchor="end" x="950" y="280">Panorama Zervreilastausee, Vals, Graubünden, Switzerland, taken 2006-02-04 (&#169; A. Neumann)</text>
	<svg id="svg2" x="50" y="350" width="900" height="200" viewBox="0 0 900 200">
		<g id="content2" cursor="move" batik:static="true" onmousedown="scrolledObject2.handleEvent(evt)" onmousemove="scrolledObject2.handleEvent(evt)" onmouseup="scrolledObject2.handleEvent(evt)" onmouseout="scrolledObject2.handleEvent(evt)">
			<image id="image2" x="0" y="0" width="3560.75" height="689" xlink:href="images/chalchtrittli_tierfed_panorama_low_res.jpg" />
		</g>
	</svg>
	<g id="scrollbars2" />
	<text font-size="12" font-family="Arial,Helvetica" text-anchor="end" x="950" y="575">Panorama Chalchtrittli, Tierfed, Linthal, Glarus, Switzerland, taken 2005-09-03 (&#169; A. Neumann)</text>
	<g id="buttons" />
</svg>
