<html><body onload="scroll()">
<script>
function scroll() {
    if (window.eventSender) {
        var scrollbar = document.getElementById("subframe").contentDocument.getElementById('scrollable');

        var startX = scrollbar.offsetLeft + scrollbar.offsetWidth - 5;
        var startY = scrollbar.offsetTop + 200;

        eventSender.mouseMoveTo(startX, startY - 100);
        eventSender.mouseDown();
        eventSender.mouseMoveTo(startX, startY);
        eventSender.mouseUp();

        eventSender.mouseDown();
        eventSender.mouseMoveTo(startX, startY + 200);
        eventSender.mouseMoveTo(startX + 200, startY + 200);
        eventSender.mouseUp();
        var scrollAfterMoveToMainFrame = scrollbar.scrollTop;

        eventSender.mouseMoveTo(startX - 100, startY - 100);
        var scrollAfterReturnToSubframe = scrollbar.scrollTop;
        var result = scrollAfterMoveToMainFrame == scrollAfterReturnToSubframe ? "PASS"
            : "FAIL: scrollAfterMoveToMainFrame = " + scrollAfterMoveToMainFrame + ", scrollAfterReturnToSubframe = " + scrollAfterReturnToSubframe;
        document.getElementById("console").appendChild(document.createTextNode(result));
        testRunner.notifyDone();
    }
}

if (window.testRunner) {
    testRunner.dumpAsText();
    testRunner.waitUntilDone();
}
</script>
<div id="console"></div>
<iframe id="subframe" width="500px" height="350" scrolling="no" marginwidth="0" marginheight="0" src="resources/subframe-with-scrollable-div.html"></iframe>
<br>This test does the following via EventSender:<br>
1. Click and drag the div scrollbar to a middle point.<br>
2. Click and drag again, this time down and to right, with the mouseup occurring in a parent frame.<br>
3. Move the mouse back into the div with the scrollbar.<br>
Per https://bugs.webkit.org/show_bug.cgi?id=73097, because the div with the scrollbar had a mousedown event that called preventDefault(),
the mouse moves would not properly be handled by the scrollbar. We pass if the div's scrollTop property is the same after all 3 steps.
</body></html>
