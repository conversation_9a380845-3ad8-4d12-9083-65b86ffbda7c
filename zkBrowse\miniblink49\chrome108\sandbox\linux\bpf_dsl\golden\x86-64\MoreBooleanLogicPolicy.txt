  1) LOAD 4  // Architecture
  2) if A == 0xc000003e; then JMP 3 else JMP 48
  3) LOAD 0  // System call number
  4) if A & 0x40000000; then JMP 48 else JMP 5
  5) if A >= 0x76; then JMP 6 else JMP 7
  6) if A >= 0x401; then JMP 55 else JMP 54
  7) if A >= 0x75; then JMP 8 else JMP 54
  8) LOAD 20  // Argument 0 (MSB)
  9) if A == 0x0; then JMP 13 else JMP 10
 10) if A == 0xffffffff; then JMP 11 else JMP 48
 11) LOAD 16  // Argument 0 (LSB)
 12) if A & 0x80000000; then JMP 13 else JMP 48
 13) LOAD 16  // Argument 0 (LSB)
 14) if A == 0x0; then JMP 53 else JMP 15
 15) LOAD 28  // Argument 1 (MSB)
 16) if A == 0x0; then JMP 20 else JMP 17
 17) if A == 0xffffffff; then JMP 18 else JMP 48
 18) LOAD 24  // Argument 1 (LSB)
 19) if A & 0x80000000; then JMP 20 else JMP 48
 20) LOAD 24  // Argument 1 (LSB)
 21) if A == 0x0; then JMP 53 else JMP 22
 22) LOAD 36  // Argument 2 (MSB)
 23) if A == 0x0; then JMP 27 else JMP 24
 24) if A == 0xffffffff; then JMP 25 else JMP 48
 25) LOAD 32  // Argument 2 (LSB)
 26) if A & 0x80000000; then JMP 27 else JMP 48
 27) LOAD 32  // Argument 2 (LSB)
 28) if A == 0x0; then JMP 53 else JMP 29
 29) LOAD 20  // Argument 0 (MSB)
 30) if A == 0x0; then JMP 34 else JMP 31
 31) if A == 0xffffffff; then JMP 32 else JMP 48
 32) LOAD 16  // Argument 0 (LSB)
 33) if A & 0x80000000; then JMP 34 else JMP 48
 34) LOAD 16  // Argument 0 (LSB)
 35) if A == 0x1; then JMP 36 else JMP 51
 36) LOAD 28  // Argument 1 (MSB)
 37) if A == 0x0; then JMP 41 else JMP 38
 38) if A == 0xffffffff; then JMP 39 else JMP 48
 39) LOAD 24  // Argument 1 (LSB)
 40) if A & 0x80000000; then JMP 41 else JMP 48
 41) LOAD 24  // Argument 1 (LSB)
 42) if A == 0x1; then JMP 43 else JMP 51
 43) LOAD 36  // Argument 2 (MSB)
 44) if A == 0x0; then JMP 49 else JMP 45
 45) if A == 0xffffffff; then JMP 46 else JMP 48
 46) LOAD 32  // Argument 2 (LSB)
 47) if A & 0x80000000; then JMP 49 else JMP 48
 48) RET 0x0  // Kill
 49) LOAD 32  // Argument 2 (LSB)
 50) if A == 0x1; then JMP 52 else JMP 51
 51) RET 0x50016  // errno = 22
 52) RET 0x5000b  // errno = 11
 53) RET 0x50001  // errno = 1
 54) RET 0x7fff0000  // Allowed
 55) RET 0x50026  // errno = 38
