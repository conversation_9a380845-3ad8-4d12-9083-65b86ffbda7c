<!DOCTYPE html>
<script src="../resources/testharness.js"></script>
<script src="../resources/testharnessreport.js"></script>
<script src="resources/property-parsing-test.js"></script>
<script>
// Verifies that offset-path property and its value are properly parsed

assert_valid_value("offset-path", "path('M 0 0 L 100 100 M 100 200 L 200 200 Z L 300 300 Z')",
                                  "path(\"M 0 0 L 100 100 M 100 200 L 200 200 Z L 300 300 Z\")");
assert_valid_value("offset-path", "none");
assert_valid_value("offset-path", "path('')", "none");
assert_invalid_value("offset-path", "path('M 20 30 A 60 70 80')");

assert_valid_value("offset-path", "ray(45deg closest-side)");
assert_valid_value("offset-path", "ray(sides -1turn)", "ray(-1turn sides)");
assert_valid_value("offset-path", "ray(400grad farthest-corner contain)");
assert_valid_value("offset-path", "ray(calc(180deg - 45deg) farthest-side)", "ray(calc(135deg) farthest-side)");
assert_invalid_value("offset-path", "ray(0deg)");
assert_invalid_value("offset-path", "ray(closest-side)");
assert_invalid_value("offset-path", "ray(closest-side 0deg closest-side)");
assert_invalid_value("offset-path", "ray(0deg closest-side 0deg)");
assert_invalid_value("offset-path", "ray(contain 0deg closest-side contain)");
</script>
