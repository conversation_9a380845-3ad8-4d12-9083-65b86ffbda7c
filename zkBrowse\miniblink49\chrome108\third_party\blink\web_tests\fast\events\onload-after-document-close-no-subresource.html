<p>This test verifies that the load event fires after a document.close, even if the document contains no subresources. See <a href="http://bugs.webkit.org/show_bug.cgi?id=13241">bug 13241</a>.</p>
<hr>
<pre id="console"></pre>

<iframe></iframe>

<script>
function log(s)
{
    document.getElementById("console").appendChild(document.createTextNode(s + "\n"));
}

function afterOnload()
{
    log("PASS: onload fired");
    if (window.testRunner)
        testRunner.notifyDone();
}

function runTest()
{
    if (window.testRunner) {
        testRunner.dumpAsText();

        // Quirkily, the FrameLoadDelegate API considers our <iframe> fully loaded 
        // once it has loaded its initial empty document. So, if we want DumpRenderTree 
        // to wait for the load event caused by our document.close() before dumping,
        // we need to tell it so explicitly.
        testRunner.waitUntilDone();
    }

    var doc = frames[0].document;
    doc.open();
    doc.write("<body onload=\"parent.afterOnload()\">");
    doc.write("</body>");
    doc.close();
}

runTest();
</script>
