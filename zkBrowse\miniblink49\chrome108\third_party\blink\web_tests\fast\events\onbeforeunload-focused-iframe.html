<html>
<body>
    New window with beforeUnload handler.<br>
    You should see an alert with "beforeUnload" if you try to close the window.<br>
    <iframe src="resources/onbeforeunload-focused-iframe-frame.html" name="focusedFrame"></iframe>
    <script>
        if (window.testRunner) {
            testRunner.dumpAsText();
            testRunner.dumpChildFrames();
            testRunner.waitUntilDone();
        }

        window.onbeforeunload = beforeUnload;
        function beforeUnload()
        {
            alert("beforeUnload");
            window.onbeforeunload = null;
            testRunner.notifyDone();
            return false;
        }


        function simulateCloseWindow()
        {
            if (window.testRunner) {
                // Simulate an attempt to close the window
                setTimeout(function() {
                    location.href = "resources/notify-done.html";
                }, 0);
            }
        }
    </script>
</body>
</html>
