Test error handling for JWK import.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".

error is: DataError: The required JWK member "kty" was missing
error is: DataError: The required JWK member "kty" was missing
error is: DataError: The required JWK member "kty" was missing
error is: DataError: The JWK "kty" member was not "oct"
error is: DataError: The JWK "alg" member was inconsistent with that specified by the Web Crypto call
error is: DataError: The JWK "alg" member was inconsistent with that specified by the Web Crypto call
error is: DataError: The JWK "alg" member was inconsistent with that specified by the Web Crypto call
error is: DataError: The required JWK member "k" was missing
error is: DataError: The required JWK member "k" was missing
error is: DataError: The JWK "k" member did not include the right length of key data for the given algorithm.
error is: DataError: The JWK "k" member did not include the right length of key data for the given algorithm.

importKey() with 'k' member containing '+' and '/' characters...
error is: DataError: The JWK member "k" could not be base64url decoded or contained padding
error is: DataError: The JWK "kty" member was not "oct"
error is: DataError: The JWK "alg" member was inconsistent with that specified by the Web Crypto call
error is: DataError: The JWK "use" member could not be parsed
Boolean JWK property passed as a string and worked
String JWK property passed as a number and worked
PASS successfullyParsed is true

TEST COMPLETE

