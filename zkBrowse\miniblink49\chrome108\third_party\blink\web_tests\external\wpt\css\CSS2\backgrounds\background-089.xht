<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>CSS Test: Background with (color image attachment position)</title>
        <link rel="author" title="Microsoft" href="http://www.microsoft.com/" />
        <link rel="reviewer" title="<PERSON><PERSON>" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/" /> <!-- 2012-03-24 -->
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#propdef-background" />
        <link rel="help" href="http://www.w3.org/TR/CSS21/colors.html#background-properties" />
        <meta name="flags" content="image interact" />
        <meta name="assert" content="Background with (color image attachment position) sets the background to the image and the image scrolls with the element. The image is centered across the bottom and then tile out from there." />
        <style type="text/css">
            #div1
            {
                height: 200px;
                overflow: scroll;
                width: 200px;
            }
            div div
            {
                background: green url("support/cat.png") scroll bottom center;
                width: 400px;
                height: 400px;
            }
        </style>
    </head>
    <body>
        <p>Test passes if the box below is filled with cat images that move when the box is scrolled. Also, after scrolling to the bottom of the box, there is a cat image that is not cut off, and it is centered at the bottom of the box (there can be additional cat images).</p>
        <div id="div1">
            <div></div>
        </div>
    </body>
</html>