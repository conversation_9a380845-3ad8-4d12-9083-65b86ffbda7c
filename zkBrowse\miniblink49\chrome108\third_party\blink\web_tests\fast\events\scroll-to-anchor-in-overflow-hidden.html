<html>
    <head>
       <style type="text/css">   
          html { overflow: hidden; }
          #top { margin: 100em 3em 0; padding: 2em 0 0 .5em; }
       </style>
        <script>
       function log(msg)
       {
           document.getElementById('console').appendChild(document.createTextNode(msg + '\n'));
       }


      function logMousePosition(event) {
   log ("Mouse:x = " + event.x + " , y = " + event.y);
}

       function test()
       {
           if (window.testRunner) {
               testRunner.waitUntilDone();
               testRunner.dumpAsText();
               setTimeout(scrollToAnchor, 0);
           }
       }

       function scrollToAnchor()
       {
           if (window.eventSender) {
               var input = document.getElementById('ANCHORLINK');
               var x = input.offsetLeft;
               var y = input.offsetTop;
               eventSender.mouseMoveTo(x, y);
               eventSender.mouseDown();
               eventSender.mouseUp();
           }

           if (document.scrollingElement.scrollTop == 0)
               log("Automated test : FAILED  : no scroll has occurred ");
           else
               log("Automated test : PASSED");

           if (window.testRunner)
               testRunner.notifyDone();
       }     
        </script>
    </head>
<body onload="test()">

    
    <div id="ANCHORLINK">
        <a href="#bottom">Link to Bottom of the page</a>
   </div>
   
       https://bugs.webkit.org/show_bug.cgi?id=20270 <br>
       This test verifies that a anchor link overrides the overflow:hidden attribute by scrolling somewhere on a page.<br>
       To do the test manually you have to click on the anchor link above. If the scroll occurs to the bottom of the page : the test has PASSED.<br>
       If you stay here after the click, the test has failed. <br><br><br><br>
<div id="console" style="height: 1000px;"></div>
<a name="bottom">Bottom of the page</a>
Manual Test : PASSED
   
    
</body>
</html>
