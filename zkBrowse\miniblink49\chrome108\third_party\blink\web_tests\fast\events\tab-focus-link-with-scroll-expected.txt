This tests that the scroll should be happened on moving the focus to out of visible area by tabbing.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".

PASS document.activeElement.id is 'tab1'
PASS document.activeElement.id is 'tab2'
PASS document.activeElement.id is 'tab3'
PASS document.activeElement.id is 'tab4'
PASS document.body.clientHeight is document.scrollingElement.scrollHeight - window.scrollY
PASS successfullyParsed is true

TEST COMPLETE

Tab1
Tab2
Tab3
Tab4
