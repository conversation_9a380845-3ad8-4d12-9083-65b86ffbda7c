<script src="../../resources/testharness.js"></script>
<script src="../../resources/testharnessreport.js"></script>
<script>
    const mainTest = async_test(
        'window.scrollTo while zoomed causes correct amount of scroll', {});
    const frameTest = async_test(
        'window.scrollTo while zoomed causes correct amount of scroll in '
        + 'iframe', {});

    function runTest() {
        var scaleFactor = 2;
        if (window.internals)
            internals.setPageScaleFactor(scaleFactor);

        window.scrollTo(0, 100);

        mainTest.step(function() {
            assert_equals(document.scrollingElement.scrollLeft, 0,
                "scrollingElement has correct scrollLeft");
            assert_equals(document.scrollingElement.scrollTop, 100,
                "scrollingElement has correct scrollTop");
            assert_equals(window.scrollX, 0,
                "window has correct scrollX");
            assert_equals(window.scrollY, 100,
                "window has correct scrollY");
        });
        mainTest.done();

        var frame = document.getElementById('frame');
        frame.contentWindow.scrollTo(100,100);

        frameTest.step(function() {
            assert_equals(frame.contentDocument.scrollingElement.scrollLeft,
                100,
                "IFrame ScrollingElement has correct scrollLeft");
            assert_equals(frame.contentDocument.scrollingElement.scrollTop,
                100,
                "IFrame ScrollingElement has correct scrollTop");
            assert_equals(frame.contentWindow.scrollX, 100,
                "IFrame window has correct scrollX");
            assert_equals(frame.contentWindow.scrollY, 100,
                "IFrame window has correct scrollY");
        });
        frameTest.done();
    }

    addEventListener('load', runTest);
</script>
<style>
  body {
    width: 4000px;
    height: 4000px;
  }
</style>

<div id="console"></div>
<iframe id="frame" style="width:400px; height: 400px; position: absolute; top: 100px;"></iframe>
<script>
    var frame = document.getElementById('frame');
    var doc = frame.contentDocument.open();
    doc.write("<html><style>::-webkit-scrollbar {width: 0px;height: 0px;}</style></html>");
    doc.write("<div style='left:0; top:0; width:100px; height:100px; position:absolute; background:yellow;'></div>");
    doc.write("<div style='left:100px; top:0; width:100px; height:100px; position:absolute; background:green;'></div>");
    doc.write("<div style='left:200px; top:0; width:100px; height:100px; position:absolute; background:blue;'></div>");
    doc.write("<div style='left:300px; top:0; width:100px; height:100px; position:absolute; background:green;'></div>");
    doc.write("<div style='left:400px; top:0; width:100px; height:100px; position:absolute; background:blue;'></div>");

    doc.write("<div style='left:0; top:100px; width:100px; height:100px; position:absolute; background: green;'></div>");
    doc.write("<div style='left:100px; top:100px; width:100px; height:100px; position:absolute; background:blue;'></div>");
    doc.write("<div style='left:200px; top:100px; width:100px; height:100px; position:absolute; background:green;'></div>");
    doc.write("<div style='left:300px; top:100px; width:100px; height:100px; position:absolute; background:blue;'></div>");
    doc.write("<div style='left:400px; top:100px; width:100px; height:100px; position:absolute; background:green;'></div>");
    doc.close();
    frame.contentDocument.body.style.width = "2000px";
    frame.contentDocument.body.style.height = "2000px";
</script>
