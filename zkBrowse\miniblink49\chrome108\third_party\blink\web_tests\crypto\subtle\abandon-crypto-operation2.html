<!DOCTYPE html>
<html>
<head>
<script src="../../resources/js-test.js"></script>
<script src="resources/common.js"></script>
</head>
<body>
<p id="description"></p>
<div id="console"></div>

<script>
    description("Tests cancelling of crypto operations when the context is destroyed.");

    // Start a worker thread which starts a LOT of expensive
    // webcrypto operations (generating RSA keys).
    //
    // Now from the main page try to generate an AES key.
    //
    // Lastly, the web worker closes itself. This should have the
    // effect of aborting all of the web crypto operations it had
    // started, and now the AES key generation will be able to complete.
    //
    // If the crypto tasks started by the web worker are NOT
    // cancelled (and are merely orphaned), then the test is going to
    // timeout.
    jsTestIsAsync = true;

    function startWorker() {
        return new Promise(function(resolve, reject) {
            var worker = new Worker("resources/worker-start-slow-operations.js");
            worker.onmessage = function(event)
            {
                debug(event.data);
                resolve();
            };
            worker.onerror = function(error)
            {
                debug(error.filename + ':' + error.lineno + ': ' + error.message);
                reject('worker failed');
            }
        });
    }

    function generateTestKey() {
        var algorithm = {name: "AES-CBC", length: 128};
        return crypto.subtle.generateKey(algorithm, true, ['encrypt']).then(function() {
            debug('Successfully generated AES-CBC key');
        });
    }

    startWorker().then(generateTestKey).then(finishJSTest, failAndFinishJSTest);
</script>
</body>
</html>
