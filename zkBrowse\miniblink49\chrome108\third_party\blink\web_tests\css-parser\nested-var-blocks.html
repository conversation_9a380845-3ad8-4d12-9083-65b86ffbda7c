<!DOCTYPE html>
<script src="../resources/testharness.js"></script>
<script src="../resources/testharnessreport.js"></script>
<style>
.test { color: var(--m, var(--m, var(--m, var(--m, var(--m, var(--m, var(--m, var(--m, var(--m, var(--m, var(--m, var(--m, var(--m, var(--m, var(--m, var(--m, var(--m, var(--m, var(--m, var(--m,
</style>
<div id="test"></div>
<script>
test(() => {
    var elem = document.getElementById('test');
    elem.className = 'test'
}, 'This test passes if it does not crash or timeout.');
</script>
