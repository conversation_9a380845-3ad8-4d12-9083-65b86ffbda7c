<!DOCTYPE html>
<script src="../../resources/testharness.js"></script>
<script src="../../resources/testharnessreport.js"></script>
<script>
setup({allow_uncaught_exception: true});
promise_test(async () => {
  let lengthPromise = new Promise((resolve, reject) => {
    function handler() { resolve(arguments.length); }
    window.onerror = window.onclick = handler;
  });

  // This will yield a call to the onerror handler.
  setTimeout('{', 0);

  assert_equals(await lengthPromise, 5);
}, "onerror handler should receive 5 arguments, even if also an onclick handler");
</script>
