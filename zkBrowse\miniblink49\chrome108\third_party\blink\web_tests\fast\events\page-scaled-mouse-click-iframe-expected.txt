This tests that page scaling and CSS transforms do not affect mouse event pageX and pageY coordinates for content embedded in an iframe.

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".

Unscaled
PASS eventLog is "click(10, 10)"


setPageScale(0.5)
PASS eventLog is "click(20, 20)"


CSS scale(0.5, 2.0)
PASS eventLog is "click(20, 5)"


setPageScale(0.5), CSS scale(0.5, 2.0)
PASS eventLog is "click(40, 10)"


PASS successfullyParsed is true

TEST COMPLETE

