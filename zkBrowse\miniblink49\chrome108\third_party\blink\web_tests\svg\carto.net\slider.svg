<?xml version="1.0" encoding="iso-8859-1" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" [
]>
<?AdobeSVGViewer save="snapshot"?>
<svg width="100%" height="100%" viewBox="0 0 1024 768" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" onload="init(evt);">
	<title>Demonstration of the carto.net SVG GUI slider object</title>
	<script type="text/ecmascript" xlink:href="resources/helper_functions.js" />
	<script type="text/ecmascript" xlink:href="resources/slider.js" />
	<script type="text/ecmascript" xlink:href="resources/mapApp.js" />
	<script type="text/ecmascript"><![CDATA[
		var myMapApp = new mapApp(false,undefined);
		var slider1;
		var slider2;
		var slider3;
		var slider4;

		function init(evt) {
			//fist the slider styles
			var sliderStyles={"stroke":"dimgray","stroke-width":3};
			var invisSliderWidth = 15;
			slider1 = new slider("slider1","slider1",710,65,0,710,165,100,50,sliderStyles,invisSliderWidth,"sliderSymbol",showVal,true);
			slider2 = new slider("slider2","slider2",100,100,0,300,300,50,30,sliderStyles,invisSliderWidth,"sliderSymbol",showVal,false);
			slider3 = new slider("slider3","slider3",100,100,0,200,100,100,0,sliderStyles,invisSliderWidth,"sliderSymbol",showVal,false);
			slider4 = new slider("slider4","slider4",300,300,50,500,300,100,70,sliderStyles,invisSliderWidth,"sliderSymbol",showVal,true);
		}
		
		function showVal(valType,groupId,value) {
			//valType can be "change" (on mouse move or click) or "release" (mouseup or mouseout)
			if (valType == "change") {
				statusChange("Value of Slider '"+groupId+"' = "+Math.round(value));
			}
			if (valType == "release") {
				statusChange("Slider '"+groupId+"' was released, value = "+Math.round(value));
			}
		}
		function reposSlider() {
			var newx1 = 300 + Math.round(Math.random() * 50);
			var newx2 = 500 + Math.round(Math.random() * 50);
			var newy1 = 300 + Math.round(Math.random() * 50);
			var newy2 = 300 + Math.round(Math.random() * 50);
			slider4.moveTo(newx1,newy1,newx2,newy2);
		}
]]></script>
	<defs>
		<!-- Symbol for Slider -->
		<symbol id="sliderSymbol" overflow="visible">
			<line x1="0" y1="-10" x2="0" y2="10" stroke="dimgray" stroke-width="5" pointer-events="none"/>
		</symbol>
	</defs>
	<rect x="-2000" y="-2000" width="6000" height="6000" fill="white" stroke="none" />
	<text x="512" y="40" font-family="Arial,Helvetica" text-anchor="middle" font-weight="bold" font-size="20" fill="dimgray">Demonstration of the carto.net SVG GUI slider object</text>
	<g id="slider1" />
	<g id="slider2" />
	<g id="slider3" transform="translate(50 0)" />
	<g id="slider4" transform="translate(0 500) rotate(-50)" />
	<text x="20" y="700" id="statusText">Statusbar:</text>
	<text x="500" y="600" onclick="reposSlider()">Click on this text to randomly reposition slider 4</text>
</svg>
