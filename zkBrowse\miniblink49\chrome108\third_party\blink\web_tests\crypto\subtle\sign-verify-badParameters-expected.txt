Tests calling cypto.subtle.sign and crypto.subtle.verify with incorrect inputs

On success, you will see a series of "PASS" messages, followed by "TEST COMPLETE".

error is: TypeError: Failed to execute 'verify' on 'SubtleCrypto': The provided value is not of type '(ArrayBuffer or ArrayBufferView)'.
error is: TypeError: Failed to execute 'verify' on 'SubtleCrypto': The provided value is not of type '(ArrayBuffer or ArrayBufferView)'.
error is: TypeError: Failed to execute 'verify' on 'SubtleCrypto': The provided value is not of type '(ArrayBuffer or ArrayBufferView)'.
error is: NotSupportedError: Failed to execute 'sign' on 'SubtleCrypto': SHA-1: Unsupported operation: sign
error is: NotSupportedError: Failed to execute 'sign' on 'SubtleCrypto': AES-CBC: Unsupported operation: sign
PASS successfullyParsed is true

TEST COMPLETE

