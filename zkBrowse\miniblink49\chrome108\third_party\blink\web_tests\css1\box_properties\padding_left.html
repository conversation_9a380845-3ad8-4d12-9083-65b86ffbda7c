<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<HTML>
<HEAD>
<TITLE>CSS1 Test Suite: 5.5.09 padding-left</TITLE>
<META http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK rel="stylesheet" type="text/css" media="screen" href="../resources/base.css">
<SCRIPT src="../resources/base.js"></SCRIPT>
<STYLE type="text/css">
.zero {background-color: silver; padding-left: 0;}
.one {padding-left: 0.5in; background-color: aqua;}
.two {padding-left: 25px; background-color: aqua;}
.three {padding-left: 5em; background-color: aqua;}
.four {padding-left: 25%; background-color: aqua;}
.five {padding-left: -20px; background-color: aqua;}</STYLE>

</HEAD>

<BODY><P>The style declarations which apply to the text below are:</P>
<PRE>.zero {background-color: silver; padding-left: 0;}
.one {padding-left: 0.5in; background-color: aqua;}
.two {padding-left: 25px; background-color: aqua;}
.three {padding-left: 5em; background-color: aqua;}
.four {padding-left: 25%; background-color: aqua;}
.five {padding-left: -20px; background-color: aqua;}
</PRE>
<HR>
<P class="zero">
This element has a class of zero.
</P>
<P class="one">
This element should have a left padding of half an inch, which will require extra text in order to test.  Both the content background and the padding should be aqua (light blue).
</P>
<P class="two">
This element should have a left padding of 25 pixels, which will require extra text in order to test.  Both the content background and the padding should be aqua (light blue).
</P>
<P class="three">
This element should have a left padding of 5em, which will require extra text in order to test.  Both the content background and the padding should be aqua (light blue).
</P>
<P class="four">
This element should have a left padding of 25%, which is calculated with respect to the width of the parent element.  Both the content background and the padding should be aqua (light blue).
</P>
<UL class="two" style="background-color: gray;">
<LI>The left padding on this unordered list has been set to 25 pixels, which will require some extra test in order to test.</LI>
<LI class="two" style="background-color: white;">Another list item might not be such a bad idea, either, considering that such things do need to be double-checked.  This list item has its left padding also set to 25 pixels, which should combine with the list's padding to make 50 pixels of margin.
</UL>
<P class="five">
This element should have no left padding, since negative padding values are not allowed.  Both the content background and the normal padding should be aqua (light blue).
</P>


<TABLE border cellspacing="0" cellpadding="3" class="tabletest">
<TR>
<TD colspan="2" bgcolor="silver"><STRONG>TABLE Testing Section</STRONG></TD>
</TR>
<TR>
<TD bgcolor="silver">&nbsp;</TD>
<TD><P class="zero">
This element has a class of zero.
</P>
<P class="one">
This element should have a left padding of half an inch, which will require extra text in order to test.  Both the content background and the padding should be aqua (light blue).
</P>
<P class="two">
This element should have a left padding of 25 pixels, which will require extra text in order to test.  Both the content background and the padding should be aqua (light blue).
</P>
<P class="three">
This element should have a left padding of 5em, which will require extra text in order to test.  Both the content background and the padding should be aqua (light blue).
</P>
<P class="four">
This element should have a left padding of 25%, which is calculated with respect to the width of the parent element.  Both the content background and the padding should be aqua (light blue).
</P>
<UL class="two" style="background-color: gray;">
<LI>The left padding on this unordered list has been set to 25 pixels, which will require some extra test in order to test.</LI>
<LI class="two" style="background-color: white;">Another list item might not be such a bad idea, either, considering that such things do need to be double-checked.  This list item has its left padding also set to 25 pixels, which should combine with the list's padding to make 50 pixels of margin.
</UL>
<P class="five">
This element should have no left padding, since negative padding values are not allowed.  Both the content background and the normal padding should be aqua (light blue).
</P>
</TD></TR></TABLE></BODY>
</HTML>
