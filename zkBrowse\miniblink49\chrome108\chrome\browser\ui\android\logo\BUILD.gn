# Copyright 2022 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/config/android/rules.gni")

android_library("java") {
  sources = [
    "java/src/org/chromium/chrome/browser/logo/LogoBridge.java",
    "java/src/org/chromium/chrome/browser/logo/LogoCoordinator.java",
    "java/src/org/chromium/chrome/browser/logo/LogoDelegateImpl.java",
    "java/src/org/chromium/chrome/browser/logo/LogoView.java",
  ]

  deps = [
    ":java_resources",
    "//base:base_java",
    "//base:jni_java",
    "//build/android:build_java",
    "//chrome/android/features/start_surface:public_java",
    "//chrome/browser/flags:java",
    "//chrome/browser/preferences:java",
    "//chrome/browser/profiles/android:java",
    "//chrome/browser/search_engines/android:java",
    "//components/image_fetcher:java",
    "//components/search_engines/android:java",
    "//content/public/android:content_full_java",
    "//third_party/androidx:androidx_annotation_annotation_java",
    "//third_party/gif_player:gif_player_java",
    "//ui/android:ui_no_recycler_view_java",
  ]
  annotation_processor_deps = [ "//base/android/jni_generator:jni_processor" ]
  resources_package = "org.chromium.chrome.browser.logo"
}

generate_jni("jni_headers") {
  sources = [ "java/src/org/chromium/chrome/browser/logo/LogoBridge.java" ]
}

android_resources("java_resources") {
  sources = [
    "java/res/drawable-hdpi/google_logo.png",
    "java/res/drawable-mdpi/google_logo.png",
    "java/res/drawable-sw600dp-hdpi/google_logo.png",
    "java/res/drawable-sw600dp-mdpi/google_logo.png",
    "java/res/drawable-sw600dp-xhdpi/google_logo.png",
    "java/res/drawable-sw600dp-xxhdpi/google_logo.png",
    "java/res/drawable-sw600dp-xxxhdpi/google_logo.png",
    "java/res/drawable-xhdpi/google_logo.png",
    "java/res/drawable-xxhdpi/google_logo.png",
    "java/res/drawable-xxxhdpi/google_logo.png",
    "java/res/values-night/colors.xml",
    "java/res/values/colors.xml",
  ]

  deps = [ "//chrome/browser/ui/android/strings:ui_strings_grd" ]
}

robolectric_library("junit") {
  sources = [
    "java/src/org/chromium/chrome/browser/logo/LogoCoordinatorUnitTest.java",
    "java/src/org/chromium/chrome/browser/logo/LogoViewTest.java",
  ]

  deps = [
    ":java",
    "//base:base_java",
    "//base:base_java_test_support",
    "//base:base_junit_test_support",
    "//chrome/android:chrome_java",
    "//chrome/android/features/start_surface:public_java",
    "//chrome/browser/flags:java",
    "//chrome/browser/profiles/android:java",
    "//chrome/browser/search_engines/android:java",
    "//components/search_engines/android:java",
    "//content/public/android:content_full_java",
    "//content/public/test/android:content_java_test_support",
    "//third_party/androidx:androidx_test_core_java",
    "//third_party/androidx:androidx_test_ext_junit_java",
    "//third_party/junit:junit",
    "//third_party/mockito:mockito_java",
    "//ui/android:ui_java_test_support",
  ]
}
