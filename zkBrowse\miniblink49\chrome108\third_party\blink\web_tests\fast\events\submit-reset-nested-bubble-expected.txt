This tests that we stop propagation when bubbling a submit or reset event to more than one form. This matches Firefox behavior
This can only occur when misnested tags cause forms to be nested.

You can manually test the submit event, but this automated test will only test the reset event.


 
window reset capture
outer_form reset capture
div reset capture
inner_form reset target
inner_form reset target
div reset bubble

