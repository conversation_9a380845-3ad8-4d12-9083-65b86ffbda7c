<!DOCTYPE html>

<html>

 <head>

  <meta charset="UTF-8">

  <title>CSS Writing Modes Test: 'background-size: 100%' filling padding-box of document root element</title>

  <link rel="author" title="<PERSON><PERSON>" href="mailto:ko<PERSON><PERSON>@gmail.com">
  <link rel="reviewer" title="Gérard Talbot" href="http://www.gtalbot.org/BrowserBugsSection/css21testsuite/"> <!-- 2015-05-01 -->
  <link rel="help" href="http://www.w3.org/TR/css-writing-modes-3/#physical-only" title="7.6 Purely Physical Mappings">
  <link rel="match" href="background-size-document-root-vrl-002-ref.xht">

  <meta name="flags" content="image">
  <meta name="assert" content="Test checks that 'background-size: 100% 100%' applied for the html root element will make the background-image fill the padding-box of the document box. Since 'background-color' is 'transparent' and since 'background-repeat' is set to 'no-repeat', then only the padding-box of the document root box should be painted green and such padding-box should start at top-right corner of canvas because 'writing-mode' is set to 'vertical-rl'.">

  <style>
  img
    {
      margin-left: 100px;
    }

  iframe
    {
      border: transparent 0px none;
      height: 100px;
      vertical-align: top;
      width: 250px;
    }

  img#reference-overlapped-red
    {
      bottom: 100px;
      position: relative;
      z-index: -1;
    }
  </style>
 </head>

 <body>

  <p>Test passes if there is a filled green square and <strong>no red</strong>.</p>

  <div><iframe src="support/embedded-doc-for-background-size-root-vrl-006.html">This test requires an user agent with capability to embed an HTML document thanks to the HTML &lt;iframe&gt; element.</iframe></div>

  <div><img id="reference-overlapped-red" src="support/100x100-red.png" alt="Image download support must be enabled"></div>

 </body>
</html>
