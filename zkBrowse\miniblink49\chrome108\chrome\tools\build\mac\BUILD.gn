# Copyright 2016 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

if (current_toolchain != default_toolchain) {
  # Builds string resources with the default toolchain, then copies them into a temp
  # include directory for the current toolchain.
  copy("copy_chrome_strings_headers") {
    sources = [ "$root_build_dir/gen/chrome/grit/chromium_strings.h" ]
    outputs = [
      "$root_out_dir/infoplist_strings_util_gen/chrome/grit/chromium_strings.h",
    ]

    deps = [ "//chrome:strings($default_toolchain)" ]
  }
}

executable("infoplist_strings_util") {
  configs += [ "//build/config/compiler:wexit_time_destructors" ]

  sources = [ "infoplist_strings_util.cc" ]

  deps = [
    "//base",
    "//base:i18n",
    "//third_party/icu",
    "//ui/base:ui_data_pack",
  ]

  # This runs as part of the build, on the host, but needs to process strings
  # for the target.
  if (current_toolchain == default_toolchain) {
    deps += [ "//chrome:strings" ]
  } else {
    deps += [ ":copy_chrome_strings_headers" ]
    include_dirs = [ "$root_out_dir/infoplist_strings_util_gen" ]
  }
}
