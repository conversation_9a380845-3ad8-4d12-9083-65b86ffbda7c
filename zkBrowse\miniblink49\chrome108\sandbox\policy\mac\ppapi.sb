; Copyright 2017 The Chromium Authors
; Use of this source code is governed by a BSD-style license that can be
; found in the LICENSE file.

; --- The contents of common.sb implicitly included here. ---

; Params specific to ppapi.
(define ppapi-plugin-0 "PPAPI_PATH_0")
(define ppapi-plugin-1 "PPAPI_PATH_1")
(define ppapi-plugin-2 "PPAPI_PATH_2")
(define ppapi-plugin-3 "PPAPI_PATH_3")
(define ppapi-plugin-4 "PPAPI_PATH_4")

; Needed for Fonts.
(allow-font-access)

; Mach lookups.
(allow mach-lookup
  (global-name "com.apple.windowserver.active")
)

; IOKit
(allow iokit-open
  (iokit-registry-entry-class "IOSurfaceRootUserClient")
)

; Reads from home dir.
(allow file-read-data
  (path (user-homedir-path "/Library/Preferences/com.apple.universalaccess.plist"))
)

; Reads from /System.
(allow file-read-data
  (path "/System/Library/Colors/System.clr/System.clr")
  (subpath "/System/Library/ColorSync/Profiles")  ; https://crbug.com/822218
  (subpath "/System/Library/CoreServices/SystemAppearance.bundle")
)

; Allow the ppapi plugin binaries to be loaded.
(if (param-defined? ppapi-plugin-0)
  (allow file-read-data (subpath (param ppapi-plugin-0))))
(if (param-defined? ppapi-plugin-1)
  (allow file-read-data (subpath (param ppapi-plugin-1))))
(if (param-defined? ppapi-plugin-2)
  (allow file-read-data (subpath (param ppapi-plugin-2))))
(if (param-defined? ppapi-plugin-3)
  (allow file-read-data (subpath (param ppapi-plugin-3))))
(if (param-defined? ppapi-plugin-4)
  (allow file-read-data (subpath (param ppapi-plugin-4))))
