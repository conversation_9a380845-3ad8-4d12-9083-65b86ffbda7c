<!DOCTYPE html>
<style>
html, body, .workspace {
    height: 100%;
}
</style>
<script src="../../resources/js-test.js"></script>
<div class="workspace">
    <p id="description"></p>
    <div id="console"></div>
    <li><a id="tab1" href="#"><div class="a">Tab1</div></a></li>
</div>
<li><a id="tab2" href="#"><div class="a">Tab2</div></a></li>
<li><a id="tab3" href="#"><div class="a">Tab3</div></a></li>
<li><a id="tab4" href="#"><div class="a">Tab4</div></a></li>
<script>
    description("This tests that the scroll should be happened on moving the focus to out of visible area by tabbing.");
    testRunner.overridePreference('WebKitTabToLinksPreferenceKey', true);

    document.getElementById('tab1').focus();
    shouldBe("document.activeElement.id", "'tab1'");
 
    eventSender.keyDown('\t');
    shouldBe("document.activeElement.id", "'tab2'");

    eventSender.keyDown('\t');
    shouldBe("document.activeElement.id", "'tab3'");

    eventSender.keyDown('\t');
    shouldBe("document.activeElement.id", "'tab4'");

    shouldBe("document.body.clientHeight", "document.scrollingElement.scrollHeight - window.scrollY");
</script>
