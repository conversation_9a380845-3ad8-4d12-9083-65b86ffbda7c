// Copyright 2020 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

module data_decoder.mojom;

import "mojo/public/mojom/base/big_buffer.mojom";

// An interface that lets callers compress and uncompress data using gzip.
interface Gzipper {
  // Compresses |data| using raw DEFLATE, i.e. with no headers.
  Deflate(mojo_base.mojom.BigBuffer data)
      => (mojo_base.mojom.BigBuffer? deflated_data);

  // Uncompresses |data| that was compressed using raw DEFLATE, i.e. with no
  // headers. If the uncompressed data exceeds |max_uncompressed_size|, returns
  // empty. Note that this function will always allocate
  // |max_uncompressed_size| bytes regardless of actual uncompressed size.
  Inflate(mojo_base.mojom.BigBuffer data, uint64 max_uncompressed_size)
      => (mojo_base.mojom.BigBuffer? inflated_data);

  // Compresses |data| using gzip and returns it as |compressed_data|. Returns
  // null if compression fails.
  Compress(mojo_base.mojom.BigBuffer data)
      => (mojo_base.mojom.BigBuffer? compressed_data);

  // Uncompresses |compressed_data| using gzip and returns it as |data|. Returns
  // null if uncompression fails.
  Uncompress(mojo_base.mojom.BigBuffer compressed_data)
      => (mojo_base.mojom.BigBuffer? data);
};
