<!DOCTYPE html>
<script src="../../resources/run-after-layout-and-paint.js"></script>
<div id="container" style="overflow: auto; height: 200px; width: 200px; backface-visibility: hidden">
    <div id="target" style="margin: 50px 0; overflow: auto; height: 250px; width: 100px">
        <div onmouseover="mouseOver(event)" style="margin: 250px 0; width: 20px; height: 20px; background-color: blue;"></div>
    </div>
</div>
<div id="result">Test needs DumpRenderTree</div>
<script>
    function mouseOver(event) {
        document.getElementById("result").innerText = "PASS";
        testRunner.notifyDone();
    }

    if (window.testRunner) {
        testRunner.dumpAsText();
        testRunner.waitUntilDone();

        onload = function() {
            document.getElementById("container").scrollTop = 50;
            runAfterLayoutAndPaint(function() {
                eventSender.mouseMoveTo(10, 10);
                document.getElementById("target").scrollTop = 250;
            });
        };
    }
</script>
