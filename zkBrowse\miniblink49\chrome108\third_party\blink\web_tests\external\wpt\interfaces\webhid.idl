// GENERATED CONTENT - DO NOT EDIT
// Content was automatically extracted by <PERSON><PERSON> into webref
// (https://github.com/w3c/webref)
// Source: WebHID API (https://wicg.github.io/webhid/)

[SecureContext] partial interface Navigator {
    [SameObject] readonly attribute HID hid;
};

[Exposed=ServiceWorker, SecureContext]
partial interface WorkerNavigator {
    [SameObject] readonly attribute HID hid;
};

[Exposed=(Window,ServiceWorker), SecureContext]
interface HID : EventTarget {
    attribute EventHandler onconnect;
    attribute EventHandler ondisconnect;
    Promise<sequence<HIDDevice>> getDevices();
    Promise<sequence<HIDDevice>> requestDevice(
        HIDDeviceRequestOptions options);
};

dictionary HIDDeviceRequestOptions {
    required sequence<HIDDeviceFilter> filters;
    sequence<HIDDeviceFilter> exclusionFilters;
};

dictionary HIDDeviceFilter {
    unsigned long vendorId;
    unsigned short productId;
    unsigned short usagePage;
    unsigned short usage;
};

[Exposed=Window, SecureContext]
interface HIDDevice : EventTarget {
    attribute EventHandler oninputreport;
    readonly attribute boolean opened;
    readonly attribute unsigned short vendorId;
    readonly attribute unsigned short productId;
    readonly attribute DOMString productName;
    readonly attribute FrozenArray<HIDCollectionInfo> collections;
    Promise<undefined> open();
    Promise<undefined> close();
    Promise<undefined> forget();
    Promise<undefined> sendReport([EnforceRange] octet reportId, BufferSource data);
    Promise<undefined> sendFeatureReport(
        [EnforceRange] octet reportId,
        BufferSource data);
    Promise<DataView> receiveFeatureReport([EnforceRange] octet reportId);
};

[Exposed=Window, SecureContext]
interface HIDConnectionEvent : Event {
    constructor(DOMString type, HIDConnectionEventInit eventInitDict);
    [SameObject] readonly attribute HIDDevice device;
};

dictionary HIDConnectionEventInit : EventInit {
    required HIDDevice device;
};

[Exposed=Window, SecureContext]
interface HIDInputReportEvent : Event {
    constructor(DOMString type, HIDInputReportEventInit eventInitDict);
    [SameObject] readonly attribute HIDDevice device;
    readonly attribute octet reportId;
    readonly attribute DataView data;
};

dictionary HIDInputReportEventInit : EventInit {
    required HIDDevice device;
    required octet reportId;
    required DataView data;
};

dictionary HIDCollectionInfo {
    unsigned short usagePage;
    unsigned short usage;
    octet type;
    sequence<HIDCollectionInfo> children;
    sequence<HIDReportInfo> inputReports;
    sequence<HIDReportInfo> outputReports;
    sequence<HIDReportInfo> featureReports;
};

dictionary HIDReportInfo {
    octet reportId;
    sequence<HIDReportItem> items;
};

dictionary HIDReportItem {
    boolean isAbsolute;
    boolean isArray;
    boolean isBufferedBytes;
    boolean isConstant;
    boolean isLinear;
    boolean isRange;
    boolean isVolatile;
    boolean hasNull;
    boolean hasPreferredState;
    boolean wrap;
    sequence<unsigned long> usages;
    unsigned long usageMinimum;
    unsigned long usageMaximum;
    unsigned short reportSize;
    unsigned short reportCount;
    byte unitExponent;
    HIDUnitSystem unitSystem;
    byte unitFactorLengthExponent;
    byte unitFactorMassExponent;
    byte unitFactorTimeExponent;
    byte unitFactorTemperatureExponent;
    byte unitFactorCurrentExponent;
    byte unitFactorLuminousIntensityExponent;
    long logicalMinimum;
    long logicalMaximum;
    long physicalMinimum;
    long physicalMaximum;
    sequence<DOMString> strings;
};

enum HIDUnitSystem {
    "none", "si-linear", "si-rotation", "english-linear",
    "english-rotation", "vendor-defined", "reserved"
};
